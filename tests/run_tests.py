#!/usr/bin/env python3
"""
Test runner for CertRats frontend testing
Supports both Selenium and Playwright with BEHAVE integration
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


class TestRunner:
    """Test runner for frontend tests"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_dir = Path(__file__).parent
        
    def run_behave_tests(self, browser='chromium', use_playwright=True, tags=None, headless=True):
        """Run BEHAVE tests with specified configuration"""
        
        # Set environment variables
        env = os.environ.copy()
        env['BROWSER'] = browser
        env['USE_PLAYWRIGHT'] = 'true' if use_playwright else 'false'
        env['HEADLESS'] = 'true' if headless else 'false'
        env['BASE_URL'] = env.get('BASE_URL', 'http://localhost:3000')
        
        # Build behave command
        cmd = ['behave']
        
        # Add tags if specified
        if tags:
            for tag in tags:
                cmd.extend(['--tags', tag])
        
        # Add format options
        cmd.extend([
            '--format', 'pretty',
            '--format', 'json',
            '--outfile', 'test-results/behave-results.json'
        ])
        
        # Add feature directory
        cmd.append(str(self.test_dir / 'features'))
        
        print(f"Running BEHAVE tests with {browser} ({'Playwright' if use_playwright else 'Selenium'})")
        print(f"Command: {' '.join(cmd)}")
        print(f"Environment: BROWSER={browser}, USE_PLAYWRIGHT={use_playwright}, HEADLESS={headless}")
        
        # Create results directory
        results_dir = self.project_root / 'test-results'
        results_dir.mkdir(exist_ok=True)
        
        # Run tests
        try:
            result = subprocess.run(cmd, env=env, cwd=self.test_dir, check=False)
            return result.returncode == 0
        except Exception as e:
            print(f"Error running BEHAVE tests: {e}")
            return False
    
    def run_playwright_tests(self, browser='chromium', headless=True):
        """Run Playwright tests directly"""
        
        # Set environment variables
        env = os.environ.copy()
        env['BROWSER'] = browser
        env['HEADLESS'] = 'true' if headless else 'false'
        
        # Build playwright command
        cmd = ['npx', 'playwright', 'test']
        
        if browser != 'all':
            cmd.extend(['--project', browser])
        
        if headless:
            cmd.append('--headed')
        
        print(f"Running Playwright tests with {browser}")
        print(f"Command: {' '.join(cmd)}")
        
        # Run tests from frontend directory
        frontend_dir = self.project_root / 'frontend'
        
        try:
            result = subprocess.run(cmd, env=env, cwd=frontend_dir, check=False)
            return result.returncode == 0
        except Exception as e:
            print(f"Error running Playwright tests: {e}")
            return False
    
    def run_unit_tests(self):
        """Run unit tests"""
        
        print("Running unit tests...")
        
        # Run Jest tests
        frontend_dir = self.project_root / 'frontend'
        cmd = ['npm', 'test', '--', '--coverage', '--watchAll=false']
        
        try:
            result = subprocess.run(cmd, cwd=frontend_dir, check=False)
            return result.returncode == 0
        except Exception as e:
            print(f"Error running unit tests: {e}")
            return False
    
    def run_api_tests(self):
        """Run API tests"""
        
        print("Running API tests...")
        
        # Run pytest for API tests
        api_dir = self.project_root / 'api'
        cmd = ['python', '-m', 'pytest', 'tests/', '-v', '--cov=.', '--cov-report=html']
        
        try:
            result = subprocess.run(cmd, cwd=api_dir, check=False)
            return result.returncode == 0
        except Exception as e:
            print(f"Error running API tests: {e}")
            return False
    
    def run_accessibility_tests(self, browser='chromium'):
        """Run accessibility-specific tests"""
        
        print("Running accessibility tests...")
        
        return self.run_behave_tests(
            browser=browser,
            use_playwright=True,
            tags=['@accessibility'],
            headless=True
        )
    
    def run_performance_tests(self, browser='chromium'):
        """Run performance-specific tests"""
        
        print("Running performance tests...")
        
        return self.run_behave_tests(
            browser=browser,
            use_playwright=True,
            tags=['@performance'],
            headless=True
        )
    
    def run_mobile_tests(self, browser='chromium'):
        """Run mobile-specific tests"""
        
        print("Running mobile tests...")
        
        return self.run_behave_tests(
            browser=browser,
            use_playwright=True,
            tags=['@mobile'],
            headless=True
        )
    
    def run_cross_browser_tests(self):
        """Run tests across multiple browsers"""
        
        print("Running cross-browser tests...")
        
        browsers = ['chromium', 'firefox', 'webkit']
        results = []
        
        for browser in browsers:
            print(f"\n--- Testing with {browser} ---")
            success = self.run_behave_tests(
                browser=browser,
                use_playwright=True,
                tags=['@cross-browser'],
                headless=True
            )
            results.append((browser, success))
        
        # Print summary
        print("\n--- Cross-browser Test Results ---")
        for browser, success in results:
            status = "PASSED" if success else "FAILED"
            print(f"{browser}: {status}")
        
        return all(success for _, success in results)
    
    def run_smoke_tests(self, browser='chromium'):
        """Run smoke tests"""
        
        print("Running smoke tests...")
        
        return self.run_behave_tests(
            browser=browser,
            use_playwright=True,
            tags=['@smoke'],
            headless=True
        )
    
    def run_all_tests(self):
        """Run all test suites"""
        
        print("Running all test suites...")
        
        results = []
        
        # Unit tests
        print("\n=== Unit Tests ===")
        results.append(("Unit Tests", self.run_unit_tests()))
        
        # API tests
        print("\n=== API Tests ===")
        results.append(("API Tests", self.run_api_tests()))
        
        # Smoke tests
        print("\n=== Smoke Tests ===")
        results.append(("Smoke Tests", self.run_smoke_tests()))
        
        # Accessibility tests
        print("\n=== Accessibility Tests ===")
        results.append(("Accessibility Tests", self.run_accessibility_tests()))
        
        # Performance tests
        print("\n=== Performance Tests ===")
        results.append(("Performance Tests", self.run_performance_tests()))
        
        # Mobile tests
        print("\n=== Mobile Tests ===")
        results.append(("Mobile Tests", self.run_mobile_tests()))
        
        # Cross-browser tests
        print("\n=== Cross-browser Tests ===")
        results.append(("Cross-browser Tests", self.run_cross_browser_tests()))
        
        # Print final summary
        print("\n" + "="*50)
        print("FINAL TEST RESULTS")
        print("="*50)
        
        all_passed = True
        for test_suite, success in results:
            status = "PASSED" if success else "FAILED"
            print(f"{test_suite}: {status}")
            if not success:
                all_passed = False
        
        print("="*50)
        overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
        print(f"Overall: {overall_status}")
        
        return all_passed


def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(description='CertRats Frontend Test Runner')
    parser.add_argument('--type', choices=['unit', 'api', 'e2e', 'accessibility', 'performance', 'mobile', 'cross-browser', 'smoke', 'all'], 
                       default='smoke', help='Type of tests to run')
    parser.add_argument('--browser', choices=['chromium', 'firefox', 'webkit', 'all'], 
                       default='chromium', help='Browser to use for tests')
    parser.add_argument('--playwright', action='store_true', default=True, 
                       help='Use Playwright instead of Selenium')
    parser.add_argument('--selenium', action='store_true', 
                       help='Use Selenium instead of Playwright')
    parser.add_argument('--headless', action='store_true', default=True, 
                       help='Run tests in headless mode')
    parser.add_argument('--headed', action='store_true', 
                       help='Run tests in headed mode (with browser UI)')
    parser.add_argument('--tags', nargs='+', 
                       help='BEHAVE tags to run (e.g., @smoke @accessibility)')
    
    args = parser.parse_args()
    
    # Determine browser automation tool
    use_playwright = not args.selenium
    headless = not args.headed
    
    runner = TestRunner()
    
    # Run specified test type
    if args.type == 'unit':
        success = runner.run_unit_tests()
    elif args.type == 'api':
        success = runner.run_api_tests()
    elif args.type == 'e2e':
        success = runner.run_behave_tests(args.browser, use_playwright, args.tags, headless)
    elif args.type == 'accessibility':
        success = runner.run_accessibility_tests(args.browser)
    elif args.type == 'performance':
        success = runner.run_performance_tests(args.browser)
    elif args.type == 'mobile':
        success = runner.run_mobile_tests(args.browser)
    elif args.type == 'cross-browser':
        success = runner.run_cross_browser_tests()
    elif args.type == 'smoke':
        success = runner.run_smoke_tests(args.browser)
    elif args.type == 'all':
        success = runner.run_all_tests()
    else:
        print(f"Unknown test type: {args.type}")
        return 1
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
