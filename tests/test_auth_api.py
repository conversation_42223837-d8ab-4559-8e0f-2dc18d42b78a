"""Unit tests for authentication API endpoints"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import json

from api.app import app
from database import get_db
from models import Base
from models.user import User, UserSession


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_auth.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="module")
def setup_database():
    """Create test database tables"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client(setup_database):
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def db_session():
    """Create database session for testing"""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


class TestUserRegistration:
    """Test user registration functionality"""
    
    def test_register_user_success(self, client):
        """Test successful user registration"""
        registration_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "name": "Test User",
            "first_name": "Test",
            "last_name": "User"
        }
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        
        assert response.status_code == 201
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert data["user"]["email"] == "<EMAIL>"
        assert data["user"]["name"] == "Test User"
    
    def test_register_user_duplicate_email(self, client):
        """Test registration with duplicate email"""
        registration_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "name": "First User"
        }
        
        # First registration should succeed
        response1 = client.post("/api/v1/auth/register", json=registration_data)
        assert response1.status_code == 201
        
        # Second registration with same email should fail
        registration_data["name"] = "Second User"
        response2 = client.post("/api/v1/auth/register", json=registration_data)
        assert response2.status_code == 409
        assert "already exists" in response2.json()["detail"]
    
    def test_register_user_weak_password(self, client):
        """Test registration with weak password"""
        registration_data = {
            "email": "<EMAIL>",
            "password": "weak",
            "name": "Weak Password User"
        }
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 422
        assert "validation error" in response.json()["detail"][0]["type"]
    
    def test_register_user_invalid_email(self, client):
        """Test registration with invalid email"""
        registration_data = {
            "email": "invalid-email",
            "password": "SecurePass123!",
            "name": "Invalid Email User"
        }
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 422


class TestUserLogin:
    """Test user login functionality"""
    
    @pytest.fixture
    def registered_user(self, client):
        """Create a registered user for login tests"""
        registration_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "name": "Login Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 201
        return registration_data
    
    def test_login_success(self, client, registered_user):
        """Test successful login"""
        login_data = {
            "email": registered_user["email"],
            "password": registered_user["password"]
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert data["user"]["email"] == registered_user["email"]
    
    def test_login_invalid_credentials(self, client, registered_user):
        """Test login with invalid credentials"""
        login_data = {
            "email": registered_user["email"],
            "password": "WrongPassword123!"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 401
        assert "Invalid email or password" in response.json()["detail"]
    
    def test_login_nonexistent_user(self, client):
        """Test login with non-existent user"""
        login_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 401
        assert "Invalid email or password" in response.json()["detail"]
    
    def test_login_with_remember_me(self, client, registered_user):
        """Test login with remember me option"""
        login_data = {
            "email": registered_user["email"],
            "password": registered_user["password"],
            "remember_me": True
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 200
        # Token should be valid (we can't easily test extended expiration in unit tests)


class TestTokenRefresh:
    """Test token refresh functionality"""
    
    @pytest.fixture
    def authenticated_user(self, client):
        """Create authenticated user with tokens"""
        registration_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "name": "Refresh Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 201
        return response.json()
    
    def test_refresh_token_success(self, client, authenticated_user):
        """Test successful token refresh"""
        refresh_data = {
            "refresh_token": authenticated_user["refresh_token"]
        }
        
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        # New tokens should be different from original
        assert data["access_token"] != authenticated_user["access_token"]
        assert data["refresh_token"] != authenticated_user["refresh_token"]
    
    def test_refresh_token_invalid(self, client):
        """Test refresh with invalid token"""
        refresh_data = {
            "refresh_token": "invalid_token"
        }
        
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        assert response.status_code == 401


class TestProtectedEndpoints:
    """Test protected endpoints requiring authentication"""
    
    @pytest.fixture
    def authenticated_user(self, client):
        """Create authenticated user with tokens"""
        registration_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "name": "Protected Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=registration_data)
        assert response.status_code == 201
        return response.json()
    
    def test_get_current_user_success(self, client, authenticated_user):
        """Test getting current user profile"""
        headers = {
            "Authorization": f"Bearer {authenticated_user['access_token']}"
        }
        
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["name"] == "Protected Test User"
    
    def test_get_current_user_no_token(self, client):
        """Test getting current user without token"""
        response = client.get("/api/v1/auth/me")
        assert response.status_code == 403  # FastAPI returns 403 for missing auth
    
    def test_get_current_user_invalid_token(self, client):
        """Test getting current user with invalid token"""
        headers = {
            "Authorization": "Bearer invalid_token"
        }
        
        response = client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 401
    
    def test_logout_success(self, client, authenticated_user):
        """Test successful logout"""
        headers = {
            "Authorization": f"Bearer {authenticated_user['access_token']}"
        }
        
        response = client.post("/api/v1/auth/logout", headers=headers)
        assert response.status_code == 200
        
        # Token should be invalidated - accessing protected endpoint should fail
        response = client.get("/api/v1/auth/me", headers=headers)
        # Note: In a real implementation, this should return 401 after logout
        # For now, we just test that logout endpoint works
    
    def test_update_preferences(self, client, authenticated_user):
        """Test updating user preferences"""
        headers = {
            "Authorization": f"Bearer {authenticated_user['access_token']}"
        }
        
        preferences_data = {
            "preferences": {
                "theme": "dark",
                "notifications": True,
                "language": "en"
            }
        }
        
        response = client.put("/api/v1/auth/preferences", json=preferences_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["preferences"]["theme"] == "dark"
        assert data["preferences"]["notifications"] is True
