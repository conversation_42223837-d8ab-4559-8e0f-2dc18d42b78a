"""Comprehensive tests for Budget Optimization API endpoints.

This module provides extensive test coverage for all budget optimization
API endpoints, including optimization algorithms, ROI calculations, and analytics.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
import json

from api.app import app
from database import get_db
from models.enterprise import Enterprise, EnterpriseTeam


class TestBudgetOptimizationAPI:
    """Test suite for Budget Optimization API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def sample_enterprise_data(self):
        """Sample enterprise data for testing."""
        return {
            "enterprise_id": 1,
            "total_budget": 100000.0,
            "strategic_priorities": ["cybersecurity", "cloud_security"],
            "timeline_months": 12
        }
    
    @pytest.fixture
    def sample_roi_request(self):
        """Sample ROI calculation request."""
        return {
            "enterprise_id": 1,
            "investment_amount": 50000.0,
            "training_programs": ["CISSP", "CISM", "Security+"],
            "timeline_months": 18,
            "success_metrics": {
                "certification_completion_rate": 0.85,
                "employee_retention_rate": 0.95
            }
        }
    
    def test_optimize_budget_success(self, client, sample_enterprise_data):
        """Test successful budget optimization."""
        response = client.post(
            "/api/v1/budget-optimization/optimize",
            json=sample_enterprise_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "enterprise_id" in data
        assert "total_budget" in data
        assert "optimized_allocation" in data
        assert "projected_roi" in data
        assert "cost_savings" in data
        assert "efficiency_score" in data
        assert "recommendations" in data
        assert "risk_assessment" in data
        assert "timeline_months" in data
        assert "optimization_date" in data
        
        # Verify data types and ranges
        assert data["enterprise_id"] == sample_enterprise_data["enterprise_id"]
        assert data["total_budget"] == sample_enterprise_data["total_budget"]
        assert isinstance(data["optimized_allocation"], dict)
        assert isinstance(data["projected_roi"], (int, float))
        assert isinstance(data["cost_savings"], (int, float))
        assert isinstance(data["efficiency_score"], (int, float))
        assert isinstance(data["recommendations"], list)
        assert isinstance(data["risk_assessment"], dict)
        assert data["timeline_months"] == sample_enterprise_data["timeline_months"]
        
        # Verify optimization results are reasonable
        assert data["projected_roi"] >= 0
        assert data["cost_savings"] >= 0
        assert 0 <= data["efficiency_score"] <= 100
        assert len(data["recommendations"]) > 0
    
    def test_optimize_budget_invalid_data(self, client):
        """Test budget optimization with invalid data."""
        invalid_data = {
            "enterprise_id": -1,
            "total_budget": -1000,
            "strategic_priorities": [],
            "timeline_months": 0
        }
        
        response = client.post(
            "/api/v1/budget-optimization/optimize",
            json=invalid_data
        )
        
        assert response.status_code == 400
    
    def test_get_budget_recommendations_success(self, client):
        """Test successful budget recommendations retrieval."""
        response = client.get(
            "/api/v1/budget-optimization/recommendations/1",
            params={
                "budget_amount": 75000.0,
                "priority_focus": "cybersecurity"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "enterprise_id" in data
        assert "budget_amount" in data
        assert "recommendations" in data
        assert "priority_allocations" in data
        assert "expected_outcomes" in data
        assert "implementation_timeline" in data
        assert "success_metrics" in data
        assert "generated_at" in data
        
        # Verify data quality
        assert data["enterprise_id"] == 1
        assert data["budget_amount"] == 75000.0
        assert isinstance(data["recommendations"], list)
        assert isinstance(data["priority_allocations"], dict)
        assert isinstance(data["expected_outcomes"], dict)
        assert isinstance(data["implementation_timeline"], dict)
        assert isinstance(data["success_metrics"], list)
        assert len(data["recommendations"]) > 0
    
    def test_get_budget_recommendations_not_found(self, client):
        """Test budget recommendations for non-existent enterprise."""
        response = client.get(
            "/api/v1/budget-optimization/recommendations/999",
            params={"budget_amount": 50000.0}
        )
        
        assert response.status_code == 404
    
    def test_calculate_training_roi_success(self, client, sample_roi_request):
        """Test successful training ROI calculation."""
        response = client.post(
            "/api/v1/budget-optimization/roi/calculate",
            json=sample_roi_request
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "enterprise_id" in data
        assert "investment_amount" in data
        assert "projected_return" in data
        assert "roi_percentage" in data
        assert "payback_period_months" in data
        assert "net_present_value" in data
        assert "risk_adjusted_roi" in data
        assert "confidence_interval" in data
        assert "key_assumptions" in data
        assert "sensitivity_analysis" in data
        assert "calculation_date" in data
        
        # Verify data quality
        assert data["enterprise_id"] == sample_roi_request["enterprise_id"]
        assert data["investment_amount"] == sample_roi_request["investment_amount"]
        assert isinstance(data["projected_return"], (int, float))
        assert isinstance(data["roi_percentage"], (int, float))
        assert isinstance(data["payback_period_months"], int)
        assert isinstance(data["net_present_value"], (int, float))
        assert isinstance(data["risk_adjusted_roi"], (int, float))
        assert isinstance(data["confidence_interval"], dict)
        assert isinstance(data["key_assumptions"], list)
        assert isinstance(data["sensitivity_analysis"], dict)
        
        # Verify calculations are reasonable
        assert data["projected_return"] >= 0
        assert data["payback_period_months"] > 0
        assert len(data["key_assumptions"]) > 0
    
    def test_calculate_training_roi_invalid_data(self, client):
        """Test training ROI calculation with invalid data."""
        invalid_data = {
            "enterprise_id": -1,
            "investment_amount": -1000,
            "training_programs": [],
            "timeline_months": 0
        }
        
        response = client.post(
            "/api/v1/budget-optimization/roi/calculate",
            json=invalid_data
        )
        
        assert response.status_code == 400
    
    def test_get_budget_analytics_success(self, client):
        """Test successful budget analytics retrieval."""
        response = client.get(
            "/api/v1/budget-optimization/analytics/1",
            params={
                "period_months": 12,
                "include_projections": True
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "enterprise_id" in data
        assert "analysis_period_months" in data
        assert "total_budget_allocated" in data
        assert "total_budget_utilized" in data
        assert "utilization_rate" in data
        assert "cost_per_certification" in data
        assert "roi_by_program" in data
        assert "efficiency_metrics" in data
        assert "trend_analysis" in data
        assert "benchmark_comparison" in data
        assert "optimization_opportunities" in data
        assert "projections" in data
        assert "analysis_date" in data
        
        # Verify data quality
        assert data["enterprise_id"] == 1
        assert data["analysis_period_months"] == 12
        assert isinstance(data["total_budget_allocated"], (int, float))
        assert isinstance(data["total_budget_utilized"], (int, float))
        assert isinstance(data["utilization_rate"], (int, float))
        assert isinstance(data["cost_per_certification"], (int, float))
        assert isinstance(data["roi_by_program"], dict)
        assert isinstance(data["efficiency_metrics"], dict)
        assert isinstance(data["trend_analysis"], dict)
        assert isinstance(data["benchmark_comparison"], dict)
        assert isinstance(data["optimization_opportunities"], list)
        assert isinstance(data["projections"], dict)
        
        # Verify metrics are reasonable
        assert 0 <= data["utilization_rate"] <= 100
        assert data["cost_per_certification"] >= 0
    
    def test_get_budget_analytics_without_projections(self, client):
        """Test budget analytics without projections."""
        response = client.get(
            "/api/v1/budget-optimization/analytics/1",
            params={
                "period_months": 6,
                "include_projections": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify projections are not included
        assert data["projections"] is None
    
    def test_get_budget_analytics_not_found(self, client):
        """Test budget analytics for non-existent enterprise."""
        response = client.get("/api/v1/budget-optimization/analytics/999")
        
        assert response.status_code == 404
    
    def test_get_industry_benchmarks_success(self, client):
        """Test successful industry benchmarks retrieval."""
        response = client.get(
            "/api/v1/budget-optimization/benchmarks",
            params={
                "industry": "technology",
                "company_size": "medium"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "industry" in data
        assert "company_size" in data
        assert "benchmarks" in data
        assert "data_sources" in data
        assert "last_updated" in data
        
        # Verify data quality
        assert data["industry"] == "technology"
        assert data["company_size"] == "medium"
        assert isinstance(data["benchmarks"], dict)
        assert isinstance(data["data_sources"], list)
        assert len(data["data_sources"]) > 0
    
    def test_get_industry_benchmarks_no_filters(self, client):
        """Test industry benchmarks without filters."""
        response = client.get("/api/v1/budget-optimization/benchmarks")
        
        assert response.status_code == 200
        data = response.json()
        
        # Should return general benchmarks
        assert "benchmarks" in data
        assert isinstance(data["benchmarks"], dict)
    
    def test_health_check(self, client):
        """Test budget optimization health check."""
        response = client.get("/api/v1/budget-optimization/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "Budget Optimization"
        assert "version" in data
        assert "features" in data
        assert "timestamp" in data
        
        # Verify expected features
        expected_features = [
            "budget_optimization",
            "roi_calculation",
            "budget_analytics",
            "industry_benchmarks",
            "allocation_recommendations"
        ]
        
        for feature in expected_features:
            assert feature in data["features"]
    
    def test_optimization_algorithm_consistency(self, client, sample_enterprise_data):
        """Test that optimization algorithm produces consistent results."""
        # Run optimization multiple times with same input
        results = []
        for _ in range(3):
            response = client.post(
                "/api/v1/budget-optimization/optimize",
                json=sample_enterprise_data
            )
            assert response.status_code == 200
            results.append(response.json())
        
        # Verify results are consistent (within reasonable variance)
        base_result = results[0]
        for result in results[1:]:
            # ROI should be within 5% variance
            roi_variance = abs(result["projected_roi"] - base_result["projected_roi"]) / base_result["projected_roi"]
            assert roi_variance < 0.05
            
            # Efficiency score should be within 2% variance
            efficiency_variance = abs(result["efficiency_score"] - base_result["efficiency_score"]) / base_result["efficiency_score"]
            assert efficiency_variance < 0.02
    
    def test_budget_allocation_sum(self, client, sample_enterprise_data):
        """Test that budget allocation sums to total budget."""
        response = client.post(
            "/api/v1/budget-optimization/optimize",
            json=sample_enterprise_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Sum all allocations
        total_allocated = sum(data["optimized_allocation"].values())
        
        # Should equal total budget (within rounding tolerance)
        assert abs(total_allocated - data["total_budget"]) < 1.0
    
    def test_response_time_performance(self, client, sample_enterprise_data):
        """Test response time performance for optimization endpoints."""
        import time
        
        # Test budget optimization response time
        start_time = time.time()
        response = client.post(
            "/api/v1/budget-optimization/optimize",
            json=sample_enterprise_data
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Should respond within reasonable time (10 seconds for test environment)
        assert response_time < 10.0
        assert response.status_code == 200
