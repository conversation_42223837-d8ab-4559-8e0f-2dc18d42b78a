"""Performance and load testing for critical system components"""
import pytest
import time
import threading
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from typing import List, Dict, Any

from fastapi.testclient import TestClient
from api.app import create_app
from services.cost_calculator import CostCalculatorService


class TestAPIPerformance:
    """Test API endpoint performance under various loads"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_health_endpoint_response_time(self, client):
        """Test health endpoint response time under normal conditions"""
        response_times = []
        
        for _ in range(10):
            start_time = time.time()
            response = client.get("/api/v1/health")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        # Health endpoint should be very fast
        assert avg_response_time < 0.1  # 100ms average
        assert max_response_time < 0.5   # 500ms maximum
        
        print(f"Health endpoint - Avg: {avg_response_time:.3f}s, Max: {max_response_time:.3f}s")
    
    def test_concurrent_health_checks(self, client):
        """Test health endpoint under concurrent load"""
        num_threads = 20
        requests_per_thread = 10
        results = []
        
        def make_requests():
            thread_results = []
            for _ in range(requests_per_thread):
                start_time = time.time()
                response = client.get("/api/v1/health")
                end_time = time.time()
                
                thread_results.append({
                    'status_code': response.status_code,
                    'response_time': end_time - start_time,
                    'thread_id': threading.current_thread().ident
                })
            return thread_results
        
        # Execute concurrent requests
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(make_requests) for _ in range(num_threads)]
            
            for future in as_completed(futures):
                results.extend(future.result())
        
        # Analyze results
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r['status_code'] == 200)
        response_times = [r['response_time'] for r in results]
        
        success_rate = successful_requests / total_requests
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        
        assert success_rate >= 0.95  # 95% success rate minimum
        assert avg_response_time < 0.2  # 200ms average under load
        assert p95_response_time < 1.0   # 1s 95th percentile
        
        print(f"Concurrent load - Success rate: {success_rate:.2%}, "
              f"Avg: {avg_response_time:.3f}s, P95: {p95_response_time:.3f}s")
    
    def test_job_search_performance(self, client, db_session):
        """Test job search endpoint performance with large dataset"""
        # Create test data
        from models.security_career_framework import SecurityJobType
        
        # Create a reasonable number of test jobs
        test_jobs = []
        for i in range(100):  # Reduced from 1000 for faster testing
            job = SecurityJobType(
                title=f"Security Professional {i}",
                security_area="Security Operations" if i % 2 == 0 else "Security Engineering",
                seniority_level="entry" if i < 30 else "mid" if i < 70 else "senior",
                job_family="analyst" if i % 3 == 0 else "engineer",
                description=f"Security position {i} with various responsibilities",
                required_skills=["Python", "AWS", "Security"],
                salary_range_min=50000 + (i * 1000),
                salary_range_max=80000 + (i * 1500),
                created_at=datetime.utcnow()
            )
            test_jobs.append(job)
        
        db_session.add_all(test_jobs)
        db_session.commit()
        
        # Test search performance
        search_queries = [
            "Security",
            "Engineer",
            "Analyst",
            "Python",
            "AWS"
        ]
        
        response_times = []
        for query in search_queries:
            start_time = time.time()
            response = client.get(f"/api/v1/jobs/search?term={query}")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        avg_search_time = statistics.mean(response_times)
        max_search_time = max(response_times)
        
        # Search should be reasonably fast even with larger datasets
        assert avg_search_time < 2.0  # 2 seconds average
        assert max_search_time < 5.0  # 5 seconds maximum
        
        print(f"Job search - Avg: {avg_search_time:.3f}s, Max: {max_search_time:.3f}s")
    
    def test_cost_calculation_performance(self, client, db_session, certification_factory):
        """Test cost calculation endpoint performance"""
        # Create test certifications
        certs = []
        for i in range(20):
            cert = certification_factory(
                name=f"Test Cert {i}",
                cost=500 + (i * 50),
                difficulty=1 + (i % 5)
            )
            certs.append(cert)
        
        # Test various calculation sizes
        test_cases = [
            {"name": "Small", "cert_count": 2},
            {"name": "Medium", "cert_count": 5},
            {"name": "Large", "cert_count": 10},
            {"name": "Extra Large", "cert_count": 15}
        ]
        
        performance_results = {}
        
        for test_case in test_cases:
            cert_ids = [cert.id for cert in certs[:test_case["cert_count"]]]
            calc_data = {
                "name": f"Performance Test - {test_case['name']}",
                "certification_ids": cert_ids,
                "base_currency": "USD",
                "target_currency": "USD",
                "materials_cost": 500.0,
                "training_cost": 1000.0
            }
            
            response_times = []
            for _ in range(5):  # Test each case 5 times
                start_time = time.time()
                response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)
                end_time = time.time()
                
                if response.status_code == 201:
                    response_times.append(end_time - start_time)
            
            if response_times:
                performance_results[test_case["name"]] = {
                    "avg_time": statistics.mean(response_times),
                    "max_time": max(response_times),
                    "cert_count": test_case["cert_count"]
                }
        
        # Verify performance scales reasonably
        for case_name, results in performance_results.items():
            assert results["avg_time"] < 3.0  # 3 seconds average
            assert results["max_time"] < 10.0  # 10 seconds maximum
            print(f"Cost calc {case_name} ({results['cert_count']} certs) - "
                  f"Avg: {results['avg_time']:.3f}s, Max: {results['max_time']:.3f}s")


class TestServicePerformance:
    """Test service layer performance"""
    
    def test_cost_calculator_service_performance(self):
        """Test cost calculator service performance with large datasets"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Mock large certification dataset
        mock_certs = []
        for i in range(100):
            mock_cert = Mock()
            mock_cert.id = i
            mock_cert.cost = 500 + (i * 10)
            mock_cert.difficulty = 1 + (i % 5)
            mock_cert.name = f"Cert {i}"
            mock_certs.append(mock_cert)
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_certs
        
        # Test calculation performance
        calculation_data = {
            'user_id': 'perf_test_user',
            'name': 'Performance Test Calculation',
            'certification_ids': list(range(50)),  # 50 certifications
            'base_currency': 'USD',
            'target_currency': 'USD',
            'include_materials': True,
            'include_training': True,
            'include_retake_probability': True
        }
        
        start_time = time.time()
        with patch.object(service, 'get_exchange_rate', return_value=1.0):
            result = service.create_cost_calculation(calculation_data)
        end_time = time.time()
        
        calculation_time = end_time - start_time
        assert calculation_time < 5.0  # Should complete within 5 seconds
        print(f"Cost calculation service (50 certs): {calculation_time:.3f}s")


class TestMemoryAndResourceUsage:
    """Test memory usage and resource consumption"""
    
    def test_memory_usage_under_load(self):
        """Test memory usage doesn't grow excessively under load"""
        try:
            import psutil
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        except ImportError:
            pytest.skip("psutil not available for memory testing")
        
        # Simulate load
        app = create_app()
        client = TestClient(app)
        
        # Make many requests
        for i in range(100):
            response = client.get("/api/v1/health")
            assert response.status_code == 200
            
            # Force garbage collection periodically
            if i % 20 == 0:
                import gc
                gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for this test)
        assert memory_increase < 100
        print(f"Memory usage - Initial: {initial_memory:.1f}MB, "
              f"Final: {final_memory:.1f}MB, Increase: {memory_increase:.1f}MB")


class TestScalabilityLimits:
    """Test system behavior at scalability limits"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_large_payload_handling(self, client):
        """Test handling of large request payloads"""
        # Test with increasingly large payloads
        payload_sizes = [1000, 5000, 10000]  # Number of certification IDs
        
        for size in payload_sizes:
            large_payload = {
                "name": f"Large Payload Test - {size} items",
                "certification_ids": list(range(size)),
                "base_currency": "USD",
                "target_currency": "USD",
                "materials_cost": 1000.0
            }
            
            start_time = time.time()
            response = client.post("/api/v1/cost-calculator/calculations", json=large_payload)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # Should either succeed or fail gracefully
            assert response.status_code in [201, 413, 422, 400]
            
            # If it succeeds, should complete in reasonable time
            if response.status_code == 201:
                assert response_time < 30.0  # 30 seconds maximum
            
            print(f"Large payload ({size} items): {response.status_code}, {response_time:.3f}s")
    
    def test_concurrent_user_simulation(self, client):
        """Simulate multiple concurrent users"""
        num_users = 20
        requests_per_user = 5
        
        def simulate_user(user_id):
            user_results = []
            for request_num in range(requests_per_user):
                # Mix of different endpoint types
                endpoints = [
                    "/api/v1/health",
                    "/api/v1/jobs/search?term=security",
                    "/api/v1/cost-calculator/exchange-rates"
                ]
                
                endpoint = endpoints[request_num % len(endpoints)]
                
                start_time = time.time()
                response = client.get(endpoint)
                end_time = time.time()
                
                user_results.append({
                    'user_id': user_id,
                    'endpoint': endpoint,
                    'status_code': response.status_code,
                    'response_time': end_time - start_time
                })
            
            return user_results
        
        # Simulate concurrent users
        all_results = []
        with ThreadPoolExecutor(max_workers=num_users) as executor:
            futures = [executor.submit(simulate_user, f"user_{i}") for i in range(num_users)]
            
            for future in as_completed(futures):
                all_results.extend(future.result())
        
        # Analyze results
        total_requests = len(all_results)
        successful_requests = sum(1 for r in all_results if r['status_code'] in [200, 201])
        response_times = [r['response_time'] for r in all_results]
        
        success_rate = successful_requests / total_requests
        avg_response_time = statistics.mean(response_times)
        
        assert success_rate >= 0.90  # 90% success rate under concurrent load
        assert avg_response_time < 5.0  # 5 seconds average response time
        
        print(f"Concurrent users ({num_users} users, {requests_per_user} req/user) - "
              f"Success rate: {success_rate:.2%}, Avg response: {avg_response_time:.3f}s")
