"""Comprehensive tests for Enterprise Budget Optimization functionality.

This module provides extensive test coverage for enterprise budget optimization
features, including team analysis, budget allocation, and ROI calculations.
"""

import pytest
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json
from unittest.mock import Mock, patch

from services.enterprise_budget_optimizer import (
    EnterpriseBudgetOptimizer, BudgetOptimization, TeamAnalysis
)
from models.enterprise import Enterprise, EnterpriseTeam, EnterpriseUser
from models.certification import Certification
from models.career_transition import CareerRole


class TestEnterpriseBudgetOptimizerIntegration:
    """Integration tests for Enterprise Budget Optimizer."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def sample_enterprise(self):
        """Create sample enterprise."""
        enterprise = Mock()
        enterprise.id = 1
        enterprise.name = "Test Corp"
        enterprise.industry = "Technology"
        enterprise.size = "Medium"
        enterprise.is_active = True
        return enterprise
    
    @pytest.fixture
    def sample_teams(self):
        """Create sample enterprise teams."""
        teams = []
        for i in range(3):
            team = Mock()
            team.id = i + 1
            team.name = f"Team {i + 1}"
            team.enterprise_id = 1
            team.target_team_size = 10
            team.required_skills = ["cybersecurity", "cloud_security"]
            team.budget_allocated = 50000.0
            team.is_active = True
            teams.append(team)
        return teams
    
    @pytest.fixture
    def sample_certifications(self):
        """Create sample certifications."""
        certs = []
        cert_names = ["CISSP", "Security+", "AWS Solutions Architect"]
        for i, name in enumerate(cert_names):
            cert = Mock()
            cert.id = i + 1
            cert.name = name
            cert.domain = "cybersecurity" if i < 2 else "cloud"
            cert.cost = 2000 + (i * 500)
            cert.is_active = True
            certs.append(cert)
        return certs
    
    def test_enterprise_budget_optimization_workflow(self, mock_db_session, sample_enterprise, sample_teams):
        """Test complete enterprise budget optimization workflow."""
        # Setup mock database queries
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_enterprise
        mock_db_session.query.return_value.filter.return_value.all.return_value = sample_teams
        
        optimizer = EnterpriseBudgetOptimizer(mock_db_session)
        
        # Mock the team analysis method to return predictable results
        with patch.object(optimizer, '_analyze_team_needs') as mock_analyze:
            mock_team_analyses = [
                TeamAnalysis(
                    team_id=i + 1,
                    team_name=f"Team {i + 1}",
                    current_skills={"cybersecurity": 2},
                    skill_gaps=["cloud_security", "incident_response"],
                    recommended_certifications=[
                        {
                            'certification_name': 'CISSP',
                            'estimated_cost': 2000,
                            'expected_roi': 15.0,
                            'recommended_team_members': 3
                        }
                    ],
                    budget_allocation=30000.0,
                    expected_roi=18.0,
                    priority_score=70.0 + (i * 10)
                ) for i in range(3)
            ]
            mock_analyze.side_effect = mock_team_analyses
            
            # Run optimization
            result = optimizer.optimize_enterprise_budget(
                enterprise_id=1,
                total_budget=100000.0,
                budget_period_months=12,
                strategic_priorities=["cybersecurity", "cloud_security"]
            )
            
            # Verify results
            assert isinstance(result, BudgetOptimization)
            assert result.total_budget == 100000.0
            assert isinstance(result.optimized_allocation, dict)
            assert result.cost_savings >= 0
            assert isinstance(result.roi_projections, dict)
            assert isinstance(result.recommended_certifications, list)
            assert isinstance(result.team_priorities, list)
            assert isinstance(result.timeline_recommendations, dict)
            assert isinstance(result.risk_assessment, dict)
            
            # Verify budget allocation doesn't exceed total
            total_allocated = sum(result.optimized_allocation.values())
            assert total_allocated <= result.total_budget
    
    def test_team_analysis_comprehensive(self, mock_db_session, sample_teams, sample_certifications):
        """Test comprehensive team analysis functionality."""
        optimizer = EnterpriseBudgetOptimizer(mock_db_session)
        
        # Mock database queries for certifications
        mock_db_session.query.return_value.filter.return_value.all.return_value = sample_certifications
        
        # Mock team members
        team_members = []
        for i in range(5):
            member = Mock()
            member.id = i + 1
            member.team_id = 1
            member.certifications = [1, 2] if i < 2 else []  # Some members have certs
            member.role_id = 1
            member.is_active = True
            team_members.append(member)
        
        mock_db_session.query.return_value.filter.return_value.all.return_value = team_members
        
        # Mock role query
        mock_role = Mock()
        mock_role.id = 1
        mock_role.domain = "cybersecurity"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_role
        
        # Analyze team needs
        analysis = optimizer._analyze_team_needs(
            sample_teams[0], 
            ["cybersecurity", "cloud_security"]
        )
        
        assert isinstance(analysis, TeamAnalysis)
        assert analysis.team_id == sample_teams[0].id
        assert analysis.team_name == sample_teams[0].name
        assert isinstance(analysis.current_skills, dict)
        assert isinstance(analysis.skill_gaps, list)
        assert isinstance(analysis.recommended_certifications, list)
        assert analysis.budget_allocation >= 0
        assert analysis.expected_roi >= 0
        assert 0 <= analysis.priority_score <= 100
    
    def test_budget_allocation_algorithm(self, mock_db_session):
        """Test budget allocation optimization algorithm."""
        optimizer = EnterpriseBudgetOptimizer(mock_db_session)
        
        # Create test team analyses with different priorities
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="High Priority Team",
                current_skills={}, skill_gaps=["cybersecurity", "cloud"],
                recommended_certifications=[], budget_allocation=40000,
                expected_roi=25.0, priority_score=90.0
            ),
            TeamAnalysis(
                team_id=2, team_name="Medium Priority Team",
                current_skills={}, skill_gaps=["network"],
                recommended_certifications=[], budget_allocation=30000,
                expected_roi=15.0, priority_score=60.0
            ),
            TeamAnalysis(
                team_id=3, team_name="Low Priority Team",
                current_skills={}, skill_gaps=["compliance"],
                recommended_certifications=[], budget_allocation=20000,
                expected_roi=10.0, priority_score=30.0
            )
        ]
        
        # Test with sufficient budget
        allocation = optimizer._optimize_budget_allocation(team_analyses, 100000, 12)
        
        assert isinstance(allocation, dict)
        assert len(allocation) == 3
        assert sum(allocation.values()) <= 100000
        
        # Higher priority team should get more allocation
        assert allocation["High Priority Team"] >= allocation["Medium Priority Team"]
        assert allocation["Medium Priority Team"] >= allocation["Low Priority Team"]
        
        # Test with limited budget
        limited_allocation = optimizer._optimize_budget_allocation(team_analyses, 50000, 12)
        
        assert sum(limited_allocation.values()) <= 50000
        # Should still prioritize high-priority teams
        if "High Priority Team" in limited_allocation:
            assert limited_allocation["High Priority Team"] > 0
    
    def test_roi_projections_calculation(self, mock_db_session):
        """Test ROI projections calculation accuracy."""
        optimizer = EnterpriseBudgetOptimizer(mock_db_session)
        
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="Team A",
                current_skills={}, skill_gaps=[],
                recommended_certifications=[], budget_allocation=30000,
                expected_roi=20.0, priority_score=80.0
            ),
            TeamAnalysis(
                team_id=2, team_name="Team B",
                current_skills={}, skill_gaps=[],
                recommended_certifications=[], budget_allocation=20000,
                expected_roi=15.0, priority_score=60.0
            )
        ]
        
        optimized_allocation = {"Team A": 25000, "Team B": 15000}
        
        projections = optimizer._calculate_roi_projections(team_analyses, optimized_allocation)
        
        assert isinstance(projections, dict)
        assert 'one_year_roi' in projections
        assert 'three_year_roi' in projections
        assert 'five_year_roi' in projections
        assert 'break_even_months' in projections
        
        # Verify ROI progression
        assert projections['three_year_roi'] > projections['one_year_roi']
        assert projections['five_year_roi'] > projections['three_year_roi']
        assert projections['break_even_months'] > 0
    
    def test_risk_assessment_comprehensive(self, mock_db_session):
        """Test comprehensive risk assessment functionality."""
        optimizer = EnterpriseBudgetOptimizer(mock_db_session)
        
        # Test high-risk scenario (high concentration, low ROI)
        high_risk_teams = [
            TeamAnalysis(
                team_id=1, team_name="Team A",
                current_skills={}, skill_gaps=["cybersecurity", "cloud", "network"],
                recommended_certifications=[], budget_allocation=30000,
                expected_roi=8.0, priority_score=50.0
            )
        ]
        
        high_risk_allocation = {"Team A": 45000}  # 90% of budget to one team
        
        risk_assessment = optimizer._assess_optimization_risks(
            high_risk_teams, high_risk_allocation, 50000
        )
        
        assert isinstance(risk_assessment, dict)
        assert 'high_risk_factors' in risk_assessment
        assert 'medium_risk_factors' in risk_assessment
        assert 'low_risk_factors' in risk_assessment
        assert 'mitigation_strategies' in risk_assessment
        assert 'overall_risk_score' in risk_assessment
        
        # High concentration should trigger high risk
        assert len(risk_assessment['high_risk_factors']) > 0
        assert risk_assessment['overall_risk_score'] > 30
        assert len(risk_assessment['mitigation_strategies']) > 0
        
        # Test low-risk scenario
        low_risk_teams = [
            TeamAnalysis(
                team_id=i, team_name=f"Team {i}",
                current_skills={}, skill_gaps=["cybersecurity"],
                recommended_certifications=[], budget_allocation=10000,
                expected_roi=20.0, priority_score=70.0
            ) for i in range(5)
        ]
        
        low_risk_allocation = {f"Team {i}": 10000 for i in range(5)}
        
        low_risk_assessment = optimizer._assess_optimization_risks(
            low_risk_teams, low_risk_allocation, 50000
        )
        
        # Should have lower risk score
        assert low_risk_assessment['overall_risk_score'] < risk_assessment['overall_risk_score']
    
    def test_certification_recommendations_consolidation(self, mock_db_session):
        """Test certification recommendations consolidation across teams."""
        optimizer = EnterpriseBudgetOptimizer(mock_db_session)
        
        # Create team analyses with overlapping certification needs
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="Security Team",
                current_skills={}, skill_gaps=[],
                recommended_certifications=[
                    {
                        'certification_name': 'CISSP',
                        'recommended_team_members': 5,
                        'expected_roi': 18.0,
                        'estimated_cost': 2000
                    },
                    {
                        'certification_name': 'Security+',
                        'recommended_team_members': 3,
                        'expected_roi': 10.0,
                        'estimated_cost': 1500
                    }
                ],
                budget_allocation=30000, expected_roi=20.0, priority_score=80.0
            ),
            TeamAnalysis(
                team_id=2, team_name="Cloud Team",
                current_skills={}, skill_gaps=[],
                recommended_certifications=[
                    {
                        'certification_name': 'CISSP',  # Same as Security Team
                        'recommended_team_members': 3,
                        'expected_roi': 15.0,
                        'estimated_cost': 2000
                    },
                    {
                        'certification_name': 'AWS Solutions Architect',
                        'recommended_team_members': 4,
                        'expected_roi': 20.0,
                        'estimated_cost': 2500
                    }
                ],
                budget_allocation=25000, expected_roi=18.0, priority_score=70.0
            )
        ]
        
        recommendations = optimizer._generate_certification_recommendations(team_analyses)
        
        assert isinstance(recommendations, list)
        assert len(recommendations) <= 15  # Should be limited
        
        # Find CISSP recommendation (should be consolidated)
        cissp_rec = next((r for r in recommendations if r['certification_name'] == 'CISSP'), None)
        assert cissp_rec is not None
        assert cissp_rec['total_recommended_members'] == 8  # 5 + 3
        assert cissp_rec['teams_interested'] == 2
        assert cissp_rec['total_investment'] == 16000  # (5 + 3) * 2000
        
        # Verify sorting by impact
        if len(recommendations) > 1:
            for i in range(len(recommendations) - 1):
                current_impact = recommendations[i]['average_roi'] * recommendations[i]['total_recommended_members']
                next_impact = recommendations[i + 1]['average_roi'] * recommendations[i + 1]['total_recommended_members']
                assert current_impact >= next_impact
    
    def test_timeline_recommendations_logic(self, mock_db_session):
        """Test timeline recommendations generation logic."""
        optimizer = EnterpriseBudgetOptimizer(mock_db_session)
        
        # Create teams with varying priority scores
        team_analyses = []
        for i in range(10):
            team_analyses.append(
                TeamAnalysis(
                    team_id=i + 1, team_name=f"Team {i + 1}",
                    current_skills={}, skill_gaps=["cybersecurity"],
                    recommended_certifications=[], budget_allocation=10000,
                    expected_roi=15.0, priority_score=90 - (i * 8)  # Decreasing priority
                )
            )
        
        timeline = optimizer._generate_timeline_recommendations(team_analyses, 12)
        
        assert isinstance(timeline, dict)
        assert 'immediate_actions' in timeline
        assert 'short_term_goals' in timeline
        assert 'medium_term_goals' in timeline
        assert 'long_term_goals' in timeline
        
        # Verify team distribution
        assert len(timeline['immediate_actions']) <= 2  # Top 2 priority teams
        assert len(timeline['short_term_goals']) <= 3   # Next 3 teams
        assert len(timeline['medium_term_goals']) <= 3  # Next 3 teams
        
        # Verify highest priority teams are in immediate actions
        if timeline['immediate_actions']:
            immediate_team = timeline['immediate_actions'][0]['team']
            assert immediate_team in ["Team 1", "Team 2"]  # Highest priority teams
        
        # Verify timeline item structure
        for phase in timeline.values():
            for item in phase:
                assert 'team' in item
                assert 'action' in item
                assert 'budget' in item
                assert isinstance(item['budget'], (int, float))
                assert item['budget'] >= 0
    
    def test_budget_report_generation(self, mock_db_session, sample_enterprise):
        """Test comprehensive budget report generation."""
        optimizer = EnterpriseBudgetOptimizer(mock_db_session)
        
        # Mock enterprise query
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_enterprise
        
        # Create sample optimization result
        optimization_result = BudgetOptimization(
            total_budget=100000,
            optimized_allocation={"Team A": 40000, "Team B": 30000, "Team C": 20000},
            cost_savings=15000,
            roi_projections={
                'one_year_roi': 22.0,
                'three_year_roi': 55.0,
                'five_year_roi': 110.0,
                'break_even_months': 8
            },
            recommended_certifications=[
                {
                    'certification_name': 'CISSP',
                    'total_recommended_members': 10,
                    'average_roi': 18.0
                }
            ],
            team_priorities=[
                {
                    'team_name': 'Team A',
                    'priority_score': 85.0,
                    'urgency': 'high'
                }
            ],
            timeline_recommendations={
                'immediate_actions': [{'team': 'Team A', 'action': 'Begin training'}]
            },
            risk_assessment={
                'overall_risk_score': 25.0,
                'high_risk_factors': [],
                'mitigation_strategies': ['Monitor progress quarterly']
            }
        )
        
        report = optimizer.generate_budget_report(1, optimization_result)
        
        assert isinstance(report, dict)
        assert 'enterprise_name' in report
        assert 'report_date' in report
        assert 'executive_summary' in report
        assert 'detailed_allocation' in report
        assert 'roi_analysis' in report
        assert 'certification_recommendations' in report
        assert 'team_priorities' in report
        assert 'implementation_timeline' in report
        assert 'risk_assessment' in report
        assert 'key_insights' in report
        assert 'next_steps' in report
        
        # Verify executive summary
        exec_summary = report['executive_summary']
        assert exec_summary['total_budget'] == 100000
        assert exec_summary['projected_cost_savings'] == 15000
        assert exec_summary['expected_roi'] == 22.0
        assert exec_summary['teams_covered'] == 3
        assert exec_summary['top_certification_recommendation'] == 'CISSP'
        
        # Verify insights and next steps are meaningful
        assert len(report['key_insights']) > 0
        assert len(report['next_steps']) > 0
        
        for insight in report['key_insights']:
            assert isinstance(insight, str)
            assert len(insight) > 20  # Meaningful insight
        
        for step in report['next_steps']:
            assert isinstance(step, str)
            assert len(step) > 10  # Actionable step
