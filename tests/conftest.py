"""Enhanced test configuration and fixtures for comprehensive testing"""
import pytest
import asyncio
import random
import string
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Generator
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
import sys
import os
from pathlib import Path
import logging
import json
from decimal import Decimal

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# Import Base and ensure all models are imported
from database import Base
import models  # This registers all models with Base.metadata

# Get database URL from environment
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///test.db')
logger.info(f"Using test database: {DATABASE_URL}")

# Create test engine with optimized config for testing
test_engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Enable connection health checks
    pool_size=10,        # Increase pool size for concurrent test connections
    max_overflow=20,     # Allow more overflow connections
    pool_timeout=30,     # Increase timeout for connection acquisition
    echo=False,          # Disable SQL logging for cleaner test output
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# Create session factory bound to test engine
TestingSessionLocal = sessionmaker(
    bind=test_engine,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False  # Important for test scenarios
)

# ============================================================================
# DATABASE FIXTURES
# ============================================================================

@pytest.fixture(scope="session", autouse=True)
def setup_test_database():
    """Create test database tables once for all tests"""
    try:
        logger.info("Creating test database tables")
        Base.metadata.create_all(bind=test_engine)
        logger.info("Created database tables successfully")
        yield
    finally:
        logger.info("Test session complete")
        # Optionally drop tables after all tests
        # Base.metadata.drop_all(bind=test_engine)

@pytest.fixture(autouse=True)
def cleanup_tables():
    """Clean up tables after each test"""
    yield  # Run the test
    logger.debug("Cleaning up tables after test")
    session = TestingSessionLocal()
    try:
        # Get all tables in reverse order to handle dependencies
        tables = reversed(Base.metadata.sorted_tables)

        # Delete all data from each table
        for table in tables:
            logger.debug(f"Cleaning table: {table.name}")
            session.execute(text(f"DELETE FROM {table.name}"))

        session.commit()
        logger.debug("Successfully cleaned up tables")
    except Exception as e:
        logger.error(f"Error cleaning up tables: {e}")
        session.rollback()
        raise
    finally:
        session.close()

@pytest.fixture
def db_session() -> Generator[Session, None, None]:
    """Provide test database session with automatic rollback"""
    session = TestingSessionLocal()
    try:
        logger.debug("Created new test database session")
        yield session
    except Exception as e:
        logger.error(f"Database session error: {e}")
        session.rollback()
        raise
    finally:
        logger.debug("Closing test database session")
        session.close()

@pytest.fixture
def db_transaction(db_session: Session) -> Generator[Session, None, None]:
    """Provide database session with transaction that rolls back after test"""
    transaction = db_session.begin()
    try:
        yield db_session
    finally:
        transaction.rollback()

# ============================================================================
# UTILITY FIXTURES
# ============================================================================

@pytest.fixture
def random_string() -> str:
    """Generate random string for testing"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=10))

@pytest.fixture
def random_email() -> str:
    """Generate random email for testing"""
    username = ''.join(random.choices(string.ascii_lowercase, k=8))
    domain = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"{username}@{domain}.com"

@pytest.fixture
def future_date() -> datetime:
    """Generate future date for testing"""
    return datetime.utcnow() + timedelta(days=random.randint(1, 365))

@pytest.fixture
def past_date() -> datetime:
    """Generate past date for testing"""
    return datetime.utcnow() - timedelta(days=random.randint(1, 365))

@pytest.fixture
def mock_requests():
    """Mock requests module for external API testing"""
    with patch('requests.get') as mock_get, \
         patch('requests.post') as mock_post, \
         patch('requests.put') as mock_put, \
         patch('requests.delete') as mock_delete:

        # Configure default responses
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {"status": "success"}
        mock_post.return_value.status_code = 201
        mock_post.return_value.json.return_value = {"id": 1, "status": "created"}
        mock_put.return_value.status_code = 200
        mock_put.return_value.json.return_value = {"status": "updated"}
        mock_delete.return_value.status_code = 204

        yield {
            'get': mock_get,
            'post': mock_post,
            'put': mock_put,
            'delete': mock_delete
        }

# ============================================================================
# MODEL FACTORY FIXTURES
# ============================================================================

@pytest.fixture
def organization_factory(db_session: Session):
    """Factory for creating Organization instances"""
    def _create_organization(**kwargs):
        from models.certification import Organization
        defaults = {
            'name': f'Test Org {random.randint(1000, 9999)}',
            'country': 'United States',
            'description': 'Test organization for certification testing',
            'website': 'https://example.com',
            'created_at': datetime.utcnow()
        }
        defaults.update(kwargs)
        org = Organization(**defaults)
        db_session.add(org)
        db_session.commit()
        return org
    return _create_organization

@pytest.fixture
def certification_factory(db_session: Session, organization_factory):
    """Factory for creating Certification instances"""
    def _create_certification(**kwargs):
        from models.certification import Certification

        # Create organization if not provided
        if 'organization_id' not in kwargs:
            org = organization_factory()
            kwargs['organization_id'] = org.id

        defaults = {
            'name': f'Test Cert {random.randint(1000, 9999)}',
            'category': 'Security',
            'domain': 'Network Security',
            'level': 'Entry Level',
            'difficulty': random.randint(1, 5),
            'cost': random.randint(200, 1000),
            'description': 'Test certification for security professionals',
            'validity_period': 36,
            'exam_code': f'TEST-{random.randint(100, 999)}',
            'focus': 'Network',
            'custom_hours': random.randint(20, 100),
            'created_at': datetime.utcnow()
        }
        defaults.update(kwargs)
        cert = Certification(**defaults)
        db_session.add(cert)
        db_session.commit()
        return cert
    return _create_certification

@pytest.fixture
def user_experience_factory(db_session: Session):
    """Factory for creating UserExperience instances"""
    def _create_user_experience(**kwargs):
        from models.user_experience import UserExperience
        defaults = {
            'user_id': f'user_{random.randint(1000, 9999)}',
            'years_experience': random.randint(0, 20),
            'user_role': 'Security Analyst',
            'desired_role': 'Security Engineer',
            'expertise_areas': ['Network Security', 'Cloud Security'],
            'preferred_learning_style': random.choice(['Visual', 'Reading', 'Hands-on', 'Mixed']),
            'study_time_available': random.randint(5, 40),
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        defaults.update(kwargs)
        user_exp = UserExperience(**defaults)
        db_session.add(user_exp)
        db_session.commit()
        return user_exp
    return _create_user_experience

@pytest.fixture
def security_domain_factory(db_session: Session):
    """Factory for creating SecurityDomain instances"""
    def _create_security_domain(**kwargs):
        from models.security_domain import SecurityDomain
        defaults = {
            'name': f'Test Domain {random.randint(1000, 9999)}',
            'description': 'Test security domain for certification paths',
            'created_at': datetime.utcnow()
        }
        defaults.update(kwargs)
        domain = SecurityDomain(**defaults)
        db_session.add(domain)
        db_session.commit()
        return domain
    return _create_security_domain

@pytest.fixture
def cost_calculation_factory(db_session: Session):
    """Factory for creating CostCalculation instances"""
    def _create_cost_calculation(**kwargs):
        from models.cost_calculation import CostCalculation
        defaults = {
            'user_id': f'user_{random.randint(1000, 9999)}',
            'name': f'Test Calculation {random.randint(1000, 9999)}',
            'certification_ids': [1, 2, 3],
            'exam_fees_total': Decimal(str(random.randint(500, 2000))),
            'materials_cost': Decimal(str(random.randint(100, 500))),
            'training_cost': Decimal(str(random.randint(0, 1000))),
            'retake_cost': Decimal(str(random.randint(0, 200))),
            'additional_costs': Decimal(str(random.randint(0, 300))),
            'base_currency': 'USD',
            'target_currency': 'USD',
            'exchange_rate_used': Decimal('1.0'),
            'created_at': datetime.utcnow()
        }
        defaults.update(kwargs)
        calc = CostCalculation(**defaults)
        calc.update_totals()  # Calculate totals
        db_session.add(calc)
        db_session.commit()
        return calc
    return _create_cost_calculation

# ============================================================================
# API TESTING FIXTURES
# ============================================================================

@pytest.fixture
def api_client():
    """Create FastAPI test client"""
    from fastapi.testclient import TestClient
    from api.app import create_app

    app = create_app()
    return TestClient(app)

@pytest.fixture
def authenticated_headers():
    """Mock authentication headers for API testing"""
    return {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
    }

@pytest.fixture
def sample_api_data():
    """Sample data for API testing"""
    return {
        'career_path_request': {
            'completed_certifications': ['A+', 'Network+'],
            'interests': ['Network Security', 'Cloud Security'],
            'years_experience': 5,
            'current_role': 'Security Analyst',
            'target_role': 'Security Engineer',
            'learning_style': 'Mixed',
            'study_hours': 10
        },
        'cost_calculation_request': {
            'name': 'Test Calculation',
            'certification_ids': [1, 2],
            'base_currency': 'USD',
            'target_currency': 'EUR',
            'materials_cost': 500.0
        }
    }

# ============================================================================
# AGENT 3 - ENTERPRISE ANALYTICS ENGINE FIXTURES
# ============================================================================

@pytest.fixture
def enterprise_organization_factory(db_session: Session):
    """Factory for creating EnterpriseOrganization instances"""
    def _create_enterprise_organization(**kwargs):
        try:
            from models.enterprise import EnterpriseOrganization
            defaults = {
                'name': f'Enterprise Corp {random.randint(1000, 9999)}',
                'display_name': f'Enterprise {random.randint(1000, 9999)}',
                'slug': f'enterprise-{random.randint(1000, 9999)}',
                'domain': f'enterprise{random.randint(1000, 9999)}.com',
                'industry': random.choice(['Technology', 'Finance', 'Healthcare', 'Manufacturing']),
                'employee_count': random.randint(100, 10000),
                'subscription_tier': random.choice(['basic', 'professional', 'enterprise']),
                'license_count': random.randint(50, 1000),
                'is_active': True,
                'sso_settings': {},
                'features_enabled': {
                    'compliance_automation': True,
                    'data_intelligence': True,
                    'sso_integration': True
                }
            }
            defaults.update(kwargs)
            org = EnterpriseOrganization(**defaults)
            db_session.add(org)
            db_session.commit()
            return org
        except ImportError:
            return Mock(**kwargs)
    return _create_enterprise_organization

@pytest.fixture
def enterprise_user_factory(db_session: Session, enterprise_organization_factory):
    """Factory for creating EnterpriseUser instances"""
    def _create_enterprise_user(**kwargs):
        try:
            from models.enterprise import EnterpriseUser, UserRole

            # Create organization if not provided
            if 'organization_id' not in kwargs:
                org = enterprise_organization_factory()
                kwargs['organization_id'] = org.id

            defaults = {
                'user_id': f'user_{random.randint(10000, 99999)}',
                'email': f'user{random.randint(1000, 9999)}@enterprise.com',
                'first_name': f'User{random.randint(100, 999)}',
                'last_name': f'Test{random.randint(100, 999)}',
                'role': random.choice(list(UserRole)),
                'job_title': random.choice(['Security Analyst', 'Security Engineer', 'CISO', 'Manager']),
                'salary_range': f'{random.randint(60, 200)}000-{random.randint(80, 250)}000',
                'is_active': True,
                'certifications_completed': random.sample(['CISSP', 'Security+', 'CEH', 'CISM'], k=random.randint(0, 3))
            }
            defaults.update(kwargs)
            user = EnterpriseUser(**defaults)
            db_session.add(user)
            db_session.commit()
            return user
        except ImportError:
            return Mock(**kwargs)
    return _create_enterprise_user

@pytest.fixture
def compliance_requirement_factory(db_session: Session, enterprise_organization_factory):
    """Factory for creating ComplianceRequirement instances"""
    def _create_compliance_requirement(**kwargs):
        try:
            from models.compliance import ComplianceRequirement, ComplianceFramework, RiskLevel

            # Create organization if not provided
            if 'organization_id' not in kwargs:
                org = enterprise_organization_factory()
                kwargs['organization_id'] = org.id

            framework = kwargs.get('framework', random.choice(list(ComplianceFramework)))
            req_num = random.randint(100, 999)

            defaults = {
                'framework': framework,
                'requirement_id': f'{framework.value.upper()}-{req_num}',
                'title': f'{framework.value.upper()} Requirement {req_num}',
                'description': f'Test {framework.value} compliance requirement',
                'risk_level': random.choice(list(RiskLevel)),
                'automated_check': random.choice([True, False]),
                'check_frequency': random.choice(['daily', 'weekly', 'monthly', 'quarterly'])
            }
            defaults.update(kwargs)
            req = ComplianceRequirement(**defaults)
            db_session.add(req)
            db_session.commit()
            return req
        except ImportError:
            return Mock(**kwargs)
    return _create_compliance_requirement

@pytest.fixture
def compliance_assessment_factory(db_session: Session, compliance_requirement_factory, enterprise_user_factory):
    """Factory for creating ComplianceAssessment instances"""
    def _create_compliance_assessment(**kwargs):
        try:
            from models.compliance import ComplianceAssessment, ComplianceStatus

            # Create requirement if not provided
            if 'requirement_id' not in kwargs:
                req = compliance_requirement_factory()
                kwargs['requirement_id'] = req.id
                kwargs['organization_id'] = req.organization_id

            # Create assessor if not provided
            if 'assessor_id' not in kwargs:
                user = enterprise_user_factory()
                kwargs['assessor_id'] = user.user_id

            defaults = {
                'assessment_date': datetime.utcnow(),
                'status': random.choice(list(ComplianceStatus)),
                'score': random.uniform(0, 100),
                'findings': f'Test assessment findings {random.randint(100, 999)}',
                'evidence_provided': ['Test evidence 1', 'Test evidence 2']
            }
            defaults.update(kwargs)
            assessment = ComplianceAssessment(**defaults)
            db_session.add(assessment)
            db_session.commit()
            return assessment
        except ImportError:
            return Mock(**kwargs)
    return _create_compliance_assessment

@pytest.fixture
def sample_enterprise_data(enterprise_organization_factory, enterprise_user_factory, compliance_requirement_factory):
    """Create comprehensive sample enterprise data for testing"""
    # Create organization
    org = enterprise_organization_factory(
        name="Test Enterprise Corp",
        domain="testenterprise.com",
        industry="Technology",
        employee_count=500
    )

    # Create users with different roles
    users = {}
    try:
        from models.enterprise import UserRole
        role_mapping = {
            'super_admin': UserRole.SUPER_ADMIN,
            'org_admin': UserRole.ORG_ADMIN,
            'manager': UserRole.MANAGER,
            'compliance_officer': UserRole.COMPLIANCE_OFFICER,
            'learner': UserRole.LEARNER
        }

        for role_name, role_enum in role_mapping.items():
            users[role_name] = enterprise_user_factory(
                organization_id=org.id,
                role=role_enum,
                email=f'{role_name}@testenterprise.com'
            )
    except ImportError:
        users = {role: Mock() for role in ['super_admin', 'org_admin', 'manager', 'compliance_officer', 'learner']}

    # Create compliance requirements
    requirements = []
    try:
        from models.compliance import ComplianceFramework
        frameworks = [ComplianceFramework.GDPR, ComplianceFramework.HIPAA, ComplianceFramework.SOX]

        for framework in frameworks:
            for i in range(3):  # 3 requirements per framework
                req = compliance_requirement_factory(
                    organization_id=org.id,
                    framework=framework
                )
                requirements.append(req)
    except ImportError:
        requirements = [Mock() for _ in range(9)]

    return {
        'organization': org,
        'users': users,
        'requirements': requirements
    }

@pytest.fixture
def mock_compliance_service():
    """Create mock compliance service for testing"""
    service = Mock()
    service.create_compliance_requirement.return_value = Mock(id=1, title="Test Requirement")
    service.assess_compliance_requirement.return_value = Mock(id=1, status="compliant")
    service.generate_gdpr_report.return_value = Mock(id=1, overall_score=85.0)
    service.generate_hipaa_report.return_value = Mock(id=2, overall_score=90.0)
    service.generate_sox_report.return_value = Mock(id=3, overall_score=78.0)
    return service

@pytest.fixture
def mock_data_intelligence_service():
    """Create mock data intelligence service for testing"""
    service = Mock()
    service.generate_salary_intelligence.return_value = {
        'report_id': 'salary_intel_123',
        'sample_size': 100,
        'salary_statistics': {'mean': 95000, 'median': 90000}
    }
    service.analyze_skills_gap.return_value = {
        'report_id': 'skills_gap_123',
        'identified_gaps': {'critical_gaps': [], 'moderate_gaps': []}
    }
    return service

@pytest.fixture
def mock_enterprise_auth_service():
    """Create mock enterprise auth service for testing"""
    service = Mock()
    service.authenticate_user.return_value = {
        'access_token': 'test_token_123',
        'user': {'email': '<EMAIL>', 'role': 'manager'},
        'permissions': ['org_view', 'user_view']
    }
    service.check_permission.return_value = True
    service.verify_access_token.return_value = {'user_id': 'test_user', 'email': '<EMAIL>'}
    return service