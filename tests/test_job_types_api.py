"""Unit tests for Job Types API endpoints.

This module provides comprehensive unit tests for the job types API endpoints
following PEP 8, 257, and 484 standards with 95%+ test coverage.
"""

import pytest
from typing import Dict, Any, List
from unittest.mock import Mock, patch
from datetime import datetime

from fastapi.testclient import TestClient
from fastapi import status

from main import app
from services.job_types_crud import JobTypeCRUDService, JobTypeResponse
from services.base_crud import ValidationError, NotFoundError, ConflictError
from models.user import User


class TestJobTypesAPI:
    """Test cases for Job Types API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_user(self):
        """Create mock user."""
        user = Mock(spec=User)
        user.id = "test_user_123"
        user.email = "<EMAIL>"
        user.is_active = True
        return user
    
    @pytest.fixture
    def sample_job_type_response(self) -> JobTypeResponse:
        """Sample job type response for testing."""
        return JobTypeResponse(
            id=1,
            title="Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="intermediate",
            description="Security engineer role",
            responsibilities=["Implement security controls"],
            required_skills=["Network Security", "Python"],
            preferred_skills=["Cloud Security"],
            min_years_experience=2,
            max_years_experience=5,
            education_requirements=["Bachelor's degree"],
            required_certifications=["Security+"],
            preferred_certifications=["CISSP"],
            salary_min=80000.0,
            salary_max=120000.0,
            salary_currency="USD",
            career_progression_from=[],
            career_progression_to=[2, 3],
            demand_level="high",
            remote_friendly=True,
            tags=["security", "engineering"],
            is_active=True,
            created_at="2024-01-07T10:00:00Z",
            updated_at="2024-01-07T10:00:00Z"
        )
    
    @pytest.fixture
    def sample_job_type_create_data(self) -> Dict[str, Any]:
        """Sample job type creation data."""
        return {
            "title": "Security Engineer",
            "security_area": "Security Architecture and Engineering",
            "job_family": "security_engineer",
            "seniority_level": "intermediate",
            "description": "Security engineer role",
            "required_skills": ["Network Security", "Python"],
            "salary_min": 80000,
            "salary_max": 120000,
            "demand_level": "high"
        }
    
    def test_create_job_type_success(self, client, mock_user, sample_job_type_create_data, sample_job_type_response):
        """Test successful job type creation."""
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            with patch('api.v1.job_types.get_job_type_service') as mock_service:
                mock_service.return_value.create.return_value = sample_job_type_response
                
                response = client.post("/api/v1/job-types/", json=sample_job_type_create_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["id"] == 1
        assert data["title"] == "Security Engineer"
        assert data["security_area"] == "Security Architecture and Engineering"
    
    def test_create_job_type_validation_error(self, client, mock_user, sample_job_type_create_data):
        """Test job type creation with validation error."""
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            with patch('api.v1.job_types.get_job_type_service') as mock_service:
                mock_service.return_value.create.side_effect = ValidationError(
                    "Title too short",
                    details={"field": "title"}
                )
                
                response = client.post("/api/v1/job-types/", json=sample_job_type_create_data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert data["detail"]["type"] == "validation_error"
        assert "Title too short" in data["detail"]["message"]
    
    def test_create_job_type_conflict_error(self, client, mock_user, sample_job_type_create_data):
        """Test job type creation with conflict error."""
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            with patch('api.v1.job_types.get_job_type_service') as mock_service:
                mock_service.return_value.create.side_effect = ConflictError(
                    "Job type already exists",
                    details={"existing_id": 123}
                )
                
                response = client.post("/api/v1/job-types/", json=sample_job_type_create_data)
        
        assert response.status_code == status.HTTP_409_CONFLICT
        data = response.json()
        assert data["detail"]["type"] == "conflict_error"
        assert "already exists" in data["detail"]["message"]
    
    def test_create_job_type_invalid_data(self, client, mock_user):
        """Test job type creation with invalid data."""
        invalid_data = {
            "title": "SE",  # Too short
            "security_area": "Invalid Area",
            "job_family": "security_engineer",
            "seniority_level": "intermediate"
        }
        
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            response = client.post("/api/v1/job-types/", json=invalid_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_get_job_type_success(self, client, sample_job_type_response):
        """Test successful job type retrieval."""
        with patch('api.v1.job_types.get_job_type_service') as mock_service:
            mock_service.return_value.get.return_value = sample_job_type_response
            
            response = client.get("/api/v1/job-types/1")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == 1
        assert data["title"] == "Security Engineer"
    
    def test_get_job_type_not_found(self, client):
        """Test job type retrieval when not found."""
        with patch('api.v1.job_types.get_job_type_service') as mock_service:
            mock_service.return_value.get.return_value = None
            
            response = client.get("/api/v1/job-types/999")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert data["detail"]["type"] == "not_found_error"
    
    def test_list_job_types_success(self, client, sample_job_type_response):
        """Test successful job types listing."""
        mock_response = {
            "data": [sample_job_type_response.dict()],
            "pagination": {
                "page": 1,
                "page_size": 20,
                "total_items": 1,
                "total_pages": 1,
                "has_next": False,
                "has_previous": False
            },
            "total_count": 1,
            "filters_applied": {},
            "aggregations": {}
        }
        
        with patch('api.v1.job_types.get_job_type_service') as mock_service:
            mock_service.return_value.list_with_filters.return_value = mock_response
            
            response = client.get("/api/v1/job-types/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "data" in data
        assert "pagination" in data
        assert len(data["data"]) == 1
    
    def test_list_job_types_with_filters(self, client, sample_job_type_response):
        """Test job types listing with filters."""
        mock_response = {
            "data": [sample_job_type_response.dict()],
            "pagination": {"page": 1, "total_items": 1},
            "total_count": 1,
            "filters_applied": {},
            "aggregations": {}
        }
        
        with patch('api.v1.job_types.get_job_type_service') as mock_service:
            mock_service.return_value.list_with_filters.return_value = mock_response
            
            response = client.get(
                "/api/v1/job-types/",
                params={
                    "security_area": ["Security Architecture and Engineering"],
                    "seniority_level": ["intermediate"],
                    "salary_min": 80000,
                    "remote_friendly": True,
                    "page": 1,
                    "page_size": 10
                }
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "data" in data
    
    def test_update_job_type_success(self, client, mock_user, sample_job_type_response):
        """Test successful job type update."""
        update_data = {
            "title": "Senior Security Engineer",
            "salary_min": 90000
        }
        
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            with patch('api.v1.job_types.get_job_type_service') as mock_service:
                mock_service.return_value.update.return_value = sample_job_type_response
                
                response = client.put("/api/v1/job-types/1", json=update_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == 1
    
    def test_update_job_type_not_found(self, client, mock_user):
        """Test job type update when not found."""
        update_data = {"title": "New Title"}
        
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            with patch('api.v1.job_types.get_job_type_service') as mock_service:
                mock_service.return_value.update.side_effect = NotFoundError(
                    "Job type not found"
                )
                
                response = client.put("/api/v1/job-types/999", json=update_data)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert data["detail"]["type"] == "not_found_error"
    
    def test_delete_job_type_success(self, client, mock_user):
        """Test successful job type deletion."""
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            with patch('api.v1.job_types.get_job_type_service') as mock_service:
                mock_service.return_value.delete.return_value = True
                
                response = client.delete("/api/v1/job-types/1")
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
    
    def test_delete_job_type_not_found(self, client, mock_user):
        """Test job type deletion when not found."""
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            with patch('api.v1.job_types.get_job_type_service') as mock_service:
                mock_service.return_value.delete.side_effect = NotFoundError(
                    "Job type not found"
                )
                
                response = client.delete("/api/v1/job-types/999")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_bulk_create_job_types_success(self, client, mock_user, sample_job_type_create_data, sample_job_type_response):
        """Test successful bulk job type creation."""
        bulk_data = [sample_job_type_create_data, sample_job_type_create_data]
        
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            with patch('api.v1.job_types.get_job_type_service') as mock_service:
                mock_service.return_value.bulk_create.return_value = [
                    sample_job_type_response, sample_job_type_response
                ]
                
                response = client.post("/api/v1/job-types/bulk", json=bulk_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert len(data) == 2
    
    def test_bulk_create_job_types_too_many(self, client, mock_user, sample_job_type_create_data):
        """Test bulk creation with too many items."""
        bulk_data = [sample_job_type_create_data] * 1001  # Exceeds limit
        
        with patch('api.v1.job_types.get_current_user', return_value=mock_user):
            response = client.post("/api/v1/job-types/bulk", json=bulk_data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "Cannot create more than 1000" in data["detail"]["message"]
    
    def test_search_job_types_success(self, client, sample_job_type_response):
        """Test successful job type search."""
        mock_response = {
            "data": [sample_job_type_response.dict()],
            "pagination": {"page": 1, "total_items": 1},
            "total_count": 1,
            "aggregations": {}
        }
        
        with patch('api.v1.job_types.get_job_type_service') as mock_service:
            mock_service.return_value.search_job_types.return_value = mock_response
            
            response = client.get("/api/v1/job-types/search", params={"q": "security engineer"})
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "data" in data
        assert len(data["data"]) == 1
    
    def test_search_job_types_short_query(self, client):
        """Test search with too short query."""
        response = client.get("/api/v1/job-types/search", params={"q": "s"})
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_get_job_type_analytics_success(self, client):
        """Test successful job type analytics retrieval."""
        mock_analytics = {
            "job_type": {"id": 1, "title": "Security Engineer"},
            "related_jobs": [],
            "career_progression": {"from": [], "to": []},
            "market_data": {"demand_level": "high"}
        }
        
        with patch('api.v1.job_types.get_job_type_service') as mock_service:
            mock_service.return_value.get_job_type_analytics.return_value = mock_analytics
            
            response = client.get("/api/v1/job-types/1/analytics")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "job_type" in data
        assert "market_data" in data
    
    def test_get_popular_job_types_success(self, client, sample_job_type_response):
        """Test successful popular job types retrieval."""
        with patch('api.v1.job_types.get_job_type_service') as mock_service:
            mock_service.return_value.get_popular_job_types.return_value = [sample_job_type_response]
            
            response = client.get("/api/v1/job-types/popular")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == 1
    
    def test_get_popular_job_types_with_filters(self, client, sample_job_type_response):
        """Test popular job types with security area filter."""
        with patch('api.v1.job_types.get_job_type_service') as mock_service:
            mock_service.return_value.get_popular_job_types.return_value = [sample_job_type_response]
            
            response = client.get(
                "/api/v1/job-types/popular",
                params={
                    "security_area": "Security Architecture and Engineering",
                    "limit": 5
                }
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
