"""Comprehensive test suite for compliance automation system.

This module provides extensive testing for all compliance-related functionality
including GDPR, HIPAA, SOX reporting, audit logging, and data processing activities.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
import json

from models.compliance import (
    ComplianceRequirement, ComplianceAssessment, ComplianceReport, AuditLog,
    DataProcessingActivity, ComplianceFramework, ComplianceStatus, RiskLevel,
    AuditEventType
)
from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole
from services.compliance_service import ComplianceService


class TestComplianceRequirements:
    """Test compliance requirements management."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def compliance_service(self, mock_db):
        return ComplianceService(mock_db)
    
    def test_create_gdpr_requirement(self, compliance_service, mock_db):
        """Test creating GDPR compliance requirement."""
        req_data = {
            'organization_id': 1,
            'framework': ComplianceFramework.GDPR,
            'requirement_id': 'GDPR-Art-32',
            'title': 'Security of processing',
            'description': 'Implement appropriate technical and organizational measures',
            'risk_level': RiskLevel.HIGH,
            'automated_check': True,
            'check_frequency': 'monthly'
        }
        
        mock_requirement = Mock(spec=ComplianceRequirement)
        mock_requirement.id = 1
        mock_requirement.title = req_data['title']
        mock_requirement.organization_id = req_data['organization_id']
        
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.create_compliance_requirement(req_data)
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_create_hipaa_requirement(self, compliance_service, mock_db):
        """Test creating HIPAA compliance requirement."""
        req_data = {
            'organization_id': 1,
            'framework': ComplianceFramework.HIPAA,
            'requirement_id': 'HIPAA-164.312',
            'title': 'Administrative safeguards',
            'description': 'Implement administrative safeguards',
            'risk_level': RiskLevel.CRITICAL,
            'evidence_required': ['Policy documents', 'Training records']
        }
        
        mock_requirement = Mock(spec=ComplianceRequirement)
        mock_requirement.id = 2
        mock_requirement.title = req_data['title']
        
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        with patch.object(compliance_service, 'audit_service'):
            result = compliance_service.create_compliance_requirement(req_data)
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    def test_create_sox_requirement(self, compliance_service, mock_db):
        """Test creating SOX compliance requirement."""
        req_data = {
            'organization_id': 1,
            'framework': ComplianceFramework.SOX,
            'requirement_id': 'SOX-404',
            'title': 'Management assessment of internal controls',
            'description': 'Annual assessment of internal control effectiveness',
            'risk_level': RiskLevel.HIGH,
            'check_frequency': 'annually'
        }
        
        mock_requirement = Mock(spec=ComplianceRequirement)
        mock_requirement.id = 3
        mock_requirement.title = req_data['title']
        
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        with patch.object(compliance_service, 'audit_service'):
            result = compliance_service.create_compliance_requirement(req_data)
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    def test_get_requirements_by_framework(self, compliance_service, mock_db):
        """Test retrieving requirements by framework."""
        mock_requirements = [
            Mock(requirement_id='GDPR-Art-32', framework=ComplianceFramework.GDPR),
            Mock(requirement_id='GDPR-Art-33', framework=ComplianceFramework.GDPR)
        ]
        
        mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = mock_requirements
        
        result = compliance_service.get_organization_requirements(1, ComplianceFramework.GDPR)
        
        assert len(result) == 2
        mock_db.query.assert_called()
    
    def test_get_requirements_by_risk_level(self, compliance_service, mock_db):
        """Test retrieving requirements by risk level."""
        mock_requirements = [
            Mock(requirement_id='GDPR-Art-32', risk_level=RiskLevel.HIGH),
            Mock(requirement_id='HIPAA-164.312', risk_level=RiskLevel.CRITICAL)
        ]
        
        mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = mock_requirements
        
        result = compliance_service.get_organization_requirements(1)
        
        assert len(result) == 2
        mock_db.query.assert_called()


class TestComplianceAssessments:
    """Test compliance assessments functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def compliance_service(self, mock_db):
        return ComplianceService(mock_db)
    
    def test_assess_compliant_requirement(self, compliance_service, mock_db):
        """Test assessing a requirement as compliant."""
        requirement_id = 1
        assessment_data = {
            'assessment_date': datetime.now(),
            'assessor_id': 'user_123',
            'organization_id': 1,
            'status': ComplianceStatus.COMPLIANT,
            'score': 95.0,
            'findings': 'All controls implemented correctly',
            'evidence_provided': ['Security policy', 'Audit report'],
            'assessment_method': 'manual'
        }
        
        mock_requirement = Mock(spec=ComplianceRequirement)
        mock_requirement.check_frequency = 'monthly'
        
        mock_assessment = Mock(spec=ComplianceAssessment)
        mock_assessment.id = 1
        mock_assessment.status = ComplianceStatus.COMPLIANT
        mock_assessment.organization_id = 1
        
        mock_db.query.return_value.get.return_value = mock_requirement
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.assess_compliance_requirement(requirement_id, assessment_data)
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_assess_non_compliant_requirement(self, compliance_service, mock_db):
        """Test assessing a requirement as non-compliant."""
        requirement_id = 1
        assessment_data = {
            'assessment_date': datetime.now(),
            'assessor_id': 'user_123',
            'organization_id': 1,
            'status': ComplianceStatus.NON_COMPLIANT,
            'score': 35.0,
            'findings': 'Missing security controls',
            'gaps_identified': ['No encryption', 'Weak access controls'],
            'remediation_plan': 'Implement encryption and strengthen access controls',
            'remediation_due_date': datetime.now() + timedelta(days=30)
        }
        
        mock_requirement = Mock(spec=ComplianceRequirement)
        mock_requirement.check_frequency = 'quarterly'
        
        mock_assessment = Mock(spec=ComplianceAssessment)
        mock_assessment.id = 2
        mock_assessment.status = ComplianceStatus.NON_COMPLIANT
        mock_assessment.organization_id = 1
        
        mock_db.query.return_value.get.return_value = mock_requirement
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.assess_compliance_requirement(requirement_id, assessment_data)
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_assess_partially_compliant_requirement(self, compliance_service, mock_db):
        """Test assessing a requirement as partially compliant."""
        requirement_id = 1
        assessment_data = {
            'assessment_date': datetime.now(),
            'assessor_id': 'user_123',
            'organization_id': 1,
            'status': ComplianceStatus.PARTIALLY_COMPLIANT,
            'score': 65.0,
            'findings': 'Some controls implemented, others missing',
            'gaps_identified': ['Incomplete documentation'],
            'remediation_plan': 'Complete documentation and training'
        }
        
        mock_requirement = Mock(spec=ComplianceRequirement)
        mock_requirement.check_frequency = 'weekly'
        
        mock_assessment = Mock(spec=ComplianceAssessment)
        mock_assessment.id = 3
        mock_assessment.status = ComplianceStatus.PARTIALLY_COMPLIANT
        mock_assessment.organization_id = 1
        
        mock_db.query.return_value.get.return_value = mock_requirement
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.assess_compliance_requirement(requirement_id, assessment_data)
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_calculate_next_assessment_date(self, compliance_service):
        """Test calculation of next assessment due date."""
        # Test daily frequency
        next_date = compliance_service._calculate_next_assessment_date('daily')
        assert next_date > datetime.now()
        assert (next_date - datetime.now()).days <= 1
        
        # Test weekly frequency
        next_date = compliance_service._calculate_next_assessment_date('weekly')
        assert next_date > datetime.now()
        assert (next_date - datetime.now()).days <= 7
        
        # Test monthly frequency
        next_date = compliance_service._calculate_next_assessment_date('monthly')
        assert next_date > datetime.now()
        assert (next_date - datetime.now()).days <= 30
        
        # Test quarterly frequency
        next_date = compliance_service._calculate_next_assessment_date('quarterly')
        assert next_date > datetime.now()
        assert (next_date - datetime.now()).days <= 90
        
        # Test annually frequency
        next_date = compliance_service._calculate_next_assessment_date('annually')
        assert next_date > datetime.now()
        assert (next_date - datetime.now()).days <= 365
        
        # Test invalid frequency
        next_date = compliance_service._calculate_next_assessment_date('invalid')
        assert next_date > datetime.now()
        assert (next_date - datetime.now()).days <= 90  # Default to quarterly


class TestGDPRCompliance:
    """Test GDPR-specific compliance functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def compliance_service(self, mock_db):
        return ComplianceService(mock_db)
    
    def test_generate_gdpr_report_comprehensive(self, compliance_service, mock_db):
        """Test comprehensive GDPR report generation."""
        org_id = 1
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)
        
        # Mock GDPR requirements with various statuses
        mock_requirements = [
            Mock(
                requirement_id='GDPR-Art-32',
                title='Security of processing',
                status=ComplianceStatus.COMPLIANT,
                risk_level=RiskLevel.HIGH,
                last_assessed=datetime.now()
            ),
            Mock(
                requirement_id='GDPR-Art-33',
                title='Notification of breach',
                status=ComplianceStatus.NON_COMPLIANT,
                risk_level=RiskLevel.CRITICAL,
                last_assessed=datetime.now()
            ),
            Mock(
                requirement_id='GDPR-Art-35',
                title='Data protection impact assessment',
                status=ComplianceStatus.PARTIALLY_COMPLIANT,
                risk_level=RiskLevel.MEDIUM,
                last_assessed=datetime.now()
            )
        ]
        
        # Mock data processing activities
        mock_activities = [
            Mock(
                activity_name='Employee Data Processing',
                purpose_of_processing='HR management',
                legal_basis='Contract',
                data_categories=['Personal identifiers'],
                dpia_required=True,
                dpia_completed=False
            ),
            Mock(
                activity_name='Customer Data Processing',
                purpose_of_processing='Service delivery',
                legal_basis='Legitimate interest',
                data_categories=['Contact information'],
                dpia_required=False,
                dpia_completed=True
            )
        ]
        
        mock_report = Mock(spec=ComplianceReport)
        mock_report.id = 1
        mock_report.report_name = "GDPR Compliance Report - 2024-01"
        
        # Setup mock database queries
        compliance_service.get_organization_requirements = Mock(return_value=mock_requirements)
        mock_db.query.return_value.filter.return_value.all.return_value = mock_activities
        mock_db.query.return_value.filter.return_value.count.return_value = 15  # Audit events
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.generate_gdpr_report(org_id, start_date, end_date)
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_gdpr_compliance_score_calculation(self, compliance_service):
        """Test GDPR compliance score calculation with various scenarios."""
        # Scenario 1: All compliant
        gdpr_data = {
            'compliant_requirements': [{'id': 1}, {'id': 2}, {'id': 3}],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': []
        }
        score = compliance_service._calculate_gdpr_compliance_score(gdpr_data)
        assert score == 100.0
        
        # Scenario 2: Mixed compliance
        gdpr_data = {
            'compliant_requirements': [{'id': 1}, {'id': 2}],
            'non_compliant_requirements': [{'id': 3}],
            'partially_compliant_requirements': [{'id': 4}]
        }
        score = compliance_service._calculate_gdpr_compliance_score(gdpr_data)
        assert score == 62.5  # (2*100 + 1*50) / 4
        
        # Scenario 3: All non-compliant
        gdpr_data = {
            'compliant_requirements': [],
            'non_compliant_requirements': [{'id': 1}, {'id': 2}],
            'partially_compliant_requirements': []
        }
        score = compliance_service._calculate_gdpr_compliance_score(gdpr_data)
        assert score == 0.0
        
        # Scenario 4: No requirements
        gdpr_data = {
            'compliant_requirements': [],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': []
        }
        score = compliance_service._calculate_gdpr_compliance_score(gdpr_data)
        assert score == 0.0
    
    def test_gdpr_recommendations_generation(self, compliance_service):
        """Test GDPR recommendations generation."""
        gdpr_data = {
            'non_compliant_requirements': [
                {'requirement_id': 'GDPR-Art-32', 'title': 'Security of processing'},
                {'requirement_id': 'GDPR-Art-33', 'title': 'Breach notification'}
            ],
            'high_risk_findings': [
                {'requirement_id': 'GDPR-Art-33', 'risk_level': 'critical'}
            ],
            'data_processing_activities': [
                {
                    'name': 'Employee Data',
                    'dpia_required': True,
                    'dpia_completed': False
                },
                {
                    'name': 'Customer Data',
                    'dpia_required': True,
                    'dpia_completed': False
                }
            ]
        }
        
        recommendations = compliance_service._generate_gdpr_recommendations(gdpr_data)
        
        assert len(recommendations) >= 2
        assert any('non-compliant requirements' in rec for rec in recommendations)
        assert any('high-risk' in rec for rec in recommendations)
        assert any('Data Protection Impact Assessments' in rec for rec in recommendations)
    
    def test_gdpr_action_items_generation(self, compliance_service):
        """Test GDPR action items generation."""
        gdpr_data = {
            'non_compliant_requirements': [
                {
                    'requirement_id': 'GDPR-Art-32',
                    'title': 'Security of processing',
                    'risk_level': 'high'
                },
                {
                    'requirement_id': 'GDPR-Art-33',
                    'title': 'Breach notification',
                    'risk_level': 'critical'
                }
            ]
        }
        
        action_items = compliance_service._generate_gdpr_action_items(gdpr_data)
        
        assert len(action_items) == 2
        
        # Check critical item has higher priority
        critical_item = next(item for item in action_items if 'GDPR-Art-33' in item['description'])
        high_item = next(item for item in action_items if 'GDPR-Art-32' in item['description'])
        
        assert critical_item['priority'] == 'high'
        assert high_item['priority'] == 'medium'


class TestHIPAACompliance:
    """Test HIPAA-specific compliance functionality."""

    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)

    @pytest.fixture
    def compliance_service(self, mock_db):
        return ComplianceService(mock_db)

    def test_generate_hipaa_report_with_phi_events(self, compliance_service, mock_db):
        """Test HIPAA report generation with PHI access events."""
        org_id = 1
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)

        mock_requirements = [
            Mock(
                requirement_id='HIPAA-164.312',
                title='Administrative safeguards',
                status=ComplianceStatus.COMPLIANT,
                risk_level=RiskLevel.HIGH,
                last_assessed=datetime.now()
            ),
            Mock(
                requirement_id='HIPAA-164.308',
                title='Administrative safeguards',
                status=ComplianceStatus.NON_COMPLIANT,
                risk_level=RiskLevel.CRITICAL,
                last_assessed=datetime.now()
            )
        ]

        compliance_service.get_organization_requirements = Mock(return_value=mock_requirements)

        # Mock PHI access events and security incidents
        mock_db.query.return_value.filter.return_value.count.side_effect = [25, 2]  # PHI events, incidents
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None

        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.generate_hipaa_report(org_id, start_date, end_date)

        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()

    def test_hipaa_compliance_score_with_incidents(self, compliance_service):
        """Test HIPAA compliance score calculation with security incidents."""
        hipaa_data = {
            'compliant_requirements': [{'id': 1}, {'id': 2}],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': [],
            'security_incidents': 3  # Should reduce score
        }

        score = compliance_service._calculate_hipaa_compliance_score(hipaa_data)

        # Base score 100%, minus 15% penalty (3 incidents * 5%)
        assert score == 85.0

    def test_hipaa_compliance_score_max_penalty(self, compliance_service):
        """Test HIPAA compliance score with maximum penalty."""
        hipaa_data = {
            'compliant_requirements': [{'id': 1}],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': [],
            'security_incidents': 10  # Should hit max penalty of 20%
        }

        score = compliance_service._calculate_hipaa_compliance_score(hipaa_data)

        # Base score 100%, minus max 20% penalty
        assert score == 80.0

    def test_hipaa_recommendations_with_incidents(self, compliance_service):
        """Test HIPAA recommendations with security incidents."""
        hipaa_data = {
            'security_incidents': 2,
            'non_compliant_requirements': [
                {'requirement_id': 'HIPAA-164.312', 'title': 'Administrative safeguards'}
            ],
            'phi_access_events': 1500  # High access volume
        }

        recommendations = compliance_service._generate_hipaa_recommendations(hipaa_data)

        assert len(recommendations) >= 3
        assert any('security incident response' in rec for rec in recommendations)
        assert any('non-compliant HIPAA requirements' in rec for rec in recommendations)
        assert any('PHI access monitoring' in rec for rec in recommendations)

    def test_hipaa_action_items_with_incidents(self, compliance_service):
        """Test HIPAA action items generation with security incidents."""
        hipaa_data = {
            'non_compliant_requirements': [
                {
                    'requirement_id': 'HIPAA-164.312',
                    'title': 'Administrative safeguards',
                    'risk_level': 'critical'
                }
            ],
            'security_incidents': 2
        }

        action_items = compliance_service._generate_hipaa_action_items(hipaa_data)

        assert len(action_items) >= 2

        # Check for compliance gap action
        compliance_item = next(item for item in action_items if item['type'] == 'hipaa_compliance_gap')
        assert compliance_item['priority'] == 'critical'

        # Check for security review action
        security_item = next(item for item in action_items if item['type'] == 'security_review')
        assert security_item['priority'] == 'high'


class TestSOXCompliance:
    """Test SOX-specific compliance functionality."""

    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)

    @pytest.fixture
    def compliance_service(self, mock_db):
        return ComplianceService(mock_db)

    def test_generate_sox_report_with_controls(self, compliance_service, mock_db):
        """Test SOX report generation with IT general controls."""
        org_id = 1
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)

        mock_requirements = [
            Mock(
                requirement_id='SOX-404',
                title='Management assessment',
                status=ComplianceStatus.COMPLIANT,
                risk_level=RiskLevel.HIGH,
                last_assessed=datetime.now()
            ),
            Mock(
                requirement_id='SOX-302',
                title='Corporate responsibility',
                status=ComplianceStatus.PARTIALLY_COMPLIANT,
                risk_level=RiskLevel.MEDIUM,
                last_assessed=datetime.now()
            )
        ]

        compliance_service.get_organization_requirements = Mock(return_value=mock_requirements)

        # Mock change management and access review events
        mock_db.query.return_value.filter.return_value.count.side_effect = [15, 8]  # Changes, reviews
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None

        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.generate_sox_report(org_id, start_date, end_date)

        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()

    def test_sox_compliance_score_with_bonus(self, compliance_service):
        """Test SOX compliance score with governance bonus."""
        sox_data = {
            'compliant_requirements': [{'id': 1}],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': [{'id': 2}],
            'change_management_events': 10,
            'access_reviews_completed': 5
        }

        score = compliance_service._calculate_sox_compliance_score(sox_data)

        # Base score: (1*100 + 1*50) / 2 = 75%, plus 5% bonus = 80%
        assert score == 80.0

    def test_sox_compliance_score_no_bonus(self, compliance_service):
        """Test SOX compliance score without governance bonus."""
        sox_data = {
            'compliant_requirements': [{'id': 1}],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': [{'id': 2}],
            'change_management_events': 0,  # No change management
            'access_reviews_completed': 5
        }

        score = compliance_service._calculate_sox_compliance_score(sox_data)

        # Base score: (1*100 + 1*50) / 2 = 75%, no bonus
        assert score == 75.0

    def test_sox_recommendations_missing_controls(self, compliance_service):
        """Test SOX recommendations for missing controls."""
        sox_data = {
            'change_management_events': 0,
            'access_reviews_completed': 0,
            'non_compliant_requirements': [
                {'requirement_id': 'SOX-404', 'title': 'Management assessment'}
            ]
        }

        recommendations = compliance_service._generate_sox_recommendations(sox_data)

        assert len(recommendations) >= 3
        assert any('change management procedures' in rec for rec in recommendations)
        assert any('access reviews' in rec for rec in recommendations)
        assert any('non-compliant SOX requirements' in rec for rec in recommendations)

    def test_sox_action_items_process_improvement(self, compliance_service):
        """Test SOX action items for process improvements."""
        sox_data = {
            'non_compliant_requirements': [
                {
                    'requirement_id': 'SOX-404',
                    'title': 'Management assessment',
                    'risk_level': 'high'
                }
            ],
            'change_management_events': 0
        }

        action_items = compliance_service._generate_sox_action_items(sox_data)

        assert len(action_items) >= 2

        # Check for compliance gap action
        compliance_item = next(item for item in action_items if item['type'] == 'sox_compliance_gap')
        assert compliance_item['priority'] == 'high'

        # Check for process improvement action
        process_item = next(item for item in action_items if item['type'] == 'process_improvement')
        assert process_item['priority'] == 'medium'
