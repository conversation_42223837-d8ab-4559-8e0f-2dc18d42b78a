"""Unit tests for Job Types CRUD service.

This module provides comprehensive unit tests for the JobTypeCRUDService
following PEP 8, 257, and 484 standards with 95%+ test coverage.
"""

import pytest
from typing import Dict, Any, List
from unittest.mock import Mock, patch
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from services.job_types_crud import (
    JobTypeCRUDService, JobTypeCreateRequest, JobTypeUpdateRequest,
    JobTypeResponse, JobTypeFilters
)
from services.base_crud import (
    ValidationError, NotFoundError, ConflictError,
    PaginationParams, SortParams
)
from models.security_career_framework import SecurityJobType


class TestJobTypeCreateRequest:
    """Test cases for JobTypeCreateRequest validation."""
    
    def test_valid_job_type_creation(self):
        """Test valid job type creation request."""
        request = JobTypeCreateRequest(
            title="Senior Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="advanced",
            description="Senior security engineer role",
            required_skills=["Network Security", "Python"],
            preferred_skills=["Cloud Security"],
            min_years_experience=3,
            max_years_experience=7,
            salary_min=90000,
            salary_max=130000,
            demand_level="high"
        )
        
        assert request.title == "Senior Security Engineer"
        assert request.security_area == "Security Architecture and Engineering"
        assert request.demand_level == "high"
        assert request.salary_currency == "USD"
        assert request.remote_friendly is True
        assert request.is_active is True
    
    def test_invalid_title_length(self):
        """Test validation of title length."""
        with pytest.raises(ValueError, match="at least 3 characters"):
            JobTypeCreateRequest(
                title="SE",
                security_area="Security Architecture and Engineering",
                job_family="security_engineer",
                seniority_level="advanced"
            )
    
    def test_invalid_security_area(self):
        """Test validation of security area."""
        with pytest.raises(ValueError, match="Security area must be one of"):
            JobTypeCreateRequest(
                title="Security Engineer",
                security_area="Invalid Area",
                job_family="security_engineer",
                seniority_level="advanced"
            )
    
    def test_invalid_demand_level(self):
        """Test validation of demand level."""
        with pytest.raises(ValueError, match="Demand level must be one of"):
            JobTypeCreateRequest(
                title="Security Engineer",
                security_area="Security Architecture and Engineering",
                job_family="security_engineer",
                seniority_level="advanced",
                demand_level="invalid"
            )
    
    def test_invalid_experience_range(self):
        """Test validation of experience range."""
        with pytest.raises(ValueError, match="Maximum experience must be >= minimum"):
            JobTypeCreateRequest(
                title="Security Engineer",
                security_area="Security Architecture and Engineering",
                job_family="security_engineer",
                seniority_level="advanced",
                min_years_experience=5,
                max_years_experience=3
            )
    
    def test_invalid_salary_range(self):
        """Test validation of salary range."""
        with pytest.raises(ValueError, match="Maximum salary must be >= minimum"):
            JobTypeCreateRequest(
                title="Security Engineer",
                security_area="Security Architecture and Engineering",
                job_family="security_engineer",
                seniority_level="advanced",
                salary_min=100000,
                salary_max=80000
            )
    
    def test_invalid_currency_code(self):
        """Test validation of currency code."""
        from pydantic import ValidationError
        with pytest.raises(ValidationError, match="String should match pattern"):
            JobTypeCreateRequest(
                title="Security Engineer",
                security_area="Security Architecture and Engineering",
                job_family="security_engineer",
                seniority_level="advanced",
                salary_currency="INVALID"
            )


class TestJobTypeCRUDService:
    """Test cases for JobTypeCRUDService."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def job_type_service(self, mock_db_session):
        """Create JobTypeCRUDService instance."""
        return JobTypeCRUDService(mock_db_session)
    
    @pytest.fixture
    def sample_job_type_data(self) -> Dict[str, Any]:
        """Sample job type data for testing."""
        return {
            'id': 1,
            'title': 'Security Engineer',
            'security_area': 'Security Architecture and Engineering',
            'job_family': 'security_engineer',
            'seniority_level': 'intermediate',
            'description': 'Security engineer role',
            'responsibilities': ['Implement security controls', 'Monitor systems'],
            'required_skills': ['Network Security', 'Python'],
            'preferred_skills': ['Cloud Security'],
            'min_years_experience': 2,
            'max_years_experience': 5,
            'education_requirements': ['Bachelor\'s degree'],
            'required_certifications': ['Security+'],
            'preferred_certifications': ['CISSP'],
            'salary_min': 80000.0,
            'salary_max': 120000.0,
            'salary_currency': 'USD',
            'career_progression_from': [],
            'career_progression_to': [2, 3],
            'demand_level': 'high',
            'remote_friendly': True,
            'tags': ['security', 'engineering'],
            'is_active': True,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
    
    @pytest.fixture
    def mock_job_type(self, sample_job_type_data):
        """Create mock SecurityJobType instance."""
        job_type = Mock(spec=SecurityJobType)
        for key, value in sample_job_type_data.items():
            setattr(job_type, key, value)
        return job_type
    
    def test_create_job_type_success(self, job_type_service, mock_db_session, mock_job_type):
        """Test successful job type creation."""
        # Setup
        create_request = JobTypeCreateRequest(
            title="Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="intermediate",
            required_skills=["Network Security"],
            salary_min=80000,
            salary_max=120000
        )
        
        # Mock database operations
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        mock_db_session.add.return_value = None
        mock_db_session.commit.return_value = None
        mock_db_session.refresh.return_value = None
        
        # Mock the created object
        mock_job_type.id = 1
        
        with patch.object(job_type_service, '_validate_create_data', return_value=create_request):
            with patch('services.job_types_crud.SecurityJobType') as MockJobType:
                # Create a mock instance with proper attributes
                mock_instance = Mock()
                mock_instance.id = 1
                MockJobType.return_value = mock_instance

                with patch.object(JobTypeResponse, 'from_orm', return_value=mock_job_type):
                    result = job_type_service.create(create_request, user_id="test_user")
        
        # Assertions
        assert result is not None
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()
    
    def test_create_job_type_duplicate_title(self, job_type_service, mock_db_session, mock_job_type):
        """Test job type creation with duplicate title."""
        create_request = JobTypeCreateRequest(
            title="Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="intermediate"
        )
        
        # Mock existing job type
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_job_type
        
        with pytest.raises(ValidationError, match="Similar job type already exists"):
            job_type_service.create(create_request, user_id="test_user")
    
    def test_create_job_type_invalid_progression_references(self, job_type_service, mock_db_session):
        """Test job type creation with invalid career progression references."""
        create_request = JobTypeCreateRequest(
            title="Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="intermediate",
            career_progression_to=[999]  # Non-existent ID
        )
        
        # Mock no existing job type for duplicate check
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        # Mock empty result for progression reference check
        mock_db_session.query.return_value.filter.return_value.all.return_value = []
        
        with pytest.raises(ValidationError, match="Invalid job type references"):
            job_type_service.create(create_request, user_id="test_user")
    
    def test_create_job_type_skill_overlap(self, job_type_service, mock_db_session):
        """Test job type creation with overlapping required and preferred skills."""
        create_request = JobTypeCreateRequest(
            title="Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="intermediate",
            required_skills=["Python", "Network Security"],
            preferred_skills=["Python", "Cloud Security"]  # Python overlaps
        )
        
        # Mock no existing job type
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValidationError, match="Skills cannot be both required and preferred"):
            job_type_service.create(create_request, user_id="test_user")
    
    def test_get_job_type_success(self, job_type_service, mock_db_session, mock_job_type):
        """Test successful job type retrieval."""
        # Setup
        job_type_id = 1
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_job_type
        
        with patch.object(JobTypeResponse, 'from_orm', return_value=mock_job_type):
            result = job_type_service.get(job_type_id)
        
        # Assertions
        assert result is not None
        mock_db_session.query.assert_called_with(SecurityJobType)
    
    def test_get_job_type_not_found(self, job_type_service, mock_db_session):
        """Test job type retrieval when not found."""
        # Setup
        job_type_id = 999
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        result = job_type_service.get(job_type_id)
        
        # Assertions
        assert result is None
    
    def test_get_or_404_success(self, job_type_service, mock_db_session, mock_job_type):
        """Test successful get_or_404."""
        job_type_id = 1
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_job_type
        
        with patch.object(JobTypeResponse, 'from_orm', return_value=mock_job_type):
            result = job_type_service.get_or_404(job_type_id)
        
        assert result is not None
    
    def test_get_or_404_not_found(self, job_type_service, mock_db_session):
        """Test get_or_404 when not found."""
        job_type_id = 999
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(NotFoundError, match="SecurityJobType with ID 999 not found"):
            job_type_service.get_or_404(job_type_id)
    
    def test_update_job_type_success(self, job_type_service, mock_db_session, mock_job_type):
        """Test successful job type update."""
        # Setup
        job_type_id = 1
        update_request = JobTypeUpdateRequest(
            title="Senior Security Engineer",
            salary_min=90000
        )
        
        # Mock existing job type
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_job_type
        mock_db_session.commit.return_value = None
        mock_db_session.refresh.return_value = None
        
        with patch.object(job_type_service, '_validate_update_data', return_value=update_request):
            with patch.object(JobTypeResponse, 'from_orm', return_value=mock_job_type):
                result = job_type_service.update(job_type_id, update_request, user_id="test_user")
        
        # Assertions
        assert result is not None
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()
    
    def test_update_job_type_not_found(self, job_type_service, mock_db_session):
        """Test job type update when not found."""
        job_type_id = 999
        update_request = JobTypeUpdateRequest(title="New Title")
        
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(NotFoundError, match="SecurityJobType with ID 999 not found"):
            job_type_service.update(job_type_id, update_request, user_id="test_user")
    
    def test_delete_job_type_soft_delete(self, job_type_service, mock_db_session, mock_job_type):
        """Test soft delete of job type."""
        job_type_id = 1
        mock_job_type.is_deleted = False
        
        # Mock existing job type
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_job_type
        mock_db_session.commit.return_value = None
        
        # Mock hasattr to return True for soft delete fields
        with patch('builtins.hasattr', return_value=True):
            result = job_type_service.delete(job_type_id, user_id="test_user", soft=True)
        
        # Assertions
        assert result is True
        assert mock_job_type.is_deleted is True
        mock_db_session.commit.assert_called_once()
    
    def test_delete_job_type_hard_delete(self, job_type_service, mock_db_session, mock_job_type):
        """Test hard delete of job type."""
        job_type_id = 1
        
        # Mock existing job type
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_job_type
        mock_db_session.delete.return_value = None
        mock_db_session.commit.return_value = None
        
        result = job_type_service.delete(job_type_id, user_id="test_user", soft=False)
        
        # Assertions
        assert result is True
        mock_db_session.delete.assert_called_once_with(mock_job_type)
        mock_db_session.commit.assert_called_once()
    
    def test_delete_job_type_not_found(self, job_type_service, mock_db_session):
        """Test delete when job type not found."""
        job_type_id = 999
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(NotFoundError, match="SecurityJobType with ID 999 not found"):
            job_type_service.delete(job_type_id, user_id="test_user")


class TestJobTypeFilters:
    """Test cases for JobTypeFilters."""
    
    def test_job_type_filters_creation(self):
        """Test creation of job type filters."""
        filters = JobTypeFilters(
            security_area=["Security Architecture and Engineering"],
            seniority_level=["intermediate", "advanced"],
            salary_min=80000,
            salary_max=150000,
            remote_friendly=True,
            demand_level=["high", "critical"]
        )
        
        assert filters.security_area == ["Security Architecture and Engineering"]
        assert filters.seniority_level == ["intermediate", "advanced"]
        assert filters.salary_min == 80000
        assert filters.salary_max == 150000
        assert filters.remote_friendly is True
        assert filters.demand_level == ["high", "critical"]
    
    def test_empty_filters(self):
        """Test creation of empty filters."""
        filters = JobTypeFilters()
        
        assert filters.security_area is None
        assert filters.seniority_level is None
        assert filters.salary_min is None
        assert filters.salary_max is None
        assert filters.remote_friendly is None
        assert filters.demand_level is None


class TestJobTypeResponse:
    """Test cases for JobTypeResponse."""
    
    def test_job_type_response_creation(self, sample_job_type_data):
        """Test creation of job type response."""
        response = JobTypeResponse(**sample_job_type_data)
        
        assert response.id == 1
        assert response.title == "Security Engineer"
        assert response.security_area == "Security Architecture and Engineering"
        assert response.salary_min == 80000.0
        assert response.salary_max == 120000.0
        assert response.is_active is True


class TestJobTypeAdvancedOperations:
    """Test cases for advanced job type operations."""

    @pytest.fixture
    def mock_db_session(self):
        """Create mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def job_type_service(self, mock_db_session):
        """Create JobTypeCRUDService instance."""
        return JobTypeCRUDService(mock_db_session)

    def test_list_with_filters(self, job_type_service, mock_db_session):
        """Test listing job types with filters."""
        # Setup
        filters = JobTypeFilters(
            security_area=["Security Architecture and Engineering"],
            seniority_level=["intermediate"]
        )
        pagination = PaginationParams(page=1, page_size=10)
        sort_params = SortParams(sort_by="title", sort_order="asc")

        # Mock query chain
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 5
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []

        # Mock aggregations
        with patch.object(job_type_service, '_generate_aggregations', return_value={}):
            result = job_type_service.list_with_filters(filters, pagination, sort_params)

        # Assertions
        assert 'data' in result
        assert 'pagination' in result
        assert 'total_count' in result
        assert 'aggregations' in result
        assert result['total_count'] == 5

    def test_search_job_types(self, job_type_service, mock_db_session):
        """Test job type search functionality."""
        search_query = "security engineer"

        # Mock the list_with_filters method
        expected_result = {
            'data': [],
            'pagination': {'page': 1, 'total_pages': 0},
            'total_count': 0,
            'aggregations': {}
        }

        with patch.object(job_type_service, 'list_with_filters', return_value=expected_result):
            result = job_type_service.search_job_types(search_query)

        assert result == expected_result

    def test_get_job_type_analytics(self, job_type_service, mock_db_session):
        """Test job type analytics retrieval."""
        job_type_id = 1

        # Mock job type
        mock_job_type = Mock()
        mock_job_type.security_area = "Security Architecture and Engineering"
        mock_job_type.career_progression_from = [2]
        mock_job_type.career_progression_to = [3]
        mock_job_type.demand_level = "high"
        mock_job_type.remote_friendly = True
        mock_job_type.salary_min = 80000
        mock_job_type.salary_max = 120000
        mock_job_type.salary_currency = "USD"

        # Mock get_or_404
        with patch.object(job_type_service, 'get_or_404', return_value=mock_job_type):
            # Mock related queries
            mock_db_session.query.return_value.filter.return_value.limit.return_value.all.return_value = []
            mock_db_session.query.return_value.filter.return_value.all.return_value = []

            result = job_type_service.get_job_type_analytics(job_type_id)

        # Assertions
        assert 'job_type' in result
        assert 'related_jobs' in result
        assert 'career_progression' in result
        assert 'market_data' in result
        assert result['market_data']['demand_level'] == "high"

    def test_get_popular_job_types(self, job_type_service, mock_db_session):
        """Test getting popular job types."""
        security_area = "Security Architecture and Engineering"
        limit = 5

        # Mock query chain
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []

        with patch.object(JobTypeResponse, 'from_orm', return_value=Mock()):
            result = job_type_service.get_popular_job_types(security_area, limit)

        assert isinstance(result, list)
        mock_query.limit.assert_called_with(limit)

    def test_bulk_create_job_types(self, job_type_service, mock_db_session):
        """Test bulk creation of job types."""
        job_types = [
            JobTypeCreateRequest(
                title="Security Engineer 1",
                security_area="Security Architecture and Engineering",
                job_family="security_engineer",
                seniority_level="intermediate"
            ),
            JobTypeCreateRequest(
                title="Security Engineer 2",
                security_area="Security Architecture and Engineering",
                job_family="security_engineer",
                seniority_level="advanced"
            )
        ]

        # Mock validation and database operations
        with patch.object(job_type_service, '_validate_create_data', side_effect=lambda x: x):
            with patch.object(SecurityJobType, '__init__', return_value=None):
                mock_db_session.add_all.return_value = None
                mock_db_session.commit.return_value = None
                mock_db_session.refresh.return_value = None

                # Mock created objects
                mock_objects = [Mock(id=i) for i in range(1, 3)]

                with patch.object(JobTypeResponse, 'from_orm', side_effect=mock_objects):
                    result = job_type_service.bulk_create(job_types, user_id="test_user")

        assert len(result) == 2
        mock_db_session.add_all.assert_called()
        mock_db_session.commit.assert_called()

    def test_generate_aggregations(self, job_type_service, mock_db_session):
        """Test aggregation generation for faceted search."""
        filters = JobTypeFilters()

        # Mock aggregation queries
        mock_query = Mock()
        mock_db_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.subquery.return_value.select.return_value = Mock()
        mock_query.group_by.return_value.all.return_value = [
            Mock(security_area="Security Architecture and Engineering", count=5),
            Mock(security_area="Security Operations", count=3)
        ]

        # Mock salary data query
        mock_db_session.query.return_value.filter.return_value.all.return_value = [
            (80000, 120000),
            (60000, 90000),
            (100000, 150000)
        ]

        result = job_type_service._generate_aggregations(filters)

        assert 'security_areas' in result
        assert 'seniority_levels' in result
        assert 'demand_levels' in result
        assert 'salary_ranges' in result

    def test_validate_job_type_references_valid(self, job_type_service, mock_db_session):
        """Test validation of valid job type references."""
        job_type_ids = [1, 2, 3]

        # Mock existing IDs
        mock_db_session.query.return_value.filter.return_value.all.return_value = [
            (1,), (2,), (3,)
        ]

        # Should not raise exception
        job_type_service._validate_job_type_references(job_type_ids)

    def test_validate_job_type_references_invalid(self, job_type_service, mock_db_session):
        """Test validation of invalid job type references."""
        job_type_ids = [1, 2, 999]  # 999 doesn't exist

        # Mock existing IDs (missing 999)
        mock_db_session.query.return_value.filter.return_value.all.return_value = [
            (1,), (2,)
        ]

        with pytest.raises(ValidationError, match="Invalid job type references: \\[999\\]"):
            job_type_service._validate_job_type_references(job_type_ids)

    def test_validate_job_type_references_empty_list(self, job_type_service, mock_db_session):
        """Test validation with empty reference list."""
        # Should not raise exception and not query database
        job_type_service._validate_job_type_references([])
        mock_db_session.query.assert_not_called()

    def test_apply_filters_comprehensive(self, job_type_service, mock_db_session):
        """Test comprehensive filter application."""
        filters = JobTypeFilters(
            security_area=["Security Architecture and Engineering"],
            seniority_level=["intermediate", "advanced"],
            job_family=["security_engineer"],
            salary_min=80000,
            salary_max=150000,
            remote_friendly=True,
            demand_level=["high", "critical"],
            required_certifications=["Security+"],
            preferred_certifications=["CISSP"]
        )

        # Mock query
        mock_query = Mock()

        # Test filter application
        result_query = job_type_service._apply_filters(mock_query, filters)

        # Verify filter method was called multiple times
        assert mock_query.filter.call_count >= 1

    def test_error_handling_database_error(self, job_type_service, mock_db_session):
        """Test error handling for database errors."""
        create_request = JobTypeCreateRequest(
            title="Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="intermediate"
        )

        # Mock database error
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        mock_db_session.add.side_effect = IntegrityError("Database error", None, None)

        with patch.object(job_type_service, '_validate_create_data', return_value=create_request):
            with pytest.raises(ConflictError, match="Creation failed due to data conflict"):
                job_type_service.create(create_request, user_id="test_user")

    def test_audit_logging_integration(self, job_type_service, mock_db_session):
        """Test audit logging integration."""
        create_request = JobTypeCreateRequest(
            title="Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="intermediate"
        )

        # Mock successful creation
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        mock_db_session.add.return_value = None
        mock_db_session.commit.return_value = None
        mock_db_session.refresh.return_value = None

        mock_job_type = Mock()
        mock_job_type.id = 1

        with patch.object(job_type_service, '_validate_create_data', return_value=create_request):
            with patch.object(SecurityJobType, '__init__', return_value=None):
                with patch.object(JobTypeResponse, 'from_orm', return_value=mock_job_type):
                    # Mock audit service
                    with patch.object(job_type_service.audit_service, 'log_create', return_value=True):
                        result = job_type_service.create(create_request, user_id="test_user")

        assert result is not None
