"""Comprehensive unit tests for Agent 3 - Enterprise Analytics Engine.

This module provides comprehensive testing for all enterprise components including
compliance automation, data intelligence, authentication, and SSO integration.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from models.compliance import (
    ComplianceRequirement, ComplianceAssessment, ComplianceReport, AuditLog,
    DataProcessingActivity, ComplianceFramework, ComplianceStatus, RiskLevel
)
from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole
from services.compliance_service import ComplianceService
from services.data_intelligence_service import DataIntelligenceService
from services.enterprise_auth_service import EnterpriseAuthService, Permission
from services.sso_integration_service import SSOIntegrationService, SSOProvider


class TestComplianceService:
    """Test compliance automation service."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def compliance_service(self, mock_db):
        """Create compliance service instance."""
        return ComplianceService(mock_db)
    
    @pytest.fixture
    def sample_organization(self):
        """Sample organization for testing."""
        return EnterpriseOrganization(
            id=1,
            name="Test Corp",
            domain="testcorp.com",
            industry="Technology"
        )
    
    def test_create_compliance_requirement(self, compliance_service, mock_db):
        """Test creating compliance requirement."""
        # Arrange
        req_data = {
            'organization_id': 1,
            'framework': ComplianceFramework.GDPR,
            'requirement_id': 'GDPR-Art-32',
            'title': 'Security of processing',
            'description': 'Implement appropriate technical and organizational measures',
            'risk_level': RiskLevel.HIGH
        }
        
        mock_requirement = Mock(spec=ComplianceRequirement)
        mock_requirement.id = 1
        mock_requirement.title = req_data['title']
        mock_requirement.organization_id = req_data['organization_id']
        
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Act
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.create_compliance_requirement(req_data)
        
        # Assert
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_generate_gdpr_report(self, compliance_service, mock_db):
        """Test GDPR compliance report generation."""
        # Arrange
        org_id = 1
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)
        
        # Mock requirements
        mock_requirements = [
            Mock(
                requirement_id='GDPR-Art-32',
                title='Security of processing',
                status=ComplianceStatus.COMPLIANT,
                risk_level=RiskLevel.HIGH,
                last_assessed=datetime.now()
            ),
            Mock(
                requirement_id='GDPR-Art-33',
                title='Notification of breach',
                status=ComplianceStatus.NON_COMPLIANT,
                risk_level=RiskLevel.CRITICAL,
                last_assessed=datetime.now()
            )
        ]
        
        mock_report = Mock(spec=ComplianceReport)
        mock_report.id = 1
        mock_report.report_name = "GDPR Compliance Report - 2024-01"
        
        # Mock database queries
        compliance_service.get_organization_requirements = Mock(return_value=mock_requirements)
        mock_db.query.return_value.filter.return_value.all.return_value = []
        mock_db.query.return_value.filter.return_value.count.return_value = 5
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Act
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.generate_gdpr_report(org_id, start_date, end_date)
        
        # Assert
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_assess_compliance_requirement(self, compliance_service, mock_db):
        """Test compliance requirement assessment."""
        # Arrange
        requirement_id = 1
        assessment_data = {
            'assessment_date': datetime.now(),
            'assessor_id': 'user_123',
            'organization_id': 1,
            'status': ComplianceStatus.COMPLIANT,
            'score': 95.0,
            'findings': 'All controls implemented correctly'
        }
        
        mock_requirement = Mock(spec=ComplianceRequirement)
        mock_requirement.check_frequency = 'monthly'
        
        mock_assessment = Mock(spec=ComplianceAssessment)
        mock_assessment.id = 1
        mock_assessment.status = ComplianceStatus.COMPLIANT
        mock_assessment.organization_id = 1
        
        mock_db.query.return_value.get.return_value = mock_requirement
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Act
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.assess_compliance_requirement(requirement_id, assessment_data)
        
        # Assert
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_calculate_gdpr_compliance_score(self, compliance_service):
        """Test GDPR compliance score calculation."""
        # Arrange
        gdpr_data = {
            'compliant_requirements': [{'id': 1}, {'id': 2}],
            'non_compliant_requirements': [{'id': 3}],
            'partially_compliant_requirements': [{'id': 4}]
        }
        
        # Act
        score = compliance_service._calculate_gdpr_compliance_score(gdpr_data)
        
        # Assert
        # 2 compliant (200 points) + 1 partial (50 points) / 4 total = 62.5%
        assert score == 62.5
    
    def test_generate_hipaa_report(self, compliance_service, mock_db):
        """Test HIPAA compliance report generation."""
        # Arrange
        org_id = 1
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)
        
        mock_requirements = [
            Mock(
                requirement_id='HIPAA-164.312',
                title='Administrative safeguards',
                status=ComplianceStatus.COMPLIANT,
                risk_level=RiskLevel.HIGH,
                last_assessed=datetime.now()
            )
        ]
        
        compliance_service.get_organization_requirements = Mock(return_value=mock_requirements)
        mock_db.query.return_value.filter.return_value.count.return_value = 0
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Act
        with patch.object(compliance_service, 'audit_service') as mock_audit:
            result = compliance_service.generate_hipaa_report(org_id, start_date, end_date)
        
        # Assert
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()


class TestDataIntelligenceService:
    """Test data intelligence and analytics service."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def intelligence_service(self, mock_db):
        """Create data intelligence service instance."""
        return DataIntelligenceService(mock_db)
    
    def test_generate_salary_intelligence(self, intelligence_service, mock_db):
        """Test salary intelligence report generation."""
        # Arrange
        mock_users = [
            Mock(
                user_id='user1',
                role='Security Engineer',
                seniority_level='mid',
                location='San Francisco',
                salary_range='80000-100000',
                certifications_completed=['CISSP', 'Security+'],
                hire_date=datetime(2020, 1, 1)
            ),
            Mock(
                user_id='user2',
                role='Security Analyst',
                seniority_level='junior',
                location='New York',
                salary_range='60000-75000',
                certifications_completed=['Security+'],
                hire_date=datetime(2022, 1, 1)
            )
        ]
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_users
        
        # Act
        with patch.object(intelligence_service, 'audit_service') as mock_audit:
            result = intelligence_service.generate_salary_intelligence()
        
        # Assert
        assert 'report_id' in result
        assert 'salary_statistics' in result
        assert 'role_based_analysis' in result
        assert 'certification_impact' in result
        assert result['sample_size'] == 2
        mock_audit.log_event.assert_called_once()
    
    def test_analyze_skills_gap(self, intelligence_service, mock_db):
        """Test skills gap analysis."""
        # Arrange
        industry = "Technology"
        location = "San Francisco"
        
        # Mock current skills data
        mock_cert_data = [
            Mock(name='CISSP', study_sessions=100, practice_tests=50, completions=10),
            Mock(name='Security+', study_sessions=200, practice_tests=100, completions=25)
        ]
        
        mock_db.query.return_value.outerjoin.return_value.outerjoin.return_value.outerjoin.return_value.group_by.return_value.all.return_value = mock_cert_data
        
        # Act
        result = intelligence_service.analyze_skills_gap(industry, location)
        
        # Assert
        assert 'report_id' in result
        assert 'current_skills_landscape' in result
        assert 'market_demand' in result
        assert 'identified_gaps' in result
        assert 'priority_certifications' in result
    
    def test_parse_salary_ranges(self, intelligence_service):
        """Test salary range parsing."""
        # Arrange
        import pandas as pd
        df = pd.DataFrame({
            'salary_range': ['50000-70000', '$80K-$100K', '60-80K', None]
        })
        
        # Act
        result = intelligence_service._parse_salary_ranges(df)
        
        # Assert
        assert len(result) == 3  # One None value should be filtered out
        assert result.iloc[0]['salary_min'] == 50000
        assert result.iloc[0]['salary_max'] == 70000
        assert result.iloc[1]['salary_min'] == 80000
        assert result.iloc[1]['salary_max'] == 100000
    
    def test_calculate_skills_gaps(self, intelligence_service):
        """Test skills gap calculation."""
        # Arrange
        current_skills = {
            'popular_certifications': [
                {'name': 'CISSP', 'study_activity': 100, 'practice_activity': 50},
                {'name': 'Security+', 'study_activity': 200, 'practice_activity': 100}
            ]
        }
        
        market_demand = {
            'high_demand_skills': ['Cloud Security', 'DevSecOps'],
            'skill_demand_scores': {'Cloud Security': 95, 'DevSecOps': 90}
        }
        
        # Act
        result = intelligence_service._calculate_skills_gaps(current_skills, market_demand)
        
        # Assert
        assert 'critical_gaps' in result
        assert 'moderate_gaps' in result
        assert 'minor_gaps' in result
        assert len(result['critical_gaps']) + len(result['moderate_gaps']) > 0


class TestEnterpriseAuthService:
    """Test enterprise authentication and authorization service."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def auth_service(self, mock_db):
        """Create auth service instance."""
        return EnterpriseAuthService(mock_db)
    
    @pytest.fixture
    def sample_user(self):
        """Sample user for testing."""
        return EnterpriseUser(
            user_id='user_123',
            email='<EMAIL>',
            role=UserRole.MANAGER,
            organization_id=1,
            is_active=True,
            password_hash='hashed_password'
        )
    
    def test_authenticate_user_success(self, auth_service, mock_db, sample_user):
        """Test successful user authentication."""
        # Arrange
        email = '<EMAIL>'
        password = 'password123'
        
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        mock_db.commit.return_value = None
        
        # Mock password verification
        with patch.object(auth_service, '_verify_password', return_value=True):
            with patch.object(auth_service, 'audit_service') as mock_audit:
                # Act
                result = auth_service.authenticate_user(email, password)
        
        # Assert
        assert 'access_token' in result
        assert 'user' in result
        assert 'permissions' in result
        assert result['user']['email'] == email
        mock_audit.log_event.assert_called_once()
    
    def test_authenticate_user_invalid_credentials(self, auth_service, mock_db):
        """Test authentication with invalid credentials."""
        # Arrange
        email = '<EMAIL>'
        password = 'wrong_password'
        
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Act & Assert
        with pytest.raises(ValueError, match="Invalid credentials"):
            auth_service.authenticate_user(email, password)
    
    def test_check_permission_success(self, auth_service, mock_db, sample_user):
        """Test successful permission check."""
        # Arrange
        user_id = 'user_123'
        permission = Permission.USER_VIEW
        
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        # Act
        result = auth_service.check_permission(user_id, permission)
        
        # Assert
        assert result is True
    
    def test_check_permission_denied(self, auth_service, mock_db, sample_user):
        """Test permission denied."""
        # Arrange
        user_id = 'user_123'
        permission = Permission.ORG_ADMIN  # Manager doesn't have org admin permission
        
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        # Act
        with patch.object(auth_service, 'audit_service') as mock_audit:
            result = auth_service.check_permission(user_id, permission)
        
        # Assert
        assert result is False
        mock_audit.log_event.assert_called_once()
    
    def test_enforce_tenant_isolation(self, auth_service, mock_db, sample_user):
        """Test multi-tenant data isolation."""
        # Arrange
        user_id = 'user_123'
        requested_org_id = 1  # Same as user's org
        
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        # Act
        result = auth_service.enforce_tenant_isolation(user_id, requested_org_id)
        
        # Assert
        assert result is True
    
    def test_enforce_tenant_isolation_denied(self, auth_service, mock_db, sample_user):
        """Test tenant isolation denial."""
        # Arrange
        user_id = 'user_123'
        requested_org_id = 2  # Different from user's org
        
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        # Act
        result = auth_service.enforce_tenant_isolation(user_id, requested_org_id)
        
        # Assert
        assert result is False
    
    def test_generate_access_token(self, auth_service, sample_user):
        """Test access token generation."""
        # Act
        token = auth_service._generate_access_token(sample_user)
        
        # Assert
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_verify_access_token(self, auth_service, mock_db, sample_user):
        """Test access token verification."""
        # Arrange
        token = auth_service._generate_access_token(sample_user)
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        # Act
        payload = auth_service.verify_access_token(token)
        
        # Assert
        assert payload['user_id'] == sample_user.user_id
        assert payload['email'] == sample_user.email
        assert payload['organization_id'] == sample_user.organization_id


class TestSSOIntegrationService:
    """Test SSO integration service."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def sso_service(self, mock_db):
        """Create SSO service instance."""
        return SSOIntegrationService(mock_db)
    
    @pytest.fixture
    def sample_organization(self):
        """Sample organization for testing."""
        return EnterpriseOrganization(
            id=1,
            name="Test Corp",
            domain="testcorp.com",
            sso_settings={}
        )
    
    def test_configure_saml_integration(self, sso_service, mock_db, sample_organization):
        """Test SAML SSO configuration."""
        # Arrange
        org_id = 1
        saml_config = {
            'entity_id': 'https://testcorp.com/saml',
            'sso_url': 'https://idp.testcorp.com/sso',
            'x509_cert': 'MIIC...',
            'auto_provisioning': True
        }
        
        mock_db.query.return_value.get.return_value = sample_organization
        mock_db.commit.return_value = None
        
        # Act
        with patch.object(sso_service, 'audit_service') as mock_audit:
            result = sso_service.configure_saml_integration(org_id, saml_config)
        
        # Assert
        assert result['status'] == 'configured'
        assert result['provider'] == 'SAML'
        assert 'sp_metadata' in result
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_configure_oidc_integration(self, sso_service, mock_db, sample_organization):
        """Test OIDC SSO configuration."""
        # Arrange
        org_id = 1
        oidc_config = {
            'issuer': 'https://idp.testcorp.com',
            'client_id': 'client123',
            'client_secret': 'secret456',
            'authorization_endpoint': 'https://idp.testcorp.com/auth',
            'token_endpoint': 'https://idp.testcorp.com/token',
            'userinfo_endpoint': 'https://idp.testcorp.com/userinfo'
        }
        
        mock_db.query.return_value.get.return_value = sample_organization
        mock_db.commit.return_value = None
        
        # Act
        with patch.object(sso_service, 'audit_service') as mock_audit:
            result = sso_service.configure_oidc_integration(org_id, oidc_config)
        
        # Assert
        assert result['status'] == 'configured'
        assert result['provider'] == 'OIDC'
        assert 'authorization_url' in result
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_validate_saml_config_missing_fields(self, sso_service):
        """Test SAML config validation with missing fields."""
        # Arrange
        invalid_config = {
            'entity_id': 'https://testcorp.com/saml'
            # Missing required fields
        }
        
        # Act & Assert
        with pytest.raises(ValueError, match="Missing required SAML field"):
            sso_service._validate_saml_config(invalid_config)
    
    def test_validate_oidc_config_missing_fields(self, sso_service):
        """Test OIDC config validation with missing fields."""
        # Arrange
        invalid_config = {
            'issuer': 'https://idp.testcorp.com'
            # Missing required fields
        }
        
        # Act & Assert
        with pytest.raises(ValueError, match="Missing required OIDC field"):
            sso_service._validate_oidc_config(invalid_config)
