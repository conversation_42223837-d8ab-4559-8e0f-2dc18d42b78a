"""Comprehensive tests for model validation across all models"""
import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.exc import IntegrityError, DataError
from typing import List, Dict, Any
import json

# Import all models for comprehensive testing
from models.user_experience import UserExperience
from models.learning_path import LearningPath, LearningPathItem, StudyResource
from models.certification import Certification, Organization, CertificationVersion
from models.security_domain import SecurityDomain
from models.cost_calculation import CurrencyRate, CostScenario, CostCalculation, CostHistory
from models.career_transition import CareerRole, CareerTransitionPath, CareerTransitionPlan, CareerTransitionStep
from models.progress_tracking import StudySession, PracticeTestResult, LearningGoal, Achievement, LearningAnalytics
from models.enterprise import EnterpriseOrganization, Department, EnterpriseUser, UserLicense, OrganizationAnalytics
from models.security_career_framework import SecurityJobType, SecurityCareerPath, SecuritySkillMatrix, SecurityMarketData
from models.audit_log import AuditLog
from models.study_session import StudyTimerSession, StudyGoal, StudyStreak

# ============================================================================
# CORE MODEL VALIDATION TESTS
# ============================================================================

class TestUserExperienceValidation:
    """Comprehensive tests for UserExperience model validation"""

    def test_required_fields_validation(self, db_session):
        """Test that required fields are properly validated"""
        # Test empty model
        empty_user_exp = UserExperience()
        db_session.add(empty_user_exp)
        with pytest.raises(IntegrityError):
            db_session.commit()
        db_session.rollback()

    def test_valid_user_experience_creation(self, db_session):
        """Test creating valid UserExperience instance"""
        current_time = datetime.utcnow()
        valid_user_exp = UserExperience(
            user_id="test_user_123",
            years_experience=5,
            user_role="Security Analyst",
            desired_role="Security Engineer",
            expertise_areas=["Network Security", "Cloud Security"],
            preferred_learning_style="Visual",
            study_time_available=10,
            created_at=current_time,
            updated_at=current_time
        )
        db_session.add(valid_user_exp)
        db_session.commit()

        assert valid_user_exp.id is not None
        assert valid_user_exp.user_id == "test_user_123"
        assert valid_user_exp.years_experience == 5
        assert len(valid_user_exp.expertise_areas) == 2

    def test_invalid_study_time_validation(self, db_session):
        """Test validation of study time constraints"""
        invalid_study_time = UserExperience(
            user_id="test_user_negative",
            years_experience=5,
            user_role="Security Analyst",
            study_time_available=-1  # Invalid negative hours
        )
        db_session.add(invalid_study_time)
        with pytest.raises(IntegrityError):
            db_session.commit()
        db_session.rollback()

    def test_boundary_values(self, db_session):
        """Test boundary values for numeric fields"""
        # Test maximum reasonable values
        max_values_user = UserExperience(
            user_id="test_user_max",
            years_experience=50,  # Maximum career length
            user_role="Senior Security Architect",
            study_time_available=168,  # Maximum hours per week
            created_at=datetime.utcnow()
        )
        db_session.add(max_values_user)
        db_session.commit()
        assert max_values_user.id is not None

    def test_json_field_validation(self, db_session):
        """Test JSON field handling for expertise_areas"""
        user_with_json = UserExperience(
            user_id="test_user_json",
            years_experience=3,
            user_role="Security Analyst",
            expertise_areas=["Web Security", "Mobile Security", "IoT Security"],
            created_at=datetime.utcnow()
        )
        db_session.add(user_with_json)
        db_session.commit()

        # Verify JSON field is properly stored and retrieved
        retrieved_user = db_session.query(UserExperience).filter_by(user_id="test_user_json").first()
        assert len(retrieved_user.expertise_areas) == 3
        assert "Web Security" in retrieved_user.expertise_areas

class TestCertificationValidation:
    """Comprehensive tests for Certification model validation"""

    def test_certification_with_organization(self, db_session, organization_factory):
        """Test certification creation with valid organization"""
        org = organization_factory()

        valid_cert = Certification(
            name="CISSP - Certified Information Systems Security Professional",
            category="Security",
            domain="Information Security",
            level="Expert",
            difficulty=5,
            cost=749,
            description="Advanced security certification for experienced professionals",
            validity_period=36,
            exam_code="CISSP-001",
            organization_id=org.id,
            focus="Information Security",
            custom_hours=150,
            created_at=datetime.utcnow()
        )
        db_session.add(valid_cert)
        db_session.commit()

        assert valid_cert.id is not None
        assert valid_cert.organization_id == org.id
        assert valid_cert.difficulty == 5

    def test_certification_required_fields(self, db_session):
        """Test that required fields are validated"""
        empty_cert = Certification()
        db_session.add(empty_cert)
        with pytest.raises(IntegrityError):
            db_session.commit()
        db_session.rollback()

    def test_difficulty_level_constraints(self, db_session, organization_factory):
        """Test difficulty level validation (should be 1-5)"""
        org = organization_factory()

        # Test invalid difficulty levels
        for invalid_difficulty in [0, 6, -1, 10]:
            invalid_cert = Certification(
                name=f"Invalid Cert {invalid_difficulty}",
                category="Security",
                domain="Network Security",
                level="Entry Level",
                difficulty=invalid_difficulty,
                organization_id=org.id,
                created_at=datetime.utcnow()
            )
            db_session.add(invalid_cert)
            with pytest.raises((IntegrityError, DataError)):
                db_session.commit()
            db_session.rollback()

    def test_cost_validation(self, db_session, organization_factory):
        """Test cost field validation"""
        org = organization_factory()

        # Test negative cost
        negative_cost_cert = Certification(
            name="Negative Cost Cert",
            category="Security",
            domain="Network Security",
            level="Entry Level",
            difficulty=3,
            cost=-100,  # Invalid negative cost
            organization_id=org.id,
            created_at=datetime.utcnow()
        )
        db_session.add(negative_cost_cert)
        with pytest.raises((IntegrityError, DataError)):
            db_session.commit()
        db_session.rollback()

    def test_exam_code_uniqueness(self, db_session, organization_factory):
        """Test that exam codes should be unique"""
        org = organization_factory()

        # Create first certification
        cert1 = Certification(
            name="First Cert",
            category="Security",
            domain="Network Security",
            level="Entry Level",
            difficulty=2,
            exam_code="UNIQUE-001",
            organization_id=org.id,
            created_at=datetime.utcnow()
        )
        db_session.add(cert1)
        db_session.commit()

        # Try to create second certification with same exam code
        cert2 = Certification(
            name="Second Cert",
            category="Security",
            domain="Cloud Security",
            level="Intermediate",
            difficulty=3,
            exam_code="UNIQUE-001",  # Duplicate exam code
            organization_id=org.id,
            created_at=datetime.utcnow()
        )
        db_session.add(cert2)
        with pytest.raises(IntegrityError):
            db_session.commit()
        db_session.rollback()

class TestCostCalculationValidation:
    """Comprehensive tests for cost calculation models"""

    def test_currency_rate_validation(self, db_session):
        """Test CurrencyRate model validation"""
        # Test valid currency rate
        valid_rate = CurrencyRate(
            base_currency='USD',
            target_currency='EUR',
            rate=Decimal('0.92'),
            source='test_api',
            valid_from=datetime.utcnow(),
            valid_until=datetime.utcnow() + timedelta(days=1),
            is_active=True,
            created_at=datetime.utcnow()
        )
        db_session.add(valid_rate)
        db_session.commit()

        assert valid_rate.id is not None
        assert valid_rate.is_valid  # Should be valid
        assert valid_rate.rate == Decimal('0.92')

    def test_currency_rate_invalid_rate(self, db_session):
        """Test currency rate with invalid rate values"""
        # Test negative rate
        negative_rate = CurrencyRate(
            base_currency='USD',
            target_currency='EUR',
            rate=Decimal('-0.5'),  # Invalid negative rate
            source='test_api'
        )
        db_session.add(negative_rate)
        with pytest.raises((IntegrityError, DataError)):
            db_session.commit()
        db_session.rollback()

    def test_cost_calculation_comprehensive(self, db_session):
        """Test comprehensive CostCalculation validation"""
        valid_calculation = CostCalculation(
            user_id='test_user_cost',
            name='Comprehensive Security Path',
            certification_ids=[1, 2, 3, 4],
            exam_fees_total=Decimal('2000.00'),
            materials_cost=Decimal('800.00'),
            training_cost=Decimal('1500.00'),
            retake_cost=Decimal('400.00'),
            additional_costs=Decimal('200.00'),
            base_currency='USD',
            target_currency='EUR',
            exchange_rate_used=Decimal('0.92'),
            scenario_id=None,
            created_at=datetime.utcnow()
        )

        # Test total calculation
        valid_calculation.update_totals()
        db_session.add(valid_calculation)
        db_session.commit()

        assert valid_calculation.id is not None
        assert valid_calculation.certification_count == 4
        assert valid_calculation.total_cost_base == Decimal('4900.00')
        assert valid_calculation.total_cost_target == Decimal('4508.00')  # 4900 * 0.92
        assert valid_calculation.average_cost_per_certification == Decimal('1127.00')

    def test_cost_calculation_edge_cases(self, db_session):
        """Test edge cases for cost calculations"""
        # Test with zero certifications
        zero_cert_calc = CostCalculation(
            user_id='test_user_zero',
            name='Zero Certifications',
            certification_ids=[],
            exam_fees_total=Decimal('0.00'),
            created_at=datetime.utcnow()
        )
        db_session.add(zero_cert_calc)
        db_session.commit()

        assert zero_cert_calc.certification_count == 0
        assert zero_cert_calc.average_cost_per_certification == Decimal('0.00')

    def test_cost_history_tracking(self, db_session):
        """Test cost history model for price tracking"""
        # Create initial cost history
        initial_cost = CostHistory(
            certification_id=1,
            cost=Decimal('500.00'),
            currency='USD',
            previous_cost=None,
            source='manual_entry',
            created_at=datetime.utcnow()
        )
        db_session.add(initial_cost)
        db_session.commit()

        assert initial_cost.cost_change_direction is None  # No previous cost

        # Create price increase
        price_increase = CostHistory(
            certification_id=1,
            cost=Decimal('600.00'),
            currency='USD',
            previous_cost=Decimal('500.00'),
            source='api_update',
            created_at=datetime.utcnow()
        )
        price_increase.calculate_change_percentage()
        db_session.add(price_increase)
        db_session.commit()

        assert price_increase.cost_change_direction == 'increase'
        assert price_increase.change_percentage == Decimal('20.0')

    def test_cost_scenario_validation(self, db_session):
        """Test cost scenario model validation"""
        scenario = CostScenario(
            name='Bootcamp Training',
            scenario_type='bootcamp',
            description='Intensive bootcamp training scenario',
            materials_multiplier=Decimal('1.5'),
            training_multiplier=Decimal('2.0'),
            retake_probability=Decimal('0.1'),
            study_time_multiplier=Decimal('0.8'),
            is_active=True,
            created_at=datetime.utcnow()
        )
        db_session.add(scenario)
        db_session.commit()

        assert scenario.id is not None
        assert scenario.scenario_type == 'bootcamp'
        assert scenario.retake_probability == Decimal('0.1')

def test_security_domain_validation(db_session):
    """Test SecurityDomain model validation"""
    # Test required fields
    empty_domain = SecurityDomain()
    db_session.add(empty_domain)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

    # Test valid domain
    valid_domain = SecurityDomain(
        name="Application Security",
        description="Application security testing and practices",
        created_at=datetime.utcnow()
    )
    db_session.add(valid_domain)
    db_session.commit()
    assert valid_domain.id is not None

    # Test duplicate domain name
    duplicate_domain = SecurityDomain(
        name="Application Security",  # Duplicate name
        description="Different description"
    )
    db_session.add(duplicate_domain)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

def test_learning_path_validation(db_session):
    """Test LearningPath model validation"""
    # Create test user experience first
    user_exp = UserExperience(
        user_id="test_user",
        years_experience=5,
        user_role="Security Analyst",
        desired_role="Security Engineer",
        expertise_areas=["Network Security"],
        preferred_learning_style="Visual",
        study_time_available=10,
        created_at=datetime.utcnow()
    )
    db_session.add(user_exp)
    db_session.commit()

    # Test required fields
    empty_path = LearningPath()
    db_session.add(empty_path)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

    # Test valid learning path
    valid_path = LearningPath(
        user_experience_id=user_exp.id,
        name="Network Security Path",
        description="Path to become a network security expert",
        estimated_duration_weeks=12,
        difficulty_level="Intermediate",
        status="active",
        created_at=datetime.utcnow()
    )
    db_session.add(valid_path)
    db_session.commit()
    assert valid_path.id is not None

    # Test invalid duration
    invalid_duration = LearningPath(
        user_experience_id=user_exp.id,
        name="Invalid Path",
        description="Test path",
        estimated_duration_weeks=-1,  # Invalid negative weeks
        difficulty_level="Beginner",
        status="active"
    )
    db_session.add(invalid_duration)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()
