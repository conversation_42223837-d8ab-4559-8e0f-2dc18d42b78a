"""Test suite for JobRole model validation"""
import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from models.job_role import Job<PERSON><PERSON>

def test_job_role_creation(db_session):
    """Test creating a job role with valid data"""
    job_role = JobRole(
        title="Security Engineer",
        description="Responsible for system security",
        domain="Application Security",
        responsibilities="secure coding,vulnerability assessment,threat modeling"
    )
    db_session.add(job_role)
    db_session.commit()
    
    assert job_role.id is not None
    assert job_role.title == "Security Engineer"
    assert len(job_role.to_dict()['key_responsibilities']) == 3

def test_job_role_required_fields(db_session):
    """Test that required fields raise appropriate errors when missing"""
    # Test missing title
    invalid_role = JobRole(
        description="Test description",
        domain="Test Domain"
    )
    db_session.add(invalid_role)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

    # Test missing domain
    invalid_role = JobRole(
        title="Test Title",
        description="Test description"
    )
    db_session.add(invalid_role)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

def test_job_role_unique_title(db_session):
    """Test that job titles must be unique"""
    # Create first role
    role1 = JobRole(
        title="Security Analyst",
        domain="Security Operations",
        description="First analyst role"
    )
    db_session.add(role1)
    db_session.commit()

    # Try to create second role with same title
    role2 = JobRole(
        title="Security Analyst",
        domain="Application Security",
        description="Second analyst role"
    )
    db_session.add(role2)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

def test_job_role_to_dict(db_session):
    """Test the to_dict method handles all fields correctly"""
    job_role = JobRole(
        title="Security Architect",
        description="Design secure systems",
        domain="Enterprise Security",
        responsibilities="architecture review,threat modeling,security design"
    )
    db_session.add(job_role)
    db_session.commit()

    role_dict = job_role.to_dict()
    assert role_dict['id'] == job_role.id
    assert role_dict['title'] == "Security Architect"
    assert role_dict['description'] == "Design secure systems"
    assert role_dict['domain'] == "Enterprise Security"
    assert len(role_dict['key_responsibilities']) == 3
    assert "architecture review" in role_dict['key_responsibilities']
