"""Tests for model relationship integrity validation"""
import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from models.certification import Certification, Organization, CertificationVersion
from models.security_domain import SecurityDomain
from models.job_role import Job<PERSON><PERSON>

def test_organization_certification_relationship(db_session):
    """Test relationship integrity between Organization and Certification"""
    # Create test security domain first
    domain = SecurityDomain(
        name="Network Security",
        description="Network security domain"
    )
    db_session.add(domain)
    db_session.commit()

    # Create test organization
    org = Organization(
        name="Test Org",
        country="US",
        description="Test organization"
    )
    db_session.add(org)
    db_session.commit()

    # Create certification with valid organization
    cert = Certification(
        name="Test Cert",
        category="Security",
        domain="Network Security",
        level="Intermediate",
        focus="Network",
        difficulty=2,
        organization_id=org.id
    )

    # Validate relationships
    validation_results = cert.validate_relationships(db_session)
    assert validation_results['organization']
    assert validation_results['domain']

    # Test with invalid organization_id
    invalid_cert = Certification(
        name="Invalid Cert",
        category="Security",
        domain="Network Security",
        level="Intermediate",
        focus="Network",
        difficulty=2,
        organization_id=9999  # Non-existent organization_id
    )

    validation_results = invalid_cert.validate_relationships(db_session)
    assert not validation_results.get('organization', True)

def test_certification_domain_relationship(db_session):
    """Test relationship integrity for certification domains"""
    # Create test security domain
    domain = SecurityDomain(
        name="Network Security",
        description="Network security domain"
    )
    db_session.add(domain)
    db_session.commit()

    # Create certification with valid domain
    cert = Certification(
        name="Test Cert",
        category="Security",
        domain="Network Security",
        level="Intermediate",
        focus="Network",
        difficulty=2
    )

    validation_results = cert.validate_relationships(db_session)
    assert validation_results['domain']

    # Test with invalid domain
    invalid_cert = Certification(
        name="Invalid Domain Cert",
        category="Security",
        domain="Invalid Domain",
        level="Intermediate",
        focus="Network",
        difficulty=2
    )

    validation_results = invalid_cert.validate_relationships(db_session)
    assert not validation_results['domain']

def test_certification_version_integrity(db_session):
    """Test version integrity validation"""
    # Create test security domain
    domain = SecurityDomain(
        name="Network Security",
        description="Network security domain"
    )
    db_session.add(domain)
    db_session.commit()

    # Create base certification
    cert = Certification(
        name="Test Cert",
        category="Security",
        domain="Network Security",
        level="Intermediate",
        focus="Network",
        difficulty=2,
        current_version=1
    )
    db_session.add(cert)
    db_session.commit()

    # Add version history
    version1 = CertificationVersion(
        certification_id=cert.id,
        version=1,
        name="Test Cert",
        category="Security",
        domain="Network Security",
        level="Intermediate",
        focus="Network",
        difficulty=2
    )
    db_session.add(version1)
    db_session.commit()

    assert cert.validate_version_integrity(db_session)

    # Test with gap in version sequence
    invalid_version = CertificationVersion(
        certification_id=cert.id,
        version=3,  # Skip version 2
        name="Test Cert",
        category="Security",
        domain="Network Security",
        level="Intermediate",
        focus="Network",
        difficulty=2
    )
    db_session.add(invalid_version)
    db_session.commit()

    assert not cert.validate_version_integrity(db_session)

def test_soft_delete_integrity(db_session):
    """Test soft delete integrity across related records"""
    # Create test organization and security domain
    domain = SecurityDomain(
        name="Network Security",
        description="Network security domain"
    )
    db_session.add(domain)
    db_session.commit()

    org = Organization(
        name="Test Org",
        country="US",
        description="Test organization"
    )
    db_session.add(org)
    db_session.commit()

    cert = Certification(
        name="Test Cert",
        category="Security",
        domain="Network Security",
        level="Intermediate",
        focus="Network",
        difficulty=2,
        organization_id=org.id
    )
    db_session.add(cert)
    db_session.commit()

    # Test soft delete integrity
    assert cert.validate_soft_delete_integrity(db_session)

    # Soft delete organization
    org.soft_delete()
    db_session.commit()

    # Certification should detect integrity violation since parent is deleted
    assert not cert.validate_soft_delete_integrity(db_session)