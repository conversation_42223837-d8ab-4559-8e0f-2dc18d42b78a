import pytest
from utils.certification_normalizer import normalize_url, normalize_focus, normalize_certification_data

def test_normalize_url():
    """Test URL normalization function"""
    assert normalize_url("example.com") == "https://example.com"
    assert normalize_url("hhttps://example.com") == "https://example.com"
    assert normalize_url("http://example.com") == "http://example.com"

def test_normalize_focus():
    """Test focus field normalization"""
    assert normalize_focus("management") == "Mgmt"
    assert normalize_focus("defensive") == "Blueops"
    assert normalize_focus("Engineer") == "Engineer"
    assert normalize_focus("unknown") == "Engineer"  # Default case
