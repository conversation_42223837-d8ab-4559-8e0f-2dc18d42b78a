"""Comprehensive test suite for data intelligence service.

This module provides extensive testing for salary intelligence, skills gap analysis,
market trends, and data monetization capabilities.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
import pandas as pd
import numpy as np

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole, Department
from models.certification import Certification
from models.progress_tracking import StudySession, PracticeTestResult, Achievement
from services.data_intelligence_service import DataIntelligenceService


class TestSalaryIntelligence:
    """Test salary intelligence and benchmarking functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def intelligence_service(self, mock_db):
        return DataIntelligenceService(mock_db)
    
    @pytest.fixture
    def sample_users(self):
        """Create sample users with diverse salary and certification data."""
        return [
            Mock(
                user_id='user1',
                role='Security Engineer',
                seniority_level='mid',
                location='San Francisco',
                salary_range='90000-110000',
                certifications_completed=['CISSP', 'Security+'],
                hire_date=datetime(2020, 1, 1)
            ),
            Mock(
                user_id='user2',
                role='Security Analyst',
                seniority_level='junior',
                location='New York',
                salary_range='65000-80000',
                certifications_completed=['Security+'],
                hire_date=datetime(2022, 6, 1)
            ),
            Mock(
                user_id='user3',
                role='CISO',
                seniority_level='senior',
                location='San Francisco',
                salary_range='180000-220000',
                certifications_completed=['CISSP', 'CISM', 'CISA'],
                hire_date=datetime(2018, 3, 1)
            ),
            Mock(
                user_id='user4',
                role='Security Architect',
                seniority_level='senior',
                location='Austin',
                salary_range='130000-160000',
                certifications_completed=['CISSP', 'SABSA'],
                hire_date=datetime(2019, 9, 1)
            ),
            Mock(
                user_id='user5',
                role='Penetration Tester',
                seniority_level='mid',
                location='Denver',
                salary_range='85000-105000',
                certifications_completed=['CEH', 'OSCP'],
                hire_date=datetime(2021, 2, 1)
            )
        ]
    
    def test_generate_salary_intelligence_comprehensive(self, intelligence_service, mock_db, sample_users):
        """Test comprehensive salary intelligence report generation."""
        mock_db.query.return_value.filter.return_value.all.return_value = sample_users
        
        with patch.object(intelligence_service, 'audit_service') as mock_audit:
            result = intelligence_service.generate_salary_intelligence()
        
        # Verify report structure
        assert 'report_id' in result
        assert 'generated_at' in result
        assert 'sample_size' in result
        assert 'salary_statistics' in result
        assert 'role_based_analysis' in result
        assert 'location_based_analysis' in result
        assert 'certification_impact' in result
        assert 'career_progression' in result
        assert 'recommendations' in result
        
        # Verify sample size
        assert result['sample_size'] == 5
        
        # Verify audit logging
        mock_audit.log_event.assert_called_once()
    
    def test_generate_salary_intelligence_with_filters(self, intelligence_service, mock_db, sample_users):
        """Test salary intelligence with location and role filters."""
        # Filter for San Francisco only
        sf_users = [user for user in sample_users if user.location == 'San Francisco']
        mock_db.query.return_value.filter.return_value.all.return_value = sf_users
        
        filters = {'location': 'San Francisco', 'role': None}
        
        with patch.object(intelligence_service, 'audit_service'):
            result = intelligence_service.generate_salary_intelligence(filters)
        
        assert result['sample_size'] == 2
        assert result['filters_applied'] == filters
    
    def test_parse_salary_ranges_various_formats(self, intelligence_service):
        """Test parsing various salary range formats."""
        df = pd.DataFrame({
            'salary_range': [
                '50000-70000',      # Standard format
                '$80K-$100K',       # Currency and K notation
                '60-80K',           # Short K format
                '120000-150000',    # Higher range
                '$200,000-$250,000', # Comma separated
                None,               # Missing data
                'Negotiable',       # Non-numeric
                '75K-90K'          # K format
            ]
        })
        
        result = intelligence_service._parse_salary_ranges(df)
        
        # Should parse valid formats and filter out invalid ones
        assert len(result) >= 5  # At least 5 valid entries
        assert 'salary_min' in result.columns
        assert 'salary_max' in result.columns
        assert 'salary_avg' in result.columns
        
        # Check specific parsing
        first_row = result.iloc[0]
        assert first_row['salary_min'] == 50000
        assert first_row['salary_max'] == 70000
        assert first_row['salary_avg'] == 60000
    
    def test_calculate_salary_statistics(self, intelligence_service):
        """Test salary statistics calculation."""
        df = pd.DataFrame({
            'salary_min': [50000, 60000, 80000, 120000, 180000],
            'salary_max': [70000, 80000, 100000, 150000, 220000],
            'salary_avg': [60000, 70000, 90000, 135000, 200000],
            'seniority_level': ['junior', 'junior', 'mid', 'senior', 'senior']
        })
        
        stats = intelligence_service._calculate_salary_statistics(df)
        
        # Check overall statistics
        assert 'overall' in stats
        assert 'mean' in stats['overall']
        assert 'median' in stats['overall']
        assert 'percentiles' in stats['overall']
        
        # Check seniority breakdown
        assert 'by_seniority' in stats
        
        # Verify calculations
        expected_mean = df['salary_avg'].mean()
        assert abs(stats['overall']['mean'] - expected_mean) < 1
    
    def test_analyze_salaries_by_role(self, intelligence_service):
        """Test role-based salary analysis."""
        df = pd.DataFrame({
            'role': ['Security Engineer', 'Security Engineer', 'CISO', 'Security Analyst'],
            'salary_min': [80000, 85000, 180000, 60000],
            'salary_max': [100000, 105000, 220000, 75000],
            'salary_avg': [90000, 95000, 200000, 67500],
            'certifications_completed': [2, 3, 4, 1]
        })
        
        analysis = intelligence_service._analyze_salaries_by_role(df)
        
        # Should have analysis for roles with sufficient data
        assert 'Security Engineer' in analysis
        assert 'CISO' in analysis
        
        # Check Security Engineer analysis
        se_analysis = analysis['Security Engineer']
        assert se_analysis['sample_size'] == 2
        assert 'salary_range' in se_analysis
        assert 'certification_correlation' in se_analysis
    
    def test_analyze_salaries_by_location(self, intelligence_service):
        """Test location-based salary analysis."""
        df = pd.DataFrame({
            'location': ['San Francisco', 'San Francisco', 'New York', 'Austin'],
            'salary_avg': [150000, 160000, 120000, 100000]
        })
        
        analysis = intelligence_service._analyze_salaries_by_location(df)
        
        # Should have analysis for locations with sufficient data
        assert 'San Francisco' in analysis
        
        # Check San Francisco analysis
        sf_analysis = analysis['San Francisco']
        assert sf_analysis['sample_size'] == 2
        assert 'salary_statistics' in sf_analysis
        assert 'cost_of_living_adjusted' in sf_analysis
    
    def test_analyze_certification_impact(self, intelligence_service):
        """Test certification impact on salary analysis."""
        df = pd.DataFrame({
            'certifications_completed': [0, 1, 2, 3, 5],
            'salary_avg': [60000, 70000, 85000, 95000, 120000]
        })
        
        analysis = intelligence_service._analyze_certification_impact(df)
        
        assert 'correlation_coefficient' in analysis
        assert 'certification_premium_percent' in analysis
        assert 'salary_by_cert_count' in analysis
        assert 'insights' in analysis
        
        # Should show positive correlation
        assert analysis['correlation_coefficient'] > 0
        assert analysis['certification_premium_percent'] > 0
    
    def test_cost_of_living_adjustment(self, intelligence_service):
        """Test cost of living adjustment calculations."""
        # Test known locations
        sf_adjusted = intelligence_service._calculate_col_adjustment('San Francisco', 140000)
        ny_adjusted = intelligence_service._calculate_col_adjustment('New York', 130000)
        austin_adjusted = intelligence_service._calculate_col_adjustment('Austin', 100000)
        
        # San Francisco should have highest adjustment
        assert sf_adjusted == 100000  # 140000 / 1.4
        assert ny_adjusted == 100000  # 130000 / 1.3
        assert austin_adjusted == 100000  # 100000 / 1.0
        
        # Unknown location should use default multiplier
        unknown_adjusted = intelligence_service._calculate_col_adjustment('Unknown City', 100000)
        assert unknown_adjusted == 100000


class TestSkillsGapAnalysis:
    """Test skills gap analysis functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def intelligence_service(self, mock_db):
        return DataIntelligenceService(mock_db)
    
    @pytest.fixture
    def sample_cert_data(self):
        """Sample certification activity data."""
        return [
            Mock(name='CISSP', study_sessions=150, practice_tests=75, completions=25),
            Mock(name='Security+', study_sessions=300, practice_tests=150, completions=60),
            Mock(name='CEH', study_sessions=100, practice_tests=50, completions=15),
            Mock(name='CISM', study_sessions=80, practice_tests=40, completions=12),
            Mock(name='AWS Security', study_sessions=200, practice_tests=100, completions=30)
        ]
    
    def test_analyze_skills_gap_comprehensive(self, intelligence_service, mock_db, sample_cert_data):
        """Test comprehensive skills gap analysis."""
        # Mock database query for certification data
        mock_db.query.return_value.outerjoin.return_value.outerjoin.return_value.outerjoin.return_value.group_by.return_value.all.return_value = sample_cert_data
        
        result = intelligence_service.analyze_skills_gap(
            industry="Technology",
            location="San Francisco",
            organization_id=1
        )
        
        # Verify report structure
        assert 'report_id' in result
        assert 'analysis_scope' in result
        assert 'current_skills_landscape' in result
        assert 'market_demand' in result
        assert 'identified_gaps' in result
        assert 'priority_certifications' in result
        assert 'training_recommendations' in result
        assert 'roi_projections' in result
        
        # Verify analysis scope
        scope = result['analysis_scope']
        assert scope['industry'] == "Technology"
        assert scope['location'] == "San Francisco"
        assert scope['organization_id'] == 1
    
    def test_get_current_skills_landscape(self, intelligence_service, mock_db, sample_cert_data):
        """Test current skills landscape analysis."""
        mock_db.query.return_value.outerjoin.return_value.outerjoin.return_value.outerjoin.return_value.group_by.return_value.all.return_value = sample_cert_data
        
        landscape = intelligence_service._get_current_skills_landscape(
            industry="Technology",
            location="San Francisco",
            organization_id=1
        )
        
        assert 'popular_certifications' in landscape
        assert 'skill_categories' in landscape
        assert 'proficiency_levels' in landscape
        
        # Check popular certifications
        popular_certs = landscape['popular_certifications']
        assert len(popular_certs) <= 20  # Top 20 limit
        
        # Should be sorted by activity
        if len(popular_certs) > 1:
            first_cert = popular_certs[0]
            assert 'name' in first_cert
            assert 'study_activity' in first_cert
            assert 'completion_count' in first_cert
    
    def test_get_market_demand_data(self, intelligence_service):
        """Test market demand data retrieval."""
        demand_data = intelligence_service._get_market_demand_data(
            industry="Technology",
            location="San Francisco"
        )
        
        assert 'high_demand_skills' in demand_data
        assert 'emerging_skills' in demand_data
        assert 'declining_skills' in demand_data
        assert 'skill_demand_scores' in demand_data
        
        # Verify data structure
        assert isinstance(demand_data['high_demand_skills'], list)
        assert isinstance(demand_data['skill_demand_scores'], dict)
        
        # Check that high demand skills have scores
        for skill in demand_data['high_demand_skills']:
            if skill in demand_data['skill_demand_scores']:
                assert demand_data['skill_demand_scores'][skill] > 0
    
    def test_calculate_skills_gaps(self, intelligence_service):
        """Test skills gap calculation."""
        current_skills = {
            'popular_certifications': [
                {'name': 'CISSP', 'study_activity': 100, 'practice_activity': 50},
                {'name': 'Security+', 'study_activity': 200, 'practice_activity': 100}
            ]
        }
        
        market_demand = {
            'high_demand_skills': ['Cloud Security', 'DevSecOps', 'Zero Trust'],
            'skill_demand_scores': {
                'Cloud Security': 95,
                'DevSecOps': 90,
                'Zero Trust': 85
            }
        }
        
        gaps = intelligence_service._calculate_skills_gaps(current_skills, market_demand)
        
        assert 'critical_gaps' in gaps
        assert 'moderate_gaps' in gaps
        assert 'minor_gaps' in gaps
        assert 'oversupplied_skills' in gaps
        
        # Should identify gaps for high-demand skills not in current landscape
        total_gaps = len(gaps['critical_gaps']) + len(gaps['moderate_gaps']) + len(gaps['minor_gaps'])
        assert total_gaps > 0
    
    def test_calculate_gap_severity(self, intelligence_service):
        """Test gap severity calculation."""
        # Test no current activity (full gap)
        severity = intelligence_service._calculate_gap_severity(0, 90)
        assert severity == 90
        
        # Test some current activity
        severity = intelligence_service._calculate_gap_severity(500, 90)  # 50% normalized activity
        assert severity == 40  # 90 - 50
        
        # Test high current activity (no gap)
        severity = intelligence_service._calculate_gap_severity(1000, 90)  # 100% normalized activity
        assert severity == 0
        
        # Test activity exceeding demand
        severity = intelligence_service._calculate_gap_severity(1200, 90)
        assert severity == 0  # No negative gaps
    
    def test_generate_skills_recommendations(self, intelligence_service):
        """Test skills recommendations generation."""
        skills_gaps = {
            'critical_gaps': [
                {'skill': 'Cloud Security', 'gap_score': 85},
                {'skill': 'DevSecOps', 'gap_score': 80}
            ],
            'moderate_gaps': [
                {'skill': 'Zero Trust', 'gap_score': 60}
            ],
            'minor_gaps': []
        }
        
        recommendations = intelligence_service._generate_skills_recommendations(skills_gaps)
        
        assert len(recommendations) == 3  # 2 critical + 1 moderate
        
        # Check critical recommendations
        critical_recs = [rec for rec in recommendations if rec['priority'] == 'critical']
        assert len(critical_recs) == 2
        
        # Check moderate recommendations
        moderate_recs = [rec for rec in recommendations if rec['priority'] == 'moderate']
        assert len(moderate_recs) == 1
        
        # Verify recommendation structure
        for rec in recommendations:
            assert 'skill' in rec
            assert 'action' in rec
            assert 'recommended_certifications' in rec
            assert 'estimated_timeline' in rec
            assert 'business_impact' in rec
    
    def test_identify_priority_certifications(self, intelligence_service):
        """Test priority certification identification."""
        skills_gaps = {
            'critical_gaps': [
                {'skill': 'Cloud Security', 'gap_score': 85},
                {'skill': 'DevSecOps', 'gap_score': 80}
            ],
            'moderate_gaps': [
                {'skill': 'Incident Response', 'gap_score': 60}
            ]
        }
        
        priority_certs = intelligence_service._identify_priority_certifications(skills_gaps)
        
        assert len(priority_certs) > 0
        
        # Should be sorted by gap score (highest first)
        if len(priority_certs) > 1:
            assert priority_certs[0]['gap_score'] >= priority_certs[1]['gap_score']
        
        # Check certification structure
        for cert in priority_certs:
            assert 'certification' in cert
            assert 'skill_addressed' in cert
            assert 'gap_score' in cert
            assert 'priority_level' in cert
    
    def test_calculate_training_roi(self, intelligence_service):
        """Test training ROI calculation."""
        skills_gaps = {
            'critical_gaps': [
                {'skill': 'Cloud Security', 'gap_score': 85},
                {'skill': 'DevSecOps', 'gap_score': 80}
            ],
            'moderate_gaps': [
                {'skill': 'Zero Trust', 'gap_score': 60}
            ]
        }
        
        roi = intelligence_service._calculate_training_roi(skills_gaps)
        
        assert 'total_investment' in roi
        assert 'annual_benefits' in roi
        assert 'roi_percentage' in roi
        assert 'payback_period_months' in roi
        assert 'break_even_point' in roi
        
        # Should have positive ROI for skills gaps
        assert roi['total_investment'] > 0
        assert roi['annual_benefits'] > 0
        assert roi['roi_percentage'] > 0
        assert roi['payback_period_months'] > 0
    
    def test_categorize_skills(self, intelligence_service):
        """Test skill categorization."""
        cert_data = [
            Mock(name='AWS Certified Security'),
            Mock(name='CISSP'),
            Mock(name='Cisco CCNA Security'),
            Mock(name='Secure Coding Practices'),
            Mock(name='Azure Security Engineer')
        ]
        
        categories = intelligence_service._categorize_skills(cert_data)
        
        # Check expected categories exist
        expected_categories = [
            'Technical Security',
            'Governance & Compliance',
            'Cloud Security',
            'Network Security',
            'Application Security'
        ]
        
        for category in expected_categories:
            assert category in categories
            assert isinstance(categories[category], list)
        
        # Check that cloud certifications are properly categorized
        assert 'AWS Certified Security' in categories['Cloud Security']
        assert 'Azure Security Engineer' in categories['Cloud Security']
        
        # Check that CISSP is in governance
        assert 'CISSP' in categories['Governance & Compliance']
