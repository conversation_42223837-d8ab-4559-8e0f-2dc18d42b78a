"""End-to-end integration tests for complete user workflows"""
import pytest
from fastapi.testclient import TestClient
from fastapi import status
from datetime import datetime, timedelta
from decimal import Decimal
import json
from unittest.mock import patch, Mock

from api.app import create_app


class TestUserJourneyIntegration:
    """Test complete user journeys from start to finish"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    @pytest.fixture
    def authenticated_user(self, client):
        """Mock authenticated user session"""
        # In a real implementation, this would handle actual authentication
        return {
            'user_id': 'test_user_123',
            'email': '<EMAIL>',
            'headers': {'Authorization': 'Bearer test-token'}
        }
    
    def test_complete_certification_planning_workflow(self, client, db_session, authenticated_user, 
                                                     organization_factory, certification_factory):
        """Test complete workflow: user profile → career path → cost calculation → study plan"""
        
        # Step 1: Create test data
        org = organization_factory(name="(ISC)²")
        cissp = certification_factory(
            name="CISSP",
            organization_id=org.id,
            cost=749,
            difficulty=5,
            domain="Information Security"
        )
        cism = certification_factory(
            name="CISM",
            organization_id=org.id,
            cost=760,
            difficulty=4,
            domain="Information Security Management"
        )
        
        # Step 2: Create user profile
        user_profile_data = {
            "user_id": authenticated_user['user_id'],
            "years_experience": 5,
            "user_role": "Security Analyst",
            "desired_role": "Security Manager",
            "expertise_areas": ["Network Security", "Risk Management"],
            "preferred_learning_style": "Mixed",
            "study_time_available": 15
        }
        
        profile_response = client.post(
            "/api/v1/user/profile",
            json=user_profile_data,
            headers=authenticated_user['headers']
        )
        assert profile_response.status_code == status.HTTP_201_CREATED
        profile_data = profile_response.json()
        assert profile_data['user_id'] == authenticated_user['user_id']
        
        # Step 3: Generate career path recommendations
        career_path_request = {
            "completed_certifications": [],
            "interests": ["Information Security", "Risk Management"],
            "years_experience": 5,
            "current_role": "Security Analyst",
            "target_role": "Security Manager",
            "learning_style": "Mixed",
            "study_hours": 15
        }
        
        with patch('utils.claude_assistant.generate_career_path') as mock_career_path:
            mock_career_path.return_value = {
                "recommended_certifications": [
                    {"name": "CISSP", "priority": "high", "reasoning": "Essential for management roles"},
                    {"name": "CISM", "priority": "medium", "reasoning": "Complements CISSP for management"}
                ],
                "learning_path": [
                    {"phase": 1, "certifications": ["CISSP"], "duration_weeks": 20},
                    {"phase": 2, "certifications": ["CISM"], "duration_weeks": 16}
                ]
            }
            
            career_response = client.post(
                "/api/v1/career-path",
                json=career_path_request,
                headers=authenticated_user['headers']
            )
            # Note: This endpoint might be disabled, so we handle both cases
            if career_response.status_code == status.HTTP_200_OK:
                career_data = career_response.json()
                assert "recommended_certifications" in career_data
        
        # Step 4: Calculate costs for recommended certifications
        cost_calculation_data = {
            "name": "Security Management Career Path",
            "certification_ids": [cissp.id, cism.id],
            "base_currency": "USD",
            "target_currency": "USD",
            "materials_cost": 800.0,
            "training_cost": 3000.0,
            "additional_costs": 500.0
        }
        
        cost_response = client.post(
            "/api/v1/cost-calculator/calculations",
            json=cost_calculation_data,
            headers=authenticated_user['headers']
        )
        assert cost_response.status_code == status.HTTP_201_CREATED
        cost_data = cost_response.json()
        assert cost_data['name'] == "Security Management Career Path"
        assert len(cost_data['certification_ids']) == 2
        assert cost_data['total_cost_base'] > 0
        
        # Step 5: Create study plan based on recommendations
        study_plan_data = {
            "user_id": authenticated_user['user_id'],
            "name": "CISSP + CISM Study Plan",
            "certification_ids": [cissp.id, cism.id],
            "target_completion_date": (datetime.now() + timedelta(weeks=40)).isoformat(),
            "weekly_study_hours": 15,
            "study_preferences": {
                "learning_style": "Mixed",
                "preferred_times": ["evening", "weekend"],
                "difficulty_progression": "gradual"
            }
        }
        
        # This would create a learning path
        learning_path_response = client.post(
            "/api/v1/learning-paths",
            json=study_plan_data,
            headers=authenticated_user['headers']
        )
        # Handle case where endpoint might not exist yet
        if learning_path_response.status_code in [status.HTTP_201_CREATED, status.HTTP_200_OK]:
            learning_data = learning_path_response.json()
            assert learning_data['name'] == "CISSP + CISM Study Plan"
    
    def test_progress_tracking_workflow(self, client, db_session, authenticated_user, certification_factory):
        """Test progress tracking workflow: study sessions → progress analysis → recommendations"""
        
        # Create test certification
        cert = certification_factory(name="Security+", difficulty=2)
        
        # Step 1: Record study sessions
        study_sessions = [
            {
                "certification_id": cert.id,
                "topic": "Network Security",
                "duration_minutes": 90,
                "performance_score": 75,
                "notes": "Covered VPN protocols and wireless security",
                "session_date": (datetime.now() - timedelta(days=7)).isoformat()
            },
            {
                "certification_id": cert.id,
                "topic": "Cryptography",
                "duration_minutes": 120,
                "performance_score": 82,
                "notes": "Symmetric vs asymmetric encryption",
                "session_date": (datetime.now() - timedelta(days=5)).isoformat()
            },
            {
                "certification_id": cert.id,
                "topic": "Risk Management",
                "duration_minutes": 60,
                "performance_score": 68,
                "notes": "Need more practice with risk calculations",
                "session_date": (datetime.now() - timedelta(days=3)).isoformat()
            }
        ]
        
        session_ids = []
        for session_data in study_sessions:
            response = client.post(
                "/api/v1/progress/study-sessions",
                json=session_data,
                headers=authenticated_user['headers']
            )
            if response.status_code == status.HTTP_201_CREATED:
                session_ids.append(response.json()['id'])
        
        # Step 2: Get progress analytics
        progress_response = client.get(
            f"/api/v1/progress/analytics?certification_id={cert.id}",
            headers=authenticated_user['headers']
        )
        
        if progress_response.status_code == status.HTTP_200_OK:
            progress_data = progress_response.json()
            assert 'total_study_time' in progress_data
            assert 'average_performance' in progress_data
            assert 'weak_areas' in progress_data
        
        # Step 3: Get AI-powered study recommendations
        recommendations_response = client.get(
            f"/api/v1/ai-assistant/recommendations?user_id={authenticated_user['user_id']}",
            headers=authenticated_user['headers']
        )
        
        if recommendations_response.status_code == status.HTTP_200_OK:
            recommendations = recommendations_response.json()
            assert 'recommendations' in recommendations
            assert len(recommendations['recommendations']) > 0


class TestDataConsistencyIntegration:
    """Test data consistency across different operations"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_cost_calculation_consistency(self, client, db_session, certification_factory):
        """Test that cost calculations remain consistent across different operations"""
        
        # Create test certifications with known costs
        cert1 = certification_factory(name="Test Cert 1", cost=500)
        cert2 = certification_factory(name="Test Cert 2", cost=750)
        
        # Create cost calculation
        calc_data = {
            "name": "Consistency Test",
            "certification_ids": [cert1.id, cert2.id],
            "base_currency": "USD",
            "target_currency": "USD",
            "materials_cost": 300.0
        }
        
        # Create calculation multiple times and verify consistency
        responses = []
        for i in range(3):
            response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)
            if response.status_code == status.HTTP_201_CREATED:
                responses.append(response.json())
        
        if len(responses) > 1:
            # All calculations should have the same base costs
            base_costs = [r['exam_fees_total'] for r in responses]
            assert all(cost == base_costs[0] for cost in base_costs)
    
    def test_concurrent_user_operations(self, client, db_session, authenticated_user):
        """Test handling of concurrent operations by the same user"""
        import threading
        import time
        
        results = []
        
        def create_study_session():
            session_data = {
                "topic": f"Concurrent Test {threading.current_thread().ident}",
                "duration_minutes": 60,
                "performance_score": 75,
                "session_date": datetime.now().isoformat()
            }
            
            response = client.post(
                "/api/v1/progress/study-sessions",
                json=session_data,
                headers=authenticated_user['headers']
            )
            results.append(response.status_code)
        
        # Create multiple concurrent requests
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=create_study_session)
            threads.append(thread)
            thread.start()
        
        # Wait for all to complete
        for thread in threads:
            thread.join()
        
        # All operations should succeed or fail gracefully
        assert all(status in [200, 201, 409, 422] for status in results)


class TestErrorHandlingIntegration:
    """Test error handling across integrated components"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_cascading_error_handling(self, client):
        """Test that errors in one component don't cascade to others"""
        
        # Test with invalid data that should be caught early
        invalid_data = {
            "name": "",  # Invalid empty name
            "certification_ids": [-1, -2],  # Invalid negative IDs
            "base_currency": "INVALID"  # Invalid currency
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=invalid_data)
        
        # Should return proper error response, not crash
        assert response.status_code in [400, 422]
        
        # Should still be able to make valid requests after error
        health_response = client.get("/api/v1/health")
        assert health_response.status_code == 200
    
    def test_database_transaction_rollback(self, client, db_session):
        """Test that database transactions are properly rolled back on errors"""
        
        # This test would verify that partial operations are rolled back
        # when errors occur during complex multi-step operations
        
        # Create a scenario that should fail partway through
        complex_data = {
            "name": "Complex Operation",
            "certification_ids": [999999],  # Non-existent certification
            "base_currency": "USD",
            "target_currency": "USD"
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=complex_data)
        
        # Should handle the error gracefully
        assert response.status_code in [400, 404, 422]
        
        # Database should be in consistent state
        # (This would require checking that no partial data was committed)
