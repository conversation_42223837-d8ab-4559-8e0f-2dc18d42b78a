"""Comprehensive API endpoint testing with advanced scenarios"""
import pytest
import json
from fastapi.testclient import TestClient
from fastapi import status
from unittest.mock import patch, Mock
from datetime import datetime, timedelta
from decimal import Decimal

from api.app import create_app


class TestHealthAndStatusEndpoints:
    """Test health check and status endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        app = create_app()
        return TestClient(app)
    
    def test_root_endpoint(self, client):
        """Test root endpoint returns API information"""
        response = client.get("/")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "documentation_url" in data
        assert data["documentation_url"] == "/docs"
    
    def test_health_check_healthy(self, client):
        """Test health check when system is healthy"""
        response = client.get("/api/v1/health")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert data["database"] == "connected"
        assert "timestamp" in data
        assert "version" in data
    
    @patch('database.get_db')
    def test_health_check_database_error(self, mock_get_db, client):
        """Test health check when database is unavailable"""
        # Mock database connection failure
        mock_db = Mock()
        mock_db.execute.side_effect = Exception("Database connection failed")
        mock_get_db.return_value = mock_db
        
        response = client.get("/api/v1/health")
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        data = response.json()
        assert data["status"] == "unhealthy"
        assert "error" in data


class TestJobSearchEndpoints:
    """Test job search API endpoints"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_job_search_basic(self, client, db_session):
        """Test basic job search functionality"""
        # Create test jobs using the factory
        from models.security_career_framework import SecurityJobType
        
        test_job = SecurityJobType(
            title="Security Analyst",
            security_area="Security Operations",
            seniority_level="entry",
            job_family="analyst",
            description="Entry-level security analyst position",
            required_skills=["Network Security", "Incident Response"],
            preferred_certifications=["Security+", "CySA+"],
            salary_range_min=60000,
            salary_range_max=80000,
            created_at=datetime.utcnow()
        )
        db_session.add(test_job)
        db_session.commit()
        
        # Test search
        response = client.get("/api/v1/jobs/search?term=Security&area=Security+Operations")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "jobs" in data
        assert len(data["jobs"]) >= 1
        assert data["jobs"][0]["title"] == "Security Analyst"
    
    def test_job_search_pagination(self, client, db_session):
        """Test job search with pagination"""
        # Create multiple test jobs
        from models.security_career_framework import SecurityJobType
        
        for i in range(5):
            job = SecurityJobType(
                title=f"Security Engineer {i}",
                security_area="Security Engineering",
                seniority_level="mid",
                job_family="engineer",
                description=f"Security engineer position {i}",
                required_skills=["Python", "AWS"],
                salary_range_min=80000,
                salary_range_max=120000,
                created_at=datetime.utcnow()
            )
            db_session.add(job)
        db_session.commit()
        
        # Test pagination
        response = client.get("/api/v1/jobs/search?page=0&per_page=3")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["jobs"]) <= 3
        assert "total" in data
        assert data["total"] >= 5
    
    def test_job_search_sql_injection_prevention(self, client):
        """Test that SQL injection attempts are prevented"""
        malicious_queries = [
            "'; DROP TABLE security_job_types; --",
            "' OR '1'='1",
            "'; DELETE FROM security_job_types WHERE '1'='1'; --"
        ]
        
        for malicious_query in malicious_queries:
            response = client.get(f"/api/v1/jobs/search?term={malicious_query}")
            # Should not cause server error
            assert response.status_code in [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
            # Should not return all records (which would indicate successful injection)
            if response.status_code == status.HTTP_200_OK:
                data = response.json()
                # Should return empty or very limited results for malicious queries
                assert len(data.get("jobs", [])) < 100  # Reasonable upper bound


class TestCostCalculatorEndpoints:
    """Test cost calculator API endpoints"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_create_cost_calculation(self, client, db_session, certification_factory):
        """Test creating a new cost calculation"""
        # Create test certifications
        cert1 = certification_factory(name="CISSP", cost=749)
        cert2 = certification_factory(name="CISM", cost=760)
        
        calculation_data = {
            "name": "Security Management Path",
            "certification_ids": [cert1.id, cert2.id],
            "base_currency": "USD",
            "target_currency": "EUR",
            "materials_cost": 500.0,
            "training_cost": 2000.0,
            "additional_costs": 200.0
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=calculation_data)
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["name"] == "Security Management Path"
        assert len(data["certification_ids"]) == 2
        assert "total_cost_base" in data
        assert "total_cost_target" in data
    
    def test_get_exchange_rates(self, client):
        """Test getting current exchange rates"""
        response = client.get("/api/v1/cost-calculator/exchange-rates")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "rates" in data
        assert isinstance(data["rates"], list)
    
    @patch('services.cost_calculator.requests.get')
    def test_exchange_rate_api_failure(self, mock_get, client):
        """Test handling of external exchange rate API failures"""
        # Mock API failure
        mock_get.side_effect = Exception("External API unavailable")
        
        response = client.get("/api/v1/cost-calculator/exchange-rates")
        # Should handle gracefully, possibly returning cached rates or defaults
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]
    
    def test_cost_calculation_validation(self, client):
        """Test input validation for cost calculations"""
        invalid_data = {
            "name": "",  # Empty name
            "certification_ids": [],  # Empty certification list
            "base_currency": "INVALID",  # Invalid currency code
            "materials_cost": -100.0  # Negative cost
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=invalid_data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
        # Should contain validation errors
        assert len(data["detail"]) > 0


class TestSecurityAndErrorHandling:
    """Test security measures and error handling"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_cors_headers(self, client):
        """Test CORS headers are properly set"""
        response = client.options("/api/v1/health")
        assert "access-control-allow-origin" in response.headers
        assert "access-control-allow-methods" in response.headers
    
    def test_rate_limiting_headers(self, client):
        """Test rate limiting headers (if implemented)"""
        response = client.get("/api/v1/health")
        # Check if rate limiting headers are present
        # This test assumes rate limiting is implemented
        # Adjust based on actual implementation
        assert response.status_code == status.HTTP_200_OK
    
    def test_large_payload_handling(self, client):
        """Test handling of unusually large payloads"""
        large_data = {
            "name": "A" * 10000,  # Very long name
            "certification_ids": list(range(1000)),  # Many certifications
            "description": "B" * 50000  # Very long description
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=large_data)
        # Should either accept or reject gracefully
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            status.HTTP_422_UNPROCESSABLE_ENTITY
        ]
    
    def test_malformed_json_handling(self, client):
        """Test handling of malformed JSON requests"""
        malformed_json = '{"name": "test", "invalid": json}'
        
        response = client.post(
            "/api/v1/cost-calculator/calculations",
            data=malformed_json,
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_unsupported_media_type(self, client):
        """Test handling of unsupported media types"""
        response = client.post(
            "/api/v1/cost-calculator/calculations",
            data="<xml>test</xml>",
            headers={"Content-Type": "application/xml"}
        )
        assert response.status_code in [
            status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            status.HTTP_422_UNPROCESSABLE_ENTITY
        ]


class TestPerformanceAndConcurrency:
    """Test performance and concurrent access scenarios"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_concurrent_requests(self, client):
        """Test handling of concurrent requests"""
        import threading
        import time
        
        results = []
        
        def make_request():
            response = client.get("/api/v1/health")
            results.append(response.status_code)
        
        # Create multiple threads to simulate concurrent requests
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert all(status_code == status.HTTP_200_OK for status_code in results)
        assert len(results) == 10
    
    def test_response_time_reasonable(self, client):
        """Test that response times are reasonable"""
        import time
        
        start_time = time.time()
        response = client.get("/api/v1/health")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == status.HTTP_200_OK
        assert response_time < 5.0  # Should respond within 5 seconds
