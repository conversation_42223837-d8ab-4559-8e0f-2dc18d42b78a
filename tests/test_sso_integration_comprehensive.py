"""Comprehensive test suite for SSO integration service.

This module provides extensive testing for SAML, OIDC, LDAP, and Active Directory
integration, user provisioning, and enterprise identity provider support.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
import json

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole
from models.compliance import <PERSON>tLog, AuditEventType
from services.sso_integration_service import (
    SSOIntegrationService, SSOProvider, ProvisioningAction
)


class TestSAMLIntegration:
    """Test SAML SSO integration functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def sso_service(self, mock_db):
        return SSOIntegrationService(mock_db)
    
    @pytest.fixture
    def sample_organization(self):
        return EnterpriseOrganization(
            id=1,
            name="Test Corp",
            domain="testcorp.com",
            sso_settings={}
        )
    
    @pytest.fixture
    def valid_saml_config(self):
        return {
            'entity_id': 'https://testcorp.com/saml',
            'sso_url': 'https://idp.testcorp.com/sso',
            'slo_url': 'https://idp.testcorp.com/slo',
            'x509_cert': 'MIIC...certificate_content...',
            'attribute_mapping': {
                'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
                'first_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
                'last_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname'
            },
            'auto_provisioning': True
        }
    
    def test_configure_saml_integration_success(self, sso_service, mock_db, sample_organization, valid_saml_config):
        """Test successful SAML integration configuration."""
        mock_db.query.return_value.get.return_value = sample_organization
        mock_db.commit.return_value = None
        
        with patch.object(sso_service, 'audit_service') as mock_audit:
            result = sso_service.configure_saml_integration(1, valid_saml_config)
        
        # Verify result structure
        assert result['status'] == 'configured'
        assert result['provider'] == 'SAML'
        assert 'sp_metadata' in result
        assert 'sso_url' in result
        assert 'acs_url' in result
        
        # Verify organization SSO settings were updated
        assert sample_organization.sso_settings['provider'] == SSOProvider.SAML.value
        assert sample_organization.sso_settings['enabled'] is True
        assert 'saml' in sample_organization.sso_settings
        
        # Verify audit logging
        mock_audit.log_event.assert_called_once()
        mock_db.commit.assert_called_once()
    
    def test_configure_saml_integration_missing_entity_id(self, sso_service, mock_db, sample_organization):
        """Test SAML configuration with missing entity ID."""
        invalid_config = {
            'sso_url': 'https://idp.testcorp.com/sso',
            'x509_cert': 'MIIC...certificate_content...'
            # Missing entity_id
        }
        
        with pytest.raises(ValueError, match="Missing required SAML field: entity_id"):
            sso_service.configure_saml_integration(1, invalid_config)
    
    def test_configure_saml_integration_missing_sso_url(self, sso_service, mock_db, sample_organization):
        """Test SAML configuration with missing SSO URL."""
        invalid_config = {
            'entity_id': 'https://testcorp.com/saml',
            'x509_cert': 'MIIC...certificate_content...'
            # Missing sso_url
        }
        
        with pytest.raises(ValueError, match="Missing required SAML field: sso_url"):
            sso_service.configure_saml_integration(1, invalid_config)
    
    def test_configure_saml_integration_missing_certificate(self, sso_service, mock_db, sample_organization):
        """Test SAML configuration with missing certificate."""
        invalid_config = {
            'entity_id': 'https://testcorp.com/saml',
            'sso_url': 'https://idp.testcorp.com/sso'
            # Missing x509_cert
        }
        
        with pytest.raises(ValueError, match="Missing required SAML field: x509_cert"):
            sso_service.configure_saml_integration(1, invalid_config)
    
    def test_configure_saml_integration_organization_not_found(self, sso_service, mock_db, valid_saml_config):
        """Test SAML configuration with non-existent organization."""
        mock_db.query.return_value.get.return_value = None
        
        with pytest.raises(ValueError, match="Organization not found"):
            sso_service.configure_saml_integration(999, valid_saml_config)
    
    def test_process_saml_response_success(self, sso_service, mock_db, sample_organization):
        """Test successful SAML response processing."""
        sample_organization.sso_settings = {
            'provider': SSOProvider.SAML.value,
            'saml': {'entity_id': 'test'}
        }
        
        mock_user = Mock(spec=EnterpriseUser)
        mock_user.user_id = 'test_user'
        mock_user.email = '<EMAIL>'
        
        saml_response = 'base64_encoded_saml_response'
        
        with patch.object(sso_service, '_parse_saml_response') as mock_parse:
            with patch.object(sso_service, '_validate_saml_response', return_value=True):
                with patch.object(sso_service, '_extract_user_info_from_saml') as mock_extract:
                    with patch.object(sso_service, '_provision_user_from_sso', return_value=mock_user):
                        mock_parse.return_value = {'valid': 'saml_data'}
                        mock_extract.return_value = {'email': '<EMAIL>'}
                        
                        result = sso_service.process_saml_response(1, saml_response)
        
        assert result['user_id'] == 'test_user'
        assert result['email'] == '<EMAIL>'
        assert result['provisioned'] is True
    
    def test_process_saml_response_invalid(self, sso_service, mock_db, sample_organization):
        """Test SAML response processing with invalid response."""
        saml_response = 'invalid_base64_saml_response'
        
        with patch.object(sso_service, '_parse_saml_response') as mock_parse:
            with patch.object(sso_service, '_validate_saml_response', return_value=False):
                mock_parse.return_value = {'invalid': 'saml_data'}
                
                with pytest.raises(ValueError, match="Invalid SAML response"):
                    sso_service.process_saml_response(1, saml_response)


class TestOIDCIntegration:
    """Test OIDC SSO integration functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def sso_service(self, mock_db):
        return SSOIntegrationService(mock_db)
    
    @pytest.fixture
    def sample_organization(self):
        return EnterpriseOrganization(
            id=1,
            name="Test Corp",
            domain="testcorp.com",
            sso_settings={}
        )
    
    @pytest.fixture
    def valid_oidc_config(self):
        return {
            'issuer': 'https://idp.testcorp.com',
            'client_id': 'client_123456',
            'client_secret': 'secret_abcdef',
            'authorization_endpoint': 'https://idp.testcorp.com/auth',
            'token_endpoint': 'https://idp.testcorp.com/token',
            'userinfo_endpoint': 'https://idp.testcorp.com/userinfo',
            'jwks_uri': 'https://idp.testcorp.com/.well-known/jwks.json',
            'scopes': ['openid', 'email', 'profile'],
            'auto_provisioning': True
        }
    
    def test_configure_oidc_integration_success(self, sso_service, mock_db, sample_organization, valid_oidc_config):
        """Test successful OIDC integration configuration."""
        mock_db.query.return_value.get.return_value = sample_organization
        mock_db.commit.return_value = None
        
        with patch.object(sso_service, 'audit_service') as mock_audit:
            result = sso_service.configure_oidc_integration(1, valid_oidc_config)
        
        # Verify result structure
        assert result['status'] == 'configured'
        assert result['provider'] == 'OIDC'
        assert 'authorization_url' in result
        assert 'redirect_uri' in result
        
        # Verify organization SSO settings were updated
        assert sample_organization.sso_settings['provider'] == SSOProvider.OIDC.value
        assert sample_organization.sso_settings['enabled'] is True
        assert 'oidc' in sample_organization.sso_settings
        
        # Verify OIDC configuration
        oidc_config = sample_organization.sso_settings['oidc']
        assert oidc_config['issuer'] == valid_oidc_config['issuer']
        assert oidc_config['client_id'] == valid_oidc_config['client_id']
        assert oidc_config['scopes'] == valid_oidc_config['scopes']
        
        # Verify audit logging
        mock_audit.log_event.assert_called_once()
        mock_db.commit.assert_called_once()
    
    def test_configure_oidc_integration_missing_issuer(self, sso_service, mock_db, sample_organization):
        """Test OIDC configuration with missing issuer."""
        invalid_config = {
            'client_id': 'client_123456',
            'client_secret': 'secret_abcdef',
            'authorization_endpoint': 'https://idp.testcorp.com/auth',
            'token_endpoint': 'https://idp.testcorp.com/token'
            # Missing issuer
        }
        
        with pytest.raises(ValueError, match="Missing required OIDC field: issuer"):
            sso_service.configure_oidc_integration(1, invalid_config)
    
    def test_configure_oidc_integration_missing_client_credentials(self, sso_service, mock_db, sample_organization):
        """Test OIDC configuration with missing client credentials."""
        invalid_config = {
            'issuer': 'https://idp.testcorp.com',
            'authorization_endpoint': 'https://idp.testcorp.com/auth',
            'token_endpoint': 'https://idp.testcorp.com/token'
            # Missing client_id and client_secret
        }
        
        with pytest.raises(ValueError, match="Missing required OIDC field: client_id"):
            sso_service.configure_oidc_integration(1, invalid_config)
    
    def test_process_oidc_callback_success(self, sso_service, mock_db, sample_organization, valid_oidc_config):
        """Test successful OIDC callback processing."""
        sample_organization.sso_settings = {
            'oidc': valid_oidc_config
        }
        mock_db.query.return_value.get.return_value = sample_organization
        
        mock_user = Mock(spec=EnterpriseUser)
        mock_user.user_id = 'oidc_user'
        mock_user.email = '<EMAIL>'
        
        authorization_code = 'auth_code_123'
        
        with patch.object(sso_service, '_exchange_oidc_code') as mock_exchange:
            with patch.object(sso_service, '_get_oidc_user_info') as mock_userinfo:
                with patch.object(sso_service, '_provision_user_from_sso', return_value=mock_user):
                    mock_exchange.return_value = {
                        'access_token': 'access_token_123',
                        'id_token': 'id_token_123'
                    }
                    mock_userinfo.return_value = {
                        'email': '<EMAIL>',
                        'given_name': 'Test',
                        'family_name': 'User'
                    }
                    
                    result = sso_service.process_oidc_callback(1, authorization_code)
        
        assert result['user_id'] == 'oidc_user'
        assert result['email'] == '<EMAIL>'
        assert result['provisioned'] is True
        assert 'tokens' in result
    
    def test_process_oidc_callback_not_configured(self, sso_service, mock_db, sample_organization):
        """Test OIDC callback processing when not configured."""
        sample_organization.sso_settings = {}
        mock_db.query.return_value.get.return_value = sample_organization
        
        with pytest.raises(ValueError, match="OIDC not configured for organization"):
            sso_service.process_oidc_callback(1, 'auth_code_123')


class TestLDAPIntegration:
    """Test LDAP integration functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def sso_service(self, mock_db):
        return SSOIntegrationService(mock_db)
    
    @pytest.fixture
    def sample_organization(self):
        return EnterpriseOrganization(
            id=1,
            name="Test Corp",
            domain="testcorp.com",
            sso_settings={}
        )
    
    @pytest.fixture
    def valid_ldap_config(self):
        return {
            'server': 'ldap.testcorp.com',
            'port': 389,
            'use_ssl': False,
            'bind_dn': 'cn=admin,dc=testcorp,dc=com',
            'bind_password': 'admin_password',
            'base_dn': 'dc=testcorp,dc=com',
            'user_filter': '(uid={username})',
            'attribute_mapping': {
                'email': 'mail',
                'first_name': 'givenName',
                'last_name': 'sn',
                'job_title': 'title'
            },
            'auto_provisioning': True
        }
    
    def test_configure_ldap_integration_success(self, sso_service, mock_db, sample_organization, valid_ldap_config):
        """Test successful LDAP integration configuration."""
        mock_db.query.return_value.get.return_value = sample_organization
        mock_db.commit.return_value = None
        
        with patch.object(sso_service, '_test_ldap_connection', return_value=True):
            with patch.object(sso_service, 'audit_service') as mock_audit:
                result = sso_service.configure_ldap_integration(1, valid_ldap_config)
        
        # Verify result structure
        assert result['status'] == 'configured'
        assert result['provider'] == 'LDAP'
        assert result['server'] == valid_ldap_config['server']
        assert result['connection_test'] == 'successful'
        
        # Verify organization SSO settings were updated
        assert sample_organization.sso_settings['provider'] == SSOProvider.LDAP.value
        assert sample_organization.sso_settings['enabled'] is True
        assert 'ldap' in sample_organization.sso_settings
        
        # Verify LDAP configuration
        ldap_config = sample_organization.sso_settings['ldap']
        assert ldap_config['server'] == valid_ldap_config['server']
        assert ldap_config['port'] == valid_ldap_config['port']
        assert ldap_config['base_dn'] == valid_ldap_config['base_dn']
        
        # Verify audit logging
        mock_audit.log_event.assert_called_once()
        mock_db.commit.assert_called_once()
    
    def test_configure_ldap_integration_connection_failed(self, sso_service, mock_db, sample_organization, valid_ldap_config):
        """Test LDAP configuration with connection failure."""
        with patch.object(sso_service, '_test_ldap_connection', return_value=False):
            with pytest.raises(ValueError, match="Unable to connect to LDAP server"):
                sso_service.configure_ldap_integration(1, valid_ldap_config)
    
    def test_configure_ldap_integration_missing_server(self, sso_service, mock_db, sample_organization):
        """Test LDAP configuration with missing server."""
        invalid_config = {
            'bind_dn': 'cn=admin,dc=testcorp,dc=com',
            'bind_password': 'admin_password',
            'base_dn': 'dc=testcorp,dc=com'
            # Missing server
        }
        
        with pytest.raises(ValueError, match="Missing required LDAP field: server"):
            sso_service.configure_ldap_integration(1, invalid_config)
    
    def test_authenticate_ldap_user_success(self, sso_service, mock_db, sample_organization, valid_ldap_config):
        """Test successful LDAP user authentication."""
        sample_organization.sso_settings = {
            'ldap': valid_ldap_config
        }
        mock_db.query.return_value.get.return_value = sample_organization
        
        mock_user = Mock(spec=EnterpriseUser)
        mock_user.user_id = 'ldap_user'
        mock_user.email = '<EMAIL>'
        
        username = 'testuser'
        password = 'userpassword'
        
        with patch.object(sso_service, '_authenticate_ldap') as mock_auth:
            with patch.object(sso_service, '_provision_user_from_sso', return_value=mock_user):
                mock_auth.return_value = {
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'User',
                    'job_title': 'Developer'
                }
                
                result = sso_service.authenticate_ldap_user(1, username, password)
        
        assert result['user_id'] == 'ldap_user'
        assert result['email'] == '<EMAIL>'
        assert result['provisioned'] is True
        assert 'ldap_attributes' in result
    
    def test_authenticate_ldap_user_failed(self, sso_service, mock_db, sample_organization, valid_ldap_config):
        """Test failed LDAP user authentication."""
        sample_organization.sso_settings = {
            'ldap': valid_ldap_config
        }
        mock_db.query.return_value.get.return_value = sample_organization
        
        username = 'testuser'
        password = 'wrongpassword'
        
        with patch.object(sso_service, '_authenticate_ldap', return_value=None):
            with pytest.raises(ValueError, match="LDAP authentication failed"):
                sso_service.authenticate_ldap_user(1, username, password)
    
    def test_authenticate_ldap_user_not_configured(self, sso_service, mock_db, sample_organization):
        """Test LDAP authentication when not configured."""
        sample_organization.sso_settings = {}
        mock_db.query.return_value.get.return_value = sample_organization
        
        with pytest.raises(ValueError, match="LDAP not configured for organization"):
            sso_service.authenticate_ldap_user(1, 'testuser', 'password')


class TestUserProvisioning:
    """Test user provisioning functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def sso_service(self, mock_db):
        return SSOIntegrationService(mock_db)
    
    def test_provision_new_user_from_sso(self, sso_service, mock_db):
        """Test provisioning a new user from SSO."""
        user_info = {
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'job_title': 'Developer',
            'provider': 'saml',
            'sso_user_id': 'saml_123456'
        }
        
        # Mock user doesn't exist
        mock_db.query.return_value.filter.return_value.first.return_value = None
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        
        with patch.object(sso_service, 'audit_service') as mock_audit:
            result = sso_service._provision_user_from_sso(1, user_info, SSOProvider.SAML)
        
        # Verify user creation
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_update_existing_user_from_sso(self, sso_service, mock_db):
        """Test updating an existing user from SSO."""
        existing_user = Mock(spec=EnterpriseUser)
        existing_user.email = '<EMAIL>'
        existing_user.first_name = 'Old'
        existing_user.last_name = 'Name'
        
        user_info = {
            'email': '<EMAIL>',
            'first_name': 'Updated',
            'last_name': 'Name',
            'job_title': 'Senior Developer',
            'provider': 'oidc',
            'sso_user_id': 'oidc_789012'
        }
        
        # Mock user exists
        mock_db.query.return_value.filter.return_value.first.return_value = existing_user
        mock_db.commit.return_value = None
        
        with patch.object(sso_service, 'audit_service') as mock_audit:
            result = sso_service._provision_user_from_sso(1, user_info, SSOProvider.OIDC)
        
        # Verify user update
        assert existing_user.first_name == 'Updated'
        assert existing_user.job_title == 'Senior Developer'
        assert existing_user.sso_provider == 'oidc'
        assert existing_user.sso_user_id == 'oidc_789012'
        
        mock_db.commit.assert_called_once()
        mock_audit.log_event.assert_called_once()
    
    def test_provision_user_missing_email(self, sso_service, mock_db):
        """Test provisioning user without email."""
        user_info = {
            'first_name': 'No',
            'last_name': 'Email',
            'provider': 'saml'
            # Missing email
        }
        
        with pytest.raises(ValueError, match="Email not provided in SSO response"):
            sso_service._provision_user_from_sso(1, user_info, SSOProvider.SAML)
    
    def test_create_user_from_sso(self, sso_service):
        """Test creating new user from SSO information."""
        user_info = {
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'job_title': 'Developer',
            'provider': 'saml',
            'sso_user_id': 'saml_123456'
        }
        
        user = sso_service._create_user_from_sso(1, user_info)
        
        # Verify user attributes
        assert user.email == user_info['email']
        assert user.first_name == user_info['first_name']
        assert user.last_name == user_info['last_name']
        assert user.job_title == user_info['job_title']
        assert user.organization_id == 1
        assert user.role == UserRole.LEARNER  # Default role
        assert user.is_active is True
        assert user.sso_provider == user_info['provider']
        assert user.sso_user_id == user_info['sso_user_id']
    
    def test_update_user_from_sso(self, sso_service):
        """Test updating user from SSO information."""
        existing_user = Mock(spec=EnterpriseUser)
        existing_user.first_name = 'Old'
        existing_user.last_name = 'Name'
        existing_user.job_title = 'Junior Developer'
        
        user_info = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'job_title': 'Senior Developer',
            'provider': 'oidc',
            'sso_user_id': 'oidc_789012'
        }
        
        sso_service._update_user_from_sso(existing_user, user_info)
        
        # Verify updates
        assert existing_user.first_name == 'Updated'
        assert existing_user.job_title == 'Senior Developer'
        assert existing_user.sso_provider == 'oidc'
        assert existing_user.sso_user_id == 'oidc_789012'
        assert existing_user.last_login is not None
