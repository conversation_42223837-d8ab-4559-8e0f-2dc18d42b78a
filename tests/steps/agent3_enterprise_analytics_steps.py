"""Step definitions for Agent 3 Enterprise Analytics Engine BDD tests."""

from behave import given, when, then, step
from fastapi.testclient import TestClient
from datetime import datetime, timedelta
import json
import time

from api.app import app
from database import get_db
from models.enterprise import EnterpriseOrganization, EnterpriseUser, Department
from models.progress_tracking import StudySession, PracticeTestResult


@given('an enterprise organization exists with {employee_count:d} employees')
def step_create_enterprise_organization(context, employee_count):
    """Create an enterprise organization with specified number of employees."""
    context.client = TestClient(app)
    context.db = next(get_db())
    
    # Create organization
    context.organization = EnterpriseOrganization(
        name="Test Enterprise Corp",
        organization_type="corporate",
        subscription_tier="enterprise",
        employee_count=employee_count,
        is_active=True
    )
    context.db.add(context.organization)
    context.db.commit()
    context.db.refresh(context.organization)
    
    # Create users
    context.users = []
    for i in range(min(employee_count, 10)):  # Limit for testing
        user = EnterpriseUser(
            user_id=f"test_user_{i+1}",
            organization_id=context.organization.id,
            employee_id=f"EMP{i+1:03d}",
            role="security_analyst",
            is_active=True
        )
        context.db.add(user)
        context.users.append(user)
    
    context.db.commit()


@given('the organization has multiple departments')
def step_create_departments(context):
    """Create multiple departments for the organization."""
    departments = [
        {"name": "IT Security", "description": "Information Technology Security"},
        {"name": "Compliance", "description": "Regulatory Compliance"},
        {"name": "Risk Management", "description": "Enterprise Risk Management"}
    ]
    
    context.departments = []
    for dept_info in departments:
        dept = Department(
            organization_id=context.organization.id,
            name=dept_info["name"],
            description=dept_info["description"]
        )
        context.db.add(dept)
        context.departments.append(dept)
    
    context.db.commit()
    
    # Assign users to departments
    for i, user in enumerate(context.users):
        user.department_id = context.departments[i % len(context.departments)].id
    
    context.db.commit()


@given('users have completed various study sessions and assessments')
def step_create_study_data(context):
    """Create study sessions and assessments for users."""
    context.study_sessions = []
    context.test_results = []
    
    for user in context.users:
        # Create study sessions
        for i in range(5):
            session = StudySession(
                user_id=user.user_id,
                certification_id="security_plus",
                session_type="reading",
                duration_minutes=60 + i * 10,
                topics_covered=["network_security", "cryptography"],
                effectiveness_rating=4,
                started_at=datetime.now() - timedelta(days=i),
                completed_at=datetime.now() - timedelta(days=i) + timedelta(hours=1)
            )
            context.db.add(session)
            context.study_sessions.append(session)
        
        # Create test results
        for i in range(3):
            test_result = PracticeTestResult(
                user_id=user.user_id,
                certification_id="security_plus",
                test_name=f"Practice Test {i+1}",
                total_questions=50,
                correct_answers=35 + i * 2,
                percentage=70 + i * 4,
                time_taken_minutes=90,
                topics_breakdown={"network_security": 0.8, "cryptography": 0.6}
            )
            context.db.add(test_result)
            context.test_results.append(test_result)
    
    context.db.commit()


@given('Agent 2 AI Study Assistant is integrated and functional')
def step_verify_agent2_integration(context):
    """Verify that Agent 2 AI Study Assistant is integrated."""
    # Test Agent 2 health endpoint
    response = context.client.get("/api/v1/ai-assistant/health")
    assert response.status_code == 200
    
    agent2_data = response.json()
    assert agent2_data["status"] == "healthy"
    assert "personalized_recommendations" in agent2_data["features"]
    
    context.agent2_available = True


@given('I am an enterprise administrator')
def step_set_admin_context(context):
    """Set the context as an enterprise administrator."""
    context.user_role = "enterprise_administrator"
    context.admin_user_id = "admin_user_1"


@when('I request enterprise study insights for my organization')
def step_request_enterprise_insights(context):
    """Request enterprise study insights."""
    context.insights_response = context.client.post(
        f"/api/v1/agent3-enterprise-analytics/enterprise-study-insights/{context.organization.id}"
    )
    context.insights_data = context.insights_response.json()


@when('I request a skills gap analysis for my organization')
def step_request_skills_gap_analysis(context):
    """Request skills gap analysis."""
    context.skills_response = context.client.post(
        f"/api/v1/agent3-enterprise-analytics/skills-gap-analysis/{context.organization.id}"
    )
    context.skills_data = context.skills_response.json()


@when('I request a {compliance_type} compliance report for the last quarter')
def step_request_compliance_report(context, compliance_type):
    """Request compliance report for specified type."""
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    end_date = datetime.now().strftime('%Y-%m-%d')
    
    context.compliance_response = context.client.post(
        f"/api/v1/agent3-enterprise-analytics/compliance-report/{context.organization.id}",
        params={
            "compliance_type": compliance_type,
            "period_start": start_date,
            "period_end": end_date
        }
    )
    context.compliance_data = context.compliance_response.json()
    context.compliance_type = compliance_type


@when('I request salary intelligence data for cybersecurity roles')
def step_request_salary_intelligence(context):
    """Request salary intelligence data."""
    context.salary_response = context.client.get(
        "/api/v1/agent3-enterprise-analytics/data-intelligence/salary-benchmarks"
    )
    context.salary_data = context.salary_response.json()


@when('I filter by industry "{industry}" and location "{location}"')
def step_filter_salary_data(context, industry, location):
    """Filter salary data by industry and location."""
    context.salary_response = context.client.get(
        "/api/v1/agent3-enterprise-analytics/data-intelligence/salary-benchmarks",
        params={
            "industry": industry,
            "location": location,
            "role": "security_analyst"
        }
    )
    context.salary_data = context.salary_response.json()


@when('I request market trend analysis for the cybersecurity industry')
def step_request_market_trends(context):
    """Request market trend analysis."""
    context.trends_response = context.client.get(
        "/api/v1/agent3-enterprise-analytics/data-intelligence/market-trends"
    )
    context.trends_data = context.trends_response.json()


@when('I specify a {period:d}-month analysis period')
def step_specify_analysis_period(context, period):
    """Specify analysis period for market trends."""
    context.trends_response = context.client.get(
        "/api/v1/agent3-enterprise-analytics/data-intelligence/market-trends",
        params={"time_period": period}
    )
    context.trends_data = context.trends_response.json()


@when('I request skills gap analysis filtered by the {department_name} department')
def step_request_department_skills_analysis(context, department_name):
    """Request skills gap analysis for specific department."""
    # Find department by name
    dept = next((d for d in context.departments if d.name == department_name), None)
    assert dept is not None, f"Department {department_name} not found"
    
    context.dept_skills_response = context.client.post(
        f"/api/v1/agent3-enterprise-analytics/skills-gap-analysis/{context.organization.id}",
        params={"department_id": dept.id}
    )
    context.dept_skills_data = context.dept_skills_response.json()


@when('I request multiple data intelligence reports simultaneously')
def step_request_multiple_reports(context):
    """Request multiple reports simultaneously to test performance."""
    import concurrent.futures
    
    def make_request(endpoint):
        return context.client.get(endpoint)
    
    endpoints = [
        "/api/v1/agent3-enterprise-analytics/data-intelligence/salary-benchmarks",
        "/api/v1/agent3-enterprise-analytics/data-intelligence/market-trends",
        "/api/v1/agent3-enterprise-analytics/health"
    ]
    
    start_time = time.time()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(make_request, endpoint) for endpoint in endpoints]
        context.concurrent_responses = [future.result() for future in futures]
    
    context.concurrent_request_time = time.time() - start_time


@then('I should receive comprehensive analytics including')
def step_verify_comprehensive_analytics(context):
    """Verify comprehensive analytics are received."""
    assert context.insights_response.status_code == 200
    assert context.insights_data["status"] == "success"
    
    data = context.insights_data["data"]
    assert "individual_insights" in data
    assert "aggregated_patterns" in data
    assert "recommendations" in data
    assert data["organization_id"] == context.organization.id


@then('I should receive detailed analysis including')
def step_verify_detailed_analysis(context):
    """Verify detailed skills gap analysis."""
    assert context.skills_response.status_code == 200
    assert context.skills_data["status"] == "success"
    
    data = context.skills_data["data"]
    assert "skills_gaps" in data
    assert "training_priorities" in data
    assert "budget_recommendations" in data
    assert data["organization_id"] == context.organization.id


@then('I should receive a comprehensive compliance report including')
def step_verify_compliance_report(context):
    """Verify comprehensive compliance report."""
    assert context.compliance_response.status_code == 200
    assert context.compliance_data["status"] == "success"
    
    data = context.compliance_data["data"]
    assert "compliance_score" in data
    assert "findings" in data
    assert "recommendations" in data
    assert "audit_trail" in data
    assert data["compliance_type"] == context.compliance_type


@then('I should receive comprehensive salary data including')
def step_verify_salary_data(context):
    """Verify comprehensive salary intelligence data."""
    assert context.salary_response.status_code == 200
    assert context.salary_data["status"] == "success"
    
    data = context.salary_data["data"]
    assert "salary_ranges" in data
    assert "certification_premium" in data
    assert "market_trends" in data


@then('I should receive trend analysis including')
def step_verify_trend_analysis(context):
    """Verify market trend analysis."""
    assert context.trends_response.status_code == 200
    assert context.trends_data["status"] == "success"
    
    data = context.trends_data["data"]
    assert "certification_trends" in data
    assert "skills_evolution" in data
    assert "job_market_analysis" in data
    assert "future_predictions" in data


@then('the insights should be generated using Agent 2\'s AI capabilities')
def step_verify_agent2_integration(context):
    """Verify Agent 2 integration in insights."""
    data = context.insights_data["data"]
    assert len(data["individual_insights"]) > 0
    
    # Check that individual insights contain AI-generated data
    for insight in data["individual_insights"]:
        assert "insights" in insight
        assert "recommendations" in insight


@then('the compliance score should be between 0 and 100%')
def step_verify_compliance_score_range(context):
    """Verify compliance score is in valid range."""
    score = context.compliance_data["data"]["compliance_score"]
    assert 0.0 <= score <= 1.0, f"Compliance score {score} is not between 0 and 1"


@then('each request should complete within acceptable time limits')
def step_verify_performance_limits(context):
    """Verify API performance limits."""
    assert context.concurrent_request_time < 10.0, f"Concurrent requests took {context.concurrent_request_time}s"
    
    for response in context.concurrent_responses:
        assert response.status_code == 200


@then('the analysis should focus only on {department_name} department users')
def step_verify_department_focus(context, department_name):
    """Verify analysis focuses on specific department."""
    assert context.dept_skills_response.status_code == 200
    data = context.dept_skills_data["data"]
    
    # Find department ID
    dept = next((d for d in context.departments if d.name == department_name), None)
    assert data["department_id"] == dept.id
