"""
Playwright + BEHAVE integration steps for authentication testing
This provides enhanced testing capabilities with <PERSON><PERSON>'s modern features
"""

from behave import given, when, then
from playwright.sync_api import expect
import time


@given('I am on the CertRats platform using Playwright')
def step_on_certrats_platform_playwright(context):
    """Navigate to the CertRats platform using Playwright"""
    context.page.goto(context.base_url)
    expect(context.page.locator('body')).to_be_visible()


@given('I navigate to the login page using Play<PERSON>')
def step_navigate_to_login_playwright(context):
    """Navigate to the login page using Playwright"""
    context.page.goto(f"{context.base_url}/login")
    expect(context.page.get_by_test_id('email-input')).to_be_visible()


@when('I enter valid email "{email}" with Playwright')
def step_enter_valid_email_playwright(context, email):
    """Enter a valid email address using Playwright"""
    context.page.get_by_test_id('email-input').fill(email)
    context.entered_email = email


@when('I enter valid password "{password}" with Playwright')
def step_enter_valid_password_playwright(context, password):
    """Enter a valid password using <PERSON><PERSON>"""
    context.page.get_by_test_id('password-input').fill(password)
    context.entered_password = password


@when('I click the "{button_text}" button with Playwright')
def step_click_button_playwright(context, button_text):
    """Click a button with specific text using Playwright"""
    if button_text.lower() == "sign in":
        context.page.get_by_test_id('login-button').click()
    else:
        context.page.get_by_role('button', name=button_text).click()


@when('I check the "Remember me" checkbox with Playwright')
def step_check_remember_me_playwright(context):
    """Check the remember me checkbox using Playwright"""
    context.page.get_by_test_id('remember-me-checkbox').check()


@when('I test keyboard navigation with Playwright')
def step_test_keyboard_navigation_playwright(context):
    """Test keyboard navigation using Playwright"""
    # Tab through form elements
    context.page.keyboard.press('Tab')
    expect(context.page.get_by_test_id('email-input')).to_be_focused()
    
    context.page.keyboard.press('Tab')
    expect(context.page.get_by_test_id('password-input')).to_be_focused()
    
    context.page.keyboard.press('Tab')
    expect(context.page.get_by_test_id('remember-me-checkbox')).to_be_focused()
    
    context.page.keyboard.press('Tab')
    expect(context.page.get_by_test_id('login-button')).to_be_focused()


@when('I test form submission with Enter key using Playwright')
def step_test_form_submission_enter_playwright(context):
    """Test form submission using Enter key with Playwright"""
    context.page.get_by_test_id('email-input').fill('<EMAIL>')
    context.page.get_by_test_id('password-input').fill('password123')
    context.page.get_by_test_id('password-input').press('Enter')


@when('I test password visibility toggle with Playwright')
def step_test_password_toggle_playwright(context):
    """Test password visibility toggle using Playwright"""
    password_input = context.page.get_by_test_id('password-input')
    password_input.fill('testpassword')
    
    # Initially should be hidden
    expect(password_input).to_have_attribute('type', 'password')
    
    # Click show password
    context.page.get_by_role('button', name='Show password').click()
    expect(password_input).to_have_attribute('type', 'text')
    
    # Click hide password
    context.page.get_by_role('button', name='Hide password').click()
    expect(password_input).to_have_attribute('type', 'password')


@when('I test mobile responsiveness with Playwright')
def step_test_mobile_responsiveness_playwright(context):
    """Test mobile responsiveness using Playwright"""
    # Set mobile viewport
    context.page.set_viewport_size({"width": 375, "height": 667})
    
    # Verify elements are still visible and functional
    expect(context.page.get_by_test_id('email-input')).to_be_visible()
    expect(context.page.get_by_test_id('password-input')).to_be_visible()
    expect(context.page.get_by_test_id('login-button')).to_be_visible()


@when('I test accessibility with Playwright')
def step_test_accessibility_playwright(context):
    """Test accessibility features using Playwright"""
    # Check for proper ARIA labels
    email_input = context.page.get_by_test_id('email-input')
    expect(email_input).to_have_attribute('aria-label')
    
    password_input = context.page.get_by_test_id('password-input')
    expect(password_input).to_have_attribute('aria-label')
    
    # Check for proper form structure
    expect(context.page.locator('form')).to_be_visible()
    
    # Check for error message accessibility
    context.page.get_by_test_id('login-button').click()
    error_messages = context.page.locator('[role="alert"]')
    expect(error_messages.first()).to_be_visible()


@when('I test performance with Playwright')
def step_test_performance_playwright(context):
    """Test performance metrics using Playwright"""
    # Start performance monitoring
    context.page.goto(f"{context.base_url}/login")
    
    # Measure page load time
    start_time = time.time()
    expect(context.page.get_by_test_id('email-input')).to_be_visible()
    load_time = time.time() - start_time
    
    # Assert performance criteria
    assert load_time < 2.5, f"Page load time {load_time}s exceeds 2.5s threshold"
    
    # Test form interaction performance
    start_time = time.time()
    context.page.get_by_test_id('email-input').fill('<EMAIL>')
    context.page.get_by_test_id('password-input').fill('password123')
    context.page.get_by_test_id('login-button').click()
    
    # Wait for navigation
    context.page.wait_for_url('**/dashboard')
    interaction_time = time.time() - start_time
    
    assert interaction_time < 3.0, f"Form submission time {interaction_time}s exceeds 3.0s threshold"


@when('I simulate network failure with Playwright')
def step_simulate_network_failure_playwright(context):
    """Simulate network failure using Playwright"""
    # Block network requests to login endpoint
    context.page.route('**/api/auth/login', lambda route: route.abort())
    
    context.page.get_by_test_id('email-input').fill('<EMAIL>')
    context.page.get_by_test_id('password-input').fill('password123')
    context.page.get_by_test_id('login-button').click()


@when('I test error handling with Playwright')
def step_test_error_handling_playwright(context):
    """Test error handling scenarios using Playwright"""
    # Test validation errors
    context.page.get_by_test_id('login-button').click()
    expect(context.page.get_by_text('Email is required')).to_be_visible()
    expect(context.page.get_by_text('Password is required')).to_be_visible()
    
    # Test invalid email format
    context.page.get_by_test_id('email-input').fill('invalid-email')
    context.page.get_by_test_id('password-input').fill('password123')
    context.page.get_by_test_id('login-button').click()
    expect(context.page.get_by_text('Please enter a valid email address')).to_be_visible()
    
    # Test short password
    context.page.get_by_test_id('email-input').fill('<EMAIL>')
    context.page.get_by_test_id('password-input').fill('123')
    context.page.get_by_test_id('login-button').click()
    expect(context.page.get_by_text('Password must be at least 8 characters')).to_be_visible()


@then('I should be redirected to the dashboard with Playwright')
def step_redirected_to_dashboard_playwright(context):
    """Verify redirection to dashboard using Playwright"""
    expect(context.page).to_have_url('**/dashboard')


@then('I should see a welcome message with Playwright')
def step_see_welcome_message_playwright(context):
    """Verify welcome message is displayed using Playwright"""
    expect(context.page.get_by_text('Welcome')).to_be_visible()


@then('I should see an error message "{error_message}" with Playwright')
def step_see_error_message_playwright(context, error_message):
    """Verify specific error message is displayed using Playwright"""
    expect(context.page.get_by_text(error_message)).to_be_visible()


@then('I should see validation errors with Playwright')
def step_see_validation_errors_playwright(context):
    """Verify validation errors are displayed using Playwright"""
    error_messages = context.page.locator('[role="alert"], .error-message, .text-red-600')
    expect(error_messages.first()).to_be_visible()


@then('the form should maintain accessibility standards with Playwright')
def step_form_accessibility_standards_playwright(context):
    """Verify form meets accessibility standards using Playwright"""
    # Check for proper labels
    expect(context.page.get_by_label('Email address')).to_be_visible()
    expect(context.page.get_by_label('Password')).to_be_visible()
    
    # Check for proper form structure
    expect(context.page.locator('form')).to_be_visible()
    
    # Check for proper button roles
    expect(context.page.get_by_role('button', name='Sign in')).to_be_visible()
    
    # Check for proper checkbox
    expect(context.page.get_by_role('checkbox', name='Remember me')).to_be_visible()


@then('the page should load within performance thresholds with Playwright')
def step_performance_thresholds_playwright(context):
    """Verify page meets performance thresholds using Playwright"""
    # This would typically be checked during the performance test step
    # Here we can verify that performance-critical elements are optimized
    
    # Check that images are optimized (if any)
    images = context.page.locator('img')
    for i in range(images.count()):
        img = images.nth(i)
        if img.is_visible():
            # Check for loading="lazy" or other optimization attributes
            loading_attr = img.get_attribute('loading')
            # Assert optimization attributes are present
    
    # Check for proper caching headers (would need network monitoring)
    # Check for minimal JavaScript bundle size (would need dev tools)


@then('the form should work correctly on mobile devices with Playwright')
def step_form_mobile_compatibility_playwright(context):
    """Verify form works correctly on mobile devices using Playwright"""
    # Set mobile viewport
    context.page.set_viewport_size({"width": 375, "height": 667})
    
    # Test form functionality
    context.page.get_by_test_id('email-input').fill('<EMAIL>')
    context.page.get_by_test_id('password-input').fill('password123')
    context.page.get_by_test_id('login-button').click()
    
    # Verify navigation works
    expect(context.page).to_have_url('**/dashboard')


@then('network errors should be handled gracefully with Playwright')
def step_network_errors_handled_playwright(context):
    """Verify network errors are handled gracefully using Playwright"""
    # Should show appropriate error message
    expect(context.page.get_by_text('Network error')).to_be_visible()
    
    # Form should remain functional
    expect(context.page.get_by_test_id('login-button')).to_be_enabled()
    
    # User should be able to retry
    expect(context.page.get_by_test_id('email-input')).to_be_editable()
    expect(context.page.get_by_test_id('password-input')).to_be_editable()
