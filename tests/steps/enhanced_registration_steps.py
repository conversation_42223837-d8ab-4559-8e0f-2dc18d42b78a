"""
Enhanced registration step definitions for BEHAVE + Playwright integration
Provides comprehensive testing for the user registration flow
"""

from behave import given, when, then
from playwright.sync_api import expect as playwright_expect
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time
import json


@given('I navigate to the registration page')
def step_navigate_to_registration(context):
    """Navigate to the registration page"""
    if context.use_playwright:
        context.page.goto(f"{context.base_url}/register")
        playwright_expect(context.page.get_by_test_id('email-input')).to_be_visible()
    else:
        context.driver.get(f"{context.base_url}/register")
        WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='email-input']"))
        )


@when('I enter valid registration details')
def step_enter_registration_details(context):
    """Enter valid registration details from table"""
    for row in context.table:
        field = row['field']
        value = row['value']
        
        if context.use_playwright:
            if field == 'email':
                context.page.fill('[data-testid="email-input"]', value)
            elif field == 'password':
                context.page.fill('[data-testid="password-input"]', value)
            elif field == 'confirmPassword':
                context.page.fill('[data-testid="confirm-password-input"]', value)
            elif field == 'firstName':
                context.page.fill('[data-testid="first-name-input"]', value)
            elif field == 'lastName':
                context.page.fill('[data-testid="last-name-input"]', value)
        else:
            element = context.driver.find_element(By.CSS_SELECTOR, f'[data-testid="{field.lower()}-input"]')
            element.clear()
            element.send_keys(value)


@when('I accept the terms and conditions')
def step_accept_terms(context):
    """Accept terms and conditions"""
    if context.use_playwright:
        context.page.check('[data-testid="terms-checkbox"]')
    else:
        checkbox = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="terms-checkbox"]')
        if not checkbox.is_selected():
            checkbox.click()


@when('I click the "{button_text}" button')
def step_click_button(context, button_text):
    """Click a button by text"""
    if context.use_playwright:
        if button_text.lower() == "register":
            context.page.click('[data-testid="register-button"]')
        else:
            context.page.click(f'button:has-text("{button_text}")')
    else:
        if button_text.lower() == "register":
            button = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="register-button"]')
        else:
            button = context.driver.find_element(By.XPATH, f"//button[contains(text(), '{button_text}')]")
        button.click()


@then('I should see a verification email message')
def step_see_verification_message(context):
    """Verify verification email message is displayed"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_text(/verification email sent/i)).to_be_visible()
    else:
        message = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'verification email')]"))
        )
        assert message.is_displayed()


@then('I should be redirected to the email verification page')
def step_redirected_to_verification(context):
    """Verify redirection to email verification page"""
    if context.use_playwright:
        playwright_expect(context.page).to_have_url(/.*verify-email.*/)
    else:
        WebDriverWait(context.driver, 10).until(
            lambda driver: "verify-email" in driver.current_url
        )


@when('I verify my email with a valid token')
def step_verify_email_token(context):
    """Simulate email verification with valid token"""
    if context.use_playwright:
        # Navigate to verification URL with mock token
        context.page.goto(f"{context.base_url}/verify-email?token=valid_token&email=<EMAIL>")
    else:
        context.driver.get(f"{context.base_url}/verify-email?token=valid_token&email=<EMAIL>")


@then('my account should be activated')
def step_account_activated(context):
    """Verify account is activated"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_text(/email verified successfully/i)).to_be_visible()
    else:
        message = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'verified successfully')]"))
        )
        assert message.is_displayed()


@then('I should be automatically logged in')
def step_automatically_logged_in(context):
    """Verify user is automatically logged in after verification"""
    if context.use_playwright:
        # Check for authentication tokens or user context
        access_token = context.page.evaluate('() => localStorage.getItem("access_token")')
        assert access_token is not None
    else:
        access_token = context.driver.execute_script('return localStorage.getItem("access_token")')
        assert access_token is not None


@then('I should be redirected to the profile setup page')
def step_redirected_to_profile_setup(context):
    """Verify redirection to profile setup page"""
    if context.use_playwright:
        playwright_expect(context.page).to_have_url(/.*profile.*setup.*/)
    else:
        WebDriverWait(context.driver, 10).until(
            lambda driver: "profile" in driver.current_url and "setup" in driver.current_url
        )


@when('I enter an invalid email "{email}"')
def step_enter_invalid_email(context, email):
    """Enter invalid email address"""
    if context.use_playwright:
        context.page.fill('[data-testid="email-input"]', email)
        context.page.blur('[data-testid="email-input"]')
    else:
        email_input = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="email-input"]')
        email_input.clear()
        email_input.send_keys(email)
        # Trigger blur event
        context.driver.execute_script("arguments[0].blur();", email_input)


@then('I should see "{error_message}" error')
def step_see_error_message(context, error_message):
    """Verify specific error message is displayed"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_text(error_message, exact=False)).to_be_visible()
    else:
        try:
            error_element = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{error_message}')]"))
            )
            assert error_element.is_displayed()
        except TimeoutException:
            assert False, f"Error message '{error_message}' not found"


@when('I enter a weak password "{password}"')
def step_enter_weak_password(context, password):
    """Enter a weak password"""
    if context.use_playwright:
        context.page.fill('[data-testid="password-input"]', password)
    else:
        password_input = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="password-input"]')
        password_input.clear()
        password_input.send_keys(password)


@then('I should see the password strength indicator')
def step_see_password_strength_indicator(context):
    """Verify password strength indicator is visible"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_test_id('password-strength-indicator')).to_be_visible()
    else:
        indicator = WebDriverWait(context.driver, 5).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="password-strength-indicator"]'))
        )
        assert indicator.is_displayed()


@then('the password strength should be "{strength_level}"')
def step_verify_password_strength(context, strength_level):
    """Verify password strength level"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_text(strength_level, exact=False)).to_be_visible()
    else:
        strength_text = WebDriverWait(context.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{strength_level}')]"))
        )
        assert strength_text.is_displayed()


@when('I enter a strong password "{password}"')
def step_enter_strong_password(context, password):
    """Enter a strong password"""
    if context.use_playwright:
        context.page.fill('[data-testid="password-input"]', password)
    else:
        password_input = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="password-input"]')
        password_input.clear()
        password_input.send_keys(password)


@then('I should see "{message}" message')
def step_see_message(context, message):
    """Verify specific message is displayed"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_text(message, exact=False)).to_be_visible()
    else:
        message_element = WebDriverWait(context.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{message}')]"))
        )
        assert message_element.is_displayed()


@when('I enter a different password confirmation "{password}"')
def step_enter_different_confirmation(context, password):
    """Enter different password confirmation"""
    if context.use_playwright:
        context.page.fill('[data-testid="confirm-password-input"]', password)
        context.page.blur('[data-testid="confirm-password-input"]')
    else:
        confirm_input = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="confirm-password-input"]')
        confirm_input.clear()
        confirm_input.send_keys(password)
        context.driver.execute_script("arguments[0].blur();", confirm_input)


@when('I enter an existing email "{email}"')
def step_enter_existing_email(context, email):
    """Enter an existing email address"""
    # Mock the email availability check
    if context.use_playwright:
        context.page.route('**/auth/check-email*', lambda route: route.fulfill(
            status=200,
            content_type='application/json',
            body=json.dumps({
                'available': False,
                'suggestions': ['<EMAIL>', '<EMAIL>']
            })
        ))
        context.page.fill('[data-testid="email-input"]', email)
        context.page.blur('[data-testid="email-input"]')
        # Wait for debounced API call
        context.page.wait_for_timeout(600)
    else:
        # For Selenium, we'd need to mock the API differently
        email_input = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="email-input"]')
        email_input.clear()
        email_input.send_keys(email)
        context.driver.execute_script("arguments[0].blur();", email_input)


@then('I should see email suggestions')
def step_see_email_suggestions(context):
    """Verify email suggestions are displayed"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_test_id('email-suggestions')).to_be_visible()
    else:
        suggestions = WebDriverWait(context.driver, 5).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="email-suggestions"]'))
        )
        assert suggestions.is_displayed()


@when('I click on the first email suggestion')
def step_click_first_suggestion(context):
    """Click on the first email suggestion"""
    if context.use_playwright:
        context.page.click('[data-testid="email-suggestion-0"]')
    else:
        suggestion = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="email-suggestion-0"]')
        suggestion.click()


@then('the email field should be updated with the suggestion')
def step_email_field_updated(context):
    """Verify email field is updated with suggestion"""
    if context.use_playwright:
        email_value = context.page.get_by_test_id('email-input').input_value()
        assert '<EMAIL>' in email_value
    else:
        email_input = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="email-input"]')
        assert '<EMAIL>' in email_input.get_attribute('value')


@then('the error message should disappear')
def step_error_message_disappears(context):
    """Verify error message disappears"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_test_id('email-suggestions')).not_to_be_visible()
    else:
        # Wait for suggestions to disappear
        WebDriverWait(context.driver, 5).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, '[data-testid="email-suggestions"]'))
        )


@when('I navigate using only the keyboard')
def step_navigate_keyboard_only(context):
    """Test keyboard navigation through form"""
    if context.use_playwright:
        # Start keyboard navigation
        context.page.keyboard.press('Tab')
        playwright_expect(context.page.get_by_test_id('email-input')).to_be_focused()
    else:
        # Selenium keyboard navigation
        body = context.driver.find_element(By.TAG_NAME, "body")
        body.send_keys(Keys.TAB)
        email_input = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="email-input"]')
        assert email_input == context.driver.switch_to.active_element


@then('I should be able to access all form fields in logical order')
def step_verify_tab_order(context):
    """Verify logical tab order through form fields"""
    expected_fields = [row['field'] for row in context.table]
    
    if context.use_playwright:
        for field in expected_fields:
            # Convert field name to test ID
            test_id = field.replace(' ', '-').lower()
            if 'input' in test_id:
                test_id = test_id.replace(' input', '-input')
            elif 'checkbox' in test_id:
                test_id = test_id.replace(' checkbox', '-checkbox')
            elif 'button' in test_id:
                test_id = test_id.replace(' button', '-button')
            
            context.page.keyboard.press('Tab')
            playwright_expect(context.page.get_by_test_id(test_id)).to_be_focused()
    else:
        # Selenium implementation for tab order verification
        for field in expected_fields:
            context.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.TAB)
            # Verify focus is on expected element
            active_element = context.driver.switch_to.active_element
            assert active_element is not None


@then('focus indicators should be clearly visible')
def step_verify_focus_indicators(context):
    """Verify focus indicators are visible"""
    if context.use_playwright:
        # Check that focused elements have visible focus styles
        focused_element = context.page.locator(':focus')
        # Verify focus ring or outline is present
        assert focused_element.count() > 0
    else:
        # Selenium focus indicator verification
        active_element = context.driver.switch_to.active_element
        assert active_element is not None


@given('I am using a mobile device')
def step_using_mobile_device(context):
    """Set mobile viewport"""
    if context.use_playwright:
        context.page.set_viewport_size({"width": 375, "height": 667})
    else:
        # Selenium mobile emulation
        context.driver.set_window_size(375, 667)


@then('the layout should be optimized for mobile')
def step_verify_mobile_layout(context):
    """Verify mobile-optimized layout"""
    if context.use_playwright:
        # Check for mobile-specific classes or responsive behavior
        playwright_expect(context.page.get_by_test_id('email-input')).to_be_visible()
        # Verify form is still usable on mobile
        viewport_size = context.page.viewport_size
        assert viewport_size['width'] == 375
    else:
        # Selenium mobile layout verification
        window_size = context.driver.get_window_size()
        assert window_size['width'] == 375
