"""
BEHAVE Step Definitions for Skills Assessment
Feature 1.1: Skills Vector Representation & Scoring - BEHAVE Tests Phase
"""

import json
import time
import requests
from behave import given, when, then, step
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Test configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_USER_ID = 1

# Skills assessment data storage
class SkillsAssessmentContext:
    def __init__(self):
        self.assessment_data = {
            "user_id": TEST_USER_ID,
            "skills": [],
            "certifications": []
        }
        self.response = None
        self.assessment_result = None
        self.skill_profile = None
        self.start_time = None
        self.errors = []

def get_assessment_context(context):
    """Get or create assessment context"""
    if not hasattr(context, 'skills_assessment'):
        context.skills_assessment = SkillsAssessmentContext()
    return context.skills_assessment

# Background steps
@given('the skills assessment system is available')
def step_system_available(context):
    """Verify the skills assessment system is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/skills/domains", timeout=5)
        assert response.status_code == 200, f"System not available: {response.status_code}"
        context.system_available = True
    except Exception as e:
        assert False, f"Skills assessment system not available: {e}"

@given('I am a registered user')
def step_registered_user(context):
    """Set up test user context"""
    context.user_id = TEST_USER_ID
    context.authenticated = True

@given('the 8-domain cybersecurity framework is loaded')
def step_framework_loaded(context):
    """Verify the cybersecurity framework is available"""
    try:
        response = requests.get(f"{API_BASE_URL}/skills/domains")
        assert response.status_code == 200
        domains_data = response.json()
        assert "domains" in domains_data
        assert len(domains_data["domains"]) == 8
        context.framework_loaded = True
    except Exception as e:
        assert False, f"Framework not loaded: {e}"

# Assessment initialization steps
@given('I start a new skills assessment')
def step_start_assessment(context):
    """Initialize a new skills assessment"""
    assessment_ctx = get_assessment_context(context)
    assessment_ctx.assessment_data = {
        "user_id": TEST_USER_ID,
        "skills": [],
        "certifications": []
    }
    assessment_ctx.start_time = time.time()

@given('I have no existing skill profile')
def step_no_existing_profile(context):
    """Ensure no existing skill profile for test user"""
    # In a real test, this might clear the test user's profile
    assessment_ctx = get_assessment_context(context)
    assessment_ctx.skill_profile = None

@given('I have the following certifications')
def step_add_certifications(context):
    """Add certifications to the assessment"""
    assessment_ctx = get_assessment_context(context)
    for row in context.table:
        cert = {
            "name": row["name"],
            "provider": row["provider"],
            "year_obtained": int(row["year_obtained"]) if row["year_obtained"] else None
        }
        assessment_ctx.assessment_data["certifications"].append(cert)

# Skill assessment steps
@when('I assess my "{skill_name}" skill as "{level}" with "{confidence}" confidence')
def step_assess_skill(context, skill_name, level, confidence):
    """Assess a single skill"""
    assessment_ctx = get_assessment_context(context)
    skill_item = {
        "skill_name": skill_name,
        "level": level,
        "confidence": confidence
    }
    assessment_ctx.assessment_data["skills"].append(skill_item)

@when('I assess skills in the following domains')
def step_assess_multiple_skills(context):
    """Assess multiple skills across domains"""
    assessment_ctx = get_assessment_context(context)
    for row in context.table:
        skill_item = {
            "skill_name": row["skill_name"],
            "level": row["level"],
            "confidence": row["confidence"]
        }
        assessment_ctx.assessment_data["skills"].append(skill_item)

@when('I assess only {count:d} skill as "{level}" with "{confidence}" confidence')
def step_assess_minimal_skills(context, count, level, confidence):
    """Assess minimal number of skills"""
    assessment_ctx = get_assessment_context(context)
    skill_item = {
        "skill_name": "network_protocols",  # Use a standard skill for testing
        "level": level,
        "confidence": confidence
    }
    assessment_ctx.assessment_data["skills"].append(skill_item)

@when('I assess all 72 skills in the framework')
def step_assess_all_skills(context):
    """Assess all skills in the framework"""
    assessment_ctx = get_assessment_context(context)
    
    # Get all skills from the framework
    response = requests.get(f"{API_BASE_URL}/skills/domains")
    domains_data = response.json()
    
    for domain_info in domains_data["domains"].values():
        for skill in domain_info["skills"]:
            skill_item = {
                "skill_name": skill,
                "level": "intermediate",  # Use consistent level for testing
                "confidence": "confident"
            }
            assessment_ctx.assessment_data["skills"].append(skill_item)

@when('I complete a skills assessment with {count:d} skills')
def step_complete_assessment_with_count(context, count):
    """Complete assessment with specific number of skills"""
    assessment_ctx = get_assessment_context(context)
    
    # Add the specified number of skills
    skill_names = ["network_protocols", "incident_response", "threat_modeling", 
                   "vulnerability_assessment", "secure_coding_practices"]
    
    for i in range(min(count, len(skill_names))):
        skill_item = {
            "skill_name": skill_names[i],
            "level": "intermediate",
            "confidence": "confident"
        }
        assessment_ctx.assessment_data["skills"].append(skill_item)

# Submission and API steps
@when('I submit the skills assessment')
def step_submit_assessment(context):
    """Submit the skills assessment to the API"""
    assessment_ctx = get_assessment_context(context)
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/skills/assess",
            json=assessment_ctx.assessment_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        assessment_ctx.response = response
        
        if response.status_code == 200:
            assessment_ctx.assessment_result = response.json()
        else:
            assessment_ctx.errors.append(f"API error: {response.status_code} - {response.text}")
            
    except Exception as e:
        assessment_ctx.errors.append(f"Request failed: {e}")

@when('I send a POST request to "{endpoint}" with valid assessment data')
def step_send_post_request(context, endpoint):
    """Send POST request to specific endpoint"""
    assessment_ctx = get_assessment_context(context)
    
    # Use sample valid data
    valid_data = {
        "user_id": TEST_USER_ID,
        "skills": [
            {
                "skill_name": "network_protocols",
                "level": "intermediate",
                "confidence": "confident"
            }
        ],
        "certifications": []
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL.rstrip('/api/v1')}{endpoint}",
            json=valid_data,
            headers={"Content-Type": "application/json"}
        )
        assessment_ctx.response = response
        if response.status_code == 200:
            assessment_ctx.assessment_result = response.json()
    except Exception as e:
        assessment_ctx.errors.append(f"Request failed: {e}")

@when('I send a GET request to "{endpoint}"')
def step_send_get_request(context, endpoint):
    """Send GET request to retrieve profile"""
    assessment_ctx = get_assessment_context(context)
    
    # Replace {user_id} placeholder
    endpoint = endpoint.replace("{user_id}", str(TEST_USER_ID))
    
    try:
        response = requests.get(f"{API_BASE_URL.rstrip('/api/v1')}{endpoint}")
        assessment_ctx.response = response
        if response.status_code == 200:
            assessment_ctx.skill_profile = response.json()
    except Exception as e:
        assessment_ctx.errors.append(f"Request failed: {e}")

# Validation and error steps
@when('I try to assess a skill with invalid level "{invalid_level}"')
def step_assess_invalid_level(context, invalid_level):
    """Try to assess skill with invalid level"""
    assessment_ctx = get_assessment_context(context)
    skill_item = {
        "skill_name": "network_protocols",
        "level": invalid_level,
        "confidence": "confident"
    }
    assessment_ctx.assessment_data["skills"].append(skill_item)

@when('I try to assess a skill with invalid confidence "{invalid_confidence}"')
def step_assess_invalid_confidence(context, invalid_confidence):
    """Try to assess skill with invalid confidence"""
    assessment_ctx = get_assessment_context(context)
    skill_item = {
        "skill_name": "network_protocols",
        "level": "intermediate",
        "confidence": invalid_confidence
    }
    assessment_ctx.assessment_data["skills"].append(skill_item)

# Timing and performance steps
@when('multiple users are taking skills assessments simultaneously')
def step_multiple_users_setup(context):
    """Set up for concurrent user testing"""
    context.concurrent_users = 10
    context.concurrent_results = []

@when('{count:d} users submit assessments at the same time')
def step_concurrent_submissions(context, count):
    """Simulate concurrent assessment submissions"""
    import threading
    import queue
    
    results_queue = queue.Queue()
    
    def submit_assessment(user_id):
        assessment_data = {
            "user_id": user_id,
            "skills": [
                {
                    "skill_name": "network_protocols",
                    "level": "intermediate",
                    "confidence": "confident"
                }
            ],
            "certifications": []
        }
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/skills/assess",
                json=assessment_data,
                timeout=10
            )
            results_queue.put({
                "user_id": user_id,
                "status_code": response.status_code,
                "response": response.json() if response.status_code == 200 else None
            })
        except Exception as e:
            results_queue.put({
                "user_id": user_id,
                "error": str(e)
            })
    
    # Start concurrent threads
    threads = []
    for i in range(count):
        thread = threading.Thread(target=submit_assessment, args=(TEST_USER_ID + i,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Collect results
    context.concurrent_results = []
    while not results_queue.empty():
        context.concurrent_results.append(results_queue.get())

# System state steps
@when('the assessment API is temporarily unavailable')
def step_api_unavailable(context):
    """Simulate API unavailability"""
    # In a real test, this might stop the API service temporarily
    context.api_available = False

@when('the API becomes available again')
def step_api_available_again(context):
    """Restore API availability"""
    context.api_available = True

@when('the system restarts')
def step_system_restart(context):
    """Simulate system restart"""
    # In a real test, this might restart services
    time.sleep(1)  # Simulate restart time

# Time-based steps
@when('I complete another assessment {duration} later')
def step_complete_later_assessment(context, duration):
    """Complete assessment after time period"""
    assessment_ctx = get_assessment_context(context)
    
    # Simulate time passage (in real tests, this might involve actual waiting or date manipulation)
    assessment_ctx.assessment_data["skills"] = [
        {
            "skill_name": "network_protocols",
            "level": "advanced",  # Improved skill level
            "confidence": "very_confident"
        }
    ]

# Assertion steps
@then('the assessment should be processed successfully')
def step_assessment_processed_successfully(context):
    """Verify assessment was processed successfully"""
    assessment_ctx = get_assessment_context(context)
    
    assert assessment_ctx.response is not None, "No response received"
    assert assessment_ctx.response.status_code == 200, f"Expected 200, got {assessment_ctx.response.status_code}"
    assert assessment_ctx.assessment_result is not None, "No assessment result received"
    assert "assessment_id" in assessment_ctx.assessment_result, "Missing assessment_id in response"

@then('I should receive a skills vector with {count:d} assessed skills')
def step_verify_skills_vector(context, count):
    """Verify the skills vector contains expected number of skills"""
    assessment_ctx = get_assessment_context(context)
    
    assert assessment_ctx.assessment_result is not None
    assert "skill_scores" in assessment_ctx.assessment_result
    skill_scores = assessment_ctx.assessment_result["skill_scores"]
    assert len(skill_scores) == count, f"Expected {count} skills, got {len(skill_scores)}"

@then('the domain scores should be calculated correctly')
def step_verify_domain_scores(context):
    """Verify domain scores are calculated"""
    assessment_ctx = get_assessment_context(context)
    
    assert assessment_ctx.assessment_result is not None
    assert "domain_scores" in assessment_ctx.assessment_result
    domain_scores = assessment_ctx.assessment_result["domain_scores"]
    assert len(domain_scores) == 8, f"Expected 8 domain scores, got {len(domain_scores)}"

@then('my strongest domain should be identified')
def step_verify_strongest_domain(context):
    """Verify strongest domain is identified"""
    assessment_ctx = get_assessment_context(context)
    
    assert assessment_ctx.assessment_result is not None
    assert "overall_profile" in assessment_ctx.assessment_result
    overall_profile = assessment_ctx.assessment_result["overall_profile"]
    assert "strongest_domain" in overall_profile
    assert overall_profile["strongest_domain"] is not None

@then('the skill score should include certification boost')
def step_verify_certification_boost(context):
    """Verify certification boost is applied"""
    assessment_ctx = get_assessment_context(context)
    
    assert assessment_ctx.assessment_result is not None
    skill_scores = assessment_ctx.assessment_result["skill_scores"]
    assert len(skill_scores) > 0
    
    # Check that at least one skill has certification boost
    has_boost = any(skill["certification_boost"] > 0 for skill in skill_scores)
    assert has_boost, "No certification boost found in skill scores"

@then('the final score should be higher than the confidence-weighted score')
def step_verify_final_score_higher(context):
    """Verify final score includes boost"""
    assessment_ctx = get_assessment_context(context)
    
    skill_scores = assessment_ctx.assessment_result["skill_scores"]
    for skill in skill_scores:
        if skill["certification_boost"] > 0:
            assert skill["final_score"] > skill["confidence_weighted_score"], \
                f"Final score {skill['final_score']} should be higher than weighted score {skill['confidence_weighted_score']}"

@then('the certification boost should not exceed 30%')
def step_verify_boost_cap(context):
    """Verify certification boost is capped"""
    assessment_ctx = get_assessment_context(context)
    
    skill_scores = assessment_ctx.assessment_result["skill_scores"]
    for skill in skill_scores:
        assert skill["certification_boost"] <= 0.3, \
            f"Certification boost {skill['certification_boost']} exceeds 30% cap"

@then('I should receive a validation error')
def step_verify_validation_error(context):
    """Verify validation error is received"""
    assessment_ctx = get_assessment_context(context)
    
    # When we submit invalid data, we should get an error
    assert assessment_ctx.response is not None
    assert assessment_ctx.response.status_code in [400, 422], \
        f"Expected validation error (400/422), got {assessment_ctx.response.status_code}"

@then('the error should indicate valid skill levels')
def step_verify_skill_level_error(context):
    """Verify error message mentions valid skill levels"""
    assessment_ctx = get_assessment_context(context)
    
    if assessment_ctx.response and assessment_ctx.response.status_code in [400, 422]:
        error_text = assessment_ctx.response.text.lower()
        assert any(level in error_text for level in ["none", "basic", "intermediate", "advanced", "expert"]), \
            "Error message should mention valid skill levels"

@then('the error should indicate valid confidence levels')
def step_verify_confidence_level_error(context):
    """Verify error message mentions valid confidence levels"""
    assessment_ctx = get_assessment_context(context)

    if assessment_ctx.response and assessment_ctx.response.status_code in [400, 422]:
        error_text = assessment_ctx.response.text.lower()
        assert any(conf in error_text for conf in ["very_confident", "confident", "somewhat_confident", "not_confident"]), \
            "Error message should mention valid confidence levels"

# Additional assertion steps for comprehensive testing

@then('domain scores should be calculated for all 8 domains')
def step_verify_all_domain_scores(context):
    """Verify all 8 domains have calculated scores"""
    assessment_ctx = get_assessment_context(context)

    domain_scores = assessment_ctx.assessment_result["domain_scores"]
    expected_domains = [
        "communication_network_security",
        "identity_access_management",
        "security_architecture_engineering",
        "asset_security",
        "security_risk_management",
        "security_assessment_testing",
        "software_security",
        "security_operations"
    ]

    for domain in expected_domains:
        assert domain in domain_scores, f"Missing domain score for {domain}"

@then('assessed domains should have scores greater than 0')
def step_verify_assessed_domain_scores(context):
    """Verify assessed domains have positive scores"""
    assessment_ctx = get_assessment_context(context)

    domain_scores = assessment_ctx.assessment_result["domain_scores"]

    # At least one domain should have a score > 0
    has_positive_score = any(score > 0 for score in domain_scores.values())
    assert has_positive_score, "No domains have positive scores"

@then('unassessed domains should have scores of 0')
def step_verify_unassessed_domain_scores(context):
    """Verify unassessed domains have zero scores"""
    assessment_ctx = get_assessment_context(context)

    domain_scores = assessment_ctx.assessment_result["domain_scores"]

    # Check that some domains have zero scores (unless all skills were assessed)
    total_skills_assessed = len(assessment_ctx.assessment_data["skills"])
    if total_skills_assessed < 72:  # Not all skills assessed
        has_zero_score = any(score == 0 for score in domain_scores.values())
        assert has_zero_score, "Expected some domains to have zero scores"

@then('the overall assessment completeness should be calculated')
def step_verify_assessment_completeness(context):
    """Verify assessment completeness is calculated"""
    assessment_ctx = get_assessment_context(context)

    overall_profile = assessment_ctx.assessment_result["overall_profile"]
    assert "assessment_completeness" in overall_profile
    completeness = overall_profile["assessment_completeness"]
    assert 0 <= completeness <= 1, f"Completeness {completeness} should be between 0 and 1"

@then('a new skill profile should be created for me')
def step_verify_profile_created(context):
    """Verify new skill profile is created"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would check the database or make an API call
    assert assessment_ctx.assessment_result is not None
    assert "assessment_id" in assessment_ctx.assessment_result

@then('the profile should contain my skill vector')
def step_verify_profile_skill_vector(context):
    """Verify profile contains skill vector"""
    assessment_ctx = get_assessment_context(context)

    assert "skill_scores" in assessment_ctx.assessment_result
    skill_scores = assessment_ctx.assessment_result["skill_scores"]
    assert len(skill_scores) > 0, "Skill vector should not be empty"

@then('the profile should contain domain scores')
def step_verify_profile_domain_scores(context):
    """Verify profile contains domain scores"""
    assessment_ctx = get_assessment_context(context)

    assert "domain_scores" in assessment_ctx.assessment_result
    domain_scores = assessment_ctx.assessment_result["domain_scores"]
    assert len(domain_scores) == 8, "Should have 8 domain scores"

@then('the profile should track the assessment timestamp')
def step_verify_profile_timestamp(context):
    """Verify profile tracks timestamp"""
    assessment_ctx = get_assessment_context(context)

    assert "timestamp" in assessment_ctx.assessment_result
    timestamp = assessment_ctx.assessment_result["timestamp"]
    assert timestamp is not None, "Timestamp should not be None"

@then('my skill profile should be updated')
def step_verify_profile_updated(context):
    """Verify skill profile is updated"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would compare with previous profile
    assert assessment_ctx.assessment_result is not None

@then('the system should track skill growth over time')
def step_verify_skill_growth_tracking(context):
    """Verify system tracks skill growth"""
    assessment_ctx = get_assessment_context(context)

    # In a real implementation, this would check growth metrics
    assert assessment_ctx.assessment_result is not None

@then('the assessment completeness should be very low')
def step_verify_low_completeness(context):
    """Verify assessment completeness is low"""
    assessment_ctx = get_assessment_context(context)

    overall_profile = assessment_ctx.assessment_result["overall_profile"]
    completeness = overall_profile["assessment_completeness"]
    assert completeness < 0.1, f"Completeness {completeness} should be very low (< 0.1)"

@then('I should receive recommendations to assess more skills')
def step_verify_more_skills_recommendation(context):
    """Verify recommendation to assess more skills"""
    assessment_ctx = get_assessment_context(context)

    overall_profile = assessment_ctx.assessment_result["overall_profile"]
    total_assessed = overall_profile["total_skills_assessed"]
    assert total_assessed < 10, "Should recommend assessing more skills when count is low"

@then('the assessment completeness should be 100%')
def step_verify_full_completeness(context):
    """Verify assessment is 100% complete"""
    assessment_ctx = get_assessment_context(context)

    overall_profile = assessment_ctx.assessment_result["overall_profile"]
    completeness = overall_profile["assessment_completeness"]
    assert completeness >= 0.99, f"Completeness {completeness} should be nearly 100%"

@then('all 8 domains should have calculated scores')
def step_verify_all_domains_scored(context):
    """Verify all domains have calculated scores"""
    assessment_ctx = get_assessment_context(context)

    domain_scores = assessment_ctx.assessment_result["domain_scores"]
    assert len(domain_scores) == 8

    # All domains should have positive scores when all skills are assessed
    positive_scores = sum(1 for score in domain_scores.values() if score > 0)
    assert positive_scores == 8, f"Expected 8 domains with positive scores, got {positive_scores}"

@then('the processing should complete within acceptable time limits')
def step_verify_processing_time(context):
    """Verify processing completes within time limits"""
    assessment_ctx = get_assessment_context(context)

    if assessment_ctx.start_time:
        processing_time = time.time() - assessment_ctx.start_time
        assert processing_time < 10, f"Processing took {processing_time}s, should be under 10s"

@then('the confidence-weighted score should be "{expected_score}"')
def step_verify_confidence_weighted_score(context, expected_score):
    """Verify confidence-weighted score matches expected value"""
    assessment_ctx = get_assessment_context(context)

    skill_scores = assessment_ctx.assessment_result["skill_scores"]
    assert len(skill_scores) > 0

    # Check the first skill's confidence-weighted score
    actual_score = skill_scores[0]["confidence_weighted_score"]
    expected = float(expected_score)
    assert abs(actual_score - expected) < 0.01, \
        f"Expected confidence-weighted score {expected}, got {actual_score}"

@then('the final score should reflect the confidence weighting')
def step_verify_final_score_reflects_confidence(context):
    """Verify final score reflects confidence weighting"""
    assessment_ctx = get_assessment_context(context)

    skill_scores = assessment_ctx.assessment_result["skill_scores"]
    for skill in skill_scores:
        # Final score should be at least the confidence-weighted score
        assert skill["final_score"] >= skill["confidence_weighted_score"], \
            f"Final score {skill['final_score']} should be >= confidence-weighted score {skill['confidence_weighted_score']}"

@then('I should receive a {status_code:d} status code')
def step_verify_status_code(context, status_code):
    """Verify HTTP status code"""
    assessment_ctx = get_assessment_context(context)

    assert assessment_ctx.response is not None
    assert assessment_ctx.response.status_code == status_code, \
        f"Expected {status_code}, got {assessment_ctx.response.status_code}"

@then('the response should contain an assessment_id')
def step_verify_assessment_id(context):
    """Verify response contains assessment_id"""
    assessment_ctx = get_assessment_context(context)

    assert assessment_ctx.assessment_result is not None
    assert "assessment_id" in assessment_ctx.assessment_result
    assert assessment_ctx.assessment_result["assessment_id"] is not None

@then('the response should contain skill_scores array')
def step_verify_skill_scores_array(context):
    """Verify response contains skill_scores array"""
    assessment_ctx = get_assessment_context(context)

    assert "skill_scores" in assessment_ctx.assessment_result
    assert isinstance(assessment_ctx.assessment_result["skill_scores"], list)

@then('the response should contain domain_scores object')
def step_verify_domain_scores_object(context):
    """Verify response contains domain_scores object"""
    assessment_ctx = get_assessment_context(context)

    assert "domain_scores" in assessment_ctx.assessment_result
    assert isinstance(assessment_ctx.assessment_result["domain_scores"], dict)

@then('the response should contain overall_profile data')
def step_verify_overall_profile_data(context):
    """Verify response contains overall_profile data"""
    assessment_ctx = get_assessment_context(context)

    assert "overall_profile" in assessment_ctx.assessment_result
    assert isinstance(assessment_ctx.assessment_result["overall_profile"], dict)

@then('I should receive my current skill profile')
def step_verify_current_skill_profile(context):
    """Verify current skill profile is received"""
    assessment_ctx = get_assessment_context(context)

    assert assessment_ctx.skill_profile is not None
    assert "user_id" in assessment_ctx.skill_profile
    assert "skill_vector" in assessment_ctx.skill_profile

@then('the profile should match the latest assessment')
def step_verify_profile_matches_assessment(context):
    """Verify profile matches latest assessment"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would compare profile data with assessment data
    assert assessment_ctx.skill_profile is not None
    assert assessment_ctx.assessment_result is not None

# Performance and timing assertion steps

@then('the assessment should be processed within {seconds:d} seconds')
def step_verify_processing_time_limit(context, seconds):
    """Verify assessment processing time"""
    assessment_ctx = get_assessment_context(context)

    if assessment_ctx.start_time:
        processing_time = time.time() - assessment_ctx.start_time
        assert processing_time <= seconds, \
            f"Processing took {processing_time:.2f}s, should be <= {seconds}s"

@then('the API response time should be under {seconds:d} second')
def step_verify_api_response_time(context, seconds):
    """Verify API response time"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would measure actual API response time
    # For now, we'll assume if we got a response within the time limit, it was fast enough
    assert assessment_ctx.response is not None
    assert assessment_ctx.response.status_code == 200

    # Check that we have a response (implying it was within time limit)
    if assessment_ctx.start_time:
        response_time = time.time() - assessment_ctx.start_time
        assert response_time <= seconds, f"API response took {response_time:.2f}s, should be <= {seconds}s"

@then('the database operations should complete efficiently')
def step_verify_database_efficiency(context):
    """Verify database operations are efficient"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would check database query performance
    assert assessment_ctx.assessment_result is not None

# Error handling assertion steps

@then('I should receive an appropriate error message')
def step_verify_appropriate_error_message(context):
    """Verify appropriate error message is received"""
    assessment_ctx = get_assessment_context(context)

    # When API is unavailable, we should get an error
    assert len(assessment_ctx.errors) > 0 or \
           (assessment_ctx.response and assessment_ctx.response.status_code >= 400)

@then('the system should suggest retrying later')
def step_verify_retry_suggestion(context):
    """Verify system suggests retrying"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would check for retry-related error messages
    has_error = len(assessment_ctx.errors) > 0 or \
                (assessment_ctx.response and assessment_ctx.response.status_code >= 400)
    assert has_error, "Should have error when API is unavailable"

@when('I retry the submission')
def step_retry_submission(context):
    """Retry the assessment submission"""
    # Reset API availability
    context.api_available = True

    # Resubmit the assessment
    step_submit_assessment(context)

# Data persistence assertion steps

@then('my assessment data should still be available')
def step_verify_data_persistence(context):
    """Verify assessment data persists after restart"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would check database persistence
    assert assessment_ctx.assessment_result is not None

@then('my skill profile should be intact')
def step_verify_profile_integrity(context):
    """Verify skill profile integrity after restart"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would verify profile data integrity
    assert assessment_ctx.assessment_result is not None

@then('I should be able to retrieve my assessment history')
def step_verify_assessment_history(context):
    """Verify assessment history is retrievable"""
    assessment_ctx = get_assessment_context(context)

    # In a real test, this would call an assessment history API
    assert assessment_ctx.assessment_result is not None

# Concurrent users assertion steps

@then('all assessments should be processed correctly')
def step_verify_all_assessments_processed(context):
    """Verify all concurrent assessments were processed"""
    successful_results = [r for r in context.concurrent_results
                         if 'status_code' in r and r['status_code'] == 200]

    assert len(successful_results) == len(context.concurrent_results), \
        f"Only {len(successful_results)} out of {len(context.concurrent_results)} assessments succeeded"

@then('each user should receive their own unique assessment_id')
def step_verify_unique_assessment_ids(context):
    """Verify each user gets unique assessment ID"""
    assessment_ids = []

    for result in context.concurrent_results:
        if 'response' in result and result['response']:
            assessment_id = result['response'].get('assessment_id')
            if assessment_id:
                assessment_ids.append(assessment_id)

    # All assessment IDs should be unique
    assert len(assessment_ids) == len(set(assessment_ids)), \
        "Assessment IDs are not unique across concurrent users"

@then('there should be no data corruption or mixing between users')
def step_verify_no_data_corruption(context):
    """Verify no data corruption between concurrent users"""
    # Check that each result has the correct user_id
    for result in context.concurrent_results:
        if 'response' in result and result['response']:
            response_user_id = result['response'].get('user_id')
            expected_user_id = result.get('user_id')
            if response_user_id and expected_user_id:
                assert response_user_id == expected_user_id, \
                    f"User ID mismatch: expected {expected_user_id}, got {response_user_id}"

@then('the system should maintain acceptable performance')
def step_verify_acceptable_performance(context):
    """Verify system maintains acceptable performance under load"""
    # Check that all requests completed (no timeouts)
    timeout_errors = [r for r in context.concurrent_results if 'error' in r and 'timeout' in r['error'].lower()]

    assert len(timeout_errors) == 0, \
        f"{len(timeout_errors)} requests timed out during concurrent testing"

# Additional utility steps for comprehensive testing

@given('I start a new skills assessment with {count:d} skills')
def step_start_assessment_with_skills(context, count):
    """Start assessment and prepare specific number of skills"""
    step_start_assessment(context)

    # Pre-populate with skills for performance testing
    assessment_ctx = get_assessment_context(context)
    skill_names = [
        "network_protocols", "firewall_configuration", "vpn_technologies",
        "incident_response", "threat_modeling", "vulnerability_assessment",
        "secure_coding_practices", "authentication_mechanisms", "authorization_rbac",
        "data_classification", "risk_assessment", "compliance_management",
        "penetration_testing", "security_auditing", "malware_analysis",
        "digital_forensics", "threat_intelligence", "security_monitoring",
        "endpoint_security", "cloud_security"
    ]

    for i in range(min(count, len(skill_names))):
        skill_item = {
            "skill_name": skill_names[i],
            "level": "intermediate",
            "confidence": "confident"
        }
        assessment_ctx.assessment_data["skills"].append(skill_item)

@step('I try to submit the skills assessment')
def step_try_submit_assessment(context):
    """Try to submit assessment (may fail)"""
    try:
        step_submit_assessment(context)
    except Exception as e:
        assessment_ctx = get_assessment_context(context)
        assessment_ctx.errors.append(f"Submission failed: {e}")

@step('the skills assessment API is available')
def step_api_available(context):
    """Verify API is available"""
    try:
        response = requests.get(f"{API_BASE_URL}/skills/domains", timeout=5)
        assert response.status_code == 200
        context.api_available = True
    except Exception:
        context.api_available = False
        assert False, "Skills assessment API is not available"
