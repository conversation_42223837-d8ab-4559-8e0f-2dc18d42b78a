"""
Enhanced authentication steps that work with both Selenium and Playwright
This provides a unified interface for testing with either browser automation tool
"""

from behave import given, when, then
from playwright.sync_api import expect as playwright_expect
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException
import time


def get_element_by_test_id(context, test_id):
    """Get element by test ID using either Playwright or Selenium"""
    if context.use_playwright:
        return context.page.get_by_test_id(test_id)
    else:
        return context.driver.find_element(By.CSS_SELECTOR, f"[data-testid='{test_id}']")


def click_element(context, element_or_selector):
    """Click element using either Playwright or Selenium"""
    if context.use_playwright:
        if isinstance(element_or_selector, str):
            context.page.locator(element_or_selector).click()
        else:
            element_or_selector.click()
    else:
        if isinstance(element_or_selector, str):
            element = context.driver.find_element(By.CSS_SELECTOR, element_or_selector)
            element.click()
        else:
            element_or_selector.click()


def fill_input(context, element_or_selector, text):
    """Fill input using either Playwright or Selenium"""
    if context.use_playwright:
        if isinstance(element_or_selector, str):
            context.page.locator(element_or_selector).fill(text)
        else:
            element_or_selector.fill(text)
    else:
        if isinstance(element_or_selector, str):
            element = context.driver.find_element(By.CSS_SELECTOR, element_or_selector)
        else:
            element = element_or_selector
        element.clear()
        element.send_keys(text)


def wait_for_element(context, selector, timeout=10):
    """Wait for element to be visible using either Playwright or Selenium"""
    if context.use_playwright:
        context.page.locator(selector).wait_for(state='visible', timeout=timeout * 1000)
    else:
        WebDriverWait(context.driver, timeout).until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, selector))
        )


def assert_element_visible(context, selector_or_text):
    """Assert element is visible using either Playwright or Selenium"""
    if context.use_playwright:
        if selector_or_text.startswith('[') or selector_or_text.startswith('.') or selector_or_text.startswith('#'):
            playwright_expect(context.page.locator(selector_or_text)).to_be_visible()
        else:
            playwright_expect(context.page.get_by_text(selector_or_text)).to_be_visible()
    else:
        try:
            if selector_or_text.startswith('[') or selector_or_text.startswith('.') or selector_or_text.startswith('#'):
                element = WebDriverWait(context.driver, 5).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, selector_or_text))
                )
            else:
                element = WebDriverWait(context.driver, 5).until(
                    EC.visibility_of_element_located((By.XPATH, f"//*[contains(text(), '{selector_or_text}')]"))
                )
            assert element.is_displayed()
        except TimeoutException:
            assert False, f"Element '{selector_or_text}' not found or not visible"


@given('I am on the CertRats platform with enhanced testing')
def step_on_certrats_platform_enhanced(context):
    """Navigate to the CertRats platform using enhanced testing"""
    if context.use_playwright:
        context.page.goto(context.base_url)
        playwright_expect(context.page.locator('body')).to_be_visible()
    else:
        context.driver.get(context.base_url)
        WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )


@given('I navigate to the login page with enhanced testing')
def step_navigate_to_login_enhanced(context):
    """Navigate to the login page using enhanced testing"""
    if context.use_playwright:
        context.page.goto(f"{context.base_url}/login")
        playwright_expect(context.page.get_by_test_id('email-input')).to_be_visible()
    else:
        context.driver.get(f"{context.base_url}/login")
        WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='email-input']"))
        )


@when('I enter email "{email}" with enhanced testing')
def step_enter_email_enhanced(context, email):
    """Enter email using enhanced testing"""
    if context.use_playwright:
        context.page.get_by_test_id('email-input').fill(email)
    else:
        email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
        email_input.clear()
        email_input.send_keys(email)
    context.entered_email = email


@when('I enter password "{password}" with enhanced testing')
def step_enter_password_enhanced(context, password):
    """Enter password using enhanced testing"""
    if context.use_playwright:
        context.page.get_by_test_id('password-input').fill(password)
    else:
        password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
        password_input.clear()
        password_input.send_keys(password)
    context.entered_password = password


@when('I click the "{button_text}" button with enhanced testing')
def step_click_button_enhanced(context, button_text):
    """Click button using enhanced testing"""
    if context.use_playwright:
        if button_text.lower() == "sign in":
            context.page.get_by_test_id('login-button').click()
        else:
            context.page.get_by_role('button', name=button_text).click()
    else:
        if button_text.lower() == "sign in":
            button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
        else:
            button = context.driver.find_element(By.XPATH, f"//button[contains(text(), '{button_text}')]")
        button.click()
    time.sleep(0.5)  # Brief pause for UI updates


@when('I test keyboard navigation with enhanced testing')
def step_test_keyboard_navigation_enhanced(context):
    """Test keyboard navigation using enhanced testing"""
    if context.use_playwright:
        # Tab through form elements
        context.page.keyboard.press('Tab')
        playwright_expect(context.page.get_by_test_id('email-input')).to_be_focused()
        
        context.page.keyboard.press('Tab')
        playwright_expect(context.page.get_by_test_id('password-input')).to_be_focused()
        
        context.page.keyboard.press('Tab')
        playwright_expect(context.page.get_by_test_id('remember-me-checkbox')).to_be_focused()
        
        context.page.keyboard.press('Tab')
        playwright_expect(context.page.get_by_test_id('login-button')).to_be_focused()
    else:
        # Selenium keyboard navigation
        body = context.driver.find_element(By.TAG_NAME, "body")
        body.send_keys(Keys.TAB)
        
        # Check if email input is focused
        email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
        assert email_input == context.driver.switch_to.active_element
        
        body.send_keys(Keys.TAB)
        password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
        assert password_input == context.driver.switch_to.active_element


@when('I test accessibility with enhanced testing')
def step_test_accessibility_enhanced(context):
    """Test accessibility using enhanced testing"""
    if context.use_playwright:
        # Check for proper ARIA labels
        email_input = context.page.get_by_test_id('email-input')
        playwright_expect(email_input).to_have_attribute('aria-label')
        
        password_input = context.page.get_by_test_id('password-input')
        playwright_expect(password_input).to_have_attribute('aria-label')
        
        # Check for proper form structure
        playwright_expect(context.page.locator('form')).to_be_visible()
        
        # Run axe accessibility check if available
        if hasattr(context.page, 'accessibility'):
            violations = context.page.evaluate("""
                () => {
                    if (window.axe) {
                        return window.axe.run();
                    }
                    return { violations: [] };
                }
            """)
            assert len(violations.get('violations', [])) == 0, f"Accessibility violations found: {violations}"
    else:
        # Selenium accessibility checks
        email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
        assert email_input.get_attribute('aria-label') or email_input.get_attribute('id')
        
        password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
        assert password_input.get_attribute('aria-label') or password_input.get_attribute('id')
        
        # Check form structure
        form = context.driver.find_element(By.TAG_NAME, 'form')
        assert form.is_displayed()


@when('I test performance with enhanced testing')
def step_test_performance_enhanced(context):
    """Test performance using enhanced testing"""
    if context.use_playwright:
        # Navigate to page and measure performance
        start_time = time.time()
        context.page.goto(f"{context.base_url}/login")
        context.page.wait_for_load_state('networkidle')
        load_time = time.time() - start_time
        
        # Assert performance criteria
        assert load_time < 3.0, f"Page load time {load_time}s exceeds 3.0s threshold"
        
        # Get detailed performance metrics
        metrics = context.playwright_config.get_performance_metrics()
        context.performance_metrics = metrics
        
        # Assert Core Web Vitals
        if metrics.get('firstContentfulPaint'):
            assert metrics['firstContentfulPaint'] < 2500, f"FCP {metrics['firstContentfulPaint']}ms exceeds 2.5s"
    else:
        # Selenium performance testing (limited)
        start_time = time.time()
        context.driver.get(f"{context.base_url}/login")
        WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='email-input']"))
        )
        load_time = time.time() - start_time
        
        assert load_time < 5.0, f"Page load time {load_time}s exceeds 5.0s threshold"


@when('I simulate network failure with enhanced testing')
def step_simulate_network_failure_enhanced(context):
    """Simulate network failure using enhanced testing"""
    if context.use_playwright:
        # Block network requests to login endpoint
        context.page.route('**/api/auth/login', lambda route: route.abort())
        
        context.page.get_by_test_id('email-input').fill('<EMAIL>')
        context.page.get_by_test_id('password-input').fill('password123')
        context.page.get_by_test_id('login-button').click()
    else:
        # Selenium can't easily simulate network failures
        # So we'll just test the error handling UI
        context.driver.execute_script("""
            // Mock fetch to simulate network failure
            window.originalFetch = window.fetch;
            window.fetch = function() {
                return Promise.reject(new Error('Network error'));
            };
        """)
        
        email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
        password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
        login_button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
        
        email_input.send_keys('<EMAIL>')
        password_input.send_keys('password123')
        login_button.click()


@then('I should be redirected to the dashboard with enhanced testing')
def step_redirected_to_dashboard_enhanced(context):
    """Verify redirection to dashboard using enhanced testing"""
    if context.use_playwright:
        playwright_expect(context.page).to_have_url('**/dashboard')
    else:
        WebDriverWait(context.driver, 10).until(
            lambda driver: "/dashboard" in driver.current_url
        )
        assert "/dashboard" in context.driver.current_url


@then('I should see an error message "{error_message}" with enhanced testing')
def step_see_error_message_enhanced(context, error_message):
    """Verify error message using enhanced testing"""
    if context.use_playwright:
        playwright_expect(context.page.get_by_text(error_message)).to_be_visible()
    else:
        try:
            error_element = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{error_message}')]"))
            )
            assert error_element.is_displayed()
        except TimeoutException:
            assert False, f"Error message '{error_message}' not found"


@then('the page should meet accessibility standards with enhanced testing')
def step_accessibility_standards_enhanced(context):
    """Verify accessibility standards using enhanced testing"""
    if context.use_playwright:
        # Check for proper labels
        playwright_expect(context.page.get_by_label('Email address')).to_be_visible()
        playwright_expect(context.page.get_by_label('Password')).to_be_visible()
        
        # Check for proper form structure
        playwright_expect(context.page.locator('form')).to_be_visible()
        
        # Check for proper button roles
        playwright_expect(context.page.get_by_role('button', name='Sign in')).to_be_visible()
    else:
        # Selenium accessibility checks
        email_label = context.driver.find_element(By.XPATH, "//label[contains(text(), 'Email')]")
        password_label = context.driver.find_element(By.XPATH, "//label[contains(text(), 'Password')]")
        
        assert email_label.is_displayed()
        assert password_label.is_displayed()
        
        form = context.driver.find_element(By.TAG_NAME, 'form')
        assert form.is_displayed()


@then('the page should load within performance thresholds with enhanced testing')
def step_performance_thresholds_enhanced(context):
    """Verify performance thresholds using enhanced testing"""
    if context.use_playwright and hasattr(context, 'performance_metrics'):
        metrics = context.performance_metrics
        
        # Check load time
        if metrics.get('loadTime'):
            assert metrics['loadTime'] < 3000, f"Load time {metrics['loadTime']}ms exceeds 3s"
        
        # Check First Contentful Paint
        if metrics.get('firstContentfulPaint'):
            assert metrics['firstContentfulPaint'] < 2500, f"FCP {metrics['firstContentfulPaint']}ms exceeds 2.5s"
        
        # Check memory usage
        if metrics.get('memoryUsage'):
            assert metrics['memoryUsage'] < 50 * 1024 * 1024, f"Memory usage {metrics['memoryUsage']} bytes exceeds 50MB"
    else:
        # For Selenium, we just verify the page loaded successfully
        assert_element_visible(context, "[data-testid='email-input']")
        assert_element_visible(context, "[data-testid='password-input']")
        assert_element_visible(context, "[data-testid='login-button']")
