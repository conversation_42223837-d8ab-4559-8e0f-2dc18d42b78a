from behave import given, when, then
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException
import time


@given('I am on the CertRats platform')
def step_on_certrats_platform(context):
    """Navigate to the CertRats platform"""
    context.driver.get(context.base_url)
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.TAG_NAME, "body"))
    )


@given('I navigate to the login page')
def step_navigate_to_login(context):
    """Navigate to the login page"""
    context.driver.get(f"{context.base_url}/login")
    WebDriverWait(context.driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='email-input']"))
    )


@given('I am on the login page')
def step_on_login_page(context):
    """Ensure we are on the login page"""
    current_url = context.driver.current_url
    assert "/login" in current_url, f"Expected to be on login page, but was on {current_url}"
    
    # Verify login form elements are present
    email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
    password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
    login_button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
    
    assert email_input.is_displayed(), "Email input should be visible"
    assert password_input.is_displayed(), "Password input should be visible"
    assert login_button.is_displayed(), "Login button should be visible"


@when('I enter valid email "{email}"')
def step_enter_valid_email(context, email):
    """Enter a valid email address"""
    email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
    email_input.clear()
    email_input.send_keys(email)
    context.entered_email = email


@when('I enter email "{email}"')
def step_enter_email(context, email):
    """Enter any email address"""
    email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
    email_input.clear()
    email_input.send_keys(email)
    context.entered_email = email


@when('I enter valid password "{password}"')
def step_enter_valid_password(context, password):
    """Enter a valid password"""
    password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
    password_input.clear()
    password_input.send_keys(password)
    context.entered_password = password


@when('I enter password "{password}"')
def step_enter_password(context, password):
    """Enter any password"""
    password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
    password_input.clear()
    password_input.send_keys(password)
    context.entered_password = password


@when('I enter invalid email "{email}"')
def step_enter_invalid_email(context, email):
    """Enter an invalid email format"""
    email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
    email_input.clear()
    email_input.send_keys(email)
    context.entered_email = email


@when('I enter short password "{password}"')
def step_enter_short_password(context, password):
    """Enter a password that's too short"""
    password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
    password_input.clear()
    password_input.send_keys(password)
    context.entered_password = password


@when('I click the "{button_text}" button')
def step_click_button(context, button_text):
    """Click a button with specific text"""
    if button_text.lower() == "sign in":
        button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
    else:
        button = context.driver.find_element(By.XPATH, f"//button[contains(text(), '{button_text}')]")
    
    button.click()
    time.sleep(0.5)  # Brief pause for UI updates


@when('I click the "{button_text}" button without entering credentials')
def step_click_button_no_credentials(context, button_text):
    """Click button without entering any credentials"""
    button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
    button.click()
    time.sleep(0.5)


@when('I check the "Remember me" checkbox')
def step_check_remember_me(context):
    """Check the remember me checkbox"""
    checkbox = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='remember-me-checkbox']")
    if not checkbox.is_selected():
        checkbox.click()


@when('I click the "Show password" button')
def step_click_show_password(context):
    """Click the show password toggle"""
    toggle_button = context.driver.find_element(By.CSS_SELECTOR, "[aria-label='Show password']")
    toggle_button.click()


@when('I click the "Hide password" button')
def step_click_hide_password(context):
    """Click the hide password toggle"""
    toggle_button = context.driver.find_element(By.CSS_SELECTOR, "[aria-label='Hide password']")
    toggle_button.click()


@when('I click the "Forgot your password?" link')
def step_click_forgot_password(context):
    """Click the forgot password link"""
    link = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='forgot-password-link']")
    link.click()


@when('I use keyboard navigation')
def step_use_keyboard_navigation(context):
    """Test keyboard navigation through the form"""
    body = context.driver.find_element(By.TAG_NAME, "body")
    body.send_keys(Keys.TAB)
    context.keyboard_navigation = True


@when('I am using a screen reader')
def step_using_screen_reader(context):
    """Simulate screen reader usage (check accessibility attributes)"""
    context.screen_reader_mode = True


@when('I am using a mobile device')
def step_using_mobile_device(context):
    """Set mobile viewport"""
    context.driver.set_window_size(375, 667)  # iPhone SE size


@when('the network request fails')
def step_network_fails(context):
    """Simulate network failure"""
    # This would typically involve mocking the network request
    # For now, we'll just set a flag to check error handling
    context.network_failure = True


@when('my session expires')
def step_session_expires(context):
    """Simulate session expiration"""
    # Clear session storage/cookies to simulate expiration
    context.driver.delete_all_cookies()
    context.driver.execute_script("sessionStorage.clear();")
    context.driver.execute_script("localStorage.clear();")


@when('I try to access a protected page')
def step_access_protected_page(context):
    """Try to access a page that requires authentication"""
    context.driver.get(f"{context.base_url}/dashboard")


@when('I enter invalid credentials multiple times')
def step_multiple_invalid_attempts(context):
    """Enter invalid credentials multiple times"""
    for i in range(3):
        email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
        password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
        login_button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
        
        email_input.clear()
        email_input.send_keys(f"wrong{i}@example.com")
        password_input.clear()
        password_input.send_keys(f"wrongpassword{i}")
        login_button.click()
        time.sleep(1)


@when('I have triggered validation errors')
def step_trigger_validation_errors(context):
    """Trigger validation errors by submitting empty form"""
    login_button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
    login_button.click()
    time.sleep(0.5)


@when('I start typing in any form field')
def step_start_typing_form_field(context):
    """Start typing in a form field to clear errors"""
    email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
    email_input.send_keys("a")


@then('I should be redirected to the dashboard')
def step_redirected_to_dashboard(context):
    """Verify redirection to dashboard"""
    WebDriverWait(context.driver, 10).until(
        lambda driver: "/dashboard" in driver.current_url
    )
    assert "/dashboard" in context.driver.current_url


@then('I should see a welcome message')
def step_see_welcome_message(context):
    """Verify welcome message is displayed"""
    try:
        welcome_element = WebDriverWait(context.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Welcome') or contains(text(), 'Dashboard')]"))
        )
        assert welcome_element.is_displayed()
    except TimeoutException:
        assert False, "Welcome message not found"


@then('I should see my user profile in the navigation')
def step_see_user_profile_nav(context):
    """Verify user profile is visible in navigation"""
    try:
        profile_element = WebDriverWait(context.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'John Doe') or contains(@class, 'user-profile')]"))
        )
        assert profile_element.is_displayed()
    except TimeoutException:
        assert False, "User profile not found in navigation"


@then('I should see an error message "{error_message}"')
def step_see_error_message(context, error_message):
    """Verify specific error message is displayed"""
    try:
        error_element = WebDriverWait(context.driver, 5).until(
            EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{error_message}')]"))
        )
        assert error_element.is_displayed()
    except TimeoutException:
        assert False, f"Error message '{error_message}' not found"


@then('I should remain on the login page')
def step_remain_on_login_page(context):
    """Verify still on login page"""
    assert "/login" in context.driver.current_url


@then('the form fields should retain their values')
def step_form_fields_retain_values(context):
    """Verify form fields keep their entered values"""
    email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
    password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
    
    if hasattr(context, 'entered_email'):
        assert email_input.get_attribute('value') == context.entered_email
    if hasattr(context, 'entered_password'):
        assert password_input.get_attribute('value') == context.entered_password


@then('I should see validation errors')
def step_see_validation_errors(context):
    """Verify validation errors are displayed"""
    try:
        error_elements = context.driver.find_elements(By.CSS_SELECTOR, "[role='alert'], .error-message, .text-red-600")
        assert len(error_elements) > 0, "No validation errors found"
    except:
        assert False, "Validation errors not displayed"


@then('the form should not be submitted')
def step_form_not_submitted(context):
    """Verify form was not submitted (still on login page)"""
    assert "/login" in context.driver.current_url


@then('my session should be remembered for future visits')
def step_session_remembered(context):
    """Verify session persistence"""
    # Check for remember me cookie or localStorage
    cookies = context.driver.get_cookies()
    remember_cookie = any(cookie['name'] == 'remember_token' for cookie in cookies)
    
    # Or check localStorage
    remember_token = context.driver.execute_script("return localStorage.getItem('remember_token');")
    
    assert remember_cookie or remember_token, "Session not remembered"


@then('the password should be hidden by default')
def step_password_hidden_default(context):
    """Verify password field is hidden by default"""
    password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
    assert password_input.get_attribute('type') == 'password'


@then('the password should be visible')
def step_password_visible(context):
    """Verify password field shows text"""
    password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
    assert password_input.get_attribute('type') == 'text'


@then('the password should be hidden again')
def step_password_hidden_again(context):
    """Verify password field is hidden again"""
    password_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='password-input']")
    assert password_input.get_attribute('type') == 'password'


@then('I should be redirected to the forgot password page')
def step_redirected_forgot_password(context):
    """Verify redirection to forgot password page"""
    WebDriverWait(context.driver, 5).until(
        lambda driver: "/forgot-password" in driver.current_url
    )
    assert "/forgot-password" in context.driver.current_url


@then('I should see the password reset form')
def step_see_password_reset_form(context):
    """Verify password reset form is displayed"""
    try:
        reset_form = WebDriverWait(context.driver, 5).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "form, [data-testid='reset-form']"))
        )
        assert reset_form.is_displayed()
    except TimeoutException:
        assert False, "Password reset form not found"


@then('I should see a loading indicator')
def step_see_loading_indicator(context):
    """Verify loading indicator is displayed"""
    try:
        loading_element = context.driver.find_element(By.CSS_SELECTOR, ".animate-spin, [data-testid='loading']")
        assert loading_element.is_displayed()
    except:
        # Check if button text changed to indicate loading
        login_button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
        assert "Signing in" in login_button.text


@then('the submit button should be disabled')
def step_submit_button_disabled(context):
    """Verify submit button is disabled"""
    login_button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
    assert not login_button.is_enabled() or login_button.get_attribute('disabled') is not None


@then('the button text should change to "{expected_text}"')
def step_button_text_changed(context, expected_text):
    """Verify button text changed"""
    login_button = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button']")
    assert expected_text in login_button.text
