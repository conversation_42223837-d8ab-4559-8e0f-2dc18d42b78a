"""Step definitions for ROI analysis BDD tests."""

from behave import given, when, then
import requests
import json
from datetime import datetime


@given('the ROI analysis system is available')
def step_roi_system_available(context):
    """Verify ROI analysis system is operational."""
    response = requests.get(f"{context.base_url}/api/v1/salary-intelligence/health")
    assert response.status_code == 200
    context.roi_system_available = True


@given('salary intelligence data is current')
def step_salary_data_current(context):
    """Verify salary intelligence data is up to date."""
    # This would check data freshness in a real implementation
    context.salary_data_current = True


@given('cost calculation engine is operational')
def step_cost_engine_operational(context):
    """Verify cost calculation engine is working."""
    response = requests.get(f"{context.base_url}/api/v1/cost-calculator/health")
    assert response.status_code == 200
    context.cost_engine_operational = True


@given('a security analyst considering CISSP certification')
def step_analyst_considering_cissp(context):
    """Set up analyst profile considering CISSP."""
    context.roi_request = {
        'certification_id': 1,  # CISSP
        'current_role_id': 1,   # Security Analyst
        'target_role_id': None,
        'investment_cost': 3000.0,
        'location': 'remote',
        'experience_years': 5
    }


@given('the certification costs ${cost:d} total')
def step_certification_cost(context, cost):
    """Set certification cost."""
    context.roi_request['investment_cost'] = float(cost)


@given('their current salary is ${salary:d}')
def step_current_salary(context, salary):
    """Set current salary."""
    context.roi_request['current_salary'] = float(salary)


@given('they are located in "{location}"')
def step_set_location(context, location):
    """Set location for salary analysis."""
    context.roi_request['location'] = location


@when('they request ROI analysis')
def step_request_roi_analysis(context):
    """Request ROI analysis."""
    response = requests.post(
        f"{context.base_url}/api/v1/salary-intelligence/roi-analysis",
        json=context.roi_request,
        headers={'Content-Type': 'application/json'}
    )
    
    assert response.status_code == 200
    context.roi_response = response.json()


@then('they should see expected salary increase')
def step_verify_salary_increase(context):
    """Verify expected salary increase is provided."""
    assert 'expected_salary_increase' in context.roi_response
    assert context.roi_response['expected_salary_increase'] > 0


@then('payback period should be calculated')
def step_verify_payback_period(context):
    """Verify payback period is calculated."""
    assert 'payback_period_months' in context.roi_response
    assert context.roi_response['payback_period_months'] > 0


@then('5-year ROI projection should be provided')
def step_verify_five_year_roi(context):
    """Verify 5-year ROI projection is provided."""
    assert 'five_year_roi' in context.roi_response
    assert context.roi_response['five_year_roi'] > 0


@then('confidence score should be displayed')
def step_verify_confidence_score(context):
    """Verify confidence score is displayed."""
    assert 'confidence_score' in context.roi_response
    assert 0 <= context.roi_response['confidence_score'] <= 1


@then('market conditions should be considered')
def step_verify_market_conditions(context):
    """Verify market conditions are considered."""
    assert 'market_conditions' in context.roi_response
    assert isinstance(context.roi_response['market_conditions'], dict)


@given('a security professional considering multiple certifications')
def step_professional_multiple_certs(context):
    """Set up professional considering multiple certifications."""
    context.certifications = [
        {'id': 1, 'name': 'CISSP', 'cost': 3000, 'salary_increase': 0.15},
        {'id': 2, 'name': 'CISM', 'cost': 2500, 'salary_increase': 0.12},
        {'id': 3, 'name': 'CEH', 'cost': 1500, 'salary_increase': 0.08}
    ]


@given('CISSP costs ${cost:d} with {increase:d}% salary increase potential')
def step_cissp_details(context, cost, increase):
    """Set CISSP details."""
    context.cissp_details = {
        'cost': cost,
        'salary_increase': increase / 100.0
    }


@given('CISM costs ${cost:d} with {increase:d}% salary increase potential')
def step_cism_details(context, cost, increase):
    """Set CISM details."""
    context.cism_details = {
        'cost': cost,
        'salary_increase': increase / 100.0
    }


@given('CEH costs ${cost:d} with {increase:d}% salary increase potential')
def step_ceh_details(context, cost, increase):
    """Set CEH details."""
    context.ceh_details = {
        'cost': cost,
        'salary_increase': increase / 100.0
    }


@when('they compare ROI for all certifications')
def step_compare_all_rois(context):
    """Compare ROI for all certifications."""
    context.roi_comparisons = []
    
    for cert in context.certifications:
        roi_request = {
            'certification_id': cert['id'],
            'current_role_id': 1,
            'investment_cost': cert['cost'],
            'location': 'remote',
            'experience_years': 5
        }
        
        response = requests.post(
            f"{context.base_url}/api/v1/salary-intelligence/roi-analysis",
            json=roi_request,
            headers={'Content-Type': 'application/json'}
        )
        
        assert response.status_code == 200
        roi_data = response.json()
        roi_data['certification_name'] = cert['name']
        context.roi_comparisons.append(roi_data)


@then('certifications should be ranked by ROI')
def step_verify_roi_ranking(context):
    """Verify certifications are ranked by ROI."""
    assert len(context.roi_comparisons) > 1
    
    # Sort by ROI and verify ranking makes sense
    sorted_rois = sorted(context.roi_comparisons, 
                        key=lambda x: x['five_year_roi'], reverse=True)
    
    # Verify the ranking is logical (higher ROI should generally correlate with better value)
    for i in range(len(sorted_rois) - 1):
        assert sorted_rois[i]['five_year_roi'] >= sorted_rois[i + 1]['five_year_roi']


@then('cost-benefit analysis should be provided')
def step_verify_cost_benefit_analysis(context):
    """Verify cost-benefit analysis is provided."""
    for roi in context.roi_comparisons:
        assert 'investment_cost' in roi
        assert 'expected_salary_increase' in roi
        assert 'five_year_roi' in roi


@then('recommendation should be given')
def step_verify_recommendation(context):
    """Verify recommendation is given."""
    # The best ROI certification should be identifiable
    best_roi = max(context.roi_comparisons, key=lambda x: x['five_year_roi'])
    assert best_roi['five_year_roi'] > 0


@then('risk factors should be highlighted')
def step_verify_risk_factors(context):
    """Verify risk factors are highlighted."""
    for roi in context.roi_comparisons:
        assert 'risk_factors' in roi
        assert isinstance(roi['risk_factors'], list)


@given('an enterprise with {count:d} security professionals')
def step_enterprise_with_professionals(context, count):
    """Set up enterprise with security professionals."""
    context.enterprise_data = {
        'enterprise_id': 1,
        'employee_count': count,
        'professional_count': count
    }


@given('they plan to invest ${amount:d} in training')
def step_enterprise_investment(context, amount):
    """Set enterprise training investment."""
    context.enterprise_data['investment_amount'] = float(amount)


@given('the training covers CISSP, CISM, and Security+ certifications')
def step_training_covers_certifications(context):
    """Set training program certifications."""
    context.enterprise_data['training_programs'] = ['CISSP', 'CISM', 'Security+']


@given('the timeline is {months:d} months')
def step_set_enterprise_timeline(context, months):
    """Set enterprise training timeline."""
    context.enterprise_data['timeline_months'] = months


@when('they request enterprise ROI analysis')
def step_request_enterprise_roi(context):
    """Request enterprise ROI analysis."""
    response = requests.post(
        f"{context.base_url}/api/v1/budget-optimization/roi/calculate",
        json=context.enterprise_data,
        headers={'Content-Type': 'application/json'}
    )
    
    assert response.status_code == 200
    context.enterprise_roi_response = response.json()


@then('total expected return should be calculated')
def step_verify_total_return(context):
    """Verify total expected return is calculated."""
    assert 'projected_return' in context.enterprise_roi_response
    assert context.enterprise_roi_response['projected_return'] > 0


@then('cost per employee should be shown')
def step_verify_cost_per_employee(context):
    """Verify cost per employee is shown."""
    investment = context.enterprise_data['investment_amount']
    employee_count = context.enterprise_data['employee_count']
    expected_cost_per_employee = investment / employee_count
    
    # This would be calculated in the response
    assert 'cost_per_employee' in context.enterprise_roi_response or \
           context.enterprise_roi_response['investment_amount'] / employee_count > 0


@then('productivity improvements should be estimated')
def step_verify_productivity_improvements(context):
    """Verify productivity improvements are estimated."""
    # This would be part of the ROI calculation
    assert 'projected_return' in context.enterprise_roi_response
    assert context.enterprise_roi_response['projected_return'] > context.enterprise_data['investment_amount']


@then('risk reduction benefits should be quantified')
def step_verify_risk_reduction_benefits(context):
    """Verify risk reduction benefits are quantified."""
    # This would be part of the comprehensive ROI analysis
    assert 'risk_adjusted_roi' in context.enterprise_roi_response or \
           'sensitivity_analysis' in context.enterprise_roi_response
