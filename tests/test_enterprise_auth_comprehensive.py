"""Comprehensive test suite for enterprise authentication and authorization.

This module provides extensive testing for JWT authentication, RBAC permissions,
multi-tenant security, and enterprise access controls.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
import jwt

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole
from models.compliance import AuditLog, AuditEventType, RiskLevel
from services.enterprise_auth_service import (
    EnterpriseAuthService, Permission, AuthenticationMethod
)


class TestEnterpriseAuthentication:
    """Test enterprise authentication functionality."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def auth_service(self, mock_db):
        return EnterpriseAuthService(mock_db)
    
    @pytest.fixture
    def sample_organization(self):
        return EnterpriseOrganization(
            id=1,
            name="Test Corp",
            domain="testcorp.com",
            subscription_tier="enterprise",
            is_active=True
        )
    
    @pytest.fixture
    def sample_users(self, sample_organization):
        """Create users with different roles for testing."""
        return {
            'super_admin': EnterpriseUser(
                user_id='super_admin_001',
                email='<EMAIL>',
                role=UserRole.SUPER_ADMIN,
                organization_id=sample_organization.id,
                is_active=True,
                password_hash='hashed_password'
            ),
            'org_admin': EnterpriseUser(
                user_id='org_admin_001',
                email='<EMAIL>',
                role=UserRole.ORG_ADMIN,
                organization_id=sample_organization.id,
                is_active=True,
                password_hash='hashed_password'
            ),
            'manager': EnterpriseUser(
                user_id='manager_001',
                email='<EMAIL>',
                role=UserRole.MANAGER,
                organization_id=sample_organization.id,
                is_active=True,
                password_hash='hashed_password'
            ),
            'learner': EnterpriseUser(
                user_id='learner_001',
                email='<EMAIL>',
                role=UserRole.LEARNER,
                organization_id=sample_organization.id,
                is_active=True,
                password_hash='hashed_password'
            ),
            'compliance_officer': EnterpriseUser(
                user_id='compliance_001',
                email='<EMAIL>',
                role=UserRole.COMPLIANCE_OFFICER,
                organization_id=sample_organization.id,
                is_active=True,
                password_hash='hashed_password'
            )
        }
    
    def test_authenticate_user_password_success(self, auth_service, mock_db, sample_users):
        """Test successful password authentication."""
        user = sample_users['org_admin']
        mock_db.query.return_value.filter.return_value.first.return_value = user
        mock_db.commit.return_value = None
        
        with patch.object(auth_service, '_verify_password', return_value=True):
            with patch.object(auth_service, 'audit_service') as mock_audit:
                result = auth_service.authenticate_user(
                    email=user.email,
                    password='correct_password',
                    auth_method=AuthenticationMethod.PASSWORD
                )
        
        # Verify authentication result
        assert 'access_token' in result
        assert 'token_type' in result
        assert 'expires_in' in result
        assert 'user' in result
        assert 'permissions' in result
        assert 'organization' in result
        
        # Verify user data
        assert result['user']['email'] == user.email
        assert result['user']['role'] == UserRole.ORG_ADMIN.value
        
        # Verify permissions are included
        assert isinstance(result['permissions'], list)
        assert len(result['permissions']) > 0
        
        # Verify audit logging
        mock_audit.log_event.assert_called_once()
        mock_db.commit.assert_called_once()
    
    def test_authenticate_user_invalid_password(self, auth_service, mock_db, sample_users):
        """Test authentication with invalid password."""
        user = sample_users['org_admin']
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        with patch.object(auth_service, '_verify_password', return_value=False):
            with patch.object(auth_service, '_log_failed_authentication') as mock_log:
                with pytest.raises(ValueError, match="Invalid credentials"):
                    auth_service.authenticate_user(
                        email=user.email,
                        password='wrong_password'
                    )
        
        mock_log.assert_called_once()
    
    def test_authenticate_user_not_found(self, auth_service, mock_db):
        """Test authentication with non-existent user."""
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        with patch.object(auth_service, '_log_failed_authentication') as mock_log:
            with pytest.raises(ValueError, match="Invalid credentials"):
                auth_service.authenticate_user(
                    email='<EMAIL>',
                    password='password'
                )
        
        mock_log.assert_called_once()
    
    def test_authenticate_user_inactive(self, auth_service, mock_db, sample_users):
        """Test authentication with inactive user."""
        user = sample_users['org_admin']
        user.is_active = False
        
        mock_db.query.return_value.filter.return_value.first.return_value = None  # Filtered out by is_active
        
        with patch.object(auth_service, '_log_failed_authentication') as mock_log:
            with pytest.raises(ValueError, match="Invalid credentials"):
                auth_service.authenticate_user(
                    email=user.email,
                    password='password'
                )
        
        mock_log.assert_called_once()
    
    def test_authenticate_user_organization_mismatch(self, auth_service, mock_db, sample_users):
        """Test authentication with organization domain mismatch."""
        user = sample_users['org_admin']
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            None,  # Organization query returns None
            user   # User query returns user
        ]
        
        with patch.object(auth_service, '_log_failed_authentication') as mock_log:
            with pytest.raises(ValueError, match="Invalid organization context"):
                auth_service.authenticate_user(
                    email=user.email,
                    password='password',
                    organization_domain='wrongdomain.com'
                )
        
        mock_log.assert_called_once()
    
    def test_authenticate_user_sso_success(self, auth_service, mock_db, sample_users):
        """Test successful SSO authentication."""
        user = sample_users['org_admin']
        mock_db.query.return_value.filter.return_value.first.return_value = user
        mock_db.commit.return_value = None
        
        with patch.object(auth_service, '_verify_sso_token', return_value=True):
            with patch.object(auth_service, 'audit_service') as mock_audit:
                result = auth_service.authenticate_user(
                    email=user.email,
                    auth_method=AuthenticationMethod.SSO_SAML,
                    sso_token='valid_saml_token'
                )
        
        assert 'access_token' in result
        assert result['user']['email'] == user.email
        mock_audit.log_event.assert_called_once()
    
    def test_authenticate_user_sso_invalid_token(self, auth_service, mock_db, sample_users):
        """Test SSO authentication with invalid token."""
        user = sample_users['org_admin']
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        with patch.object(auth_service, '_verify_sso_token', return_value=False):
            with patch.object(auth_service, '_log_failed_authentication') as mock_log:
                with pytest.raises(ValueError, match="Invalid SSO token"):
                    auth_service.authenticate_user(
                        email=user.email,
                        auth_method=AuthenticationMethod.SSO_SAML,
                        sso_token='invalid_token'
                    )
        
        mock_log.assert_called_once()


class TestJWTTokenManagement:
    """Test JWT token generation and verification."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def auth_service(self, mock_db):
        return EnterpriseAuthService(mock_db)
    
    @pytest.fixture
    def sample_user(self):
        return EnterpriseUser(
            user_id='test_user',
            email='<EMAIL>',
            role=UserRole.MANAGER,
            organization_id=1,
            is_active=True
        )
    
    def test_generate_access_token(self, auth_service, sample_user):
        """Test JWT access token generation."""
        token = auth_service._generate_access_token(sample_user)
        
        # Verify token is a string
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Decode token to verify contents
        payload = jwt.decode(token, auth_service.jwt_secret, algorithms=[auth_service.jwt_algorithm])
        
        assert payload['user_id'] == sample_user.user_id
        assert payload['email'] == sample_user.email
        assert payload['organization_id'] == sample_user.organization_id
        assert payload['role'] == sample_user.role.value
        assert 'permissions' in payload
        assert 'exp' in payload
        assert 'iat' in payload
        assert payload['iss'] == 'certpathfinder-enterprise'
    
    def test_verify_access_token_valid(self, auth_service, mock_db, sample_user):
        """Test verification of valid access token."""
        # Generate token
        token = auth_service._generate_access_token(sample_user)
        
        # Mock user lookup
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        # Verify token
        payload = auth_service.verify_access_token(token)
        
        assert payload['user_id'] == sample_user.user_id
        assert payload['email'] == sample_user.email
        assert payload['organization_id'] == sample_user.organization_id
    
    def test_verify_access_token_expired(self, auth_service, mock_db, sample_user):
        """Test verification of expired token."""
        # Create expired token
        with patch.object(auth_service, 'token_expiry_hours', -1):  # Expired 1 hour ago
            token = auth_service._generate_access_token(sample_user)
        
        with pytest.raises(ValueError, match="Token has expired"):
            auth_service.verify_access_token(token)
    
    def test_verify_access_token_invalid_signature(self, auth_service, mock_db):
        """Test verification of token with invalid signature."""
        # Create token with different secret
        fake_payload = {
            'user_id': 'fake_user',
            'exp': datetime.utcnow() + timedelta(hours=1)
        }
        fake_token = jwt.encode(fake_payload, 'wrong_secret', algorithm='HS256')
        
        with pytest.raises(ValueError, match="Invalid token"):
            auth_service.verify_access_token(fake_token)
    
    def test_verify_access_token_user_not_found(self, auth_service, mock_db, sample_user):
        """Test verification when user no longer exists."""
        token = auth_service._generate_access_token(sample_user)
        
        # Mock user not found
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="User no longer exists or is inactive"):
            auth_service.verify_access_token(token)
    
    def test_verify_access_token_user_inactive(self, auth_service, mock_db, sample_user):
        """Test verification when user is inactive."""
        token = auth_service._generate_access_token(sample_user)
        
        # Make user inactive
        sample_user.is_active = False
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        with pytest.raises(ValueError, match="User no longer exists or is inactive"):
            auth_service.verify_access_token(token)


class TestRoleBasedAccessControl:
    """Test RBAC permissions and authorization."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def auth_service(self, mock_db):
        return EnterpriseAuthService(mock_db)
    
    @pytest.fixture
    def users_by_role(self):
        """Create users with different roles."""
        return {
            UserRole.SUPER_ADMIN: EnterpriseUser(
                user_id='super_admin',
                role=UserRole.SUPER_ADMIN,
                organization_id=1,
                is_active=True
            ),
            UserRole.ORG_ADMIN: EnterpriseUser(
                user_id='org_admin',
                role=UserRole.ORG_ADMIN,
                organization_id=1,
                is_active=True
            ),
            UserRole.MANAGER: EnterpriseUser(
                user_id='manager',
                role=UserRole.MANAGER,
                organization_id=1,
                is_active=True
            ),
            UserRole.TEAM_LEAD: EnterpriseUser(
                user_id='team_lead',
                role=UserRole.TEAM_LEAD,
                organization_id=1,
                is_active=True
            ),
            UserRole.LEARNER: EnterpriseUser(
                user_id='learner',
                role=UserRole.LEARNER,
                organization_id=1,
                is_active=True
            ),
            UserRole.COMPLIANCE_OFFICER: EnterpriseUser(
                user_id='compliance',
                role=UserRole.COMPLIANCE_OFFICER,
                organization_id=1,
                is_active=True
            ),
            UserRole.AUDITOR: EnterpriseUser(
                user_id='auditor',
                role=UserRole.AUDITOR,
                organization_id=1,
                is_active=True
            )
        }
    
    def test_super_admin_permissions(self, auth_service, mock_db, users_by_role):
        """Test that super admin has all permissions."""
        user = users_by_role[UserRole.SUPER_ADMIN]
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Test various high-level permissions
        high_level_permissions = [
            Permission.ORG_ADMIN,
            Permission.USER_ADMIN,
            Permission.COMPLIANCE_ADMIN,
            Permission.ANALYTICS_ADMIN,
            Permission.AUDIT_ADMIN,
            Permission.SECURITY_ADMIN
        ]
        
        for permission in high_level_permissions:
            assert auth_service.check_permission(user.user_id, permission, organization_id=1)
    
    def test_org_admin_permissions(self, auth_service, mock_db, users_by_role):
        """Test organization admin permissions."""
        user = users_by_role[UserRole.ORG_ADMIN]
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Should have these permissions
        allowed_permissions = [
            Permission.ORG_VIEW,
            Permission.ORG_EDIT,
            Permission.USER_ADMIN,
            Permission.DEPT_ADMIN,
            Permission.BUDGET_ADMIN,
            Permission.COMPLIANCE_ADMIN,
            Permission.ANALYTICS_ADMIN
        ]
        
        for permission in allowed_permissions:
            assert auth_service.check_permission(user.user_id, permission, organization_id=1)
        
        # Should NOT have super admin permissions
        denied_permissions = [
            Permission.ORG_ADMIN,  # Can't create/delete orgs
            Permission.SECURITY_ADMIN  # Can't manage global security
        ]
        
        for permission in denied_permissions:
            assert not auth_service.check_permission(user.user_id, permission, organization_id=1)
    
    def test_manager_permissions(self, auth_service, mock_db, users_by_role):
        """Test manager permissions."""
        user = users_by_role[UserRole.MANAGER]
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Should have these permissions
        allowed_permissions = [
            Permission.ORG_VIEW,
            Permission.USER_VIEW,
            Permission.USER_EDIT,
            Permission.DEPT_VIEW,
            Permission.DEPT_EDIT,
            Permission.BUDGET_VIEW,
            Permission.BUDGET_APPROVE,
            Permission.COMPLIANCE_VIEW,
            Permission.ANALYTICS_VIEW
        ]
        
        for permission in allowed_permissions:
            assert auth_service.check_permission(user.user_id, permission, organization_id=1)
        
        # Should NOT have admin permissions
        denied_permissions = [
            Permission.USER_ADMIN,
            Permission.DEPT_ADMIN,
            Permission.BUDGET_ADMIN,
            Permission.COMPLIANCE_ADMIN
        ]
        
        for permission in denied_permissions:
            assert not auth_service.check_permission(user.user_id, permission, organization_id=1)
    
    def test_learner_permissions(self, auth_service, mock_db, users_by_role):
        """Test learner (basic user) permissions."""
        user = users_by_role[UserRole.LEARNER]
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Should have minimal permissions
        allowed_permissions = [
            Permission.ORG_VIEW,
            Permission.USER_VIEW,
            Permission.COMPLIANCE_VIEW
        ]
        
        for permission in allowed_permissions:
            assert auth_service.check_permission(user.user_id, permission, organization_id=1)
        
        # Should NOT have any admin or edit permissions
        denied_permissions = [
            Permission.USER_EDIT,
            Permission.USER_ADMIN,
            Permission.DEPT_EDIT,
            Permission.BUDGET_VIEW,
            Permission.COMPLIANCE_ADMIN,
            Permission.ANALYTICS_ADMIN
        ]
        
        for permission in denied_permissions:
            assert not auth_service.check_permission(user.user_id, permission, organization_id=1)
    
    def test_compliance_officer_permissions(self, auth_service, mock_db, users_by_role):
        """Test compliance officer specialized permissions."""
        user = users_by_role[UserRole.COMPLIANCE_OFFICER]
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Should have compliance-focused permissions
        allowed_permissions = [
            Permission.ORG_VIEW,
            Permission.USER_VIEW,
            Permission.COMPLIANCE_ADMIN,  # Key permission
            Permission.AUDIT_VIEW,
            Permission.ANALYTICS_VIEW
        ]
        
        for permission in allowed_permissions:
            assert auth_service.check_permission(user.user_id, permission, organization_id=1)
        
        # Should NOT have user/dept admin permissions
        denied_permissions = [
            Permission.USER_ADMIN,
            Permission.DEPT_ADMIN,
            Permission.BUDGET_ADMIN
        ]
        
        for permission in denied_permissions:
            assert not auth_service.check_permission(user.user_id, permission, organization_id=1)
    
    def test_auditor_permissions(self, auth_service, mock_db, users_by_role):
        """Test auditor read-only permissions."""
        user = users_by_role[UserRole.AUDITOR]
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Should have read-only permissions
        allowed_permissions = [
            Permission.ORG_VIEW,
            Permission.USER_VIEW,
            Permission.COMPLIANCE_VIEW,
            Permission.AUDIT_VIEW,
            Permission.ANALYTICS_VIEW
        ]
        
        for permission in allowed_permissions:
            assert auth_service.check_permission(user.user_id, permission, organization_id=1)
        
        # Should NOT have any admin permissions
        denied_permissions = [
            Permission.USER_ADMIN,
            Permission.COMPLIANCE_ADMIN,
            Permission.AUDIT_ADMIN,
            Permission.ANALYTICS_ADMIN
        ]
        
        for permission in denied_permissions:
            assert not auth_service.check_permission(user.user_id, permission, organization_id=1)
    
    def test_permission_check_user_not_found(self, auth_service, mock_db):
        """Test permission check when user doesn't exist."""
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = auth_service.check_permission('nonexistent_user', Permission.ORG_VIEW)
        assert result is False
    
    def test_permission_check_organization_mismatch(self, auth_service, mock_db, users_by_role):
        """Test permission check with organization mismatch."""
        user = users_by_role[UserRole.MANAGER]
        user.organization_id = 1
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Request permission for different organization
        result = auth_service.check_permission(
            user.user_id, 
            Permission.ORG_VIEW, 
            organization_id=2  # Different org
        )
        assert result is False
    
    def test_additional_permissions(self, auth_service, mock_db, users_by_role):
        """Test additional permissions granted to users."""
        user = users_by_role[UserRole.LEARNER]
        user.permissions = {
            'additional_permissions': ['budget_view', 'analytics_view']
        }
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Should have additional permissions beyond role defaults
        assert auth_service.check_permission(user.user_id, Permission.BUDGET_VIEW, organization_id=1)
        assert auth_service.check_permission(user.user_id, Permission.ANALYTICS_VIEW, organization_id=1)
        
        # Should still not have permissions not granted
        assert not auth_service.check_permission(user.user_id, Permission.USER_ADMIN, organization_id=1)
