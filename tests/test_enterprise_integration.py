"""Integration tests for Agent 3 - Enterprise Analytics Engine.

This module provides comprehensive integration testing for enterprise workflows
including end-to-end compliance reporting, data intelligence generation,
and multi-tenant security scenarios.
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from database import Base
from api.app import app
from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole, Department
from models.compliance import (
    ComplianceRequirement, ComplianceAssessment, ComplianceFramework, 
    ComplianceStatus, RiskLevel, AuditLog, DataProcessingActivity
)
from services.compliance_service import ComplianceService
from services.data_intelligence_service import DataIntelligenceService
from services.enterprise_auth_service import EnterpriseAuthService


class TestEnterpriseIntegration:
    """Integration tests for enterprise functionality."""
    
    @pytest.fixture(scope="class")
    def test_db(self):
        """Create test database."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        return TestingSessionLocal()
    
    @pytest.fixture(scope="class")
    def test_client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def sample_organization(self, test_db):
        """Create sample organization."""
        org = EnterpriseOrganization(
            name="Test Enterprise Corp",
            display_name="Test Enterprise",
            slug="test-enterprise",
            domain="testenterprise.com",
            industry="Technology",
            employee_count=500,
            subscription_tier="enterprise",
            license_count=100,
            is_active=True
        )
        test_db.add(org)
        test_db.commit()
        test_db.refresh(org)
        return org
    
    @pytest.fixture
    def sample_department(self, test_db, sample_organization):
        """Create sample department."""
        dept = Department(
            organization_id=sample_organization.id,
            name="Cybersecurity",
            description="Information Security Department",
            budget_allocated=100000.00
        )
        test_db.add(dept)
        test_db.commit()
        test_db.refresh(dept)
        return dept
    
    @pytest.fixture
    def sample_users(self, test_db, sample_organization, sample_department):
        """Create sample users."""
        users = [
            EnterpriseUser(
                user_id="admin_001",
                organization_id=sample_organization.id,
                department_id=sample_department.id,
                email="<EMAIL>",
                first_name="Admin",
                last_name="User",
                role=UserRole.ORG_ADMIN,
                job_title="CISO",
                salary_range="150000-200000",
                is_active=True
            ),
            EnterpriseUser(
                user_id="manager_001",
                organization_id=sample_organization.id,
                department_id=sample_department.id,
                email="<EMAIL>",
                first_name="Manager",
                last_name="User",
                role=UserRole.MANAGER,
                job_title="Security Manager",
                salary_range="100000-130000",
                is_active=True
            ),
            EnterpriseUser(
                user_id="analyst_001",
                organization_id=sample_organization.id,
                department_id=sample_department.id,
                email="<EMAIL>",
                first_name="Analyst",
                last_name="User",
                role=UserRole.LEARNER,
                job_title="Security Analyst",
                salary_range="70000-90000",
                certifications_completed=["Security+", "CySA+"],
                is_active=True
            )
        ]
        
        for user in users:
            test_db.add(user)
        
        test_db.commit()
        
        for user in users:
            test_db.refresh(user)
        
        return users
    
    def test_end_to_end_compliance_workflow(self, test_db, sample_organization, sample_users):
        """Test complete compliance workflow from requirement to report."""
        # Arrange
        compliance_service = ComplianceService(test_db)
        
        # Step 1: Create compliance requirements
        gdpr_requirements = [
            {
                'organization_id': sample_organization.id,
                'framework': ComplianceFramework.GDPR,
                'requirement_id': 'GDPR-Art-32',
                'title': 'Security of processing',
                'description': 'Implement appropriate technical and organizational measures',
                'risk_level': RiskLevel.HIGH,
                'assigned_to': sample_users[0].user_id
            },
            {
                'organization_id': sample_organization.id,
                'framework': ComplianceFramework.GDPR,
                'requirement_id': 'GDPR-Art-33',
                'title': 'Notification of personal data breach',
                'description': 'Notify supervisory authority of breach within 72 hours',
                'risk_level': RiskLevel.CRITICAL,
                'assigned_to': sample_users[1].user_id
            }
        ]
        
        created_requirements = []
        for req_data in gdpr_requirements:
            req = compliance_service.create_compliance_requirement(req_data)
            created_requirements.append(req)
        
        # Step 2: Perform assessments
        assessments = []
        for i, req in enumerate(created_requirements):
            assessment_data = {
                'assessment_date': datetime.now(),
                'assessor_id': sample_users[0].user_id,
                'organization_id': sample_organization.id,
                'status': ComplianceStatus.COMPLIANT if i == 0 else ComplianceStatus.NON_COMPLIANT,
                'score': 95.0 if i == 0 else 45.0,
                'findings': 'Controls implemented' if i == 0 else 'Missing breach notification procedure'
            }
            
            assessment = compliance_service.assess_compliance_requirement(req.id, assessment_data)
            assessments.append(assessment)
        
        # Step 3: Generate GDPR report
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        report = compliance_service.generate_gdpr_report(
            sample_organization.id, start_date, end_date
        )
        
        # Assert
        assert len(created_requirements) == 2
        assert len(assessments) == 2
        assert report is not None
        assert report.report_type == ComplianceFramework.GDPR
        assert report.overall_score > 0
        assert len(report.compliant_requirements) == 1
        assert len(report.non_compliant_requirements) == 1
    
    def test_data_intelligence_salary_analysis(self, test_db, sample_organization, sample_users):
        """Test data intelligence salary analysis workflow."""
        # Arrange
        intelligence_service = DataIntelligenceService(test_db)
        
        # Act
        salary_report = intelligence_service.generate_salary_intelligence({
            'location': None,  # All locations
            'role': None       # All roles
        })
        
        # Assert
        assert salary_report is not None
        assert 'report_id' in salary_report
        assert 'salary_statistics' in salary_report
        assert salary_report['sample_size'] >= 3  # We have 3 users with salary data
        assert 'role_based_analysis' in salary_report
        assert 'certification_impact' in salary_report
    
    def test_skills_gap_analysis_workflow(self, test_db, sample_organization):
        """Test skills gap analysis workflow."""
        # Arrange
        intelligence_service = DataIntelligenceService(test_db)
        
        # Act
        skills_report = intelligence_service.analyze_skills_gap(
            industry="Technology",
            location="San Francisco",
            organization_id=sample_organization.id
        )
        
        # Assert
        assert skills_report is not None
        assert 'report_id' in skills_report
        assert 'current_skills_landscape' in skills_report
        assert 'market_demand' in skills_report
        assert 'identified_gaps' in skills_report
        assert 'priority_certifications' in skills_report
        assert 'training_recommendations' in skills_report
    
    def test_multi_tenant_data_isolation(self, test_db, sample_organization, sample_users):
        """Test multi-tenant data isolation."""
        # Arrange
        auth_service = EnterpriseAuthService(test_db)
        
        # Create second organization
        org2 = EnterpriseOrganization(
            name="Another Corp",
            slug="another-corp",
            domain="anothercorp.com",
            industry="Finance",
            is_active=True
        )
        test_db.add(org2)
        test_db.commit()
        test_db.refresh(org2)
        
        # Create user in second organization
        user2 = EnterpriseUser(
            user_id="user_org2",
            organization_id=org2.id,
            email="<EMAIL>",
            first_name="User",
            last_name="Two",
            role=UserRole.MANAGER,
            is_active=True
        )
        test_db.add(user2)
        test_db.commit()
        test_db.refresh(user2)
        
        # Test tenant isolation
        user1_id = sample_users[0].user_id
        
        # User from org1 should access org1 data
        assert auth_service.enforce_tenant_isolation(user1_id, sample_organization.id) is True
        
        # User from org1 should NOT access org2 data
        assert auth_service.enforce_tenant_isolation(user1_id, org2.id) is False
        
        # User from org2 should access org2 data
        assert auth_service.enforce_tenant_isolation(user2.user_id, org2.id) is True
        
        # User from org2 should NOT access org1 data
        assert auth_service.enforce_tenant_isolation(user2.user_id, sample_organization.id) is False
    
    def test_enterprise_authentication_workflow(self, test_db, sample_users):
        """Test enterprise authentication workflow."""
        # Arrange
        auth_service = EnterpriseAuthService(test_db)
        admin_user = sample_users[0]
        
        # Mock password verification for testing
        def mock_verify_password(password, hash):
            return password == "test_password"
        
        auth_service._verify_password = mock_verify_password
        
        # Act
        auth_result = auth_service.authenticate_user(
            email=admin_user.email,
            password="test_password"
        )
        
        # Assert
        assert 'access_token' in auth_result
        assert 'user' in auth_result
        assert 'permissions' in auth_result
        assert auth_result['user']['email'] == admin_user.email
        assert auth_result['user']['role'] == UserRole.ORG_ADMIN.value
        
        # Test token verification
        token = auth_result['access_token']
        payload = auth_service.verify_access_token(token)
        
        assert payload['user_id'] == admin_user.user_id
        assert payload['email'] == admin_user.email
    
    def test_compliance_data_processing_activities(self, test_db, sample_organization):
        """Test GDPR data processing activities management."""
        # Arrange
        activity_data = {
            'organization_id': sample_organization.id,
            'activity_name': 'Employee Data Processing',
            'activity_description': 'Processing of employee personal data for HR purposes',
            'purpose_of_processing': 'Employment management and payroll',
            'legal_basis': 'Contract',
            'data_categories': ['Personal identifiers', 'Employment data', 'Financial data'],
            'data_subjects': ['Employees', 'Job applicants'],
            'automated_decision_making': False,
            'dpia_required': True,
            'dpia_completed': False
        }
        
        # Act
        activity = DataProcessingActivity(**activity_data)
        test_db.add(activity)
        test_db.commit()
        test_db.refresh(activity)
        
        # Assert
        assert activity.id is not None
        assert activity.activity_name == 'Employee Data Processing'
        assert activity.organization_id == sample_organization.id
        assert activity.dpia_required is True
        assert activity.dpia_completed is False
    
    def test_audit_logging_integration(self, test_db, sample_organization, sample_users):
        """Test audit logging across enterprise operations."""
        # Arrange
        compliance_service = ComplianceService(test_db)
        
        # Perform various operations that should generate audit logs
        req_data = {
            'organization_id': sample_organization.id,
            'framework': ComplianceFramework.HIPAA,
            'requirement_id': 'HIPAA-164.312',
            'title': 'Administrative safeguards',
            'description': 'Implement administrative safeguards',
            'risk_level': RiskLevel.HIGH
        }
        
        requirement = compliance_service.create_compliance_requirement(req_data)
        
        # Check audit logs
        audit_logs = test_db.query(AuditLog).filter(
            AuditLog.organization_id == sample_organization.id
        ).all()
        
        # Assert
        assert len(audit_logs) > 0
        
        # Find the compliance requirement creation log
        creation_log = next(
            (log for log in audit_logs 
             if log.event_description.startswith("Created compliance requirement")),
            None
        )
        
        assert creation_log is not None
        assert creation_log.compliance_relevant is True
        assert creation_log.resource_type == "compliance_requirement"
    
    def test_enterprise_dashboard_data_aggregation(self, test_db, sample_organization, sample_users):
        """Test enterprise dashboard data aggregation."""
        # Arrange
        from services.enterprise_service import EnterpriseService
        enterprise_service = EnterpriseService(test_db)
        
        # Act
        dashboard_data = enterprise_service.get_organization_dashboard_data(sample_organization.id)
        
        # Assert
        assert dashboard_data is not None
        assert 'organization' in dashboard_data
        assert 'license_usage' in dashboard_data
        assert 'user_summary' in dashboard_data
        assert dashboard_data['organization']['id'] == sample_organization.id
    
    def test_performance_under_load(self, test_db, sample_organization):
        """Test performance under simulated load."""
        # Arrange
        compliance_service = ComplianceService(test_db)
        
        # Create multiple compliance requirements
        requirements = []
        for i in range(50):
            req_data = {
                'organization_id': sample_organization.id,
                'framework': ComplianceFramework.GDPR,
                'requirement_id': f'GDPR-Test-{i:03d}',
                'title': f'Test Requirement {i}',
                'description': f'Test requirement description {i}',
                'risk_level': RiskLevel.MEDIUM
            }
            
            req = compliance_service.create_compliance_requirement(req_data)
            requirements.append(req)
        
        # Measure performance of bulk operations
        import time
        
        start_time = time.time()
        
        # Generate report with many requirements
        report = compliance_service.generate_gdpr_report(
            sample_organization.id,
            datetime.now() - timedelta(days=30),
            datetime.now()
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Assert
        assert len(requirements) == 50
        assert report is not None
        assert processing_time < 10.0  # Should complete within 10 seconds
        assert len(report.compliant_requirements) + len(report.non_compliant_requirements) + len(report.partially_compliant_requirements) == 50
