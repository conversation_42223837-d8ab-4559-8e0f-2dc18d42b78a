"""
Unit tests for Skills Assessment API endpoints
Feature 1.1: Skills Vector Representation & Scoring - Unit Tests Phase
"""
import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from fastapi.testclient import TestClient
from fastapi import HTTPException, status

# Import the functions we're testing
from api.endpoints.skills_assessment import (
    calculate_weighted_skill_score,
    calculate_domain_scores,
    SKILL_LEVELS,
    CONFIDENCE_WEIGHTS,
    CYBERSECURITY_DOMAINS
)

class TestSkillScoringAlgorithms:
    """Test suite for skill scoring algorithms"""
    
    def test_skill_levels_mapping(self):
        """Test that skill levels are correctly mapped to numerical values"""
        assert SKILL_LEVELS['none'] == 0.0
        assert SKILL_LEVELS['basic'] == 0.25
        assert SKILL_LEVELS['intermediate'] == 0.5
        assert SKILL_LEVELS['advanced'] == 0.75
        assert SKILL_LEVELS['expert'] == 1.0
    
    def test_confidence_weights_mapping(self):
        """Test that confidence levels are correctly mapped to weights"""
        assert CONFIDENCE_WEIGHTS['very_confident'] == 1.0
        assert CONFIDENCE_WEIGHTS['confident'] == 0.8
        assert CONFIDENCE_WEIGHTS['somewhat_confident'] == 0.6
        assert CONFIDENCE_WEIGHTS['not_confident'] == 0.4
    
    def test_calculate_weighted_skill_score_basic(self):
        """Test basic skill score calculation without certifications"""
        result = calculate_weighted_skill_score('intermediate', 'confident')
        
        expected_raw = 0.5  # intermediate level
        expected_weighted = 0.5 * 0.8  # confident weight
        expected_final = expected_weighted  # no cert boost
        
        assert result['raw_score'] == expected_raw
        assert result['confidence_weighted_score'] == expected_weighted
        assert result['certification_boost'] == 0.0
        assert result['final_score'] == expected_final
    
    def test_calculate_weighted_skill_score_with_certifications(self):
        """Test skill score calculation with certification boost"""
        certifications = ['Security+', 'Network+']
        result = calculate_weighted_skill_score('advanced', 'very_confident', certifications)
        
        expected_raw = 0.75  # advanced level
        expected_weighted = 0.75 * 1.0  # very_confident weight
        expected_cert_boost = min(0.3, len(certifications) * 0.1)  # 0.2
        expected_final = min(1.0, expected_weighted + expected_cert_boost)  # 0.95
        
        assert result['raw_score'] == expected_raw
        assert result['confidence_weighted_score'] == expected_weighted
        assert result['certification_boost'] == expected_cert_boost
        assert result['final_score'] == expected_final
    
    def test_calculate_weighted_skill_score_max_cert_boost(self):
        """Test that certification boost is capped at 0.3"""
        certifications = ['Cert1', 'Cert2', 'Cert3', 'Cert4', 'Cert5']  # 5 certs
        result = calculate_weighted_skill_score('basic', 'not_confident', certifications)
        
        # Should cap at 0.3 even with 5 certifications
        assert result['certification_boost'] == 0.3
    
    def test_calculate_weighted_skill_score_final_score_cap(self):
        """Test that final score is capped at 1.0"""
        certifications = ['Cert1', 'Cert2', 'Cert3']
        result = calculate_weighted_skill_score('expert', 'very_confident', certifications)
        
        # expert (1.0) * very_confident (1.0) + cert_boost (0.3) = 1.3, but capped at 1.0
        assert result['final_score'] == 1.0
    
    def test_calculate_weighted_skill_score_edge_cases(self):
        """Test edge cases for skill score calculation"""
        # Test with empty certifications list
        result = calculate_weighted_skill_score('none', 'not_confident', [])
        assert result['certification_boost'] == 0.0
        assert result['final_score'] == 0.0  # 0.0 * 0.4 + 0.0 = 0.0
        
        # Test with None certifications
        result = calculate_weighted_skill_score('expert', 'very_confident', None)
        assert result['certification_boost'] == 0.0
        assert result['final_score'] == 1.0  # 1.0 * 1.0 + 0.0 = 1.0

class TestDomainScoring:
    """Test suite for domain-level scoring calculations"""
    
    def test_calculate_domain_scores_basic(self):
        """Test basic domain score calculation"""
        skill_scores = {
            'network_protocols': 0.8,
            'firewall_configuration': 0.6,
            'vpn_technologies': 0.7,
            'incident_response': 0.9,
            'siem_soar': 0.5
        }
        
        domain_scores = calculate_domain_scores(skill_scores)
        
        # Check that all domains are present
        assert len(domain_scores) == len(CYBERSECURITY_DOMAINS)
        
        # Check communication_network_security domain
        comm_net_skills = CYBERSECURITY_DOMAINS['communication_network_security']['skills']
        relevant_scores = [skill_scores[skill] for skill in comm_net_skills if skill in skill_scores]
        expected_comm_net_score = sum(relevant_scores) / len(relevant_scores) if relevant_scores else 0.0
        
        assert domain_scores['communication_network_security'] == expected_comm_net_score
    
    def test_calculate_domain_scores_no_skills(self):
        """Test domain score calculation with no relevant skills"""
        skill_scores = {}
        domain_scores = calculate_domain_scores(skill_scores)
        
        # All domains should have score 0.0
        for domain_score in domain_scores.values():
            assert domain_score == 0.0
    
    def test_calculate_domain_scores_partial_coverage(self):
        """Test domain score calculation with partial skill coverage"""
        # Only include skills from one domain
        skill_scores = {
            'network_protocols': 0.8,
            'firewall_configuration': 0.6
        }
        
        domain_scores = calculate_domain_scores(skill_scores)
        
        # Communication network security should have a score
        assert domain_scores['communication_network_security'] > 0.0
        
        # Other domains should have score 0.0 (no relevant skills)
        assert domain_scores['identity_access_management'] == 0.0
        assert domain_scores['security_architecture_engineering'] == 0.0
    
    def test_calculate_domain_scores_all_domains_covered(self):
        """Test domain score calculation with skills from all domains"""
        # Include at least one skill from each domain
        skill_scores = {}
        for domain_id, domain_info in CYBERSECURITY_DOMAINS.items():
            # Add first skill from each domain
            first_skill = domain_info['skills'][0]
            skill_scores[first_skill] = 0.7
        
        domain_scores = calculate_domain_scores(skill_scores)
        
        # All domains should have score 0.7
        for domain_score in domain_scores.values():
            assert domain_score == 0.7

class TestCybersecurityFramework:
    """Test suite for the 8-domain cybersecurity framework"""
    
    def test_framework_structure(self):
        """Test that the cybersecurity framework has correct structure"""
        assert len(CYBERSECURITY_DOMAINS) == 8
        
        expected_domains = [
            'communication_network_security',
            'identity_access_management',
            'security_architecture_engineering',
            'asset_security',
            'security_risk_management',
            'security_assessment_testing',
            'software_security',
            'security_operations'
        ]
        
        for domain in expected_domains:
            assert domain in CYBERSECURITY_DOMAINS
    
    def test_domain_skills_not_empty(self):
        """Test that each domain has skills defined"""
        for domain_id, domain_info in CYBERSECURITY_DOMAINS.items():
            assert 'name' in domain_info
            assert 'skills' in domain_info
            assert len(domain_info['skills']) > 0
            assert isinstance(domain_info['skills'], list)
    
    def test_skill_names_are_strings(self):
        """Test that all skill names are strings"""
        for domain_info in CYBERSECURITY_DOMAINS.values():
            for skill in domain_info['skills']:
                assert isinstance(skill, str)
                assert len(skill) > 0
    
    def test_no_duplicate_skills_across_domains(self):
        """Test that skills are not duplicated across domains"""
        all_skills = []
        for domain_info in CYBERSECURITY_DOMAINS.values():
            all_skills.extend(domain_info['skills'])
        
        # Check for duplicates
        unique_skills = set(all_skills)
        assert len(all_skills) == len(unique_skills), "Found duplicate skills across domains"

class TestSkillAssessmentValidation:
    """Test suite for skill assessment input validation"""
    
    def test_valid_skill_levels(self):
        """Test validation of skill levels"""
        valid_levels = ['none', 'basic', 'intermediate', 'advanced', 'expert']
        
        for level in valid_levels:
            assert level in SKILL_LEVELS
    
    def test_valid_confidence_levels(self):
        """Test validation of confidence levels"""
        valid_confidence = ['very_confident', 'confident', 'somewhat_confident', 'not_confident']
        
        for confidence in valid_confidence:
            assert confidence in CONFIDENCE_WEIGHTS
    
    def test_skill_level_ordering(self):
        """Test that skill levels are properly ordered"""
        levels = ['none', 'basic', 'intermediate', 'advanced', 'expert']
        scores = [SKILL_LEVELS[level] for level in levels]
        
        # Scores should be in ascending order
        assert scores == sorted(scores)
    
    def test_confidence_weight_ordering(self):
        """Test that confidence weights are properly ordered"""
        confidence_levels = ['not_confident', 'somewhat_confident', 'confident', 'very_confident']
        weights = [CONFIDENCE_WEIGHTS[level] for level in confidence_levels]
        
        # Weights should be in ascending order
        assert weights == sorted(weights)

class TestErrorHandling:
    """Test suite for error handling in skill assessment"""
    
    def test_invalid_skill_level(self):
        """Test handling of invalid skill levels"""
        with pytest.raises(KeyError):
            calculate_weighted_skill_score('invalid_level', 'confident')
    
    def test_invalid_confidence_level(self):
        """Test handling of invalid confidence levels"""
        with pytest.raises(KeyError):
            calculate_weighted_skill_score('intermediate', 'invalid_confidence')
    
    def test_empty_skill_scores_for_domain_calculation(self):
        """Test domain calculation with empty skill scores"""
        result = calculate_domain_scores({})
        
        # Should return all domains with 0.0 scores
        assert len(result) == len(CYBERSECURITY_DOMAINS)
        for score in result.values():
            assert score == 0.0

class TestPerformanceAndScalability:
    """Test suite for performance and scalability considerations"""
    
    def test_large_skill_set_performance(self):
        """Test performance with large skill sets"""
        # Create a large skill set
        large_skill_scores = {}
        for domain_info in CYBERSECURITY_DOMAINS.values():
            for skill in domain_info['skills']:
                large_skill_scores[skill] = 0.5
        
        # This should complete quickly
        import time
        start_time = time.time()
        result = calculate_domain_scores(large_skill_scores)
        end_time = time.time()
        
        # Should complete in under 1 second
        assert (end_time - start_time) < 1.0
        assert len(result) == len(CYBERSECURITY_DOMAINS)
    
    def test_many_certifications_performance(self):
        """Test performance with many certifications"""
        # Create a large certification list
        many_certs = [f"Cert_{i}" for i in range(100)]
        
        import time
        start_time = time.time()
        result = calculate_weighted_skill_score('advanced', 'confident', many_certs)
        end_time = time.time()
        
        # Should complete quickly and cap certification boost
        assert (end_time - start_time) < 1.0
        assert result['certification_boost'] == 0.3  # Should be capped

class TestMathematicalProperties:
    """Test suite for mathematical properties of scoring algorithms"""
    
    def test_score_monotonicity(self):
        """Test that higher skill levels result in higher scores"""
        levels = ['none', 'basic', 'intermediate', 'advanced', 'expert']
        scores = []
        
        for level in levels:
            result = calculate_weighted_skill_score(level, 'confident')
            scores.append(result['final_score'])
        
        # Scores should be non-decreasing
        for i in range(1, len(scores)):
            assert scores[i] >= scores[i-1]
    
    def test_confidence_monotonicity(self):
        """Test that higher confidence results in higher scores"""
        confidence_levels = ['not_confident', 'somewhat_confident', 'confident', 'very_confident']
        scores = []
        
        for confidence in confidence_levels:
            result = calculate_weighted_skill_score('intermediate', confidence)
            scores.append(result['final_score'])
        
        # Scores should be non-decreasing
        for i in range(1, len(scores)):
            assert scores[i] >= scores[i-1]
    
    def test_certification_boost_additivity(self):
        """Test that certification boost increases with more certifications"""
        cert_counts = [0, 1, 2, 3]
        boosts = []
        
        for count in cert_counts:
            certs = [f"Cert_{i}" for i in range(count)]
            result = calculate_weighted_skill_score('intermediate', 'confident', certs)
            boosts.append(result['certification_boost'])
        
        # Boosts should be non-decreasing (until cap)
        for i in range(1, len(boosts)):
            assert boosts[i] >= boosts[i-1]

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
