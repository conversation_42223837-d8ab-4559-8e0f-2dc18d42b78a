[tool:pytest]
# Pytest configuration for CertPathFinder

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    api: API tests
    service: Service layer tests
    model: Model tests
    auth: Authentication tests
    security: Security tests
    performance: Performance tests
    smoke: Smoke tests
    regression: Regression tests
    
    # Feature markers
    certification: Certification feature tests
    cost_calculator: Cost calculator feature tests
    user_profile: User profile feature tests
    career_paths: Career paths feature tests
    ai_assistant: AI assistant feature tests
    enterprise: Enterprise feature tests
    job_search: Job search feature tests
    admin: Admin feature tests

# Test timeout
timeout = 300

# Ignore warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*

# Coverage configuration
[coverage:run]
source = .
omit = 
    */tests/*
    */venv/*
    */env/*
    */.venv/*
    */migrations/*
    */alembic/*
    setup.py
    conftest.py
    */conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
