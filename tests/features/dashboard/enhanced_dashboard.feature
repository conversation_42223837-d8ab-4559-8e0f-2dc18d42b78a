Feature: Enhanced Dashboard Overview
  As a logged-in user
  I want to view my personalized dashboard
  So that I can track my certification progress and access quick actions

  Background:
    Given I am logged in as a user with certifications and learning paths
    And I navigate to the dashboard page

  @smoke @dashboard @playwright
  Scenario: Dashboard overview display with comprehensive data
    Given I am on the dashboard page
    Then I should see a personalized welcome message with my name
    And I should see quick statistics cards showing:
      | metric                | value |
      | Completed This Month  | 2     |
      | Study Streak         | 7 days |
      | Next Exam           | 15 days |
      | Total Study Hours   | 45     |
    And I should see my learning paths with progress indicators
    And I should see recent activity timeline
    And I should see recommended certifications

  @dashboard @progress @playwright
  Scenario: Learning path progress visualization
    Given I have learning paths with different progress levels
    When I view the learning paths section
    Then I should see progress bars for each path
    And progress percentages should be displayed accurately
    And I should see path descriptions and creation dates
    When I hover over a progress bar
    Then I should see detailed progress information

  @dashboard @notifications @playwright
  Scenario: Notification panel functionality
    Given I have unread notifications
    When I click the notifications button
    Then the notification panel should slide in from the right
    And I should see a list of my notifications
    And unread notifications should be highlighted
    When I click on "Unread" filter
    Then only unread notifications should be displayed
    When I mark a notification as read
    Then it should be visually updated
    And the unread count should decrease

  @dashboard @quick-actions @playwright
  Scenario: Quick action modal interactions
    Given I am on the dashboard page
    When I click the "Quick Action" button
    Then the quick action modal should open
    And I should see available action types
    When I select "Add to Learning Path" action
    And I choose a certification and learning path
    And I confirm the action
    Then the certification should be added to the path
    And I should see a success notification
    And the dashboard data should be refreshed

  @dashboard @certifications @playwright
  Scenario: Recommended certifications interaction
    Given I see recommended certifications
    When I click "Add to Path" on a certification
    Then the quick action modal should open with the certification pre-selected
    And I should see certification details in the modal
    When I select a learning path and confirm
    Then the action should be processed successfully
    And the recommendation should be updated

  @dashboard @responsive @playwright
  Scenario: Mobile dashboard experience
    Given I am using a mobile device
    When I view the dashboard
    Then the layout should be optimized for mobile
    And quick stats should stack vertically
    And the welcome section should be responsive
    And notification panel should adapt to mobile width
    When I open the quick action modal on mobile
    Then it should be properly sized for mobile screens

  @dashboard @animations @playwright
  Scenario: Dashboard animations and interactions
    Given I am on the dashboard page
    When the page loads
    Then elements should animate in with staggered timing
    When I hover over quick stats cards
    Then they should have smooth hover effects
    When I interact with progress bars
    Then they should provide visual feedback
    And animations should respect reduced motion preferences

  @dashboard @accessibility @playwright
  Scenario: Dashboard accessibility compliance
    Given I am on the dashboard page
    When I navigate using only the keyboard
    Then I should be able to access all interactive elements
    And focus indicators should be clearly visible
    And screen readers should announce dynamic content changes
    When notifications are updated
    Then screen readers should be notified
    And all images should have appropriate alt text

  @dashboard @performance @playwright
  Scenario: Dashboard performance optimization
    Given I am on the dashboard page
    When I measure the page load time
    Then the dashboard should load within 2.5 seconds
    And API calls should be optimized and batched
    When I perform quick actions
    Then they should respond within 500ms
    And the UI should remain responsive during data loading

  @dashboard @real-time @playwright
  Scenario: Real-time data updates
    Given I am on the dashboard page
    When my progress is updated from another session
    Then the dashboard should reflect the changes
    And notifications should appear for new achievements
    When I complete a certification elsewhere
    Then my statistics should update automatically
    And progress bars should animate to new values

  @dashboard @error-handling @playwright
  Scenario: Dashboard error handling and recovery
    Given I am on the dashboard page
    When the API returns an error
    Then I should see an appropriate error message
    And I should have options to retry or refresh
    When the network connection is lost
    Then I should see a network error indicator
    And the dashboard should attempt to reconnect
    When connection is restored
    Then data should be automatically refreshed

  @dashboard @empty-states @playwright
  Scenario: Dashboard empty states
    Given I am a new user with no data
    When I view the dashboard
    Then I should see helpful empty state messages
    And I should see calls-to-action to get started
    When I have no learning paths
    Then I should see "Create Your First Path" button
    When I have no recent activity
    Then I should see encouraging messages to start learning

  @dashboard @data-refresh @playwright
  Scenario: Dashboard data refresh and synchronization
    Given I am on the dashboard page
    When I perform an action that changes my data
    Then the dashboard should automatically refresh
    And updated statistics should be displayed
    When I manually refresh the page
    Then all data should be current and consistent
    And loading states should be shown during refresh

  @dashboard @customization @playwright
  Scenario: Dashboard personalization features
    Given I am on the dashboard page
    When I interact with different sections
    Then my preferences should be remembered
    And the layout should adapt to my usage patterns
    When I frequently use certain quick actions
    Then they should be prioritized in the interface
    And my dashboard should feel personalized to my journey

  @dashboard @integration @playwright
  Scenario: Dashboard integration with other features
    Given I am on the dashboard page
    When I click on a learning path
    Then I should navigate to the detailed path view
    When I click on a certification recommendation
    Then I should see detailed certification information
    When I click on recent activity items
    Then I should navigate to relevant progress pages
    And all navigation should maintain context

  @dashboard @notifications-advanced @playwright
  Scenario: Advanced notification features
    Given I have various types of notifications
    When I view the notification panel
    Then notifications should be grouped by type
    And I should see appropriate icons for each type
    When I have achievement notifications
    Then they should be visually distinct and celebratory
    When I have reminder notifications
    Then they should include actionable buttons
    And I should be able to snooze or dismiss reminders

  @dashboard @quick-actions-advanced @playwright
  Scenario: Advanced quick action workflows
    Given I am on the dashboard page
    When I use the "Start Study Session" quick action
    Then I should be able to set study duration
    And the session should be tracked in real-time
    When I use the "Set Goal" quick action
    Then I should be able to define SMART goals
    And progress toward goals should be tracked
    When I use the "Mark Complete" quick action
    Then I should see celebration animations
    And my statistics should update immediately

  @dashboard @analytics @playwright
  Scenario: Dashboard analytics and insights
    Given I have been using the platform for some time
    When I view my dashboard
    Then I should see insights about my learning patterns
    And I should see recommendations based on my progress
    When I view my study streak
    Then I should see motivational messages
    And I should see tips for maintaining consistency
    When I view completion statistics
    Then I should see trends and projections

  @dashboard @social @playwright
  Scenario: Social features integration
    Given I have connected with other learners
    When I view my dashboard
    Then I should see updates from my learning network
    And I should see leaderboards or progress comparisons
    When I achieve a milestone
    Then I should have options to share my progress
    And I should see encouragement from my network

  @dashboard @gamification @playwright
  Scenario: Gamification elements
    Given I am on the dashboard page
    When I view my progress
    Then I should see achievement badges and levels
    And I should see points or experience indicators
    When I complete learning milestones
    Then I should see celebration animations
    And I should unlock new features or content
    When I maintain study streaks
    Then I should see streak rewards and bonuses

  @dashboard @cross-browser @playwright
  Scenario: Cross-browser dashboard compatibility
    Given I am on the dashboard page
    When I use different browsers
    Then the dashboard should function consistently
    And all animations should work properly
    And data should persist across browser sessions
    And performance should be optimized for each browser

  @dashboard @integration-testing @playwright
  Scenario: Full dashboard integration testing
    Given I am on the dashboard page
    When I interact with all dashboard features
    Then all API integrations should work correctly
    And data should flow seamlessly between components
    And the user experience should be cohesive
    And all features should work together harmoniously
    And the dashboard should serve as an effective hub for my learning journey
