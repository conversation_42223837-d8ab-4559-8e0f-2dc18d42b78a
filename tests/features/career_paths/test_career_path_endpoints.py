"""Test career path endpoints"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
from api.app import app
from models.certification import Certification, Organization
from database import get_db
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def get_test_db():
    """Override database dependency for testing"""
    try:
        db = next(get_db())
        yield db
    finally:
        db.close()

# Override the database dependency
app.dependency_overrides[get_db] = get_test_db

def test_career_path_different_experience_levels(db_session: Session):
    """Test career path generation with different experience levels"""
    current_time = datetime.utcnow()

    logger.info("Setting up test data...")
    # Create organization first since it's required for certification
    org = Organization(
        name="Test Org",
        country="United States",  # Required country field
        description="Test organization",
        created_at=current_time
    )
    db_session.add(org)
    db_session.commit()
    logger.info(f"Created test organization with ID: {org.id}")

    # Create test certification with all required fields
    cert = Certification(
        name="Test Cert",
        category="Security",  # Required category field
        domain="Network Security",
        level="Entry Level",
        difficulty=1,
        cost=500,
        description="Test certification",
        prerequisites=None,
        validity_period=12,  # In months
        exam_code="TEST-001",
        organization_id=org.id,  # Required foreign key
        created_at=current_time,
        focus="Network",  # Added required focus field
        custom_hours=40
    )
    db_session.add(cert)
    db_session.commit()
    logger.info(f"Created test certification with ID: {cert.id}")

    # Create test client with proper database session
    client = TestClient(app)

    # Test with beginner
    request_data = {
        "interests": ["Network Security"],
        "years_experience": 0,
        "current_role": "Student",
        "target_role": "Security Analyst",
        "learning_style": "Mixed",
        "study_hours": 10,
        "completed_certifications": []  # Added empty list for completed certs
    }
    logger.info(f"Sending career path request: {request_data}")

    response = client.post("/api/v1/career-path", json=request_data)

    # Log response details
    logger.info(f"Response status: {response.status_code}")
    logger.info(f"Response headers: {response.headers}")
    if response.status_code != 200:
        logger.error(f"Error response body: {response.text}")
    else:
        logger.info(f"Success response body: {response.json()}")

    assert response.status_code == 200, f"Expected 200 OK but got {response.status_code}: {response.text}"
    assert "career_path" in response.json()

    # Test with expert
    expert_request = {
        "interests": ["Network Security"],
        "years_experience": 15,
        "current_role": "Security Manager", 
        "target_role": "CISO",
        "learning_style": "Mixed",
        "study_hours": 5,
        "completed_certifications": []  # Added empty list for completed certs
    }
    response = client.post("/api/v1/career-path", json=expert_request)
    assert response.status_code == 200
    assert "career_path" in response.json()

def test_career_path_input_validation():
    """Test input validation for career path endpoint"""
    client = TestClient(app)
    # Test missing required fields
    response = client.post("/api/v1/career-path", json={
        "interests": ["Network Security"]
        # Missing other required fields
    })
    assert response.status_code == 422  # Validation error