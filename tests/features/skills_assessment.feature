# Skills Assessment Feature
# Feature 1.1: Skills Vector Representation & Scoring - BEHAVE Tests Phase

Feature: Skills Assessment and Vector Generation
    As a cybersecurity professional
    I want to assess my skills across the 8 cybersecurity domains
    So that I can get personalized career recommendations and identify skill gaps

    Background:
        Given the skills assessment system is available
        And I am a registered user
        And the 8-domain cybersecurity framework is loaded

    @skills-assessment @api
    Scenario: Complete skills assessment with basic proficiency
        Given I start a new skills assessment
        When I assess my "network_protocols" skill as "intermediate" with "confident" confidence
        And I assess my "incident_response" skill as "basic" with "somewhat_confident" confidence
        And I assess my "threat_modeling" skill as "advanced" with "very_confident" confidence
        And I submit the skills assessment
        Then the assessment should be processed successfully
        And I should receive a skills vector with 3 assessed skills
        And the domain scores should be calculated correctly
        And my strongest domain should be identified

    @skills-assessment @scoring
    Scenario: Skills scoring with certification boost
        Given I start a new skills assessment
        And I have the following certifications:
            | name        | provider | year_obtained |
            | Security+   | CompTIA  | 2023          |
            | Network+    | CompTIA  | 2022          |
        When I assess my "network_protocols" skill as "intermediate" with "confident" confidence
        And I submit the skills assessment
        Then the skill score should include certification boost
        And the final score should be higher than the confidence-weighted score
        And the certification boost should not exceed 30%

    @skills-assessment @domains
    Scenario: Assessment across multiple cybersecurity domains
        Given I start a new skills assessment
        When I assess skills in the following domains:
            | domain                              | skill_name              | level        | confidence         |
            | communication_network_security      | network_protocols       | advanced     | very_confident     |
            | identity_access_management          | authentication_mechanisms| intermediate | confident          |
            | security_architecture_engineering   | threat_modeling         | basic        | somewhat_confident |
            | security_operations                 | incident_response       | advanced     | confident          |
        And I submit the skills assessment
        Then domain scores should be calculated for all 8 domains
        And assessed domains should have scores greater than 0
        And unassessed domains should have scores of 0
        And the overall assessment completeness should be calculated

    @skills-assessment @validation
    Scenario: Skills assessment input validation
        Given I start a new skills assessment
        When I try to assess a skill with invalid level "super_expert"
        Then I should receive a validation error
        And the error should indicate valid skill levels
        When I try to assess a skill with invalid confidence "extremely_confident"
        Then I should receive a validation error
        And the error should indicate valid confidence levels

    @skills-assessment @profile
    Scenario: User skill profile creation and updates
        Given I have no existing skill profile
        When I complete a skills assessment with 5 skills
        And I submit the skills assessment
        Then a new skill profile should be created for me
        And the profile should contain my skill vector
        And the profile should contain domain scores
        And the profile should track the assessment timestamp
        When I complete another assessment 1 month later
        And I submit the updated assessment
        Then my skill profile should be updated
        And the system should track skill growth over time

    @skills-assessment @edge-cases
    Scenario: Assessment with minimal skills
        Given I start a new skills assessment
        When I assess only 1 skill as "basic" with "not_confident" confidence
        And I submit the skills assessment
        Then the assessment should be processed successfully
        And the assessment completeness should be very low
        And I should receive recommendations to assess more skills

    @skills-assessment @edge-cases
    Scenario: Assessment with maximum skills
        Given I start a new skills assessment
        When I assess all 72 skills in the framework
        And I submit the skills assessment
        Then the assessment should be processed successfully
        And the assessment completeness should be 100%
        And all 8 domains should have calculated scores
        And the processing should complete within acceptable time limits

    @skills-assessment @confidence-weighting
    Scenario Outline: Confidence weighting affects final scores
        Given I start a new skills assessment
        When I assess my "network_protocols" skill as "intermediate" with "<confidence>" confidence
        And I submit the skills assessment
        Then the confidence-weighted score should be "<expected_weighted_score>"
        And the final score should reflect the confidence weighting

        Examples:
            | confidence           | expected_weighted_score |
            | very_confident       | 0.5                     |
            | confident            | 0.4                     |
            | somewhat_confident   | 0.3                     |
            | not_confident        | 0.2                     |

    @skills-assessment @api-integration
    Scenario: Skills assessment API integration
        Given the skills assessment API is available
        When I send a POST request to "/api/v1/skills/assess" with valid assessment data
        Then I should receive a 200 status code
        And the response should contain an assessment_id
        And the response should contain skill_scores array
        And the response should contain domain_scores object
        And the response should contain overall_profile data
        When I send a GET request to "/api/v1/skills/profile/{user_id}"
        Then I should receive my current skill profile
        And the profile should match the latest assessment

    @skills-assessment @performance
    Scenario: Skills assessment performance requirements
        Given I start a new skills assessment with 20 skills
        When I submit the skills assessment
        Then the assessment should be processed within 2 seconds
        And the API response time should be under 1 second
        And the database operations should complete efficiently

    @skills-assessment @error-handling
    Scenario: Skills assessment error handling
        Given I start a new skills assessment
        When the assessment API is temporarily unavailable
        And I try to submit the skills assessment
        Then I should receive an appropriate error message
        And the system should suggest retrying later
        When the API becomes available again
        And I retry the submission
        Then the assessment should be processed successfully

    @skills-assessment @data-persistence
    Scenario: Skills assessment data persistence
        Given I complete a skills assessment
        When I submit the assessment
        And the system restarts
        Then my assessment data should still be available
        And my skill profile should be intact
        And I should be able to retrieve my assessment history

    @skills-assessment @concurrent-users
    Scenario: Concurrent skills assessments
        Given multiple users are taking skills assessments simultaneously
        When 10 users submit assessments at the same time
        Then all assessments should be processed correctly
        And each user should receive their own unique assessment_id
        And there should be no data corruption or mixing between users
        And the system should maintain acceptable performance
