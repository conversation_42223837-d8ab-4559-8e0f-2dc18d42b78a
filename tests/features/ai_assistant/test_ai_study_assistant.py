"""Tests for AI Study Assistant functionality.

This module provides comprehensive tests for the on-device AI study assistant,
including recommendations, insights, adaptive paths, and question generation.
"""

import pytest
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from services.ai_study_assistant import OnDeviceAIStudyAssistant, StudyRecommendation, LearningInsight, AdaptivePath
from models.progress_tracking import StudySession, PracticeTestResult, LearningGoal, Achievement
from models.certification import Certification


class TestOnDeviceAIStudyAssistant:
    """Test cases for the AI Study Assistant."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def ai_assistant(self, mock_db):
        """Create an AI assistant instance for testing."""
        return OnDeviceAIStudyAssistant(mock_db)
    
    @pytest.fixture
    def sample_user_data(self):
        """Create sample user data for testing."""
        # Create mock study sessions
        sessions = []
        for i in range(10):
            session = Mock(spec=StudySession)
            session.user_id = "test_user"
            session.session_type = "reading" if i % 2 == 0 else "practice_test"
            session.started_at = datetime.now() - timedelta(days=i)
            session.duration_minutes = 60 + (i * 5)
            session.effectiveness_rating = 3 + (i % 3)
            session.focus_rating = 3 + (i % 3)
            session.progress_before = 50 + (i * 2)
            session.progress_after = 55 + (i * 2)
            sessions.append(session)
        
        # Create mock test results
        test_results = []
        for i in range(5):
            result = Mock(spec=PracticeTestResult)
            result.user_id = "test_user"
            result.certification_id = 1
            result.percentage = 70 + (i * 3)
            result.created_at = datetime.now() - timedelta(days=i * 2)
            result.domain_scores = {
                "Network Security": 75 + i,
                "Cryptography": 65 + i,
                "Risk Management": 80 + i
            }
            test_results.append(result)
        
        # Create mock goals
        goals = []
        goal = Mock(spec=LearningGoal)
        goal.user_id = "test_user"
        goal.title = "Pass Security+ Certification"
        goal.status = "active"
        goal.progress_percentage = 75.0
        goals.append(goal)
        
        # Create mock achievements
        achievements = []
        achievement = Mock(spec=Achievement)
        achievement.user_id = "test_user"
        achievement.title = "Study Warrior"
        achievement.is_earned = True
        achievements.append(achievement)
        
        return {
            'sessions': sessions,
            'test_results': test_results,
            'goals': goals,
            'achievements': achievements,
            'user_id': 'test_user'
        }
    
    def test_generate_personalized_recommendations(self, ai_assistant, sample_user_data):
        """Test personalized recommendation generation."""
        # Mock the _gather_user_data method
        ai_assistant._gather_user_data = Mock(return_value=sample_user_data)
        
        recommendations = ai_assistant.generate_personalized_recommendations("test_user", "general")
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # Check recommendation structure
        for rec in recommendations:
            assert isinstance(rec, StudyRecommendation)
            assert hasattr(rec, 'type')
            assert hasattr(rec, 'priority')
            assert hasattr(rec, 'title')
            assert hasattr(rec, 'description')
            assert hasattr(rec, 'confidence_score')
            assert 1 <= rec.priority <= 5
            assert 0.0 <= rec.confidence_score <= 1.0
    
    def test_analyze_learning_patterns(self, ai_assistant, sample_user_data):
        """Test learning pattern analysis."""
        # Mock the _gather_user_data method
        ai_assistant._gather_user_data = Mock(return_value=sample_user_data)
        
        insights = ai_assistant.analyze_learning_patterns("test_user")
        
        assert isinstance(insights, list)
        
        # Check insight structure
        for insight in insights:
            assert isinstance(insight, LearningInsight)
            assert hasattr(insight, 'category')
            assert hasattr(insight, 'insight_type')
            assert hasattr(insight, 'title')
            assert hasattr(insight, 'description')
            assert hasattr(insight, 'impact_score')
            assert 0.0 <= insight.impact_score <= 1.0
    
    def test_generate_adaptive_learning_path(self, ai_assistant, sample_user_data):
        """Test adaptive learning path generation."""
        # Mock dependencies
        ai_assistant._gather_user_data = Mock(return_value=sample_user_data)
        
        # Create mock certification
        mock_cert = Mock(spec=Certification)
        mock_cert.id = 1
        mock_cert.name = "Security+"
        
        ai_assistant.db.query.return_value.filter.return_value.first.return_value = mock_cert
        
        target_date = datetime.now() + timedelta(days=90)
        path = ai_assistant.generate_adaptive_learning_path("test_user", 1, target_date)
        
        assert isinstance(path, AdaptivePath)
        assert path.path_id is not None
        assert path.name is not None
        assert path.estimated_duration_weeks > 0
        assert 0.0 <= path.success_probability <= 1.0
        assert isinstance(path.topics, list)
        assert len(path.topics) > 0
    
    def test_generate_practice_questions(self, ai_assistant):
        """Test practice question generation."""
        questions = ai_assistant.generate_practice_questions("Network Security", "intermediate", 3)
        
        assert isinstance(questions, list)
        assert len(questions) == 3
        
        # Check question structure
        for question in questions:
            assert 'id' in question
            assert 'question' in question
            assert 'type' in question
            assert 'difficulty' in question
            assert 'topic' in question
            assert 'options' in question
            assert 'correct_answer' in question
            assert 'explanation' in question
    
    def test_provide_study_feedback(self, ai_assistant, sample_user_data):
        """Test study session feedback generation."""
        # Mock the _gather_user_data method
        ai_assistant._gather_user_data = Mock(return_value=sample_user_data)
        
        session_data = {
            'session_type': 'reading',
            'duration_minutes': 60,
            'focus_rating': 4,
            'effectiveness_rating': 4,
            'confidence_level': 3,
            'progress_before': 70.0,
            'progress_after': 80.0
        }
        
        feedback = ai_assistant.provide_study_feedback("test_user", session_data)
        
        assert isinstance(feedback, dict)
        assert 'overall_rating' in feedback
        assert 'strengths' in feedback
        assert 'improvements' in feedback
        assert 'next_session_suggestions' in feedback
        assert 'technique_recommendations' in feedback
        assert 'motivation_boost' in feedback
        
        assert isinstance(feedback['strengths'], list)
        assert isinstance(feedback['improvements'], list)
        assert isinstance(feedback['next_session_suggestions'], list)
        assert isinstance(feedback['technique_recommendations'], list)
        assert isinstance(feedback['motivation_boost'], str)
    
    def test_assess_knowledge_level(self, ai_assistant, sample_user_data):
        """Test knowledge level assessment."""
        # Create mock certification
        mock_cert = Mock(spec=Certification)
        mock_cert.id = 1
        mock_cert.name = "Security+"
        
        knowledge_level = ai_assistant._assess_knowledge_level(sample_user_data, mock_cert)
        
        assert isinstance(knowledge_level, float)
        assert 0.0 <= knowledge_level <= 1.0
    
    def test_identify_weak_areas(self, ai_assistant, sample_user_data):
        """Test weak area identification."""
        # Create mock certification
        mock_cert = Mock(spec=Certification)
        mock_cert.id = 1
        mock_cert.name = "Security+"
        
        weak_areas = ai_assistant._identify_weak_areas(sample_user_data, mock_cert)
        
        assert isinstance(weak_areas, list)
        # Should identify areas with scores below 75%
        assert len(weak_areas) >= 0
    
    def test_identify_strong_areas(self, ai_assistant, sample_user_data):
        """Test strong area identification."""
        # Create mock certification
        mock_cert = Mock(spec=Certification)
        mock_cert.id = 1
        mock_cert.name = "Security+"
        
        strong_areas = ai_assistant._identify_strong_areas(sample_user_data, mock_cert)
        
        assert isinstance(strong_areas, list)
        # Should identify areas with scores above 85%
        assert len(strong_areas) >= 0
    
    def test_identify_learning_style(self, ai_assistant, sample_user_data):
        """Test learning style identification."""
        learning_style = ai_assistant._identify_learning_style(sample_user_data)
        
        assert isinstance(learning_style, str)
        assert learning_style in ['visual', 'auditory', 'kinesthetic', 'analytical', 'mixed']
    
    def test_calculate_optimal_session_length(self, ai_assistant, sample_user_data):
        """Test optimal session length calculation."""
        optimal_length = ai_assistant._calculate_optimal_session_length(sample_user_data)
        
        assert isinstance(optimal_length, int)
        assert optimal_length > 0
    
    def test_analyze_performance_patterns(self, ai_assistant, sample_user_data):
        """Test performance pattern analysis."""
        insights = ai_assistant._analyze_performance_patterns(sample_user_data)
        
        assert isinstance(insights, list)
        
        for insight in insights:
            assert isinstance(insight, LearningInsight)
            assert insight.category == 'performance'
    
    def test_analyze_time_patterns(self, ai_assistant, sample_user_data):
        """Test time pattern analysis."""
        insights = ai_assistant._analyze_time_patterns(sample_user_data)
        
        assert isinstance(insights, list)
        
        for insight in insights:
            assert isinstance(insight, LearningInsight)
            assert insight.category in ['efficiency', 'optimization']
    
    def test_analyze_consistency_patterns(self, ai_assistant, sample_user_data):
        """Test consistency pattern analysis."""
        insights = ai_assistant._analyze_consistency_patterns(sample_user_data)
        
        assert isinstance(insights, list)
        
        for insight in insights:
            assert isinstance(insight, LearningInsight)
            assert insight.category == 'consistency'
    
    def test_analyze_efficiency_patterns(self, ai_assistant, sample_user_data):
        """Test efficiency pattern analysis."""
        insights = ai_assistant._analyze_efficiency_patterns(sample_user_data)
        
        assert isinstance(insights, list)
        
        for insight in insights:
            assert isinstance(insight, LearningInsight)
            assert insight.category == 'efficiency'
    
    def test_estimate_success_probability(self, ai_assistant, sample_user_data):
        """Test success probability estimation."""
        # Create mock certification
        mock_cert = Mock(spec=Certification)
        mock_cert.id = 1
        mock_cert.name = "Security+"
        
        success_prob = ai_assistant._estimate_success_probability(sample_user_data, mock_cert)
        
        assert isinstance(success_prob, float)
        assert 0.0 <= success_prob <= 1.0
    
    def test_estimate_duration(self, ai_assistant, sample_user_data):
        """Test duration estimation."""
        # Create mock certification
        mock_cert = Mock(spec=Certification)
        mock_cert.id = 1
        mock_cert.name = "Security+"
        
        target_date = datetime.now() + timedelta(days=90)
        duration = ai_assistant._estimate_duration(sample_user_data, mock_cert, target_date)
        
        assert isinstance(duration, int)
        assert duration > 0
        assert duration <= 13  # Should not exceed available weeks
    
    def test_knowledge_base_initialization(self, ai_assistant):
        """Test knowledge base initialization."""
        assert hasattr(ai_assistant, 'knowledge_base')
        assert 'study_techniques' in ai_assistant.knowledge_base
        assert 'common_weak_areas' in ai_assistant.knowledge_base
        assert 'study_tips' in ai_assistant.knowledge_base
        
        # Check study techniques structure
        techniques = ai_assistant.knowledge_base['study_techniques']
        for technique_name, technique_info in techniques.items():
            assert 'description' in technique_info
            assert 'effectiveness' in technique_info
            assert 'difficulty_levels' in technique_info
            assert 'time_requirements' in technique_info
    
    def test_model_training(self, ai_assistant):
        """Test model training functionality."""
        # Mock model saving
        with patch.object(ai_assistant, '_save_model') as mock_save:
            ai_assistant.train_models("test_user")
            
            # Should save all three models
            assert mock_save.call_count == 3
    
    def test_recommendation_priority_sorting(self, ai_assistant, sample_user_data):
        """Test that recommendations are sorted by priority and confidence."""
        # Mock the _gather_user_data method
        ai_assistant._gather_user_data = Mock(return_value=sample_user_data)
        
        recommendations = ai_assistant.generate_personalized_recommendations("test_user", "general")
        
        # Check that recommendations are sorted by priority (descending) and confidence
        for i in range(len(recommendations) - 1):
            current = recommendations[i]
            next_rec = recommendations[i + 1]
            
            # Higher priority should come first, or if same priority, higher confidence
            assert (current.priority > next_rec.priority or 
                   (current.priority == next_rec.priority and 
                    current.confidence_score >= next_rec.confidence_score))
    
    def test_empty_user_data_handling(self, ai_assistant):
        """Test handling of empty user data."""
        empty_data = {
            'sessions': [],
            'test_results': [],
            'goals': [],
            'achievements': [],
            'user_id': 'test_user'
        }
        
        ai_assistant._gather_user_data = Mock(return_value=empty_data)
        
        # Should not crash with empty data
        recommendations = ai_assistant.generate_personalized_recommendations("test_user", "general")
        insights = ai_assistant.analyze_learning_patterns("test_user")
        
        assert isinstance(recommendations, list)
        assert isinstance(insights, list)
    
    def test_confidence_score_bounds(self, ai_assistant, sample_user_data):
        """Test that confidence scores are within valid bounds."""
        ai_assistant._gather_user_data = Mock(return_value=sample_user_data)
        
        recommendations = ai_assistant.generate_personalized_recommendations("test_user", "general")
        
        for rec in recommendations:
            assert 0.0 <= rec.confidence_score <= 1.0
    
    def test_impact_score_bounds(self, ai_assistant, sample_user_data):
        """Test that impact scores are within valid bounds."""
        ai_assistant._gather_user_data = Mock(return_value=sample_user_data)
        
        insights = ai_assistant.analyze_learning_patterns("test_user")
        
        for insight in insights:
            assert 0.0 <= insight.impact_score <= 1.0


if __name__ == "__main__":
    pytest.main([__file__])
