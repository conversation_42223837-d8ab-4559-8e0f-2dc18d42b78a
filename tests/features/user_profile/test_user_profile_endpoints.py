"""Test user profile API endpoints"""
import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json
import logging
from api.app import app
from models.user_experience import UserExperience
from database import get_db
from tests.conftest import TestingSessionLocal

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def override_get_db():
    """Override database dependency for testing"""
    session = TestingSessionLocal()
    try:
        logger.debug("Creating test database session for API endpoint")
        yield session
    finally:
        logger.debug("Closing test database session from API endpoint")
        session.close()

# Configure app to use test database
app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def sample_profile_data():
    """Sample profile data for testing"""
    return {
        'years_experience': 5,
        'user_role': 'Security Analyst',
        'desired_role': 'Security Engineer',
        'expertise_areas': ['Network Security', 'Cloud Security'],
        'preferred_learning_style': 'Visual',
        'study_time_available': 10,
        'tutorial_completed': False
    }

def test_create_user_profile(db_session: Session, sample_profile_data):
    """Test creating a new user profile"""
    client = TestClient(app)
    user_id = "test_create_user"

    # Test successful creation
    response = client.post(
        f"/api/v1/user/profile?user_id={user_id}",
        json=sample_profile_data
    )
    assert response.status_code == 201
    data = response.json()
    assert data['user_id'] == user_id
    assert data['years_experience'] == sample_profile_data['years_experience']
    assert data['expertise_areas'] == sample_profile_data['expertise_areas']

    # Test duplicate creation
    response = client.post(
        f"/api/v1/user/profile?user_id={user_id}",
        json=sample_profile_data
    )
    assert response.status_code == 409

def test_get_user_profile(db_session: Session, sample_profile_data):
    """Test getting user profile"""
    client = TestClient(app)
    user_id = "test_get_user"

    # Create test profile
    profile = UserExperience(user_id=user_id, **sample_profile_data)
    db_session.add(profile)
    db_session.commit()

    # Test successful retrieval
    response = client.get(f"/api/v1/user/profile?user_id={user_id}")
    assert response.status_code == 200
    data = response.json()
    assert data['user_id'] == user_id
    assert data['years_experience'] == sample_profile_data['years_experience']

    # Test profile not found
    response = client.get("/api/v1/user/profile?user_id=nonexistent")
    assert response.status_code == 404

def test_update_user_profile(db_session: Session, sample_profile_data):
    """Test updating user profile"""
    client = TestClient(app)
    user_id = "test_update_user"

    # Create test profile
    profile = UserExperience(user_id=user_id, **sample_profile_data)
    db_session.add(profile)
    db_session.commit()

    # Test partial update
    update_data = {
        'years_experience': 6,
        'user_role': 'Senior Security Analyst'
    }
    response = client.put(
        f"/api/v1/user/profile?user_id={user_id}",
        json=update_data
    )
    assert response.status_code == 200
    data = response.json()
    assert data['years_experience'] == 6
    assert data['user_role'] == 'Senior Security Analyst'
    assert data['expertise_areas'] == sample_profile_data['expertise_areas']

    # Test update nonexistent profile
    response = client.put(
        "/api/v1/user/profile?user_id=nonexistent",
        json=update_data
    )
    assert response.status_code == 404

def test_sync_user_profile(db_session: Session, sample_profile_data):
    """Test profile synchronization"""
    client = TestClient(app)
    user_id = "test_sync_user"

    # Create test profile
    profile = UserExperience(user_id=user_id, **sample_profile_data)
    db_session.add(profile)
    db_session.commit()

    # Get current timestamp
    now = datetime.utcnow()
    past = now - timedelta(hours=1)

    # Test sync with no conflicts
    sync_data = {
        'last_sync': past.isoformat(),
        'changes': ['years_experience', 'user_role'],
        'force': False
    }
    response = client.post(
        f"/api/v1/user/profile/sync?user_id={user_id}",
        json=sync_data
    )
    assert response.status_code == 200

    # Test sync conflict detection
    profile.last_updated = now
    db_session.commit()

    sync_data['last_sync'] = (now - timedelta(minutes=30)).isoformat()
    response = client.post(
        f"/api/v1/user/profile/sync?user_id={user_id}",
        json=sync_data
    )
    assert response.status_code == 409

    # Test force sync
    sync_data['force'] = True
    response = client.post(
        f"/api/v1/user/profile/sync?user_id={user_id}",
        json=sync_data
    )
    assert response.status_code == 200

def test_delete_user_profile(db_session: Session, sample_profile_data):
    """Test soft deleting user profile"""
    client = TestClient(app)
    user_id = "test_delete_user"

    # Create test profile
    profile = UserExperience(user_id=user_id, **sample_profile_data)
    db_session.add(profile)
    db_session.commit()

    # Test successful deletion
    response = client.delete(f"/api/v1/user/profile?user_id={user_id}")
    assert response.status_code == 204

    # Verify soft delete
    profile = db_session.query(UserExperience).filter_by(user_id=user_id).first()
    assert profile.is_deleted
    assert profile.deleted_at is not None

    # Test delete nonexistent profile
    response = client.delete("/api/v1/user/profile?user_id=nonexistent")
    assert response.status_code == 404

def test_input_validation(db_session: Session):
    """Test input validation for all endpoints"""
    client = TestClient(app)
    user_id = "test_validation"

    # Test invalid years_experience
    invalid_data = {
        'years_experience': -1,
        'user_role': 'Security Analyst',
        'desired_role': 'Security Engineer',
        'expertise_areas': ['Network Security'],
        'preferred_learning_style': 'Visual',
        'study_time_available': 10
    }
    response = client.post(
        f"/api/v1/user/profile?user_id={user_id}",
        json=invalid_data
    )
    assert response.status_code == 422

    # Test invalid learning style
    invalid_data = {
        'years_experience': 5,
        'user_role': 'Security Analyst',
        'desired_role': 'Security Engineer',
        'expertise_areas': ['Network Security'],
        'preferred_learning_style': 'Invalid',
        'study_time_available': 10
    }
    response = client.post(
        f"/api/v1/user/profile?user_id={user_id}",
        json=invalid_data
    )
    assert response.status_code == 422

    # Test empty user_id
    response = client.get("/api/v1/user/profile?user_id=")
    assert response.status_code == 422

def test_error_handling(db_session: Session):
    """Test error handling scenarios"""
    client = TestClient(app)

    # Test missing required fields
    invalid_data = {
        'years_experience': 5  # Missing other required fields
    }
    response = client.post(
        "/api/v1/user/profile?user_id=test_user",
        json=invalid_data
    )
    assert response.status_code == 422

    # Test invalid JSON
    response = client.post(
        "/api/v1/user/profile?user_id=test_user",
        data="invalid json"
    )
    assert response.status_code == 422

    # Test method not allowed
    response = client.patch("/api/v1/user/profile?user_id=test_user")
    assert response.status_code == 405