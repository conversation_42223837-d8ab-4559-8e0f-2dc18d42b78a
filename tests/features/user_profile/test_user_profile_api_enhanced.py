"""Enhanced API tests for user profile endpoints"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
import json

from api.app import app


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def sample_user_profile():
    """Sample user profile data"""
    return {
        "id": "user_123",
        "email": "<EMAIL>",
        "name": "Test User",
        "title": "Security Analyst",
        "company": "Test Corp",
        "location": "New York, NY",
        "bio": "Cybersecurity professional with 5 years experience",
        "years_experience": 5,
        "experience_level": "mid_level",
        "current_role": "Security Analyst",
        "desired_role": "Senior Security Engineer",
        "expertise_areas": ["Network Security", "Incident Response"],
        "learning_style": "visual",
        "study_time_available": 10,
        "email_notifications": True,
        "push_notifications": False,
        "privacy_level": "public",
        "tutorial_completed": True,
        "onboarding_step": 5,
        "is_active": True,
        "is_verified": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def sample_user_stats():
    """Sample user statistics"""
    return {
        "certifications_completed": 3,
        "certifications_in_progress": 2,
        "total_study_hours": 150,
        "average_score": 85.5,
        "streak_days": 15,
        "achievements_earned": 8,
        "rank": 42,
        "percentile": 78
    }


class TestUserProfileAPI:
    """Test suite for user profile API endpoints"""

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_get_user_profile_success(self, client, sample_user_profile):
        """Test successful user profile retrieval"""
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.get_profile.return_value = sample_user_profile
            
            # Execute
            response = client.get("/api/v1/user/profile")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == "user_123"
            assert data["email"] == "<EMAIL>"
            assert data["name"] == "Test User"

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_get_user_profile_not_found(self, client):
        """Test user profile retrieval when not found"""
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.get_profile.return_value = None
            
            # Execute
            response = client.get("/api/v1/user/profile")
            
            # Verify
            assert response.status_code == 404

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_create_user_profile_success(self, client, sample_user_profile):
        """Test successful user profile creation"""
        create_data = {
            "email": "<EMAIL>",
            "name": "New User",
            "title": "Junior Analyst",
            "years_experience": 1,
            "experience_level": "entry_level",
            "expertise_areas": ["Security Fundamentals"],
            "learning_style": "auditory",
            "study_time_available": 5
        }
        
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.create_profile.return_value = sample_user_profile
            
            # Execute
            response = client.post("/api/v1/user/profile", json=create_data)
            
            # Verify
            assert response.status_code == 201
            data = response.json()
            assert "id" in data

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_create_user_profile_validation_error(self, client):
        """Test user profile creation with validation errors"""
        invalid_data = {
            "email": "invalid-email",  # Invalid email format
            "name": "",  # Empty name
            "years_experience": -1,  # Negative experience
            "study_time_available": 200  # Too many hours
        }
        
        # Execute
        response = client.post("/api/v1/user/profile", json=invalid_data)
        
        # Verify
        assert response.status_code == 422

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_update_user_profile_success(self, client, sample_user_profile):
        """Test successful user profile update"""
        update_data = {
            "title": "Senior Security Analyst",
            "years_experience": 6,
            "expertise_areas": ["Network Security", "Incident Response", "Threat Hunting"],
            "study_time_available": 15
        }
        
        with patch('api.v1.user_profile.UserService') as mock_service:
            updated_profile = sample_user_profile.copy()
            updated_profile.update(update_data)
            mock_service.return_value.update_profile.return_value = updated_profile
            
            # Execute
            response = client.put("/api/v1/user/profile", json=update_data)
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert data["title"] == "Senior Security Analyst"
            assert data["years_experience"] == 6

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_update_user_profile_partial(self, client, sample_user_profile):
        """Test partial user profile update"""
        update_data = {
            "bio": "Updated bio with new information"
        }
        
        with patch('api.v1.user_profile.UserService') as mock_service:
            updated_profile = sample_user_profile.copy()
            updated_profile["bio"] = update_data["bio"]
            mock_service.return_value.update_profile.return_value = updated_profile
            
            # Execute
            response = client.patch("/api/v1/user/profile", json=update_data)
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert data["bio"] == "Updated bio with new information"

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_delete_user_profile_success(self, client):
        """Test successful user profile deletion"""
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.delete_profile.return_value = True
            
            # Execute
            response = client.delete("/api/v1/user/profile")
            
            # Verify
            assert response.status_code == 204

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_get_user_statistics(self, client, sample_user_stats):
        """Test user statistics retrieval"""
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.get_stats.return_value = sample_user_stats
            
            # Execute
            response = client.get("/api/v1/user/stats")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert data["certifications_completed"] == 3
            assert data["total_study_hours"] == 150
            assert data["rank"] == 42

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_get_user_achievements(self, client):
        """Test user achievements retrieval"""
        mock_achievements = [
            {
                "id": "first_cert",
                "name": "First Certification",
                "description": "Completed your first certification",
                "icon": "trophy",
                "category": "certification",
                "points": 100,
                "rarity": "common",
                "earned_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": "study_streak",
                "name": "Study Streak",
                "description": "Studied for 7 consecutive days",
                "icon": "fire",
                "category": "study",
                "points": 50,
                "rarity": "uncommon",
                "earned_at": "2024-01-05T00:00:00Z"
            }
        ]
        
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.get_achievements.return_value = mock_achievements
            
            # Execute
            response = client.get("/api/v1/user/achievements")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["name"] == "First Certification"

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_get_user_activity(self, client):
        """Test user activity feed retrieval"""
        mock_activities = [
            {
                "id": "activity_1",
                "type": "certification_completed",
                "description": "Completed CISSP certification",
                "metadata": {"certification_id": 1, "score": 85},
                "timestamp": "2024-01-01T00:00:00Z"
            },
            {
                "id": "activity_2",
                "type": "study_session",
                "description": "Studied for 2 hours",
                "metadata": {"duration": 120, "topic": "Network Security"},
                "timestamp": "2024-01-02T00:00:00Z"
            }
        ]
        
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.get_activity.return_value = mock_activities
            
            # Execute
            response = client.get("/api/v1/user/activity")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["type"] == "certification_completed"

    @pytest.mark.api
    @pytest.mark.user_profile
    def test_update_user_preferences(self, client):
        """Test user preferences update"""
        preferences_data = {
            "theme": "dark",
            "language": "en",
            "timezone": "America/New_York",
            "email_frequency": "weekly",
            "dashboard_layout": "grid",
            "items_per_page": 25
        }
        
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.update_preferences.return_value = preferences_data
            
            # Execute
            response = client.put("/api/v1/user/preferences", json=preferences_data)
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert data["theme"] == "dark"
            assert data["items_per_page"] == 25

    @pytest.mark.api
    @pytest.mark.user_profile
    @pytest.mark.security
    def test_profile_access_control(self, client):
        """Test that users can only access their own profile"""
        # This test assumes proper authentication middleware
        with patch('api.v1.user_profile.get_current_user') as mock_user:
            mock_user.return_value = {"id": "user_123"}
            
            # Execute
            response = client.get("/api/v1/user/profile")
            
            # Verify - should only return current user's profile
            assert response.status_code in [200, 401, 403]

    @pytest.mark.api
    @pytest.mark.user_profile
    @pytest.mark.performance
    def test_profile_response_time(self, client, sample_user_profile):
        """Test profile endpoint response time"""
        with patch('api.v1.user_profile.UserService') as mock_service:
            mock_service.return_value.get_profile.return_value = sample_user_profile
            
            # Execute
            import time
            start_time = time.time()
            response = client.get("/api/v1/user/profile")
            end_time = time.time()
            
            # Verify
            assert response.status_code == 200
            assert (end_time - start_time) < 1.0  # Should respond within 1 second
