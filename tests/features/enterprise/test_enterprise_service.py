"""Tests for Enterprise Service functionality.

This module provides comprehensive tests for the enterprise service,
including organization management, user administration, and analytics.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from services.enterprise_service import EnterpriseService
from models.enterprise import (
    EnterpriseOrganization, Department, EnterpriseUser, UserLicense, 
    OrganizationAnalytics, OrganizationType, SubscriptionTier, UserRole, LicenseStatus
)


class TestEnterpriseService:
    """Test cases for the Enterprise Service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def enterprise_service(self, mock_db):
        """Create an enterprise service instance for testing."""
        return EnterpriseService(mock_db)
    
    @pytest.fixture
    def sample_organization_data(self):
        """Create sample organization data for testing."""
        return {
            'name': 'Test University',
            'display_name': 'Test University - IT Department',
            'organization_type': OrganizationType.EDUCATIONAL,
            'industry': 'Education',
            'size_category': 'large',
            'employee_count': 5000,
            'primary_contact_name': '<PERSON>',
            'primary_contact_email': '<EMAIL>',
            'domain': 'test.edu',
            'website': 'https://www.test.edu',
            'description': 'Leading university in technology education',
            'subscription_tier': SubscriptionTier.PROFESSIONAL,
            'license_count': 100
        }
    
    @pytest.fixture
    def sample_user_data(self):
        """Create sample user data for testing."""
        return {
            'user_id': 'john.smith',
            'organization_id': 1,
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Smith',
            'display_name': 'John Smith',
            'role': UserRole.LEARNER,
            'employee_id': 'EMP001',
            'job_title': 'IT Specialist',
            'department_id': 1
        }
    
    @pytest.fixture
    def sample_department_data(self):
        """Create sample department data for testing."""
        return {
            'organization_id': 1,
            'name': 'Information Technology',
            'description': 'IT department responsible for technology infrastructure',
            'code': 'IT',
            'budget_allocated': 50000.0
        }
    
    def test_create_organization(self, enterprise_service, sample_organization_data):
        """Test organization creation."""
        # Mock the database operations
        mock_org = Mock(spec=EnterpriseOrganization)
        mock_org.id = 1
        mock_org.name = sample_organization_data['name']
        mock_org.to_dict.return_value = sample_organization_data
        
        enterprise_service.db.add = Mock()
        enterprise_service.db.commit = Mock()
        enterprise_service.db.refresh = Mock()
        
        # Mock the organization creation
        with patch('models.enterprise.EnterpriseOrganization', return_value=mock_org):
            result = enterprise_service.create_organization(sample_organization_data)
        
        assert result == mock_org
        enterprise_service.db.add.assert_called_once()
        enterprise_service.db.commit.assert_called_once()
        enterprise_service.db.refresh.assert_called_once_with(mock_org)
    
    def test_get_organization(self, enterprise_service):
        """Test getting organization by ID."""
        mock_org = Mock(spec=EnterpriseOrganization)
        mock_org.id = 1
        mock_org.name = 'Test University'
        
        enterprise_service.db.query.return_value.filter.return_value.first.return_value = mock_org
        
        result = enterprise_service.get_organization(1)
        
        assert result == mock_org
        enterprise_service.db.query.assert_called_with(EnterpriseOrganization)
    
    def test_get_organization_not_found(self, enterprise_service):
        """Test getting non-existent organization."""
        enterprise_service.db.query.return_value.filter.return_value.first.return_value = None
        
        result = enterprise_service.get_organization(999)
        
        assert result is None
    
    def test_list_organizations(self, enterprise_service):
        """Test listing organizations with pagination."""
        mock_orgs = [Mock(spec=EnterpriseOrganization) for _ in range(3)]
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 3
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = mock_orgs
        
        enterprise_service.db.query.return_value = mock_query
        
        organizations, total_count = enterprise_service.list_organizations(page=1, page_size=10)
        
        assert len(organizations) == 3
        assert total_count == 3
        enterprise_service.db.query.assert_called_with(EnterpriseOrganization)
    
    def test_list_organizations_with_filters(self, enterprise_service):
        """Test listing organizations with filters."""
        mock_orgs = [Mock(spec=EnterpriseOrganization)]
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 1
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = mock_orgs
        
        enterprise_service.db.query.return_value = mock_query
        
        filters = {
            'organization_type': OrganizationType.EDUCATIONAL,
            'subscription_tier': SubscriptionTier.PROFESSIONAL,
            'is_active': True,
            'search': 'test'
        }
        
        organizations, total_count = enterprise_service.list_organizations(
            page=1, page_size=10, filters=filters
        )
        
        assert len(organizations) == 1
        assert total_count == 1
    
    def test_update_organization(self, enterprise_service):
        """Test updating organization."""
        mock_org = Mock(spec=EnterpriseOrganization)
        mock_org.id = 1
        mock_org.name = 'Test University'
        
        enterprise_service.get_organization = Mock(return_value=mock_org)
        enterprise_service.db.commit = Mock()
        enterprise_service.db.refresh = Mock()
        
        update_data = {'name': 'Updated University'}
        result = enterprise_service.update_organization(1, update_data)
        
        assert result == mock_org
        assert mock_org.name == 'Updated University'
        enterprise_service.db.commit.assert_called_once()
        enterprise_service.db.refresh.assert_called_once_with(mock_org)
    
    def test_delete_organization(self, enterprise_service):
        """Test soft deleting organization."""
        mock_org = Mock(spec=EnterpriseOrganization)
        mock_org.id = 1
        
        enterprise_service.get_organization = Mock(return_value=mock_org)
        enterprise_service.db.commit = Mock()
        
        result = enterprise_service.delete_organization(1)
        
        assert result is True
        assert mock_org.deleted_at is not None
        assert mock_org.is_active is False
        enterprise_service.db.commit.assert_called_once()
    
    def test_create_user(self, enterprise_service, sample_user_data):
        """Test user creation."""
        mock_user = Mock(spec=EnterpriseUser)
        mock_user.user_id = sample_user_data['user_id']
        mock_user.organization_id = sample_user_data['organization_id']
        
        mock_org = Mock(spec=EnterpriseOrganization)
        mock_org.licenses_used = 5
        
        enterprise_service.db.add = Mock()
        enterprise_service.db.commit = Mock()
        enterprise_service.db.refresh = Mock()
        enterprise_service.get_organization = Mock(return_value=mock_org)
        
        with patch('models.enterprise.EnterpriseUser', return_value=mock_user):
            result = enterprise_service.create_user(sample_user_data)
        
        assert result == mock_user
        assert mock_org.licenses_used == 6  # Should increment
        enterprise_service.db.add.assert_called_once()
        enterprise_service.db.commit.assert_called_once()
        enterprise_service.db.refresh.assert_called_once_with(mock_user)
    
    def test_get_user(self, enterprise_service):
        """Test getting user by ID."""
        mock_user = Mock(spec=EnterpriseUser)
        mock_user.user_id = 'john.smith'
        
        enterprise_service.db.query.return_value.filter.return_value.first.return_value = mock_user
        
        result = enterprise_service.get_user('john.smith')
        
        assert result == mock_user
        enterprise_service.db.query.assert_called_with(EnterpriseUser)
    
    def test_list_organization_users(self, enterprise_service):
        """Test listing users in an organization."""
        mock_users = [Mock(spec=EnterpriseUser) for _ in range(2)]
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 2
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = mock_users
        
        enterprise_service.db.query.return_value = mock_query
        
        users, total_count = enterprise_service.list_organization_users(1)
        
        assert len(users) == 2
        assert total_count == 2
        enterprise_service.db.query.assert_called_with(EnterpriseUser)
    
    def test_create_department(self, enterprise_service, sample_department_data):
        """Test department creation."""
        mock_dept = Mock(spec=Department)
        mock_dept.id = 1
        mock_dept.name = sample_department_data['name']
        
        enterprise_service.db.add = Mock()
        enterprise_service.db.commit = Mock()
        enterprise_service.db.refresh = Mock()
        
        with patch('models.enterprise.Department', return_value=mock_dept):
            result = enterprise_service.create_department(sample_department_data)
        
        assert result == mock_dept
        enterprise_service.db.add.assert_called_once()
        enterprise_service.db.commit.assert_called_once()
        enterprise_service.db.refresh.assert_called_once_with(mock_dept)
    
    def test_list_organization_departments(self, enterprise_service):
        """Test listing departments in an organization."""
        mock_depts = [Mock(spec=Department) for _ in range(2)]
        
        enterprise_service.db.query.return_value.filter.return_value.order_by.return_value.all.return_value = mock_depts
        
        result = enterprise_service.list_organization_departments(1)
        
        assert len(result) == 2
        enterprise_service.db.query.assert_called_with(Department)
    
    def test_assign_license_new(self, enterprise_service):
        """Test assigning a new license to a user."""
        mock_user = Mock(spec=EnterpriseUser)
        mock_user.organization_id = 1
        
        mock_license = Mock(spec=UserLicense)
        mock_license.user_id = 'john.smith'
        mock_license.license_type = 'professional'
        
        enterprise_service.get_user = Mock(return_value=mock_user)
        enterprise_service.db.query.return_value.filter.return_value.first.return_value = None
        enterprise_service.db.add = Mock()
        enterprise_service.db.commit = Mock()
        enterprise_service.db.refresh = Mock()
        
        with patch('models.enterprise.UserLicense', return_value=mock_license):
            result = enterprise_service.assign_license('john.smith', 'professional')
        
        assert result == mock_license
        enterprise_service.db.add.assert_called_once()
        enterprise_service.db.commit.assert_called_once()
        enterprise_service.db.refresh.assert_called_once_with(mock_license)
    
    def test_assign_license_existing(self, enterprise_service):
        """Test updating an existing license."""
        mock_user = Mock(spec=EnterpriseUser)
        mock_user.organization_id = 1
        
        mock_license = Mock(spec=UserLicense)
        mock_license.user_id = 'john.smith'
        mock_license.license_type = 'basic'
        
        enterprise_service.get_user = Mock(return_value=mock_user)
        enterprise_service.db.query.return_value.filter.return_value.first.return_value = mock_license
        enterprise_service.db.commit = Mock()
        enterprise_service.db.refresh = Mock()
        
        result = enterprise_service.assign_license('john.smith', 'professional')
        
        assert result == mock_license
        assert mock_license.license_type == 'professional'
        assert mock_license.status == LicenseStatus.ACTIVE
        enterprise_service.db.commit.assert_called_once()
        enterprise_service.db.refresh.assert_called_once_with(mock_license)
    
    def test_get_organization_license_usage(self, enterprise_service):
        """Test getting license usage statistics."""
        mock_org = Mock(spec=EnterpriseOrganization)
        mock_org.license_count = 100
        
        enterprise_service.get_organization = Mock(return_value=mock_org)
        
        # Mock license counts
        enterprise_service.db.query.return_value.filter.return_value.count.side_effect = [80, 5]  # active, expired
        
        # Mock license breakdown
        mock_breakdown = [
            Mock(license_type='basic', count=30),
            Mock(license_type='professional', count=50)
        ]
        enterprise_service.db.query.return_value.filter.return_value.group_by.return_value.all.return_value = mock_breakdown
        
        result = enterprise_service.get_organization_license_usage(1)
        
        assert result['total_licenses'] == 100
        assert result['active_licenses'] == 80
        assert result['expired_licenses'] == 5
        assert result['available_licenses'] == 20
        assert result['utilization_rate'] == 80.0
        assert result['license_breakdown']['basic'] == 30
        assert result['license_breakdown']['professional'] == 50
    
    def test_generate_organization_analytics(self, enterprise_service):
        """Test generating organization analytics."""
        mock_org = Mock(spec=EnterpriseOrganization)
        mock_org.id = 1
        
        enterprise_service.get_organization = Mock(return_value=mock_org)
        enterprise_service._calculate_user_metrics = Mock(return_value={'total_users': 100})
        enterprise_service._calculate_learning_metrics = Mock(return_value={'total_study_hours': 500})
        enterprise_service._calculate_engagement_metrics = Mock(return_value={'total_sessions': 200})
        enterprise_service._calculate_performance_metrics = Mock(return_value={'average_test_scores': 85})
        enterprise_service._calculate_department_breakdown = Mock(return_value={'IT': {'user_count': 50}})
        enterprise_service._calculate_certification_breakdown = Mock(return_value={'Security+': {'test_count': 25}})
        enterprise_service._store_analytics = Mock()
        
        result = enterprise_service.generate_organization_analytics(1)
        
        assert result['organization_id'] == 1
        assert result['period_type'] == 'monthly'
        assert 'user_metrics' in result
        assert 'learning_metrics' in result
        assert 'engagement_metrics' in result
        assert 'performance_metrics' in result
        assert 'department_breakdown' in result
        assert 'certification_breakdown' in result
        
        enterprise_service._store_analytics.assert_called_once()
    
    def test_get_organization_dashboard_data(self, enterprise_service):
        """Test getting comprehensive dashboard data."""
        mock_org = Mock(spec=EnterpriseOrganization)
        mock_org.to_dict.return_value = {'id': 1, 'name': 'Test Org'}
        
        enterprise_service.get_organization = Mock(return_value=mock_org)
        enterprise_service.get_organization_license_usage = Mock(return_value={'total_licenses': 100})
        enterprise_service.generate_organization_analytics = Mock(return_value={'period_type': 'monthly'})
        enterprise_service._get_department_summary = Mock(return_value={'total_departments': 5})
        enterprise_service._get_user_summary = Mock(return_value={'total_users': 100})
        enterprise_service._get_recent_activity = Mock(return_value=[])
        enterprise_service._get_organization_alerts = Mock(return_value=[])
        
        result = enterprise_service.get_organization_dashboard_data(1)
        
        assert 'organization' in result
        assert 'license_usage' in result
        assert 'recent_analytics' in result
        assert 'department_summary' in result
        assert 'user_summary' in result
        assert 'recent_activity' in result
        assert 'alerts' in result
    
    def test_generate_slug(self, enterprise_service):
        """Test slug generation from organization name."""
        result = enterprise_service._generate_slug("Test University & College")
        assert result == "test-university-college"
        
        result = enterprise_service._generate_slug("ABC Corp.")
        assert result == "abc-corp"
        
        result = enterprise_service._generate_slug("  Spaces  Around  ")
        assert result == "spaces-around"


if __name__ == "__main__":
    pytest.main([__file__])
