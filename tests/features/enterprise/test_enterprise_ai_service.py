"""Tests for Enterprise AI Service functionality.

This module provides comprehensive tests for the enterprise AI service,
including predictive analytics, optimization, insights, and model management.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from services.enterprise_ai_service import EnterpriseAIService
from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserLicense
from models.progress_tracking import StudySession, PracticeTestResult


class TestEnterpriseAIService:
    """Test cases for the Enterprise AI Service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def ai_service(self, mock_db):
        """Create an AI service instance for testing."""
        with patch('services.enterprise_ai_service.joblib.load') as mock_load:
            mock_load.side_effect = FileNotFoundError("No existing models")
            service = EnterpriseAIService(mock_db)
            return service
    
    @pytest.fixture
    def sample_user_features(self):
        """Create sample user features for testing."""
        return {
            'study_hours_per_week': 10,
            'consistency_score': 0.8,
            'previous_test_scores': [75, 82, 78],
            'engagement_level': 0.7,
            'account_age_days': 90
        }
    
    @pytest.fixture
    def sample_user_data(self):
        """Create sample user data for testing."""
        return {
            'user_id': 'test_user',
            'total_sessions': 25,
            'recent_sessions': 8,
            'avg_session_duration': 45,
            'last_login': datetime.now() - timedelta(days=2),
            'days_since_last_login': 2,
            'test_count': 5,
            'avg_test_score': 78.5,
            'account_age_days': 90
        }
    
    def test_initialization(self, ai_service):
        """Test AI service initialization."""
        assert ai_service.db is not None
        assert hasattr(ai_service, 'user_success_predictor')
        assert hasattr(ai_service, 'churn_predictor')
        assert hasattr(ai_service, 'performance_forecaster')
        assert hasattr(ai_service, 'anomaly_detector')
    
    def test_predict_user_success_probability(self, ai_service, sample_user_features):
        """Test user success probability prediction."""
        # Mock the predictor
        ai_service.user_success_predictor.predict = Mock(return_value=[0.82])
        ai_service._extract_user_features = Mock(return_value=[0.5, 0.7, 0.3, 0.8, 0.6])
        ai_service._calculate_prediction_confidence = Mock(return_value=0.89)
        ai_service._categorize_risk = Mock(return_value="Low Risk")
        ai_service._generate_success_insights = Mock(return_value=["Strong study patterns"])
        ai_service._generate_success_recommendations = Mock(return_value=["Continue current schedule"])
        
        result = ai_service.predict_user_success_probability(1, sample_user_features)
        
        assert 'success_probability' in result
        assert 'confidence_score' in result
        assert 'risk_level' in result
        assert 'insights' in result
        assert 'recommendations' in result
        assert result['success_probability'] == 0.82
        assert result['confidence_score'] == 0.89
        assert result['risk_level'] == "Low Risk"
    
    def test_predict_churn_risk(self, ai_service):
        """Test churn risk prediction."""
        # Mock dependencies
        ai_service._gather_user_engagement_data = Mock(return_value={
            'user_id': 'test_user',
            'days_since_last_login': 5,
            'recent_sessions': 3,
            'avg_session_duration': 30,
            'avg_test_score': 65,
            'account_age_days': 120
        })
        ai_service._extract_churn_features = Mock(return_value=[0.17, 0.3, 0.5, 0.65, 0.33])
        ai_service.churn_predictor.predict = Mock(return_value=[0.35])
        ai_service._estimate_time_to_churn = Mock(return_value=45)
        ai_service._categorize_churn_risk = Mock(return_value="Medium")
        ai_service._identify_churn_risk_factors = Mock(return_value=["Low engagement"])
        ai_service._recommend_churn_interventions = Mock(return_value=["Send re-engagement email"])
        ai_service._calculate_prediction_confidence = Mock(return_value=0.76)
        
        result = ai_service.predict_churn_risk(1, 'test_user')
        
        assert 'churn_risk' in result
        assert 'risk_category' in result
        assert 'estimated_time_to_churn_days' in result
        assert 'key_risk_factors' in result
        assert 'intervention_recommendations' in result
        assert 'confidence_score' in result
        assert result['churn_risk'] == 0.35
        assert result['risk_category'] == "Medium"
    
    def test_predict_churn_risk_no_data(self, ai_service):
        """Test churn risk prediction with no user data."""
        ai_service._gather_user_engagement_data = Mock(return_value=None)
        
        result = ai_service.predict_churn_risk(1, 'nonexistent_user')
        
        assert 'error' in result
        assert result['error'] == 'Insufficient data for churn prediction'
    
    def test_forecast_organization_performance(self, ai_service):
        """Test organization performance forecasting."""
        # Mock historical data
        historical_data = []
        for i in range(60):  # 60 days of data
            date = datetime.now() - timedelta(days=60-i)
            historical_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'active_users': 1000 + i * 2,
                'study_hours': 500 + i * 1.5,
                'completion_rate': 0.75 + (i * 0.001),
                'satisfaction_score': 4.2 + (i * 0.002)
            })
        
        ai_service._gather_organization_performance_data = Mock(return_value=historical_data)
        ai_service._forecast_metric = Mock(return_value={
            'metric': 'active_users',
            'forecast_values': [1120, 1122, 1124],
            'forecast_dates': ['2024-01-16', '2024-01-17', '2024-01-18'],
            'trend': 'increasing',
            'trend_strength': 2.0
        })
        ai_service._calculate_forecast_confidence = Mock(return_value=0.85)
        ai_service._generate_performance_insights = Mock(return_value=["Positive growth trend"])
        ai_service._generate_performance_recommendations = Mock(return_value=["Continue current strategy"])
        
        result = ai_service.forecast_organization_performance(1, 30)
        
        assert 'forecasts' in result
        assert 'confidence_score' in result
        assert 'insights' in result
        assert 'recommendations' in result
        assert 'forecast_period_days' in result
        assert result['forecast_period_days'] == 30
        assert result['confidence_score'] == 0.85
    
    def test_forecast_organization_performance_insufficient_data(self, ai_service):
        """Test forecasting with insufficient historical data."""
        # Mock insufficient data (less than 30 days)
        historical_data = []
        for i in range(10):  # Only 10 days of data
            date = datetime.now() - timedelta(days=10-i)
            historical_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'active_users': 1000 + i * 2
            })
        
        ai_service._gather_organization_performance_data = Mock(return_value=historical_data)
        
        result = ai_service.forecast_organization_performance(1, 30)
        
        assert 'error' in result
        assert result['error'] == 'Insufficient historical data for forecasting'
    
    def test_optimize_license_allocation(self, ai_service):
        """Test license allocation optimization."""
        # Mock license usage data
        usage_data = [
            {
                'license_id': 1,
                'user_id': 'user1',
                'license_type': 'professional',
                'status': 'active',
                'days_since_last_use': 5,
                'usage_frequency': 0.8
            },
            {
                'license_id': 2,
                'user_id': 'user2',
                'license_type': 'basic',
                'status': 'active',
                'days_since_last_use': 30,
                'usage_frequency': 0.2
            }
        ]
        
        ai_service._gather_license_usage_data = Mock(return_value=usage_data)
        ai_service._calculate_allocation_efficiency = Mock(return_value=0.73)
        ai_service._generate_license_optimizations = Mock(return_value=[
            {'type': 'reallocation', 'description': 'Reallocate underused licenses'}
        ])
        ai_service._calculate_potential_savings = Mock(return_value=12500.0)
        ai_service._predict_optimal_allocation = Mock(return_value={'professional': 15, 'basic': 25})
        ai_service._prioritize_optimizations = Mock(return_value=['high_impact', 'medium_impact'])
        ai_service._estimate_optimization_roi = Mock(return_value=3.4)
        
        result = ai_service.optimize_license_allocation(1)
        
        assert 'current_efficiency' in result
        assert 'optimization_recommendations' in result
        assert 'potential_cost_savings' in result
        assert 'optimal_allocation' in result
        assert 'implementation_priority' in result
        assert 'roi_estimate' in result
        assert result['current_efficiency'] == 0.73
        assert result['potential_cost_savings'] == 12500.0
    
    def test_optimize_license_allocation_no_data(self, ai_service):
        """Test license optimization with no usage data."""
        ai_service._gather_license_usage_data = Mock(return_value=[])
        
        result = ai_service.optimize_license_allocation(1)
        
        assert 'error' in result
        assert result['error'] == 'No license usage data available'
    
    def test_generate_automated_insights(self, ai_service):
        """Test automated insights generation."""
        # Mock insight generation methods
        ai_service._generate_performance_insights_ai = Mock(return_value=[
            {'category': 'performance', 'title': 'Performance Insight', 'impact_score': 0.8}
        ])
        ai_service._generate_user_behavior_insights = Mock(return_value=[
            {'category': 'behavior', 'title': 'Behavior Insight', 'impact_score': 0.7}
        ])
        ai_service._generate_efficiency_insights = Mock(return_value=[])
        ai_service._generate_cost_insights = Mock(return_value=[])
        ai_service._generate_predictive_insights = Mock(return_value=[])
        ai_service._detect_anomalies = Mock(return_value=[])
        
        ai_service._prioritize_insights = Mock(return_value=[
            {'category': 'performance', 'title': 'Performance Insight', 'impact_score': 0.8}
        ])
        ai_service._generate_action_recommendations = Mock(return_value=['Take action'])
        ai_service._create_insight_summary = Mock(return_value='Summary of insights')
        ai_service._calculate_insight_confidence = Mock(return_value={'performance': 0.85})
        
        result = ai_service.generate_automated_insights(1)
        
        assert 'insights' in result
        assert 'action_recommendations' in result
        assert 'insight_summary' in result
        assert 'confidence_scores' in result
        assert 'generated_at' in result
        assert result['insight_summary'] == 'Summary of insights'
    
    def test_detect_anomalies(self, ai_service):
        """Test anomaly detection."""
        # Mock anomaly detection data
        mock_data = [
            {'metric1': 100, 'metric2': 200},
            {'metric1': 105, 'metric2': 210},
            {'metric1': 500, 'metric2': 800}  # Anomalous
        ]
        
        ai_service._gather_anomaly_detection_data = Mock(return_value=mock_data)
        ai_service._prepare_anomaly_features = Mock(return_value=np.array([[100, 200], [105, 210], [500, 800]]))
        
        # Mock anomaly detector
        ai_service.anomaly_detector.decision_function = Mock(return_value=np.array([0.1, 0.2, -0.8]))
        ai_service.anomaly_detector.predict = Mock(return_value=np.array([1, 1, -1]))
        
        ai_service._categorize_anomaly_severity = Mock(return_value='high')
        ai_service._explain_anomalies = Mock(return_value=['Unusual spike in metrics'])
        ai_service._recommend_anomaly_actions = Mock(return_value=['Investigate immediately'])
        ai_service._calculate_anomaly_confidence = Mock(return_value=0.87)
        
        result = ai_service.detect_anomalies(1)
        
        assert 'anomalies_detected' in result
        assert 'anomalous_records' in result
        assert 'explanations' in result
        assert 'recommendations' in result
        assert 'detection_confidence' in result
        assert result['anomalies_detected'] == 1
        assert result['detection_confidence'] == 0.87
    
    def test_detect_anomalies_insufficient_data(self, ai_service):
        """Test anomaly detection with insufficient data."""
        # Mock insufficient data (less than 50 records)
        mock_data = [{'metric1': 100} for _ in range(10)]
        
        ai_service._gather_anomaly_detection_data = Mock(return_value=mock_data)
        
        result = ai_service.detect_anomalies(1)
        
        assert 'error' in result
        assert result['error'] == 'Insufficient data for anomaly detection'
    
    def test_segment_users(self, ai_service):
        """Test user segmentation."""
        # Mock user segmentation data
        user_data = [
            {'user_id': f'user_{i}', 'engagement': 0.5 + (i * 0.1), 'performance': 0.6 + (i * 0.05)}
            for i in range(20)
        ]
        
        ai_service._gather_user_segmentation_data = Mock(return_value=user_data)
        ai_service._prepare_segmentation_features = Mock(return_value=np.random.rand(20, 2))
        
        # Mock clustering
        ai_service.user_segmentation_model.fit_predict = Mock(return_value=np.array([0, 0, 1, 1, 2, 2] + [0] * 14))
        
        ai_service._analyze_user_segments = Mock(return_value={
            0: {'users': 16, 'avg_engagement': 0.7},
            1: {'users': 2, 'avg_engagement': 0.8},
            2: {'users': 2, 'avg_engagement': 0.9}
        })
        ai_service._create_segment_profiles = Mock(return_value=[
            {'segment_id': 0, 'name': 'Regular Users', 'user_count': 16}
        ])
        ai_service._recommend_segment_strategies = Mock(return_value={'0': ['Strategy for segment 0']})
        ai_service._evaluate_segmentation_quality = Mock(return_value=0.75)
        ai_service._create_user_segment_assignments = Mock(return_value={'user_0': 0, 'user_1': 0})
        
        result = ai_service.segment_users(1)
        
        assert 'total_segments' in result
        assert 'segment_profiles' in result
        assert 'segment_strategies' in result
        assert 'segmentation_quality' in result
        assert 'user_assignments' in result
        assert result['segmentation_quality'] == 0.75
    
    def test_segment_users_insufficient_users(self, ai_service):
        """Test user segmentation with insufficient users."""
        # Mock insufficient users (less than 10)
        user_data = [{'user_id': f'user_{i}'} for i in range(5)]
        
        ai_service._gather_user_segmentation_data = Mock(return_value=user_data)
        
        result = ai_service.segment_users(1)
        
        assert 'error' in result
        assert result['error'] == 'Insufficient users for segmentation'
    
    def test_train_enterprise_models(self, ai_service):
        """Test enterprise model training."""
        # Mock training methods
        ai_service._train_user_success_model = Mock(return_value={'status': 'success', 'accuracy': 0.85})
        ai_service._train_churn_model = Mock(return_value={'status': 'success', 'accuracy': 0.78})
        ai_service._train_performance_model = Mock(return_value={'status': 'success', 'accuracy': 0.82})
        ai_service._train_license_optimization_model = Mock(return_value={'status': 'success'})
        ai_service._train_resource_optimization_model = Mock(return_value={'status': 'success'})
        ai_service._train_segmentation_model = Mock(return_value={'status': 'success'})
        ai_service._train_anomaly_detection_model = Mock(return_value={'status': 'success'})
        
        ai_service._save_all_models = Mock()
        ai_service._calculate_training_success = Mock(return_value=0.95)
        
        result = ai_service.train_enterprise_models(1)
        
        assert 'training_results' in result
        assert 'overall_success_rate' in result
        assert 'models_updated' in result
        assert 'training_date' in result
        assert 'next_training_recommended' in result
        assert result['overall_success_rate'] == 0.95
        assert len(result['models_updated']) == 7  # All models
    
    def test_extract_user_features(self, ai_service, sample_user_features):
        """Test user feature extraction."""
        features = ai_service._extract_user_features(sample_user_features)
        
        assert isinstance(features, list)
        assert len(features) == 5  # Expected number of features
        assert all(isinstance(f, (int, float)) for f in features)
    
    def test_extract_churn_features(self, ai_service, sample_user_data):
        """Test churn feature extraction."""
        features = ai_service._extract_churn_features(sample_user_data)
        
        assert isinstance(features, list)
        assert len(features) == 5  # Expected number of features
        assert all(isinstance(f, (int, float)) for f in features)
        assert all(0 <= f <= 1 for f in features)  # Features should be normalized
    
    def test_categorize_risk(self, ai_service):
        """Test risk categorization."""
        assert ai_service._categorize_risk(0.9) == "Low Risk"
        assert ai_service._categorize_risk(0.7) == "Medium Risk"
        assert ai_service._categorize_risk(0.5) == "High Risk"
        assert ai_service._categorize_risk(0.2) == "Very High Risk"
    
    def test_categorize_churn_risk(self, ai_service):
        """Test churn risk categorization."""
        assert ai_service._categorize_churn_risk(0.9) == "Critical"
        assert ai_service._categorize_churn_risk(0.7) == "High"
        assert ai_service._categorize_churn_risk(0.5) == "Medium"
        assert ai_service._categorize_churn_risk(0.3) == "Low"
        assert ai_service._categorize_churn_risk(0.1) == "Very Low"
    
    def test_estimate_time_to_churn(self, ai_service):
        """Test time to churn estimation."""
        assert ai_service._estimate_time_to_churn([], 0.9) == 7
        assert ai_service._estimate_time_to_churn([], 0.7) == 30
        assert ai_service._estimate_time_to_churn([], 0.5) == 90
        assert ai_service._estimate_time_to_churn([], 0.2) == 365
    
    def test_gather_user_engagement_data(self, ai_service):
        """Test gathering user engagement data."""
        # Mock database queries
        mock_user = Mock()
        mock_user.user_id = 'test_user'
        mock_user.last_login = datetime.now() - timedelta(days=2)
        mock_user.created_at = datetime.now() - timedelta(days=90)
        
        mock_sessions = [Mock(duration_minutes=45, started_at=datetime.now() - timedelta(days=1))]
        mock_test_results = [Mock(percentage=78.5)]
        
        ai_service.db.query.return_value.filter.return_value.first.return_value = mock_user
        ai_service.db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.side_effect = [
            mock_sessions, mock_test_results
        ]
        
        result = ai_service._gather_user_engagement_data(1, 'test_user')
        
        assert result is not None
        assert result['user_id'] == 'test_user'
        assert 'total_sessions' in result
        assert 'recent_sessions' in result
        assert 'days_since_last_login' in result
    
    def test_gather_user_engagement_data_no_user(self, ai_service):
        """Test gathering engagement data for non-existent user."""
        ai_service.db.query.return_value.filter.return_value.first.return_value = None
        
        result = ai_service._gather_user_engagement_data(1, 'nonexistent_user')
        
        assert result is None


if __name__ == "__main__":
    pytest.main([__file__])
