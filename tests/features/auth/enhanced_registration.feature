Feature: Enhanced User Registration
  As a new user
  I want to create an account on CertRats with comprehensive validation
  So that I can access the platform securely and track my certifications

  Background:
    Given I am on the CertRats platform
    And I navigate to the registration page

  @smoke @registration @playwright
  Scenario: Successful user registration with enhanced flow
    Given I am on the registration page
    When I enter valid registration details:
      | field            | value                |
      | email           | <EMAIL>  |
      | password        | SecurePass123!       |
      | confirmPassword | SecurePass123!       |
      | firstName       | John                 |
      | lastName        | Doe                  |
    And I accept the terms and conditions
    And I click the "Register" button
    Then I should see a verification email message
    And I should be redirected to the email verification page
    When I verify my email with a valid token
    Then my account should be activated
    And I should be automatically logged in
    And I should be redirected to the profile setup page

  @validation @registration @playwright
  Scenario: Enhanced registration form validation
    Given I am on the registration page
    When I enter an invalid email "invalid-email"
    Then I should see "Please enter a valid email address" error
    When I enter a weak password "weak"
    Then I should see the password strength indicator
    And the password strength should be "weak"
    When I enter a strong password "StrongPass123!"
    Then the password strength should be "strong"
    And I should see "Great password!" message
    When I enter a different password confirmation "DifferentPass123!"
    Then I should see "Passwords don't match" error

  @email-availability @registration @playwright
  Scenario: Email availability checking with suggestions
    Given I am on the registration page
    When I enter an existing email "<EMAIL>"
    Then I should see "This email is already registered" error
    And I should see email suggestions
    When I click on the first email suggestion
    Then the email field should be updated with the suggestion
    And the error message should disappear

  @password-strength @registration @playwright
  Scenario: Password strength validation and feedback
    Given I am on the registration page
    When I enter password "weak"
    Then I should see password strength indicator
    And the strength level should be "weak"
    And I should see missing requirements:
      | requirement           |
      | At least 8 characters |
      | One uppercase letter  |
      | One number           |
      | One special character |
    When I enter password "StrongPassword123!"
    Then the strength level should be "strong"
    And all requirements should be met
    And I should see "Great password!" feedback

  @terms-modal @registration @playwright
  Scenario: Terms and conditions modal interaction
    Given I am on the registration page
    When I click on "Terms of Service" link
    Then the terms modal should open
    And I should see "Terms of Service & Privacy Policy" title
    When I click "Accept Terms" in the modal
    Then the modal should close
    And the terms checkbox should be checked
    When I click on "Privacy Policy" link
    Then the terms modal should open again
    When I click the close button
    Then the modal should close
    And the terms checkbox should remain unchecked

  @accessibility @registration @playwright
  Scenario: Registration form accessibility compliance
    Given I am on the registration page
    When I navigate using only the keyboard
    Then I should be able to access all form fields in logical order:
      | field                    |
      | email input             |
      | first name input        |
      | last name input         |
      | password input          |
      | confirm password input  |
      | referral code input     |
      | terms checkbox          |
      | marketing checkbox      |
      | register button         |
    And focus indicators should be clearly visible
    And form labels should be properly associated
    And error messages should be announced to screen readers

  @performance @registration @playwright
  Scenario: Registration page performance optimization
    Given I am on the registration page
    When I measure the page load time
    Then the page should load within 2.5 seconds
    And form interactions should be responsive
    When I enter data in form fields
    Then input responses should be under 100ms
    And validation should occur in real-time
    When I submit the registration form
    Then the registration process should complete within 5 seconds

  @mobile @registration @playwright
  Scenario: Mobile registration experience
    Given I am using a mobile device
    And I am on the registration page
    Then the layout should be optimized for mobile
    And touch targets should be at least 44px
    When I enter registration details using touch input
    Then the virtual keyboard should behave appropriately
    And form validation should work on mobile
    When I submit the form
    Then the registration should work seamlessly on mobile

  @security @registration @playwright
  Scenario: Registration security measures
    Given I am on the registration page
    When I attempt to register with a weak password "123"
    Then the system should reject the password
    And provide password strength guidance
    When I attempt to register with an existing email
    Then the system should prevent duplicate registration
    And suggest alternative email addresses
    When I enter a strong password
    Then the password should be hidden by default
    And I should be able to toggle password visibility

  @error-handling @registration @playwright
  Scenario: Registration error handling and recovery
    Given I am on the registration page
    When the registration API returns an error
    And I submit a valid registration form
    Then I should see an appropriate error message
    And the form should remain functional
    And I should be able to retry registration
    When the network connection fails
    Then I should see a network error message
    And the form data should be preserved

  @referral @registration @playwright
  Scenario: Referral code handling
    Given I am on the registration page with referral code "FRIEND123"
    Then the referral code field should be pre-filled
    When I complete the registration
    Then the referral code should be processed
    And the referring user should receive credit

  @marketing-consent @registration @playwright
  Scenario: Marketing consent management
    Given I am on the registration page
    When I check the marketing consent checkbox
    Then I should opt-in to marketing communications
    When I uncheck the marketing consent checkbox
    Then I should opt-out of marketing communications
    And this preference should be saved with my account

  @email-verification @registration @playwright
  Scenario: Email verification flow
    Given I have completed registration
    And I am on the email verification page
    When I click the verification link with a valid token
    Then my email should be verified
    And I should be automatically logged in
    And I should see a success message
    When I click the verification link with an expired token
    Then I should see an "expired token" error
    And I should be able to request a new verification email

  @resend-verification @registration @playwright
  Scenario: Resend verification email
    Given I have completed registration
    And I am on the email verification page
    When I click "Resend verification email"
    Then a new verification email should be sent
    And I should see a success message
    When I try to resend again immediately
    Then I should see a rate limiting message
    And I should wait before requesting another email

  @profile-setup @registration @playwright
  Scenario: Initial profile setup after registration
    Given I have verified my email
    And I am redirected to profile setup
    When I complete the profile setup wizard:
      | field            | value              |
      | currentRole      | Security Analyst   |
      | experienceLevel  | intermediate       |
      | interests        | cybersecurity      |
      | goals           | get-cissp          |
      | timezone        | America/New_York   |
    And I click "Complete Setup"
    Then my profile should be saved
    And I should be redirected to the dashboard
    And I should see personalized recommendations

  @cross-browser @registration @playwright
  Scenario: Cross-browser registration compatibility
    Given I am on the registration page
    When I complete registration in different browsers
    Then the registration should work consistently
    And all features should function properly
    And the user experience should be uniform

  @integration @registration @playwright
  Scenario: Full registration integration testing
    Given I am on the registration page
    When I complete the entire registration flow
    Then my account should be created in the database
    And verification email should be sent
    And email verification should work
    And profile setup should be accessible
    And dashboard should load with my data
    And all integrated services should respond correctly
