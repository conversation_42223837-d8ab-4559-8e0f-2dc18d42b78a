Feature: User Authentication
  As a user
  I want to log into the CertRats platform
  So that I can access my certification dashboard

  Background:
    Given I am on the CertRats platform
    And I navigate to the login page

  Scenario: Successful login with valid credentials
    Given I am on the login page
    When I enter valid email "<EMAIL>"
    And I enter valid password "password123"
    And I click the "Sign In" button
    Then I should be redirected to the dashboard
    And I should see a welcome message
    And I should see my user profile in the navigation

  Scenario: Failed login with invalid credentials
    Given I am on the login page
    When I enter email "<EMAIL>"
    And I enter password "wrongpassword"
    And I click the "Sign In" button
    Then I should see an error message "Invalid email or password"
    And I should remain on the login page
    And the form fields should retain their values

  Sc<PERSON>rio: Form validation for empty fields
    Given I am on the login page
    When I click the "Sign In" button without entering credentials
    Then I should see validation errors
    And I should see "Email is required" error message
    And I should see "Password is required" error message
    And the form should not be submitted

  Scenario: Email format validation
    Given I am on the login page
    When I enter invalid email "not-an-email"
    And I enter valid password "password123"
    And I click the "Sign In" button
    Then I should see "Please enter a valid email address" error message
    And the form should not be submitted

  Scenario: Password length validation
    Given I am on the login page
    When I enter valid email "<EMAIL>"
    And I enter short password "123"
    And I click the "Sign In" button
    Then I should see "Password must be at least 8 characters" error message
    And the form should not be submitted

  Scenario: Remember me functionality
    Given I am on the login page
    When I enter valid email "<EMAIL>"
    And I enter valid password "password123"
    And I check the "Remember me" checkbox
    And I click the "Sign In" button
    Then I should be redirected to the dashboard
    And my session should be remembered for future visits

  Scenario: Password visibility toggle
    Given I am on the login page
    When I enter password "secretpassword"
    Then the password should be hidden by default
    When I click the "Show password" button
    Then the password should be visible
    When I click the "Hide password" button
    Then the password should be hidden again

  Scenario: Forgot password navigation
    Given I am on the login page
    When I click the "Forgot your password?" link
    Then I should be redirected to the forgot password page
    And I should see the password reset form

  Scenario: Loading state during authentication
    Given I am on the login page
    When I enter valid email "<EMAIL>"
    And I enter valid password "password123"
    And I click the "Sign In" button
    Then I should see a loading indicator
    And the submit button should be disabled
    And the button text should change to "Signing in..."

  Scenario: Keyboard navigation accessibility
    Given I am on the login page
    When I use keyboard navigation
    Then I should be able to tab through all form elements
    And the tab order should be logical (email, password, remember me, submit)
    And I should be able to submit the form using Enter key
    And focus indicators should be clearly visible

  Scenario: Screen reader accessibility
    Given I am on the login page
    And I am using a screen reader
    Then all form elements should have proper labels
    And error messages should be announced
    And the form structure should be semantic
    And ARIA attributes should be properly set

  Scenario: Mobile responsive design
    Given I am on the login page
    And I am using a mobile device
    Then the login form should be properly sized
    And all elements should be easily tappable
    And the form should work with virtual keyboards
    And the layout should adapt to different screen sizes

  Scenario: Network error handling
    Given I am on the login page
    When I enter valid credentials
    And I click the "Sign In" button
    But the network request fails
    Then I should see a network error message
    And I should be able to retry the login
    And the form should remain functional

  Scenario: Session timeout handling
    Given I am logged into the platform
    When my session expires
    And I try to access a protected page
    Then I should be redirected to the login page
    And I should see a session timeout message
    And I should be able to log in again

  Scenario: Multiple failed login attempts
    Given I am on the login page
    When I enter invalid credentials multiple times
    Then I should see appropriate error messages
    And the system should handle rate limiting gracefully
    And I should still be able to enter correct credentials

  Scenario: Auto-focus on page load
    Given I navigate to the login page
    Then the email input field should be automatically focused
    And I should be able to start typing immediately
    And the focus should be clearly visible

  Scenario: Form error clearing
    Given I am on the login page
    And I have triggered validation errors
    When I start typing in any form field
    Then the error messages should be cleared
    And the form should return to a clean state
    And I should be able to submit with valid data
