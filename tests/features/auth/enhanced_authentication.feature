Feature: Enhanced User Authentication
  As a registered user
  I want to log into my CertRats account securely
  So that I can access my personalized dashboard and features

  Background:
    Given I am on the CertRats platform
    And I have a registered account with email "<EMAIL>" and password "password123"

  @smoke @authentication @playwright
  Scenario: Successful login with enhanced features
    Given I am on the login page
    When I enter email "<EMAIL>"
    And I enter password "password123"
    And I check "Remember me"
    And I click the "Sign In" button
    Then I should be redirected to the dashboard
    And I should see a personalized welcome message
    And my session should be remembered
    And my authentication tokens should be stored securely

  @authentication @validation @playwright
  Scenario: Enhanced login form validation
    Given I am on the login page
    When I enter an invalid email "invalid-email"
    Then I should see "Please enter a valid email address" error
    When I try to submit without a password
    Then I should see "Password is required" error
    When I enter valid credentials
    Then all validation errors should disappear
    And the login button should be enabled

  @authentication @password-visibility @playwright
  Scenario: Password visibility toggle functionality
    Given I am on the login page
    When I enter password "secretpassword"
    Then the password should be hidden by default
    When I click the password visibility toggle
    Then the password should be visible as text
    When I click the password visibility toggle again
    Then the password should be hidden again
    And the toggle button should have appropriate accessibility labels

  @authentication @security @playwright
  Scenario: Secure authentication with token management
    Given I am on the login page
    When I login with valid credentials
    Then I should receive JWT access and refresh tokens
    And the tokens should be stored securely
    And the access token should have appropriate expiration
    When my access token expires
    Then it should be automatically refreshed
    And I should remain logged in without interruption

  @authentication @error-handling @playwright
  Scenario: Authentication error handling
    Given I am on the login page
    When I enter invalid credentials "<EMAIL>" and "wrongpassword"
    And I click the "Sign In" button
    Then I should see an error message "Invalid email or password"
    And I should remain on the login page
    And the email field should be focused for retry
    And the form should remain functional

  @password-reset @authentication @playwright
  Scenario: Password reset modal functionality
    Given I am on the login page
    When I click "Forgot your password?"
    Then the password reset modal should open
    And I should see "Reset Password" title
    When I enter email "<EMAIL>" in the reset form
    And I click "Send Reset Email"
    Then I should see "Reset Email Sent!" success message
    And I should see a cooldown timer for resending

  @password-reset @validation @playwright
  Scenario: Password reset form validation
    Given I am on the login page
    When I open the password reset modal
    And I enter an invalid email "invalid-email"
    Then I should see email validation error
    When I enter a valid email "<EMAIL>"
    Then the validation error should disappear
    And the send button should be enabled

  @password-reset @cooldown @playwright
  Scenario: Password reset cooldown mechanism
    Given I am on the login page
    When I successfully send a password reset email
    Then I should see a 60-second cooldown timer
    And the resend button should be disabled
    When I wait for the cooldown to expire
    Then the resend button should be enabled again
    And I should be able to send another reset email

  @accessibility @authentication @playwright
  Scenario: Login form accessibility compliance
    Given I am on the login page
    When I navigate using only the keyboard
    Then I should be able to access all form elements in order:
      | element                    |
      | email input               |
      | password input            |
      | password toggle button    |
      | remember me checkbox      |
      | forgot password link      |
      | sign in button           |
    And focus indicators should be clearly visible
    And form labels should be properly associated
    And error messages should be announced to screen readers

  @accessibility @password-reset @playwright
  Scenario: Password reset modal accessibility
    Given I am on the login page
    When I open the password reset modal
    Then the modal should trap focus properly
    And I should be able to navigate with keyboard only
    And the modal should have proper ARIA attributes
    When I press Escape key
    Then the modal should close
    And focus should return to the forgot password link

  @performance @authentication @playwright
  Scenario: Authentication performance optimization
    Given I am on the login page
    When I measure the page load time
    Then the page should load within 2 seconds
    And form interactions should be responsive
    When I submit valid credentials
    Then the authentication should complete within 3 seconds
    And the dashboard should load within 2 seconds after login

  @mobile @authentication @playwright
  Scenario: Mobile authentication experience
    Given I am using a mobile device
    And I am on the login page
    Then the layout should be optimized for mobile
    And touch targets should be appropriately sized
    When I enter credentials using touch input
    Then the virtual keyboard should behave appropriately
    And the password toggle should be easily accessible
    When I submit the form
    Then the authentication should work seamlessly on mobile

  @security @token-refresh @playwright
  Scenario: Automatic token refresh mechanism
    Given I am logged in with an access token
    When my access token expires during a session
    And I make an API request
    Then my token should be automatically refreshed
    And the original request should be retried
    And I should remain logged in without interruption
    When the refresh token also expires
    Then I should be redirected to the login page
    And my session should be cleared

  @logout @authentication @playwright
  Scenario: Secure logout functionality
    Given I am logged in and on the dashboard
    When I click the logout button
    And I confirm the logout action
    Then I should be redirected to the login page
    And my authentication tokens should be cleared
    And my session should be terminated on the server
    And I should not be able to access protected pages

  @remember-me @authentication @playwright
  Scenario: Remember me functionality
    Given I am on the login page
    When I login with "Remember me" checked
    Then my session should persist across browser restarts
    And I should remain logged in for extended period
    When I login without "Remember me" checked
    Then my session should expire when browser closes
    And I should need to login again

  @cross-browser @authentication @playwright
  Scenario: Cross-browser authentication compatibility
    Given I am on the login page
    When I login using different browsers
    Then the authentication should work consistently
    And all security features should function properly
    And the user experience should be uniform
    And tokens should be managed correctly

  @network-resilience @authentication @playwright
  Scenario: Network error handling during authentication
    Given I am on the login page
    When the network connection fails during login
    Then I should see an appropriate error message
    And the form should remain functional
    When the connection is restored
    Then I should be able to retry login successfully
    And the authentication should complete normally

  @session-management @authentication @playwright
  Scenario: Advanced session management
    Given I am logged in on multiple devices
    When I logout from one device
    Then I should remain logged in on other devices
    When I choose "Logout from all devices"
    Then all my sessions should be terminated
    And I should need to login again on all devices

  @integration @authentication @playwright
  Scenario: Full authentication integration testing
    Given I am on the login page
    When I complete the entire authentication flow
    Then my credentials should be validated against the database
    And JWT tokens should be generated correctly
    And my session should be established properly
    And the dashboard should load with my personalized data
    And all integrated services should recognize my authentication
    And security measures should be properly enforced

  @rate-limiting @authentication @playwright
  Scenario: Authentication rate limiting protection
    Given I am on the login page
    When I make multiple failed login attempts
    Then I should be rate limited after 5 attempts
    And I should see a cooldown message
    When the cooldown period expires
    Then I should be able to attempt login again
    And the rate limiting should reset

  @device-tracking @authentication @playwright
  Scenario: Device and location tracking
    Given I am logging in from a new device
    When I complete authentication
    Then my device information should be recorded
    And I should receive a security notification
    When I login from a suspicious location
    Then additional security measures should be triggered
    And I should be prompted for additional verification
