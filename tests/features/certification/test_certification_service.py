"""Tests for the enhanced certification service"""
import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from services.certification import (
    CertificationService, CertificationCreate, CertificationUpdate,
    CertificationResponse, CertificationFilters
)
from services.base_crud import ValidationError, NotFoundError, ConflictError
from models.certification import Certification


class TestCertificationService:
    """Test suite for CertificationService"""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def certification_service(self, mock_db):
        """Create certification service instance"""
        return CertificationService(mock_db)
    
    @pytest.fixture
    def sample_certification_data(self):
        """Sample certification data for testing"""
        return {
            'name': 'CISSP',
            'description': 'Certified Information Systems Security Professional',
            'level': 'Professional',
            'difficulty': 'Advanced',
            'focus': 'General',
            'domain': 'Information Security',
            'category': 'Security Management',
            'cost': 749.0,
            'currency': 'USD',
            'organization_id': 1,
            'url': 'https://www.isc2.org/Certifications/CISSP',
            'exam_code': 'CISSP',
            'validity_period': 36,
            'prerequisites': ['5 years experience'],
            'recommended_experience': '5+ years in information security'
        }
    
    @pytest.fixture
    def sample_certification_create(self, sample_certification_data):
        """Sample CertificationCreate object"""
        return CertificationCreate(**sample_certification_data)
    
    @pytest.fixture
    def mock_certification(self, sample_certification_data):
        """Mock certification model instance"""
        cert = Mock(spec=Certification)
        cert.id = 1
        cert.is_deleted = False
        cert.is_active = True
        cert.created_at = '2024-01-01T00:00:00Z'
        cert.updated_at = '2024-01-01T00:00:00Z'
        
        # Set all attributes from sample data
        for key, value in sample_certification_data.items():
            setattr(cert, key, value)
        
        return cert

    def test_create_certification_success(self, certification_service, sample_certification_create, mock_certification):
        """Test successful certification creation"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.first.return_value = None
        certification_service.db.add = Mock()
        certification_service.db.commit = Mock()
        certification_service.db.refresh = Mock()
        
        # Mock the model constructor
        with patch('services.certification.Certification', return_value=mock_certification):
            # Execute
            result = certification_service.create(sample_certification_create, user_id='test_user')
            
            # Verify
            certification_service.db.add.assert_called_once()
            certification_service.db.commit.assert_called_once()
            certification_service.db.refresh.assert_called_once()
            assert isinstance(result, CertificationResponse)

    def test_create_certification_duplicate_name(self, certification_service, sample_certification_create, mock_certification):
        """Test certification creation with duplicate name"""
        # Setup - existing certification found
        certification_service.db.query.return_value.filter.return_value.first.return_value = mock_certification
        
        # Execute & Verify
        with pytest.raises(ValidationError) as exc_info:
            certification_service.create(sample_certification_create)
        
        assert "already exists" in str(exc_info.value)

    def test_create_certification_database_error(self, certification_service, sample_certification_create):
        """Test certification creation with database error"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.first.return_value = None
        certification_service.db.commit.side_effect = IntegrityError("test", "test", "test")
        certification_service.db.rollback = Mock()
        
        # Execute & Verify
        with pytest.raises(ConflictError):
            certification_service.create(sample_certification_create)
        
        certification_service.db.rollback.assert_called_once()

    def test_get_certification_success(self, certification_service, mock_certification):
        """Test successful certification retrieval"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.first.return_value = mock_certification
        
        # Execute
        result = certification_service.get(1)
        
        # Verify
        assert result is not None
        assert isinstance(result, CertificationResponse)

    def test_get_certification_not_found(self, certification_service):
        """Test certification retrieval when not found"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.first.return_value = None
        
        # Execute
        result = certification_service.get(999)
        
        # Verify
        assert result is None

    def test_get_or_404_success(self, certification_service, mock_certification):
        """Test successful get_or_404"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.first.return_value = mock_certification
        
        # Execute
        result = certification_service.get_or_404(1)
        
        # Verify
        assert isinstance(result, CertificationResponse)

    def test_get_or_404_not_found(self, certification_service):
        """Test get_or_404 when not found"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.first.return_value = None
        
        # Execute & Verify
        with pytest.raises(NotFoundError):
            certification_service.get_or_404(999)

    def test_update_certification_success(self, certification_service, mock_certification):
        """Test successful certification update"""
        # Setup
        update_data = CertificationUpdate(name="Updated CISSP", cost=799.0)
        certification_service.db.query.return_value.filter.return_value.first.return_value = mock_certification
        certification_service.db.commit = Mock()
        certification_service.db.refresh = Mock()
        
        # Execute
        result = certification_service.update(1, update_data, user_id='test_user')
        
        # Verify
        certification_service.db.commit.assert_called_once()
        certification_service.db.refresh.assert_called_once()
        assert isinstance(result, CertificationResponse)

    def test_update_certification_not_found(self, certification_service):
        """Test certification update when not found"""
        # Setup
        update_data = CertificationUpdate(name="Updated CISSP")
        certification_service.db.query.return_value.filter.return_value.first.return_value = None
        
        # Execute & Verify
        with pytest.raises(NotFoundError):
            certification_service.update(999, update_data)

    def test_delete_certification_soft_delete(self, certification_service, mock_certification):
        """Test soft delete of certification"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.first.return_value = mock_certification
        certification_service.db.commit = Mock()
        
        # Execute
        result = certification_service.delete(1, user_id='test_user', soft=True)
        
        # Verify
        assert result is True
        assert mock_certification.is_deleted is True
        certification_service.db.commit.assert_called_once()

    def test_delete_certification_hard_delete(self, certification_service, mock_certification):
        """Test hard delete of certification"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.first.return_value = mock_certification
        certification_service.db.delete = Mock()
        certification_service.db.commit = Mock()
        
        # Execute
        result = certification_service.delete(1, user_id='test_user', soft=False)
        
        # Verify
        assert result is True
        certification_service.db.delete.assert_called_once_with(mock_certification)
        certification_service.db.commit.assert_called_once()

    def test_search_certifications(self, certification_service, mock_certification):
        """Test certification search functionality"""
        # Setup
        certification_service.db.query.return_value.filter.return_value.count.return_value = 1
        certification_service.db.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [mock_certification]
        
        # Execute
        result = certification_service.search_certifications("CISSP")
        
        # Verify
        assert result.total_count == 1
        assert len(result.data) == 1
        assert isinstance(result.data[0], CertificationResponse)

    def test_get_certification_stats(self, certification_service):
        """Test certification statistics retrieval"""
        # Setup mock query results
        certification_service.db.query.return_value.filter.return_value.count.return_value = 100
        certification_service.db.query.return_value.filter.return_value.group_by.return_value.all.return_value = [
            ('Information Security', 50),
            ('Network Security', 30),
            ('Cloud Security', 20)
        ]
        
        # Mock cost statistics
        mock_cost_stats = Mock()
        mock_cost_stats.min_cost = 100.0
        mock_cost_stats.max_cost = 1000.0
        mock_cost_stats.avg_cost = 500.0
        certification_service.db.query.return_value.filter.return_value.first.return_value = mock_cost_stats
        
        # Execute
        result = certification_service.get_certification_stats()
        
        # Verify
        assert 'total_certifications' in result
        assert 'domain_distribution' in result
        assert 'cost_statistics' in result
        assert result['cost_statistics']['min_cost'] == 100.0

    def test_get_related_certifications(self, certification_service, mock_certification):
        """Test getting related certifications"""
        # Setup
        certification_service.get_or_404 = Mock(return_value=CertificationResponse(
            id=1, name="CISSP", domain="Information Security", 
            organization_id=1, level="Professional", **{
                k: v for k, v in mock_certification.__dict__.items() 
                if not k.startswith('_')
            }
        ))
        certification_service.db.query.return_value.filter.return_value.limit.return_value.all.return_value = [mock_certification]
        
        # Execute
        result = certification_service.get_related_certifications(1, limit=3)
        
        # Verify
        assert len(result) == 1
        assert isinstance(result[0], CertificationResponse)

    def test_apply_certification_filters(self, certification_service):
        """Test application of certification-specific filters"""
        # Setup
        mock_query = Mock()
        filters = CertificationFilters(
            domains=['Information Security'],
            levels=['Professional'],
            cost_min=100.0,
            cost_max=1000.0,
            is_active=True
        )
        
        # Execute
        result = certification_service._apply_filters(mock_query, filters)
        
        # Verify that filter methods were called
        # Note: This is a simplified test - in practice you'd verify the actual filter conditions
        assert result is not None
