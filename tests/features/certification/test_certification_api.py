"""API tests for certification endpoints"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from api.app import app
from services.certification import CertificationService, CertificationResponse
from services.base_crud import PaginatedResponse, NotFoundError


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_certification_service():
    """Mock certification service"""
    return Mock(spec=CertificationService)


@pytest.fixture
def sample_certification_response():
    """Sample certification response data"""
    return CertificationResponse(
        id=1,
        name="CISSP",
        description="Certified Information Systems Security Professional",
        level="Professional",
        difficulty="Advanced",
        focus="General",
        domain="Information Security",
        category="Security Management",
        cost=749.0,
        currency="USD",
        organization_id=1,
        url="https://www.isc2.org/Certifications/CISSP",
        exam_code="CISSP",
        validity_period=36,
        prerequisites=["5 years experience"],
        recommended_experience="5+ years in information security",
        is_active=True,
        is_deleted=False,
        created_at="2024-01-01T00:00:00Z",
        updated_at="2024-01-01T00:00:00Z"
    )


@pytest.fixture
def sample_paginated_response(sample_certification_response):
    """Sample paginated response"""
    return PaginatedResponse(
        data=[sample_certification_response],
        pagination={
            "page": 1,
            "page_size": 20,
            "total_items": 1,
            "total_pages": 1,
            "has_next": False,
            "has_previous": False
        },
        total_count=1,
        filters_applied={}
    )


class TestCertificationAPI:
    """Test suite for certification API endpoints"""

    @pytest.mark.api
    @pytest.mark.certification
    def test_get_certifications_success(self, client, mock_certification_service, sample_paginated_response):
        """Test successful retrieval of certifications"""
        # Setup
        mock_certification_service.list.return_value = sample_paginated_response
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert "data" in data
            assert "pagination" in data
            assert len(data["data"]) == 1
            assert data["data"][0]["name"] == "CISSP"

    @pytest.mark.api
    @pytest.mark.certification
    def test_get_certifications_with_filters(self, client, mock_certification_service, sample_paginated_response):
        """Test certification retrieval with filters"""
        # Setup
        mock_certification_service.list.return_value = sample_paginated_response
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/?domain=Information Security&level=Professional")
            
            # Verify
            assert response.status_code == 200
            mock_certification_service.list.assert_called_once()

    @pytest.mark.api
    @pytest.mark.certification
    def test_get_certification_by_id_success(self, client, mock_certification_service, sample_certification_response):
        """Test successful retrieval of certification by ID"""
        # Setup
        mock_certification_service.get_or_404.return_value = sample_certification_response
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/1")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == 1
            assert data["name"] == "CISSP"

    @pytest.mark.api
    @pytest.mark.certification
    def test_get_certification_by_id_not_found(self, client, mock_certification_service):
        """Test certification retrieval when not found"""
        # Setup
        mock_certification_service.get_or_404.side_effect = NotFoundError("Certification not found")
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/999")
            
            # Verify
            assert response.status_code == 404
            data = response.json()
            assert "detail" in data

    @pytest.mark.api
    @pytest.mark.certification
    def test_search_certifications(self, client, mock_certification_service, sample_paginated_response):
        """Test certification search"""
        # Setup
        mock_certification_service.search_certifications.return_value = sample_paginated_response
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/search?q=CISSP")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert "data" in data
            mock_certification_service.search_certifications.assert_called_once()

    @pytest.mark.api
    @pytest.mark.certification
    def test_get_certification_stats(self, client, mock_certification_service):
        """Test certification statistics endpoint"""
        # Setup
        mock_stats = {
            "total_certifications": 100,
            "active_certifications": 95,
            "domain_distribution": {"Information Security": 50, "Network Security": 30},
            "cost_statistics": {"min_cost": 100.0, "max_cost": 1000.0, "avg_cost": 500.0}
        }
        mock_certification_service.get_certification_stats.return_value = mock_stats
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/stats")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert data["total_certifications"] == 100
            assert "domain_distribution" in data

    @pytest.mark.api
    @pytest.mark.certification
    def test_get_related_certifications(self, client, mock_certification_service, sample_certification_response):
        """Test related certifications endpoint"""
        # Setup
        mock_certification_service.get_related_certifications.return_value = [sample_certification_response]
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/1/related")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["name"] == "CISSP"

    @pytest.mark.api
    @pytest.mark.certification
    def test_get_certifications_by_domain(self, client, mock_certification_service, sample_paginated_response):
        """Test certification retrieval by domain"""
        # Setup
        mock_certification_service.get_certifications_by_domain.return_value = sample_paginated_response
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/domain/Information Security")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert "data" in data

    @pytest.mark.api
    @pytest.mark.certification
    def test_get_certifications_by_level(self, client, mock_certification_service, sample_paginated_response):
        """Test certification retrieval by level"""
        # Setup
        mock_certification_service.get_certifications_by_level.return_value = sample_paginated_response
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/level/Professional")
            
            # Verify
            assert response.status_code == 200
            data = response.json()
            assert "data" in data

    @pytest.mark.api
    @pytest.mark.certification
    def test_invalid_pagination_parameters(self, client):
        """Test API with invalid pagination parameters"""
        # Execute
        response = client.get("/api/v1/certifications/?page=0&limit=-1")
        
        # Verify
        assert response.status_code == 422  # Validation error

    @pytest.mark.api
    @pytest.mark.certification
    def test_api_error_handling(self, client, mock_certification_service):
        """Test API error handling"""
        # Setup
        mock_certification_service.list.side_effect = Exception("Database error")
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            response = client.get("/api/v1/certifications/")
            
            # Verify
            assert response.status_code == 500

    @pytest.mark.api
    @pytest.mark.certification
    def test_authentication_required(self, client):
        """Test that authentication is required for protected endpoints"""
        # Note: This test assumes authentication is implemented
        # Execute
        response = client.post("/api/v1/certifications/", json={})
        
        # Verify - should require authentication
        assert response.status_code in [401, 403]  # Unauthorized or Forbidden

    @pytest.mark.api
    @pytest.mark.certification
    @pytest.mark.performance
    def test_large_dataset_performance(self, client, mock_certification_service):
        """Test API performance with large datasets"""
        # Setup - simulate large dataset
        large_response = PaginatedResponse(
            data=[],  # Empty for performance test
            pagination={
                "page": 1,
                "page_size": 100,
                "total_items": 10000,
                "total_pages": 100,
                "has_next": True,
                "has_previous": False
            },
            total_count=10000,
            filters_applied={}
        )
        mock_certification_service.list.return_value = large_response
        
        with patch('api.v1.certifications.CertificationService', return_value=mock_certification_service):
            # Execute
            import time
            start_time = time.time()
            response = client.get("/api/v1/certifications/?limit=100")
            end_time = time.time()
            
            # Verify
            assert response.status_code == 200
            assert (end_time - start_time) < 2.0  # Should respond within 2 seconds

    @pytest.mark.api
    @pytest.mark.certification
    @pytest.mark.security
    def test_sql_injection_protection(self, client):
        """Test protection against SQL injection"""
        # Execute with potential SQL injection
        malicious_query = "'; DROP TABLE certifications; --"
        response = client.get(f"/api/v1/certifications/search?q={malicious_query}")
        
        # Verify - should not cause server error
        assert response.status_code in [200, 400, 422]  # Should handle gracefully
