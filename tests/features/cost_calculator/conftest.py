"""Test configuration and fixtures for cost calculator tests.

This module provides comprehensive test fixtures and configuration
for achieving 95%+ test coverage of the cost calculator functionality.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import tempfile
import os

from models.cost_calculation import CurrencyRate, CostScenario, CostCalculation, CostHistory
from models.certification import Certification, Organization
from services.cost_calculator import CostCalculatorService


@pytest.fixture
def mock_auth_user():
    """Mock authenticated user for testing."""
    return "test_user_1"


@pytest.fixture
def sample_organization(db_session):
    """Create a sample organization for testing."""
    org = Organization(
        name="Test Organization",
        country="US",
        website="https://test-org.com",
        description="Test organization for unit testing"
    )
    db_session.add(org)
    db_session.commit()
    return org


@pytest.fixture
def comprehensive_certifications(db_session, sample_organization):
    """Create comprehensive set of certifications for testing."""
    certifications = [
        # Entry Level
        Certification(
            name="CompTIA Security+",
            category="Entry Level",
            domain="General Security",
            level="Entry",
            difficulty=2,
            cost=349.0,
            organization_id=sample_organization.id,
            description="Entry-level security certification",
            prerequisites="None",
            exam_duration=90,
            passing_score=750,
            total_questions=90,
            exam_format="Multiple choice",
            renewal_period=36,
            cpe_required=50
        ),
        # Intermediate Level
        Certification(
            name="Certified Ethical Hacker (CEH)",
            category="Intermediate",
            domain="Ethical Hacking",
            level="Intermediate",
            difficulty=3,
            cost=1199.0,
            organization_id=sample_organization.id,
            description="Intermediate ethical hacking certification",
            prerequisites="2 years experience",
            exam_duration=240,
            passing_score=70,
            total_questions=125,
            exam_format="Multiple choice",
            renewal_period=36,
            cpe_required=120
        ),
        # Advanced Level
        Certification(
            name="Certified Information Systems Security Professional (CISSP)",
            category="Advanced",
            domain="Security Management",
            level="Expert",
            difficulty=5,
            cost=749.0,
            organization_id=sample_organization.id,
            description="Advanced security management certification",
            prerequisites="5 years experience",
            exam_duration=360,
            passing_score=700,
            total_questions=250,
            exam_format="Adaptive",
            renewal_period=36,
            cpe_required=120
        ),
        # Specialized
        Certification(
            name="Certified Information Security Manager (CISM)",
            category="Management",
            domain="Information Security Management",
            level="Expert",
            difficulty=4,
            cost=899.0,
            organization_id=sample_organization.id,
            description="Information security management certification",
            prerequisites="5 years experience",
            exam_duration=240,
            passing_score=450,
            total_questions=150,
            exam_format="Multiple choice",
            renewal_period=36,
            cpe_required=120
        ),
        # Cloud Security
        Certification(
            name="Certified Cloud Security Professional (CCSP)",
            category="Cloud Security",
            domain="Cloud Security",
            level="Expert",
            difficulty=4,
            cost=749.0,
            organization_id=sample_organization.id,
            description="Cloud security specialist certification",
            prerequisites="5 years experience",
            exam_duration=240,
            passing_score=700,
            total_questions=150,
            exam_format="Multiple choice",
            renewal_period=36,
            cpe_required=120
        )
    ]
    
    for cert in certifications:
        db_session.add(cert)
    db_session.commit()
    return certifications


@pytest.fixture
def comprehensive_currency_rates(db_session):
    """Create comprehensive set of currency rates for testing."""
    now = datetime.utcnow()
    
    rates = [
        # Major currency pairs
        CurrencyRate(
            base_currency='USD', target_currency='EUR', rate=0.92,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        CurrencyRate(
            base_currency='USD', target_currency='GBP', rate=0.79,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        CurrencyRate(
            base_currency='USD', target_currency='JPY', rate=150.0,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        CurrencyRate(
            base_currency='USD', target_currency='CAD', rate=1.35,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        CurrencyRate(
            base_currency='USD', target_currency='AUD', rate=1.52,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        
        # Reverse pairs
        CurrencyRate(
            base_currency='EUR', target_currency='USD', rate=1.09,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        CurrencyRate(
            base_currency='GBP', target_currency='USD', rate=1.27,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        
        # Cross pairs
        CurrencyRate(
            base_currency='EUR', target_currency='GBP', rate=0.86,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        CurrencyRate(
            base_currency='GBP', target_currency='EUR', rate=1.16,
            source='api', valid_from=now, valid_until=now + timedelta(days=30)
        ),
        
        # Historical rates (expired)
        CurrencyRate(
            base_currency='USD', target_currency='EUR', rate=0.88,
            source='api', 
            valid_from=now - timedelta(days=60),
            valid_until=now - timedelta(days=30),
            is_active=False
        ),
        
        # Future rates
        CurrencyRate(
            base_currency='USD', target_currency='CHF', rate=0.91,
            source='api', valid_from=now + timedelta(days=1)
        )
    ]
    
    for rate in rates:
        db_session.add(rate)
    db_session.commit()
    return rates


@pytest.fixture
def comprehensive_cost_scenarios(db_session):
    """Create comprehensive set of cost scenarios for testing."""
    scenarios = [
        CostScenario(
            name='Self-Study Basic',
            description='Basic self-study approach with minimal resources',
            scenario_type='self_study',
            materials_multiplier=1.0,
            training_multiplier=0.0,
            retake_probability=0.30,
            includes_training=False,
            includes_mentoring=False,
            includes_practice_exams=True,
            study_time_multiplier=1.3,
            preparation_weeks=16
        ),
        CostScenario(
            name='Self-Study Premium',
            description='Premium self-study with additional resources',
            scenario_type='self_study',
            materials_multiplier=1.5,
            training_multiplier=0.0,
            retake_probability=0.20,
            includes_training=False,
            includes_mentoring=False,
            includes_practice_exams=True,
            study_time_multiplier=1.1,
            preparation_weeks=14
        ),
        CostScenario(
            name='Intensive Bootcamp',
            description='Intensive bootcamp with instructor support',
            scenario_type='bootcamp',
            materials_multiplier=2.0,
            training_multiplier=4.0,
            retake_probability=0.08,
            includes_training=True,
            includes_mentoring=True,
            includes_practice_exams=True,
            study_time_multiplier=0.7,
            preparation_weeks=6
        ),
        CostScenario(
            name='Standard Bootcamp',
            description='Standard bootcamp training program',
            scenario_type='bootcamp',
            materials_multiplier=1.5,
            training_multiplier=2.5,
            retake_probability=0.12,
            includes_training=True,
            includes_mentoring=False,
            includes_practice_exams=True,
            study_time_multiplier=0.8,
            preparation_weeks=8
        ),
        CostScenario(
            name='University Course',
            description='Formal university course with academic support',
            scenario_type='university',
            materials_multiplier=2.5,
            training_multiplier=5.0,
            retake_probability=0.05,
            includes_training=True,
            includes_mentoring=True,
            includes_practice_exams=True,
            study_time_multiplier=1.0,
            preparation_weeks=16
        ),
        CostScenario(
            name='Corporate Training',
            description='Company-sponsored training program',
            scenario_type='corporate',
            materials_multiplier=1.8,
            training_multiplier=3.0,
            retake_probability=0.10,
            includes_training=True,
            includes_mentoring=False,
            includes_practice_exams=True,
            study_time_multiplier=0.9,
            preparation_weeks=12
        ),
        CostScenario(
            name='Hybrid Approach',
            description='Combination of self-study and formal training',
            scenario_type='hybrid',
            materials_multiplier=1.3,
            training_multiplier=1.5,
            retake_probability=0.15,
            includes_training=True,
            includes_mentoring=False,
            includes_practice_exams=True,
            study_time_multiplier=1.0,
            preparation_weeks=14
        ),
        CostScenario(
            name='Budget Conscious',
            description='Minimal cost approach for budget-conscious learners',
            scenario_type='self_study',
            materials_multiplier=0.8,
            training_multiplier=0.0,
            retake_probability=0.35,
            includes_training=False,
            includes_mentoring=False,
            includes_practice_exams=False,
            study_time_multiplier=1.5,
            preparation_weeks=20
        ),
        CostScenario(
            name='Premium All-Inclusive',
            description='Premium approach with all available resources',
            scenario_type='hybrid',
            materials_multiplier=3.0,
            training_multiplier=5.0,
            retake_probability=0.03,
            includes_training=True,
            includes_mentoring=True,
            includes_practice_exams=True,
            study_time_multiplier=0.6,
            preparation_weeks=10
        ),
        CostScenario(
            name='Inactive Scenario',
            description='Inactive scenario for testing',
            scenario_type='self_study',
            materials_multiplier=1.0,
            training_multiplier=0.0,
            retake_probability=0.25,
            includes_training=False,
            includes_mentoring=False,
            includes_practice_exams=True,
            study_time_multiplier=1.2,
            preparation_weeks=16,
            is_active=False
        )
    ]
    
    for scenario in scenarios:
        db_session.add(scenario)
    db_session.commit()
    return scenarios


@pytest.fixture
def sample_cost_calculations(db_session, comprehensive_certifications, comprehensive_cost_scenarios, mock_auth_user):
    """Create sample cost calculations for testing."""
    calculations = []
    
    # Basic calculation
    calc1 = CostCalculation(
        user_id=mock_auth_user,
        name='Basic Security Path',
        description='Entry-level security certification path',
        certification_ids=[comprehensive_certifications[0].id],  # Security+
        scenario_id=comprehensive_cost_scenarios[0].id,  # Self-Study Basic
        base_currency='USD',
        target_currency='USD',
        exam_fees_total=349.0,
        materials_cost=400.0,
        training_cost=0.0,
        retake_cost=104.7,  # 349 * 0.3
        additional_costs=100.0,
        exchange_rate_used=1.0,
        estimated_study_hours=104,  # 80 * 1.3
        estimated_weeks=16,
        is_saved=True
    )
    calc1.update_totals()
    calculations.append(calc1)
    
    # Multi-certification calculation
    calc2 = CostCalculation(
        user_id=mock_auth_user,
        name='Advanced Security Path',
        description='Advanced security certification path',
        certification_ids=[comprehensive_certifications[0].id, comprehensive_certifications[2].id],  # Security+ + CISSP
        scenario_id=comprehensive_cost_scenarios[2].id,  # Intensive Bootcamp
        base_currency='USD',
        target_currency='EUR',
        exam_fees_total=1098.0,
        materials_cost=1200.0,  # 600 * 2.0
        training_cost=4392.0,  # 1098 * 4.0
        retake_cost=87.84,  # 1098 * 0.08
        additional_costs=500.0,
        exchange_rate_used=0.92,
        estimated_study_hours=196,  # (80 + 200) * 0.7
        estimated_weeks=6,
        is_saved=True
    )
    calc2.update_totals()
    calculations.append(calc2)
    
    # Different user calculation
    calc3 = CostCalculation(
        user_id='other_user',
        name='Other User Calculation',
        description='Calculation by different user',
        certification_ids=[comprehensive_certifications[1].id],  # CEH
        scenario_id=comprehensive_cost_scenarios[1].id,  # Self-Study Premium
        base_currency='USD',
        target_currency='GBP',
        exam_fees_total=1199.0,
        materials_cost=1798.5,  # 1199 * 1.5
        training_cost=0.0,
        retake_cost=239.8,  # 1199 * 0.2
        additional_costs=200.0,
        exchange_rate_used=0.79,
        estimated_study_hours=132,  # 120 * 1.1
        estimated_weeks=14,
        is_saved=False
    )
    calc3.update_totals()
    calculations.append(calc3)
    
    for calc in calculations:
        db_session.add(calc)
    db_session.commit()
    return calculations


@pytest.fixture
def sample_cost_history(db_session, comprehensive_certifications):
    """Create sample cost history for testing."""
    histories = []
    
    # Recent price increase
    history1 = CostHistory(
        certification_id=comprehensive_certifications[0].id,  # Security+
        cost=379.0,
        currency='USD',
        source='vendor_website',
        effective_date=datetime.utcnow() - timedelta(days=1),
        previous_cost=349.0,
        change_reason='Annual price increase',
        is_verified=True,
        verified_by='admin_user',
        verified_date=datetime.utcnow()
    )
    history1.calculate_change_percentage()
    histories.append(history1)
    
    # Historical price change
    history2 = CostHistory(
        certification_id=comprehensive_certifications[2].id,  # CISSP
        cost=749.0,
        currency='USD',
        source='api_update',
        effective_date=datetime.utcnow() - timedelta(days=30),
        previous_cost=699.0,
        change_reason='Market adjustment',
        is_verified=True,
        verified_by='system',
        verified_date=datetime.utcnow() - timedelta(days=29)
    )
    history2.calculate_change_percentage()
    histories.append(history2)
    
    # Unverified price report
    history3 = CostHistory(
        certification_id=comprehensive_certifications[1].id,  # CEH
        cost=1299.0,
        currency='USD',
        source='user_report',
        effective_date=datetime.utcnow() - timedelta(hours=2),
        previous_cost=1199.0,
        change_reason='Reported price increase',
        is_verified=False
    )
    history3.calculate_change_percentage()
    histories.append(history3)
    
    for history in histories:
        db_session.add(history)
    db_session.commit()
    return histories


@pytest.fixture
def cost_calculator_service(db_session):
    """Create CostCalculatorService instance for testing."""
    return CostCalculatorService(db_session)


@pytest.fixture
def mock_external_api():
    """Mock external API responses for testing."""
    with patch('requests.get') as mock_get:
        mock_response = Mock()
        mock_response.json.return_value = {
            'rates': {
                'EUR': 0.92,
                'GBP': 0.79,
                'JPY': 150.0,
                'CAD': 1.35,
                'AUD': 1.52,
                'CHF': 0.91,
                'CNY': 7.25,
                'INR': 83.15,
                'NZD': 1.65
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        yield mock_get


@pytest.fixture
def temp_file():
    """Create temporary file for testing file operations."""
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
        yield f.name
    os.unlink(f.name)


@pytest.fixture(autouse=True)
def reset_test_state(db_session):
    """Reset test state before each test."""
    # This fixture runs before each test to ensure clean state
    yield
    # Cleanup after test if needed
    db_session.rollback()


# Pytest markers for test categorization
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.api = pytest.mark.api
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.performance = pytest.mark.performance
pytest.mark.security = pytest.mark.security
