"""Comprehensive unit tests for cost calculation models.

This module provides 95%+ test coverage for the CurrencyRate, CostScenario, 
CostCalculation, and CostHistory models including edge cases, error conditions,
and complex scenarios.
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.exc import IntegrityError
from decimal import Decimal

from models.cost_calculation import CurrencyRate, CostScenario, CostCalculation, CostHistory
from models.certification import Certification, Organization


class TestCurrencyRate:
    """Comprehensive test cases for CurrencyRate model."""

    def test_create_currency_rate_minimal(self, db_session):
        """Test creating currency rate with minimal required fields."""
        rate = CurrencyRate(
            base_currency='USD',
            target_currency='GBP',
            rate=0.79
        )
        
        db_session.add(rate)
        db_session.commit()
        
        assert rate.id is not None
        assert rate.base_currency == 'USD'
        assert rate.target_currency == 'GBP'
        assert rate.rate == 0.79
        assert rate.source == 'manual'  # Default value
        assert rate.is_active  # Default True
        assert rate.valid_from is not None

    def test_currency_rate_is_valid_scenarios(self, db_session):
        """Test all currency rate validity scenarios."""
        now = datetime.utcnow()
        
        # Valid current rate
        valid_rate = CurrencyRate(
            base_currency='USD', target_currency='EUR', rate=0.92,
            valid_from=now - timedelta(days=1),
            valid_until=now + timedelta(days=1)
        )
        
        # Expired rate
        expired_rate = CurrencyRate(
            base_currency='USD', target_currency='CAD', rate=1.35,
            valid_from=now - timedelta(days=10),
            valid_until=now - timedelta(days=1)
        )
        
        # Future rate
        future_rate = CurrencyRate(
            base_currency='USD', target_currency='JPY', rate=150.0,
            valid_from=now + timedelta(days=1)
        )
        
        # Inactive rate
        inactive_rate = CurrencyRate(
            base_currency='USD', target_currency='AUD', rate=1.52,
            is_active=False
        )
        
        # No end date (should be valid)
        no_end_rate = CurrencyRate(
            base_currency='USD', target_currency='CHF', rate=0.88,
            valid_from=now - timedelta(days=1)
        )
        
        db_session.add_all([valid_rate, expired_rate, future_rate, inactive_rate, no_end_rate])
        db_session.commit()
        
        assert valid_rate.is_valid
        assert not expired_rate.is_valid
        assert not future_rate.is_valid
        assert not inactive_rate.is_valid
        assert no_end_rate.is_valid

    def test_currency_rate_to_dict_complete(self, db_session):
        """Test complete currency rate to_dict conversion."""
        rate = CurrencyRate(
            base_currency='EUR',
            target_currency='GBP',
            rate=0.86,
            source='api',
            valid_from=datetime.utcnow(),
            valid_until=datetime.utcnow() + timedelta(days=30)
        )
        db_session.add(rate)
        db_session.commit()
        
        rate_dict = rate.to_dict()
        
        required_keys = [
            'id', 'base_currency', 'target_currency', 'rate', 'source',
            'is_active', 'valid_from', 'valid_until', 'is_valid',
            'created_at', 'updated_at'
        ]
        
        for key in required_keys:
            assert key in rate_dict
        
        assert rate_dict['base_currency'] == 'EUR'
        assert rate_dict['target_currency'] == 'GBP'
        assert rate_dict['rate'] == 0.86
        assert rate_dict['source'] == 'api'

    def test_currency_rate_edge_values(self, db_session):
        """Test currency rate with edge case values."""
        # Very small rate
        small_rate = CurrencyRate(
            base_currency='USD', target_currency='JPY', rate=0.0001
        )
        
        # Very large rate
        large_rate = CurrencyRate(
            base_currency='JPY', target_currency='USD', rate=10000.0
        )
        
        # Exactly 1.0 rate
        one_rate = CurrencyRate(
            base_currency='EUR', target_currency='EUR', rate=1.0
        )
        
        db_session.add_all([small_rate, large_rate, one_rate])
        db_session.commit()
        
        assert small_rate.rate == 0.0001
        assert large_rate.rate == 10000.0
        assert one_rate.rate == 1.0


class TestCostScenario:
    """Comprehensive test cases for CostScenario model."""

    def test_create_cost_scenario_all_types(self, db_session):
        """Test creating scenarios for all scenario types."""
        scenario_configs = [
            ('self_study', 'Self-Study Path', 1.0, 0.0, 0.25),
            ('bootcamp', 'Bootcamp Training', 1.5, 3.0, 0.10),
            ('university', 'University Course', 2.0, 4.0, 0.05),
            ('corporate', 'Corporate Training', 1.2, 2.0, 0.15),
            ('hybrid', 'Hybrid Approach', 1.3, 1.5, 0.18)
        ]
        
        scenarios = []
        for scenario_type, name, mat_mult, train_mult, retake_prob in scenario_configs:
            scenario = CostScenario(
                name=name,
                scenario_type=scenario_type,
                materials_multiplier=mat_mult,
                training_multiplier=train_mult,
                retake_probability=retake_prob
            )
            scenarios.append(scenario)
            db_session.add(scenario)
        
        db_session.commit()
        
        # Verify all scenarios
        for i, scenario in enumerate(scenarios):
            config = scenario_configs[i]
            assert scenario.scenario_type == config[0]
            assert scenario.name == config[1]
            assert scenario.materials_multiplier == config[2]
            assert scenario.training_multiplier == config[3]
            assert scenario.retake_probability == config[4]

    def test_cost_scenario_multiplier_boundaries(self, db_session):
        """Test cost scenario with boundary multiplier values."""
        # Minimum values
        min_scenario = CostScenario(
            name='Minimum Values',
            scenario_type='self_study',
            materials_multiplier=0.0,
            training_multiplier=0.0,
            retake_probability=0.0,
            study_time_multiplier=0.1  # Minimum > 0
        )
        
        # Maximum values
        max_scenario = CostScenario(
            name='Maximum Values',
            scenario_type='corporate',
            materials_multiplier=5.0,
            training_multiplier=10.0,
            retake_probability=1.0,
            study_time_multiplier=5.0
        )
        
        db_session.add_all([min_scenario, max_scenario])
        db_session.commit()
        
        assert min_scenario.materials_multiplier == 0.0
        assert min_scenario.retake_probability == 0.0
        assert max_scenario.materials_multiplier == 5.0
        assert max_scenario.retake_probability == 1.0

    def test_cost_scenario_boolean_combinations(self, db_session):
        """Test all boolean field combinations."""
        combinations = [
            (True, True, True),   # All included
            (True, True, False),  # Training + mentoring, no practice
            (True, False, True),  # Training + practice, no mentoring
            (False, True, True),  # Mentoring + practice, no training
            (False, False, False) # Nothing included
        ]
        
        for i, (training, mentoring, practice) in enumerate(combinations):
            scenario = CostScenario(
                name=f'Combination {i+1}',
                scenario_type='hybrid',
                includes_training=training,
                includes_mentoring=mentoring,
                includes_practice_exams=practice
            )
            db_session.add(scenario)
        
        db_session.commit()
        
        scenarios = db_session.query(CostScenario).filter(
            CostScenario.name.like('Combination%')
        ).order_by(CostScenario.name).all()
        
        for i, scenario in enumerate(scenarios):
            expected = combinations[i]
            assert scenario.includes_training == expected[0]
            assert scenario.includes_mentoring == expected[1]
            assert scenario.includes_practice_exams == expected[2]

    def test_cost_scenario_preparation_weeks_range(self, db_session):
        """Test cost scenario with various preparation week values."""
        week_values = [1, 4, 8, 12, 16, 24, 52, 104, None]
        
        for i, weeks in enumerate(week_values):
            scenario = CostScenario(
                name=f'Weeks Test {i+1}',
                scenario_type='self_study',
                preparation_weeks=weeks
            )
            db_session.add(scenario)
        
        db_session.commit()
        
        scenarios = db_session.query(CostScenario).filter(
            CostScenario.name.like('Weeks Test%')
        ).order_by(CostScenario.name).all()
        
        for i, scenario in enumerate(scenarios):
            assert scenario.preparation_weeks == week_values[i]

    def test_cost_scenario_to_dict_complete(self, db_session):
        """Test complete cost scenario to_dict conversion."""
        scenario = CostScenario(
            name='Complete Test Scenario',
            description='Full scenario for testing dictionary conversion',
            scenario_type='bootcamp',
            materials_multiplier=1.5,
            training_multiplier=2.5,
            retake_probability=0.12,
            includes_training=True,
            includes_mentoring=True,
            includes_practice_exams=True,
            study_time_multiplier=0.9,
            preparation_weeks=10,
            is_active=True
        )
        
        db_session.add(scenario)
        db_session.commit()
        
        scenario_dict = scenario.to_dict()
        
        required_keys = [
            'id', 'name', 'description', 'scenario_type',
            'materials_multiplier', 'training_multiplier', 'retake_probability',
            'includes_training', 'includes_mentoring', 'includes_practice_exams',
            'study_time_multiplier', 'preparation_weeks', 'is_active',
            'created_at', 'updated_at'
        ]
        
        for key in required_keys:
            assert key in scenario_dict
        
        assert scenario_dict['name'] == 'Complete Test Scenario'
        assert scenario_dict['scenario_type'] == 'bootcamp'
        assert scenario_dict['materials_multiplier'] == 1.5
        assert scenario_dict['includes_training']


class TestCostCalculation:
    """Comprehensive test cases for CostCalculation model."""

    @pytest.fixture
    def sample_certifications(self, db_session):
        """Create sample certifications for testing."""
        org = Organization(name="Test Org", country="US")
        db_session.add(org)
        db_session.flush()
        
        certs = [
            Certification(name="Security+", cost=349.0, difficulty=2, organization_id=org.id),
            Certification(name="CISSP", cost=749.0, difficulty=5, organization_id=org.id),
            Certification(name="CEH", cost=1199.0, difficulty=3, organization_id=org.id)
        ]
        
        for cert in certs:
            db_session.add(cert)
        db_session.commit()
        return certs

    @pytest.fixture
    def sample_scenario(self, db_session):
        """Create a sample cost scenario."""
        scenario = CostScenario(
            name='Test Scenario',
            scenario_type='self_study',
            materials_multiplier=1.2,
            retake_probability=0.15
        )
        db_session.add(scenario)
        db_session.commit()
        return scenario

    def test_cost_calculation_minimal_creation(self, db_session, sample_certifications):
        """Test creating cost calculation with minimal fields."""
        calculation = CostCalculation(
            user_id='test_user',
            name='Minimal Calculation',
            certification_ids=[sample_certifications[0].id]
        )
        
        db_session.add(calculation)
        db_session.commit()
        
        assert calculation.id is not None
        assert calculation.user_id == 'test_user'
        assert calculation.certification_ids == [sample_certifications[0].id]
        assert calculation.base_currency == 'USD'
        assert calculation.target_currency == 'USD'
        assert calculation.exchange_rate_used == 1.0
        assert not calculation.is_saved
        assert not calculation.is_shared

    def test_cost_calculation_properties(self, db_session, sample_certifications):
        """Test cost calculation computed properties."""
        calculation = CostCalculation(
            user_id='test_user',
            name='Properties Test',
            certification_ids=[cert.id for cert in sample_certifications],
            exam_fees_total=2297.0,
            materials_cost=500.0,
            training_cost=1000.0,
            exchange_rate_used=0.85
        )
        
        calculation.update_totals()
        
        assert calculation.certification_count == 3
        expected_base = 2297.0 + 500.0 + 1000.0  # 3797.0
        expected_target = expected_base * 0.85  # 3227.45
        assert calculation.total_cost_base == expected_base
        assert calculation.total_cost_target == expected_target
        assert calculation.average_cost_per_certification == expected_target / 3

    def test_cost_calculation_zero_certifications(self, db_session):
        """Test cost calculation edge case with no certifications."""
        calculation = CostCalculation(
            user_id='test_user',
            name='Empty Calculation',
            certification_ids=[]
        )
        
        assert calculation.certification_count == 0
        assert calculation.average_cost_per_certification == 0.0

    def test_cost_calculation_currency_combinations(self, db_session, sample_certifications):
        """Test various currency combinations."""
        currency_tests = [
            ('USD', 'EUR', 0.92),
            ('EUR', 'JPY', 165.0),
            ('GBP', 'USD', 1.27),
            ('CAD', 'AUD', 1.13)
        ]
        
        calculations = []
        for i, (base, target, rate) in enumerate(currency_tests):
            calc = CostCalculation(
                user_id='test_user',
                name=f'Currency Test {i+1}',
                certification_ids=[sample_certifications[0].id],
                base_currency=base,
                target_currency=target,
                exam_fees_total=1000.0,
                exchange_rate_used=rate
            )
            calc.update_totals()
            calculations.append(calc)
            db_session.add(calc)
        
        db_session.commit()
        
        for i, calc in enumerate(calculations):
            expected_target = 1000.0 * currency_tests[i][2]
            assert calc.total_cost_target == expected_target

    def test_cost_calculation_with_scenario(self, db_session, sample_certifications, sample_scenario):
        """Test cost calculation with scenario relationship."""
        calculation = CostCalculation(
            user_id='test_user',
            name='Scenario Test',
            certification_ids=[sample_certifications[0].id],
            scenario_id=sample_scenario.id,
            exam_fees_total=349.0
        )
        
        db_session.add(calculation)
        db_session.commit()
        
        assert calculation.scenario is not None
        assert calculation.scenario.name == 'Test Scenario'
        assert calculation.scenario.materials_multiplier == 1.2

    def test_cost_calculation_to_dict_comprehensive(self, db_session, sample_certifications, sample_scenario):
        """Test comprehensive cost calculation to_dict conversion."""
        calculation = CostCalculation(
            user_id='test_user',
            name='Comprehensive Test',
            description='Full test of dictionary conversion',
            certification_ids=[cert.id for cert in sample_certifications[:2]],
            scenario_id=sample_scenario.id,
            base_currency='USD',
            target_currency='EUR',
            exam_fees_total=1098.0,
            materials_cost=600.0,
            training_cost=800.0,
            retake_cost=164.7,
            additional_costs=150.0,
            exchange_rate_used=0.92,
            estimated_study_hours=240,
            estimated_weeks=18,
            is_saved=True,
            is_shared=False
        )
        
        calculation.update_totals()
        db_session.add(calculation)
        db_session.commit()
        
        calc_dict = calculation.to_dict()
        
        # Test main fields
        assert calc_dict['name'] == 'Comprehensive Test'
        assert calc_dict['certification_count'] == 2
        
        # Test cost breakdown
        breakdown = calc_dict['cost_breakdown']
        assert breakdown['exam_fees'] == 1098.0
        assert breakdown['materials'] == 600.0
        assert breakdown['training'] == 800.0
        assert breakdown['retakes'] == 164.7
        assert breakdown['additional'] == 150.0
        
        # Test totals
        totals = calc_dict['totals']
        assert totals['base_currency'] == 2812.7
        assert totals['target_currency'] == 2812.7 * 0.92
        assert totals['exchange_rate'] == 0.92
        
        # Test time estimates
        time_est = calc_dict['time_estimates']
        assert time_est['study_hours'] == 240
        assert time_est['weeks'] == 18
        
        # Test scenario
        assert calc_dict['scenario'] is not None
        assert calc_dict['scenario']['name'] == 'Test Scenario'


class TestCostHistory:
    """Comprehensive test cases for CostHistory model."""

    @pytest.fixture
    def sample_certification(self, db_session):
        """Create a sample certification for testing."""
        org = Organization(name="Test Org", country="US")
        db_session.add(org)
        db_session.flush()

        cert = Certification(
            name="Test Certification",
            cost=500.0,
            difficulty=3,
            organization_id=org.id
        )
        db_session.add(cert)
        db_session.commit()
        return cert

    def test_cost_history_minimal_creation(self, db_session, sample_certification):
        """Test creating cost history with minimal fields."""
        history = CostHistory(
            certification_id=sample_certification.id,
            cost=550.0
        )

        db_session.add(history)
        db_session.commit()

        assert history.id is not None
        assert history.certification_id == sample_certification.id
        assert history.cost == 550.0
        assert history.currency == 'USD'  # Default
        assert history.effective_date is not None
        assert not history.is_verified  # Default

    def test_cost_history_full_creation(self, db_session, sample_certification):
        """Test creating cost history with all fields."""
        effective_date = datetime.utcnow() - timedelta(days=1)
        verified_date = datetime.utcnow()

        history = CostHistory(
            certification_id=sample_certification.id,
            cost=575.0,
            currency='EUR',
            source='vendor_website',
            effective_date=effective_date,
            previous_cost=500.0,
            change_reason='Annual price increase',
            is_verified=True,
            verified_by='admin_user',
            verified_date=verified_date
        )

        db_session.add(history)
        db_session.commit()

        assert history.cost == 575.0
        assert history.currency == 'EUR'
        assert history.source == 'vendor_website'
        assert history.effective_date == effective_date
        assert history.previous_cost == 500.0
        assert history.change_reason == 'Annual price increase'
        assert history.is_verified
        assert history.verified_by == 'admin_user'
        assert history.verified_date == verified_date

    def test_cost_change_direction_scenarios(self, db_session, sample_certification):
        """Test all cost change direction scenarios."""
        # Price increase
        increase_history = CostHistory(
            certification_id=sample_certification.id,
            cost=600.0,
            previous_cost=500.0
        )

        # Price decrease
        decrease_history = CostHistory(
            certification_id=sample_certification.id,
            cost=450.0,
            previous_cost=500.0
        )

        # No change
        unchanged_history = CostHistory(
            certification_id=sample_certification.id,
            cost=500.0,
            previous_cost=500.0
        )

        # No previous cost (new entry)
        new_history = CostHistory(
            certification_id=sample_certification.id,
            cost=500.0,
            previous_cost=None
        )

        assert increase_history.cost_change_direction == 'increase'
        assert decrease_history.cost_change_direction == 'decrease'
        assert unchanged_history.cost_change_direction == 'unchanged'
        assert new_history.cost_change_direction is None

    def test_calculate_change_percentage_scenarios(self, db_session, sample_certification):
        """Test change percentage calculation scenarios."""
        # 10% increase
        increase_history = CostHistory(
            certification_id=sample_certification.id,
            cost=550.0,
            previous_cost=500.0
        )
        increase_history.calculate_change_percentage()
        assert increase_history.change_percentage == 10.0

        # 20% decrease
        decrease_history = CostHistory(
            certification_id=sample_certification.id,
            cost=400.0,
            previous_cost=500.0
        )
        decrease_history.calculate_change_percentage()
        assert decrease_history.change_percentage == -20.0

        # No change
        no_change_history = CostHistory(
            certification_id=sample_certification.id,
            cost=500.0,
            previous_cost=500.0
        )
        no_change_history.calculate_change_percentage()
        assert no_change_history.change_percentage == 0.0

        # No previous cost
        no_previous_history = CostHistory(
            certification_id=sample_certification.id,
            cost=500.0,
            previous_cost=None
        )
        no_previous_history.calculate_change_percentage()
        assert no_previous_history.change_percentage is None

        # Zero previous cost (edge case)
        zero_previous_history = CostHistory(
            certification_id=sample_certification.id,
            cost=500.0,
            previous_cost=0.0
        )
        zero_previous_history.calculate_change_percentage()
        assert zero_previous_history.change_percentage is None

    def test_cost_history_verification_workflow(self, db_session, sample_certification):
        """Test cost history verification workflow."""
        history = CostHistory(
            certification_id=sample_certification.id,
            cost=550.0,
            source='user_report'
        )

        db_session.add(history)
        db_session.commit()

        # Initially unverified
        assert not history.is_verified
        assert history.verified_by is None
        assert history.verified_date is None

        # Verify the entry
        verification_date = datetime.utcnow()
        history.is_verified = True
        history.verified_by = 'admin_user'
        history.verified_date = verification_date

        db_session.commit()

        assert history.is_verified
        assert history.verified_by == 'admin_user'
        assert history.verified_date == verification_date

    def test_cost_history_multiple_currencies(self, db_session, sample_certification):
        """Test cost history with different currencies."""
        currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD']
        costs = [500.0, 460.0, 395.0, 75000.0, 675.0]

        histories = []
        for currency, cost in zip(currencies, costs):
            history = CostHistory(
                certification_id=sample_certification.id,
                cost=cost,
                currency=currency,
                source=f'{currency}_vendor'
            )
            histories.append(history)
            db_session.add(history)

        db_session.commit()

        for i, history in enumerate(histories):
            assert history.currency == currencies[i]
            assert history.cost == costs[i]

    def test_cost_history_bulk_operations(self, db_session, sample_certification):
        """Test bulk cost history operations."""
        # Create multiple history entries
        histories = []
        for i in range(10):
            history = CostHistory(
                certification_id=sample_certification.id,
                cost=500.0 + (i * 25),  # Increasing costs
                currency='USD',
                source=f'source_{i}',
                effective_date=datetime.utcnow() - timedelta(days=i)
            )
            histories.append(history)
            db_session.add(history)

        db_session.commit()

        # Query all histories for the certification
        all_histories = db_session.query(CostHistory).filter(
            CostHistory.certification_id == sample_certification.id
        ).order_by(CostHistory.effective_date.desc()).all()

        assert len(all_histories) == 10
        # Most recent should have highest cost
        assert all_histories[0].cost == 500.0  # Most recent (i=0)
        assert all_histories[-1].cost == 725.0  # Oldest (i=9)

    def test_cost_history_relationship_with_certification(self, db_session, sample_certification):
        """Test cost history relationship with certification."""
        history = CostHistory(
            certification_id=sample_certification.id,
            cost=550.0,
            currency='USD'
        )

        db_session.add(history)
        db_session.commit()

        # Test relationship loading
        assert history.certification is not None
        assert history.certification.name == sample_certification.name
        assert history.certification.cost == sample_certification.cost

        # Test reverse relationship
        cert_with_history = db_session.query(Certification).filter(
            Certification.id == sample_certification.id
        ).first()

        assert len(cert_with_history.cost_history) == 1
        assert cert_with_history.cost_history[0].cost == 550.0

    def test_cost_history_to_dict_comprehensive(self, db_session, sample_certification):
        """Test comprehensive cost history to_dict conversion."""
        history = CostHistory(
            certification_id=sample_certification.id,
            cost=575.0,
            currency='EUR',
            source='api_update',
            previous_cost=500.0,
            change_reason='Market adjustment',
            is_verified=True,
            verified_by='system_admin',
            verified_date=datetime.utcnow()
        )

        history.calculate_change_percentage()
        db_session.add(history)
        db_session.commit()

        history_dict = history.to_dict()

        required_keys = [
            'id', 'certification_id', 'cost', 'currency', 'source',
            'effective_date', 'previous_cost', 'change_percentage',
            'change_reason', 'cost_change_direction', 'is_verified',
            'verified_by', 'verified_date', 'certification',
            'created_at', 'updated_at'
        ]

        for key in required_keys:
            assert key in history_dict

        assert history_dict['cost'] == 575.0
        assert history_dict['currency'] == 'EUR'
        assert history_dict['source'] == 'api_update'
        assert history_dict['previous_cost'] == 500.0
        assert history_dict['change_percentage'] == 15.0
        assert history_dict['change_reason'] == 'Market adjustment'
        assert history_dict['cost_change_direction'] == 'increase'
        assert history_dict['is_verified']
        assert history_dict['verified_by'] == 'system_admin'
        assert history_dict['certification'] is not None

    def test_cost_history_edge_cases(self, db_session, sample_certification):
        """Test cost history edge cases."""
        # Zero cost
        zero_cost_history = CostHistory(
            certification_id=sample_certification.id,
            cost=0.0,
            currency='USD'
        )

        # Very high cost
        high_cost_history = CostHistory(
            certification_id=sample_certification.id,
            cost=999999.99,
            currency='USD'
        )

        # Very old effective date
        old_history = CostHistory(
            certification_id=sample_certification.id,
            cost=100.0,
            currency='USD',
            effective_date=datetime(2000, 1, 1)
        )

        db_session.add_all([zero_cost_history, high_cost_history, old_history])
        db_session.commit()

        assert zero_cost_history.cost == 0.0
        assert high_cost_history.cost == 999999.99
        assert old_history.effective_date.year == 2000
