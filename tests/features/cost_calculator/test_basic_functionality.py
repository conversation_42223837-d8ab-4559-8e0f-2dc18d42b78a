"""Basic functionality tests for cost calculator to verify setup.

This module provides basic tests to verify that the cost calculator
implementation works correctly and achieves the required coverage.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Test basic imports work
def test_imports():
    """Test that all cost calculator modules can be imported."""
    try:
        from models.cost_calculation import CurrencyRate, CostScenario, CostCalculation, CostHistory
        from services.cost_calculator import CostCalculatorService
        from schemas.cost_calculation import (
            CostCalculationCreate, CostCalculationResponse, CostScenarioCreate,
            CurrencyRateCreate, CostComparisonRequest
        )
        assert True
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")


def test_currency_rate_basic():
    """Test basic CurrencyRate functionality."""
    from models.cost_calculation import CurrencyRate
    
    rate = CurrencyRate(
        base_currency='USD',
        target_currency='EUR',
        rate=0.92,
        source='test'
    )
    
    assert rate.base_currency == 'USD'
    assert rate.target_currency == 'EUR'
    assert rate.rate == 0.92
    assert rate.source == 'test'
    assert rate.is_active  # Default True


def test_cost_scenario_basic():
    """Test basic CostScenario functionality."""
    from models.cost_calculation import CostScenario
    
    scenario = CostScenario(
        name='Test Scenario',
        scenario_type='self_study',
        materials_multiplier=1.2,
        retake_probability=0.15
    )
    
    assert scenario.name == 'Test Scenario'
    assert scenario.scenario_type == 'self_study'
    assert scenario.materials_multiplier == 1.2
    assert scenario.retake_probability == 0.15


def test_cost_calculation_basic():
    """Test basic CostCalculation functionality."""
    from models.cost_calculation import CostCalculation
    
    calculation = CostCalculation(
        user_id='test_user',
        name='Test Calculation',
        certification_ids=[1, 2],
        exam_fees_total=1000.0,
        materials_cost=500.0
    )
    
    assert calculation.user_id == 'test_user'
    assert calculation.name == 'Test Calculation'
    assert calculation.certification_ids == [1, 2]
    assert calculation.exam_fees_total == 1000.0
    assert calculation.materials_cost == 500.0


def test_cost_calculation_update_totals():
    """Test CostCalculation total calculation."""
    from models.cost_calculation import CostCalculation
    
    calculation = CostCalculation(
        user_id='test_user',
        name='Test Calculation',
        certification_ids=[1],
        exam_fees_total=500.0,
        materials_cost=300.0,
        training_cost=200.0,
        retake_cost=50.0,
        additional_costs=100.0,
        exchange_rate_used=0.9
    )
    
    calculation.update_totals()
    
    expected_base = 500.0 + 300.0 + 200.0 + 50.0 + 100.0  # 1150.0
    expected_target = expected_base * 0.9  # 1035.0
    
    assert calculation.total_cost_base == expected_base
    assert calculation.total_cost_target == expected_target


def test_cost_calculation_properties():
    """Test CostCalculation computed properties."""
    from models.cost_calculation import CostCalculation
    
    calculation = CostCalculation(
        user_id='test_user',
        name='Test Calculation',
        certification_ids=[1, 2, 3],
        total_cost_target=1500.0
    )
    
    assert calculation.certification_count == 3
    assert calculation.average_cost_per_certification == 500.0


def test_cost_calculation_zero_certifications():
    """Test CostCalculation with zero certifications."""
    from models.cost_calculation import CostCalculation
    
    calculation = CostCalculation(
        user_id='test_user',
        name='Empty Calculation',
        certification_ids=[]
    )
    
    assert calculation.certification_count == 0
    assert calculation.average_cost_per_certification == 0.0


def test_cost_history_basic():
    """Test basic CostHistory functionality."""
    from models.cost_calculation import CostHistory
    
    history = CostHistory(
        certification_id=1,
        cost=500.0,
        currency='USD',
        previous_cost=450.0
    )
    
    assert history.certification_id == 1
    assert history.cost == 500.0
    assert history.currency == 'USD'
    assert history.previous_cost == 450.0


def test_cost_history_change_direction():
    """Test CostHistory change direction calculation."""
    from models.cost_calculation import CostHistory
    
    # Price increase
    increase_history = CostHistory(
        certification_id=1,
        cost=600.0,
        previous_cost=500.0
    )
    assert increase_history.cost_change_direction == 'increase'
    
    # Price decrease
    decrease_history = CostHistory(
        certification_id=1,
        cost=400.0,
        previous_cost=500.0
    )
    assert decrease_history.cost_change_direction == 'decrease'
    
    # No change
    unchanged_history = CostHistory(
        certification_id=1,
        cost=500.0,
        previous_cost=500.0
    )
    assert unchanged_history.cost_change_direction == 'unchanged'
    
    # No previous cost
    new_history = CostHistory(
        certification_id=1,
        cost=500.0
    )
    assert new_history.cost_change_direction is None


def test_cost_history_calculate_change_percentage():
    """Test CostHistory change percentage calculation."""
    from models.cost_calculation import CostHistory
    
    # 10% increase
    history = CostHistory(
        certification_id=1,
        cost=550.0,
        previous_cost=500.0
    )
    history.calculate_change_percentage()
    assert history.change_percentage == 10.0
    
    # 20% decrease
    history2 = CostHistory(
        certification_id=1,
        cost=400.0,
        previous_cost=500.0
    )
    history2.calculate_change_percentage()
    assert history2.change_percentage == -20.0
    
    # No previous cost
    history3 = CostHistory(
        certification_id=1,
        cost=500.0
    )
    history3.calculate_change_percentage()
    assert history3.change_percentage is None


def test_currency_rate_is_valid():
    """Test CurrencyRate validity checking."""
    from models.cost_calculation import CurrencyRate
    
    now = datetime.utcnow()
    
    # Valid rate
    valid_rate = CurrencyRate(
        base_currency='USD',
        target_currency='EUR',
        rate=0.92,
        valid_from=now - timedelta(days=1),
        valid_until=now + timedelta(days=1)
    )
    assert valid_rate.is_valid
    
    # Expired rate
    expired_rate = CurrencyRate(
        base_currency='USD',
        target_currency='EUR',
        rate=0.92,
        valid_from=now - timedelta(days=10),
        valid_until=now - timedelta(days=1)
    )
    assert not expired_rate.is_valid
    
    # Inactive rate
    inactive_rate = CurrencyRate(
        base_currency='USD',
        target_currency='EUR',
        rate=0.92,
        is_active=False
    )
    assert not inactive_rate.is_valid


def test_model_to_dict_methods():
    """Test to_dict methods for all models."""
    from models.cost_calculation import CurrencyRate, CostScenario, CostCalculation, CostHistory
    
    # Test CurrencyRate.to_dict()
    rate = CurrencyRate(
        base_currency='USD',
        target_currency='EUR',
        rate=0.92
    )
    rate_dict = rate.to_dict()
    assert 'base_currency' in rate_dict
    assert 'target_currency' in rate_dict
    assert 'rate' in rate_dict
    assert rate_dict['base_currency'] == 'USD'
    
    # Test CostScenario.to_dict()
    scenario = CostScenario(
        name='Test Scenario',
        scenario_type='self_study'
    )
    scenario_dict = scenario.to_dict()
    assert 'name' in scenario_dict
    assert 'scenario_type' in scenario_dict
    assert scenario_dict['name'] == 'Test Scenario'
    
    # Test CostCalculation.to_dict()
    calculation = CostCalculation(
        user_id='test_user',
        name='Test Calculation',
        certification_ids=[1, 2]
    )
    calc_dict = calculation.to_dict()
    assert 'user_id' in calc_dict
    assert 'name' in calc_dict
    assert 'certification_count' in calc_dict
    assert calc_dict['user_id'] == 'test_user'
    
    # Test CostHistory.to_dict()
    history = CostHistory(
        certification_id=1,
        cost=500.0
    )
    history_dict = history.to_dict()
    assert 'certification_id' in history_dict
    assert 'cost' in history_dict
    assert history_dict['certification_id'] == 1


@patch('services.cost_calculator.requests.get')
def test_cost_calculator_service_basic(mock_get):
    """Test basic CostCalculatorService functionality."""
    from services.cost_calculator import CostCalculatorService
    
    # Mock database session
    mock_db = Mock()
    service = CostCalculatorService(mock_db)
    
    # Test get_exchange_rate with same currency
    rate = service.get_exchange_rate('USD', 'USD')
    assert rate == 1.0
    
    # Test _estimate_study_hours
    mock_cert = Mock()
    mock_cert.difficulty = 3
    
    hours = service._estimate_study_hours(mock_cert, None)
    assert hours == 120  # 3 * 40
    
    # Test with scenario multiplier
    mock_scenario = Mock()
    mock_scenario.study_time_multiplier = 1.5
    
    hours_with_scenario = service._estimate_study_hours(mock_cert, mock_scenario)
    assert hours_with_scenario == 180  # 120 * 1.5


def test_schema_validation():
    """Test basic schema validation."""
    from schemas.cost_calculation import CostCalculationCreate, CurrencyCode, ScenarioType
    
    # Test valid data
    valid_data = {
        'name': 'Test Calculation',
        'certification_ids': [1, 2],
        'base_currency': CurrencyCode.USD,
        'target_currency': CurrencyCode.EUR,
        'materials_cost': 500.0
    }
    
    try:
        calc_create = CostCalculationCreate(**valid_data)
        assert calc_create.name == 'Test Calculation'
        assert calc_create.base_currency == CurrencyCode.USD
        assert calc_create.materials_cost == 500.0
    except Exception as e:
        pytest.fail(f"Schema validation failed: {e}")


def test_enum_values():
    """Test enum values are correctly defined."""
    from schemas.cost_calculation import CurrencyCode, ScenarioType
    
    # Test CurrencyCode enum
    assert CurrencyCode.USD == 'USD'
    assert CurrencyCode.EUR == 'EUR'
    assert CurrencyCode.GBP == 'GBP'
    
    # Test ScenarioType enum
    assert ScenarioType.SELF_STUDY == 'self_study'
    assert ScenarioType.BOOTCAMP == 'bootcamp'
    assert ScenarioType.UNIVERSITY == 'university'


def test_coverage_edge_cases():
    """Test edge cases to improve coverage."""
    from models.cost_calculation import CostCalculation, CostHistory
    
    # Test CostCalculation with None scenario
    calc = CostCalculation(
        user_id='test_user',
        name='No Scenario Calc',
        certification_ids=[1],
        scenario_id=None
    )
    assert calc.scenario_id is None
    
    # Test CostHistory with zero previous cost
    history = CostHistory(
        certification_id=1,
        cost=500.0,
        previous_cost=0.0
    )
    history.calculate_change_percentage()
    assert history.change_percentage is None  # Division by zero case
    
    # Test string representations
    calc_repr = repr(calc)
    assert 'CostCalculation' in calc_repr
    
    history_repr = repr(history)
    assert 'CostHistory' in history_repr


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
