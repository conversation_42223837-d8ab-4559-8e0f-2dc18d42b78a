"""End-to-end tests for cost calculator functionality using Playwright.

This module provides comprehensive E2E tests covering the complete user journey
for cost calculation, comparison, and analysis features.
"""

import pytest
from playwright.sync_api import Page, expect
import json
import time


class TestCostCalculatorE2E:
    """End-to-end test cases for cost calculator functionality."""

    @pytest.fixture(autouse=True)
    def setup_test_data(self, page: Page):
        """Set up test data before each test."""
        # Navigate to the application
        page.goto("http://localhost:3000")
        
        # Wait for the application to load
        page.wait_for_selector("[data-testid='app-loaded']", timeout=10000)
        
        # Set up test data via API calls
        self._setup_test_certifications(page)
        self._setup_test_scenarios(page)
        self._setup_test_currency_rates(page)

    def _setup_test_certifications(self, page: Page):
        """Set up test certifications via API."""
        # This would typically be done via API calls or database seeding
        # For now, we'll assume the data exists
        pass

    def _setup_test_scenarios(self, page: Page):
        """Set up test cost scenarios via API."""
        # Create test scenarios via API
        scenarios = [
            {
                "name": "E2E Self-Study",
                "scenario_type": "self_study",
                "materials_multiplier": 1.0,
                "training_multiplier": 0.0,
                "retake_probability": 0.25
            },
            {
                "name": "E2E Bootcamp",
                "scenario_type": "bootcamp",
                "materials_multiplier": 1.5,
                "training_multiplier": 3.0,
                "retake_probability": 0.10
            }
        ]
        
        for scenario in scenarios:
            page.evaluate(f"""
                fetch('/api/v1/cost-calculator/scenarios', {{
                    method: 'POST',
                    headers: {{ 'Content-Type': 'application/json' }},
                    body: JSON.stringify({json.dumps(scenario)})
                }})
            """)

    def _setup_test_currency_rates(self, page: Page):
        """Set up test currency rates via API."""
        rates = [
            {"base_currency": "USD", "target_currency": "EUR", "rate": 0.92},
            {"base_currency": "USD", "target_currency": "GBP", "rate": 0.79}
        ]
        
        for rate in rates:
            page.evaluate(f"""
                fetch('/api/v1/cost-calculator/currency-rates', {{
                    method: 'POST',
                    headers: {{ 'Content-Type': 'application/json' }},
                    body: JSON.stringify({json.dumps(rate)})
                }})
            """)


class TestCostCalculationWorkflow:
    """Test complete cost calculation workflow."""

    def test_create_basic_cost_calculation(self, page: Page):
        """Test creating a basic cost calculation through the UI."""
        # Navigate to cost calculator page
        page.click("[data-testid='nav-cost-calculator']")
        page.wait_for_selector("[data-testid='cost-calculator-page']")
        
        # Click create new calculation button
        page.click("[data-testid='create-calculation-btn']")
        page.wait_for_selector("[data-testid='calculation-form']")
        
        # Fill in calculation details
        page.fill("[data-testid='calculation-name']", "E2E Test Calculation")
        page.fill("[data-testid='calculation-description']", "End-to-end test calculation")
        
        # Select certifications
        page.click("[data-testid='certification-selector']")
        page.click("[data-testid='certification-option-security-plus']")
        page.click("[data-testid='certification-option-cissp']")
        page.click("[data-testid='certification-selector-close']")
        
        # Select scenario
        page.click("[data-testid='scenario-selector']")
        page.click("[data-testid='scenario-option-self-study']")
        
        # Set currency
        page.click("[data-testid='target-currency-selector']")
        page.click("[data-testid='currency-option-eur']")
        
        # Fill in additional costs
        page.fill("[data-testid='materials-cost']", "500")
        page.fill("[data-testid='additional-costs']", "100")
        
        # Save calculation
        page.check("[data-testid='save-calculation']")
        
        # Submit form
        page.click("[data-testid='create-calculation-submit']")
        
        # Wait for success message
        page.wait_for_selector("[data-testid='calculation-created-success']")
        
        # Verify calculation appears in list
        page.wait_for_selector("[data-testid='calculation-list']")
        expect(page.locator("[data-testid='calculation-item']")).to_contain_text("E2E Test Calculation")
        
        # Verify cost breakdown is displayed
        expect(page.locator("[data-testid='cost-breakdown']")).to_be_visible()
        expect(page.locator("[data-testid='exam-fees']")).to_contain_text("1,098")  # Security+ + CISSP
        expect(page.locator("[data-testid='materials-cost']")).to_contain_text("500")
        expect(page.locator("[data-testid='total-cost']")).to_be_visible()

    def test_edit_cost_calculation(self, page: Page):
        """Test editing an existing cost calculation."""
        # First create a calculation (reuse previous test logic or assume it exists)
        self.test_create_basic_cost_calculation(page)
        
        # Click edit button on the calculation
        page.click("[data-testid='edit-calculation-btn']")
        page.wait_for_selector("[data-testid='calculation-edit-form']")
        
        # Update calculation name
        page.fill("[data-testid='calculation-name']", "Updated E2E Test Calculation")
        
        # Update materials cost
        page.fill("[data-testid='materials-cost']", "750")
        
        # Change target currency
        page.click("[data-testid='target-currency-selector']")
        page.click("[data-testid='currency-option-gbp']")
        
        # Save changes
        page.click("[data-testid='save-calculation-changes']")
        
        # Wait for success message
        page.wait_for_selector("[data-testid='calculation-updated-success']")
        
        # Verify changes are reflected
        expect(page.locator("[data-testid='calculation-name']")).to_contain_text("Updated E2E Test Calculation")
        expect(page.locator("[data-testid='materials-cost']")).to_contain_text("750")
        expect(page.locator("[data-testid='currency-display']")).to_contain_text("GBP")

    def test_delete_cost_calculation(self, page: Page):
        """Test deleting a cost calculation."""
        # First create a calculation
        self.test_create_basic_cost_calculation(page)
        
        # Click delete button
        page.click("[data-testid='delete-calculation-btn']")
        
        # Confirm deletion in modal
        page.wait_for_selector("[data-testid='delete-confirmation-modal']")
        page.click("[data-testid='confirm-delete-btn']")
        
        # Wait for success message
        page.wait_for_selector("[data-testid='calculation-deleted-success']")
        
        # Verify calculation is removed from list
        expect(page.locator("[data-testid='calculation-item']")).not_to_contain_text("E2E Test Calculation")

    def test_cost_calculation_with_scenario_changes(self, page: Page):
        """Test how cost calculation updates when scenario changes."""
        # Navigate to cost calculator
        page.click("[data-testid='nav-cost-calculator']")
        page.click("[data-testid='create-calculation-btn']")
        
        # Fill basic details
        page.fill("[data-testid='calculation-name']", "Scenario Test Calculation")
        page.click("[data-testid='certification-selector']")
        page.click("[data-testid='certification-option-security-plus']")
        page.click("[data-testid='certification-selector-close']")
        
        # Select self-study scenario
        page.click("[data-testid='scenario-selector']")
        page.click("[data-testid='scenario-option-self-study']")
        
        # Note the initial cost
        initial_cost = page.locator("[data-testid='total-cost']").text_content()
        
        # Change to bootcamp scenario
        page.click("[data-testid='scenario-selector']")
        page.click("[data-testid='scenario-option-bootcamp']")
        
        # Wait for cost recalculation
        page.wait_for_timeout(1000)
        
        # Verify cost has changed
        updated_cost = page.locator("[data-testid='total-cost']").text_content()
        assert initial_cost != updated_cost
        
        # Verify training cost is now included
        expect(page.locator("[data-testid='training-cost']")).not_to_contain_text("0")


class TestCostComparisonWorkflow:
    """Test cost comparison functionality."""

    def test_compare_multiple_calculations(self, page: Page):
        """Test comparing multiple cost calculations."""
        # Create multiple calculations first
        calculations = [
            {"name": "Self-Study Path", "scenario": "self-study", "materials": "400"},
            {"name": "Bootcamp Path", "scenario": "bootcamp", "materials": "600"},
            {"name": "Corporate Path", "scenario": "corporate", "materials": "500"}
        ]
        
        for calc in calculations:
            page.click("[data-testid='create-calculation-btn']")
            page.fill("[data-testid='calculation-name']", calc["name"])
            page.click("[data-testid='certification-selector']")
            page.click("[data-testid='certification-option-security-plus']")
            page.click("[data-testid='certification-selector-close']")
            page.click("[data-testid='scenario-selector']")
            page.click(f"[data-testid='scenario-option-{calc['scenario']}']")
            page.fill("[data-testid='materials-cost']", calc["materials"])
            page.click("[data-testid='create-calculation-submit']")
            page.wait_for_selector("[data-testid='calculation-created-success']")
        
        # Navigate to comparison page
        page.click("[data-testid='nav-cost-comparison']")
        page.wait_for_selector("[data-testid='comparison-page']")
        
        # Select calculations to compare
        page.check("[data-testid='compare-checkbox-self-study-path']")
        page.check("[data-testid='compare-checkbox-bootcamp-path']")
        page.check("[data-testid='compare-checkbox-corporate-path']")
        
        # Set comparison currency
        page.click("[data-testid='comparison-currency-selector']")
        page.click("[data-testid='currency-option-usd']")
        
        # Start comparison
        page.click("[data-testid='start-comparison-btn']")
        
        # Wait for comparison results
        page.wait_for_selector("[data-testid='comparison-results']")
        
        # Verify comparison table is displayed
        expect(page.locator("[data-testid='comparison-table']")).to_be_visible()
        expect(page.locator("[data-testid='comparison-row']")).to_have_count(3)
        
        # Verify summary statistics
        expect(page.locator("[data-testid='lowest-cost']")).to_be_visible()
        expect(page.locator("[data-testid='highest-cost']")).to_be_visible()
        expect(page.locator("[data-testid='average-cost']")).to_be_visible()
        
        # Verify recommendations are shown
        expect(page.locator("[data-testid='recommendations']")).to_be_visible()
        expect(page.locator("[data-testid='recommendation-item']")).to_have_count_greater_than(0)

    def test_comparison_with_different_currencies(self, page: Page):
        """Test cost comparison with different currencies."""
        # Create calculations with different currencies
        page.click("[data-testid='create-calculation-btn']")
        page.fill("[data-testid='calculation-name']", "USD Calculation")
        page.click("[data-testid='certification-selector']")
        page.click("[data-testid='certification-option-security-plus']")
        page.click("[data-testid='certification-selector-close']")
        page.click("[data-testid='target-currency-selector']")
        page.click("[data-testid='currency-option-usd']")
        page.click("[data-testid='create-calculation-submit']")
        page.wait_for_selector("[data-testid='calculation-created-success']")
        
        page.click("[data-testid='create-calculation-btn']")
        page.fill("[data-testid='calculation-name']", "EUR Calculation")
        page.click("[data-testid='certification-selector']")
        page.click("[data-testid='certification-option-security-plus']")
        page.click("[data-testid='certification-selector-close']")
        page.click("[data-testid='target-currency-selector']")
        page.click("[data-testid='currency-option-eur']")
        page.click("[data-testid='create-calculation-submit']")
        page.wait_for_selector("[data-testid='calculation-created-success']")
        
        # Compare with USD as comparison currency
        page.click("[data-testid='nav-cost-comparison']")
        page.check("[data-testid='compare-checkbox-usd-calculation']")
        page.check("[data-testid='compare-checkbox-eur-calculation']")
        page.click("[data-testid='comparison-currency-selector']")
        page.click("[data-testid='currency-option-usd']")
        page.click("[data-testid='start-comparison-btn']")
        
        # Verify both calculations are converted to USD
        page.wait_for_selector("[data-testid='comparison-results']")
        expect(page.locator("[data-testid='comparison-currency-display']")).to_contain_text("USD")
        
        # Verify exchange rate information is shown
        expect(page.locator("[data-testid='exchange-rate-info']")).to_be_visible()


class TestCurrencyManagementWorkflow:
    """Test currency management functionality."""

    def test_view_exchange_rates(self, page: Page):
        """Test viewing current exchange rates."""
        # Navigate to currency management page
        page.click("[data-testid='nav-currency-management']")
        page.wait_for_selector("[data-testid='currency-page']")
        
        # Verify exchange rates table is displayed
        expect(page.locator("[data-testid='exchange-rates-table']")).to_be_visible()
        expect(page.locator("[data-testid='rate-row']")).to_have_count_greater_than(0)
        
        # Verify rate information is displayed
        expect(page.locator("[data-testid='base-currency']")).to_be_visible()
        expect(page.locator("[data-testid='target-currency']")).to_be_visible()
        expect(page.locator("[data-testid='exchange-rate']")).to_be_visible()
        expect(page.locator("[data-testid='rate-source']")).to_be_visible()

    def test_update_exchange_rates_from_api(self, page: Page):
        """Test updating exchange rates from external API."""
        page.click("[data-testid='nav-currency-management']")
        
        # Click update rates button
        page.click("[data-testid='update-rates-btn']")
        
        # Wait for update process
        page.wait_for_selector("[data-testid='update-in-progress']")
        page.wait_for_selector("[data-testid='update-completed']", timeout=30000)
        
        # Verify success message
        expect(page.locator("[data-testid='update-success-message']")).to_be_visible()
        
        # Verify rates table is refreshed
        expect(page.locator("[data-testid='last-updated']")).to_be_visible()

    def test_manual_exchange_rate_entry(self, page: Page):
        """Test manually entering exchange rates."""
        page.click("[data-testid='nav-currency-management']")
        
        # Click add rate button
        page.click("[data-testid='add-rate-btn']")
        page.wait_for_selector("[data-testid='rate-form']")
        
        # Fill in rate details
        page.click("[data-testid='base-currency-selector']")
        page.click("[data-testid='currency-option-usd']")
        page.click("[data-testid='target-currency-selector']")
        page.click("[data-testid='currency-option-jpy']")
        page.fill("[data-testid='exchange-rate-input']", "150.0")
        page.click("[data-testid='source-selector']")
        page.click("[data-testid='source-option-manual']")
        
        # Submit rate
        page.click("[data-testid='save-rate-btn']")
        
        # Verify rate is added
        page.wait_for_selector("[data-testid='rate-added-success']")
        expect(page.locator("[data-testid='rate-row']")).to_contain_text("USD/JPY")
        expect(page.locator("[data-testid='rate-row']")).to_contain_text("150.0")


class TestScenarioManagementWorkflow:
    """Test cost scenario management functionality."""

    def test_create_custom_scenario(self, page: Page):
        """Test creating a custom cost scenario."""
        # Navigate to scenario management page
        page.click("[data-testid='nav-scenario-management']")
        page.wait_for_selector("[data-testid='scenario-page']")
        
        # Click create scenario button
        page.click("[data-testid='create-scenario-btn']")
        page.wait_for_selector("[data-testid='scenario-form']")
        
        # Fill in scenario details
        page.fill("[data-testid='scenario-name']", "Custom E2E Scenario")
        page.fill("[data-testid='scenario-description']", "Custom scenario for E2E testing")
        page.click("[data-testid='scenario-type-selector']")
        page.click("[data-testid='type-option-hybrid']")
        
        # Set multipliers
        page.fill("[data-testid='materials-multiplier']", "1.3")
        page.fill("[data-testid='training-multiplier']", "1.8")
        page.fill("[data-testid='retake-probability']", "0.15")
        page.fill("[data-testid='study-time-multiplier']", "1.1")
        page.fill("[data-testid='preparation-weeks']", "18")
        
        # Set includes options
        page.check("[data-testid='includes-training']")
        page.uncheck("[data-testid='includes-mentoring']")
        page.check("[data-testid='includes-practice-exams']")
        
        # Save scenario
        page.click("[data-testid='save-scenario-btn']")
        
        # Verify scenario is created
        page.wait_for_selector("[data-testid='scenario-created-success']")
        expect(page.locator("[data-testid='scenario-list']")).to_contain_text("Custom E2E Scenario")

    def test_edit_existing_scenario(self, page: Page):
        """Test editing an existing cost scenario."""
        # First create a scenario
        self.test_create_custom_scenario(page)
        
        # Click edit button
        page.click("[data-testid='edit-scenario-btn']")
        page.wait_for_selector("[data-testid='scenario-edit-form']")
        
        # Update scenario details
        page.fill("[data-testid='scenario-name']", "Updated Custom Scenario")
        page.fill("[data-testid='materials-multiplier']", "1.5")
        page.fill("[data-testid='retake-probability']", "0.12")
        
        # Save changes
        page.click("[data-testid='save-scenario-changes']")
        
        # Verify changes are saved
        page.wait_for_selector("[data-testid='scenario-updated-success']")
        expect(page.locator("[data-testid='scenario-name']")).to_contain_text("Updated Custom Scenario")


class TestBulkOperationsWorkflow:
    """Test bulk operations functionality."""

    def test_bulk_calculation_creation(self, page: Page):
        """Test creating multiple calculations in bulk."""
        # Navigate to bulk operations page
        page.click("[data-testid='nav-bulk-operations']")
        page.wait_for_selector("[data-testid='bulk-page']")
        
        # Click bulk calculation button
        page.click("[data-testid='bulk-calculation-btn']")
        page.wait_for_selector("[data-testid='bulk-form']")
        
        # Add multiple calculations
        page.click("[data-testid='add-calculation-btn']")
        page.fill("[data-testid='calc-name-0']", "Bulk Calculation 1")
        page.click("[data-testid='cert-selector-0']")
        page.click("[data-testid='cert-option-security-plus']")
        page.click("[data-testid='cert-selector-close-0']")
        
        page.click("[data-testid='add-calculation-btn']")
        page.fill("[data-testid='calc-name-1']", "Bulk Calculation 2")
        page.click("[data-testid='cert-selector-1']")
        page.click("[data-testid='cert-option-cissp']")
        page.click("[data-testid='cert-selector-close-1']")
        
        # Enable bulk discount
        page.check("[data-testid='apply-bulk-discount']")
        page.fill("[data-testid='bulk-discount-percentage']", "10")
        
        # Submit bulk creation
        page.click("[data-testid='create-bulk-calculations']")
        
        # Verify bulk creation success
        page.wait_for_selector("[data-testid='bulk-creation-success']")
        expect(page.locator("[data-testid='created-calculations']")).to_have_count(2)
        
        # Verify discount was applied
        expect(page.locator("[data-testid='discount-applied']")).to_contain_text("10%")


class TestResponsiveDesign:
    """Test responsive design and mobile compatibility."""

    def test_mobile_cost_calculator(self, page: Page):
        """Test cost calculator on mobile viewport."""
        # Set mobile viewport
        page.set_viewport_size({"width": 375, "height": 667})
        
        # Navigate to cost calculator
        page.click("[data-testid='mobile-menu-btn']")
        page.click("[data-testid='nav-cost-calculator']")
        
        # Verify mobile layout
        expect(page.locator("[data-testid='mobile-calculator-form']")).to_be_visible()
        
        # Test form interaction on mobile
        page.click("[data-testid='create-calculation-btn']")
        page.fill("[data-testid='calculation-name']", "Mobile Test")
        
        # Verify mobile-specific UI elements
        expect(page.locator("[data-testid='mobile-cert-selector']")).to_be_visible()
        expect(page.locator("[data-testid='mobile-scenario-selector']")).to_be_visible()

    def test_tablet_comparison_view(self, page: Page):
        """Test comparison view on tablet viewport."""
        # Set tablet viewport
        page.set_viewport_size({"width": 768, "height": 1024})
        
        # Navigate to comparison page
        page.click("[data-testid='nav-cost-comparison']")
        
        # Verify tablet layout
        expect(page.locator("[data-testid='tablet-comparison-layout']")).to_be_visible()
        expect(page.locator("[data-testid='comparison-table']")).to_be_visible()
        
        # Test horizontal scrolling for comparison table
        page.locator("[data-testid='comparison-table']").scroll_into_view_if_needed()


class TestAccessibility:
    """Test accessibility features."""

    def test_keyboard_navigation(self, page: Page):
        """Test keyboard navigation through cost calculator."""
        # Navigate to cost calculator
        page.click("[data-testid='nav-cost-calculator']")
        
        # Test tab navigation
        page.keyboard.press("Tab")  # Focus on create button
        page.keyboard.press("Enter")  # Activate create button
        
        page.keyboard.press("Tab")  # Focus on name field
        page.keyboard.type("Keyboard Test Calculation")
        
        page.keyboard.press("Tab")  # Focus on certification selector
        page.keyboard.press("Enter")  # Open selector
        page.keyboard.press("ArrowDown")  # Navigate options
        page.keyboard.press("Enter")  # Select option
        
        # Verify form can be completed via keyboard
        page.keyboard.press("Tab")  # Continue to next field
        page.keyboard.press("Tab")  # Continue to submit button
        page.keyboard.press("Enter")  # Submit form

    def test_screen_reader_compatibility(self, page: Page):
        """Test screen reader compatibility."""
        # Navigate to cost calculator
        page.click("[data-testid='nav-cost-calculator']")
        
        # Verify ARIA labels are present
        expect(page.locator("[aria-label='Create new cost calculation']")).to_be_visible()
        expect(page.locator("[aria-label='Calculation name']")).to_be_visible()
        expect(page.locator("[aria-label='Select certifications']")).to_be_visible()
        
        # Verify form validation messages have proper ARIA attributes
        page.click("[data-testid='create-calculation-submit']")  # Submit empty form
        expect(page.locator("[aria-describedby='name-error']")).to_be_visible()
        expect(page.locator("[role='alert']")).to_be_visible()
