"""Comprehensive integration tests for cost calculator API endpoints.

This module provides 95%+ test coverage for the FastAPI endpoints
including request/response validation, error handling, and business logic integration.
"""

import pytest
from fastapi.testclient import TestClient
from datetime import datetime, timedelta
from unittest.mock import patch, Mock
import json

from api.app import create_app
from models.cost_calculation import CurrencyRate, CostScenario, CostCalculation, CostHistory
from models.certification import Certification, Organization


class TestCostCalculatorAPI:
    """Comprehensive test cases for cost calculator API endpoints."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        app = create_app()
        return TestClient(app)

    @pytest.fixture
    def sample_certifications(self, db_session):
        """Create sample certifications for testing."""
        org = Organization(name="Test Org", country="US")
        db_session.add(org)
        db_session.flush()
        
        certs = [
            Certification(name="Security+", cost=349.0, difficulty=2, organization_id=org.id),
            Certification(name="CISSP", cost=749.0, difficulty=5, organization_id=org.id),
            Certification(name="CEH", cost=1199.0, difficulty=3, organization_id=org.id)
        ]
        
        for cert in certs:
            db_session.add(cert)
        db_session.commit()
        return certs

    @pytest.fixture
    def sample_currency_rates(self, db_session):
        """Create sample currency rates for testing."""
        rates = [
            CurrencyRate(base_currency='USD', target_currency='EUR', rate=0.92),
            CurrencyRate(base_currency='USD', target_currency='GBP', rate=0.79),
            CurrencyRate(base_currency='EUR', target_currency='USD', rate=1.09)
        ]
        
        for rate in rates:
            db_session.add(rate)
        db_session.commit()
        return rates

    @pytest.fixture
    def sample_cost_scenarios(self, db_session):
        """Create sample cost scenarios for testing."""
        scenarios = [
            CostScenario(
                name='Self-Study',
                scenario_type='self_study',
                materials_multiplier=1.0,
                training_multiplier=0.0,
                retake_probability=0.25
            ),
            CostScenario(
                name='Bootcamp',
                scenario_type='bootcamp',
                materials_multiplier=1.5,
                training_multiplier=3.0,
                retake_probability=0.10
            )
        ]
        
        for scenario in scenarios:
            db_session.add(scenario)
        db_session.commit()
        return scenarios


class TestCurrencyRateEndpoints:
    """Test currency rate management endpoints."""

    def test_create_currency_rate_success(self, client):
        """Test successful currency rate creation."""
        rate_data = {
            "base_currency": "USD",
            "target_currency": "JPY",
            "rate": 150.0,
            "source": "manual"
        }
        
        response = client.post("/api/v1/cost-calculator/currency-rates", json=rate_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["base_currency"] == "USD"
        assert data["target_currency"] == "JPY"
        assert data["rate"] == 150.0
        assert data["source"] == "manual"
        assert data["is_active"]

    def test_create_currency_rate_validation_error(self, client):
        """Test currency rate creation with validation errors."""
        # Same base and target currency
        rate_data = {
            "base_currency": "USD",
            "target_currency": "USD",
            "rate": 1.0
        }
        
        response = client.post("/api/v1/cost-calculator/currency-rates", json=rate_data)
        
        assert response.status_code == 422
        assert "validation error" in response.json()["detail"][0]["type"]

    def test_create_currency_rate_invalid_rate(self, client):
        """Test currency rate creation with invalid rate."""
        rate_data = {
            "base_currency": "USD",
            "target_currency": "EUR",
            "rate": -0.5  # Negative rate
        }
        
        response = client.post("/api/v1/cost-calculator/currency-rates", json=rate_data)
        
        assert response.status_code == 422

    def test_get_exchange_rate_success(self, client, sample_currency_rates):
        """Test successful exchange rate retrieval."""
        response = client.get("/api/v1/cost-calculator/currency-rates/USD/EUR")
        
        assert response.status_code == 200
        data = response.json()
        assert data["base_currency"] == "USD"
        assert data["target_currency"] == "EUR"
        assert data["rate"] == 0.92
        assert "timestamp" in data

    def test_get_exchange_rate_not_found(self, client):
        """Test exchange rate retrieval when not found."""
        response = client.get("/api/v1/cost-calculator/currency-rates/USD/JPY")
        
        assert response.status_code == 404
        assert "Exchange rate not found" in response.json()["detail"]

    @patch('services.cost_calculator.requests.get')
    def test_update_exchange_rates_from_api_success(self, mock_get, client):
        """Test successful exchange rate update from API."""
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = {
            'rates': {
                'EUR': 0.92,
                'GBP': 0.79,
                'JPY': 150.0
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        response = client.post("/api/v1/cost-calculator/currency-rates/update-from-api?base_currency=USD")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"]
        assert data["updated_count"] == 3

    @patch('services.cost_calculator.requests.get')
    def test_update_exchange_rates_from_api_failure(self, mock_get, client):
        """Test exchange rate update API failure."""
        mock_get.side_effect = Exception("API Error")
        
        response = client.post("/api/v1/cost-calculator/currency-rates/update-from-api")
        
        assert response.status_code == 200  # Service handles errors gracefully
        data = response.json()
        assert not data["success"]
        assert "API Error" in data["error"]


class TestCostScenarioEndpoints:
    """Test cost scenario management endpoints."""

    def test_create_cost_scenario_success(self, client):
        """Test successful cost scenario creation."""
        scenario_data = {
            "name": "Test Scenario",
            "description": "Test scenario for API testing",
            "scenario_type": "self_study",
            "materials_multiplier": 1.2,
            "training_multiplier": 0.0,
            "retake_probability": 0.20,
            "includes_training": False,
            "includes_mentoring": False,
            "includes_practice_exams": True,
            "study_time_multiplier": 1.1,
            "preparation_weeks": 14
        }
        
        response = client.post("/api/v1/cost-calculator/scenarios", json=scenario_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Scenario"
        assert data["scenario_type"] == "self_study"
        assert data["materials_multiplier"] == 1.2
        assert data["retake_probability"] == 0.20
        assert data["preparation_weeks"] == 14

    def test_create_cost_scenario_validation_error(self, client):
        """Test cost scenario creation with validation errors."""
        scenario_data = {
            "name": "",  # Empty name
            "scenario_type": "invalid_type",  # Invalid type
            "materials_multiplier": -1.0,  # Negative multiplier
            "retake_probability": 1.5  # > 1.0
        }
        
        response = client.post("/api/v1/cost-calculator/scenarios", json=scenario_data)
        
        assert response.status_code == 422

    def test_get_cost_scenarios_pagination(self, client, sample_cost_scenarios):
        """Test paginated cost scenario retrieval."""
        response = client.get("/api/v1/cost-calculator/scenarios?page=1&page_size=1")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["scenarios"]) == 1
        assert data["total_count"] == 2
        assert data["page"] == 1
        assert data["page_size"] == 1
        assert data["total_pages"] == 2

    def test_get_cost_scenarios_filtering(self, client, sample_cost_scenarios):
        """Test cost scenario filtering."""
        # Filter by scenario type
        response = client.get("/api/v1/cost-calculator/scenarios?scenario_type=bootcamp")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["scenarios"]) == 1
        assert data["scenarios"][0]["scenario_type"] == "bootcamp"

    def test_update_cost_scenario_success(self, client, sample_cost_scenarios):
        """Test successful cost scenario update."""
        scenario = sample_cost_scenarios[0]
        update_data = {
            "name": "Updated Self-Study",
            "materials_multiplier": 1.3,
            "retake_probability": 0.18
        }
        
        response = client.put(f"/api/v1/cost-calculator/scenarios/{scenario.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Self-Study"
        assert data["materials_multiplier"] == 1.3
        assert data["retake_probability"] == 0.18

    def test_update_cost_scenario_not_found(self, client):
        """Test updating non-existent cost scenario."""
        update_data = {"name": "Non-existent"}
        
        response = client.put("/api/v1/cost-calculator/scenarios/999", json=update_data)
        
        assert response.status_code == 404
        assert "Cost scenario not found" in response.json()["detail"]


class TestCostCalculationEndpoints:
    """Test cost calculation management endpoints."""

    def test_create_cost_calculation_success(self, client, sample_certifications, sample_cost_scenarios):
        """Test successful cost calculation creation."""
        calculation_data = {
            "name": "Test Calculation",
            "description": "Test calculation for API testing",
            "certification_ids": [sample_certifications[0].id, sample_certifications[1].id],
            "base_currency": "USD",
            "target_currency": "EUR",
            "scenario_id": sample_cost_scenarios[0].id,
            "materials_cost": 500.0,
            "additional_costs": 100.0,
            "is_saved": True
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=calculation_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Calculation"
        assert data["user_id"] == "test_user_1"  # From mock auth
        assert len(data["certification_ids"]) == 2
        assert data["base_currency"] == "USD"
        assert data["target_currency"] == "EUR"
        assert data["scenario_id"] == sample_cost_scenarios[0].id
        assert data["is_saved"]
        assert "cost_breakdown" in data
        assert "totals" in data

    def test_create_cost_calculation_validation_error(self, client):
        """Test cost calculation creation with validation errors."""
        calculation_data = {
            "name": "",  # Empty name
            "certification_ids": [],  # Empty list
            "materials_cost": -100.0  # Negative cost
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=calculation_data)
        
        assert response.status_code == 422

    def test_create_cost_calculation_invalid_certifications(self, client):
        """Test cost calculation with invalid certification IDs."""
        calculation_data = {
            "name": "Invalid Certs",
            "certification_ids": [999, 1000],  # Non-existent IDs
            "materials_cost": 500.0
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=calculation_data)
        
        assert response.status_code == 400
        assert "Certifications not found" in response.json()["detail"]

    def test_get_cost_calculations_pagination(self, client, sample_certifications):
        """Test paginated cost calculation retrieval."""
        # Create multiple calculations
        for i in range(3):
            calc_data = {
                "name": f"Test Calculation {i}",
                "certification_ids": [sample_certifications[0].id],
                "materials_cost": 100.0 * i,
                "is_saved": (i % 2 == 0)
            }
            client.post("/api/v1/cost-calculator/calculations", json=calc_data)
        
        # Test pagination
        response = client.get("/api/v1/cost-calculator/calculations?page=1&page_size=2")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["calculations"]) == 2
        assert data["total_count"] == 3
        assert data["page"] == 1
        assert data["page_size"] == 2

    def test_get_cost_calculations_filtering(self, client, sample_certifications):
        """Test cost calculation filtering."""
        # Create calculations with different saved status
        calc_data_saved = {
            "name": "Saved Calculation",
            "certification_ids": [sample_certifications[0].id],
            "materials_cost": 500.0,
            "is_saved": True
        }
        
        calc_data_unsaved = {
            "name": "Unsaved Calculation",
            "certification_ids": [sample_certifications[0].id],
            "materials_cost": 600.0,
            "is_saved": False
        }
        
        client.post("/api/v1/cost-calculator/calculations", json=calc_data_saved)
        client.post("/api/v1/cost-calculator/calculations", json=calc_data_unsaved)
        
        # Filter by saved status
        response = client.get("/api/v1/cost-calculator/calculations?is_saved=true")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["calculations"]) == 1
        assert data["calculations"][0]["is_saved"]

    def test_get_cost_calculation_by_id(self, client, sample_certifications):
        """Test retrieving specific cost calculation."""
        # Create calculation
        calc_data = {
            "name": "Specific Calculation",
            "certification_ids": [sample_certifications[0].id],
            "materials_cost": 500.0
        }
        
        create_response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)
        calculation_id = create_response.json()["id"]
        
        # Retrieve calculation
        response = client.get(f"/api/v1/cost-calculator/calculations/{calculation_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == calculation_id
        assert data["name"] == "Specific Calculation"

    def test_get_cost_calculation_not_found(self, client):
        """Test retrieving non-existent cost calculation."""
        response = client.get("/api/v1/cost-calculator/calculations/999")
        
        assert response.status_code == 404
        assert "Cost calculation not found" in response.json()["detail"]

    def test_update_cost_calculation_success(self, client, sample_certifications, sample_currency_rates):
        """Test successful cost calculation update."""
        # Create calculation
        calc_data = {
            "name": "Original Calculation",
            "certification_ids": [sample_certifications[0].id],
            "materials_cost": 500.0
        }
        
        create_response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)
        calculation_id = create_response.json()["id"]
        
        # Update calculation
        update_data = {
            "name": "Updated Calculation",
            "target_currency": "EUR",
            "materials_cost": 600.0,
            "is_saved": True
        }
        
        response = client.put(f"/api/v1/cost-calculator/calculations/{calculation_id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Calculation"
        assert data["target_currency"] == "EUR"
        assert data["cost_breakdown"]["materials"] == 600.0
        assert data["is_saved"]

    def test_update_cost_calculation_not_found(self, client):
        """Test updating non-existent cost calculation."""
        update_data = {"name": "Non-existent"}
        
        response = client.put("/api/v1/cost-calculator/calculations/999", json=update_data)
        
        assert response.status_code == 404
        assert "Cost calculation not found" in response.json()["detail"]

    def test_delete_cost_calculation_success(self, client, sample_certifications):
        """Test successful cost calculation deletion."""
        # Create calculation
        calc_data = {
            "name": "To Delete",
            "certification_ids": [sample_certifications[0].id],
            "materials_cost": 500.0
        }
        
        create_response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)
        calculation_id = create_response.json()["id"]
        
        # Delete calculation
        response = client.delete(f"/api/v1/cost-calculator/calculations/{calculation_id}")
        
        assert response.status_code == 204
        
        # Verify deletion
        get_response = client.get(f"/api/v1/cost-calculator/calculations/{calculation_id}")
        assert get_response.status_code == 404

    def test_delete_cost_calculation_not_found(self, client):
        """Test deleting non-existent cost calculation."""
        response = client.delete("/api/v1/cost-calculator/calculations/999")

        assert response.status_code == 404
        assert "Cost calculation not found" in response.json()["detail"]


class TestCostComparisonEndpoints:
    """Test cost comparison and analysis endpoints."""

    def test_compare_cost_calculations_success(self, client, sample_certifications, sample_cost_scenarios):
        """Test successful cost calculation comparison."""
        # Create multiple calculations
        calc_ids = []

        for i, scenario in enumerate(sample_cost_scenarios):
            calc_data = {
                "name": f"Comparison Calculation {i+1}",
                "certification_ids": [sample_certifications[0].id],
                "scenario_id": scenario.id,
                "materials_cost": 400.0 + (i * 200)
            }

            response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)
            calc_ids.append(response.json()["id"])

        # Compare calculations
        comparison_data = {
            "calculation_ids": calc_ids,
            "comparison_currency": "USD",
            "include_time_analysis": True
        }

        response = client.post("/api/v1/cost-calculator/calculations/compare", json=comparison_data)

        assert response.status_code == 200
        data = response.json()
        assert len(data["calculations"]) == 2
        assert data["comparison_currency"] == "USD"

        summary = data["summary"]
        assert "lowest_cost" in summary
        assert "highest_cost" in summary
        assert "average_cost" in summary
        assert "cost_range" in summary

        assert len(data["recommendations"]) > 0

    def test_compare_cost_calculations_validation_error(self, client):
        """Test cost comparison with validation errors."""
        comparison_data = {
            "calculation_ids": [1],  # Too few calculations
            "comparison_currency": "USD"
        }

        response = client.post("/api/v1/cost-calculator/calculations/compare", json=comparison_data)

        assert response.status_code == 422

    def test_compare_cost_calculations_invalid_ids(self, client):
        """Test cost comparison with invalid calculation IDs."""
        comparison_data = {
            "calculation_ids": [999, 1000],  # Non-existent IDs
            "comparison_currency": "USD"
        }

        response = client.post("/api/v1/cost-calculator/calculations/compare", json=comparison_data)

        assert response.status_code == 400
        assert "Calculations not found or not accessible" in response.json()["detail"]

    def test_bulk_cost_calculations_success(self, client, sample_certifications, sample_cost_scenarios):
        """Test successful bulk cost calculation creation."""
        bulk_data = {
            "calculations": [
                {
                    "name": "Bulk Calculation 1",
                    "certification_ids": [sample_certifications[0].id],
                    "scenario_id": sample_cost_scenarios[0].id,
                    "materials_cost": 400.0
                },
                {
                    "name": "Bulk Calculation 2",
                    "certification_ids": [sample_certifications[1].id, sample_certifications[2].id],
                    "scenario_id": sample_cost_scenarios[1].id,
                    "materials_cost": 600.0
                }
            ],
            "apply_bulk_discount": False,
            "bulk_discount_percentage": 0.0
        }

        response = client.post("/api/v1/cost-calculator/calculations/bulk", json=bulk_data)

        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["name"] == "Bulk Calculation 1"
        assert data[1]["name"] == "Bulk Calculation 2"
        assert len(data[0]["certification_ids"]) == 1
        assert len(data[1]["certification_ids"]) == 2

    def test_bulk_cost_calculations_with_discount(self, client, sample_certifications):
        """Test bulk cost calculations with discount applied."""
        bulk_data = {
            "calculations": [
                {
                    "name": "Discounted Calculation 1",
                    "certification_ids": [sample_certifications[0].id],
                    "materials_cost": 1000.0,
                    "additional_costs": 200.0
                },
                {
                    "name": "Discounted Calculation 2",
                    "certification_ids": [sample_certifications[1].id],
                    "materials_cost": 800.0,
                    "additional_costs": 300.0
                }
            ],
            "apply_bulk_discount": True,
            "bulk_discount_percentage": 10.0
        }

        response = client.post("/api/v1/cost-calculator/calculations/bulk", json=bulk_data)

        assert response.status_code == 200
        data = response.json()

        # Verify discount was applied to additional costs
        # Original additional_costs should be reduced by 10%
        assert data[0]["cost_breakdown"]["additional"] == 180.0  # 200 * 0.9
        assert data[1]["cost_breakdown"]["additional"] == 270.0  # 300 * 0.9

    def test_bulk_cost_calculations_validation_error(self, client):
        """Test bulk cost calculations with validation errors."""
        bulk_data = {
            "calculations": [],  # Empty list
            "apply_bulk_discount": True,
            "bulk_discount_percentage": 60.0  # > 50%
        }

        response = client.post("/api/v1/cost-calculator/calculations/bulk", json=bulk_data)

        assert response.status_code == 422

    def test_bulk_cost_calculations_too_many(self, client, sample_certifications):
        """Test bulk cost calculations with too many calculations."""
        calculations = []
        for i in range(15):  # More than max allowed (10)
            calculations.append({
                "name": f"Calculation {i}",
                "certification_ids": [sample_certifications[0].id],
                "materials_cost": 500.0
            })

        bulk_data = {
            "calculations": calculations,
            "apply_bulk_discount": False
        }

        response = client.post("/api/v1/cost-calculator/calculations/bulk", json=bulk_data)

        assert response.status_code == 422


class TestAPIErrorHandling:
    """Test API error handling and edge cases."""

    def test_invalid_json_request(self, client):
        """Test API with invalid JSON."""
        response = client.post(
            "/api/v1/cost-calculator/calculations",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )

        assert response.status_code == 422

    def test_missing_required_fields(self, client):
        """Test API with missing required fields."""
        # Missing name and certification_ids
        calc_data = {
            "materials_cost": 500.0
        }

        response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)

        assert response.status_code == 422
        errors = response.json()["detail"]
        field_errors = [error["loc"][-1] for error in errors]
        assert "name" in field_errors
        assert "certification_ids" in field_errors

    def test_invalid_pagination_parameters(self, client):
        """Test API with invalid pagination parameters."""
        # Negative page number
        response = client.get("/api/v1/cost-calculator/calculations?page=-1")
        assert response.status_code == 422

        # Page size too large
        response = client.get("/api/v1/cost-calculator/calculations?page_size=200")
        assert response.status_code == 422

        # Zero page size
        response = client.get("/api/v1/cost-calculator/calculations?page_size=0")
        assert response.status_code == 422

    def test_invalid_currency_codes(self, client):
        """Test API with invalid currency codes."""
        rate_data = {
            "base_currency": "INVALID",
            "target_currency": "EUR",
            "rate": 0.92
        }

        response = client.post("/api/v1/cost-calculator/currency-rates", json=rate_data)

        assert response.status_code == 422

    def test_invalid_scenario_type(self, client):
        """Test API with invalid scenario type."""
        scenario_data = {
            "name": "Invalid Scenario",
            "scenario_type": "invalid_type",
            "materials_multiplier": 1.0
        }

        response = client.post("/api/v1/cost-calculator/scenarios", json=scenario_data)

        assert response.status_code == 422

    def test_boundary_value_validation(self, client):
        """Test API with boundary values."""
        # Test minimum valid values
        scenario_data = {
            "name": "A",  # Minimum length
            "scenario_type": "self_study",
            "materials_multiplier": 0.0,  # Minimum
            "training_multiplier": 0.0,   # Minimum
            "retake_probability": 0.0,    # Minimum
            "study_time_multiplier": 0.1, # Minimum
            "preparation_weeks": 1        # Minimum
        }

        response = client.post("/api/v1/cost-calculator/scenarios", json=scenario_data)
        assert response.status_code == 201

        # Test maximum valid values
        scenario_data_max = {
            "name": "A" * 100,  # Maximum length
            "scenario_type": "corporate",
            "materials_multiplier": 5.0,  # Maximum
            "training_multiplier": 10.0,  # Maximum
            "retake_probability": 1.0,    # Maximum
            "study_time_multiplier": 5.0, # Maximum
            "preparation_weeks": 104      # Maximum
        }

        response = client.post("/api/v1/cost-calculator/scenarios", json=scenario_data_max)
        assert response.status_code == 201

    def test_concurrent_requests(self, client, sample_certifications):
        """Test API with concurrent requests."""
        import threading
        import time

        results = []

        def create_calculation(index):
            calc_data = {
                "name": f"Concurrent Calculation {index}",
                "certification_ids": [sample_certifications[0].id],
                "materials_cost": 500.0 + index
            }

            response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)
            results.append(response.status_code)

        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_calculation, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All requests should succeed
        assert all(status == 201 for status in results)
        assert len(results) == 5

    def test_large_request_payload(self, client, sample_certifications):
        """Test API with large request payload."""
        # Create calculation with very long description
        long_description = "A" * 5000  # 5KB description

        calc_data = {
            "name": "Large Payload Test",
            "description": long_description,
            "certification_ids": [sample_certifications[0].id],
            "materials_cost": 500.0
        }

        response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)

        # Should handle large payloads gracefully
        assert response.status_code in [201, 413, 422]  # Created, Payload Too Large, or Validation Error
