"""Test suite for job search API endpoints"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from typing import List
from api.endpoints.job_search import router
from models.job_role import JobRole

@pytest.fixture
def sample_jobs(db_session):
    """Create sample jobs for testing"""
    jobs = [
        JobRole(
            title="Security Engineer",
            domain="Application Security",
            description="Application security role",
            responsibilities="secure coding,vulnerability assessment"
        ),
        JobRole(
            title="Security Analyst",
            domain="Security Operations",
            description="SOC analyst role",
            responsibilities="incident response,threat hunting"
        ),
        JobRole(
            title="Security Engineer",
            domain="Cloud Security",
            description="Cloud security role",
            responsibilities="cloud security,container security"
        )
    ]
    for job in jobs:
        db_session.add(job)
    db_session.commit()
    return jobs

def test_search_jobs_no_filter(client: TestClient, sample_jobs: List[JobRole]):
    """Test job search with no filters"""
    response = client.get("/jobs/search")
    assert response.status_code == 200
    
    data = response.json()
    assert len(data) == 2  # Should combine Security Engineer entries
    
    engineer_job = next(job for job in data if job["title"] == "Security Engineer")
    assert len(engineer_job["domains"]) == 2
    assert "Application Security" in engineer_job["domains"]
    assert "Cloud Security" in engineer_job["domains"]

def test_search_jobs_with_query(client: TestClient, sample_jobs: List[JobRole]):
    """Test job search with title query"""
    response = client.get("/jobs/search", params={"query": "analyst"})
    assert response.status_code == 200
    
    data = response.json()
    assert len(data) == 1
    assert data[0]["title"] == "Security Analyst"
    assert data[0]["domains"] == ["Security Operations"]

def test_search_jobs_with_domain(client: TestClient, sample_jobs: List[JobRole]):
    """Test job search with domain filter"""
    response = client.get("/jobs/search", params={"domain": "Cloud Security"})
    assert response.status_code == 200
    
    data = response.json()
    assert len(data) == 1
    assert "Cloud Security" in data[0]["domains"]

def test_search_jobs_no_results(client: TestClient, sample_jobs: List[JobRole]):
    """Test job search with no matching results"""
    response = client.get("/jobs/search", params={"query": "nonexistent"})
    assert response.status_code == 200
    assert response.json() == []
