"""Unit tests for study session API endpoints"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from datetime import datetime, timedelta
import json

from api.app import app
from database import get_db
from models import Base
from models.user import User
from models.progress_tracking import StudySession
from models.certification import Certification, Organization


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_study_sessions.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="module")
def setup_database():
    """Create test database tables"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client(setup_database):
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def db_session():
    """Create database session for testing"""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def authenticated_user(client):
    """Create and authenticate a test user"""
    # Register user
    registration_data = {
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "name": "Study Test User"
    }
    
    response = client.post("/api/v1/auth/register", json=registration_data)
    assert response.status_code == 201
    
    auth_data = response.json()
    return {
        "user": auth_data["user"],
        "token": auth_data["access_token"],
        "headers": {"Authorization": f"Bearer {auth_data['access_token']}"}
    }


@pytest.fixture
def test_certification(db_session):
    """Create a test certification"""
    # Create organization first
    org = Organization(
        name="Test Org",
        description="Test Organization",
        website="https://test.org"
    )
    db_session.add(org)
    db_session.commit()
    
    # Create certification
    cert = Certification(
        name="Test Security Certification",
        category="Security",
        domain="Network Security",
        level="Intermediate",
        difficulty=3,
        cost=299.99,
        organization_id=org.id,
        description="Test certification for unit tests"
    )
    db_session.add(cert)
    db_session.commit()
    
    return cert


class TestStudySessionLogging:
    """Test study session logging functionality"""
    
    def test_log_study_session_success(self, client, authenticated_user, test_certification):
        """Test successful study session logging"""
        session_data = {
            "certification_id": test_certification.id,
            "planned_duration_minutes": 60,
            "session_type": "study",
            "topic": "Network Security Fundamentals"
        }
        
        response = client.post(
            "/api/v1/study/sessions",
            json=session_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["user_id"] == authenticated_user["user"]["user_id"]
        assert data["certification_id"] == test_certification.id
        assert data["session_type"] == "study"
        assert data["topic"] == "Network Security Fundamentals"
    
    def test_log_study_session_without_auth(self, client, test_certification):
        """Test study session logging without authentication"""
        session_data = {
            "certification_id": test_certification.id,
            "planned_duration_minutes": 60,
            "session_type": "study"
        }
        
        response = client.post("/api/v1/study/sessions", json=session_data)
        assert response.status_code == 403  # No authentication
    
    def test_log_study_session_invalid_data(self, client, authenticated_user):
        """Test study session logging with invalid data"""
        session_data = {
            "planned_duration_minutes": -10,  # Invalid duration
            "session_type": "invalid_type"
        }
        
        response = client.post(
            "/api/v1/study/sessions",
            json=session_data,
            headers=authenticated_user["headers"]
        )
        assert response.status_code == 422  # Validation error


class TestStudySessionHistory:
    """Test study session history retrieval"""
    
    @pytest.fixture
    def sample_sessions(self, db_session, authenticated_user, test_certification):
        """Create sample study sessions"""
        user_id = authenticated_user["user"]["user_id"]
        sessions = []
        
        # Create sessions over the past week
        for i in range(5):
            session = StudySession(
                user_id=user_id,
                certification_id=test_certification.id,
                session_type="study",
                topic=f"Topic {i+1}",
                started_at=datetime.utcnow() - timedelta(days=i),
                ended_at=datetime.utcnow() - timedelta(days=i, hours=-1),
                duration_minutes=60,
                progress_before=i * 10.0,
                progress_after=(i + 1) * 10.0
            )
            db_session.add(session)
            sessions.append(session)
        
        db_session.commit()
        return sessions
    
    def test_get_study_history_success(self, client, authenticated_user, sample_sessions):
        """Test successful study history retrieval"""
        response = client.get(
            "/api/v1/study/sessions",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5
        assert all(session["user_id"] == authenticated_user["user"]["user_id"] for session in data)
    
    def test_get_study_history_with_filters(self, client, authenticated_user, sample_sessions, test_certification):
        """Test study history with filters"""
        response = client.get(
            f"/api/v1/study/sessions?certification_id={test_certification.id}&session_type=study",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5
        assert all(session["certification_id"] == test_certification.id for session in data)
        assert all(session["session_type"] == "study" for session in data)
    
    def test_get_study_history_without_auth(self, client):
        """Test study history retrieval without authentication"""
        response = client.get("/api/v1/study/sessions")
        assert response.status_code == 403


class TestStudyProgress:
    """Test study progress analytics"""
    
    @pytest.fixture
    def progress_sessions(self, db_session, authenticated_user, test_certification):
        """Create sessions for progress testing"""
        user_id = authenticated_user["user"]["user_id"]
        sessions = []
        
        # Create sessions with different types and durations
        session_types = ["study", "practice", "review"]
        durations = [30, 45, 60, 90, 120]
        
        for i in range(10):
            session = StudySession(
                user_id=user_id,
                certification_id=test_certification.id,
                session_type=session_types[i % len(session_types)],
                topic=f"Progress Topic {i+1}",
                started_at=datetime.utcnow() - timedelta(days=i),
                ended_at=datetime.utcnow() - timedelta(days=i, hours=-1),
                duration_minutes=durations[i % len(durations)],
                progress_before=i * 5.0,
                progress_after=(i + 1) * 5.0
            )
            db_session.add(session)
            sessions.append(session)
        
        db_session.commit()
        return sessions
    
    def test_get_study_progress_success(self, client, authenticated_user, progress_sessions):
        """Test successful study progress retrieval"""
        response = client.get(
            "/api/v1/study/progress",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Check required fields
        assert "total_sessions" in data
        assert "total_study_hours" in data
        assert "total_study_minutes" in data
        assert "average_session_duration_minutes" in data
        assert "session_types" in data
        assert "recent_activity" in data
        assert "study_streak" in data
        
        # Verify calculations
        assert data["total_sessions"] == 10
        assert data["total_study_minutes"] > 0
        assert data["total_study_hours"] > 0
    
    def test_get_study_progress_with_period(self, client, authenticated_user, progress_sessions):
        """Test study progress with different time periods"""
        response = client.get(
            "/api/v1/study/progress?period=week",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["period"] == "week"
        assert data["period_days"] == 7
    
    def test_get_study_progress_with_certification_filter(self, client, authenticated_user, progress_sessions, test_certification):
        """Test study progress filtered by certification"""
        response = client.get(
            f"/api/v1/study/progress?certification_id={test_certification.id}",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["certification_id"] == test_certification.id
    
    def test_get_study_progress_without_auth(self, client):
        """Test study progress without authentication"""
        response = client.get("/api/v1/study/progress")
        assert response.status_code == 403


class TestStudyGoals:
    """Test study goal management"""
    
    def test_set_study_goal_success(self, client, authenticated_user, test_certification):
        """Test successful study goal creation"""
        goal_data = {
            "title": "Complete Security Certification",
            "goal_type": "certification_deadline",
            "target_value": 1.0,
            "target_unit": "certification",
            "target_date": (datetime.utcnow() + timedelta(days=90)).isoformat(),
            "certification_id": test_certification.id,
            "description": "Complete the security certification within 3 months"
        }
        
        response = client.post(
            "/api/v1/study/goals",
            json=goal_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == goal_data["title"]
        assert data["goal_type"] == goal_data["goal_type"]
        assert data["certification_id"] == test_certification.id
    
    def test_get_study_goals_success(self, client, authenticated_user):
        """Test successful study goals retrieval"""
        response = client.get(
            "/api/v1/study/goals",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_set_study_goal_without_auth(self, client):
        """Test study goal creation without authentication"""
        goal_data = {
            "title": "Test Goal",
            "goal_type": "daily_time",
            "target_value": 60.0,
            "target_unit": "minutes"
        }
        
        response = client.post("/api/v1/study/goals", json=goal_data)
        assert response.status_code == 403
