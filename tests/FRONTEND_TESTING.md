# Frontend Testing with Playwright + <PERSON><PERSON>HAVE Integration

This document describes the enhanced frontend testing setup that integrates <PERSON><PERSON> with BEHAVE for comprehensive end-to-end testing.

## Overview

The frontend testing suite combines the power of <PERSON><PERSON>'s modern browser automation with BEHAVE's behavior-driven development approach, providing:

- **Dual Browser Support**: Both Playwright (modern) and Selenium (legacy) support
- **Comprehensive Testing**: Unit, integration, E2E, accessibility, and performance tests
- **Cross-Browser Testing**: Chromium, Firefox, and WebKit support
- **Mobile Testing**: Responsive design validation
- **Accessibility Compliance**: WCAG 2.1 AA validation
- **Performance Monitoring**: Core Web Vitals tracking

## Quick Start

### Installation

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers
npx playwright install

# Install frontend dependencies
cd frontend && npm install
```

### Running Tests

```bash
# Run with <PERSON>wright (recommended)
python run_tests.py --type smoke --playwright

# Run with Selenium (legacy)
python run_tests.py --type smoke --selenium

# Run specific test types
python run_tests.py --type accessibility
python run_tests.py --type performance
python run_tests.py --type mobile
```

## Test Architecture

### Playwright + BEHAVE Integration

The integration provides a unified testing interface that can switch between Playwright and Selenium based on configuration:

```python
# Environment variable controls the browser automation tool
USE_PLAYWRIGHT=true  # Use Playwright (default)
USE_PLAYWRIGHT=false # Use Selenium (fallback)
```

### Enhanced Step Definitions

Step definitions work with both automation tools:

```python
@when('I enter email "{email}" with enhanced testing')
def step_enter_email_enhanced(context, email):
    if context.use_playwright:
        context.page.get_by_test_id('email-input').fill(email)
    else:
        email_input = context.driver.find_element(By.CSS_SELECTOR, "[data-testid='email-input']")
        email_input.send_keys(email)
```

## Test Categories

### 1. Smoke Tests (@smoke)
Quick validation of core functionality:
- Login/logout flow
- Dashboard loading
- Navigation between pages
- Basic form interactions

### 2. Accessibility Tests (@accessibility)
WCAG 2.1 AA compliance validation:
- Keyboard navigation
- Screen reader compatibility
- Color contrast ratios
- ARIA attributes
- Semantic HTML structure

### 3. Performance Tests (@performance)
Core Web Vitals and performance metrics:
- Page load times (LCP < 2.5s)
- First Input Delay (FID < 100ms)
- Cumulative Layout Shift (CLS < 0.1)
- Memory usage monitoring
- Bundle size validation

### 4. Mobile Tests (@mobile)
Responsive design validation:
- Mobile viewport testing
- Touch target sizing
- Mobile navigation
- Responsive layouts
- Mobile-specific interactions

### 5. Cross-Browser Tests (@cross-browser)
Multi-browser compatibility:
- Chromium (Chrome, Edge)
- Firefox
- WebKit (Safari)
- Consistent behavior validation

## Configuration

### Environment Variables

```bash
# Browser automation tool
USE_PLAYWRIGHT=true          # Use Playwright (default)
USE_PLAYWRIGHT=false         # Use Selenium

# Browser selection
BROWSER=chromium            # chromium, firefox, webkit
BROWSER=chrome              # For Selenium: chrome, firefox

# Display mode
HEADLESS=true               # Run without browser UI
HEADLESS=false              # Show browser UI

# Test environment
BASE_URL=http://localhost:3000
TIMEOUT=30                  # Timeout in seconds

# Recording and debugging
RECORD_VIDEO=true           # Record test videos
TAKE_SCREENSHOTS=true       # Take screenshots on failure
SLOW_MO=0                   # Slow down actions (ms)
```

### Playwright Configuration

Key features enabled in `playwright-behave-config.py`:

```python
class PlaywrightBehaveConfig:
    def __init__(self):
        self.browser_name = os.getenv('BROWSER', 'chromium')
        self.headless = os.getenv('HEADLESS', 'true').lower() == 'true'
        self.base_url = os.getenv('BASE_URL', 'http://localhost:3000')
        
    def authenticate_user(self, user_type='valid'):
        """Authenticate user for protected routes"""
        
    def mock_api_response(self, endpoint, response_data):
        """Mock API responses for testing"""
        
    def simulate_network_failure(self, endpoint):
        """Simulate network failures"""
        
    def get_performance_metrics(self):
        """Get performance metrics"""
```

## Feature Files

### Enhanced Login Feature

```gherkin
Feature: Enhanced User Authentication with Playwright + BEHAVE
  As a user
  I want to log into the CertRats platform with comprehensive testing
  So that I can access my certification dashboard with confidence

  @playwright @smoke
  Scenario: Successful login with Playwright enhanced testing
    Given I am on the login page
    When I enter email "<EMAIL>" with enhanced testing
    And I enter password "password123" with enhanced testing
    And I click the "Sign In" button with enhanced testing
    Then I should be redirected to the dashboard with enhanced testing

  @playwright @accessibility
  Scenario: Login page accessibility compliance
    Given I am on the login page
    When I test accessibility with enhanced testing
    Then the page should meet accessibility standards with enhanced testing

  @playwright @performance
  Scenario: Login page performance optimization
    Given I am on the login page
    When I test performance with enhanced testing
    Then the page should load within performance thresholds with enhanced testing
```

## Test Execution

### Command Line Interface

```bash
# Basic test execution
python run_tests.py --type [TEST_TYPE] [OPTIONS]

# Test types
--type smoke              # Quick validation tests
--type accessibility      # WCAG compliance tests
--type performance        # Performance and Core Web Vitals
--type mobile            # Mobile responsiveness tests
--type cross-browser     # Multi-browser compatibility
--type all               # All test suites

# Browser options
--browser chromium       # Specific browser
--browser all            # All supported browsers
--playwright             # Use Playwright (default)
--selenium               # Use Selenium
--headless               # Run without browser UI
--headed                 # Show browser UI

# BEHAVE tags
--tags @smoke @accessibility  # Run specific tagged scenarios
```

### Example Commands

```bash
# Smoke tests with Playwright in headless mode
python run_tests.py --type smoke --playwright --headless

# Accessibility tests with browser UI visible
python run_tests.py --type accessibility --headed

# Performance tests across all browsers
python run_tests.py --type performance --browser all

# Mobile tests with specific tags
python run_tests.py --type mobile --tags @responsive @mobile

# Cross-browser compatibility testing
python run_tests.py --type cross-browser

# Full test suite
python run_tests.py --type all
```

## Advanced Features

### API Mocking

```python
# Mock successful API response
context.playwright_config.mock_api_response(
    '/api/auth/login',
    {'access_token': 'mock_token', 'user': {'name': 'Test User'}}
)

# Simulate network failure
context.playwright_config.simulate_network_failure('/api/auth/login')
```

### Performance Monitoring

```python
# Get performance metrics
metrics = context.playwright_config.get_performance_metrics()
assert metrics['firstContentfulPaint'] < 2500  # 2.5s FCP target
assert metrics['loadTime'] < 3000              # 3s load time target
```

### Accessibility Testing

```python
# Enable accessibility testing
context.playwright_config.enable_accessibility_testing()

# Run accessibility scan
violations = context.page.evaluate("() => window.axe.run()")
assert len(violations['violations']) == 0
```

### Responsive Testing

```python
# Test different viewports
context.playwright_config.set_mobile_viewport()    # 375x667
context.playwright_config.set_tablet_viewport()    # 768x1024
context.playwright_config.set_desktop_viewport()   # 1920x1080
```

## Best Practices

### Test Writing Guidelines

1. **Use data-testid attributes** for reliable element selection
2. **Write descriptive scenario names** that explain business value
3. **Use Given-When-Then structure** consistently
4. **Keep scenarios focused** on single user journeys
5. **Use tags effectively** for test organization

### Performance Testing

1. **Set realistic thresholds** based on user expectations
2. **Test on different network conditions** (3G, 4G, WiFi)
3. **Monitor Core Web Vitals** consistently
4. **Test with realistic data volumes**
5. **Validate bundle sizes** and loading strategies

### Accessibility Testing

1. **Test with keyboard navigation** exclusively
2. **Validate ARIA attributes** and roles
3. **Check color contrast ratios** programmatically
4. **Test with screen reader simulation**
5. **Validate semantic HTML structure**

## Troubleshooting

### Common Issues

1. **Browser not found**: Run `npx playwright install`
2. **Tests timing out**: Increase timeout values
3. **Flaky tests**: Add proper wait conditions
4. **Memory issues**: Use headless mode for CI

### Debug Mode

```bash
# Run with debug output
DEBUG=pw:api python run_tests.py --type e2e --headed

# Run single scenario with debugging
behave features/auth/enhanced_login.feature:10 --no-capture
```

### CI/CD Integration

```yaml
# GitHub Actions example
- name: Run Frontend Tests
  run: |
    python tests/run_tests.py --type smoke --headless
    python tests/run_tests.py --type accessibility --headless
    python tests/run_tests.py --type performance --headless
```

## Results and Reporting

### Test Artifacts

- **Screenshots**: Captured on test failures
- **Videos**: Recorded for failed test runs
- **Performance metrics**: JSON reports with Core Web Vitals
- **Accessibility reports**: WCAG compliance details
- **Coverage reports**: Test coverage statistics

### Report Locations

```
test-results/
├── screenshots/          # Failure screenshots
├── videos/              # Test execution videos
├── behave-results.json  # BEHAVE test results
├── performance/         # Performance metrics
└── accessibility/       # Accessibility reports
```

This enhanced testing setup provides comprehensive validation of the CertRats frontend, ensuring high quality, accessibility, and performance across all supported browsers and devices.
