"""Comprehensive service layer testing with business logic validation"""
import pytest
from unittest.mock import Mock, patch, MagicMock, call
from datetime import datetime, timedelta
from decimal import Decimal
import json
import requests
from typing import List, Dict, Any

from services.cost_calculator import CostCalculatorService


class TestCostCalculatorService:
    """Comprehensive tests for cost calculator service"""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session"""
        return Mock()
    
    @pytest.fixture
    def cost_service(self, mock_db_session):
        """Create cost calculator service instance"""
        return CostCalculatorService(mock_db_session)
    
    def test_get_exchange_rate_same_currency(self, cost_service):
        """Test exchange rate for same currency returns 1.0"""
        rate = cost_service.get_exchange_rate('USD', 'USD')
        assert rate == 1.0
    
    @patch('services.cost_calculator.requests.get')
    def test_get_exchange_rate_external_api_success(self, mock_get, cost_service):
        """Test successful external API call for exchange rates"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'rates': {'EUR': 0.92, 'GBP': 0.79},
            'base': 'USD'
        }
        mock_get.return_value = mock_response
        
        rate = cost_service.get_exchange_rate('USD', 'EUR')
        assert rate == 0.92
        
        # Verify API was called correctly
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert 'USD' in call_args[0][0]  # URL should contain base currency
    
    @patch('services.cost_calculator.requests.get')
    def test_get_exchange_rate_api_failure_fallback(self, mock_get, cost_service):
        """Test fallback behavior when external API fails"""
        # Mock API failure
        mock_get.side_effect = requests.RequestException("API unavailable")
        
        # Mock database fallback
        mock_rate = Mock()
        mock_rate.rate = Decimal('0.85')
        mock_rate.is_valid = True
        cost_service.db.query.return_value.filter.return_value.filter.return_value.first.return_value = mock_rate
        
        rate = cost_service.get_exchange_rate('USD', 'EUR')
        assert rate == 0.85
    
    def test_calculate_certification_cost_basic(self, cost_service):
        """Test basic certification cost calculation"""
        # Mock certification
        mock_cert = Mock()
        mock_cert.cost = 500
        mock_cert.difficulty = 3
        mock_cert.name = "Test Cert"
        
        cost_breakdown = cost_service.calculate_certification_cost(
            certification=mock_cert,
            scenario=None,
            include_materials=True,
            include_retake_probability=True
        )
        
        assert 'exam_fee' in cost_breakdown
        assert 'materials_cost' in cost_breakdown
        assert 'total_cost' in cost_breakdown
        assert cost_breakdown['exam_fee'] == 500
        assert cost_breakdown['total_cost'] > 500  # Should include materials
    
    def test_calculate_certification_cost_with_scenario(self, cost_service):
        """Test certification cost calculation with scenario"""
        mock_cert = Mock()
        mock_cert.cost = 500
        mock_cert.difficulty = 3
        mock_cert.name = "Test Cert"
        
        mock_scenario = Mock()
        mock_scenario.materials_multiplier = Decimal('1.5')
        mock_scenario.retake_probability = Decimal('0.2')
        mock_scenario.training_multiplier = Decimal('2.0')
        
        cost_breakdown = cost_service.calculate_certification_cost(
            certification=mock_cert,
            scenario=mock_scenario,
            include_materials=True,
            include_training=True,
            include_retake_probability=True
        )
        
        assert cost_breakdown['materials_cost'] > 0
        assert cost_breakdown['training_cost'] > 0
        assert cost_breakdown['retake_cost'] > 0
        assert cost_breakdown['total_cost'] > 500
    
    def test_estimate_study_hours(self, cost_service):
        """Test study hours estimation"""
        mock_cert = Mock()
        mock_cert.difficulty = 4
        mock_cert.custom_hours = None
        
        # Test without scenario
        hours = cost_service._estimate_study_hours(mock_cert, None)
        assert hours == 160  # 4 * 40 (base hours per difficulty)
        
        # Test with scenario multiplier
        mock_scenario = Mock()
        mock_scenario.study_time_multiplier = Decimal('0.8')
        
        hours_with_scenario = cost_service._estimate_study_hours(mock_cert, mock_scenario)
        assert hours_with_scenario == 128  # 160 * 0.8
    
    def test_create_cost_calculation_comprehensive(self, cost_service):
        """Test comprehensive cost calculation creation"""
        # Mock certifications
        mock_certs = [
            Mock(id=1, cost=500, difficulty=3, name="Cert 1"),
            Mock(id=2, cost=750, difficulty=4, name="Cert 2")
        ]
        cost_service.db.query.return_value.filter.return_value.all.return_value = mock_certs
        
        # Mock scenario
        mock_scenario = Mock()
        mock_scenario.id = 1
        mock_scenario.materials_multiplier = Decimal('1.2')
        mock_scenario.retake_probability = Decimal('0.15')
        
        calculation_data = {
            'user_id': 'test_user',
            'name': 'Test Calculation',
            'certification_ids': [1, 2],
            'scenario_id': 1,
            'base_currency': 'USD',
            'target_currency': 'EUR',
            'include_materials': True,
            'include_training': False,
            'include_retake_probability': True
        }
        
        with patch.object(cost_service, 'get_exchange_rate', return_value=0.92):
            result = cost_service.create_cost_calculation(calculation_data)
            
            assert result is not None
            # Verify database operations
            cost_service.db.add.assert_called()
            cost_service.db.commit.assert_called()


class TestServiceIntegration:
    """Test service layer integration and business logic"""
    
    def test_service_error_handling(self):
        """Test that services handle errors gracefully"""
        mock_db = Mock()
        mock_db.query.side_effect = Exception("Database error")
        
        service = CostCalculatorService(mock_db)
        
        # Should handle database errors gracefully
        with pytest.raises(Exception):
            service.get_exchange_rate('USD', 'EUR')
    
    def test_service_validation(self):
        """Test service input validation"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Test invalid currency codes
        with pytest.raises((ValueError, TypeError)):
            service.get_exchange_rate('INVALID', 'EUR')
        
        with pytest.raises((ValueError, TypeError)):
            service.get_exchange_rate('USD', 'INVALID')
    
    def test_service_caching_behavior(self):
        """Test service caching mechanisms"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Mock cached exchange rate
        with patch.object(service, '_get_cached_rate', return_value=0.92):
            rate = service.get_exchange_rate('USD', 'EUR')
            assert rate == 0.92
    
    def test_service_performance_optimization(self):
        """Test service performance optimizations"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Test bulk operations
        mock_certs = [Mock(id=i, cost=500+i*50) for i in range(10)]
        mock_db.query.return_value.filter.return_value.all.return_value = mock_certs
        
        # Should handle bulk calculations efficiently
        calculation_data = {
            'user_id': 'test_user',
            'name': 'Bulk Test',
            'certification_ids': list(range(10)),
            'base_currency': 'USD',
            'target_currency': 'USD'
        }
        
        with patch.object(service, 'get_exchange_rate', return_value=1.0):
            result = service.create_cost_calculation(calculation_data)
            assert result is not None


class TestServiceSecurity:
    """Test service layer security measures"""
    
    def test_input_sanitization(self):
        """Test that services sanitize inputs"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Test SQL injection attempts
        malicious_inputs = [
            "'; DROP TABLE certifications; --",
            "' OR '1'='1",
            "<script>alert('xss')</script>"
        ]
        
        for malicious_input in malicious_inputs:
            # Should handle malicious inputs safely
            try:
                service.get_exchange_rate(malicious_input, 'USD')
            except (ValueError, TypeError):
                # Expected to reject invalid input
                pass
    
    def test_authorization_checks(self):
        """Test service authorization mechanisms"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Test user authorization for cost calculations
        calculation_data = {
            'user_id': 'unauthorized_user',
            'name': 'Unauthorized Test',
            'certification_ids': [1, 2],
            'base_currency': 'USD',
            'target_currency': 'USD'
        }
        
        # Should check user permissions
        with patch.object(service, '_check_user_permissions', return_value=False):
            with pytest.raises(PermissionError):
                service.create_cost_calculation(calculation_data)
    
    def test_rate_limiting(self):
        """Test service rate limiting"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Test rate limiting for external API calls
        with patch.object(service, '_check_rate_limit', return_value=False):
            with pytest.raises(Exception):  # Should raise rate limit exception
                service.get_exchange_rate('USD', 'EUR')


class TestServiceReliability:
    """Test service reliability and fault tolerance"""
    
    def test_retry_mechanisms(self):
        """Test service retry logic"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Test retry on transient failures
        with patch('services.cost_calculator.requests.get') as mock_get:
            # First call fails, second succeeds
            mock_get.side_effect = [
                requests.RequestException("Temporary failure"),
                Mock(status_code=200, json=lambda: {'rates': {'EUR': 0.92}})
            ]
            
            with patch.object(service, '_should_retry', return_value=True):
                rate = service.get_exchange_rate('USD', 'EUR')
                assert rate == 0.92
                assert mock_get.call_count == 2
    
    def test_circuit_breaker(self):
        """Test circuit breaker pattern"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Test circuit breaker after multiple failures
        with patch.object(service, '_circuit_breaker_open', return_value=True):
            with pytest.raises(Exception):  # Should fail fast when circuit is open
                service.get_exchange_rate('USD', 'EUR')
    
    def test_graceful_degradation(self):
        """Test graceful degradation when services are unavailable"""
        mock_db = Mock()
        service = CostCalculatorService(mock_db)
        
        # Test fallback to cached/default values
        with patch('services.cost_calculator.requests.get', side_effect=Exception("Service unavailable")):
            with patch.object(service, '_get_fallback_rate', return_value=1.0):
                rate = service.get_exchange_rate('USD', 'EUR')
                assert rate == 1.0  # Should return fallback value
