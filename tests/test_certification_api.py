"""Unit tests for enhanced certification API endpoints"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from api.app import app
from database import get_db
from models import Base
from models.certification import Certification, Organization


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_certifications.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="module")
def setup_database():
    """Create test database tables"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client(setup_database):
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def db_session():
    """Create database session for testing"""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def sample_certifications(db_session):
    """Create sample certifications for testing"""
    # Create organizations
    org1 = Organization(
        name="CompTIA",
        description="Computing Technology Industry Association",
        website="https://comptia.org"
    )
    org2 = Organization(
        name="(ISC)²",
        description="International Information System Security Certification Consortium",
        website="https://isc2.org"
    )
    db_session.add_all([org1, org2])
    db_session.commit()
    
    # Create certifications
    certifications = [
        Certification(
            name="CompTIA Security+",
            category="Security",
            domain="General Security",
            level="Entry",
            focus="Foundational Security",
            difficulty=2,
            cost=370.0,
            description="Entry-level security certification",
            organization_id=org1.id,
            is_active=True
        ),
        Certification(
            name="CISSP",
            category="Security",
            domain="Information Security",
            level="Expert",
            focus="Security Management",
            difficulty=4,
            cost=749.0,
            description="Advanced security management certification",
            organization_id=org2.id,
            is_active=True
        ),
        Certification(
            name="CompTIA Network+",
            category="Networking",
            domain="Network Infrastructure",
            level="Intermediate",
            focus="Network Administration",
            difficulty=3,
            cost=358.0,
            description="Network infrastructure certification",
            organization_id=org1.id,
            is_active=True
        )
    ]
    
    db_session.add_all(certifications)
    db_session.commit()
    
    return certifications


class TestCertificationListing:
    """Test certification listing and filtering"""
    
    def test_list_certifications_basic(self, client, sample_certifications):
        """Test basic certification listing"""
        response = client.get("/api/v1/certifications/")
        
        assert response.status_code == 200
        data = response.json()
        assert "certifications" in data
        assert len(data["certifications"]) == 3
        assert "total_count" in data
        assert data["total_count"] == 3
    
    def test_list_certifications_with_domain_filter(self, client, sample_certifications):
        """Test certification listing with domain filter"""
        response = client.get("/api/v1/certifications/?domain=Security")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["certifications"]) == 2  # Security+ and CISSP
    
    def test_list_certifications_with_level_filter(self, client, sample_certifications):
        """Test certification listing with level filter"""
        response = client.get("/api/v1/certifications/?level=Entry")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["certifications"]) == 1
        assert data["certifications"][0]["name"] == "CompTIA Security+"
    
    def test_list_certifications_with_cost_filter(self, client, sample_certifications):
        """Test certification listing with cost filters"""
        response = client.get("/api/v1/certifications/?min_cost=400&max_cost=800")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["certifications"]) == 1
        assert data["certifications"][0]["name"] == "CISSP"
    
    def test_list_certifications_with_pagination(self, client, sample_certifications):
        """Test certification listing with pagination"""
        response = client.get("/api/v1/certifications/?page=1&page_size=2")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["certifications"]) == 2
        assert data["page"] == 1
        assert data["page_size"] == 2
        assert data["total_pages"] == 2


class TestCertificationDetails:
    """Test certification detail retrieval"""
    
    def test_get_certification_details_success(self, client, sample_certifications):
        """Test successful certification detail retrieval"""
        cert_id = sample_certifications[0].id
        response = client.get(f"/api/v1/certifications/{cert_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == cert_id
        assert data["name"] == "CompTIA Security+"
        assert "description" in data
    
    def test_get_certification_details_not_found(self, client):
        """Test certification detail retrieval for non-existent certification"""
        response = client.get("/api/v1/certifications/99999")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()


class TestCertificationSearch:
    """Test certification search functionality"""
    
    def test_search_certifications_by_name(self, client, sample_certifications):
        """Test searching certifications by name"""
        response = client.get("/api/v1/certifications/search?q=Security")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["certifications"]) == 1
        assert "Security+" in data["certifications"][0]["name"]
    
    def test_search_certifications_by_description(self, client, sample_certifications):
        """Test searching certifications by description"""
        response = client.get("/api/v1/certifications/search?q=management")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["certifications"]) == 1
        assert data["certifications"][0]["name"] == "CISSP"
    
    def test_search_certifications_with_filters(self, client, sample_certifications):
        """Test searching with additional filters"""
        response = client.get("/api/v1/certifications/search?q=CompTIA&level=Entry")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["certifications"]) == 1
        assert data["certifications"][0]["name"] == "CompTIA Security+"
    
    def test_search_certifications_no_results(self, client, sample_certifications):
        """Test search with no results"""
        response = client.get("/api/v1/certifications/search?q=NonExistentCert")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["certifications"]) == 0
    
    def test_search_certifications_short_query(self, client):
        """Test search with too short query"""
        response = client.get("/api/v1/certifications/search?q=a")
        
        assert response.status_code == 422  # Validation error


class TestCertificationDomains:
    """Test certification domains and categories"""
    
    def test_get_domains_success(self, client, sample_certifications):
        """Test successful domains retrieval"""
        response = client.get("/api/v1/certifications/domains")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "domains" in data
        assert "categories" in data
        assert "levels" in data
        assert "organizations" in data
        assert "difficulty_range" in data
        assert "total_certifications" in data
        
        assert "General Security" in data["domains"]
        assert "Security" in data["categories"]
        assert "Entry" in data["levels"]
        assert "CompTIA" in data["organizations"]


class TestCertificationComparison:
    """Test certification comparison functionality"""
    
    def test_compare_certifications_success(self, client, sample_certifications):
        """Test successful certification comparison"""
        cert_ids = [sample_certifications[0].id, sample_certifications[1].id]
        comparison_data = {
            "certification_ids": cert_ids
        }
        
        response = client.post("/api/v1/certifications/compare", json=comparison_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "certifications" in data
        assert "comparison_matrix" in data
        assert "summary" in data
        
        assert len(data["certifications"]) == 2
        assert "cost" in data["comparison_matrix"]
        assert "difficulty" in data["comparison_matrix"]
        assert "total_cost" in data["summary"]
    
    def test_compare_certifications_insufficient_ids(self, client):
        """Test comparison with insufficient certification IDs"""
        comparison_data = {
            "certification_ids": [1]
        }
        
        response = client.post("/api/v1/certifications/compare", json=comparison_data)
        
        assert response.status_code == 400
        assert "at least 2" in response.json()["detail"].lower()
    
    def test_compare_certifications_too_many_ids(self, client):
        """Test comparison with too many certification IDs"""
        comparison_data = {
            "certification_ids": [1, 2, 3, 4, 5, 6]
        }
        
        response = client.post("/api/v1/certifications/compare", json=comparison_data)
        
        assert response.status_code == 400
        assert "maximum 5" in response.json()["detail"].lower()
    
    def test_compare_certifications_not_found(self, client):
        """Test comparison with non-existent certification IDs"""
        comparison_data = {
            "certification_ids": [99999, 99998]
        }
        
        response = client.post("/api/v1/certifications/compare", json=comparison_data)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()
