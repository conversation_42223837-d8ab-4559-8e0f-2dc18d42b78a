# Testing Requirements for CertRatsAgent4
# Install with: pip install -r tests/requirements-test.txt

# Core Testing Framework
pytest>=8.0.0
pytest-asyncio>=0.23.2
pytest-cov>=4.1.0
pytest-mock>=3.12.0
pytest-xdist>=3.5.0  # For parallel test execution

# FastAPI Testing
fastapi>=0.109.0
httpx>=0.26.0  # For async HTTP testing
starlette>=0.35.0

# Database Testing
sqlalchemy>=2.0.25
alembic>=1.13.1

# Mock and Fixtures
factory-boy>=3.3.0
faker>=22.0.0
responses>=0.24.1  # For mocking HTTP requests

# Coverage and Reporting
coverage>=7.4.0
pytest-html>=4.1.1  # HTML test reports
pytest-json-report>=1.5.0  # JSON test reports

# Performance Testing
pytest-benchmark>=4.0.0
pytest-timeout>=2.2.0

# Code Quality
flake8>=7.0.0
mypy>=1.8.0
black>=24.0.0
isort>=5.13.2

# Additional Testing Utilities
freezegun>=1.4.0  # For mocking datetime
parameterized>=0.9.0  # For parameterized tests
testcontainers>=3.7.1  # For integration testing with containers

# Development Dependencies
ipython>=8.20.0  # For debugging
pdbpp>=0.10.3  # Enhanced debugger
