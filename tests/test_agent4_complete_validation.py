"""Complete validation test suite for Agent 4 Career & Cost Intelligence.

This comprehensive test validates the entire Agent 4 system implementation
against PRD 04 requirements, ensuring all features work correctly and
meet performance standards.
"""

import pytest
import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import concurrent.futures


class TestAgent4CompleteValidation:
    """Complete validation test suite for Agent 4 system."""
    
    @pytest.fixture
    def base_url(self):
        """Base URL for API endpoints."""
        return "http://localhost:8000"
    
    def test_prd04_requirement_1_career_pathfinding(self, base_url):
        """Validate PRD 04 Requirement 1: Career Pathfinding with A* Algorithm."""
        
        # Test career pathfinding with multiple constraints
        request_data = {
            'current_role_id': 1,  # Security Analyst
            'target_role_id': 3,   # Senior Security Engineer
            'max_budget': 5000,
            'max_timeline_months': 18,
            'max_difficulty': 'Medium',
            'learning_style': 'mixed',
            'study_hours_per_week': 15,
            'currency': 'USD'
        }
        
        response = requests.post(
            f"{base_url}/api/v1/career-transition/pathfinding",
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate A* algorithm results
        assert 'path_options' in data
        assert len(data['path_options']) > 0
        
        # Validate path optimization
        for path in data['path_options']:
            assert 'total_cost' in path
            assert 'estimated_duration_months' in path
            assert 'success_probability' in path
            assert 'certifications' in path
            
            # Validate constraints are respected
            assert path['total_cost'] <= request_data['max_budget']
            assert path['estimated_duration_months'] <= request_data['max_timeline_months']
            assert 0 <= path['success_probability'] <= 1
        
        # Validate paths are sorted by efficiency
        if len(data['path_options']) > 1:
            for i in range(len(data['path_options']) - 1):
                current_efficiency = (data['path_options'][i]['success_probability'] / 
                                    data['path_options'][i]['total_cost'])
                next_efficiency = (data['path_options'][i + 1]['success_probability'] / 
                                 data['path_options'][i + 1]['total_cost'])
                assert current_efficiency >= next_efficiency
    
    def test_prd04_requirement_2_cost_intelligence(self, base_url):
        """Validate PRD 04 Requirement 2: Comprehensive Cost Intelligence."""
        
        # Test cost calculation with hidden costs
        request_data = {
            'certification_ids': [1, 2],  # CISSP, CISM
            'location': 'San Francisco, CA',
            'currency': 'USD',
            'include_hidden_costs': True,
            'study_materials': True,
            'practice_exams': True,
            'bootcamp_training': False
        }
        
        response = requests.post(
            f"{base_url}/api/v1/cost-calculator/calculate",
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate comprehensive cost breakdown
        assert 'total_cost' in data
        assert 'cost_breakdown' in data
        assert 'currency' in data
        assert 'location_adjustments' in data
        
        # Validate cost components
        cost_breakdown = data['cost_breakdown']
        assert 'exam_fees' in cost_breakdown
        assert 'study_materials' in cost_breakdown
        assert 'practice_exams' in cost_breakdown
        
        # Validate hidden costs are included
        if data.get('include_hidden_costs'):
            assert 'travel_costs' in cost_breakdown or 'time_opportunity_cost' in cost_breakdown
        
        # Validate location adjustments
        assert isinstance(data['location_adjustments'], dict)
        assert data['total_cost'] > 0
    
    def test_prd04_requirement_3_roi_analysis(self, base_url):
        """Validate PRD 04 Requirement 3: Advanced ROI Analysis."""
        
        # Test comprehensive ROI analysis
        request_data = {
            'certification_id': 1,  # CISSP
            'current_role_id': 1,   # Security Analyst
            'target_role_id': 3,    # Senior Security Engineer
            'investment_cost': 3000,
            'location': 'remote',
            'experience_years': 5,
            'current_salary': 75000
        }
        
        response = requests.post(
            f"{base_url}/api/v1/salary-intelligence/roi-analysis",
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate ROI analysis components
        assert 'expected_salary_increase' in data
        assert 'payback_period_months' in data
        assert 'five_year_roi' in data
        assert 'ten_year_roi' in data
        assert 'confidence_score' in data
        assert 'risk_factors' in data
        assert 'market_conditions' in data
        
        # Validate ROI calculations
        assert data['expected_salary_increase'] > 0
        assert data['payback_period_months'] > 0
        assert data['five_year_roi'] > 0
        assert 0 <= data['confidence_score'] <= 1
        
        # Validate risk assessment
        assert isinstance(data['risk_factors'], list)
        assert len(data['risk_factors']) > 0
        
        # Validate market intelligence integration
        assert isinstance(data['market_conditions'], dict)
    
    def test_prd04_requirement_4_budget_optimization(self, base_url):
        """Validate PRD 04 Requirement 4: Enterprise Budget Optimization."""
        
        # Test enterprise budget optimization
        request_data = {
            'enterprise_id': 1,
            'total_budget': 100000,
            'strategic_priorities': ['cybersecurity', 'cloud_security'],
            'timeline_months': 12,
            'team_constraints': {
                'security_team_size': 25,
                'experience_levels': {'junior': 10, 'mid': 10, 'senior': 5}
            }
        }
        
        response = requests.post(
            f"{base_url}/api/v1/budget-optimization/optimize",
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate optimization results
        assert 'optimized_allocation' in data
        assert 'projected_roi' in data
        assert 'cost_savings' in data
        assert 'efficiency_score' in data
        assert 'recommendations' in data
        assert 'risk_assessment' in data
        
        # Validate budget allocation
        allocation = data['optimized_allocation']
        total_allocated = sum(allocation.values())
        budget_variance = abs(total_allocated - data['total_budget']) / data['total_budget']
        assert budget_variance < 0.01  # Within 1%
        
        # Validate optimization metrics
        assert data['projected_roi'] > 0
        assert data['cost_savings'] >= 0
        assert 0 <= data['efficiency_score'] <= 100
        
        # Validate recommendations
        assert isinstance(data['recommendations'], list)
        assert len(data['recommendations']) > 0
    
    def test_prd04_requirement_5_market_intelligence(self, base_url):
        """Validate PRD 04 Requirement 5: Real-time Market Intelligence."""
        
        # Test market intelligence analysis
        request_data = {
            'region': 'north_america',
            'industry': 'technology',
            'timeframe': '12months',
            'include_projections': True
        }
        
        response = requests.post(
            f"{base_url}/api/v1/market-intelligence/analysis",
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate market intelligence components
        assert 'trends' in data
        assert 'locations' in data
        assert 'industries' in data
        
        # Validate trend analysis
        trends = data['trends']
        assert len(trends) > 0
        
        for trend in trends:
            assert 'certification' in trend
            assert 'demand_change' in trend
            assert 'salary_trend' in trend
            assert 'job_openings' in trend
            assert 'competition_level' in trend
            assert 'growth_projection' in trend
        
        # Validate location analysis
        locations = data['locations']
        assert len(locations) > 0
        
        for location in locations:
            assert 'location' in location
            assert 'average_salary' in location
            assert 'job_count' in location
            assert 'cost_of_living' in location
            assert 'demand_level' in location
        
        # Validate industry insights
        industries = data['industries']
        assert len(industries) > 0
        
        for industry in industries:
            assert 'industry' in industry
            assert 'top_certifications' in industry
            assert 'average_budget' in industry
            assert 'growth_rate' in industry
            assert 'key_trends' in industry
    
    def test_prd04_performance_requirements(self, base_url):
        """Validate PRD 04 Performance Requirements."""
        
        # Test response time requirements
        endpoints = [
            ('/api/v1/career-transition/pathfinding', {
                'current_role_id': 1, 'target_role_id': 3, 'max_budget': 5000,
                'max_timeline_months': 18, 'max_difficulty': 'Medium',
                'learning_style': 'mixed', 'study_hours_per_week': 15, 'currency': 'USD'
            }),
            ('/api/v1/salary-intelligence/roi-analysis', {
                'certification_id': 1, 'current_role_id': 1, 'target_role_id': 3,
                'investment_cost': 3000, 'location': 'remote', 'experience_years': 5
            }),
            ('/api/v1/budget-optimization/optimize', {
                'enterprise_id': 1, 'total_budget': 100000,
                'strategic_priorities': ['cybersecurity'], 'timeline_months': 12
            })
        ]
        
        for endpoint, payload in endpoints:
            start_time = time.time()
            response = requests.post(f"{base_url}{endpoint}", json=payload)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # Validate response time requirements
            if 'pathfinding' in endpoint:
                assert response_time < 3.0  # Career pathfinding < 3 seconds
            elif 'roi-analysis' in endpoint:
                assert response_time < 2.0  # ROI analysis < 2 seconds
            elif 'optimize' in endpoint:
                assert response_time < 5.0  # Budget optimization < 5 seconds
            
            assert response.status_code == 200
    
    def test_prd04_scalability_requirements(self, base_url):
        """Validate PRD 04 Scalability Requirements."""
        
        def make_concurrent_request():
            request_data = {
                'current_role_id': 1,
                'target_role_id': 3,
                'max_budget': 5000,
                'max_timeline_months': 18,
                'max_difficulty': 'Medium',
                'learning_style': 'mixed',
                'study_hours_per_week': 15,
                'currency': 'USD'
            }
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/v1/career-transition/pathfinding",
                json=request_data
            )
            end_time = time.time()
            
            return {
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code == 200
            }
        
        # Test concurrent load (10 simultaneous requests)
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_concurrent_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Validate all requests succeeded
        success_count = sum(1 for result in results if result['success'])
        assert success_count == 10  # 100% success rate
        
        # Validate average response time under load
        avg_response_time = sum(result['response_time'] for result in results) / len(results)
        assert avg_response_time < 5.0  # Average response time < 5 seconds under load
    
    def test_prd04_data_consistency(self, base_url):
        """Validate PRD 04 Data Consistency Requirements."""
        
        # Test data consistency across services
        
        # Step 1: Get career path
        path_request = {
            'current_role_id': 1, 'target_role_id': 3, 'max_budget': 5000,
            'max_timeline_months': 18, 'max_difficulty': 'Medium',
            'learning_style': 'mixed', 'study_hours_per_week': 15, 'currency': 'USD'
        }
        
        path_response = requests.post(
            f"{base_url}/api/v1/career-transition/pathfinding",
            json=path_request
        )
        
        assert path_response.status_code == 200
        path_data = path_response.json()
        selected_path = path_data['path_options'][0]
        
        # Step 2: Get cost calculation
        cost_request = {
            'certification_ids': [1, 2],
            'location': 'remote',
            'currency': 'USD',
            'include_hidden_costs': True
        }
        
        cost_response = requests.post(
            f"{base_url}/api/v1/cost-calculator/calculate",
            json=cost_request
        )
        
        assert cost_response.status_code == 200
        cost_data = cost_response.json()
        
        # Step 3: Validate consistency
        # Cost variance should be within acceptable range
        cost_variance = abs(selected_path['total_cost'] - cost_data['total_cost']) / cost_data['total_cost']
        assert cost_variance < 0.15  # Within 15% variance
    
    def test_prd04_system_health(self, base_url):
        """Validate PRD 04 System Health Requirements."""
        
        # Test all health endpoints
        health_endpoints = [
            '/api/v1/career-transition/health',
            '/api/v1/cost-calculator/health',
            '/api/v1/salary-intelligence/health',
            '/api/v1/budget-optimization/health'
        ]
        
        for endpoint in health_endpoints:
            response = requests.get(f"{base_url}{endpoint}")
            
            assert response.status_code == 200
            data = response.json()
            
            # Validate health response structure
            assert 'status' in data
            assert 'service' in data
            assert 'version' in data
            assert 'features' in data
            assert 'timestamp' in data
            
            # Validate system is healthy
            assert data['status'] == 'healthy'
            assert isinstance(data['features'], list)
            assert len(data['features']) > 0
    
    def test_prd04_complete_workflow(self, base_url):
        """Validate PRD 04 Complete End-to-End Workflow."""
        
        # Complete workflow: Career planning → Cost analysis → ROI analysis → Budget optimization
        
        # Step 1: Career pathfinding
        career_request = {
            'current_role_id': 1, 'target_role_id': 3, 'max_budget': 5000,
            'max_timeline_months': 18, 'max_difficulty': 'Medium',
            'learning_style': 'mixed', 'study_hours_per_week': 15, 'currency': 'USD'
        }
        
        career_response = requests.post(
            f"{base_url}/api/v1/career-transition/pathfinding",
            json=career_request
        )
        
        assert career_response.status_code == 200
        career_data = career_response.json()
        selected_path = career_data['path_options'][0]
        
        # Step 2: ROI analysis
        roi_request = {
            'certification_id': 1, 'current_role_id': 1, 'target_role_id': 3,
            'investment_cost': selected_path['total_cost'],
            'location': 'remote', 'experience_years': 5
        }
        
        roi_response = requests.post(
            f"{base_url}/api/v1/salary-intelligence/roi-analysis",
            json=roi_request
        )
        
        assert roi_response.status_code == 200
        roi_data = roi_response.json()
        
        # Step 3: Budget optimization (enterprise level)
        budget_request = {
            'enterprise_id': 1, 'total_budget': 100000,
            'strategic_priorities': ['cybersecurity'], 'timeline_months': 12
        }
        
        budget_response = requests.post(
            f"{base_url}/api/v1/budget-optimization/optimize",
            json=budget_request
        )
        
        assert budget_response.status_code == 200
        budget_data = budget_response.json()
        
        # Validate complete workflow success
        assert selected_path['total_cost'] > 0
        assert roi_data['five_year_roi'] > 0
        assert budget_data['projected_roi'] > 0
        
        # Validate workflow consistency
        assert roi_data['investment_cost'] == selected_path['total_cost']
        
        print("✅ PRD 04 - Agent 4 Career & Cost Intelligence - COMPLETE VALIDATION PASSED")
        print(f"   Career Path Cost: ${selected_path['total_cost']}")
        print(f"   5-Year ROI: {roi_data['five_year_roi']}%")
        print(f"   Enterprise ROI: {budget_data['projected_roi']}%")
        print(f"   System Status: All services operational")
