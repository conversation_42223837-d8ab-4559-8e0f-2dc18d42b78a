"""Comprehensive test runner with advanced reporting and analysis"""
import pytest
import sys
import os
import time
import json
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestRunner:
    """Advanced test runner with comprehensive reporting and analysis"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
    def run_test_suite(self, test_categories: List[str] = None, 
                      coverage: bool = True, 
                      parallel: bool = False,
                      verbose: bool = True) -> Dict[str, Any]:
        """Run comprehensive test suite with specified options"""
        
        logger.info("Starting comprehensive test suite execution")
        self.start_time = datetime.now()
        
        # Define test categories
        all_categories = {
            'unit': 'tests/test_models tests/test_utils',
            'integration': 'tests/test_api tests/test_services tests/test_integration',
            'security': 'tests/test_security',
            'performance': 'tests/test_performance',
            'e2e': 'tests/e2e',
            'admin': 'tests/test_admin',
            'components': 'tests/test_components',
            'cost_calculator': 'tests/test_cost_calculator',
            'deployment': 'tests/test_deployment'
        }
        
        # Determine which categories to run
        categories_to_run = test_categories if test_categories else list(all_categories.keys())
        
        results = {}
        
        for category in categories_to_run:
            if category in all_categories:
                logger.info(f"Running {category} tests...")
                result = self._run_category(category, all_categories[category], 
                                          coverage, parallel, verbose)
                results[category] = result
            else:
                logger.warning(f"Unknown test category: {category}")
        
        self.end_time = datetime.now()
        self.test_results = results
        
        # Generate comprehensive report
        report = self._generate_report()
        
        return report
    
    def _run_category(self, category: str, test_paths: str, 
                     coverage: bool, parallel: bool, verbose: bool) -> Dict[str, Any]:
        """Run tests for a specific category"""
        
        cmd = ['python', '-m', 'pytest']
        
        # Add test paths
        cmd.extend(test_paths.split())
        
        # Add common options
        cmd.extend([
            '--tb=short',
            '--strict-markers',
            '--disable-warnings' if not verbose else '--verbose'
        ])
        
        # Add coverage if requested
        if coverage:
            cmd.extend([
                '--cov=.',
                '--cov-report=term-missing',
                '--cov-report=html:htmlcov',
                '--cov-report=json:coverage.json'
            ])
        
        # Add parallel execution if requested
        if parallel:
            cmd.extend(['-n', 'auto'])
        
        # Add output format
        cmd.extend(['--json-report', '--json-report-file=test_results.json'])
        
        start_time = time.time()
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env['DATABASE_URL'] = 'sqlite:///test.db'
            env['PYTHONPATH'] = str(self.project_root)
            
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                env=env,
                timeout=1800  # 30 minutes timeout
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Parse results
            test_result = {
                'category': category,
                'duration': duration,
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
            
            # Try to parse JSON report if available
            json_report_path = self.project_root / 'test_results.json'
            if json_report_path.exists():
                try:
                    with open(json_report_path, 'r') as f:
                        json_data = json.load(f)
                        test_result['detailed_results'] = json_data
                except Exception as e:
                    logger.warning(f"Could not parse JSON report: {e}")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            logger.error(f"Test category {category} timed out after 30 minutes")
            return {
                'category': category,
                'duration': 1800,
                'return_code': -1,
                'success': False,
                'error': 'Timeout'
            }
        except Exception as e:
            logger.error(f"Error running {category} tests: {e}")
            return {
                'category': category,
                'duration': 0,
                'return_code': -1,
                'success': False,
                'error': str(e)
            }
    
    def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        
        total_duration = (self.end_time - self.start_time).total_seconds()
        
        # Calculate summary statistics
        total_categories = len(self.test_results)
        successful_categories = sum(1 for r in self.test_results.values() if r['success'])
        failed_categories = total_categories - successful_categories
        
        # Extract detailed test statistics
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        skipped_tests = 0
        
        for category_result in self.test_results.values():
            if 'detailed_results' in category_result:
                summary = category_result['detailed_results'].get('summary', {})
                total_tests += summary.get('total', 0)
                passed_tests += summary.get('passed', 0)
                failed_tests += summary.get('failed', 0)
                skipped_tests += summary.get('skipped', 0)
        
        # Generate report
        report = {
            'execution_info': {
                'start_time': self.start_time.isoformat(),
                'end_time': self.end_time.isoformat(),
                'total_duration_seconds': total_duration,
                'total_duration_formatted': self._format_duration(total_duration)
            },
            'summary': {
                'total_categories': total_categories,
                'successful_categories': successful_categories,
                'failed_categories': failed_categories,
                'success_rate': successful_categories / total_categories if total_categories > 0 else 0,
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'skipped_tests': skipped_tests,
                'test_success_rate': passed_tests / total_tests if total_tests > 0 else 0
            },
            'category_results': self.test_results,
            'recommendations': self._generate_recommendations()
        }
        
        # Save report to file
        report_file = self.project_root / 'test_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Test report saved to {report_file}")
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Check for failed categories
        failed_categories = [cat for cat, result in self.test_results.items() 
                           if not result['success']]
        
        if failed_categories:
            recommendations.append(
                f"Address failures in categories: {', '.join(failed_categories)}"
            )
        
        # Check for slow tests
        slow_categories = [cat for cat, result in self.test_results.items() 
                          if result.get('duration', 0) > 300]  # 5 minutes
        
        if slow_categories:
            recommendations.append(
                f"Optimize performance for slow test categories: {', '.join(slow_categories)}"
            )
        
        # Check coverage (if available)
        # This would require parsing coverage reports
        
        # General recommendations
        if not failed_categories:
            recommendations.append("All test categories passed! Consider adding more edge case tests.")
        
        recommendations.append("Regularly review and update test cases to maintain quality.")
        recommendations.append("Consider implementing continuous integration for automated testing.")
        
        return recommendations
    
    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format"""
        if seconds < 60:
            return f"{seconds:.1f} seconds"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f} minutes"
        else:
            hours = seconds / 3600
            return f"{hours:.1f} hours"
    
    def print_summary(self):
        """Print test execution summary"""
        if not self.test_results:
            logger.warning("No test results available")
            return
        
        print("\n" + "="*80)
        print("COMPREHENSIVE TEST SUITE SUMMARY")
        print("="*80)
        
        total_duration = (self.end_time - self.start_time).total_seconds()
        print(f"Total execution time: {self._format_duration(total_duration)}")
        print(f"Start time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"End time: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\nCategory Results:")
        print("-" * 50)
        
        for category, result in self.test_results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            duration = self._format_duration(result.get('duration', 0))
            print(f"{category:20} {status:10} {duration}")
        
        # Overall statistics
        successful = sum(1 for r in self.test_results.values() if r['success'])
        total = len(self.test_results)
        success_rate = (successful / total * 100) if total > 0 else 0
        
        print(f"\nOverall Success Rate: {success_rate:.1f}% ({successful}/{total})")
        
        # Failed categories details
        failed_categories = [cat for cat, result in self.test_results.items() 
                           if not result['success']]
        
        if failed_categories:
            print(f"\n❌ Failed Categories: {', '.join(failed_categories)}")
            print("\nReview the detailed logs above for specific failure information.")
        else:
            print("\n🎉 All test categories passed successfully!")
        
        print("="*80)


def main():
    """Main entry point for test runner"""
    parser = argparse.ArgumentParser(description='Comprehensive Test Runner')
    parser.add_argument('--categories', nargs='+', 
                       help='Test categories to run (unit, integration, security, performance, e2e)')
    parser.add_argument('--no-coverage', action='store_true', 
                       help='Disable coverage reporting')
    parser.add_argument('--parallel', action='store_true', 
                       help='Run tests in parallel')
    parser.add_argument('--quiet', action='store_true', 
                       help='Reduce output verbosity')
    parser.add_argument('--project-root', 
                       help='Project root directory')
    
    args = parser.parse_args()
    
    # Create test runner
    runner = TestRunner(args.project_root)
    
    # Run tests
    try:
        report = runner.run_test_suite(
            test_categories=args.categories,
            coverage=not args.no_coverage,
            parallel=args.parallel,
            verbose=not args.quiet
        )
        
        # Print summary
        runner.print_summary()
        
        # Exit with appropriate code
        failed_categories = sum(1 for r in runner.test_results.values() if not r['success'])
        sys.exit(1 if failed_categories > 0 else 0)
        
    except KeyboardInterrupt:
        logger.info("Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
