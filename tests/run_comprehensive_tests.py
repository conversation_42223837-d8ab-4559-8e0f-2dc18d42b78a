#!/usr/bin/env python3
"""Unified Comprehensive Test Runner for CertRats Platform.

This script runs all test suites including Agent 3 (Enterprise Analytics) and
CertRatsAgent4 (Unified AI Intelligence) with comprehensive coverage reporting.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import json
from datetime import datetime


def run_command(command, description=""):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    print(f"Running: {description or command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=False
        )
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0, result
    except Exception as e:
        print(f"Error running command: {e}")
        return False, None


def setup_test_environment():
    """Setup test environment variables and dependencies."""
    print("Setting up test environment...")
    
    # Set environment variables
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = "sqlite:///test_certratsagent4.db"
    os.environ["AI_MODEL_PATH"] = "/tmp/test_models"
    os.environ["LOG_LEVEL"] = "INFO"
    
    # Install test dependencies
    success, _ = run_command(
        "pip install pytest pytest-cov pytest-asyncio pytest-mock coverage",
        "Installing test dependencies"
    )
    
    return success


def run_unit_tests():
    """Run unit tests with coverage."""
    print("\n🧪 Running Unit Tests...")
    
    command = (
        "python -m pytest tests/test_certratsagent4.py "
        "--cov=services --cov=api --cov=schemas "
        "--cov-report=term-missing "
        "--cov-report=html:htmlcov/unit "
        "--cov-report=json:coverage_unit.json "
        "-v --tb=short"
    )
    
    return run_command(command, "Unit Tests with Coverage")


def run_api_tests():
    """Run API endpoint tests."""
    print("\n🌐 Running API Tests...")
    
    command = (
        "python -m pytest tests/test_salary_intelligence_api.py "
        "--cov=api --cov-append "
        "--cov-report=term-missing "
        "--cov-report=html:htmlcov/api "
        "--cov-report=json:coverage_api.json "
        "-v --tb=short"
    )
    
    return run_command(command, "API Endpoint Tests")


def run_enterprise_tests():
    """Run enterprise functionality tests."""
    print("\n🏢 Running Enterprise Tests...")
    
    command = (
        "python -m pytest tests/test_enterprise_budget_api.py "
        "--cov=services.enterprise_budget_optimizer "
        "--cov-append "
        "--cov-report=term-missing "
        "--cov-report=html:htmlcov/enterprise "
        "--cov-report=json:coverage_enterprise.json "
        "-v --tb=short"
    )
    
    return run_command(command, "Enterprise Budget Optimization Tests")


def run_integration_tests():
    """Run integration tests."""
    print("\n🔗 Running Integration Tests...")
    
    command = (
        "python -m pytest tests/ -k 'integration or workflow or end_to_end' "
        "--cov-append "
        "--cov-report=term-missing "
        "-v --tb=short"
    )
    
    return run_command(command, "Integration Tests")


def run_performance_tests():
    """Run performance tests."""
    print("\n⚡ Running Performance Tests...")
    
    command = (
        "python -m pytest tests/ -k 'performance or concurrent or scalability' "
        "-v --tb=short"
    )
    
    return run_command(command, "Performance Tests")


def generate_coverage_report():
    """Generate comprehensive coverage report."""
    print("\n📊 Generating Coverage Report...")
    
    # Combine coverage data
    run_command("coverage combine", "Combining coverage data")
    
    # Generate HTML report
    success, _ = run_command(
        "coverage html -d htmlcov/combined",
        "Generating HTML coverage report"
    )
    
    # Generate JSON report
    run_command(
        "coverage json -o coverage_combined.json",
        "Generating JSON coverage report"
    )
    
    # Generate text report
    success_text, result = run_command(
        "coverage report --show-missing",
        "Generating text coverage report"
    )
    
    return success and success_text, result


def analyze_coverage_results():
    """Analyze coverage results and provide insights."""
    print("\n📈 Analyzing Coverage Results...")
    
    try:
        # Read coverage data
        with open("coverage_combined.json", "r") as f:
            coverage_data = json.load(f)
        
        total_coverage = coverage_data["totals"]["percent_covered"]
        
        print(f"\n{'='*60}")
        print(f"COVERAGE ANALYSIS SUMMARY")
        print(f"{'='*60}")
        print(f"Overall Coverage: {total_coverage:.2f}%")
        
        # Analyze by file
        files = coverage_data["files"]
        high_coverage = []
        medium_coverage = []
        low_coverage = []
        
        for file_path, file_data in files.items():
            coverage_percent = file_data["summary"]["percent_covered"]
            
            if coverage_percent >= 90:
                high_coverage.append((file_path, coverage_percent))
            elif coverage_percent >= 70:
                medium_coverage.append((file_path, coverage_percent))
            else:
                low_coverage.append((file_path, coverage_percent))
        
        print(f"\nHigh Coverage (≥90%): {len(high_coverage)} files")
        for file_path, percent in sorted(high_coverage, key=lambda x: x[1], reverse=True)[:5]:
            print(f"  ✅ {file_path}: {percent:.1f}%")
        
        print(f"\nMedium Coverage (70-89%): {len(medium_coverage)} files")
        for file_path, percent in sorted(medium_coverage, key=lambda x: x[1], reverse=True)[:5]:
            print(f"  ⚠️  {file_path}: {percent:.1f}%")
        
        print(f"\nLow Coverage (<70%): {len(low_coverage)} files")
        for file_path, percent in sorted(low_coverage, key=lambda x: x[1])[:5]:
            print(f"  ❌ {file_path}: {percent:.1f}%")
        
        # Coverage recommendations
        print(f"\n{'='*60}")
        print(f"RECOMMENDATIONS")
        print(f"{'='*60}")
        
        if total_coverage >= 95:
            print("🎉 Excellent coverage! Your code is well-tested.")
        elif total_coverage >= 85:
            print("✅ Good coverage! Consider adding tests for edge cases.")
        elif total_coverage >= 70:
            print("⚠️  Moderate coverage. Focus on testing critical paths.")
        else:
            print("❌ Low coverage. Significant testing improvements needed.")
        
        if low_coverage:
            print(f"\nPriority: Add tests for {len(low_coverage)} files with low coverage")
        
        return total_coverage >= 85
        
    except FileNotFoundError:
        print("❌ Coverage data not found. Tests may have failed.")
        return False
    except Exception as e:
        print(f"❌ Error analyzing coverage: {e}")
        return False


def run_linting():
    """Run code linting and style checks."""
    print("\n🔍 Running Code Quality Checks...")
    
    # Run flake8 for style checking
    success_flake8, _ = run_command(
        "flake8 services/ api/ schemas/ --max-line-length=100 --ignore=E203,W503",
        "Running flake8 style checks"
    )
    
    # Run mypy for type checking
    success_mypy, _ = run_command(
        "mypy services/ api/ schemas/ --ignore-missing-imports",
        "Running mypy type checks"
    )
    
    return success_flake8 and success_mypy


def generate_test_report():
    """Generate comprehensive test report."""
    print("\n📋 Generating Test Report...")
    
    report = {
        "test_run": {
            "timestamp": datetime.utcnow().isoformat(),
            "environment": "test",
            "python_version": sys.version,
            "platform": sys.platform
        },
        "results": {
            "unit_tests": "completed",
            "api_tests": "completed", 
            "enterprise_tests": "completed",
            "integration_tests": "completed",
            "performance_tests": "completed"
        }
    }
    
    # Save report
    with open("test_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("✅ Test report saved to test_report.json")
    return True


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="CertRatsAgent4 Comprehensive Test Runner")
    parser.add_argument("--unit", action="store_true", help="Run only unit tests")
    parser.add_argument("--api", action="store_true", help="Run only API tests")
    parser.add_argument("--enterprise", action="store_true", help="Run only enterprise tests")
    parser.add_argument("--integration", action="store_true", help="Run only integration tests")
    parser.add_argument("--performance", action="store_true", help="Run only performance tests")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report only")
    parser.add_argument("--lint", action="store_true", help="Run linting only")
    parser.add_argument("--all", action="store_true", help="Run all tests (default)")
    
    args = parser.parse_args()
    
    # If no specific test type is specified, run all
    if not any([args.unit, args.api, args.enterprise, args.integration, args.performance, args.coverage, args.lint]):
        args.all = True
    
    print("🚀 CertRatsAgent4 Comprehensive Test Suite")
    print("=" * 60)
    
    # Setup environment
    if not setup_test_environment():
        print("❌ Failed to setup test environment")
        return 1
    
    success_count = 0
    total_count = 0
    
    # Run selected tests
    if args.all or args.unit:
        total_count += 1
        success, _ = run_unit_tests()
        if success:
            success_count += 1
    
    if args.all or args.api:
        total_count += 1
        success, _ = run_api_tests()
        if success:
            success_count += 1
    
    if args.all or args.enterprise:
        total_count += 1
        success, _ = run_enterprise_tests()
        if success:
            success_count += 1
    
    if args.all or args.integration:
        total_count += 1
        success, _ = run_integration_tests()
        if success:
            success_count += 1
    
    if args.all or args.performance:
        total_count += 1
        success, _ = run_performance_tests()
        if success:
            success_count += 1
    
    # Generate coverage report
    if args.all or args.coverage:
        coverage_success, _ = generate_coverage_report()
        coverage_good = analyze_coverage_results()
    
    # Run linting
    if args.all or args.lint:
        lint_success = run_linting()
    
    # Generate test report
    generate_test_report()
    
    # Final summary
    print(f"\n{'='*60}")
    print(f"TEST EXECUTION SUMMARY")
    print(f"{'='*60}")
    print(f"Tests Passed: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All tests passed successfully!")
        return 0
    else:
        print(f"❌ {total_count - success_count} test suite(s) failed")
        return 1


if __name__ == "__main__":
    exit(main())
