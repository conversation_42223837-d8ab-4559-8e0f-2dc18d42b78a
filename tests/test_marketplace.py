"""Unit tests for marketplace functionality.

This module contains comprehensive tests for the Agent 5 Marketplace & Integration Hub,
including vendor management, course operations, commission calculations, and analytics.
"""

import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from models.marketplace import (
    MarketplaceVendor, MarketplaceCourse, PartnershipAgreement, CommissionRecord,
    CourseEnrollment, CourseReview, VendorStatus, CourseStatus, PartnershipTier
)
from services.marketplace_service import MarketplaceService
from schemas.marketplace import (
    MarketplaceVendorCreate, MarketplaceCourseCreate, PartnershipAgreementCreate,
    CourseEnrollmentCreate, CourseReviewCreate, MarketplaceSearchRequest
)


class TestMarketplaceService:
    """Test cases for MarketplaceService."""
    
    def test_create_vendor(self, db_session: Session):
        """Test vendor creation."""
        service = MarketplaceService(db_session)
        
        vendor_data = MarketplaceVendorCreate(
            vendor_name="Test Vendor",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>",
            contact_phone="******-0123",
            website_url="https://testvendor.com",
            business_name="Test Vendor LLC",
            content_categories=["cybersecurity", "networking"],
            specializations=["CISSP", "CompTIA Security+"],
            commission_rate=0.25
        )
        
        vendor = service.create_vendor(vendor_data)
        
        assert vendor.id is not None
        assert vendor.vendor_name == "Test Vendor"
        assert vendor.vendor_slug == "test-vendor"
        assert vendor.contact_email == "<EMAIL>"
        assert vendor.status == VendorStatus.PENDING
        assert vendor.commission_rate == 0.25
        assert vendor.quality_score == 0.0
        assert vendor.total_courses == 0
    
    def test_create_vendor_duplicate_slug(self, db_session: Session):
        """Test vendor creation with duplicate slug."""
        service = MarketplaceService(db_session)
        
        # Create first vendor
        vendor_data1 = MarketplaceVendorCreate(
            vendor_name="Test Vendor 1",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>"
        )
        service.create_vendor(vendor_data1)
        
        # Try to create second vendor with same slug
        vendor_data2 = MarketplaceVendorCreate(
            vendor_name="Test Vendor 2",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>"
        )
        
        with pytest.raises(ValueError, match="already exists"):
            service.create_vendor(vendor_data2)
    
    def test_verify_vendor(self, db_session: Session):
        """Test vendor verification."""
        service = MarketplaceService(db_session)
        
        # Create vendor
        vendor_data = MarketplaceVendorCreate(
            vendor_name="Test Vendor",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>"
        )
        vendor = service.create_vendor(vendor_data)
        
        # Verify vendor
        verified_vendor = service.verify_vendor(vendor.id, "verified")
        
        assert verified_vendor.verification_status == "verified"
        assert verified_vendor.verification_date is not None
        assert verified_vendor.status == VendorStatus.ACTIVE
    
    def test_create_course(self, db_session: Session):
        """Test course creation."""
        service = MarketplaceService(db_session)
        
        # Create and verify vendor first
        vendor_data = MarketplaceVendorCreate(
            vendor_name="Test Vendor",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>"
        )
        vendor = service.create_vendor(vendor_data)
        service.verify_vendor(vendor.id, "verified")
        
        # Create course
        course_data = MarketplaceCourseCreate(
            vendor_id=vendor.id,
            course_title="Test Course",
            course_slug="test-course",
            course_description="A comprehensive test course",
            course_level="intermediate",
            duration_hours=40.0,
            price=Decimal("299.99"),
            currency="USD",
            learning_objectives=["Learn testing", "Master automation"],
            target_certifications=["CISSP", "CompTIA Security+"],
            tags=["security", "testing"]
        )
        
        course = service.create_course(course_data)
        
        assert course.id is not None
        assert course.vendor_id == vendor.id
        assert course.course_title == "Test Course"
        assert course.course_slug == "test-course"
        assert course.course_level == "intermediate"
        assert course.price == Decimal("299.99")
        assert course.status == CourseStatus.DRAFT
        assert len(course.learning_objectives) == 2
        assert len(course.target_certifications) == 2
    
    def test_course_approval_workflow(self, db_session: Session):
        """Test course approval and publication workflow."""
        service = MarketplaceService(db_session)
        
        # Create vendor and course
        vendor_data = MarketplaceVendorCreate(
            vendor_name="Test Vendor",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>"
        )
        vendor = service.create_vendor(vendor_data)
        service.verify_vendor(vendor.id, "verified")
        
        course_data = MarketplaceCourseCreate(
            vendor_id=vendor.id,
            course_title="Test Course",
            course_slug="test-course",
            course_level="beginner",
            price=Decimal("199.99")
        )
        course = service.create_course(course_data)
        
        # Approve course
        approved_course = service.approve_course(course.id)
        assert approved_course.status == CourseStatus.APPROVED
        assert approved_course.approval_date is not None
        
        # Publish course
        published_course = service.publish_course(course.id)
        assert published_course.status == CourseStatus.PUBLISHED
        assert published_course.publication_date is not None
    
    def test_commission_calculation(self, db_session: Session):
        """Test commission calculation with bonuses."""
        service = MarketplaceService(db_session)
        
        # Create vendor with high ratings
        vendor_data = MarketplaceVendorCreate(
            vendor_name="High Quality Vendor",
            vendor_slug="hq-vendor",
            contact_email="<EMAIL>",
            commission_rate=0.25
        )
        vendor = service.create_vendor(vendor_data)
        
        # Set high performance metrics
        vendor.average_rating = 4.8
        vendor.total_enrollments = 1500
        db_session.commit()
        
        # Calculate commission
        commission_calc = service.calculate_commission(vendor.id, Decimal("1000.00"))
        
        assert commission_calc['base_commission'] == 0.25
        assert commission_calc['quality_bonus'] == 0.05  # 4.8 rating >= 4.5
        assert commission_calc['volume_bonus'] == 0.02   # 1500 enrollments >= 1000
        assert commission_calc['total_commission_rate'] == 0.32  # 25% + 5% + 2%
        assert commission_calc['commission_amount'] == Decimal("320.00")  # 32% of $1000
    
    def test_course_search(self, db_session: Session):
        """Test course search functionality."""
        service = MarketplaceService(db_session)
        
        # Create vendor and courses
        vendor_data = MarketplaceVendorCreate(
            vendor_name="Test Vendor",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>"
        )
        vendor = service.create_vendor(vendor_data)
        service.verify_vendor(vendor.id, "verified")
        
        # Create multiple courses
        courses_data = [
            {
                "course_title": "CISSP Preparation",
                "course_slug": "cissp-prep",
                "course_level": "advanced",
                "price": Decimal("499.99"),
                "target_certifications": ["CISSP"],
                "tags": ["security", "management"]
            },
            {
                "course_title": "CompTIA Security+ Basics",
                "course_slug": "security-plus-basics",
                "course_level": "beginner",
                "price": Decimal("199.99"),
                "target_certifications": ["CompTIA Security+"],
                "tags": ["security", "fundamentals"]
            },
            {
                "course_title": "Network Security Advanced",
                "course_slug": "network-security-adv",
                "course_level": "advanced",
                "price": Decimal("399.99"),
                "target_certifications": ["CCNA Security"],
                "tags": ["networking", "security"]
            }
        ]
        
        created_courses = []
        for course_data in courses_data:
            course_create = MarketplaceCourseCreate(
                vendor_id=vendor.id,
                **course_data
            )
            course = service.create_course(course_create)
            service.approve_course(course.id)
            service.publish_course(course.id)
            created_courses.append(course)
        
        # Test search by query
        search_request = MarketplaceSearchRequest(
            query="CISSP",
            page=1,
            per_page=10
        )
        courses, total = service.search_courses(search_request)
        assert total == 1
        assert courses[0].course_title == "CISSP Preparation"
        
        # Test search by level
        search_request = MarketplaceSearchRequest(
            level="advanced",
            page=1,
            per_page=10
        )
        courses, total = service.search_courses(search_request)
        assert total == 2
        
        # Test search by price range
        search_request = MarketplaceSearchRequest(
            price_min=Decimal("200.00"),
            price_max=Decimal("400.00"),
            page=1,
            per_page=10
        )
        courses, total = service.search_courses(search_request)
        assert total == 1
        assert courses[0].course_title == "Network Security Advanced"
    
    def test_enrollment_process(self, db_session: Session):
        """Test course enrollment process."""
        service = MarketplaceService(db_session)
        
        # Create vendor and course
        vendor_data = MarketplaceVendorCreate(
            vendor_name="Test Vendor",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>"
        )
        vendor = service.create_vendor(vendor_data)
        service.verify_vendor(vendor.id, "verified")
        
        course_data = MarketplaceCourseCreate(
            vendor_id=vendor.id,
            course_title="Test Course",
            course_slug="test-course",
            course_level="beginner",
            price=Decimal("199.99")
        )
        course = service.create_course(course_data)
        service.approve_course(course.id)
        service.publish_course(course.id)
        
        # Enroll user
        enrollment_data = CourseEnrollmentCreate(
            course_id=course.id,
            user_id="user123",
            purchase_price=Decimal("199.99"),
            currency="USD",
            payment_method="credit_card"
        )
        
        enrollment = service.enroll_user_in_course(enrollment_data)
        
        assert enrollment.id is not None
        assert enrollment.course_id == course.id
        assert enrollment.user_id == "user123"
        assert enrollment.purchase_price == Decimal("199.99")
        assert enrollment.status == "active"
        assert enrollment.progress_percentage == 0.0
        
        # Verify course enrollment count updated
        db_session.refresh(course)
        assert course.enrollment_count == 1
        
        # Verify vendor enrollment count updated
        db_session.refresh(vendor)
        assert vendor.total_enrollments == 1
    
    def test_course_review_system(self, db_session: Session):
        """Test course review and rating system."""
        service = MarketplaceService(db_session)
        
        # Create vendor and course
        vendor_data = MarketplaceVendorCreate(
            vendor_name="Test Vendor",
            vendor_slug="test-vendor",
            contact_email="<EMAIL>"
        )
        vendor = service.create_vendor(vendor_data)
        service.verify_vendor(vendor.id, "verified")
        
        course_data = MarketplaceCourseCreate(
            vendor_id=vendor.id,
            course_title="Test Course",
            course_slug="test-course",
            course_level="beginner",
            price=Decimal("199.99")
        )
        course = service.create_course(course_data)
        service.approve_course(course.id)
        service.publish_course(course.id)
        
        # Add review
        review_data = CourseReviewCreate(
            course_id=course.id,
            user_id="user123",
            rating=5,
            title="Excellent Course!",
            review_text="This course exceeded my expectations.",
            is_verified_purchase=True
        )
        
        review = service.add_course_review(review_data)
        
        assert review.id is not None
        assert review.course_id == course.id
        assert review.user_id == "user123"
        assert review.rating == 5
        assert review.title == "Excellent Course!"
        assert review.is_verified_purchase is True
        
        # Verify course rating updated
        db_session.refresh(course)
        assert course.review_count == 1
        assert course.average_rating == 5.0
        
        # Verify vendor rating updated
        db_session.refresh(vendor)
        assert vendor.total_reviews == 1
        assert vendor.average_rating == 5.0
