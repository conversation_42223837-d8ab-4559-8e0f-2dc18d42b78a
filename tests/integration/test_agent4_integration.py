"""Integration tests for Agent 4 Career & Cost Intelligence system.

This module provides comprehensive integration tests that verify the complete
Agent 4 system works end-to-end, including career pathfinding, cost calculation,
salary intelligence, and budget optimization.
"""

import pytest
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any


class TestAgent4Integration:
    """Integration test suite for Agent 4 Career & Cost Intelligence."""
    
    @pytest.fixture
    def base_url(self):
        """Base URL for API endpoints."""
        return "http://localhost:8000"
    
    @pytest.fixture
    def sample_user_profile(self):
        """Sample user profile for testing."""
        return {
            'user_id': 1,
            'current_role': 'Security Analyst',
            'experience_years': 3,
            'location': 'remote',
            'current_salary': 75000,
            'budget': 5000,
            'timeline_months': 18,
            'learning_style': 'mixed',
            'study_hours_per_week': 15
        }
    
    @pytest.fixture
    def sample_enterprise_profile(self):
        """Sample enterprise profile for testing."""
        return {
            'enterprise_id': 1,
            'company_name': 'TechCorp Security',
            'industry': 'technology',
            'employee_count': 100,
            'security_team_size': 25,
            'annual_training_budget': 200000,
            'strategic_priorities': ['cybersecurity', 'cloud_security', 'compliance']
        }
    
    def test_complete_career_transition_workflow(self, base_url, sample_user_profile):
        """Test complete career transition workflow from analysis to execution."""
        
        # Step 1: Get career pathfinding analysis
        pathfinding_request = {
            'current_role_id': 1,  # Security Analyst
            'target_role_id': 3,   # Senior Security Engineer
            'max_budget': sample_user_profile['budget'],
            'max_timeline_months': sample_user_profile['timeline_months'],
            'max_difficulty': 'Medium',
            'learning_style': sample_user_profile['learning_style'],
            'study_hours_per_week': sample_user_profile['study_hours_per_week'],
            'currency': 'USD'
        }
        
        pathfinding_response = requests.post(
            f"{base_url}/api/v1/career-transition/pathfinding",
            json=pathfinding_request
        )
        
        assert pathfinding_response.status_code == 200
        pathfinding_data = pathfinding_response.json()
        
        # Verify pathfinding results
        assert 'path_options' in pathfinding_data
        assert len(pathfinding_data['path_options']) > 0
        
        selected_path = pathfinding_data['path_options'][0]
        assert 'total_cost' in selected_path
        assert 'estimated_duration_months' in selected_path
        assert 'certifications' in selected_path
        
        # Step 2: Analyze ROI for the selected path
        roi_request = {
            'certification_id': 1,  # CISSP (assuming first cert in path)
            'current_role_id': 1,
            'target_role_id': 3,
            'investment_cost': selected_path['total_cost'],
            'location': sample_user_profile['location'],
            'experience_years': sample_user_profile['experience_years']
        }
        
        roi_response = requests.post(
            f"{base_url}/api/v1/salary-intelligence/roi-analysis",
            json=roi_request
        )
        
        assert roi_response.status_code == 200
        roi_data = roi_response.json()
        
        # Verify ROI analysis results
        assert 'expected_salary_increase' in roi_data
        assert 'payback_period_months' in roi_data
        assert 'five_year_roi' in roi_data
        assert 'confidence_score' in roi_data
        
        # Step 3: Get cost breakdown
        cost_request = {
            'certification_ids': [1, 2],  # CISSP, CISM
            'location': sample_user_profile['location'],
            'currency': 'USD',
            'include_hidden_costs': True
        }
        
        cost_response = requests.post(
            f"{base_url}/api/v1/cost-calculator/calculate",
            json=cost_request
        )
        
        assert cost_response.status_code == 200
        cost_data = cost_response.json()
        
        # Verify cost calculation results
        assert 'total_cost' in cost_data
        assert 'cost_breakdown' in cost_data
        assert 'currency' in cost_data
        
        # Step 4: Verify data consistency across services
        # The total cost from pathfinding should be consistent with cost calculator
        cost_variance = abs(selected_path['total_cost'] - cost_data['total_cost']) / cost_data['total_cost']
        assert cost_variance < 0.1  # Within 10% variance
        
        # ROI investment cost should match the calculated cost
        roi_cost_variance = abs(roi_data['investment_cost'] - cost_data['total_cost']) / cost_data['total_cost']
        assert roi_cost_variance < 0.05  # Within 5% variance
    
    def test_enterprise_budget_optimization_workflow(self, base_url, sample_enterprise_profile):
        """Test complete enterprise budget optimization workflow."""
        
        # Step 1: Get budget optimization recommendations
        budget_request = {
            'enterprise_id': sample_enterprise_profile['enterprise_id'],
            'total_budget': sample_enterprise_profile['annual_training_budget'],
            'strategic_priorities': sample_enterprise_profile['strategic_priorities'],
            'timeline_months': 12
        }
        
        budget_response = requests.post(
            f"{base_url}/api/v1/budget-optimization/optimize",
            json=budget_request
        )
        
        assert budget_response.status_code == 200
        budget_data = budget_response.json()
        
        # Verify budget optimization results
        assert 'optimized_allocation' in budget_data
        assert 'projected_roi' in budget_data
        assert 'cost_savings' in budget_data
        assert 'efficiency_score' in budget_data
        assert 'recommendations' in budget_data
        
        # Step 2: Calculate ROI for the optimized budget
        roi_request = {
            'enterprise_id': sample_enterprise_profile['enterprise_id'],
            'investment_amount': sample_enterprise_profile['annual_training_budget'],
            'training_programs': ['CISSP', 'CISM', 'Security+'],
            'timeline_months': 12
        }
        
        roi_response = requests.post(
            f"{base_url}/api/v1/budget-optimization/roi/calculate",
            json=roi_request
        )
        
        assert roi_response.status_code == 200
        roi_data = roi_response.json()
        
        # Verify enterprise ROI results
        assert 'projected_return' in roi_data
        assert 'roi_percentage' in roi_data
        assert 'payback_period_months' in roi_data
        assert 'net_present_value' in roi_data
        
        # Step 3: Get budget analytics
        analytics_response = requests.get(
            f"{base_url}/api/v1/budget-optimization/analytics/{sample_enterprise_profile['enterprise_id']}",
            params={'period_months': 12, 'include_projections': True}
        )
        
        assert analytics_response.status_code == 200
        analytics_data = analytics_response.json()
        
        # Verify analytics results
        assert 'total_budget_allocated' in analytics_data
        assert 'utilization_rate' in analytics_data
        assert 'roi_by_program' in analytics_data
        assert 'efficiency_metrics' in analytics_data
        
        # Step 4: Verify data consistency
        # Budget allocation should sum to total budget
        total_allocated = sum(budget_data['optimized_allocation'].values())
        budget_variance = abs(total_allocated - budget_data['total_budget']) / budget_data['total_budget']
        assert budget_variance < 0.01  # Within 1% variance
        
        # ROI projections should be consistent
        roi_variance = abs(budget_data['projected_roi'] - roi_data['roi_percentage']) / roi_data['roi_percentage']
        assert roi_variance < 0.15  # Within 15% variance (different calculation methods)
    
    def test_salary_intelligence_integration(self, base_url, sample_user_profile):
        """Test salary intelligence integration with other services."""
        
        # Step 1: Get salary intelligence for current role
        salary_request = {
            'role_id': 1,  # Security Analyst
            'location': sample_user_profile['location'],
            'experience_years': sample_user_profile['experience_years'],
            'certifications': [],
            'skills': ['network_security', 'incident_response']
        }
        
        salary_response = requests.post(
            f"{base_url}/api/v1/salary-intelligence/analysis",
            json=salary_request
        )
        
        assert salary_response.status_code == 200
        salary_data = salary_response.json()
        
        # Verify salary intelligence results
        assert 'salary_range' in salary_data
        assert 'market_percentile' in salary_data
        assert 'location_adjustment' in salary_data
        assert 'certification_impact' in salary_data
        
        # Step 2: Get salary projection with certification
        projection_request = {
            'current_role_id': 1,
            'target_role_id': 3,
            'certification_id': 1,  # CISSP
            'location': sample_user_profile['location'],
            'experience_years': sample_user_profile['experience_years']
        }
        
        projection_response = requests.post(
            f"{base_url}/api/v1/salary-intelligence/projection",
            json=projection_request
        )
        
        assert projection_response.status_code == 200
        projection_data = projection_response.json()
        
        # Verify salary projection results
        assert 'current_salary_estimate' in projection_data
        assert 'target_salary_estimate' in projection_data
        assert 'salary_increase' in projection_data
        assert 'certification_premium' in projection_data
        
        # Step 3: Verify integration with ROI analysis
        roi_request = {
            'certification_id': 1,
            'current_role_id': 1,
            'target_role_id': 3,
            'investment_cost': 3000,
            'location': sample_user_profile['location'],
            'experience_years': sample_user_profile['experience_years']
        }
        
        roi_response = requests.post(
            f"{base_url}/api/v1/salary-intelligence/roi-analysis",
            json=roi_request
        )
        
        assert roi_response.status_code == 200
        roi_data = roi_response.json()
        
        # Verify salary data consistency across services
        # Current salary estimates should be similar
        current_salary_variance = abs(
            salary_data['salary_range']['median'] - projection_data['current_salary_estimate']
        ) / projection_data['current_salary_estimate']
        assert current_salary_variance < 0.2  # Within 20% variance
        
        # ROI salary increase should match projection
        roi_salary_variance = abs(
            roi_data['expected_salary_increase'] - projection_data['salary_increase']
        ) / projection_data['salary_increase']
        assert roi_salary_variance < 0.15  # Within 15% variance
    
    def test_cross_service_data_flow(self, base_url, sample_user_profile, sample_enterprise_profile):
        """Test data flow and consistency across all Agent 4 services."""
        
        # Test individual user workflow feeding into enterprise analytics
        
        # Step 1: Individual career pathfinding
        individual_path_request = {
            'current_role_id': 1,
            'target_role_id': 3,
            'max_budget': 5000,
            'max_timeline_months': 18,
            'max_difficulty': 'Medium',
            'learning_style': 'mixed',
            'study_hours_per_week': 15,
            'currency': 'USD'
        }
        
        path_response = requests.post(
            f"{base_url}/api/v1/career-transition/pathfinding",
            json=individual_path_request
        )
        
        assert path_response.status_code == 200
        path_data = path_response.json()
        
        # Step 2: Scale individual results to enterprise level
        selected_path = path_data['path_options'][0]
        enterprise_budget_per_employee = selected_path['total_cost']
        total_enterprise_budget = enterprise_budget_per_employee * sample_enterprise_profile['security_team_size']
        
        enterprise_request = {
            'enterprise_id': sample_enterprise_profile['enterprise_id'],
            'total_budget': total_enterprise_budget,
            'strategic_priorities': ['cybersecurity', 'cloud_security'],
            'timeline_months': 18
        }
        
        enterprise_response = requests.post(
            f"{base_url}/api/v1/budget-optimization/optimize",
            json=enterprise_request
        )
        
        assert enterprise_response.status_code == 200
        enterprise_data = enterprise_response.json()
        
        # Step 3: Verify scaling consistency
        # Enterprise total should be reasonable multiple of individual cost
        scaling_factor = enterprise_data['total_budget'] / selected_path['total_cost']
        assert 20 <= scaling_factor <= 30  # Should be close to team size (25)
        
        # Enterprise ROI should be in reasonable range compared to individual
        individual_roi_request = {
            'certification_id': 1,
            'current_role_id': 1,
            'target_role_id': 3,
            'investment_cost': selected_path['total_cost'],
            'location': 'remote',
            'experience_years': 3
        }
        
        individual_roi_response = requests.post(
            f"{base_url}/api/v1/salary-intelligence/roi-analysis",
            json=individual_roi_request
        )
        
        assert individual_roi_response.status_code == 200
        individual_roi_data = individual_roi_response.json()
        
        # Enterprise and individual ROI should be in similar range
        roi_ratio = enterprise_data['projected_roi'] / individual_roi_data['five_year_roi']
        assert 0.5 <= roi_ratio <= 2.0  # Within reasonable variance
    
    def test_system_performance_under_load(self, base_url):
        """Test system performance with multiple concurrent requests."""
        import concurrent.futures
        import time
        
        def make_pathfinding_request():
            request_data = {
                'current_role_id': 1,
                'target_role_id': 3,
                'max_budget': 5000,
                'max_timeline_months': 18,
                'max_difficulty': 'Medium',
                'learning_style': 'mixed',
                'study_hours_per_week': 15,
                'currency': 'USD'
            }
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/v1/career-transition/pathfinding",
                json=request_data
            )
            end_time = time.time()
            
            return {
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code == 200
            }
        
        # Execute 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_pathfinding_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Verify all requests succeeded
        success_count = sum(1 for result in results if result['success'])
        assert success_count == 10
        
        # Verify reasonable response times
        avg_response_time = sum(result['response_time'] for result in results) / len(results)
        assert avg_response_time < 5.0  # Average response time under 5 seconds
        
        # Verify no request took too long
        max_response_time = max(result['response_time'] for result in results)
        assert max_response_time < 15.0  # No request over 15 seconds
