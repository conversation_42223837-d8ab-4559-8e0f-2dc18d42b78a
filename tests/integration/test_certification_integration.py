"""Integration tests for certification functionality"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.pool import StaticPool

from api.app import app
from database import get_db, Base
from models.certification import Certification, Organization
from models.user import User


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_integration.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)


@pytest.fixture(scope="function")
def db_session():
    """Create a test database session"""
    Base.metadata.create_all(bind=engine)
    session = Session(bind=engine)
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create test client with test database"""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear()


@pytest.fixture
def sample_organization(db_session):
    """Create a sample organization"""
    org = Organization(
        name="Test Security Institute",
        website="https://test-security.org",
        description="Test organization for security certifications"
    )
    db_session.add(org)
    db_session.commit()
    db_session.refresh(org)
    return org


@pytest.fixture
def sample_certifications(db_session, sample_organization):
    """Create sample certifications"""
    certifications = [
        Certification(
            name="Certified Ethical Hacker",
            description="Entry-level ethical hacking certification",
            level="Associate",
            difficulty="Intermediate",
            focus="Technical",
            domain="Penetration Testing",
            category="Ethical Hacking",
            cost=1199.0,
            currency="USD",
            organization_id=sample_organization.id,
            url="https://test-security.org/ceh",
            exam_code="312-50",
            validity_period=36,
            prerequisites=["Basic networking knowledge"],
            recommended_experience="1-2 years in IT security",
            is_active=True,
            is_deleted=False
        ),
        Certification(
            name="Certified Information Systems Security Professional",
            description="Advanced security management certification",
            level="Professional",
            difficulty="Advanced",
            focus="Management",
            domain="Information Security",
            category="Security Management",
            cost=749.0,
            currency="USD",
            organization_id=sample_organization.id,
            url="https://test-security.org/cissp",
            exam_code="CISSP",
            validity_period=36,
            prerequisites=["5 years experience"],
            recommended_experience="5+ years in information security",
            is_active=True,
            is_deleted=False
        ),
        Certification(
            name="CompTIA Security+",
            description="Foundation-level security certification",
            level="Entry Level",
            difficulty="Beginner",
            focus="General",
            domain="Information Security",
            category="Security Fundamentals",
            cost=349.0,
            currency="USD",
            organization_id=sample_organization.id,
            url="https://test-security.org/secplus",
            exam_code="SY0-601",
            validity_period=36,
            prerequisites=None,
            recommended_experience="2+ years in IT administration",
            is_active=True,
            is_deleted=False
        )
    ]
    
    for cert in certifications:
        db_session.add(cert)
    
    db_session.commit()
    
    for cert in certifications:
        db_session.refresh(cert)
    
    return certifications


class TestCertificationIntegration:
    """Integration tests for certification functionality"""

    @pytest.mark.integration
    @pytest.mark.certification
    def test_full_certification_workflow(self, client, sample_certifications):
        """Test complete certification workflow"""
        # 1. Get all certifications
        response = client.get("/api/v1/certifications/")
        assert response.status_code == 200
        data = response.json()
        assert data["total_count"] == 3
        assert len(data["data"]) == 3

        # 2. Filter by domain
        response = client.get("/api/v1/certifications/?domains=Information Security")
        assert response.status_code == 200
        data = response.json()
        assert data["total_count"] == 2  # CISSP and Security+

        # 3. Filter by level
        response = client.get("/api/v1/certifications/?levels=Professional")
        assert response.status_code == 200
        data = response.json()
        assert data["total_count"] == 1  # CISSP only

        # 4. Search certifications
        response = client.get("/api/v1/certifications/search?q=Security")
        assert response.status_code == 200
        data = response.json()
        assert data["total_count"] >= 2  # Should find Security+ and CISSP

        # 5. Get specific certification
        cert_id = sample_certifications[0].id
        response = client.get(f"/api/v1/certifications/{cert_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == cert_id
        assert data["name"] == "Certified Ethical Hacker"

        # 6. Get related certifications
        response = client.get(f"/api/v1/certifications/{cert_id}/related")
        assert response.status_code == 200
        related = response.json()
        assert len(related) >= 0  # May or may not have related certs

        # 7. Get certification statistics
        response = client.get("/api/v1/certifications/stats")
        assert response.status_code == 200
        stats = response.json()
        assert stats["total_certifications"] == 3
        assert stats["active_certifications"] == 3
        assert "domain_distribution" in stats

    @pytest.mark.integration
    @pytest.mark.certification
    def test_certification_pagination(self, client, sample_certifications):
        """Test certification pagination"""
        # Test first page
        response = client.get("/api/v1/certifications/?page=1&limit=2")
        assert response.status_code == 200
        data = response.json()
        assert len(data["data"]) == 2
        assert data["pagination"]["page"] == 1
        assert data["pagination"]["total_pages"] == 2
        assert data["pagination"]["has_next"] is True
        assert data["pagination"]["has_previous"] is False

        # Test second page
        response = client.get("/api/v1/certifications/?page=2&limit=2")
        assert response.status_code == 200
        data = response.json()
        assert len(data["data"]) == 1
        assert data["pagination"]["page"] == 2
        assert data["pagination"]["has_next"] is False
        assert data["pagination"]["has_previous"] is True

    @pytest.mark.integration
    @pytest.mark.certification
    def test_certification_sorting(self, client, sample_certifications):
        """Test certification sorting"""
        # Sort by name ascending
        response = client.get("/api/v1/certifications/?sort_by=name&sort_order=asc")
        assert response.status_code == 200
        data = response.json()
        names = [cert["name"] for cert in data["data"]]
        assert names == sorted(names)

        # Sort by cost descending
        response = client.get("/api/v1/certifications/?sort_by=cost&sort_order=desc")
        assert response.status_code == 200
        data = response.json()
        costs = [cert["cost"] for cert in data["data"]]
        assert costs == sorted(costs, reverse=True)

    @pytest.mark.integration
    @pytest.mark.certification
    def test_certification_complex_filtering(self, client, sample_certifications):
        """Test complex certification filtering"""
        # Multiple filters
        response = client.get(
            "/api/v1/certifications/"
            "?domains=Information Security"
            "&levels=Professional,Entry Level"
            "&cost_min=300"
            "&cost_max=800"
        )
        assert response.status_code == 200
        data = response.json()
        
        # Should return CISSP and Security+ (both in Information Security domain)
        assert data["total_count"] == 2
        
        for cert in data["data"]:
            assert cert["domain"] == "Information Security"
            assert cert["level"] in ["Professional", "Entry Level"]
            assert 300 <= cert["cost"] <= 800

    @pytest.mark.integration
    @pytest.mark.certification
    def test_certification_search_functionality(self, client, sample_certifications):
        """Test certification search functionality"""
        # Search by name
        response = client.get("/api/v1/certifications/search?q=Ethical")
        assert response.status_code == 200
        data = response.json()
        assert data["total_count"] == 1
        assert "Ethical" in data["data"][0]["name"]

        # Search by exam code
        response = client.get("/api/v1/certifications/search?q=CISSP")
        assert response.status_code == 200
        data = response.json()
        assert data["total_count"] == 1
        assert data["data"][0]["exam_code"] == "CISSP"

        # Search by domain
        response = client.get("/api/v1/certifications/search?q=Penetration")
        assert response.status_code == 200
        data = response.json()
        assert data["total_count"] == 1
        assert "Penetration" in data["data"][0]["domain"]

    @pytest.mark.integration
    @pytest.mark.certification
    def test_certification_error_handling(self, client):
        """Test certification error handling"""
        # Non-existent certification
        response = client.get("/api/v1/certifications/99999")
        assert response.status_code == 404

        # Invalid pagination parameters
        response = client.get("/api/v1/certifications/?page=0")
        assert response.status_code == 422

        response = client.get("/api/v1/certifications/?limit=-1")
        assert response.status_code == 422

        # Invalid sort parameters
        response = client.get("/api/v1/certifications/?sort_order=invalid")
        assert response.status_code == 422

    @pytest.mark.integration
    @pytest.mark.certification
    @pytest.mark.performance
    def test_certification_performance(self, client, db_session, sample_organization):
        """Test certification endpoint performance with larger dataset"""
        # Create more certifications for performance testing
        certifications = []
        for i in range(50):
            cert = Certification(
                name=f"Test Certification {i}",
                description=f"Test certification number {i}",
                level="Professional",
                difficulty="Intermediate",
                focus="Technical",
                domain="Test Domain",
                category="Test Category",
                cost=500.0 + i,
                currency="USD",
                organization_id=sample_organization.id,
                is_active=True,
                is_deleted=False
            )
            certifications.append(cert)
        
        db_session.add_all(certifications)
        db_session.commit()

        # Test performance
        import time
        start_time = time.time()
        response = client.get("/api/v1/certifications/?limit=50")
        end_time = time.time()

        assert response.status_code == 200
        assert (end_time - start_time) < 2.0  # Should complete within 2 seconds
        
        data = response.json()
        assert len(data["data"]) == 50

    @pytest.mark.integration
    @pytest.mark.certification
    def test_certification_data_consistency(self, client, sample_certifications):
        """Test data consistency across different endpoints"""
        # Get certification from list endpoint
        response = client.get("/api/v1/certifications/")
        list_data = response.json()
        first_cert_from_list = list_data["data"][0]

        # Get same certification from detail endpoint
        cert_id = first_cert_from_list["id"]
        response = client.get(f"/api/v1/certifications/{cert_id}")
        detail_data = response.json()

        # Verify data consistency
        assert first_cert_from_list["id"] == detail_data["id"]
        assert first_cert_from_list["name"] == detail_data["name"]
        assert first_cert_from_list["cost"] == detail_data["cost"]
        assert first_cert_from_list["domain"] == detail_data["domain"]
