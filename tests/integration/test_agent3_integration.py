"""Integration tests for Agent 3: Enterprise & Analytics Engine.

This module provides comprehensive integration tests for Agent 3 functionality,
testing the complete flow from API endpoints to database interactions.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json

from api.app import app
from database import get_db
from models.enterprise import EnterpriseOrganization, EnterpriseUser, Department
from models.progress_tracking import StudySession, PracticeTestResult
from models.certification import Certification


class TestAgent3Integration:
    """Integration tests for Agent 3 Enterprise Analytics."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def test_db(self):
        """Create test database session."""
        # This would use a test database in a real implementation
        return next(get_db())
    
    @pytest.fixture
    def test_organization(self, test_db):
        """Create test organization."""
        org = EnterpriseOrganization(
            name="Test Corporation",
            organization_type="corporate",
            subscription_tier="enterprise",
            employee_count=100,
            is_active=True
        )
        test_db.add(org)
        test_db.commit()
        test_db.refresh(org)
        return org
    
    @pytest.fixture
    def test_department(self, test_db, test_organization):
        """Create test department."""
        dept = Department(
            organization_id=test_organization.id,
            name="IT Security",
            description="Information Technology Security Department"
        )
        test_db.add(dept)
        test_db.commit()
        test_db.refresh(dept)
        return dept
    
    @pytest.fixture
    def test_users(self, test_db, test_organization, test_department):
        """Create test users."""
        users = []
        for i in range(3):
            user = EnterpriseUser(
                user_id=f"test_user_{i+1}",
                organization_id=test_organization.id,
                department_id=test_department.id,
                employee_id=f"EMP{i+1:03d}",
                role="security_analyst",
                is_active=True
            )
            test_db.add(user)
            users.append(user)
        
        test_db.commit()
        for user in users:
            test_db.refresh(user)
        return users
    
    @pytest.fixture
    def test_study_sessions(self, test_db, test_users):
        """Create test study sessions."""
        sessions = []
        for user in test_users:
            for i in range(5):
                session = StudySession(
                    user_id=user.user_id,
                    certification_id="security_plus",
                    session_type="reading",
                    duration_minutes=60 + i * 10,
                    topics_covered=["network_security", "cryptography"],
                    effectiveness_rating=4,
                    started_at=datetime.now() - timedelta(days=i),
                    completed_at=datetime.now() - timedelta(days=i) + timedelta(hours=1)
                )
                test_db.add(session)
                sessions.append(session)
        
        test_db.commit()
        return sessions
    
    def test_enterprise_study_insights_endpoint(self, client, test_organization, test_users, test_study_sessions):
        """Test enterprise study insights API endpoint."""
        response = client.post(f"/api/v1/agent3-enterprise-analytics/enterprise-study-insights/{test_organization.id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert "data" in data
        assert data["data"]["organization_id"] == test_organization.id
        assert "individual_insights" in data["data"]
        assert "aggregated_patterns" in data["data"]
        assert "recommendations" in data["data"]
    
    def test_skills_gap_analysis_endpoint(self, client, test_organization, test_department, test_users):
        """Test skills gap analysis API endpoint."""
        response = client.post(
            f"/api/v1/agent3-enterprise-analytics/skills-gap-analysis/{test_organization.id}",
            params={"department_id": test_department.id}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert "data" in data
        assert data["data"]["organization_id"] == test_organization.id
        assert data["data"]["department_id"] == test_department.id
        assert "skills_gaps" in data["data"]
        assert "training_priorities" in data["data"]
    
    def test_compliance_report_gdpr_endpoint(self, client, test_organization):
        """Test GDPR compliance report API endpoint."""
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        response = client.post(
            f"/api/v1/agent3-enterprise-analytics/compliance-report/{test_organization.id}",
            params={
                "compliance_type": "GDPR",
                "period_start": start_date,
                "period_end": end_date
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert "data" in data
        assert data["data"]["compliance_type"] == "GDPR"
        assert "compliance_score" in data["data"]
        assert "findings" in data["data"]
        assert "recommendations" in data["data"]
    
    def test_compliance_report_hipaa_endpoint(self, client, test_organization):
        """Test HIPAA compliance report API endpoint."""
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        response = client.post(
            f"/api/v1/agent3-enterprise-analytics/compliance-report/{test_organization.id}",
            params={
                "compliance_type": "HIPAA",
                "period_start": start_date,
                "period_end": end_date
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert data["data"]["compliance_type"] == "HIPAA"
        assert data["data"]["compliance_score"] >= 0.0
        assert data["data"]["compliance_score"] <= 1.0
    
    def test_compliance_report_sox_endpoint(self, client, test_organization):
        """Test SOX compliance report API endpoint."""
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        response = client.post(
            f"/api/v1/agent3-enterprise-analytics/compliance-report/{test_organization.id}",
            params={
                "compliance_type": "SOX",
                "period_start": start_date,
                "period_end": end_date
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert data["data"]["compliance_type"] == "SOX"
        assert len(data["data"]["findings"]) > 0
    
    def test_salary_intelligence_endpoint(self, client):
        """Test salary intelligence API endpoint."""
        response = client.get(
            "/api/v1/agent3-enterprise-analytics/data-intelligence/salary-benchmarks",
            params={
                "industry": "technology",
                "location": "US",
                "role": "security_analyst",
                "certification": "Security+"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert "data" in data
        assert "salary_ranges" in data["data"]
        assert "certification_premium" in data["data"]
        assert "market_trends" in data["data"]
    
    def test_market_trends_endpoint(self, client):
        """Test market trends analysis API endpoint."""
        response = client.get(
            "/api/v1/agent3-enterprise-analytics/data-intelligence/market-trends",
            params={
                "vertical": "healthcare",
                "time_period": 12
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert "data" in data
        assert "certification_trends" in data["data"]
        assert "skills_evolution" in data["data"]
        assert "job_market_analysis" in data["data"]
        assert "future_predictions" in data["data"]
    
    def test_health_check_endpoint(self, client):
        """Test Agent 3 health check endpoint."""
        response = client.get("/api/v1/agent3-enterprise-analytics/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "Agent 3: Enterprise & Analytics Engine"
        assert "features" in data
        assert "dependencies" in data
        assert "agent2_integration" in data["features"]
    
    def test_invalid_organization_id(self, client):
        """Test handling of invalid organization ID."""
        response = client.post("/api/v1/agent3-enterprise-analytics/enterprise-study-insights/99999")
        
        # Should handle gracefully - either 404 or empty results
        assert response.status_code in [200, 404, 500]
    
    def test_invalid_compliance_type(self, client, test_organization):
        """Test handling of invalid compliance type."""
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        response = client.post(
            f"/api/v1/agent3-enterprise-analytics/compliance-report/{test_organization.id}",
            params={
                "compliance_type": "INVALID_TYPE",
                "period_start": start_date,
                "period_end": end_date
            }
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "Invalid compliance type" in data["detail"]
    
    def test_invalid_date_format(self, client, test_organization):
        """Test handling of invalid date format."""
        response = client.post(
            f"/api/v1/agent3-enterprise-analytics/compliance-report/{test_organization.id}",
            params={
                "compliance_type": "GDPR",
                "period_start": "invalid-date",
                "period_end": "2024-03-31"
            }
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "Invalid date format" in data["detail"]
    
    def test_agent2_integration_flow(self, client, test_organization, test_users, test_study_sessions):
        """Test integration between Agent 2 and Agent 3."""
        # First, generate enterprise insights (which uses Agent 2)
        insights_response = client.post(
            f"/api/v1/agent3-enterprise-analytics/enterprise-study-insights/{test_organization.id}"
        )
        
        assert insights_response.status_code == 200
        insights_data = insights_response.json()
        
        # Then, generate skills gap analysis (which also uses Agent 2)
        skills_response = client.post(
            f"/api/v1/agent3-enterprise-analytics/skills-gap-analysis/{test_organization.id}"
        )
        
        assert skills_response.status_code == 200
        skills_data = skills_response.json()
        
        # Verify that both endpoints return consistent organization data
        assert insights_data["data"]["organization_id"] == skills_data["data"]["organization_id"]
        
        # Verify that Agent 2 integration is working (should have individual insights)
        assert len(insights_data["data"]["individual_insights"]) > 0
        
        # Verify that skills gap analysis includes training priorities
        assert len(skills_data["data"]["training_priorities"]) >= 0
