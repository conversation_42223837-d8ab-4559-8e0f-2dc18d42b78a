#!/usr/bin/env python3
"""Test validation script for CertRatsAgent4.

This script validates that all test files are properly structured
and can be imported without errors.
"""

import sys
import os
from pathlib import Path
import importlib.util
import ast


def validate_test_file(file_path):
    """Validate a single test file."""
    print(f"Validating {file_path}...")
    
    try:
        # Check if file can be parsed as valid Python
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Parse the AST to check for syntax errors
        ast.parse(content)
        
        # Check for required test patterns
        has_test_classes = 'class Test' in content
        has_test_functions = 'def test_' in content
        has_fixtures = '@pytest.fixture' in content or 'def ' in content
        
        print(f"  ✅ Syntax: Valid")
        print(f"  ✅ Test Classes: {'Found' if has_test_classes else 'None'}")
        print(f"  ✅ Test Functions: {'Found' if has_test_functions else 'None'}")
        print(f"  ✅ Fixtures: {'Found' if has_fixtures else 'None'}")
        
        return True
        
    except SyntaxError as e:
        print(f"  ❌ Syntax Error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False


def validate_test_structure():
    """Validate overall test structure."""
    print("Validating test structure...")
    
    test_dir = Path(__file__).parent
    
    # Check for required files
    required_files = [
        'conftest.py',
        'test_certratsagent4.py',
        'test_salary_intelligence_api.py',
        'test_enterprise_budget_api.py',
        'run_comprehensive_tests.py',
        'requirements-test.txt'
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = test_dir / file_name
        if not file_path.exists():
            missing_files.append(file_name)
        else:
            print(f"  ✅ {file_name}: Found")
    
    if missing_files:
        print(f"  ❌ Missing files: {missing_files}")
        return False
    
    return True


def count_test_coverage():
    """Count test functions and classes."""
    print("\nCounting test coverage...")
    
    test_dir = Path(__file__).parent
    test_files = list(test_dir.glob('test_*.py'))
    
    total_test_functions = 0
    total_test_classes = 0
    total_lines = 0
    
    for test_file in test_files:
        with open(test_file, 'r') as f:
            content = f.read()
            lines = len(content.splitlines())
            
        # Count test functions and classes
        test_functions = content.count('def test_')
        test_classes = content.count('class Test')
        
        print(f"  📄 {test_file.name}:")
        print(f"    - Lines: {lines}")
        print(f"    - Test Functions: {test_functions}")
        print(f"    - Test Classes: {test_classes}")
        
        total_lines += lines
        total_test_functions += test_functions
        total_test_classes += test_classes
    
    print(f"\n📊 Total Test Coverage:")
    print(f"  - Total Lines: {total_lines}")
    print(f"  - Total Test Functions: {total_test_functions}")
    print(f"  - Total Test Classes: {total_test_classes}")
    print(f"  - Test Files: {len(test_files)}")
    
    return total_test_functions, total_test_classes, total_lines


def main():
    """Main validation function."""
    print("🧪 CertRatsAgent4 Test Validation")
    print("=" * 50)
    
    # Validate test structure
    structure_valid = validate_test_structure()
    
    # Validate individual test files
    test_dir = Path(__file__).parent
    test_files = list(test_dir.glob('test_*.py'))
    
    valid_files = 0
    for test_file in test_files:
        if validate_test_file(test_file):
            valid_files += 1
        print()
    
    # Count test coverage
    test_functions, test_classes, total_lines = count_test_coverage()
    
    # Summary
    print("\n" + "=" * 50)
    print("VALIDATION SUMMARY")
    print("=" * 50)
    
    if structure_valid:
        print("✅ Test Structure: Valid")
    else:
        print("❌ Test Structure: Invalid")
    
    print(f"✅ Valid Test Files: {valid_files}/{len(test_files)}")
    print(f"📊 Test Coverage: {test_functions} functions, {test_classes} classes")
    print(f"📝 Total Test Code: {total_lines} lines")
    
    # Quality assessment
    if test_functions >= 50:
        print("🎉 Excellent test coverage!")
    elif test_functions >= 30:
        print("✅ Good test coverage!")
    elif test_functions >= 15:
        print("⚠️  Moderate test coverage")
    else:
        print("❌ Insufficient test coverage")
    
    # Return success if all validations pass
    success = (
        structure_valid and 
        valid_files == len(test_files) and 
        test_functions >= 30
    )
    
    if success:
        print("\n🎉 All validations passed! Tests are ready to run.")
        return 0
    else:
        print("\n❌ Some validations failed. Please review and fix issues.")
        return 1


if __name__ == "__main__":
    exit(main())
