"""Unit tests for Agent 3: Enterprise & Analytics Engine.

This module provides comprehensive unit tests for Agent 3 functionality,
including enterprise analytics, compliance automation, and data intelligence.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from services.enterprise_ai_service import EnterpriseAIService
from api.v1.agent3_enterprise_analytics import router
from schemas.agent3_enterprise_analytics import (
    EnterpriseInsightRequest, SkillsGapAnalysisRequest, ComplianceReportRequest
)


class TestEnterpriseAIServiceAgent3:
    """Test Agent 3 specific functionality in EnterpriseAIService."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def mock_ai_assistant(self):
        """Mock AI Study Assistant."""
        mock_assistant = Mock()
        mock_assistant.analyze_learning_patterns.return_value = [
            Mock(dict=lambda: {'type': 'learning_style', 'value': 'visual'}),
            <PERSON><PERSON>(dict=lambda: {'type': 'study_duration', 'value': 60}),
            <PERSON><PERSON>(dict=lambda: {'type': 'weak_area', 'value': 'network_security'})
        ]
        mock_assistant.generate_personalized_recommendations.return_value = [
            Mock(dict=lambda: {'type': 'topic', 'priority': 4, 'title': 'Focus on Network Security'})
        ]
        mock_assistant._gather_user_data.return_value = {
            'sessions': [],
            'test_results': [],
            'certifications': []
        }
        mock_assistant.generate_adaptive_learning_path.return_value = {
            'topics': [
                {'name': 'Network Security', 'priority': 4, 'difficulty': 'intermediate', 'estimated_hours': 12}
            ]
        }
        return mock_assistant
    
    @pytest.fixture
    def enterprise_ai_service(self, mock_db, mock_ai_assistant):
        """Create EnterpriseAIService instance with mocked dependencies."""
        with patch('services.enterprise_ai_service.OnDeviceAIStudyAssistant', return_value=mock_ai_assistant):
            service = EnterpriseAIService(mock_db)
            return service
    
    def test_generate_enterprise_study_insights_success(self, enterprise_ai_service, mock_db):
        """Test successful enterprise study insights generation."""
        # Mock organization users
        mock_users = [
            Mock(user_id='user1', department_id=1, role=Mock(value='analyst'), is_active=True),
            Mock(user_id='user2', department_id=2, role=Mock(value='engineer'), is_active=True)
        ]
        mock_db.query.return_value.filter.return_value.all.return_value = mock_users
        
        # Test insights generation
        result = enterprise_ai_service.generate_enterprise_study_insights(org_id=1)
        
        # Assertions
        assert 'error' not in result
        assert result['organization_id'] == 1
        assert result['total_users'] == 2
        assert 'individual_insights' in result
        assert 'aggregated_patterns' in result
        assert 'recommendations' in result
        assert 'generated_at' in result
    
    def test_generate_skills_gap_analysis_success(self, enterprise_ai_service, mock_db):
        """Test successful skills gap analysis."""
        # Mock organization users
        mock_users = [
            Mock(user_id='user1', department_id=1, role=Mock(value='analyst'), is_active=True)
        ]
        mock_db.query.return_value.filter.return_value.all.return_value = mock_users
        
        # Test skills gap analysis
        result = enterprise_ai_service.generate_skills_gap_analysis(org_id=1)
        
        # Assertions
        assert 'error' not in result
        assert result['organization_id'] == 1
        assert 'skills_gaps' in result
        assert 'certification_gaps' in result
        assert 'training_priorities' in result
        assert 'budget_recommendations' in result
    
    def test_generate_compliance_automation_report_gdpr(self, enterprise_ai_service, mock_db):
        """Test GDPR compliance report generation."""
        # Mock organization
        mock_org = Mock(id=1, name='Test Corp')
        mock_db.query.return_value.filter.return_value.first.return_value = mock_org
        
        # Test GDPR compliance report
        start_date = datetime.now() - timedelta(days=90)
        end_date = datetime.now()
        result = enterprise_ai_service.generate_compliance_automation_report(
            org_id=1, compliance_type='GDPR', period_start=start_date, period_end=end_date
        )
        
        # Assertions
        assert 'error' not in result
        assert result['organization_id'] == 1
        assert result['compliance_type'] == 'GDPR'
        assert 'compliance_score' in result
        assert 'findings' in result
        assert 'recommendations' in result
        assert 'audit_trail' in result
    
    def test_generate_compliance_automation_report_hipaa(self, enterprise_ai_service, mock_db):
        """Test HIPAA compliance report generation."""
        # Mock organization
        mock_org = Mock(id=1, name='Healthcare Corp')
        mock_db.query.return_value.filter.return_value.first.return_value = mock_org
        
        # Test HIPAA compliance report
        start_date = datetime.now() - timedelta(days=90)
        end_date = datetime.now()
        result = enterprise_ai_service.generate_compliance_automation_report(
            org_id=1, compliance_type='HIPAA', period_start=start_date, period_end=end_date
        )
        
        # Assertions
        assert 'error' not in result
        assert result['compliance_type'] == 'HIPAA'
        assert result['compliance_score'] >= 0.0
        assert result['compliance_score'] <= 1.0
    
    def test_generate_compliance_automation_report_sox(self, enterprise_ai_service, mock_db):
        """Test SOX compliance report generation."""
        # Mock organization
        mock_org = Mock(id=1, name='Public Corp')
        mock_db.query.return_value.filter.return_value.first.return_value = mock_org
        
        # Test SOX compliance report
        start_date = datetime.now() - timedelta(days=90)
        end_date = datetime.now()
        result = enterprise_ai_service.generate_compliance_automation_report(
            org_id=1, compliance_type='SOX', period_start=start_date, period_end=end_date
        )
        
        # Assertions
        assert 'error' not in result
        assert result['compliance_type'] == 'SOX'
        assert len(result['findings']) > 0
        assert len(result['recommendations']) > 0
    
    def test_aggregate_learning_patterns(self, enterprise_ai_service):
        """Test learning pattern aggregation."""
        # Mock individual insights
        individual_insights = [
            {
                'department_id': 1,
                'insights': [
                    {'type': 'learning_style', 'value': 'visual'},
                    {'type': 'study_duration', 'value': 60},
                    {'type': 'weak_area', 'value': 'network_security'}
                ]
            },
            {
                'department_id': 2,
                'insights': [
                    {'type': 'learning_style', 'value': 'auditory'},
                    {'type': 'study_duration', 'value': 45},
                    {'type': 'weak_area', 'value': 'cryptography'}
                ]
            }
        ]
        
        # Test aggregation
        patterns = enterprise_ai_service._aggregate_learning_patterns(individual_insights)
        
        # Assertions
        assert 'common_learning_styles' in patterns
        assert 'average_study_duration' in patterns
        assert 'common_weak_areas' in patterns
        assert 'department_patterns' in patterns
        assert patterns['common_learning_styles']['visual'] == 1
        assert patterns['common_learning_styles']['auditory'] == 1
        assert patterns['average_study_duration'] == 52.5  # (60 + 45) / 2
    
    def test_generate_enterprise_recommendations(self, enterprise_ai_service):
        """Test enterprise recommendation generation."""
        # Mock aggregated patterns
        patterns = {
            'common_learning_styles': {'visual': 15, 'auditory': 5},
            'average_study_duration': 25,  # Low duration
            'common_weak_areas': {'network_security': 10, 'cryptography': 3}
        }
        
        # Test recommendation generation
        recommendations = enterprise_ai_service._generate_enterprise_recommendations(patterns)
        
        # Assertions
        assert len(recommendations) > 0
        assert any('visual learning style' in rec for rec in recommendations)
        assert any('longer study sessions' in rec for rec in recommendations)
        assert any('network_security' in rec for rec in recommendations)
    
    def test_analyze_organizational_skills_gaps(self, enterprise_ai_service):
        """Test organizational skills gap analysis."""
        # Mock user data
        user_data = [
            {
                'department_id': 1,
                'adaptive_path': {
                    'topics': [
                        {'name': 'Network Security', 'priority': 5, 'difficulty': 'high', 'estimated_hours': 20},
                        {'name': 'Cryptography', 'priority': 4, 'difficulty': 'medium', 'estimated_hours': 15}
                    ]
                }
            },
            {
                'department_id': 2,
                'adaptive_path': {
                    'topics': [
                        {'name': 'Network Security', 'priority': 4, 'difficulty': 'medium', 'estimated_hours': 12}
                    ]
                }
            }
        ]
        
        # Test skills gap analysis
        gaps = enterprise_ai_service._analyze_organizational_skills_gaps(user_data)
        
        # Assertions
        assert 'critical_gaps' in gaps
        assert 'moderate_gaps' in gaps
        assert 'minor_gaps' in gaps
        assert 'department_specific_gaps' in gaps
    
    def test_calculate_gap_severity(self, enterprise_ai_service):
        """Test gap severity calculation."""
        # Test critical gap
        assert enterprise_ai_service._calculate_gap_severity(15, 150) == 'critical'
        
        # Test moderate gap
        assert enterprise_ai_service._calculate_gap_severity(7, 70) == 'moderate'
        
        # Test minor gap
        assert enterprise_ai_service._calculate_gap_severity(3, 30) == 'minor'
    
    def test_compliance_report_error_handling(self, enterprise_ai_service, mock_db):
        """Test error handling in compliance report generation."""
        # Mock organization not found
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Test error handling
        start_date = datetime.now() - timedelta(days=90)
        end_date = datetime.now()
        result = enterprise_ai_service.generate_compliance_automation_report(
            org_id=999, compliance_type='GDPR', period_start=start_date, period_end=end_date
        )
        
        # Assertions
        assert 'error' in result
        assert result['error'] == 'Organization not found'
    
    def test_unsupported_compliance_type(self, enterprise_ai_service, mock_db):
        """Test unsupported compliance type handling."""
        # Mock organization
        mock_org = Mock(id=1, name='Test Corp')
        mock_db.query.return_value.filter.return_value.first.return_value = mock_org
        
        # Test unsupported compliance type
        start_date = datetime.now() - timedelta(days=90)
        end_date = datetime.now()
        result = enterprise_ai_service.generate_compliance_automation_report(
            org_id=1, compliance_type='INVALID', period_start=start_date, period_end=end_date
        )
        
        # Assertions
        assert 'error' in result
        assert 'Unsupported compliance type' in result['error']


class TestAgent3Schemas:
    """Test Agent 3 specific schemas."""
    
    def test_enterprise_insight_request_validation(self):
        """Test EnterpriseInsightRequest validation."""
        # Valid request
        request = EnterpriseInsightRequest(
            organization_id=1,
            include_individual_insights=True,
            max_users=50
        )
        assert request.organization_id == 1
        assert request.include_individual_insights is True
        assert request.max_users == 50
    
    def test_skills_gap_analysis_request_validation(self):
        """Test SkillsGapAnalysisRequest validation."""
        # Valid request with department filter
        request = SkillsGapAnalysisRequest(
            organization_id=1,
            department_id=5,
            certification_focus="Security+"
        )
        assert request.organization_id == 1
        assert request.department_id == 5
        assert request.certification_focus == "Security+"
    
    def test_compliance_report_request_validation(self):
        """Test ComplianceReportRequest validation."""
        # Valid request
        request = ComplianceReportRequest(
            organization_id=1,
            compliance_type="GDPR",
            period_start="2024-01-01",
            period_end="2024-03-31",
            include_recommendations=True
        )
        assert request.organization_id == 1
        assert request.compliance_type == "GDPR"
        assert request.period_start == "2024-01-01"
        assert request.period_end == "2024-03-31"
        assert request.include_recommendations is True
