"""Comprehensive tests for Salary Intelligence API endpoints.

This module provides extensive test coverage for all salary intelligence
API endpoints, including edge cases and error handling.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
import json

from api.app import app
from database import get_db
from models.certification import Certification
from models.career_transition import CareerRole


class TestSalaryIntelligenceAPI:
    """Test suite for Salary Intelligence API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def sample_role_data(self):
        """Sample role data for testing."""
        return {
            "role_id": 1,
            "location": "remote",
            "experience_years": 5
        }
    
    def test_analyze_role_salary_success(self, client, sample_role_data):
        """Test successful role salary analysis."""
        response = client.get(
            f"/api/v1/salary-intelligence/roles/{sample_role_data['role_id']}/analysis",
            params={
                "location": sample_role_data["location"],
                "experience_years": sample_role_data["experience_years"]
            }
        )
        
        # Should succeed or fail gracefully
        assert response.status_code in [200, 404, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "role_id" in data
            assert "role_name" in data
            assert "current_salary_range" in data
            assert "market_salary_range" in data
            assert "certification_impact" in data
            assert "analysis_date" in data
            
            # Verify salary range structure
            salary_range = data["current_salary_range"]
            assert "min" in salary_range
            assert "max" in salary_range
            assert "median" in salary_range
            assert salary_range["min"] <= salary_range["median"] <= salary_range["max"]
    
    def test_analyze_role_salary_invalid_role(self, client):
        """Test role salary analysis with invalid role ID."""
        response = client.get(
            "/api/v1/salary-intelligence/roles/99999/analysis",
            params={"location": "remote", "experience_years": 5}
        )
        
        assert response.status_code == 404
        error_data = response.json()
        assert "detail" in error_data
    
    def test_analyze_role_salary_parameter_validation(self, client):
        """Test parameter validation for role salary analysis."""
        # Test with invalid experience years
        response = client.get(
            "/api/v1/salary-intelligence/roles/1/analysis",
            params={"location": "remote", "experience_years": -5}
        )
        
        assert response.status_code == 422  # Validation error
        
        # Test with experience years too high
        response = client.get(
            "/api/v1/salary-intelligence/roles/1/analysis",
            params={"location": "remote", "experience_years": 100}
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_calculate_roi_success(self, client):
        """Test successful ROI calculation."""
        roi_request = {
            "certification_id": 1,
            "current_role_id": 1,
            "target_role_id": 2,
            "investment_cost": 3000.0,
            "location": "remote",
            "experience_years": 5
        }
        
        response = client.post("/api/v1/salary-intelligence/roi-analysis", json=roi_request)
        
        # Should succeed or fail gracefully
        assert response.status_code in [200, 404, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "certification_id" in data
            assert "investment_cost" in data
            assert "expected_salary_increase" in data
            assert "payback_period_months" in data
            assert "five_year_roi" in data
            assert "ten_year_roi" in data
            assert "confidence_score" in data
            assert "risk_factors" in data
            
            # Verify data types and ranges
            assert isinstance(data["investment_cost"], (int, float))
            assert isinstance(data["expected_salary_increase"], (int, float))
            assert isinstance(data["payback_period_months"], int)
            assert 0.0 <= data["confidence_score"] <= 1.0
            assert isinstance(data["risk_factors"], list)
    
    def test_calculate_roi_validation(self, client):
        """Test ROI calculation request validation."""
        # Test with negative investment cost
        invalid_request = {
            "certification_id": 1,
            "current_role_id": 1,
            "investment_cost": -1000.0,
            "experience_years": 5
        }
        
        response = client.post("/api/v1/salary-intelligence/roi-analysis", json=invalid_request)
        assert response.status_code == 422  # Validation error
        
        # Test with invalid experience years
        invalid_request = {
            "certification_id": 1,
            "current_role_id": 1,
            "investment_cost": 3000.0,
            "experience_years": -1
        }
        
        response = client.post("/api/v1/salary-intelligence/roi-analysis", json=invalid_request)
        assert response.status_code == 422  # Validation error
    
    def test_get_salary_benchmarks_success(self, client):
        """Test successful salary benchmarks retrieval."""
        response = client.get(
            "/api/v1/salary-intelligence/benchmarks/cybersecurity",
            params={"location": "remote"}
        )
        
        # Should succeed or fail gracefully
        assert response.status_code in [200, 404, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "domain" in data
            assert "location" in data
            assert "location_multiplier" in data
            assert "benchmarks" in data
            assert "market_summary" in data
            assert "generated_at" in data
            
            # Verify benchmarks structure
            if data["benchmarks"]:
                benchmark_key = list(data["benchmarks"].keys())[0]
                benchmark = data["benchmarks"][benchmark_key]
                assert "level" in benchmark
                assert "salary_range" in benchmark
                assert "market_position" in benchmark
                assert "growth_potential" in benchmark
    
    def test_get_certification_impact_success(self, client):
        """Test successful certification impact analysis."""
        response = client.get("/api/v1/salary-intelligence/certifications/1/impact")
        
        # Should succeed or fail gracefully
        assert response.status_code in [200, 404, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "certification_id" in data
            assert "certification_name" in data
            assert "average_salary_premium" in data
            assert "role_impacts" in data
            assert "market_demand" in data
            assert "investment_recommendation" in data
            assert "analysis_date" in data
            
            # Verify role impacts structure
            if data["role_impacts"]:
                role_key = list(data["role_impacts"].keys())[0]
                role_impact = data["role_impacts"][role_key]
                assert "base_salary" in role_impact
                assert "with_certification" in role_impact
                assert "increase_amount" in role_impact
                assert "increase_percentage" in role_impact
                assert "role_level" in role_impact
    
    def test_get_certification_impact_invalid_id(self, client):
        """Test certification impact with invalid certification ID."""
        response = client.get("/api/v1/salary-intelligence/certifications/99999/impact")
        
        assert response.status_code == 404
        error_data = response.json()
        assert "detail" in error_data
    
    def test_get_market_trends_success(self, client):
        """Test successful market trends retrieval."""
        response = client.get("/api/v1/salary-intelligence/market-trends/cybersecurity")
        
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "domain" in data
            assert "trends" in data
            assert "salary_growth_forecast" in data
            assert "skill_demand" in data
            assert "market_drivers" in data
            assert "risk_factors" in data
            assert "generated_at" in data
            
            # Verify trends structure
            trends = data["trends"]
            assert "growth_rate" in trends
            assert "market_outlook" in trends
            assert "demand_level" in trends
            assert "key_drivers" in trends
            assert "emerging_skills" in trends
            assert "risk_factors" in trends
    
    def test_salary_calculator_success(self, client):
        """Test successful salary calculator."""
        response = client.get(
            "/api/v1/salary-intelligence/salary-calculator",
            params={
                "base_salary": 100000,
                "location": "san_francisco",
                "domain": "cybersecurity",
                "experience_years": 7,
                "certifications": ["CISSP", "Security+"]
            }
        )
        
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "base_salary" in data
            assert "adjusted_salary" in data
            assert "total_increase" in data
            assert "adjustments" in data
            assert "calculation_date" in data
            
            # Verify adjustments structure
            adjustments = data["adjustments"]
            assert "location" in adjustments
            assert "experience" in adjustments
            assert "certifications" in adjustments
            
            # Verify salary calculations
            assert data["adjusted_salary"] >= data["base_salary"]
            assert data["total_increase"] == data["adjusted_salary"] - data["base_salary"]
    
    def test_salary_calculator_validation(self, client):
        """Test salary calculator parameter validation."""
        # Test with salary too low
        response = client.get(
            "/api/v1/salary-intelligence/salary-calculator",
            params={"base_salary": 10000}  # Below minimum
        )
        
        assert response.status_code == 422  # Validation error
        
        # Test with salary too high
        response = client.get(
            "/api/v1/salary-intelligence/salary-calculator",
            params={"base_salary": 1000000}  # Above maximum
        )
        
        assert response.status_code == 422  # Validation error
        
        # Test with invalid experience years
        response = client.get(
            "/api/v1/salary-intelligence/salary-calculator",
            params={
                "base_salary": 100000,
                "experience_years": -1
            }
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_health_check(self, client):
        """Test salary intelligence health check."""
        response = client.get("/api/v1/salary-intelligence/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "Salary Intelligence"
        assert "version" in data
        assert "features" in data
        assert "timestamp" in data
        
        # Verify expected features
        expected_features = [
            "salary_analysis",
            "roi_calculation", 
            "market_benchmarks",
            "certification_impact",
            "market_trends",
            "salary_calculator"
        ]
        
        for feature in expected_features:
            assert feature in data["features"]
    
    def test_error_handling_consistency(self, client):
        """Test consistent error handling across endpoints."""
        # Test various error scenarios and ensure consistent response format
        
        # 404 errors should have consistent format
        response_404_role = client.get("/api/v1/salary-intelligence/roles/99999/analysis")
        response_404_cert = client.get("/api/v1/salary-intelligence/certifications/99999/impact")
        
        if response_404_role.status_code == 404 and response_404_cert.status_code == 404:
            error_1 = response_404_role.json()
            error_2 = response_404_cert.json()
            
            # Both should have detail field
            assert "detail" in error_1
            assert "detail" in error_2
    
    def test_response_time_performance(self, client):
        """Test response time performance for key endpoints."""
        import time
        
        # Test salary analysis response time
        start_time = time.time()
        response = client.get("/api/v1/salary-intelligence/roles/1/analysis")
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Should respond within reasonable time (5 seconds for test environment)
        assert response_time < 5.0
        
        # Test ROI calculation response time
        roi_request = {
            "certification_id": 1,
            "current_role_id": 1,
            "investment_cost": 3000.0
        }
        
        start_time = time.time()
        response = client.post("/api/v1/salary-intelligence/roi-analysis", json=roi_request)
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response_time < 5.0
    
    def test_data_consistency_across_endpoints(self, client):
        """Test data consistency across related endpoints."""
        # Get role analysis
        role_response = client.get("/api/v1/salary-intelligence/roles/1/analysis")
        
        # Get benchmarks for same domain
        benchmark_response = client.get("/api/v1/salary-intelligence/benchmarks/cybersecurity")
        
        # If both succeed, they should have consistent data
        if role_response.status_code == 200 and benchmark_response.status_code == 200:
            role_data = role_response.json()
            benchmark_data = benchmark_response.json()
            
            # Both should reference the same domain concepts
            assert isinstance(role_data["current_salary_range"], dict)
            assert isinstance(benchmark_data["benchmarks"], dict)
    
    def test_concurrent_requests_handling(self, client):
        """Test handling of concurrent requests to salary intelligence endpoints."""
        import threading
        import time
        
        results = []
        
        def make_salary_request():
            response = client.get("/api/v1/salary-intelligence/roles/1/analysis")
            results.append(response.status_code)
        
        def make_roi_request():
            roi_request = {
                "certification_id": 1,
                "current_role_id": 1,
                "investment_cost": 3000.0
            }
            response = client.post("/api/v1/salary-intelligence/roi-analysis", json=roi_request)
            results.append(response.status_code)
        
        # Create multiple threads with different request types
        threads = []
        for _ in range(5):
            thread1 = threading.Thread(target=make_salary_request)
            thread2 = threading.Thread(target=make_roi_request)
            threads.extend([thread1, thread2])
            thread1.start()
            thread2.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should complete (success or expected failure)
        assert len(results) == 10
        for status in results:
            assert status in [200, 404, 422, 500]  # Expected status codes
