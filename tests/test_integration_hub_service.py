"""Tests for Integration Hub Service functionality.

This module provides comprehensive tests for the integration hub service,
including SSO, LDAP, LMS, HR system integrations, and synchronization
capabilities with enterprise systems.
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from services.integration_hub_service import IntegrationHubService, IntegrationType, SyncDirection, IntegrationStatus
from services.sso_authentication_service import SSOAuthenticationService
from services.ldap_sync_service import LDAPSyncService
from models.enterprise import EnterpriseOrganization, EnterpriseUser


class TestIntegrationHubService:
    """Test cases for the Integration Hub Service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def integration_service(self, mock_db):
        """Create an integration hub service instance for testing."""
        return IntegrationHubService(mock_db)
    
    @pytest.fixture
    def sample_sso_config(self):
        """Create sample SSO configuration data."""
        return {
            'type': IntegrationType.SSO_SAML.value,
            'provider_name': 'Azure AD',
            'entity_id': 'https://sts.windows.net/tenant-id/',
            'sso_url': 'https://login.microsoftonline.com/tenant-id/saml2',
            'x509_certificate': '-----BEGIN CERTIFICATE-----\nMIIC...\n-----END CERTIFICATE-----',
            'attribute_mapping': {
                'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
                'first_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
                'last_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname'
            },
            'auto_provision_users': True,
            'default_role': 'learner'
        }
    
    @pytest.fixture
    def sample_ldap_config(self):
        """Create sample LDAP configuration data."""
        return {
            'server_url': 'ldaps://dc.company.com:636',
            'bind_dn': 'CN=service-account,OU=Service Accounts,DC=company,DC=com',
            'bind_password': 'secure_password',
            'user_base_dn': 'OU=Users,DC=company,DC=com',
            'group_base_dn': 'OU=Groups,DC=company,DC=com',
            'attribute_mapping': {
                'email': 'mail',
                'first_name': 'givenName',
                'last_name': 'sn'
            },
            'auto_provision_users': True
        }
    
    @pytest.fixture
    def sample_lms_config(self):
        """Create sample LMS configuration data."""
        return {
            'type': IntegrationType.LMS_CANVAS.value,
            'lms_url': 'https://company.instructure.com',
            'api_key': 'canvas_api_key_here',
            'course_mapping': {
                'cert_001': 'canvas_course_123'
            },
            'grade_sync_enabled': True,
            'enrollment_sync_enabled': True
        }
    
    @pytest.fixture
    def sample_hr_config(self):
        """Create sample HR system configuration data."""
        return {
            'type': IntegrationType.HR_WORKDAY.value,
            'hr_system_url': 'https://company.workday.com',
            'username': 'integration_user',
            'password': 'secure_password',
            'tenant_id': 'company_tenant',
            'field_mapping': {
                'employee_id': 'Employee_ID',
                'email': 'Email_Address'
            },
            'employee_sync_enabled': True
        }
    
    def test_configure_sso_integration(self, integration_service, sample_sso_config):
        """Test SSO integration configuration."""
        # Mock organization lookup
        mock_org = Mock()
        mock_org.id = 1
        mock_org.name = 'Test Organization'
        integration_service.db.query.return_value.filter.return_value.first.return_value = mock_org
        
        # Mock validation and test methods
        integration_service._validate_sso_config = Mock(return_value={'valid': True, 'errors': []})
        integration_service._test_sso_connection = Mock(return_value={'success': True})
        integration_service._encrypt_sensitive_data = Mock(return_value='encrypted_config')
        
        result = integration_service.configure_sso_integration(1, sample_sso_config)
        
        assert 'integration_id' in result
        assert result['status'] == IntegrationStatus.ACTIVE.value
        assert result['provider_name'] == 'Azure AD'
        assert 'metadata_url' in result
        assert 'acs_url' in result
        assert result['auto_provision_enabled'] is True
    
    def test_configure_sso_integration_invalid_org(self, integration_service, sample_sso_config):
        """Test SSO configuration with invalid organization."""
        # Mock no organization found
        integration_service.db.query.return_value.filter.return_value.first.return_value = None
        
        result = integration_service.configure_sso_integration(999, sample_sso_config)
        
        assert 'error' in result
        assert result['error'] == 'Organization not found'
    
    def test_configure_sso_integration_invalid_config(self, integration_service, sample_sso_config):
        """Test SSO configuration with invalid config."""
        # Mock organization lookup
        mock_org = Mock()
        integration_service.db.query.return_value.filter.return_value.first.return_value = mock_org
        
        # Mock validation failure
        integration_service._validate_sso_config = Mock(return_value={
            'valid': False, 
            'errors': ['Missing required field: entity_id']
        })
        
        result = integration_service.configure_sso_integration(1, sample_sso_config)
        
        assert 'error' in result
        assert 'Invalid SSO configuration' in result['error']
    
    def test_authenticate_sso_user(self, integration_service):
        """Test SSO user authentication."""
        # Mock integration lookup
        integration_service._get_integration = Mock(return_value={
            'integration_id': 'sso_001',
            'organization_id': 1,
            'provider_name': 'Azure AD',
            'configuration': 'encrypted_config',
            'attribute_mapping': {
                'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'
            },
            'auto_provision_users': True
        })
        
        # Mock SAML response parsing
        integration_service._parse_saml_response = Mock(return_value={
            'valid': True,
            'attributes': {
                'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': '<EMAIL>'
            }
        })
        
        # Mock user creation/lookup
        integration_service._find_or_create_sso_user = Mock(return_value={
            'user_id': 'user_123',
            'email': '<EMAIL>',
            'display_name': 'Test User'
        })
        
        # Mock token generation
        integration_service._generate_session_token = Mock(return_value='session_token_123')
        integration_service._log_sso_authentication = Mock()
        
        result = integration_service.authenticate_sso_user('sso_001', 'saml_response_data')
        
        assert result['authentication_status'] == 'success'
        assert 'user_profile' in result
        assert 'session_token' in result
        assert result['sso_provider'] == 'Azure AD'
    
    def test_configure_ldap_integration(self, integration_service, sample_ldap_config):
        """Test LDAP integration configuration."""
        # Mock validation and test methods
        integration_service._validate_ldap_config = Mock(return_value={'valid': True, 'errors': []})
        integration_service._test_ldap_connection = Mock(return_value={'success': True})
        integration_service._encrypt_sensitive_data = Mock(return_value='encrypted_config')
        
        result = integration_service.configure_ldap_integration(1, sample_ldap_config)
        
        assert 'integration_id' in result
        assert result['status'] == IntegrationStatus.ACTIVE.value
        assert result['server_url'] == 'ldaps://dc.company.com:636'
        assert result['sync_direction'] == SyncDirection.INBOUND.value
        assert result['auto_provision_enabled'] is True
    
    def test_sync_ldap_users(self, integration_service):
        """Test LDAP user synchronization."""
        # Mock integration lookup
        integration_service._get_integration = Mock(return_value={
            'integration_id': 'ldap_001',
            'organization_id': 1,
            'configuration': 'encrypted_config'
        })
        
        # Mock LDAP connection and search
        integration_service._decrypt_sensitive_data = Mock(return_value={
            'server_url': 'ldaps://dc.company.com:636',
            'bind_dn': 'CN=service',
            'bind_password': 'password'
        })
        
        mock_connection = Mock()
        integration_service._connect_to_ldap = Mock(return_value=mock_connection)
        integration_service._search_ldap_users = Mock(return_value=[
            {'cn': 'user1', 'mail': '<EMAIL>'},
            {'cn': 'user2', 'mail': '<EMAIL>'}
        ])
        
        # Mock sync processing
        integration_service._sync_ldap_user = Mock(return_value={'action': 'created'})
        
        result = integration_service.sync_ldap_users('ldap_001')
        
        assert 'sync_id' in result
        assert result['users_found'] == 2
        assert result['users_created'] == 2
    
    def test_configure_lms_integration(self, integration_service, sample_lms_config):
        """Test LMS integration configuration."""
        # Mock validation and test methods
        integration_service._validate_lms_config = Mock(return_value={'valid': True, 'errors': []})
        integration_service._test_lms_connection = Mock(return_value={'success': True})
        integration_service._encrypt_sensitive_data = Mock(return_value='encrypted_config')
        
        result = integration_service.configure_lms_integration(1, sample_lms_config)
        
        assert 'integration_id' in result
        assert result['status'] == IntegrationStatus.ACTIVE.value
        assert result['lms_url'] == 'https://company.instructure.com'
        assert result['lms_type'] == IntegrationType.LMS_CANVAS.value
        assert 'sync_capabilities' in result
        assert result['sync_capabilities']['grades'] is True
    
    def test_sync_lms_data(self, integration_service):
        """Test LMS data synchronization."""
        # Mock integration lookup
        integration_service._get_integration = Mock(return_value={
            'integration_id': 'lms_001',
            'organization_id': 1,
            'integration_type': IntegrationType.LMS_CANVAS.value,
            'configuration': 'encrypted_config'
        })
        
        # Mock LMS client and sync methods
        integration_service._decrypt_sensitive_data = Mock(return_value={
            'lms_url': 'https://company.instructure.com',
            'api_key': 'api_key'
        })
        
        mock_lms_client = Mock()
        integration_service._get_lms_client = Mock(return_value=mock_lms_client)
        integration_service._sync_lms_courses = Mock(return_value={'courses_synced': 5})
        integration_service._sync_lms_enrollments = Mock(return_value={'enrollments_synced': 25})
        integration_service._sync_lms_grades = Mock(return_value={'grades_synced': 100})
        
        result = integration_service.sync_lms_data('lms_001', ['courses', 'enrollments', 'grades'])
        
        assert 'sync_id' in result
        assert result['integration_id'] == 'lms_001'
        assert 'courses' in result['results']
        assert 'enrollments' in result['results']
        assert 'grades' in result['results']
    
    def test_configure_hr_integration(self, integration_service, sample_hr_config):
        """Test HR system integration configuration."""
        # Mock validation and test methods
        integration_service._validate_hr_config = Mock(return_value={'valid': True, 'errors': []})
        integration_service._test_hr_connection = Mock(return_value={'success': True})
        integration_service._encrypt_sensitive_data = Mock(return_value='encrypted_config')
        
        result = integration_service.configure_hr_integration(1, sample_hr_config)
        
        assert 'integration_id' in result
        assert result['status'] == IntegrationStatus.ACTIVE.value
        assert result['hr_system_url'] == 'https://company.workday.com'
        assert result['hr_system_type'] == IntegrationType.HR_WORKDAY.value
        assert 'sync_capabilities' in result
        assert result['sync_capabilities']['employees'] is True
    
    def test_encryption_decryption(self, integration_service):
        """Test data encryption and decryption."""
        test_data = {
            'username': 'test_user',
            'password': 'secret_password',
            'api_key': 'sensitive_api_key'
        }
        
        # Test encryption
        encrypted_data = integration_service._encrypt_sensitive_data(test_data)
        assert isinstance(encrypted_data, str)
        assert encrypted_data != json.dumps(test_data)
        
        # Test decryption
        decrypted_data = integration_service._decrypt_sensitive_data(encrypted_data)
        assert decrypted_data == test_data
    
    def test_validate_sso_config(self, integration_service):
        """Test SSO configuration validation."""
        # Valid configuration
        valid_config = {
            'type': IntegrationType.SSO_SAML.value,
            'provider_name': 'Azure AD',
            'entity_id': 'test_entity',
            'sso_url': 'https://login.example.com',
            'x509_certificate': 'cert_data'
        }
        
        result = integration_service._validate_sso_config(valid_config)
        assert result['valid'] is True
        assert len(result['errors']) == 0
        
        # Invalid configuration (missing required fields)
        invalid_config = {
            'type': IntegrationType.SSO_SAML.value,
            'provider_name': 'Azure AD'
            # Missing entity_id, sso_url, x509_certificate
        }
        
        result = integration_service._validate_sso_config(invalid_config)
        assert result['valid'] is False
        assert len(result['errors']) > 0
    
    def test_validate_ldap_config(self, integration_service):
        """Test LDAP configuration validation."""
        # Valid configuration
        valid_config = {
            'server_url': 'ldaps://dc.company.com:636',
            'bind_dn': 'CN=service',
            'bind_password': 'password',
            'user_base_dn': 'OU=Users,DC=company,DC=com'
        }
        
        result = integration_service._validate_ldap_config(valid_config)
        assert result['valid'] is True
        assert len(result['errors']) == 0
        
        # Invalid configuration
        invalid_config = {
            'server_url': 'ldaps://dc.company.com:636'
            # Missing required fields
        }
        
        result = integration_service._validate_ldap_config(invalid_config)
        assert result['valid'] is False
        assert len(result['errors']) > 0
    
    def test_validate_lms_config(self, integration_service):
        """Test LMS configuration validation."""
        # Valid configuration
        valid_config = {
            'type': IntegrationType.LMS_CANVAS.value,
            'lms_url': 'https://company.instructure.com',
            'api_key': 'api_key_here'
        }
        
        result = integration_service._validate_lms_config(valid_config)
        assert result['valid'] is True
        assert len(result['errors']) == 0
        
        # Invalid configuration
        invalid_config = {
            'type': IntegrationType.LMS_CANVAS.value
            # Missing required fields
        }
        
        result = integration_service._validate_lms_config(invalid_config)
        assert result['valid'] is False
        assert len(result['errors']) > 0
    
    def test_validate_hr_config(self, integration_service):
        """Test HR system configuration validation."""
        # Valid configuration
        valid_config = {
            'type': IntegrationType.HR_WORKDAY.value,
            'hr_system_url': 'https://company.workday.com',
            'username': 'user',
            'password': 'password'
        }
        
        result = integration_service._validate_hr_config(valid_config)
        assert result['valid'] is True
        assert len(result['errors']) == 0
        
        # Invalid configuration
        invalid_config = {
            'type': IntegrationType.HR_WORKDAY.value
            # Missing required fields
        }
        
        result = integration_service._validate_hr_config(invalid_config)
        assert result['valid'] is False
        assert len(result['errors']) > 0


class TestSSOAuthenticationService:
    """Test cases for the SSO Authentication Service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def sso_service(self, mock_db):
        """Create an SSO authentication service instance for testing."""
        return SSOAuthenticationService(mock_db)
    
    def test_initiate_saml_login(self, sso_service):
        """Test SAML login initiation."""
        # Mock SSO integration
        sso_service._get_sso_integration = Mock(return_value={
            'integration_id': 'sso_001',
            'provider_name': 'Azure AD',
            'entity_id': 'test_entity',
            'sso_url': 'https://login.example.com/saml2'
        })
        
        sso_service._get_acs_url = Mock(return_value='https://api.certpathfinder.com/acs')
        sso_service._store_saml_request = Mock()
        
        result = sso_service.initiate_saml_login('sso_001', 'relay_state_123')
        
        assert 'sso_url' in result
        assert 'request_id' in result
        assert result['relay_state'] == 'relay_state_123'
        assert result['method'] == 'redirect'
    
    def test_process_saml_response(self, sso_service):
        """Test SAML response processing."""
        # Mock SSO integration
        sso_service._get_sso_integration = Mock(return_value={
            'integration_id': 'sso_001',
            'organization_id': 1,
            'provider_name': 'Azure AD',
            'attribute_mapping': {
                'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'
            },
            'auto_provision_users': True
        })
        
        # Mock SAML parsing
        sso_service._parse_saml_response = Mock(return_value={
            'valid': True,
            'attributes': {
                'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': '<EMAIL>'
            },
            'subject_id': 'user_123'
        })
        
        # Mock user operations
        sso_service._map_saml_attributes = Mock(return_value={'email': '<EMAIL>'})
        sso_service._find_or_create_user = Mock(return_value={
            'user_id': 'user_123',
            'email': '<EMAIL>'
        })
        sso_service._generate_session_token = Mock(return_value='session_token_123')
        sso_service._log_authentication_event = Mock()
        
        result = sso_service.process_saml_response('sso_001', 'saml_response_data')
        
        assert result['authentication_status'] == 'success'
        assert 'user_profile' in result
        assert 'session_token' in result
        assert result['sso_provider'] == 'Azure AD'
    
    def test_initiate_oidc_login(self, sso_service):
        """Test OpenID Connect login initiation."""
        # Mock SSO integration
        sso_service._get_sso_integration = Mock(return_value={
            'integration_id': 'sso_001',
            'client_id': 'client_123',
            'authorization_endpoint': 'https://login.example.com/oauth2/authorize'
        })
        
        sso_service._get_oidc_redirect_uri = Mock(return_value='https://api.certpathfinder.com/callback')
        sso_service._store_oidc_state = Mock()
        
        result = sso_service.initiate_oidc_login('sso_001')
        
        assert 'auth_url' in result
        assert 'state' in result
        assert 'nonce' in result
        assert result['method'] == 'redirect'
    
    def test_logout_sso_user(self, sso_service):
        """Test SSO user logout."""
        # Mock session validation
        sso_service._validate_session_token = Mock(return_value={
            'user_id': 'user_123',
            'integration_id': 'sso_001'
        })
        
        # Mock SSO integration
        sso_service._get_sso_integration = Mock(return_value={
            'slo_url': 'https://login.example.com/logout'
        })
        
        sso_service._invalidate_session_token = Mock()
        sso_service._build_sso_logout_url = Mock(return_value='https://login.example.com/logout?user=123')
        sso_service._log_authentication_event = Mock()
        
        result = sso_service.logout_sso_user('session_token_123')
        
        assert result['logout_status'] == 'success'
        assert 'sso_logout_url' in result
        assert result['message'] == 'User logged out successfully'


class TestLDAPSyncService:
    """Test cases for the LDAP Sync Service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def ldap_service(self, mock_db):
        """Create an LDAP sync service instance for testing."""
        return LDAPSyncService(mock_db)
    
    def test_test_ldap_connection(self, ldap_service):
        """Test LDAP connection testing."""
        ldap_config = {
            'server_url': 'ldaps://dc.company.com:636',
            'bind_dn': 'CN=service',
            'bind_password': 'password',
            'user_base_dn': 'OU=Users,DC=company,DC=com',
            'use_ssl': True
        }
        
        # Mock successful connection
        mock_connection = Mock()
        mock_connection.search.return_value = True
        mock_connection.server.info.vendor_name = 'Microsoft'
        mock_connection.server.info.vendor_version = '2019'
        
        ldap_service.connect_to_ldap = Mock(return_value=mock_connection)
        
        result = ldap_service.test_ldap_connection(ldap_config)
        
        assert result['success'] is True
        assert 'server_info' in result
        assert result['server_info']['vendor'] == 'Microsoft'
    
    @patch('services.ldap_sync_service.asyncio')
    async def test_sync_ldap_users(self, mock_asyncio, ldap_service):
        """Test LDAP user synchronization."""
        # Mock integration lookup
        ldap_service._get_ldap_integration = Mock(return_value={
            'integration_id': 'ldap_001',
            'organization_id': 1,
            'user_base_dn': 'OU=Users,DC=company,DC=com',
            'user_filter': '(objectClass=person)',
            'attribute_mapping': {'email': 'mail'},
            'configuration': 'encrypted_config'
        })
        
        # Mock connection and search
        mock_connection = Mock()
        ldap_service.connect_to_ldap = Mock(return_value=mock_connection)
        ldap_service.integration_service._decrypt_sensitive_data = Mock(return_value={
            'server_url': 'ldaps://dc.company.com:636'
        })
        
        # Mock user search and processing
        ldap_service._search_ldap_users = Mock(return_value=[
            {'attributes': {'email': '<EMAIL>'}},
            {'attributes': {'email': '<EMAIL>'}}
        ])
        ldap_service._process_user_sync = Mock(return_value={
            'users_created': 2,
            'users_updated': 0,
            'errors': []
        })
        ldap_service._update_integration_sync_time = Mock()
        
        result = await ldap_service.sync_ldap_users('ldap_001')
        
        assert result['users_created'] == 2
        assert result['users_updated'] == 0
        assert len(result['errors']) == 0


if __name__ == "__main__":
    pytest.main([__file__])
