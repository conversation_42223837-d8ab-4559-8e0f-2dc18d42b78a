"""Comprehensive tests for CertRatsAgent4 unified system.

This module tests the integration of Agent 2 (AI Study Assistant) and 
Agent 4 (Career & Cost Intelligence) through the unified API.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json

from api.app import app
from database import get_db
from models.certification import Certification
from models.career_transition import CareerRole
from models.enterprise import Enterprise, EnterpriseTeam
from services.ai_study_assistant import OnDeviceAIStudyAssistant
from services.salary_intelligence import SalaryIntelligenceService
from services.enterprise_budget_optimizer import EnterpriseBudgetOptimizer


class TestCertRatsAgent4Integration:
    """Test suite for CertRatsAgent4 unified system."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def db_session(self):
        """Create test database session."""
        # This would be implemented with actual test database setup
        pass
    
    @pytest.fixture
    def sample_certification(self, db_session):
        """Create sample certification for testing."""
        cert = Certification(
            name="CISSP",
            organization="(ISC)²",
            domain="cybersecurity",
            difficulty=4,
            cost=749.0,
            is_active=True
        )
        db_session.add(cert)
        db_session.commit()
        return cert
    
    @pytest.fixture
    def sample_career_role(self, db_session):
        """Create sample career role for testing."""
        role = CareerRole(
            name="Senior Security Engineer",
            domain="cybersecurity",
            level="Senior",
            salary_min=120000,
            salary_max=180000,
            is_active=True
        )
        db_session.add(role)
        db_session.commit()
        return role
    
    def test_comprehensive_plan_creation(self, client, sample_certification, sample_career_role):
        """Test comprehensive plan creation endpoint."""
        request_data = {
            "target_certification_id": sample_certification.id,
            "current_role_id": sample_career_role.id,
            "target_role_id": sample_career_role.id,
            "max_budget": 5000.0,
            "max_timeline_months": 12,
            "max_difficulty": "Medium",
            "learning_style": "visual",
            "study_hours_per_week": 15,
            "currency": "USD",
            "location": "remote",
            "experience_years": 7
        }
        
        response = client.post("/api/v1/certratsagent4/comprehensive-plan", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "plan_id" in data
        assert "study_recommendations" in data
        assert "adaptive_learning_path" in data
        assert "career_transition_paths" in data
        assert "roi_analysis" in data
        assert "success_probability" in data
        assert "integrated_recommendations" in data
        
        # Verify data quality
        assert 0.0 <= data["success_probability"] <= 1.0
        assert data["estimated_timeline_weeks"] > 0
        assert data["total_investment_estimate"] >= 0
        assert len(data["study_recommendations"]) <= 10
        assert len(data["integrated_recommendations"]) <= 5
    
    def test_personalized_dashboard(self, client):
        """Test personalized dashboard endpoint."""
        response = client.get("/api/v1/certratsagent4/dashboard")
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "user_id" in data
        assert "top_recommendations" in data
        assert "key_insights" in data
        assert "study_efficiency_score" in data
        assert "consistency_score" in data
        assert "learning_style" in data
        assert "next_milestones" in data
        assert "motivational_message" in data
        assert "quick_actions" in data
        
        # Verify data ranges
        assert 0.0 <= data["current_knowledge_level"] <= 1.0
        assert 0.0 <= data["study_efficiency_score"] <= 1.0
        assert 0.0 <= data["consistency_score"] <= 1.0
        assert len(data["top_recommendations"]) <= 5
        assert len(data["key_insights"]) <= 3
        assert len(data["next_milestones"]) <= 4
        assert len(data["quick_actions"]) > 0
    
    def test_enterprise_analysis(self, client):
        """Test enterprise analysis endpoint."""
        request_data = {
            "enterprise_id": 1,
            "total_budget": 100000.0,
            "budget_period_months": 12,
            "strategic_priorities": ["cybersecurity", "cloud_security"],
            "include_roi_analysis": True,
            "include_risk_assessment": True
        }
        
        response = client.post("/api/v1/certratsagent4/enterprise-analysis", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "enterprise_id" in data
        assert "optimized_allocation" in data
        assert "projected_cost_savings" in data
        assert "roi_projections" in data
        assert "team_priorities" in data
        assert "recommended_certifications" in data
        assert "implementation_timeline" in data
        assert "risk_assessment" in data
        assert "enterprise_metrics" in data
        assert "key_insights" in data
        assert "next_steps" in data
        
        # Verify data quality
        assert data["total_budget"] == 100000.0
        assert data["projected_cost_savings"] >= 0
        assert isinstance(data["optimized_allocation"], dict)
        assert isinstance(data["roi_projections"], dict)
        assert len(data["key_insights"]) > 0
        assert len(data["next_steps"]) > 0
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/api/v1/certratsagent4/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert "CertRatsAgent4" in data["service"]
        assert "agents" in data
        assert "agent_2" in data["agents"]
        assert "agent_4" in data["agents"]
        assert len(data["features"]) >= 8


class TestAIStudyAssistantService:
    """Test suite for AI Study Assistant service."""
    
    def test_recommendation_generation(self, db_session):
        """Test AI recommendation generation."""
        assistant = OnDeviceAIStudyAssistant(db_session)
        
        recommendations = assistant.generate_personalized_recommendations(
            user_id="test_user",
            context="general"
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) <= 10
        
        if recommendations:
            rec = recommendations[0]
            assert hasattr(rec, 'type')
            assert hasattr(rec, 'priority')
            assert hasattr(rec, 'title')
            assert hasattr(rec, 'confidence_score')
            assert 1 <= rec.priority <= 5
            assert 0.0 <= rec.confidence_score <= 1.0
    
    def test_adaptive_path_generation(self, db_session, sample_certification):
        """Test adaptive learning path generation."""
        assistant = OnDeviceAIStudyAssistant(db_session)
        
        path = assistant.generate_adaptive_learning_path(
            user_id="test_user",
            certification_id=sample_certification.id
        )
        
        assert hasattr(path, 'path_id')
        assert hasattr(path, 'name')
        assert hasattr(path, 'estimated_duration_weeks')
        assert hasattr(path, 'success_probability')
        assert hasattr(path, 'topics')
        
        assert path.estimated_duration_weeks > 0
        assert 0.0 <= path.success_probability <= 1.0
        assert isinstance(path.topics, list)
    
    def test_learning_insights(self, db_session):
        """Test learning pattern analysis."""
        assistant = OnDeviceAIStudyAssistant(db_session)
        
        insights = assistant.analyze_learning_patterns("test_user")
        
        assert isinstance(insights, list)
        
        if insights:
            insight = insights[0]
            assert hasattr(insight, 'category')
            assert hasattr(insight, 'insight_type')
            assert hasattr(insight, 'title')
            assert hasattr(insight, 'impact_score')
            assert 0.0 <= insight.impact_score <= 1.0


class TestSalaryIntelligenceService:
    """Test suite for Salary Intelligence service."""
    
    def test_salary_analysis(self, db_session, sample_career_role):
        """Test salary intelligence analysis."""
        service = SalaryIntelligenceService(db_session)
        
        analysis = service.analyze_salary_intelligence(
            role_id=sample_career_role.id,
            location="remote",
            experience_years=5
        )
        
        assert analysis.role_id == sample_career_role.id
        assert analysis.role_name == sample_career_role.name
        assert isinstance(analysis.current_salary_range, dict)
        assert isinstance(analysis.market_salary_range, dict)
        assert isinstance(analysis.certification_impact, dict)
        assert isinstance(analysis.growth_projections, dict)
        
        # Verify salary ranges have required keys
        assert 'min' in analysis.current_salary_range
        assert 'max' in analysis.current_salary_range
        assert 'median' in analysis.current_salary_range
    
    def test_roi_calculation(self, db_session, sample_certification, sample_career_role):
        """Test ROI calculation for certification."""
        service = SalaryIntelligenceService(db_session)
        
        roi = service.calculate_certification_roi(
            certification_id=sample_certification.id,
            current_role_id=sample_career_role.id,
            investment_cost=3000.0,
            location="remote",
            experience_years=5
        )
        
        assert roi.investment_cost == 3000.0
        assert roi.expected_salary_increase >= 0
        assert roi.payback_period_months > 0
        assert roi.five_year_roi >= 0
        assert roi.ten_year_roi >= 0
        assert 0.0 <= roi.confidence_score <= 1.0
        assert isinstance(roi.risk_factors, list)
        assert isinstance(roi.market_conditions, dict)
    
    def test_salary_benchmarks(self, db_session):
        """Test salary benchmark generation."""
        service = SalaryIntelligenceService(db_session)
        
        benchmarks = service.get_salary_benchmarks(
            domain="cybersecurity",
            location="remote"
        )
        
        assert isinstance(benchmarks, dict)
        assert "domain" in benchmarks
        assert "location" in benchmarks
        assert "benchmarks" in benchmarks
        assert "market_summary" in benchmarks


class TestEnterpriseBudgetOptimizer:
    """Test suite for Enterprise Budget Optimizer."""
    
    @pytest.fixture
    def sample_enterprise(self, db_session):
        """Create sample enterprise for testing."""
        enterprise = Enterprise(
            name="Test Corp",
            industry="Technology",
            size="Medium",
            is_active=True
        )
        db_session.add(enterprise)
        db_session.commit()
        return enterprise
    
    def test_budget_optimization(self, db_session, sample_enterprise):
        """Test enterprise budget optimization."""
        optimizer = EnterpriseBudgetOptimizer(db_session)
        
        optimization = optimizer.optimize_enterprise_budget(
            enterprise_id=sample_enterprise.id,
            total_budget=100000.0,
            budget_period_months=12,
            strategic_priorities=["cybersecurity", "cloud"]
        )
        
        assert optimization.total_budget == 100000.0
        assert isinstance(optimization.optimized_allocation, dict)
        assert optimization.cost_savings >= 0
        assert isinstance(optimization.roi_projections, dict)
        assert isinstance(optimization.recommended_certifications, list)
        assert isinstance(optimization.team_priorities, list)
        assert isinstance(optimization.timeline_recommendations, dict)
        assert isinstance(optimization.risk_assessment, dict)
    
    def test_budget_report_generation(self, db_session, sample_enterprise):
        """Test budget report generation."""
        optimizer = EnterpriseBudgetOptimizer(db_session)
        
        # First optimize
        optimization = optimizer.optimize_enterprise_budget(
            enterprise_id=sample_enterprise.id,
            total_budget=50000.0,
            budget_period_months=12
        )
        
        # Then generate report
        report = optimizer.generate_budget_report(sample_enterprise.id, optimization)
        
        assert isinstance(report, dict)
        assert "enterprise_name" in report
        assert "executive_summary" in report
        assert "detailed_allocation" in report
        assert "roi_analysis" in report
        assert "key_insights" in report
        assert "next_steps" in report
        
        # Verify executive summary
        exec_summary = report["executive_summary"]
        assert "total_budget" in exec_summary
        assert "projected_cost_savings" in exec_summary
        assert "expected_roi" in exec_summary


class TestSalaryIntelligenceServiceExtended:
    """Extended test suite for Salary Intelligence service with comprehensive coverage."""

    def test_market_trends_analysis(self, db_session):
        """Test market trends analysis for different domains."""
        service = SalaryIntelligenceService(db_session)

        # Test cybersecurity domain
        trends = service._analyze_market_trends('cybersecurity')
        assert isinstance(trends, dict)
        assert 'growth_rate' in trends
        assert 'market_outlook' in trends
        assert 'demand_level' in trends
        assert 'key_drivers' in trends
        assert 'emerging_skills' in trends
        assert 'risk_factors' in trends

        # Test cloud domain
        cloud_trends = service._analyze_market_trends('cloud')
        assert cloud_trends['growth_rate'] > 0
        assert isinstance(cloud_trends['key_drivers'], list)
        assert len(cloud_trends['emerging_skills']) > 0

    def test_certification_impact_analysis(self, db_session, sample_career_role):
        """Test certification impact analysis."""
        service = SalaryIntelligenceService(db_session)

        impact = service._analyze_certification_impact(sample_career_role)
        assert isinstance(impact, dict)

        # Check if any certifications are analyzed
        if impact:
            cert_name = list(impact.keys())[0]
            cert_impact = impact[cert_name]
            assert 'salary_premium_percentage' in cert_impact
            assert 'estimated_increase' in cert_impact
            assert 'market_demand' in cert_impact
            assert cert_impact['salary_premium_percentage'] >= 0

    def test_location_adjustments(self, db_session):
        """Test location-based salary adjustments."""
        service = SalaryIntelligenceService(db_session)

        adjustments = service._calculate_location_adjustments('cybersecurity')
        assert isinstance(adjustments, dict)

        # Check major locations are included
        location_names = [loc.lower() for loc in adjustments.keys()]
        assert any('san francisco' in loc for loc in location_names)
        assert any('new york' in loc for loc in location_names)
        assert any('remote' in loc for loc in location_names)

        # Verify adjustment structure
        for location, adjustment in adjustments.items():
            assert 'multiplier' in adjustment
            assert 'description' in adjustment
            assert adjustment['multiplier'] > 0

    def test_experience_multipliers(self, db_session):
        """Test experience-based salary multipliers."""
        service = SalaryIntelligenceService(db_session)

        multipliers = service._get_experience_multipliers('cybersecurity')
        assert isinstance(multipliers, dict)

        # Check standard experience levels
        assert 'entry' in multipliers
        assert 'senior' in multipliers
        assert 'lead' in multipliers

        # Verify multipliers increase with experience
        assert multipliers['senior'] > multipliers['entry']
        assert multipliers['lead'] > multipliers['senior']

    def test_salary_growth_projections(self, db_session, sample_career_role):
        """Test salary growth projections."""
        service = SalaryIntelligenceService(db_session)

        current_range = {'min': 100000, 'max': 150000, 'median': 125000}
        projections = service._project_salary_growth(sample_career_role, current_range)

        assert isinstance(projections, dict)
        assert 'one_year' in projections
        assert 'three_year' in projections
        assert 'five_year' in projections
        assert 'ten_year' in projections
        assert 'annual_growth_rate' in projections

        # Verify growth progression
        assert projections['three_year'] > projections['one_year']
        assert projections['five_year'] > projections['three_year']
        assert projections['ten_year'] > projections['five_year']

    def test_roi_risk_assessment(self, db_session, sample_certification, sample_career_role):
        """Test ROI risk factor assessment."""
        service = SalaryIntelligenceService(db_session)

        risk_factors = service._assess_roi_risk_factors(
            sample_certification, sample_career_role, None
        )

        assert isinstance(risk_factors, list)
        assert len(risk_factors) > 0

        # Check that risk factors are meaningful strings
        for factor in risk_factors:
            assert isinstance(factor, str)
            assert len(factor) > 10  # Meaningful description

    def test_roi_confidence_calculation(self, db_session, sample_certification, sample_career_role):
        """Test ROI confidence score calculation."""
        service = SalaryIntelligenceService(db_session)

        confidence = service._calculate_roi_confidence(
            sample_certification, sample_career_role, 15000.0, {}
        )

        assert isinstance(confidence, float)
        assert 0.0 <= confidence <= 1.0

    def test_market_conditions_analysis(self, db_session):
        """Test market conditions analysis."""
        service = SalaryIntelligenceService(db_session)

        conditions = service._get_market_conditions('cybersecurity')
        assert isinstance(conditions, dict)
        assert 'market_health' in conditions
        assert 'job_availability' in conditions
        assert 'salary_trend' in conditions
        assert 'competition_level' in conditions
        assert 'economic_factors' in conditions

        # Verify economic factors is a list
        assert isinstance(conditions['economic_factors'], list)
        assert len(conditions['economic_factors']) > 0

    def test_market_position_calculation(self, db_session):
        """Test market position calculation."""
        service = SalaryIntelligenceService(db_session)

        current_range = {'median': 120000}
        market_range = {'median': 125000}

        position = service._calculate_market_position(current_range, market_range)
        assert position in ['above_market', 'at_market', 'below_market']

        # Test different scenarios
        high_current = {'median': 140000}
        position_high = service._calculate_market_position(high_current, market_range)
        assert position_high == 'above_market'

        low_current = {'median': 100000}
        position_low = service._calculate_market_position(low_current, market_range)
        assert position_low == 'below_market'

    def test_investment_recommendation_generation(self, db_session):
        """Test investment recommendation generation."""
        service = SalaryIntelligenceService(db_session)

        # Test high ROI scenario
        high_roi_rec = service._generate_investment_recommendation(0.20, 8)
        assert 'highly recommended' in high_roi_rec.lower()

        # Test moderate ROI scenario
        mod_roi_rec = service._generate_investment_recommendation(0.10, 4)
        assert 'recommended' in mod_roi_rec.lower()

        # Test low ROI scenario
        low_roi_rec = service._generate_investment_recommendation(0.05, 2)
        assert 'evaluate carefully' in low_roi_rec.lower()


class TestEnterpriseBudgetOptimizerExtended:
    """Extended test suite for Enterprise Budget Optimizer with comprehensive coverage."""

    @pytest.fixture
    def sample_enterprise_team(self, db_session, sample_enterprise):
        """Create sample enterprise team for testing."""
        team = EnterpriseTeam(
            name="Security Team",
            enterprise_id=sample_enterprise.id,
            target_team_size=10,
            required_skills=["cybersecurity", "incident_response"],
            budget_allocated=50000.0,
            is_active=True
        )
        db_session.add(team)
        db_session.commit()
        return team

    def test_team_needs_analysis(self, db_session, sample_enterprise_team):
        """Test individual team needs analysis."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        analysis = optimizer._analyze_team_needs(
            sample_enterprise_team,
            ["cybersecurity", "cloud_security"]
        )

        assert analysis.team_id == sample_enterprise_team.id
        assert analysis.team_name == sample_enterprise_team.name
        assert isinstance(analysis.current_skills, dict)
        assert isinstance(analysis.skill_gaps, list)
        assert isinstance(analysis.recommended_certifications, list)
        assert analysis.budget_allocation >= 0
        assert analysis.expected_roi >= 0
        assert analysis.priority_score >= 0

    def test_current_skills_analysis(self, db_session, sample_enterprise_team):
        """Test current skills analysis for team members."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        # Create mock team members (would normally be in database)
        team_members = []  # Empty for this test

        skills = optimizer._analyze_current_skills(team_members)
        assert isinstance(skills, dict)

    def test_skill_gap_identification(self, db_session, sample_enterprise_team):
        """Test skill gap identification."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        current_skills = {"cybersecurity": 2, "network_security": 1}
        strategic_priorities = ["cloud_security", "incident_response"]

        gaps = optimizer._identify_skill_gaps(
            sample_enterprise_team, current_skills, strategic_priorities
        )

        assert isinstance(gaps, list)
        # Should identify gaps in strategic priorities
        assert "cloud_security" in gaps or "incident_response" in gaps

    def test_team_certification_recommendations(self, db_session, sample_enterprise_team):
        """Test team certification recommendations."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        skill_gaps = ["cybersecurity", "cloud_security"]
        strategic_priorities = ["cybersecurity"]

        recommendations = optimizer._recommend_team_certifications(
            sample_enterprise_team, skill_gaps, strategic_priorities
        )

        assert isinstance(recommendations, list)
        assert len(recommendations) <= 10  # Should be limited

        if recommendations:
            rec = recommendations[0]
            assert 'certification_id' in rec
            assert 'certification_name' in rec
            assert 'skill_gap' in rec
            assert 'estimated_cost' in rec
            assert 'expected_roi' in rec
            assert 'priority' in rec
            assert rec['priority'] in ['high', 'medium', 'low']

    def test_team_budget_calculation(self, db_session):
        """Test team budget needs calculation."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        recommended_certs = [
            {
                'estimated_cost': 2000,
                'recommended_team_members': 5
            },
            {
                'estimated_cost': 1500,
                'recommended_team_members': 3
            }
        ]

        budget = optimizer._calculate_team_budget_needs(recommended_certs, 10)
        expected = (2000 * 5) + (1500 * 3)
        assert budget == expected

    def test_team_roi_calculation(self, db_session, sample_enterprise_team):
        """Test team ROI calculation."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        recommended_certs = [
            {
                'estimated_cost': 2000,
                'recommended_team_members': 3,
                'expected_roi': 15.0
            }
        ]

        roi = optimizer._calculate_team_roi(sample_enterprise_team, recommended_certs, 10)
        assert isinstance(roi, float)
        assert roi >= 0

    def test_team_priority_calculation(self, db_session, sample_enterprise_team):
        """Test team priority score calculation."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        skill_gaps = ["cybersecurity", "cloud_security"]
        strategic_priorities = ["cybersecurity"]

        priority = optimizer._calculate_team_priority(
            sample_enterprise_team, skill_gaps, 10, strategic_priorities
        )

        assert isinstance(priority, float)
        assert 0 <= priority <= 100

    def test_budget_allocation_optimization(self, db_session):
        """Test budget allocation optimization algorithm."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        # Create mock team analyses
        from services.enterprise_budget_optimizer import TeamAnalysis
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="Team A", current_skills={}, skill_gaps=[],
                recommended_certifications=[], budget_allocation=30000,
                expected_roi=20.0, priority_score=80.0
            ),
            TeamAnalysis(
                team_id=2, team_name="Team B", current_skills={}, skill_gaps=[],
                recommended_certifications=[], budget_allocation=25000,
                expected_roi=15.0, priority_score=60.0
            )
        ]

        allocation = optimizer._optimize_budget_allocation(team_analyses, 50000, 12)

        assert isinstance(allocation, dict)
        assert "Team A" in allocation
        assert "Team B" in allocation
        assert sum(allocation.values()) <= 50000  # Should not exceed budget
        assert allocation["Team A"] >= allocation["Team B"]  # Higher priority gets more

    def test_cost_savings_calculation(self, db_session):
        """Test cost savings calculation from optimization."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        from services.enterprise_budget_optimizer import TeamAnalysis
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="Team A", current_skills={}, skill_gaps=[],
                recommended_certifications=[], budget_allocation=30000,
                expected_roi=20.0, priority_score=80.0
            )
        ]

        optimized_allocation = {"Team A": 25000}

        savings = optimizer._calculate_cost_savings(team_analyses, optimized_allocation)
        assert isinstance(savings, float)
        assert savings >= 0

    def test_roi_projections_calculation(self, db_session):
        """Test ROI projections calculation."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        from services.enterprise_budget_optimizer import TeamAnalysis
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="Team A", current_skills={}, skill_gaps=[],
                recommended_certifications=[], budget_allocation=30000,
                expected_roi=20.0, priority_score=80.0
            )
        ]

        optimized_allocation = {"Team A": 25000}

        projections = optimizer._calculate_roi_projections(team_analyses, optimized_allocation)

        assert isinstance(projections, dict)
        assert 'one_year_roi' in projections
        assert 'three_year_roi' in projections
        assert 'five_year_roi' in projections
        assert 'break_even_months' in projections

        # Verify progression
        assert projections['three_year_roi'] > projections['one_year_roi']
        assert projections['five_year_roi'] > projections['three_year_roi']

    def test_certification_recommendations_generation(self, db_session):
        """Test enterprise-wide certification recommendations."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        from services.enterprise_budget_optimizer import TeamAnalysis
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="Team A", current_skills={}, skill_gaps=[],
                recommended_certifications=[
                    {
                        'certification_name': 'CISSP',
                        'recommended_team_members': 5,
                        'expected_roi': 18.0,
                        'estimated_cost': 2000
                    }
                ],
                budget_allocation=30000, expected_roi=20.0, priority_score=80.0
            )
        ]

        recommendations = optimizer._generate_certification_recommendations(team_analyses)

        assert isinstance(recommendations, list)
        assert len(recommendations) <= 15  # Should be limited

        if recommendations:
            rec = recommendations[0]
            assert 'certification_name' in rec
            assert 'total_recommended_members' in rec
            assert 'average_roi' in rec
            assert 'total_investment' in rec
            assert 'teams_interested' in rec
            assert 'priority' in rec

    def test_team_prioritization(self, db_session):
        """Test team prioritization for training investment."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        from services.enterprise_budget_optimizer import TeamAnalysis
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="High Priority Team", current_skills={},
                skill_gaps=["cybersecurity", "cloud"], recommended_certifications=[],
                budget_allocation=30000, expected_roi=25.0, priority_score=85.0
            ),
            TeamAnalysis(
                team_id=2, team_name="Medium Priority Team", current_skills={},
                skill_gaps=["network"], recommended_certifications=[],
                budget_allocation=20000, expected_roi=15.0, priority_score=60.0
            )
        ]

        priorities = optimizer._prioritize_teams(team_analyses)

        assert isinstance(priorities, list)
        assert len(priorities) == 2

        # Should be sorted by priority score
        assert priorities[0]['priority_score'] >= priorities[1]['priority_score']
        assert priorities[0]['team_name'] == "High Priority Team"

        # Check structure
        for priority in priorities:
            assert 'team_name' in priority
            assert 'priority_score' in priority
            assert 'skill_gaps_count' in priority
            assert 'expected_roi' in priority
            assert 'budget_needed' in priority
            assert 'urgency' in priority
            assert 'key_skill_gaps' in priority
            assert priority['urgency'] in ['high', 'medium', 'low']

    def test_timeline_recommendations_generation(self, db_session):
        """Test implementation timeline recommendations."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        from services.enterprise_budget_optimizer import TeamAnalysis
        team_analyses = [
            TeamAnalysis(
                team_id=i, team_name=f"Team {i}", current_skills={},
                skill_gaps=["cybersecurity"], recommended_certifications=[],
                budget_allocation=10000, expected_roi=15.0, priority_score=70.0 - i*10
            ) for i in range(10)  # Create 10 teams with decreasing priority
        ]

        timeline = optimizer._generate_timeline_recommendations(team_analyses, 12)

        assert isinstance(timeline, dict)
        assert 'immediate_actions' in timeline
        assert 'short_term_goals' in timeline
        assert 'medium_term_goals' in timeline
        assert 'long_term_goals' in timeline

        # Check that high priority teams are in immediate actions
        assert len(timeline['immediate_actions']) <= 2
        assert len(timeline['short_term_goals']) <= 3

        # Verify structure of timeline items
        if timeline['immediate_actions']:
            action = timeline['immediate_actions'][0]
            assert 'team' in action
            assert 'action' in action
            assert 'budget' in action

    def test_optimization_risk_assessment(self, db_session):
        """Test optimization risk assessment."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        from services.enterprise_budget_optimizer import TeamAnalysis
        team_analyses = [
            TeamAnalysis(
                team_id=1, team_name="Team A", current_skills={},
                skill_gaps=["cybersecurity", "cloud"], recommended_certifications=[],
                budget_allocation=30000, expected_roi=10.0, priority_score=50.0
            )
        ]

        optimized_allocation = {"Team A": 45000}  # High concentration
        total_budget = 50000

        risk_assessment = optimizer._assess_optimization_risks(
            team_analyses, optimized_allocation, total_budget
        )

        assert isinstance(risk_assessment, dict)
        assert 'high_risk_factors' in risk_assessment
        assert 'medium_risk_factors' in risk_assessment
        assert 'low_risk_factors' in risk_assessment
        assert 'mitigation_strategies' in risk_assessment
        assert 'overall_risk_score' in risk_assessment

        assert isinstance(risk_assessment['high_risk_factors'], list)
        assert isinstance(risk_assessment['mitigation_strategies'], list)
        assert 0 <= risk_assessment['overall_risk_score'] <= 100

    def test_key_insights_generation(self, db_session):
        """Test key insights generation from optimization."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        from services.enterprise_budget_optimizer import BudgetOptimization
        optimization_result = BudgetOptimization(
            total_budget=100000,
            optimized_allocation={"Team A": 50000},
            cost_savings=15000,
            roi_projections={'one_year_roi': 25.0},
            recommended_certifications=[{'certification_name': 'CISSP'}],
            team_priorities=[{'urgency': 'high'}],
            timeline_recommendations={},
            risk_assessment={}
        )

        insights = optimizer._generate_key_insights(optimization_result)

        assert isinstance(insights, list)
        assert len(insights) > 0

        # Check that insights are meaningful strings
        for insight in insights:
            assert isinstance(insight, str)
            assert len(insight) > 20  # Meaningful insight

    def test_next_steps_generation(self, db_session):
        """Test next steps generation."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        from services.enterprise_budget_optimizer import BudgetOptimization
        optimization_result = BudgetOptimization(
            total_budget=100000,
            optimized_allocation={"Team A": 50000},
            cost_savings=15000,
            roi_projections={'one_year_roi': 25.0},
            recommended_certifications=[],
            team_priorities=[],
            timeline_recommendations={'immediate_actions': [{'team': 'Team A'}]},
            risk_assessment={'overall_risk_score': 25.0}
        )

        next_steps = optimizer._generate_next_steps(optimization_result)

        assert isinstance(next_steps, list)
        assert len(next_steps) > 0

        # Check that steps are actionable
        for step in next_steps:
            assert isinstance(step, str)
            assert len(step) > 10  # Meaningful step


class TestAPIEndpointsExtended:
    """Extended test suite for API endpoints with comprehensive coverage."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    def test_comprehensive_plan_validation(self, client):
        """Test comprehensive plan request validation."""
        # Test with invalid data
        invalid_request = {
            "target_certification_id": -1,  # Invalid ID
            "max_budget": -1000,  # Negative budget
            "max_timeline_months": 0,  # Invalid timeline
            "study_hours_per_week": 50  # Too many hours
        }

        response = client.post("/api/v1/certratsagent4/comprehensive-plan", json=invalid_request)
        assert response.status_code == 422  # Validation error

    def test_comprehensive_plan_minimal_request(self, client):
        """Test comprehensive plan with minimal valid request."""
        minimal_request = {
            "target_certification_id": 1
        }

        response = client.post("/api/v1/certratsagent4/comprehensive-plan", json=minimal_request)
        # Should work with defaults
        assert response.status_code in [200, 404, 500]  # 404/500 if no data, 200 if successful

    def test_dashboard_with_parameters(self, client):
        """Test dashboard with different parameter combinations."""
        # Test with market trends disabled
        response = client.get("/api/v1/certratsagent4/dashboard?include_market_trends=false")
        assert response.status_code == 200

        data = response.json()
        assert data.get("market_trends") is None

        # Test with salary intelligence disabled
        response = client.get("/api/v1/certratsagent4/dashboard?include_salary_intelligence=false")
        assert response.status_code == 200

        data = response.json()
        assert data.get("salary_insights") is None

    def test_enterprise_analysis_validation(self, client):
        """Test enterprise analysis request validation."""
        # Test with invalid enterprise ID
        invalid_request = {
            "enterprise_id": -1,
            "total_budget": -5000,  # Negative budget
            "budget_period_months": 0  # Invalid period
        }

        response = client.post("/api/v1/certratsagent4/enterprise-analysis", json=invalid_request)
        assert response.status_code == 422  # Validation error

    def test_enterprise_analysis_with_priorities(self, client):
        """Test enterprise analysis with strategic priorities."""
        request_with_priorities = {
            "enterprise_id": 1,
            "total_budget": 50000.0,
            "budget_period_months": 6,
            "strategic_priorities": ["cybersecurity", "cloud_security", "compliance"],
            "include_roi_analysis": True,
            "include_risk_assessment": True
        }

        response = client.post("/api/v1/certratsagent4/enterprise-analysis", json=request_with_priorities)
        # Should handle priorities properly
        assert response.status_code in [200, 404, 500]

    def test_error_handling_comprehensive_plan(self, client):
        """Test error handling in comprehensive plan endpoint."""
        # Test with non-existent certification
        request_data = {
            "target_certification_id": 99999,  # Non-existent
            "current_role_id": 99999,  # Non-existent
            "max_budget": 5000.0
        }

        response = client.post("/api/v1/certratsagent4/comprehensive-plan", json=request_data)
        # Should handle gracefully
        assert response.status_code in [404, 500]

        if response.status_code == 500:
            error_data = response.json()
            assert "detail" in error_data

    def test_error_handling_enterprise_analysis(self, client):
        """Test error handling in enterprise analysis endpoint."""
        # Test with non-existent enterprise
        request_data = {
            "enterprise_id": 99999,  # Non-existent
            "total_budget": 100000.0
        }

        response = client.post("/api/v1/certratsagent4/enterprise-analysis", json=request_data)
        # Should handle gracefully
        assert response.status_code in [404, 500]


class TestIntegrationScenarios:
    """Test suite for integration scenarios and workflows."""

    def test_end_to_end_individual_workflow(self, client):
        """Test complete individual user workflow."""
        # Step 1: Get dashboard
        dashboard_response = client.get("/api/v1/certratsagent4/dashboard")
        assert dashboard_response.status_code == 200

        # Step 2: Create comprehensive plan
        plan_request = {
            "target_certification_id": 1,
            "max_budget": 5000.0,
            "study_hours_per_week": 10
        }

        plan_response = client.post("/api/v1/certratsagent4/comprehensive-plan", json=plan_request)
        # Should complete workflow
        assert plan_response.status_code in [200, 404, 500]

    def test_end_to_end_enterprise_workflow(self, client):
        """Test complete enterprise workflow."""
        # Step 1: Analyze enterprise needs
        analysis_request = {
            "enterprise_id": 1,
            "total_budget": 100000.0,
            "strategic_priorities": ["cybersecurity"]
        }

        analysis_response = client.post("/api/v1/certratsagent4/enterprise-analysis", json=analysis_request)
        # Should complete workflow
        assert analysis_response.status_code in [200, 404, 500]

    def test_service_integration_consistency(self, db_session):
        """Test consistency between integrated services."""
        # Initialize services
        salary_service = SalaryIntelligenceService(db_session)
        optimizer = EnterpriseBudgetOptimizer(db_session)

        # Test that both services use consistent data
        market_trends_salary = salary_service._analyze_market_trends('cybersecurity')

        # Both should return consistent market outlook
        assert isinstance(market_trends_salary, dict)
        assert 'growth_rate' in market_trends_salary

    def test_data_consistency_across_apis(self, client):
        """Test data consistency across different API endpoints."""
        # This would test that the same underlying data is used consistently
        # across different endpoints

        # Get health status
        health_response = client.get("/api/v1/certratsagent4/health")
        assert health_response.status_code == 200

        health_data = health_response.json()
        assert health_data["status"] == "healthy"
        assert "agents" in health_data
        assert len(health_data["features"]) >= 8


class TestPerformanceAndScalability:
    """Test suite for performance and scalability scenarios."""

    def test_large_enterprise_analysis(self, db_session):
        """Test performance with large enterprise scenarios."""
        optimizer = EnterpriseBudgetOptimizer(db_session)

        # Create multiple team analyses to simulate large enterprise
        from services.enterprise_budget_optimizer import TeamAnalysis
        large_team_analyses = [
            TeamAnalysis(
                team_id=i, team_name=f"Team {i}", current_skills={},
                skill_gaps=["cybersecurity"], recommended_certifications=[],
                budget_allocation=10000, expected_roi=15.0, priority_score=50.0
            ) for i in range(50)  # 50 teams
        ]

        # Should handle large number of teams efficiently
        allocation = optimizer._optimize_budget_allocation(large_team_analyses, 500000, 12)

        assert isinstance(allocation, dict)
        assert len(allocation) <= 50
        assert sum(allocation.values()) <= 500000

    def test_multiple_concurrent_requests(self, client):
        """Test handling of multiple concurrent requests."""
        import threading
        import time

        results = []

        def make_request():
            response = client.get("/api/v1/certratsagent4/health")
            results.append(response.status_code)

        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All requests should succeed
        assert all(status == 200 for status in results)
        assert len(results) == 10
