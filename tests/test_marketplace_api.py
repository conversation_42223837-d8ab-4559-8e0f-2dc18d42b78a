"""Integration tests for marketplace API endpoints.

This module contains integration tests for the Agent 5 Marketplace & Integration Hub API,
testing the complete request/response cycle for marketplace operations.
"""

import pytest
from decimal import Decimal
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.app import app
from models.marketplace import MarketplaceVendor, MarketplaceCourse


class TestMarketplaceAPI:
    """Test cases for Marketplace API endpoints."""
    
    def test_create_vendor_endpoint(self, client: TestClient, db_session: Session):
        """Test vendor creation endpoint."""
        vendor_data = {
            "vendor_name": "API Test Vendor",
            "vendor_slug": "api-test-vendor",
            "contact_email": "<EMAIL>",
            "contact_phone": "******-0123",
            "website_url": "https://apitestvendor.com",
            "business_name": "API Test Vendor LLC",
            "content_categories": ["cybersecurity", "cloud"],
            "specializations": ["AWS", "Azure"],
            "commission_rate": 0.30,
            "payment_terms": "net_15",
            "minimum_payout": "50.00"
        }
        
        response = client.post("/api/v1/marketplace/vendors", json=vendor_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["vendor_name"] == "API Test Vendor"
        assert data["vendor_slug"] == "api-test-vendor"
        assert data["contact_email"] == "<EMAIL>"
        assert data["status"] == "pending"
        assert data["commission_rate"] == 0.30
        assert data["total_courses"] == 0
    
    def test_get_vendor_endpoint(self, client: TestClient, db_session: Session):
        """Test get vendor endpoint."""
        # Create vendor first
        vendor_data = {
            "vendor_name": "Get Test Vendor",
            "vendor_slug": "get-test-vendor",
            "contact_email": "<EMAIL>"
        }
        
        create_response = client.post("/api/v1/marketplace/vendors", json=vendor_data)
        vendor_id = create_response.json()["id"]
        
        # Get vendor
        response = client.get(f"/api/v1/marketplace/vendors/{vendor_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == vendor_id
        assert data["vendor_name"] == "Get Test Vendor"
        assert data["vendor_slug"] == "get-test-vendor"
    
    def test_get_nonexistent_vendor(self, client: TestClient):
        """Test getting non-existent vendor."""
        response = client.get("/api/v1/marketplace/vendors/99999")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()
    
    def test_list_vendors_endpoint(self, client: TestClient, db_session: Session):
        """Test list vendors endpoint."""
        # Create multiple vendors
        vendors_data = [
            {
                "vendor_name": "List Vendor 1",
                "vendor_slug": "list-vendor-1",
                "contact_email": "<EMAIL>"
            },
            {
                "vendor_name": "List Vendor 2",
                "vendor_slug": "list-vendor-2",
                "contact_email": "<EMAIL>"
            }
        ]
        
        for vendor_data in vendors_data:
            client.post("/api/v1/marketplace/vendors", json=vendor_data)
        
        # List vendors
        response = client.get("/api/v1/marketplace/vendors?page=1&per_page=10")
        
        assert response.status_code == 200
        data = response.json()
        assert "vendors" in data
        assert "total" in data
        assert "page" in data
        assert "per_page" in data
        assert data["total"] >= 2
        assert len(data["vendors"]) >= 2
    
    def test_verify_vendor_endpoint(self, client: TestClient, db_session: Session):
        """Test vendor verification endpoint."""
        # Create vendor
        vendor_data = {
            "vendor_name": "Verify Test Vendor",
            "vendor_slug": "verify-test-vendor",
            "contact_email": "<EMAIL>"
        }
        
        create_response = client.post("/api/v1/marketplace/vendors", json=vendor_data)
        vendor_id = create_response.json()["id"]
        
        # Verify vendor
        response = client.post(f"/api/v1/marketplace/vendors/{vendor_id}/verify?verification_status=verified")
        
        assert response.status_code == 200
        data = response.json()
        assert data["verification_status"] == "verified"
        assert data["status"] == "active"
        assert data["verification_date"] is not None
    
    def test_create_course_endpoint(self, client: TestClient, db_session: Session):
        """Test course creation endpoint."""
        # Create and verify vendor first
        vendor_data = {
            "vendor_name": "Course Test Vendor",
            "vendor_slug": "course-test-vendor",
            "contact_email": "<EMAIL>"
        }
        
        vendor_response = client.post("/api/v1/marketplace/vendors", json=vendor_data)
        vendor_id = vendor_response.json()["id"]
        
        # Verify vendor
        client.post(f"/api/v1/marketplace/vendors/{vendor_id}/verify")
        
        # Create course
        course_data = {
            "vendor_id": vendor_id,
            "course_title": "API Test Course",
            "course_slug": "api-test-course",
            "course_description": "A comprehensive API test course",
            "short_description": "Learn API testing",
            "course_level": "intermediate",
            "duration_hours": 25.5,
            "language": "en",
            "price": "299.99",
            "currency": "USD",
            "learning_objectives": ["Master API testing", "Understand REST principles"],
            "prerequisites": ["Basic programming knowledge"],
            "target_certifications": ["API Testing Certification"],
            "certification_domains": ["testing", "api"],
            "tags": ["api", "testing", "automation"]
        }
        
        response = client.post("/api/v1/marketplace/courses", json=course_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["course_title"] == "API Test Course"
        assert data["course_slug"] == "api-test-course"
        assert data["vendor_id"] == vendor_id
        assert data["course_level"] == "intermediate"
        assert data["price"] == "299.99"
        assert data["status"] == "draft"
        assert len(data["learning_objectives"]) == 2
        assert len(data["tags"]) == 3
    
    def test_course_approval_workflow_endpoints(self, client: TestClient, db_session: Session):
        """Test course approval and publication workflow endpoints."""
        # Create vendor and course
        vendor_data = {
            "vendor_name": "Workflow Test Vendor",
            "vendor_slug": "workflow-test-vendor",
            "contact_email": "<EMAIL>"
        }
        
        vendor_response = client.post("/api/v1/marketplace/vendors", json=vendor_data)
        vendor_id = vendor_response.json()["id"]
        client.post(f"/api/v1/marketplace/vendors/{vendor_id}/verify")
        
        course_data = {
            "vendor_id": vendor_id,
            "course_title": "Workflow Test Course",
            "course_slug": "workflow-test-course",
            "course_level": "beginner",
            "price": "199.99"
        }
        
        course_response = client.post("/api/v1/marketplace/courses", json=course_data)
        course_id = course_response.json()["id"]
        
        # Approve course
        approve_response = client.post(f"/api/v1/marketplace/courses/{course_id}/approve")
        assert approve_response.status_code == 200
        assert approve_response.json()["status"] == "approved"
        assert approve_response.json()["approval_date"] is not None
        
        # Publish course
        publish_response = client.post(f"/api/v1/marketplace/courses/{course_id}/publish")
        assert publish_response.status_code == 200
        assert publish_response.json()["status"] == "published"
        assert publish_response.json()["publication_date"] is not None
    
    def test_course_search_endpoint(self, client: TestClient, db_session: Session):
        """Test course search endpoint."""
        # Create vendor and courses
        vendor_data = {
            "vendor_name": "Search Test Vendor",
            "vendor_slug": "search-test-vendor",
            "contact_email": "<EMAIL>"
        }
        
        vendor_response = client.post("/api/v1/marketplace/vendors", json=vendor_data)
        vendor_id = vendor_response.json()["id"]
        client.post(f"/api/v1/marketplace/vendors/{vendor_id}/verify")
        
        # Create and publish multiple courses
        courses_data = [
            {
                "course_title": "Python Security Fundamentals",
                "course_slug": "python-security-fundamentals",
                "course_level": "beginner",
                "price": "149.99",
                "tags": ["python", "security"]
            },
            {
                "course_title": "Advanced Cybersecurity",
                "course_slug": "advanced-cybersecurity",
                "course_level": "advanced",
                "price": "399.99",
                "tags": ["cybersecurity", "advanced"]
            }
        ]
        
        for course_data in courses_data:
            course_data["vendor_id"] = vendor_id
            course_response = client.post("/api/v1/marketplace/courses", json=course_data)
            course_id = course_response.json()["id"]
            client.post(f"/api/v1/marketplace/courses/{course_id}/approve")
            client.post(f"/api/v1/marketplace/courses/{course_id}/publish")
        
        # Search courses
        search_data = {
            "query": "security",
            "page": 1,
            "per_page": 10,
            "sort_by": "price",
            "sort_order": "asc"
        }
        
        response = client.post("/api/v1/marketplace/courses/search", json=search_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "courses" in data
        assert "total" in data
        assert data["total"] >= 2
        assert len(data["courses"]) >= 2
        
        # Verify sorting by price (ascending)
        courses = data["courses"]
        if len(courses) >= 2:
            assert float(courses[0]["price"]) <= float(courses[1]["price"])
    
    def test_enrollment_endpoint(self, client: TestClient, db_session: Session):
        """Test course enrollment endpoint."""
        # Create vendor and course
        vendor_data = {
            "vendor_name": "Enrollment Test Vendor",
            "vendor_slug": "enrollment-test-vendor",
            "contact_email": "<EMAIL>"
        }
        
        vendor_response = client.post("/api/v1/marketplace/vendors", json=vendor_data)
        vendor_id = vendor_response.json()["id"]
        client.post(f"/api/v1/marketplace/vendors/{vendor_id}/verify")
        
        course_data = {
            "vendor_id": vendor_id,
            "course_title": "Enrollment Test Course",
            "course_slug": "enrollment-test-course",
            "course_level": "beginner",
            "price": "99.99"
        }
        
        course_response = client.post("/api/v1/marketplace/courses", json=course_data)
        course_id = course_response.json()["id"]
        client.post(f"/api/v1/marketplace/courses/{course_id}/approve")
        client.post(f"/api/v1/marketplace/courses/{course_id}/publish")
        
        # Enroll in course
        enrollment_data = {
            "course_id": course_id,
            "user_id": "test_user_123",  # Will be overridden by auth
            "purchase_price": "99.99",
            "currency": "USD",
            "payment_method": "credit_card"
        }
        
        response = client.post("/api/v1/marketplace/enrollments", json=enrollment_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["course_id"] == course_id
        assert data["purchase_price"] == "99.99"
        assert data["status"] == "active"
        assert data["progress_percentage"] == 0.0
    
    def test_course_review_endpoint(self, client: TestClient, db_session: Session):
        """Test course review endpoint."""
        # Create vendor and course
        vendor_data = {
            "vendor_name": "Review Test Vendor",
            "vendor_slug": "review-test-vendor",
            "contact_email": "<EMAIL>"
        }
        
        vendor_response = client.post("/api/v1/marketplace/vendors", json=vendor_data)
        vendor_id = vendor_response.json()["id"]
        client.post(f"/api/v1/marketplace/vendors/{vendor_id}/verify")
        
        course_data = {
            "vendor_id": vendor_id,
            "course_title": "Review Test Course",
            "course_slug": "review-test-course",
            "course_level": "beginner",
            "price": "79.99"
        }
        
        course_response = client.post("/api/v1/marketplace/courses", json=course_data)
        course_id = course_response.json()["id"]
        client.post(f"/api/v1/marketplace/courses/{course_id}/approve")
        client.post(f"/api/v1/marketplace/courses/{course_id}/publish")
        
        # Add review
        review_data = {
            "rating": 5,
            "title": "Excellent Course!",
            "review_text": "This course was fantastic and well-structured.",
            "is_verified_purchase": True
        }
        
        response = client.post(f"/api/v1/marketplace/courses/{course_id}/reviews", json=review_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["course_id"] == course_id
        assert data["rating"] == 5
        assert data["title"] == "Excellent Course!"
        assert data["is_verified_purchase"] is True
    
    def test_marketplace_health_endpoint(self, client: TestClient):
        """Test marketplace health check endpoint."""
        response = client.get("/api/v1/marketplace/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "Marketplace & Integration Hub"
        assert "features" in data
        assert "vendor_management" in data["features"]
        assert "course_marketplace" in data["features"]
        assert "supported_currencies" in data
        assert "supported_languages" in data
