"""
Configuration for Playwright + BEHAVE integration
This module provides the bridge between BEHAVE and Playwright for E2E testing
"""

import os
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from typing import Optional


class PlaywrightBehaveConfig:
    """Configuration class for Playwright + BEHAVE integration"""
    
    def __init__(self):
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # Configuration from environment variables
        self.browser_name = os.getenv('BROWSER', 'chromium').lower()
        self.headless = os.getenv('HEADLESS', 'true').lower() == 'true'
        self.base_url = os.getenv('BASE_URL', 'http://localhost:3000')
        self.timeout = int(os.getenv('TIMEOUT', '30000'))
        self.slow_mo = int(os.getenv('SLOW_MO', '0'))
        
        # Test data
        self.test_users = {
            'valid': {
                'email': '<EMAIL>',
                'password': 'password123'
            },
            'invalid': {
                'email': '<EMAIL>',
                'password': 'wrongpassword'
            },
            'admin': {
                'email': '<EMAIL>',
                'password': 'admin123'
            }
        }
    
    def start_playwright(self):
        """Start Playwright and browser"""
        self.playwright = sync_playwright().start()
        
        # Browser options
        browser_options = {
            'headless': self.headless,
            'slow_mo': self.slow_mo,
            'args': [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        }
        
        # Launch browser based on configuration
        if self.browser_name == 'firefox':
            self.browser = self.playwright.firefox.launch(**browser_options)
        elif self.browser_name == 'webkit':
            self.browser = self.playwright.webkit.launch(**browser_options)
        else:  # Default to chromium
            self.browser = self.playwright.chromium.launch(**browser_options)
    
    def create_context(self):
        """Create a new browser context"""
        if not self.browser:
            raise RuntimeError("Browser not started. Call start_playwright() first.")
        
        context_options = {
            'viewport': {'width': 1920, 'height': 1080},
            'ignore_https_errors': True,
            'java_script_enabled': True,
            'accept_downloads': True,
            'record_video_dir': 'test-results/videos' if os.getenv('RECORD_VIDEO') else None,
            'record_video_size': {'width': 1920, 'height': 1080} if os.getenv('RECORD_VIDEO') else None
        }
        
        self.context = self.browser.new_context(**context_options)
        
        # Set default timeout
        self.context.set_default_timeout(self.timeout)
        self.context.set_default_navigation_timeout(self.timeout)
        
        return self.context
    
    def create_page(self):
        """Create a new page"""
        if not self.context:
            self.create_context()
        
        self.page = self.context.new_page()
        
        # Add console logging for debugging
        self.page.on('console', lambda msg: print(f"Console {msg.type}: {msg.text}"))
        
        # Add error handling
        self.page.on('pageerror', lambda error: print(f"Page error: {error}"))
        
        return self.page
    
    def authenticate_user(self, user_type='valid'):
        """Authenticate a user for testing protected routes"""
        if not self.page:
            raise RuntimeError("Page not created. Call create_page() first.")
        
        user = self.test_users.get(user_type, self.test_users['valid'])
        
        # Add authentication token to localStorage
        self.page.add_init_script(f"""
            localStorage.setItem('access_token', 'mock_token_{user_type}');
            localStorage.setItem('user_email', '{user["email"]}');
            localStorage.setItem('user_type', '{user_type}');
        """)
    
    def cleanup(self):
        """Clean up resources"""
        if self.page:
            self.page.close()
            self.page = None
        
        if self.context:
            self.context.close()
            self.context = None
        
        if self.browser:
            self.browser.close()
            self.browser = None
        
        if self.playwright:
            self.playwright.stop()
            self.playwright = None
    
    def take_screenshot(self, name: str):
        """Take a screenshot for debugging"""
        if self.page:
            screenshot_dir = 'test-results/screenshots'
            os.makedirs(screenshot_dir, exist_ok=True)
            screenshot_path = f"{screenshot_dir}/{name}.png"
            self.page.screenshot(path=screenshot_path, full_page=True)
            return screenshot_path
        return None
    
    def wait_for_network_idle(self):
        """Wait for network to be idle"""
        if self.page:
            self.page.wait_for_load_state('networkidle')
    
    def mock_api_response(self, endpoint: str, response_data: dict, status_code: int = 200):
        """Mock API responses for testing"""
        if not self.page:
            return
        
        def handle_route(route):
            route.fulfill(
                status=status_code,
                content_type='application/json',
                body=str(response_data) if isinstance(response_data, dict) else response_data
            )
        
        self.page.route(f"**{endpoint}", handle_route)
    
    def simulate_network_failure(self, endpoint: str):
        """Simulate network failure for testing error handling"""
        if self.page:
            self.page.route(f"**{endpoint}", lambda route: route.abort())
    
    def simulate_slow_network(self, endpoint: str, delay_ms: int = 2000):
        """Simulate slow network for testing loading states"""
        if not self.page:
            return
        
        async def handle_route(route):
            import asyncio
            await asyncio.sleep(delay_ms / 1000)
            await route.continue_()
        
        self.page.route(f"**{endpoint}", handle_route)
    
    def set_mobile_viewport(self):
        """Set mobile viewport for responsive testing"""
        if self.context:
            self.context.set_viewport_size({'width': 375, 'height': 667})
    
    def set_tablet_viewport(self):
        """Set tablet viewport for responsive testing"""
        if self.context:
            self.context.set_viewport_size({'width': 768, 'height': 1024})
    
    def set_desktop_viewport(self):
        """Set desktop viewport for responsive testing"""
        if self.context:
            self.context.set_viewport_size({'width': 1920, 'height': 1080})
    
    def enable_accessibility_testing(self):
        """Enable accessibility testing features"""
        if self.page:
            # Inject axe-core for accessibility testing
            self.page.add_init_script("""
                // This would inject axe-core library
                // In a real implementation, you'd load the actual axe-core library
                window.axe = {
                    run: () => Promise.resolve({ violations: [] })
                };
            """)
    
    def get_performance_metrics(self):
        """Get performance metrics from the page"""
        if not self.page:
            return {}
        
        return self.page.evaluate("""
            () => {
                const navigation = performance.getEntriesByType('navigation')[0];
                const paint = performance.getEntriesByType('paint');
                
                return {
                    loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
                    domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
                    firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                    firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                    memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0
                };
            }
        """)


# Global configuration instance
playwright_config = PlaywrightBehaveConfig()
