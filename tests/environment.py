import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from playwright_behave_config import playwright_config


def before_all(context):
    """Set up test environment before all tests"""
    context.base_url = os.getenv('BASE_URL', 'http://localhost:3000')
    context.browser = os.getenv('BROWSER', 'chrome').lower()
    context.headless = os.getenv('HEADLESS', 'false').lower() == 'true'
    context.timeout = int(os.getenv('TIMEOUT', '10'))
    context.use_playwright = os.getenv('USE_PLAYWRIGHT', 'false').lower() == 'true'

    # Initialize Playwright if enabled
    if context.use_playwright:
        playwright_config.start_playwright()


def before_scenario(context, scenario):
    """Set up browser before each scenario"""
    # Initialize test data
    context.test_users = {
        'valid': {
            'email': '<EMAIL>',
            'password': 'password123'
        },
        'invalid': {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
    }

    if context.use_playwright:
        # Use Playwright
        context.page = playwright_config.create_page()
        context.playwright_config = playwright_config

        # Set up scenario-specific configurations
        if 'mobile' in scenario.tags:
            playwright_config.set_mobile_viewport()
        elif 'tablet' in scenario.tags:
            playwright_config.set_tablet_viewport()
        else:
            playwright_config.set_desktop_viewport()

        if 'accessibility' in scenario.tags:
            playwright_config.enable_accessibility_testing()

        # Authenticate user if needed
        if 'authenticated' in scenario.tags:
            playwright_config.authenticate_user('valid')
        elif 'admin' in scenario.tags:
            playwright_config.authenticate_user('admin')

    else:
        # Use Selenium (legacy)
        if context.browser == 'chrome':
            chrome_options = Options()
            if context.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')

            # Enable accessibility features for testing
            chrome_options.add_argument('--enable-accessibility-logging')
            chrome_options.add_argument('--force-renderer-accessibility')

            context.driver = webdriver.Chrome(options=chrome_options)

        elif context.browser == 'firefox':
            firefox_options = FirefoxOptions()
            if context.headless:
                firefox_options.add_argument('--headless')
            firefox_options.add_argument('--width=1920')
            firefox_options.add_argument('--height=1080')

            context.driver = webdriver.Firefox(options=firefox_options)

        else:
            raise ValueError(f"Unsupported browser: {context.browser}")

        # Set implicit wait
        context.driver.implicitly_wait(context.timeout)


def after_scenario(context, scenario):
    """Clean up after each scenario"""
    if context.use_playwright and hasattr(context, 'page'):
        # Take screenshot on failure
        if scenario.status == 'failed':
            screenshot_name = f"{scenario.name.replace(' ', '_')}_failed"
            screenshot_path = playwright_config.take_screenshot(screenshot_name)
            if screenshot_path:
                print(f"Screenshot saved: {screenshot_path}")

        # Get performance metrics for performance scenarios
        if 'performance' in scenario.tags:
            metrics = playwright_config.get_performance_metrics()
            print(f"Performance metrics: {metrics}")

        # Clear browser state
        if context.page:
            context.page.evaluate("localStorage.clear();")
            context.page.evaluate("sessionStorage.clear();")

        # Close page but keep browser for reuse
        if context.page:
            context.page.close()

    elif hasattr(context, 'driver'):
        # Selenium cleanup (legacy)
        # Take screenshot on failure
        if scenario.status == 'failed':
            screenshot_dir = 'tests/screenshots'
            os.makedirs(screenshot_dir, exist_ok=True)
            screenshot_path = f"{screenshot_dir}/{scenario.name.replace(' ', '_')}.png"
            context.driver.save_screenshot(screenshot_path)
            print(f"Screenshot saved: {screenshot_path}")

        # Clear browser state
        context.driver.delete_all_cookies()
        context.driver.execute_script("sessionStorage.clear();")
        context.driver.execute_script("localStorage.clear();")

        # Close browser
        context.driver.quit()


def after_all(context):
    """Clean up after all tests"""
    if context.use_playwright:
        playwright_config.cleanup()

    # Any other global cleanup can go here
    pass


# Custom hooks for specific tags
def before_tag(context, tag):
    """Handle specific tags before scenarios"""
    if tag == 'mobile':
        # Set mobile viewport
        context.mobile_mode = True
    elif tag == 'accessibility':
        # Enable accessibility testing mode
        context.accessibility_mode = True
    elif tag == 'performance':
        # Enable performance monitoring
        context.performance_mode = True


def after_tag(context, tag):
    """Handle specific tags after scenarios"""
    if tag == 'mobile':
        # Reset viewport
        if hasattr(context, 'driver'):
            context.driver.set_window_size(1920, 1080)
    elif tag == 'performance':
        # Collect performance metrics
        if hasattr(context, 'driver'):
            performance_logs = context.driver.get_log('performance')
            # Process performance logs if needed
            pass
