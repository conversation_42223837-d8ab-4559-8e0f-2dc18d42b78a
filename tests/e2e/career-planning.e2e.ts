import { test, expect } from '@playwright/test';

test.describe('Career Planning Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to career planning page
    await page.goto('/career-planning');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display career planning interface', async ({ page }) => {
    // Verify page title and description
    await expect(page.locator('h1')).toContainText('Career Planning');
    await expect(page.locator('p')).toContainText('Plan your cybersecurity career transition');
    
    // Verify career path finder form is present
    await expect(page.locator('[data-testid="career-path-finder"]')).toBeVisible();
    
    // Verify form fields are present
    await expect(page.locator('label:has-text("Current Role")')).toBeVisible();
    await expect(page.locator('label:has-text("Target Role")')).toBeVisible();
    await expect(page.locator('label:has-text("Budget")')).toBeVisible();
    await expect(page.locator('label:has-text("Timeline")')).toBeVisible();
  });

  test('should allow user to select roles and enter constraints', async ({ page }) => {
    // Select current role
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    // Select target role
    await page.click('[data-testid="target-role-select"]');
    await page.click('text=Senior Security Engineer');
    
    // Enter budget
    await page.fill('[data-testid="budget-input"]', '5000');
    
    // Enter timeline
    await page.fill('[data-testid="timeline-input"]', '18');
    
    // Verify values are set
    await expect(page.locator('[data-testid="current-role-select"]')).toContainText('Security Analyst');
    await expect(page.locator('[data-testid="target-role-select"]')).toContainText('Senior Security Engineer');
    await expect(page.locator('[data-testid="budget-input"]')).toHaveValue('5000');
    await expect(page.locator('[data-testid="timeline-input"]')).toHaveValue('18');
  });

  test('should find and display career paths', async ({ page }) => {
    // Fill out the form
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.click('[data-testid="target-role-select"]');
    await page.click('text=Senior Security Engineer');
    
    await page.fill('[data-testid="budget-input"]', '5000');
    await page.fill('[data-testid="timeline-input"]', '18');
    
    // Click find paths button
    await page.click('[data-testid="find-paths-button"]');
    
    // Wait for results to load
    await page.waitForSelector('[data-testid="path-options"]', { timeout: 10000 });
    
    // Verify path options are displayed
    await expect(page.locator('[data-testid="path-options"]')).toBeVisible();
    await expect(page.locator('[data-testid="path-option"]')).toHaveCount.greaterThan(0);
    
    // Verify each path option has required information
    const pathOptions = page.locator('[data-testid="path-option"]');
    const firstPath = pathOptions.first();
    
    await expect(firstPath.locator('[data-testid="path-cost"]')).toBeVisible();
    await expect(firstPath.locator('[data-testid="path-duration"]')).toBeVisible();
    await expect(firstPath.locator('[data-testid="path-success-rate"]')).toBeVisible();
  });

  test('should display detailed path information when selected', async ({ page }) => {
    // Set up and find paths (reusing previous test logic)
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.click('[data-testid="target-role-select"]');
    await page.click('text=Senior Security Engineer');
    
    await page.fill('[data-testid="budget-input"]', '5000');
    await page.fill('[data-testid="timeline-input"]', '18');
    
    await page.click('[data-testid="find-paths-button"]');
    await page.waitForSelector('[data-testid="path-options"]');
    
    // Select first path option
    await page.click('[data-testid="path-option"]');
    
    // Verify detailed path information is displayed
    await expect(page.locator('[data-testid="path-details"]')).toBeVisible();
    
    // Verify tabs are present
    await expect(page.locator('[data-testid="overview-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="certifications-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="timeline-tab"]')).toBeVisible();
    
    // Verify overview metrics are displayed
    await expect(page.locator('[data-testid="total-cost-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="duration-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="salary-increase-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="roi-metric"]')).toBeVisible();
  });

  test('should display certifications tab content', async ({ page }) => {
    // Set up and select a path (reusing previous logic)
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.click('[data-testid="target-role-select"]');
    await page.click('text=Senior Security Engineer');
    
    await page.fill('[data-testid="budget-input"]', '5000');
    await page.fill('[data-testid="timeline-input"]', '18');
    
    await page.click('[data-testid="find-paths-button"]');
    await page.waitForSelector('[data-testid="path-options"]');
    await page.click('[data-testid="path-option"]');
    
    // Click certifications tab
    await page.click('[data-testid="certifications-tab"]');
    
    // Verify certifications are listed
    await expect(page.locator('[data-testid="certification-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="certification-item"]')).toHaveCount.greaterThan(0);
    
    // Verify each certification has required information
    const certificationItems = page.locator('[data-testid="certification-item"]');
    const firstCert = certificationItems.first();
    
    await expect(firstCert.locator('[data-testid="certification-name"]')).toBeVisible();
    await expect(firstCert.locator('[data-testid="certification-description"]')).toBeVisible();
  });

  test('should display timeline tab content', async ({ page }) => {
    // Set up and select a path (reusing previous logic)
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.click('[data-testid="target-role-select"]');
    await page.click('text=Senior Security Engineer');
    
    await page.fill('[data-testid="budget-input"]', '5000');
    await page.fill('[data-testid="timeline-input"]', '18');
    
    await page.click('[data-testid="find-paths-button"]');
    await page.waitForSelector('[data-testid="path-options"]');
    await page.click('[data-testid="path-option"]');
    
    // Click timeline tab
    await page.click('[data-testid="timeline-tab"]');
    
    // Verify timeline phases are displayed
    await expect(page.locator('[data-testid="timeline-phases"]')).toBeVisible();
    await expect(page.locator('[data-testid="timeline-phase"]')).toHaveCount.greaterThan(0);
    
    // Verify each phase has required information
    const timelinePhases = page.locator('[data-testid="timeline-phase"]');
    const firstPhase = timelinePhases.first();
    
    await expect(firstPhase.locator('[data-testid="phase-name"]')).toBeVisible();
    await expect(firstPhase.locator('[data-testid="phase-duration"]')).toBeVisible();
  });

  test('should validate form inputs', async ({ page }) => {
    // Try to submit without filling required fields
    await page.click('[data-testid="find-paths-button"]');
    
    // Button should be disabled
    await expect(page.locator('[data-testid="find-paths-button"]')).toBeDisabled();
    
    // Fill partial form
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    // Button should still be disabled
    await expect(page.locator('[data-testid="find-paths-button"]')).toBeDisabled();
    
    // Complete the form
    await page.click('[data-testid="target-role-select"]');
    await page.click('text=Senior Security Engineer');
    await page.fill('[data-testid="budget-input"]', '5000');
    await page.fill('[data-testid="timeline-input"]', '18');
    
    // Button should now be enabled
    await expect(page.locator('[data-testid="find-paths-button"]')).toBeEnabled();
  });

  test('should handle loading states', async ({ page }) => {
    // Fill out the form
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.click('[data-testid="target-role-select"]');
    await page.click('text=Senior Security Engineer');
    
    await page.fill('[data-testid="budget-input"]', '5000');
    await page.fill('[data-testid="timeline-input"]', '18');
    
    // Click find paths button
    await page.click('[data-testid="find-paths-button"]');
    
    // Verify loading state is shown
    await expect(page.locator('[data-testid="find-paths-button"]')).toContainText('Finding Paths...');
    await expect(page.locator('[data-testid="find-paths-button"]')).toBeDisabled();
    
    // Wait for results and verify loading state is cleared
    await page.waitForSelector('[data-testid="path-options"]', { timeout: 10000 });
    await expect(page.locator('[data-testid="find-paths-button"]')).toContainText('Find Career Paths');
    await expect(page.locator('[data-testid="find-paths-button"]')).toBeEnabled();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verify page is still functional on mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="career-path-finder"]')).toBeVisible();
    
    // Verify form fields are accessible
    await expect(page.locator('[data-testid="current-role-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="target-role-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="budget-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="timeline-input"]')).toBeVisible();
    
    // Verify button spans full width on mobile
    await expect(page.locator('[data-testid="find-paths-button"]')).toHaveClass(/w-full/);
  });
});
