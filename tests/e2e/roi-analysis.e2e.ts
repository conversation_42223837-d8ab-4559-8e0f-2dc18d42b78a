import { test, expect } from '@playwright/test';

test.describe('ROI Analysis Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to ROI analysis page
    await page.goto('/roi-analysis');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display ROI analysis interface', async ({ page }) => {
    // Verify page title and description
    await expect(page.locator('h1')).toContainText('ROI Analysis');
    await expect(page.locator('p')).toContainText('Analyze the return on investment');
    
    // Verify ROI calculator form is present
    await expect(page.locator('[data-testid="roi-calculator"]')).toBeVisible();
    
    // Verify form fields are present
    await expect(page.locator('label:has-text("Certification")')).toBeVisible();
    await expect(page.locator('label:has-text("Current Role")')).toBeVisible();
    await expect(page.locator('label:has-text("Investment Cost")')).toBeVisible();
    await expect(page.locator('label:has-text("Location")')).toBeVisible();
    await expect(page.locator('label:has-text("Experience")')).toBeVisible();
  });

  test('should allow user to configure ROI analysis parameters', async ({ page }) => {
    // Select certification
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    // Select current role
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    // Enter investment cost
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    // Select location
    await page.click('[data-testid="location-select"]');
    await page.click('text=San Francisco, CA');
    
    // Set experience
    await page.fill('[data-testid="experience-input"]', '5');
    
    // Verify values are set
    await expect(page.locator('[data-testid="certification-select"]')).toContainText('CISSP');
    await expect(page.locator('[data-testid="current-role-select"]')).toContainText('Security Analyst');
    await expect(page.locator('[data-testid="investment-cost-input"]')).toHaveValue('3000');
    await expect(page.locator('[data-testid="location-select"]')).toContainText('San Francisco, CA');
    await expect(page.locator('[data-testid="experience-input"]')).toHaveValue('5');
  });

  test('should analyze ROI and display results', async ({ page }) => {
    // Configure ROI analysis parameters
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    await page.click('[data-testid="location-select"]');
    await page.click('text=remote');
    
    await page.fill('[data-testid="experience-input"]', '5');
    
    // Click analyze ROI button
    await page.click('[data-testid="analyze-roi-button"]');
    
    // Wait for results to load
    await page.waitForSelector('[data-testid="roi-results"]', { timeout: 10000 });
    
    // Verify ROI results are displayed
    await expect(page.locator('[data-testid="roi-results"]')).toBeVisible();
    
    // Verify key metrics are displayed
    await expect(page.locator('[data-testid="investment-cost-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="salary-increase-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="payback-period-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="five-year-roi-metric"]')).toBeVisible();
    
    // Verify metrics contain expected values
    await expect(page.locator('[data-testid="investment-cost-metric"]')).toContainText('$3,000');
    await expect(page.locator('[data-testid="salary-increase-metric"]')).toContainText('$');
    await expect(page.locator('[data-testid="payback-period-metric"]')).toContainText(/\d+/);
    await expect(page.locator('[data-testid="five-year-roi-metric"]')).toContainText('%');
  });

  test('should display projections tab content', async ({ page }) => {
    // Set up and analyze ROI (reusing previous logic)
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    await page.click('[data-testid="analyze-roi-button"]');
    await page.waitForSelector('[data-testid="roi-results"]');
    
    // Verify projections tab is active by default
    await expect(page.locator('[data-testid="projections-tab"]')).toHaveClass(/bg-white/);
    
    // Verify projections content is displayed
    await expect(page.locator('[data-testid="projections-content"]')).toBeVisible();
    
    // Verify ROI timeline information
    await expect(page.locator('text=5-Year ROI')).toBeVisible();
    await expect(page.locator('text=10-Year ROI')).toBeVisible();
    await expect(page.locator('text=Payback Period')).toBeVisible();
    
    // Verify confidence score section
    await expect(page.locator('text=Confidence Score')).toBeVisible();
    await expect(page.locator('[data-testid="confidence-progress"]')).toBeVisible();
  });

  test('should display risks tab content', async ({ page }) => {
    // Set up and analyze ROI
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    await page.click('[data-testid="analyze-roi-button"]');
    await page.waitForSelector('[data-testid="roi-results"]');
    
    // Click risks tab
    await page.click('[data-testid="risks-tab"]');
    
    // Verify risks content is displayed
    await expect(page.locator('[data-testid="risks-content"]')).toBeVisible();
    
    // Verify risk factors are listed
    await expect(page.locator('[data-testid="risk-factor"]')).toHaveCount.greaterThan(0);
    
    // Verify each risk factor has required information
    const riskFactors = page.locator('[data-testid="risk-factor"]');
    const firstRisk = riskFactors.first();
    
    await expect(firstRisk.locator('[data-testid="risk-icon"]')).toBeVisible();
    await expect(firstRisk.locator('[data-testid="risk-text"]')).toBeVisible();
  });

  test('should display market conditions tab content', async ({ page }) => {
    // Set up and analyze ROI
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    await page.click('[data-testid="analyze-roi-button"]');
    await page.waitForSelector('[data-testid="roi-results"]');
    
    // Click market tab
    await page.click('[data-testid="market-tab"]');
    
    // Verify market content is displayed
    await expect(page.locator('[data-testid="market-content"]')).toBeVisible();
    
    // Verify market conditions information is present
    await expect(page.locator('[data-testid="market-condition"]')).toHaveCount.greaterThan(0);
  });

  test('should validate form inputs', async ({ page }) => {
    // Try to submit without filling required fields
    await page.click('[data-testid="analyze-roi-button"]');
    
    // Button should be disabled
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toBeDisabled();
    
    // Fill partial form
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    // Button should still be disabled
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toBeDisabled();
    
    // Complete required fields
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    // Button should now be enabled
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toBeEnabled();
  });

  test('should handle loading states', async ({ page }) => {
    // Configure ROI analysis parameters
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    // Click analyze ROI button
    await page.click('[data-testid="analyze-roi-button"]');
    
    // Verify loading state is shown
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toContainText('Analyzing...');
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toBeDisabled();
    
    // Wait for results and verify loading state is cleared
    await page.waitForSelector('[data-testid="roi-results"]', { timeout: 10000 });
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toContainText('Analyze ROI');
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toBeEnabled();
  });

  test('should display different ROI colors based on performance', async ({ page }) => {
    // Configure and analyze ROI
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    await page.click('[data-testid="analyze-roi-button"]');
    await page.waitForSelector('[data-testid="roi-results"]');
    
    // Verify ROI metric has color coding
    const roiMetric = page.locator('[data-testid="five-year-roi-metric"]');
    await expect(roiMetric).toBeVisible();
    
    // Should have color class (green, blue, yellow, or red)
    const hasColorClass = await roiMetric.evaluate((el) => {
      return el.className.includes('text-green') || 
             el.className.includes('text-blue') || 
             el.className.includes('text-yellow') || 
             el.className.includes('text-red');
    });
    expect(hasColorClass).toBe(true);
  });

  test('should update investment cost when certification changes', async ({ page }) => {
    // Select CISSP certification
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP - $3,000');
    
    // Verify investment cost is auto-filled
    await expect(page.locator('[data-testid="investment-cost-input"]')).toHaveValue('3000');
    
    // Change to different certification
    await page.click('[data-testid="certification-select"]');
    await page.click('text=Security+ - $800');
    
    // Verify investment cost is updated
    await expect(page.locator('[data-testid="investment-cost-input"]')).toHaveValue('800');
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verify page is still functional on mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="roi-calculator"]')).toBeVisible();
    
    // Verify form fields are accessible
    await expect(page.locator('[data-testid="certification-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="current-role-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="investment-cost-input"]')).toBeVisible();
    
    // Verify button spans full width on mobile
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toHaveClass(/w-full/);
  });

  test('should handle optional target role selection', async ({ page }) => {
    // Configure basic parameters without target role
    await page.click('[data-testid="certification-select"]');
    await page.click('text=CISSP');
    
    await page.click('[data-testid="current-role-select"]');
    await page.click('text=Security Analyst');
    
    await page.fill('[data-testid="investment-cost-input"]', '3000');
    
    // Should be able to analyze without target role
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toBeEnabled();
    
    // Now select target role
    await page.click('[data-testid="target-role-select"]');
    await page.click('text=Senior Security Engineer');
    
    // Should still be enabled
    await expect(page.locator('[data-testid="analyze-roi-button"]')).toBeEnabled();
  });
});
