/**
 * E2E tests for certification explorer functionality
 */
import { test, expect, Page } from '@playwright/test';

// Test data
const testCertifications = [
  {
    name: 'CISSP',
    domain: 'Information Security',
    level: 'Professional',
    cost: '$749'
  },
  {
    name: 'CEH',
    domain: 'Penetration Testing',
    level: 'Associate',
    cost: '$1,199'
  },
  {
    name: 'Security+',
    domain: 'Information Security',
    level: 'Entry Level',
    cost: '$349'
  }
];

test.describe('Certification Explorer', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to certification explorer
    await page.goto('/certifications');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display certification list on page load', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Certification Explorer/);
    
    // Check main heading
    await expect(page.locator('h1')).toContainText('Certification Explorer');
    
    // Check that certifications are loaded
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(3, { timeout: 10000 });
    
    // Verify specific certifications are present
    for (const cert of testCertifications) {
      await expect(page.locator(`text=${cert.name}`)).toBeVisible();
    }
  });

  test('should filter certifications by domain', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Open domain filter
    await page.click('[data-testid="domain-filter"]');
    
    // Select Information Security domain
    await page.click('text=Information Security');
    
    // Apply filter
    await page.click('[data-testid="apply-filters"]');
    
    // Wait for filtered results
    await page.waitForLoadState('networkidle');
    
    // Should show only Information Security certifications
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(2);
    await expect(page.locator('text=CISSP')).toBeVisible();
    await expect(page.locator('text=Security+')).toBeVisible();
    await expect(page.locator('text=CEH')).not.toBeVisible();
  });

  test('should filter certifications by level', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Open level filter
    await page.click('[data-testid="level-filter"]');
    
    // Select Professional level
    await page.click('text=Professional');
    
    // Apply filter
    await page.click('[data-testid="apply-filters"]');
    
    // Wait for filtered results
    await page.waitForLoadState('networkidle');
    
    // Should show only Professional level certifications
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(1);
    await expect(page.locator('text=CISSP')).toBeVisible();
  });

  test('should search certifications by name', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Enter search term
    await page.fill('[data-testid="search-input"]', 'CISSP');
    
    // Wait for search results
    await page.waitForLoadState('networkidle');
    
    // Should show only CISSP
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(1);
    await expect(page.locator('text=CISSP')).toBeVisible();
  });

  test('should sort certifications by cost', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Open sort dropdown
    await page.click('[data-testid="sort-dropdown"]');
    
    // Select sort by cost (ascending)
    await page.click('text=Cost (Low to High)');
    
    // Wait for sorted results
    await page.waitForLoadState('networkidle');
    
    // Verify order: Security+ ($349), CISSP ($749), CEH ($1,199)
    const certificationCards = page.locator('[data-testid="certification-card"]');
    await expect(certificationCards.first()).toContainText('Security+');
    await expect(certificationCards.nth(1)).toContainText('CISSP');
    await expect(certificationCards.last()).toContainText('CEH');
  });

  test('should navigate to certification details', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Click on CISSP certification
    await page.click('text=CISSP');
    
    // Should navigate to details page
    await expect(page).toHaveURL(/\/certifications\/\d+/);
    
    // Check details page content
    await expect(page.locator('h1')).toContainText('CISSP');
    await expect(page.locator('text=Certified Information Systems Security Professional')).toBeVisible();
    await expect(page.locator('text=$749')).toBeVisible();
  });

  test('should handle pagination', async ({ page }) => {
    // Mock large dataset by setting small page size
    await page.goto('/certifications?limit=2');
    
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Should show 2 certifications per page
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(2);
    
    // Check pagination controls
    await expect(page.locator('[data-testid="pagination"]')).toBeVisible();
    await expect(page.locator('[data-testid="next-page"]')).toBeVisible();
    
    // Go to next page
    await page.click('[data-testid="next-page"]');
    
    // Wait for next page to load
    await page.waitForLoadState('networkidle');
    
    // Should show remaining certification
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(1);
  });

  test('should clear all filters', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Apply multiple filters
    await page.click('[data-testid="domain-filter"]');
    await page.click('text=Information Security');
    await page.click('[data-testid="level-filter"]');
    await page.click('text=Professional');
    await page.click('[data-testid="apply-filters"]');
    
    // Wait for filtered results
    await page.waitForLoadState('networkidle');
    
    // Should show filtered results
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(1);
    
    // Clear all filters
    await page.click('[data-testid="clear-filters"]');
    
    // Wait for all results to load
    await page.waitForLoadState('networkidle');
    
    // Should show all certifications again
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(3);
  });

  test('should handle empty search results', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Search for non-existent certification
    await page.fill('[data-testid="search-input"]', 'NonExistentCert');
    
    // Wait for search results
    await page.waitForLoadState('networkidle');
    
    // Should show no results message
    await expect(page.locator('[data-testid="no-results"]')).toBeVisible();
    await expect(page.locator('text=No certifications found')).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Check mobile layout
    await expect(page.locator('[data-testid="mobile-filter-button"]')).toBeVisible();
    
    // Open mobile filter menu
    await page.click('[data-testid="mobile-filter-button"]');
    
    // Check filter menu is visible
    await expect(page.locator('[data-testid="mobile-filter-menu"]')).toBeVisible();
    
    // Apply filter on mobile
    await page.click('text=Information Security');
    await page.click('[data-testid="apply-mobile-filters"]');
    
    // Wait for filtered results
    await page.waitForLoadState('networkidle');
    
    // Should show filtered results
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(2);
  });

  test('should handle loading states', async ({ page }) => {
    // Intercept API call to add delay
    await page.route('**/api/v1/certifications/**', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    // Navigate to page
    await page.goto('/certifications');
    
    // Should show loading state
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
    
    // Wait for content to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Loading state should be gone
    await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible();
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Intercept API call to return error
    await page.route('**/api/v1/certifications/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Internal server error' })
      });
    });
    
    // Navigate to page
    await page.goto('/certifications');
    
    // Should show error message
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('text=Failed to load certifications')).toBeVisible();
    
    // Should show retry button
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
  });

  test('should maintain filter state in URL', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]');
    
    // Apply filters
    await page.click('[data-testid="domain-filter"]');
    await page.click('text=Information Security');
    await page.click('[data-testid="apply-filters"]');
    
    // Wait for filtered results
    await page.waitForLoadState('networkidle');
    
    // Check URL contains filter parameters
    expect(page.url()).toContain('domain=Information%20Security');
    
    // Refresh page
    await page.reload();
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Filters should be maintained
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCount(2);
    await expect(page.locator('[data-testid="domain-filter"]')).toContainText('Information Security');
  });
});
