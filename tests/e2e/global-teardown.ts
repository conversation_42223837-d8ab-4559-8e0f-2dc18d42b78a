/**
 * Global teardown for Playwright E2E tests
 */
import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown for E2E tests...');

  try {
    // Clean up test data
    console.log('🗑️ Cleaning up test data...');
    await cleanupTestData();
    console.log('✅ Test data cleaned up');

    // Clean up test files
    console.log('📁 Cleaning up test files...');
    await cleanupTestFiles();
    console.log('✅ Test files cleaned up');

    // Generate test report summary
    console.log('📊 Generating test report summary...');
    await generateTestSummary();
    console.log('✅ Test report summary generated');

    console.log('🎉 Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid masking test failures
  }
}

async function cleanupTestData(): Promise<void> {
  try {
    // Clean up test certifications
    await fetch('http://localhost:8000/api/v1/admin/cleanup/certifications', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'test_admin'
      },
      body: JSON.stringify({
        test_data_only: true
      })
    });

    // Clean up test organizations
    await fetch('http://localhost:8000/api/v1/admin/cleanup/organizations', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'test_admin'
      },
      body: JSON.stringify({
        test_data_only: true
      })
    });

    // Clean up test users
    await fetch('http://localhost:8000/api/v1/admin/cleanup/users', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'test_admin'
      },
      body: JSON.stringify({
        test_users_only: true
      })
    });

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Failed to cleanup test data:', error);
    // Don't throw - cleanup is best effort
  }
}

async function cleanupTestFiles(): Promise<void> {
  try {
    const fs = require('fs').promises;
    const path = require('path');

    // Clean up temporary test files
    const tempDir = path.join(process.cwd(), 'temp-test-files');
    try {
      await fs.rmdir(tempDir, { recursive: true });
    } catch (error) {
      // Directory might not exist
    }

    // Clean up old test artifacts (keep last 5 runs)
    const testResultsDir = path.join(process.cwd(), 'test-results');
    try {
      const files = await fs.readdir(testResultsDir);
      const sortedFiles = files
        .filter(file => file.startsWith('e2e-'))
        .sort()
        .reverse();

      // Keep only the 5 most recent
      const filesToDelete = sortedFiles.slice(5);
      for (const file of filesToDelete) {
        await fs.unlink(path.join(testResultsDir, file));
      }
    } catch (error) {
      // Directory might not exist or be empty
    }

    console.log('✅ Test files cleanup completed');
  } catch (error) {
    console.error('❌ Failed to cleanup test files:', error);
    // Don't throw - cleanup is best effort
  }
}

async function generateTestSummary(): Promise<void> {
  try {
    const fs = require('fs').promises;
    const path = require('path');

    // Read test results
    const resultsPath = path.join(process.cwd(), 'test-results', 'e2e-results.json');
    let testResults = null;

    try {
      const resultsData = await fs.readFile(resultsPath, 'utf8');
      testResults = JSON.parse(resultsData);
    } catch (error) {
      console.log('ℹ️ No test results file found, skipping summary generation');
      return;
    }

    // Generate summary
    const summary = {
      timestamp: new Date().toISOString(),
      total_tests: testResults.stats?.total || 0,
      passed_tests: testResults.stats?.passed || 0,
      failed_tests: testResults.stats?.failed || 0,
      skipped_tests: testResults.stats?.skipped || 0,
      duration: testResults.stats?.duration || 0,
      success_rate: testResults.stats?.total > 0 
        ? ((testResults.stats?.passed || 0) / testResults.stats.total * 100).toFixed(2) + '%'
        : '0%',
      environment: {
        node_version: process.version,
        platform: process.platform,
        ci: !!process.env.CI
      },
      failed_test_details: testResults.suites
        ?.flatMap(suite => suite.specs || [])
        ?.filter(spec => spec.tests?.some(test => test.status === 'failed'))
        ?.map(spec => ({
          title: spec.title,
          file: spec.file,
          errors: spec.tests
            ?.filter(test => test.status === 'failed')
            ?.map(test => test.error?.message)
        })) || []
    };

    // Write summary to file
    const summaryPath = path.join(process.cwd(), 'test-results', 'e2e-summary.json');
    await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));

    // Log summary to console
    console.log('\n📊 E2E Test Summary:');
    console.log(`   Total Tests: ${summary.total_tests}`);
    console.log(`   Passed: ${summary.passed_tests}`);
    console.log(`   Failed: ${summary.failed_tests}`);
    console.log(`   Skipped: ${summary.skipped_tests}`);
    console.log(`   Success Rate: ${summary.success_rate}`);
    console.log(`   Duration: ${(summary.duration / 1000).toFixed(2)}s`);

    if (summary.failed_tests > 0) {
      console.log('\n❌ Failed Tests:');
      summary.failed_test_details.forEach(test => {
        console.log(`   - ${test.title} (${test.file})`);
      });
    }

    console.log('✅ Test summary generated');
  } catch (error) {
    console.error('❌ Failed to generate test summary:', error);
    // Don't throw - summary generation is optional
  }
}

export default globalTeardown;
