/**
 * E2E tests for cost calculator functionality
 */
import { test, expect, Page } from '@playwright/test';

test.describe('Cost Calculator', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to cost calculator
    await page.goto('/cost-calculator');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display cost calculator interface', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Cost Calculator/);
    
    // Check main heading
    await expect(page.locator('h1')).toContainText('Certification Cost Calculator');
    
    // Check key elements are present
    await expect(page.locator('[data-testid="certification-selector"]')).toBeVisible();
    await expect(page.locator('[data-testid="options-panel"]')).toBeVisible();
    await expect(page.locator('[data-testid="calculate-button"]')).toBeVisible();
  });

  test('should calculate costs for single certification', async ({ page }) => {
    // Select a certification
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    
    // Verify certification is selected
    await expect(page.locator('[data-testid="selected-certifications"]')).toContainText('CISSP');
    
    // Calculate costs
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for calculation results
    await page.waitForSelector('[data-testid="calculation-results"]');
    
    // Verify results are displayed
    await expect(page.locator('[data-testid="total-cost"]')).toBeVisible();
    await expect(page.locator('[data-testid="cost-breakdown"]')).toBeVisible();
    
    // Check specific cost elements
    await expect(page.locator('[data-testid="exam-cost"]')).toContainText('$749');
    await expect(page.locator('[data-testid="total-cost"]')).toContainText('$749');
  });

  test('should calculate costs for multiple certifications', async ({ page }) => {
    // Select multiple certifications
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    await page.click('text=CISM');
    await page.click('text=CEH');
    
    // Verify certifications are selected
    await expect(page.locator('[data-testid="selected-certifications"]')).toContainText('CISSP');
    await expect(page.locator('[data-testid="selected-certifications"]')).toContainText('CISM');
    await expect(page.locator('[data-testid="selected-certifications"]')).toContainText('CEH');
    
    // Calculate costs
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for calculation results
    await page.waitForSelector('[data-testid="calculation-results"]');
    
    // Verify total cost is sum of all certifications
    await expect(page.locator('[data-testid="total-cost"]')).toContainText('$1,497'); // 749 + 499 + 249
    
    // Check breakdown shows all certifications
    await expect(page.locator('[data-testid="cost-breakdown"]')).toContainText('CISSP');
    await expect(page.locator('[data-testid="cost-breakdown"]')).toContainText('CISM');
    await expect(page.locator('[data-testid="cost-breakdown"]')).toContainText('CEH');
  });

  test('should include study materials in calculation', async ({ page }) => {
    // Select certification
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    
    // Enable study materials option
    await page.check('[data-testid="include-study-materials"]');
    
    // Calculate costs
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for results
    await page.waitForSelector('[data-testid="calculation-results"]');
    
    // Verify study materials are included
    await expect(page.locator('[data-testid="study-materials-cost"]')).toBeVisible();
    await expect(page.locator('[data-testid="study-materials-cost"]')).toContainText('$150');
    
    // Total should include study materials
    await expect(page.locator('[data-testid="total-cost"]')).toContainText('$899'); // 749 + 150
  });

  test('should include training costs in calculation', async ({ page }) => {
    // Select certification
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    
    // Enable training option
    await page.check('[data-testid="include-training"]');
    
    // Calculate costs
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for results
    await page.waitForSelector('[data-testid="calculation-results"]');
    
    // Verify training costs are included
    await expect(page.locator('[data-testid="training-cost"]')).toBeVisible();
    await expect(page.locator('[data-testid="training-cost"]')).toContainText('$299');
    
    // Total should include training
    await expect(page.locator('[data-testid="total-cost"]')).toContainText('$1,048'); // 749 + 299
  });

  test('should apply discount percentage', async ({ page }) => {
    // Select certification
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    
    // Apply 10% discount
    await page.fill('[data-testid="discount-input"]', '10');
    
    // Calculate costs
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for results
    await page.waitForSelector('[data-testid="calculation-results"]');
    
    // Verify discount is applied
    await expect(page.locator('[data-testid="discount-amount"]')).toContainText('$74.90'); // 10% of 749
    await expect(page.locator('[data-testid="total-cost"]')).toContainText('$674.10'); // 749 - 74.90
  });

  test('should save and load calculations', async ({ page }) => {
    // Create a calculation
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    await page.check('[data-testid="include-study-materials"]');
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for results
    await page.waitForSelector('[data-testid="calculation-results"]');
    
    // Save calculation
    await page.click('[data-testid="save-calculation"]');
    await page.fill('[data-testid="calculation-name"]', 'My CISSP Calculation');
    await page.click('[data-testid="confirm-save"]');
    
    // Verify save confirmation
    await expect(page.locator('[data-testid="save-success"]')).toBeVisible();
    
    // Navigate to saved calculations
    await page.click('[data-testid="saved-calculations"]');
    
    // Verify calculation is saved
    await expect(page.locator('text=My CISSP Calculation')).toBeVisible();
    
    // Load the calculation
    await page.click('text=My CISSP Calculation');
    
    // Verify calculation is loaded
    await expect(page.locator('[data-testid="selected-certifications"]')).toContainText('CISSP');
    await expect(page.locator('[data-testid="include-study-materials"]')).toBeChecked();
  });

  test('should compare certification costs', async ({ page }) => {
    // Navigate to comparison mode
    await page.click('[data-testid="comparison-mode"]');
    
    // Select certifications to compare
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    await page.click('text=CISM');
    await page.click('text=CEH');
    
    // Generate comparison
    await page.click('[data-testid="compare-button"]');
    
    // Wait for comparison results
    await page.waitForSelector('[data-testid="comparison-table"]');
    
    // Verify comparison table
    await expect(page.locator('[data-testid="comparison-table"]')).toBeVisible();
    
    // Check that all certifications are in the table
    await expect(page.locator('[data-testid="comparison-table"]')).toContainText('CISSP');
    await expect(page.locator('[data-testid="comparison-table"]')).toContainText('CISM');
    await expect(page.locator('[data-testid="comparison-table"]')).toContainText('CEH');
    
    // Verify cost comparison
    await expect(page.locator('[data-testid="comparison-table"]')).toContainText('$749');
    await expect(page.locator('[data-testid="comparison-table"]')).toContainText('$499');
    await expect(page.locator('[data-testid="comparison-table"]')).toContainText('$249');
  });

  test('should handle currency conversion', async ({ page }) => {
    // Select certification
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    
    // Change currency to EUR
    await page.click('[data-testid="currency-selector"]');
    await page.click('text=EUR');
    
    // Calculate costs
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for results
    await page.waitForSelector('[data-testid="calculation-results"]');
    
    // Verify currency conversion
    await expect(page.locator('[data-testid="total-cost"]')).toContainText('€');
    await expect(page.locator('[data-testid="exchange-rate"]')).toBeVisible();
  });

  test('should export calculation results', async ({ page }) => {
    // Create a calculation
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    await page.click('text=CISM');
    await page.check('[data-testid="include-study-materials"]');
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for results
    await page.waitForSelector('[data-testid="calculation-results"]');
    
    // Set up download handler
    const downloadPromise = page.waitForEvent('download');
    
    // Export as PDF
    await page.click('[data-testid="export-pdf"]');
    
    // Wait for download
    const download = await downloadPromise;
    
    // Verify download
    expect(download.suggestedFilename()).toContain('.pdf');
    
    // Export as CSV
    const csvDownloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-csv"]');
    const csvDownload = await csvDownloadPromise;
    
    expect(csvDownload.suggestedFilename()).toContain('.csv');
  });

  test('should handle validation errors', async ({ page }) => {
    // Try to calculate without selecting certifications
    await page.click('[data-testid="calculate-button"]');
    
    // Verify validation error
    await expect(page.locator('[data-testid="validation-error"]')).toBeVisible();
    await expect(page.locator('text=Please select at least one certification')).toBeVisible();
    
    // Try invalid discount percentage
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    await page.fill('[data-testid="discount-input"]', '150'); // Invalid: > 100%
    await page.click('[data-testid="calculate-button"]');
    
    // Verify validation error
    await expect(page.locator('[data-testid="validation-error"]')).toContainText('Discount must be between 0 and 100');
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check mobile layout
    await expect(page.locator('[data-testid="mobile-calculator"]')).toBeVisible();
    
    // Select certification on mobile
    await page.click('[data-testid="mobile-certification-selector"]');
    await page.click('text=CISSP');
    
    // Verify mobile calculation interface
    await expect(page.locator('[data-testid="mobile-options"]')).toBeVisible();
    
    // Calculate on mobile
    await page.click('[data-testid="mobile-calculate-button"]');
    
    // Verify mobile results
    await page.waitForSelector('[data-testid="mobile-results"]');
    await expect(page.locator('[data-testid="mobile-results"]')).toBeVisible();
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Intercept calculation API to return error
    await page.route('**/api/v1/cost-calculator/calculations', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Calculation service unavailable' })
      });
    });
    
    // Try to calculate
    await page.click('[data-testid="certification-selector"]');
    await page.click('text=CISSP');
    await page.click('[data-testid="calculate-button"]');
    
    // Verify error handling
    await expect(page.locator('[data-testid="calculation-error"]')).toBeVisible();
    await expect(page.locator('text=Failed to calculate costs')).toBeVisible();
    await expect(page.locator('[data-testid="retry-calculation"]')).toBeVisible();
  });
});
