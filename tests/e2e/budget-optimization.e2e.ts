import { test, expect } from '@playwright/test';

test.describe('Budget Optimization Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to budget optimization page
    await page.goto('/budget-optimization');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display budget optimization interface', async ({ page }) => {
    // Verify page title and description
    await expect(page.locator('h1')).toContainText('Budget Optimization');
    await expect(page.locator('p')).toContainText('Optimize your training budget allocation');
    
    // Verify budget configuration form is present
    await expect(page.locator('[data-testid="budget-configuration"]')).toBeVisible();
    
    // Verify form fields are present
    await expect(page.locator('label:has-text("Total Budget")')).toBeVisible();
    await expect(page.locator('label:has-text("Timeline")')).toBeVisible();
    await expect(page.locator('label:has-text("Strategic Priorities")')).toBeVisible();
  });

  test('should allow user to configure budget parameters', async ({ page }) => {
    // Enter total budget
    await page.fill('[data-testid="total-budget-input"]', '100000');
    
    // Set timeline
    await page.fill('[data-testid="timeline-input"]', '12');
    
    // Select strategic priorities
    await page.click('[data-testid="priority-cybersecurity"]');
    await page.click('[data-testid="priority-cloud-security"]');
    
    // Verify values are set
    await expect(page.locator('[data-testid="total-budget-input"]')).toHaveValue('100000');
    await expect(page.locator('[data-testid="timeline-input"]')).toHaveValue('12');
    
    // Verify selected priorities are highlighted
    await expect(page.locator('[data-testid="priority-cybersecurity"]')).toHaveClass(/bg-blue/);
    await expect(page.locator('[data-testid="priority-cloud-security"]')).toHaveClass(/bg-blue/);
  });

  test('should optimize budget and display results', async ({ page }) => {
    // Configure budget parameters
    await page.fill('[data-testid="total-budget-input"]', '100000');
    await page.fill('[data-testid="timeline-input"]', '12');
    await page.click('[data-testid="priority-cybersecurity"]');
    await page.click('[data-testid="priority-cloud-security"]');
    
    // Click optimize button
    await page.click('[data-testid="optimize-budget-button"]');
    
    // Wait for optimization results
    await page.waitForSelector('[data-testid="optimization-results"]', { timeout: 10000 });
    
    // Verify key metrics are displayed
    await expect(page.locator('[data-testid="total-budget-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="projected-roi-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="cost-savings-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="efficiency-score-metric"]')).toBeVisible();
    
    // Verify metrics contain expected values
    await expect(page.locator('[data-testid="total-budget-metric"]')).toContainText('$100,000');
    await expect(page.locator('[data-testid="projected-roi-metric"]')).toContainText('%');
    await expect(page.locator('[data-testid="cost-savings-metric"]')).toContainText('$');
    await expect(page.locator('[data-testid="efficiency-score-metric"]')).toContainText('%');
  });

  test('should display budget allocation breakdown', async ({ page }) => {
    // Set up and optimize budget (reusing previous logic)
    await page.fill('[data-testid="total-budget-input"]', '100000');
    await page.fill('[data-testid="timeline-input"]', '12');
    await page.click('[data-testid="priority-cybersecurity"]');
    await page.click('[data-testid="priority-cloud-security"]');
    await page.click('[data-testid="optimize-budget-button"]');
    await page.waitForSelector('[data-testid="optimization-results"]');
    
    // Verify allocation tab is active by default
    await expect(page.locator('[data-testid="allocation-tab"]')).toHaveClass(/bg-white/);
    
    // Verify budget allocation breakdown is displayed
    await expect(page.locator('[data-testid="budget-allocation"]')).toBeVisible();
    await expect(page.locator('[data-testid="allocation-item"]')).toHaveCount.greaterThan(0);
    
    // Verify each allocation item has required information
    const allocationItems = page.locator('[data-testid="allocation-item"]');
    const firstItem = allocationItems.first();
    
    await expect(firstItem.locator('[data-testid="category-name"]')).toBeVisible();
    await expect(firstItem.locator('[data-testid="allocation-amount"]')).toBeVisible();
    await expect(firstItem.locator('[data-testid="allocation-percentage"]')).toBeVisible();
    await expect(firstItem.locator('[data-testid="allocation-progress"]')).toBeVisible();
  });

  test('should display recommendations tab', async ({ page }) => {
    // Set up and optimize budget
    await page.fill('[data-testid="total-budget-input"]', '100000');
    await page.fill('[data-testid="timeline-input"]', '12');
    await page.click('[data-testid="priority-cybersecurity"]');
    await page.click('[data-testid="optimize-budget-button"]');
    await page.waitForSelector('[data-testid="optimization-results"]');
    
    // Click recommendations tab
    await page.click('[data-testid="recommendations-tab"]');
    
    // Verify recommendations are displayed
    await expect(page.locator('[data-testid="recommendations-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="recommendation-item"]')).toHaveCount.greaterThan(0);
    
    // Verify each recommendation has required information
    const recommendationItems = page.locator('[data-testid="recommendation-item"]');
    const firstRecommendation = recommendationItems.first();
    
    await expect(firstRecommendation.locator('[data-testid="recommendation-icon"]')).toBeVisible();
    await expect(firstRecommendation.locator('[data-testid="recommendation-text"]')).toBeVisible();
  });

  test('should display risk assessment tab', async ({ page }) => {
    // Set up and optimize budget
    await page.fill('[data-testid="total-budget-input"]', '100000');
    await page.fill('[data-testid="timeline-input"]', '12');
    await page.click('[data-testid="priority-cybersecurity"]');
    await page.click('[data-testid="optimize-budget-button"]');
    await page.waitForSelector('[data-testid="optimization-results"]');
    
    // Click risks tab
    await page.click('[data-testid="risks-tab"]');
    
    // Verify risk assessment is displayed
    await expect(page.locator('[data-testid="risk-assessment"]')).toBeVisible();
    await expect(page.locator('[data-testid="risk-item"]')).toHaveCount.greaterThan(0);
    
    // Verify each risk item has required information
    const riskItems = page.locator('[data-testid="risk-item"]');
    const firstRisk = riskItems.first();
    
    await expect(firstRisk.locator('[data-testid="risk-icon"]')).toBeVisible();
    await expect(firstRisk.locator('[data-testid="risk-name"]')).toBeVisible();
    await expect(firstRisk.locator('[data-testid="risk-level"]')).toBeVisible();
    await expect(firstRisk.locator('[data-testid="risk-badge"]')).toBeVisible();
  });

  test('should display implementation timeline', async ({ page }) => {
    // Set up and optimize budget
    await page.fill('[data-testid="total-budget-input"]', '100000');
    await page.fill('[data-testid="timeline-input"]', '12');
    await page.click('[data-testid="priority-cybersecurity"]');
    await page.click('[data-testid="optimize-budget-button"]');
    await page.waitForSelector('[data-testid="optimization-results"]');
    
    // Verify implementation timeline is displayed
    await expect(page.locator('[data-testid="implementation-timeline"]')).toBeVisible();
    await expect(page.locator('[data-testid="timeline-phase"]')).toHaveCount.greaterThan(0);
    
    // Verify each timeline phase has required information
    const timelinePhases = page.locator('[data-testid="timeline-phase"]');
    const firstPhase = timelinePhases.first();
    
    await expect(firstPhase.locator('[data-testid="phase-indicator"]')).toBeVisible();
    await expect(firstPhase.locator('[data-testid="phase-name"]')).toBeVisible();
    await expect(firstPhase.locator('[data-testid="phase-duration"]')).toBeVisible();
  });

  test('should validate form inputs', async ({ page }) => {
    // Try to submit without filling required fields
    await page.click('[data-testid="optimize-budget-button"]');
    
    // Button should be disabled
    await expect(page.locator('[data-testid="optimize-budget-button"]')).toBeDisabled();
    
    // Fill partial form
    await page.fill('[data-testid="total-budget-input"]', '100000');
    
    // Button should still be disabled (no priorities selected)
    await expect(page.locator('[data-testid="optimize-budget-button"]')).toBeDisabled();
    
    // Select a priority
    await page.click('[data-testid="priority-cybersecurity"]');
    
    // Button should now be enabled
    await expect(page.locator('[data-testid="optimize-budget-button"]')).toBeEnabled();
  });

  test('should handle loading states', async ({ page }) => {
    // Configure budget parameters
    await page.fill('[data-testid="total-budget-input"]', '100000');
    await page.fill('[data-testid="timeline-input"]', '12');
    await page.click('[data-testid="priority-cybersecurity"]');
    
    // Click optimize button
    await page.click('[data-testid="optimize-budget-button"]');
    
    // Verify loading state is shown
    await expect(page.locator('[data-testid="optimize-budget-button"]')).toContainText('Optimizing...');
    await expect(page.locator('[data-testid="optimize-budget-button"]')).toBeDisabled();
    
    // Wait for results and verify loading state is cleared
    await page.waitForSelector('[data-testid="optimization-results"]', { timeout: 10000 });
    await expect(page.locator('[data-testid="optimize-budget-button"]')).toContainText('Optimize Budget');
    await expect(page.locator('[data-testid="optimize-budget-button"]')).toBeEnabled();
  });

  test('should toggle strategic priorities correctly', async ({ page }) => {
    // Initially no priorities should be selected
    await expect(page.locator('[data-testid="priority-cybersecurity"]')).not.toHaveClass(/bg-blue/);
    
    // Select a priority
    await page.click('[data-testid="priority-cybersecurity"]');
    await expect(page.locator('[data-testid="priority-cybersecurity"]')).toHaveClass(/bg-blue/);
    
    // Deselect the priority
    await page.click('[data-testid="priority-cybersecurity"]');
    await expect(page.locator('[data-testid="priority-cybersecurity"]')).not.toHaveClass(/bg-blue/);
    
    // Select multiple priorities
    await page.click('[data-testid="priority-cybersecurity"]');
    await page.click('[data-testid="priority-cloud-security"]');
    await page.click('[data-testid="priority-network-security"]');
    
    // Verify all selected priorities are highlighted
    await expect(page.locator('[data-testid="priority-cybersecurity"]')).toHaveClass(/bg-blue/);
    await expect(page.locator('[data-testid="priority-cloud-security"]')).toHaveClass(/bg-blue/);
    await expect(page.locator('[data-testid="priority-network-security"]')).toHaveClass(/bg-blue/);
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verify page is still functional on mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="budget-configuration"]')).toBeVisible();
    
    // Verify form fields are accessible
    await expect(page.locator('[data-testid="total-budget-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="timeline-input"]')).toBeVisible();
    
    // Verify priority badges wrap properly on mobile
    await expect(page.locator('[data-testid="strategic-priorities"]')).toBeVisible();
    
    // Verify button spans full width on mobile
    await expect(page.locator('[data-testid="optimize-budget-button"]')).toHaveClass(/w-full/);
  });
});
