/**
 * Global setup for Playwright E2E tests
 */
import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...');

  // Create a browser instance for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for API server to be ready
    console.log('⏳ Waiting for API server...');
    await waitForServer('http://localhost:8000/health', 60000);
    console.log('✅ API server is ready');

    // Wait for frontend server to be ready
    console.log('⏳ Waiting for frontend server...');
    await waitForServer('http://localhost:3000', 60000);
    console.log('✅ Frontend server is ready');

    // Seed test data if needed
    console.log('🌱 Seeding test data...');
    await seedTestData();
    console.log('✅ Test data seeded');

    // Create test user account
    console.log('👤 Creating test user...');
    await createTestUser(page);
    console.log('✅ Test user created');

    console.log('🎉 Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

async function waitForServer(url: string, timeout: number): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        return;
      }
    } catch (error) {
      // Server not ready yet, continue waiting
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error(`Server at ${url} did not become ready within ${timeout}ms`);
}

async function seedTestData(): Promise<void> {
  try {
    // Seed organizations
    await fetch('http://localhost:8000/api/v1/admin/seed/organizations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'test_admin'
      },
      body: JSON.stringify({
        organizations: [
          {
            name: 'ISC2',
            website: 'https://www.isc2.org',
            description: 'International Information System Security Certification Consortium'
          },
          {
            name: 'EC-Council',
            website: 'https://www.eccouncil.org',
            description: 'International Council of Electronic Commerce Consultants'
          },
          {
            name: 'CompTIA',
            website: 'https://www.comptia.org',
            description: 'Computing Technology Industry Association'
          }
        ]
      })
    });

    // Seed certifications
    await fetch('http://localhost:8000/api/v1/admin/seed/certifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'test_admin'
      },
      body: JSON.stringify({
        certifications: [
          {
            name: 'CISSP',
            description: 'Certified Information Systems Security Professional',
            level: 'Professional',
            difficulty: 'Advanced',
            focus: 'General',
            domain: 'Information Security',
            category: 'Security Management',
            cost: 749.0,
            currency: 'USD',
            organization_name: 'ISC2',
            exam_code: 'CISSP',
            validity_period: 36,
            prerequisites: ['5 years experience'],
            recommended_experience: '5+ years in information security'
          },
          {
            name: 'CEH',
            description: 'Certified Ethical Hacker',
            level: 'Associate',
            difficulty: 'Intermediate',
            focus: 'Technical',
            domain: 'Penetration Testing',
            category: 'Ethical Hacking',
            cost: 1199.0,
            currency: 'USD',
            organization_name: 'EC-Council',
            exam_code: '312-50',
            validity_period: 36,
            prerequisites: ['Basic networking knowledge'],
            recommended_experience: '1-2 years in IT security'
          },
          {
            name: 'Security+',
            description: 'CompTIA Security+ Certification',
            level: 'Entry Level',
            difficulty: 'Beginner',
            focus: 'General',
            domain: 'Information Security',
            category: 'Security Fundamentals',
            cost: 349.0,
            currency: 'USD',
            organization_name: 'CompTIA',
            exam_code: 'SY0-601',
            validity_period: 36,
            prerequisites: null,
            recommended_experience: '2+ years in IT administration'
          }
        ]
      })
    });

    console.log('✅ Test data seeded successfully');
  } catch (error) {
    console.error('❌ Failed to seed test data:', error);
    // Don't throw error - tests can still run with existing data
  }
}

async function createTestUser(page: any): Promise<void> {
  try {
    // Navigate to registration page
    await page.goto('http://localhost:3000/register');
    
    // Fill registration form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'TestPassword123!');
    await page.fill('[data-testid="name-input"]', 'Test User');
    
    // Submit registration
    await page.click('[data-testid="register-button"]');
    
    // Wait for registration to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    
    console.log('✅ Test user created and logged in');
  } catch (error) {
    console.log('ℹ️ Test user creation skipped (user may already exist)');
    // Don't throw error - user might already exist
  }
}

export default globalSetup;
