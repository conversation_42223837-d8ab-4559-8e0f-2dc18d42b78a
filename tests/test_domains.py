"""Tests for the security domains API endpoints"""
import pytest
from fastapi.testclient import TestClient
from api.app import create_app
from datetime import datetime

@pytest.fixture
def client():
    """Create a test client"""
    app = create_app()
    return TestClient(app)

def test_list_domains(client):
    """Test listing all security domains"""
    response = client.get("/api/v1/domains/")
    assert response.status_code == 200
    data = response.json()
    
    # Check response structure
    assert "domains" in data
    assert isinstance(data["domains"], list)
    
    # Should have exactly 8 core security domains
    assert len(data["domains"]) == 8
    
    # Check first domain structure
    domain = data["domains"][0]
    assert "id" in domain
    assert "name" in domain
    assert "description" in domain
    assert "created_at" in domain
    assert "updated_at" in domain
    
    # Verify the presence of all core domains
    domain_names = {d["name"] for d in data["domains"]}
    expected_domains = {
        "Security and Risk Management",
        "Asset Security",
        "Security Architecture and Engineering",
        "Communication and Network Security",
        "Identity and Access Management",
        "Security Assessment and Testing",
        "Security Operations",
        "Software Development Security"
    }
    assert domain_names == expected_domains

def test_get_single_domain(client):
    """Test getting a single security domain"""
    # First get all domains to find a valid ID
    response = client.get("/api/v1/domains/")
    domains = response.json()["domains"]
    domain_id = domains[0]["id"]
    
    # Test getting the specific domain
    response = client.get(f"/api/v1/domains/{domain_id}")
    assert response.status_code == 200
    domain = response.json()
    
    # Verify domain structure
    assert domain["id"] == domain_id
    assert isinstance(domain["name"], str)
    assert isinstance(domain["description"], str)
    assert isinstance(domain["created_at"], str)
    assert isinstance(domain["updated_at"], str)

def test_get_nonexistent_domain(client):
    """Test getting a domain that doesn't exist"""
    response = client.get("/api/v1/domains/999999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Domain not found"
