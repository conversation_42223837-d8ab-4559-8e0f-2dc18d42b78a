"""
Core unit tests for Skills Assessment algorithms
Feature 1.1: Skills Vector Representation & Scoring - Unit Tests Phase
Tests the core algorithms without API dependencies
"""

# Skill level enumeration
SKILL_LEVELS = {
    'none': 0.0,
    'basic': 0.25,
    'intermediate': 0.5,
    'advanced': 0.75,
    'expert': 1.0
}

# Confidence weighting
CONFIDENCE_WEIGHTS = {
    'very_confident': 1.0,
    'confident': 0.8,
    'somewhat_confident': 0.6,
    'not_confident': 0.4
}

# 8-Domain Cybersecurity Framework
CYBERSECURITY_DOMAINS = {
    'communication_network_security': {
        'name': 'Communication and Network Security',
        'skills': [
            'network_protocols', 'firewall_configuration', 'vpn_technologies',
            'network_monitoring', 'cloud_network_security', 'wireless_security',
            'network_architecture', 'intrusion_detection', 'packet_analysis'
        ]
    },
    'identity_access_management': {
        'name': 'Identity and Access Management (IAM)',
        'skills': [
            'authentication_mechanisms', 'authorization_rbac', 'privileged_access_management',
            'identity_governance', 'zero_trust_principles', 'single_sign_on',
            'multi_factor_authentication', 'identity_federation', 'access_controls'
        ]
    },
    'security_architecture_engineering': {
        'name': 'Security Architecture and Engineering',
        'skills': [
            'security_frameworks', 'threat_modeling', 'secure_system_design',
            'risk_assessment', 'security_controls', 'security_patterns',
            'defense_in_depth', 'security_by_design', 'architecture_review'
        ]
    },
    'asset_security': {
        'name': 'Asset Security',
        'skills': [
            'data_classification', 'data_loss_prevention', 'endpoint_security',
            'asset_inventory', 'privacy_compliance', 'data_governance',
            'information_lifecycle', 'data_retention', 'asset_management'
        ]
    },
    'security_risk_management': {
        'name': 'Security and Risk Management',
        'skills': [
            'governance_frameworks', 'risk_assessment_methodologies', 'compliance_management',
            'policy_development', 'business_continuity', 'disaster_recovery',
            'risk_mitigation', 'regulatory_compliance', 'security_governance'
        ]
    },
    'security_assessment_testing': {
        'name': 'Security Assessment and Testing',
        'skills': [
            'vulnerability_assessment', 'penetration_testing', 'security_auditing',
            'red_team_operations', 'security_metrics', 'code_review',
            'security_testing', 'compliance_auditing', 'threat_hunting'
        ]
    },
    'software_security': {
        'name': 'Software Security',
        'skills': [
            'secure_coding_practices', 'application_security_testing', 'devsecops',
            'api_security', 'software_supply_chain', 'code_analysis',
            'secure_development_lifecycle', 'application_hardening', 'container_security'
        ]
    },
    'security_operations': {
        'name': 'Security Operations',
        'skills': [
            'incident_response', 'siem_soar', 'threat_hunting_techniques',
            'digital_forensics', 'threat_intelligence', 'security_monitoring',
            'log_analysis', 'malware_analysis', 'security_orchestration'
        ]
    }
}

def calculate_weighted_skill_score(skill_level: str, confidence: str, certifications=None):
    """Calculate final skill score with confidence weighting and certification boost"""
    base_score = SKILL_LEVELS[skill_level]
    confidence_weight = CONFIDENCE_WEIGHTS[confidence]
    
    # Calculate certification boost
    cert_boost = 0.0
    if certifications:
        # Simple boost based on number of relevant certifications
        cert_boost = min(0.3, len(certifications) * 0.1)
    
    # Apply confidence weighting and certification boost
    confidence_weighted = base_score * confidence_weight
    final_score = min(1.0, confidence_weighted + cert_boost)
    
    return {
        'raw_score': base_score,
        'confidence_weighted_score': confidence_weighted,
        'certification_boost': cert_boost,
        'final_score': final_score
    }

def calculate_domain_scores(skill_scores):
    """Calculate domain-level scores from individual skill scores"""
    domain_scores = {}
    
    for domain_id, domain_info in CYBERSECURITY_DOMAINS.items():
        domain_skills = domain_info['skills']
        relevant_scores = [skill_scores.get(skill, 0.0) for skill in domain_skills if skill in skill_scores]
        
        if relevant_scores:
            domain_scores[domain_id] = sum(relevant_scores) / len(relevant_scores)
        else:
            domain_scores[domain_id] = 0.0
    
    return domain_scores

# Test functions
def test_skill_levels_mapping():
    """Test that skill levels are correctly mapped to numerical values"""
    assert SKILL_LEVELS['none'] == 0.0
    assert SKILL_LEVELS['basic'] == 0.25
    assert SKILL_LEVELS['intermediate'] == 0.5
    assert SKILL_LEVELS['advanced'] == 0.75
    assert SKILL_LEVELS['expert'] == 1.0
    print("✅ test_skill_levels_mapping passed")

def test_confidence_weights_mapping():
    """Test that confidence levels are correctly mapped to weights"""
    assert CONFIDENCE_WEIGHTS['very_confident'] == 1.0
    assert CONFIDENCE_WEIGHTS['confident'] == 0.8
    assert CONFIDENCE_WEIGHTS['somewhat_confident'] == 0.6
    assert CONFIDENCE_WEIGHTS['not_confident'] == 0.4
    print("✅ test_confidence_weights_mapping passed")

def test_calculate_weighted_skill_score_basic():
    """Test basic skill score calculation without certifications"""
    result = calculate_weighted_skill_score('intermediate', 'confident')
    
    expected_raw = 0.5  # intermediate level
    expected_weighted = 0.5 * 0.8  # confident weight
    expected_final = expected_weighted  # no cert boost
    
    assert result['raw_score'] == expected_raw
    assert result['confidence_weighted_score'] == expected_weighted
    assert result['certification_boost'] == 0.0
    assert result['final_score'] == expected_final
    print("✅ test_calculate_weighted_skill_score_basic passed")

def test_calculate_weighted_skill_score_with_certifications():
    """Test skill score calculation with certification boost"""
    certifications = ['Security+', 'Network+']
    result = calculate_weighted_skill_score('advanced', 'very_confident', certifications)
    
    expected_raw = 0.75  # advanced level
    expected_weighted = 0.75 * 1.0  # very_confident weight
    expected_cert_boost = min(0.3, len(certifications) * 0.1)  # 0.2
    expected_final = min(1.0, expected_weighted + expected_cert_boost)  # 0.95
    
    assert result['raw_score'] == expected_raw
    assert result['confidence_weighted_score'] == expected_weighted
    assert result['certification_boost'] == expected_cert_boost
    assert result['final_score'] == expected_final
    print("✅ test_calculate_weighted_skill_score_with_certifications passed")

def test_calculate_domain_scores_basic():
    """Test basic domain score calculation"""
    skill_scores = {
        'network_protocols': 0.8,
        'firewall_configuration': 0.6,
        'vpn_technologies': 0.7,
        'incident_response': 0.9,
        'siem_soar': 0.5
    }
    
    domain_scores = calculate_domain_scores(skill_scores)
    
    # Check that all domains are present
    assert len(domain_scores) == len(CYBERSECURITY_DOMAINS)
    
    # Check communication_network_security domain
    comm_net_skills = CYBERSECURITY_DOMAINS['communication_network_security']['skills']
    relevant_scores = [skill_scores[skill] for skill in comm_net_skills if skill in skill_scores]
    expected_comm_net_score = sum(relevant_scores) / len(relevant_scores) if relevant_scores else 0.0
    
    assert domain_scores['communication_network_security'] == expected_comm_net_score
    print("✅ test_calculate_domain_scores_basic passed")

def test_framework_structure():
    """Test that the cybersecurity framework has correct structure"""
    assert len(CYBERSECURITY_DOMAINS) == 8
    
    expected_domains = [
        'communication_network_security',
        'identity_access_management',
        'security_architecture_engineering',
        'asset_security',
        'security_risk_management',
        'security_assessment_testing',
        'software_security',
        'security_operations'
    ]
    
    for domain in expected_domains:
        assert domain in CYBERSECURITY_DOMAINS
    print("✅ test_framework_structure passed")

def test_score_monotonicity():
    """Test that higher skill levels result in higher scores"""
    levels = ['none', 'basic', 'intermediate', 'advanced', 'expert']
    scores = []
    
    for level in levels:
        result = calculate_weighted_skill_score(level, 'confident')
        scores.append(result['final_score'])
    
    # Scores should be non-decreasing
    for i in range(1, len(scores)):
        assert scores[i] >= scores[i-1]
    print("✅ test_score_monotonicity passed")

def test_confidence_monotonicity():
    """Test that higher confidence results in higher scores"""
    confidence_levels = ['not_confident', 'somewhat_confident', 'confident', 'very_confident']
    scores = []
    
    for confidence in confidence_levels:
        result = calculate_weighted_skill_score('intermediate', confidence)
        scores.append(result['final_score'])
    
    # Scores should be non-decreasing
    for i in range(1, len(scores)):
        assert scores[i] >= scores[i-1]
    print("✅ test_confidence_monotonicity passed")

def test_certification_boost_cap():
    """Test that certification boost is capped at 0.3"""
    certifications = ['Cert1', 'Cert2', 'Cert3', 'Cert4', 'Cert5']  # 5 certs
    result = calculate_weighted_skill_score('basic', 'not_confident', certifications)
    
    # Should cap at 0.3 even with 5 certifications
    assert result['certification_boost'] == 0.3
    print("✅ test_certification_boost_cap passed")

def test_final_score_cap():
    """Test that final score is capped at 1.0"""
    certifications = ['Cert1', 'Cert2', 'Cert3']
    result = calculate_weighted_skill_score('expert', 'very_confident', certifications)
    
    # expert (1.0) * very_confident (1.0) + cert_boost (0.3) = 1.3, but capped at 1.0
    assert result['final_score'] == 1.0
    print("✅ test_final_score_cap passed")

def run_all_tests():
    """Run all unit tests"""
    print("Running Skills Assessment Core Unit Tests...")
    print("=" * 50)
    
    tests = [
        test_skill_levels_mapping,
        test_confidence_weights_mapping,
        test_calculate_weighted_skill_score_basic,
        test_calculate_weighted_skill_score_with_certifications,
        test_calculate_domain_scores_basic,
        test_framework_structure,
        test_score_monotonicity,
        test_confidence_monotonicity,
        test_certification_boost_cap,
        test_final_score_cap
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__}: {str(e)}")
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All unit tests passed!")
        return True
    else:
        print(f"❌ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    run_all_tests()
