"""Tests for Mobile API Service functionality.

This module provides comprehensive tests for the mobile API service,
including device registration, authentication, offline sync, push notifications,
and enterprise mobile management features.
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from mobile.mobile_api_service import MobileAPIService, DeviceType, SyncStatus, NotificationType
from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserLicense
from models.progress_tracking import StudySession, PracticeTestResult, LearningGoal


class TestMobileAPIService:
    """Test cases for the Mobile API Service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def mobile_service(self, mock_db):
        """Create a mobile API service instance for testing."""
        return MobileAPIService(mock_db)
    
    @pytest.fixture
    def sample_device_data(self):
        """Create sample device registration data."""
        return {
            'device_type': DeviceType.IOS.value,
            'device_model': 'iPhone 14 Pro',
            'os_version': '17.0',
            'app_version': '1.2.0',
            'push_token': 'abc123def456',
            'device_name': 'Test iPhone',
            'timezone': 'America/New_York',
            'language': 'en',
            'capabilities': {
                'ai_processing': True,
                'storage_mb': 1000,
                'background_processing': True
            },
            'security_settings': {
                'biometric_enabled': True,
                'pin_required': True
            }
        }
    
    @pytest.fixture
    def sample_auth_credentials(self):
        """Create sample authentication credentials."""
        return {
            'email': '<EMAIL>',
            'password': 'secure_password',
            'device_id': 'device_123',
            'biometric_token': None,
            'remember_device': True
        }
    
    def test_register_mobile_device(self, mobile_service, sample_device_data):
        """Test mobile device registration."""
        # Mock user lookup
        mock_user = Mock()
        mock_user.user_id = 'test_user'
        mock_user.organization_id = 1
        mobile_service.db.query.return_value.filter.return_value.first.return_value = mock_user
        
        result = mobile_service.register_mobile_device('test_user', sample_device_data)
        
        assert 'device_id' in result
        assert 'registration_status' in result
        assert result['registration_status'] == 'success'
        assert 'sync_token' in result
        assert 'offline_capabilities' in result
        assert result['initial_sync_required'] is True
    
    def test_register_mobile_device_invalid_user(self, mobile_service, sample_device_data):
        """Test device registration with invalid user."""
        # Mock no user found
        mobile_service.db.query.return_value.filter.return_value.first.return_value = None
        
        result = mobile_service.register_mobile_device('invalid_user', sample_device_data)
        
        assert 'error' in result
    
    def test_authenticate_mobile_user(self, mobile_service, sample_auth_credentials):
        """Test mobile user authentication."""
        # Mock user lookup
        mock_user = Mock()
        mock_user.user_id = 'test_user'
        mock_user.email = '<EMAIL>'
        mock_user.display_name = 'Test User'
        mock_user.role = Mock()
        mock_user.role.value = 'learner'
        mock_user.organization_id = 1
        mock_user.department_id = None
        mock_user.is_active = True
        
        mobile_service.db.query.return_value.filter.return_value.first.return_value = mock_user
        
        result = mobile_service.authenticate_mobile_user(sample_auth_credentials)
        
        assert 'authentication_status' in result
        assert result['authentication_status'] == 'success'
        assert 'session_token' in result
        assert 'user_profile' in result
        assert 'permissions' in result
        assert 'offline_data' in result
        assert result['requires_sync'] is True
    
    def test_authenticate_mobile_user_invalid_credentials(self, mobile_service, sample_auth_credentials):
        """Test authentication with invalid credentials."""
        # Mock no user found
        mobile_service.db.query.return_value.filter.return_value.first.return_value = None
        
        result = mobile_service.authenticate_mobile_user(sample_auth_credentials)
        
        assert 'error' in result
        assert result['error_code'] == 'AUTH_FAILED'
    
    def test_prepare_offline_data_package(self, mobile_service):
        """Test offline data package preparation."""
        # Mock user and related data
        mock_user = Mock()
        mock_user.user_id = 'test_user'
        mock_user.to_dict.return_value = {'user_id': 'test_user', 'name': 'Test User'}
        
        mock_goals = [Mock()]
        mock_goals[0].to_dict.return_value = {'goal_id': 'goal1', 'title': 'Test Goal'}
        
        mock_sessions = [Mock()]
        mock_sessions[0].to_dict.return_value = {'session_id': 'session1', 'duration': 30}
        
        mock_results = [Mock()]
        mock_results[0].to_dict.return_value = {'test_id': 'test1', 'score': 85}
        
        mobile_service.db.query.return_value.filter.return_value.first.return_value = mock_user
        mobile_service.db.query.return_value.filter.return_value.all.return_value = mock_goals
        mobile_service.db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.side_effect = [
            mock_sessions, mock_results
        ]
        
        # Mock AI service methods
        mobile_service._get_offline_ai_recommendations = Mock(return_value={'recommendations': []})
        mobile_service._get_offline_study_materials = Mock(return_value={'materials': []})
        mobile_service._get_offline_ai_models = Mock(return_value={'models': []})
        mobile_service._get_mobile_app_config = Mock(return_value={'config': {}})
        mobile_service._get_sync_settings = Mock(return_value={'settings': {}})
        
        result = mobile_service.prepare_offline_data_package('test_user', 'sync_token_123')
        
        assert 'package_id' in result
        assert 'user_id' in result
        assert result['user_id'] == 'test_user'
        assert 'generated_at' in result
        assert 'sync_token' in result
        assert 'user_profile' in result
        assert 'learning_goals' in result
        assert 'recent_sessions' in result
        assert 'test_results' in result
        assert 'ai_recommendations' in result
    
    def test_prepare_offline_data_package_no_user(self, mobile_service):
        """Test offline data package preparation with no user."""
        mobile_service.db.query.return_value.filter.return_value.first.return_value = None
        
        result = mobile_service.prepare_offline_data_package('invalid_user', 'sync_token_123')
        
        assert 'error' in result
        assert result['error'] == 'User not found'
    
    def test_sync_mobile_data(self, mobile_service):
        """Test mobile data synchronization."""
        sync_data = {
            'sync_token': 'sync_token_123',
            'last_sync_time': '2024-01-15T10:00:00Z',
            'study_sessions': [
                {'session_id': 'session1', 'duration_minutes': 30}
            ],
            'test_results': [
                {'test_id': 'test1', 'score': 85}
            ],
            'learning_goals': [
                {'goal_id': 'goal1', 'title': 'Updated Goal'}
            ]
        }
        
        # Mock sync methods
        mobile_service._sync_study_sessions = Mock(return_value={'uploaded': 1, 'conflicts': 0})
        mobile_service._sync_test_results = Mock(return_value={'uploaded': 1, 'conflicts': 0})
        mobile_service._sync_learning_goals = Mock(return_value={'uploaded': 1, 'conflicts': 0})
        mobile_service._get_updated_data_since_last_sync = Mock(return_value={'updated': []})
        mobile_service._generate_sync_token = Mock(return_value='new_sync_token_456')
        
        result = mobile_service.sync_mobile_data('test_user', 'device_123', sync_data)
        
        assert 'sync_id' in result
        assert 'sync_status' in result
        assert result['sync_status'] == SyncStatus.COMPLETED.value
        assert 'sync_results' in result
        assert 'new_sync_token' in result
        assert result['new_sync_token'] == 'new_sync_token_456'
    
    def test_sync_mobile_data_with_conflicts(self, mobile_service):
        """Test mobile data synchronization with conflicts."""
        sync_data = {
            'sync_token': 'sync_token_123',
            'study_sessions': [
                {'session_id': 'session1', 'duration_minutes': 30}
            ]
        }
        
        # Mock sync with conflicts
        mobile_service._sync_study_sessions = Mock(return_value={
            'uploaded': 0, 
            'conflicts': 1,
            'conflicts': [{'type': 'study_session', 'id': 'session1'}]
        })
        mobile_service._sync_test_results = Mock(return_value={'uploaded': 0, 'conflicts': 0})
        mobile_service._sync_learning_goals = Mock(return_value={'uploaded': 0, 'conflicts': 0})
        mobile_service._get_updated_data_since_last_sync = Mock(return_value={'updated': []})
        mobile_service._generate_sync_token = Mock(return_value='new_sync_token_456')
        
        result = mobile_service.sync_mobile_data('test_user', 'device_123', sync_data)
        
        assert result['sync_status'] == SyncStatus.CONFLICT.value
        assert 'conflicts' in result
        assert len(result['conflicts']) > 0
    
    def test_send_push_notification(self, mobile_service):
        """Test push notification sending."""
        notification_data = {
            'type': NotificationType.STUDY_REMINDER.value,
            'title': 'Time to Study!',
            'message': 'Your daily study session is ready.',
            'data': {'action': 'open_study_session'},
            'priority': 'normal'
        }
        
        # Mock user devices
        mock_devices = [
            {
                'device_id': 'device1',
                'push_token': 'token1',
                'is_active': True
            },
            {
                'device_id': 'device2',
                'push_token': 'token2',
                'is_active': True
            }
        ]
        
        mobile_service._get_user_devices = Mock(return_value=mock_devices)
        mobile_service._send_to_device = Mock(return_value={'status': 'sent', 'device_id': 'device1'})
        
        result = mobile_service.send_push_notification('test_user', notification_data)
        
        assert 'notification_id' in result
        assert 'delivery_status' in result
        assert result['delivery_status'] == 'sent'
        assert 'devices_targeted' in result
        assert result['devices_targeted'] == 2
        assert 'delivery_results' in result
    
    def test_send_push_notification_no_devices(self, mobile_service):
        """Test push notification with no registered devices."""
        notification_data = {
            'type': NotificationType.STUDY_REMINDER.value,
            'title': 'Test Notification',
            'message': 'Test message'
        }
        
        mobile_service._get_user_devices = Mock(return_value=[])
        
        result = mobile_service.send_push_notification('test_user', notification_data)
        
        assert 'error' in result
        assert result['error'] == 'No registered devices found'
    
    def test_schedule_smart_notifications(self, mobile_service):
        """Test smart notification scheduling."""
        # Mock user patterns and AI insights
        mobile_service._analyze_user_study_patterns = Mock(return_value={
            'optimal_study_times': ['2024-01-15T14:00:00Z'],
            'preferred_duration': 30
        })
        
        mock_ai_insights = {
            'insights': [
                {
                    'category': 'performance',
                    'title': 'Performance Insight',
                    'impact_score': 0.8,
                    'id': 'insight1'
                }
            ]
        }
        mobile_service.ai_service.generate_automated_insights = Mock(return_value=mock_ai_insights)
        mobile_service.send_push_notification = Mock(return_value={'delivery_status': 'sent'})
        
        result = mobile_service.schedule_smart_notifications('test_user')
        
        assert 'notifications_scheduled' in result
        assert 'total_notifications' in result
        assert result['notifications_scheduled'] > 0
    
    def test_track_mobile_analytics(self, mobile_service):
        """Test mobile analytics tracking."""
        analytics_data = {
            'event_type': 'study_session_completed',
            'event_data': {
                'duration_minutes': 30,
                'topics_covered': 3,
                'completion_rate': 0.85
            },
            'session_id': 'session_123',
            'app_version': '1.2.0',
            'platform': 'ios',
            'network_type': 'wifi',
            'battery_level': 0.75,
            'memory_usage': 120.5
        }
        
        mobile_service._generate_mobile_insights = Mock(return_value=['Insight 1'])
        mobile_service._get_mobile_recommendations = Mock(return_value=['Recommendation 1'])
        
        result = mobile_service.track_mobile_analytics('test_user', 'device_123', analytics_data)
        
        assert 'tracking_status' in result
        assert result['tracking_status'] == 'success'
        assert 'event_id' in result
        assert 'insights' in result
        assert 'recommendations' in result
    
    def test_get_mobile_dashboard_data(self, mobile_service):
        """Test mobile dashboard data retrieval."""
        # Mock user and related data
        mock_user = Mock()
        mock_user.user_id = 'test_user'
        mock_user.display_name = 'Test User'
        mock_user.role = Mock()
        mock_user.role.value = 'learner'
        mock_user.organization_id = 1
        
        mock_goals = [Mock()]
        mock_goals[0].to_dict.return_value = {'goal_id': 'goal1', 'title': 'Test Goal'}
        
        mock_achievements = [Mock()]
        mock_achievements[0].to_dict.return_value = {'achievement_id': 'ach1', 'title': 'Test Achievement'}
        
        mobile_service.db.query.return_value.filter.return_value.first.return_value = mock_user
        mobile_service.db.query.return_value.filter.return_value.limit.return_value.all.return_value = mock_goals
        mobile_service.db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = mock_achievements
        
        # Mock helper methods
        mobile_service._calculate_study_streak = Mock(return_value={'current_streak': 5})
        mobile_service._get_mobile_quick_stats = Mock(return_value=[])
        mobile_service._get_next_actions = Mock(return_value=['Action 1'])
        mobile_service._get_motivational_message = Mock(return_value='Keep it up!')
        
        # Mock AI service
        mobile_service.ai_service.predict_user_success_probability = Mock(return_value={
            'success_probability': 0.85,
            'confidence_score': 0.89
        })
        
        result = mobile_service.get_mobile_dashboard_data('test_user')
        
        assert 'user_profile' in result
        assert 'active_goals' in result
        assert 'recent_achievements' in result
        assert 'study_streak' in result
        assert 'ai_recommendations' in result
        assert 'quick_stats' in result
        assert 'next_actions' in result
        assert 'motivational_message' in result
        assert 'last_updated' in result
    
    def test_get_mobile_dashboard_data_no_user(self, mobile_service):
        """Test dashboard data retrieval with no user."""
        mobile_service.db.query.return_value.filter.return_value.first.return_value = None
        
        result = mobile_service.get_mobile_dashboard_data('invalid_user')
        
        assert 'error' in result
        assert result['error'] == 'User not found'
    
    def test_generate_sync_token(self, mobile_service):
        """Test sync token generation."""
        token1 = mobile_service._generate_sync_token('user1', 'device1')
        token2 = mobile_service._generate_sync_token('user1', 'device2')
        token3 = mobile_service._generate_sync_token('user2', 'device1')
        
        # Tokens should be different for different users/devices
        assert token1 != token2
        assert token1 != token3
        assert token2 != token3
        
        # Tokens should be strings
        assert isinstance(token1, str)
        assert len(token1) > 0
    
    def test_generate_session_token(self, mobile_service):
        """Test session token generation."""
        token1 = mobile_service._generate_session_token('user1', 'device1')
        token2 = mobile_service._generate_session_token('user1', 'device2')
        
        # Tokens should be different
        assert token1 != token2
        
        # Tokens should be strings
        assert isinstance(token1, str)
        assert len(token1) > 0
    
    def test_get_offline_capabilities(self, mobile_service):
        """Test offline capabilities determination."""
        device_info = {
            'capabilities': {
                'ai_processing': True,
                'storage_mb': 1000,
                'background_processing': True
            }
        }
        
        capabilities = mobile_service._get_offline_capabilities(device_info)
        
        assert 'offline_ai' in capabilities
        assert 'offline_storage_mb' in capabilities
        assert 'offline_sync' in capabilities
        assert 'offline_study_materials' in capabilities
        assert 'offline_practice_tests' in capabilities
        assert 'offline_progress_tracking' in capabilities
        assert 'background_sync' in capabilities
        
        assert capabilities['offline_ai'] is True
        assert capabilities['offline_storage_mb'] == 1000
        assert capabilities['background_sync'] is True
    
    def test_get_mobile_permissions(self, mobile_service):
        """Test mobile permissions determination."""
        # Test regular user
        mock_user = Mock()
        mock_user.role = Mock()
        mock_user.role.value = 'learner'
        
        permissions = mobile_service._get_mobile_permissions(mock_user)
        
        assert 'view_dashboard' in permissions
        assert 'track_progress' in permissions
        assert 'take_practice_tests' in permissions
        assert 'view_study_materials' in permissions
        assert 'sync_data' in permissions
        assert 'receive_notifications' in permissions
        
        assert permissions['view_dashboard'] is True
        assert 'view_team_progress' not in permissions
        
        # Test admin user
        mock_admin = Mock()
        mock_admin.role = Mock()
        mock_admin.role.value = 'org_admin'
        
        admin_permissions = mobile_service._get_mobile_permissions(mock_admin)
        
        assert 'view_team_progress' in admin_permissions
        assert 'manage_team_goals' in admin_permissions
        assert 'view_analytics' in admin_permissions
        assert admin_permissions['view_team_progress'] is True


if __name__ == "__main__":
    pytest.main([__file__])
