"""Unit tests for organization management API endpoints"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from api.app import app
from database import get_db
from models import Base
from models.user import User
from models.certification import Organization


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_organizations.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="module")
def setup_database():
    """Create test database tables"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client(setup_database):
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def db_session():
    """Create database session for testing"""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def authenticated_user(client):
    """Create and authenticate a test user"""
    # Register user
    registration_data = {
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "name": "Organization Test User"
    }
    
    response = client.post("/api/v1/auth/register", json=registration_data)
    assert response.status_code == 201
    
    auth_data = response.json()
    return {
        "user": auth_data["user"],
        "token": auth_data["access_token"],
        "headers": {"Authorization": f"Bearer {auth_data['access_token']}"}
    }


@pytest.fixture
def sample_organizations(db_session):
    """Create sample organizations for testing"""
    organizations = [
        Organization(
            name="CompTIA",
            description="Computing Technology Industry Association",
            website="https://comptia.org",
            category="Technology",
            contact_email="<EMAIL>",
            is_verified=True
        ),
        Organization(
            name="(ISC)²",
            description="International Information System Security Certification Consortium",
            website="https://isc2.org",
            category="Security",
            contact_email="<EMAIL>",
            is_verified=True
        ),
        Organization(
            name="Test Training Corp",
            description="Test training organization",
            website="https://testtraining.com",
            category="Training Provider",
            contact_email="<EMAIL>",
            is_verified=False
        )
    ]
    
    db_session.add_all(organizations)
    db_session.commit()
    
    return organizations


class TestOrganizationListing:
    """Test organization listing functionality"""
    
    def test_list_organizations_basic(self, client, sample_organizations):
        """Test basic organization listing"""
        response = client.get("/api/v1/organizations/")
        
        assert response.status_code == 200
        data = response.json()
        assert "organizations" in data
        assert len(data["organizations"]) == 3
        assert "total_count" in data
        assert data["total_count"] == 3
    
    def test_list_organizations_with_search(self, client, sample_organizations):
        """Test organization listing with search"""
        response = client.get("/api/v1/organizations/?search=CompTIA")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["organizations"]) == 1
        assert data["organizations"][0]["name"] == "CompTIA"
    
    def test_list_organizations_with_category_filter(self, client, sample_organizations):
        """Test organization listing with category filter"""
        response = client.get("/api/v1/organizations/?category=Security")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["organizations"]) == 1
        assert data["organizations"][0]["name"] == "(ISC)²"
    
    def test_list_organizations_with_pagination(self, client, sample_organizations):
        """Test organization listing with pagination"""
        response = client.get("/api/v1/organizations/?page=1&page_size=2")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["organizations"]) == 2
        assert data["page"] == 1
        assert data["page_size"] == 2
        assert data["total_pages"] == 2


class TestOrganizationDetails:
    """Test organization detail retrieval"""
    
    def test_get_organization_details_success(self, client, sample_organizations):
        """Test successful organization detail retrieval"""
        org_id = sample_organizations[0].id
        response = client.get(f"/api/v1/organizations/{org_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == org_id
        assert data["name"] == "CompTIA"
        assert "description" in data
        assert "website" in data
    
    def test_get_organization_details_not_found(self, client):
        """Test organization detail retrieval for non-existent organization"""
        response = client.get("/api/v1/organizations/99999")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()


class TestOrganizationCreation:
    """Test organization creation functionality"""
    
    def test_create_organization_success(self, client, authenticated_user):
        """Test successful organization creation"""
        org_data = {
            "name": "New Test Organization",
            "description": "A new test organization",
            "website": "https://newtest.org",
            "category": "Education",
            "contact_email": "<EMAIL>"
        }
        
        response = client.post(
            "/api/v1/organizations/",
            json=org_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == org_data["name"]
        assert data["description"] == org_data["description"]
        assert data["is_verified"] is False  # New organizations are not verified
        assert data["created_by"] == authenticated_user["user"]["user_id"]
    
    def test_create_organization_without_auth(self, client):
        """Test organization creation without authentication"""
        org_data = {
            "name": "Unauthorized Organization",
            "description": "Should not be created"
        }
        
        response = client.post("/api/v1/organizations/", json=org_data)
        assert response.status_code == 403
    
    def test_create_organization_duplicate_name(self, client, authenticated_user, sample_organizations):
        """Test organization creation with duplicate name"""
        org_data = {
            "name": "CompTIA",  # Already exists
            "description": "Duplicate organization"
        }
        
        response = client.post(
            "/api/v1/organizations/",
            json=org_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 409
        assert "already exists" in response.json()["detail"].lower()
    
    def test_create_organization_invalid_data(self, client, authenticated_user):
        """Test organization creation with invalid data"""
        org_data = {
            "name": "",  # Empty name
            "description": "Invalid organization"
        }
        
        response = client.post(
            "/api/v1/organizations/",
            json=org_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 422  # Validation error


class TestOrganizationUpdate:
    """Test organization update functionality"""
    
    def test_update_organization_success(self, client, authenticated_user, sample_organizations):
        """Test successful organization update"""
        org_id = sample_organizations[0].id
        update_data = {
            "description": "Updated description",
            "website": "https://updated.comptia.org"
        }
        
        response = client.put(
            f"/api/v1/organizations/{org_id}",
            json=update_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["description"] == update_data["description"]
        assert data["website"] == update_data["website"]
    
    def test_update_organization_not_found(self, client, authenticated_user):
        """Test organization update for non-existent organization"""
        update_data = {
            "description": "Updated description"
        }
        
        response = client.put(
            "/api/v1/organizations/99999",
            json=update_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 404
    
    def test_update_organization_without_auth(self, client, sample_organizations):
        """Test organization update without authentication"""
        org_id = sample_organizations[0].id
        update_data = {
            "description": "Unauthorized update"
        }
        
        response = client.put(f"/api/v1/organizations/{org_id}", json=update_data)
        assert response.status_code == 403


class TestOrganizationTeamManagement:
    """Test organization team management functionality"""
    
    def test_get_organization_users_success(self, client, authenticated_user, sample_organizations):
        """Test successful organization users retrieval"""
        org_id = sample_organizations[0].id
        
        response = client.get(
            f"/api/v1/organizations/{org_id}/users",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_organization_users_not_found(self, client, authenticated_user):
        """Test organization users retrieval for non-existent organization"""
        response = client.get(
            "/api/v1/organizations/99999/users",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 404
    
    def test_invite_user_to_organization_success(self, client, authenticated_user, sample_organizations):
        """Test successful user invitation to organization"""
        org_id = sample_organizations[0].id
        invite_data = {
            "email": "<EMAIL>",
            "role": "member",
            "message": "Welcome to our organization!"
        }
        
        response = client.post(
            f"/api/v1/organizations/{org_id}/invite",
            json=invite_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 202
        data = response.json()
        assert data["invited_email"] == invite_data["email"]
        assert data["role"] == invite_data["role"]
        assert data["organization_id"] == org_id
    
    def test_invite_user_organization_not_found(self, client, authenticated_user):
        """Test user invitation to non-existent organization"""
        invite_data = {
            "email": "<EMAIL>",
            "role": "member"
        }
        
        response = client.post(
            "/api/v1/organizations/99999/invite",
            json=invite_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 404
    
    def test_invite_user_without_auth(self, client, sample_organizations):
        """Test user invitation without authentication"""
        org_id = sample_organizations[0].id
        invite_data = {
            "email": "<EMAIL>",
            "role": "member"
        }
        
        response = client.post(f"/api/v1/organizations/{org_id}/invite", json=invite_data)
        assert response.status_code == 403


class TestOrganizationCategories:
    """Test organization categories functionality"""
    
    def test_get_organization_categories_success(self, client, sample_organizations):
        """Test successful organization categories retrieval"""
        response = client.get("/api/v1/organizations/categories")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert "Technology" in data
        assert "Security" in data
        assert "Training Provider" in data
    
    def test_get_organization_categories_empty_db(self, client):
        """Test organization categories retrieval with empty database"""
        response = client.get("/api/v1/organizations/categories")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        # Should return default categories when none exist
        assert len(data) > 0
