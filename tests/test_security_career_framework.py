"""Tests for Security Career Framework functionality.

This module provides comprehensive tests for the security career framework,
including job types, career paths, skill matrices, and market data based on
<PERSON>'s 8 security areas and industry standards.
"""

import pytest
import json
from datetime import datetime
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from models.security_career_framework import (
    SecurityJobType, SecurityCareerPath, SecuritySkillMatrix, SecurityMarketData,
    SecurityArea, SeniorityLevel, JobFamily
)
from schemas.security_career_framework import (
    SecurityJobTypeCreate, SecurityJobTypeResponse,
    SecurityCareerPathCreate, SecurityCareerPathResponse,
    SecuritySkillMatrixCreate, SecuritySkillMatrixResponse,
    CareerRecommendationRequest, CareerRecommendationResponse,
    SecurityAreaEnum, SeniorityLevelEnum, DemandLevelEnum
)


class TestSecurityCareerFramework:
    """Test cases for the Security Career Framework."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def sample_job_type_data(self):
        """Create sample job type data."""
        return {
            'title': 'Senior Security Engineer',
            'security_area': SecurityAreaEnum.SECURITY_ENGINEERING,
            'job_family': 'security_engineer',
            'seniority_level': SeniorityLevelEnum.SENIOR,
            'description': 'Senior security engineer responsible for designing and implementing security controls.',
            'responsibilities': [
                'Design security architecture',
                'Implement security controls',
                'Conduct security assessments',
                'Mentor junior engineers'
            ],
            'required_skills': [
                'Network security',
                'Security architecture',
                'Risk assessment',
                'Security frameworks'
            ],
            'preferred_skills': [
                'Cloud security',
                'DevSecOps',
                'Threat modeling'
            ],
            'min_years_experience': 5,
            'max_years_experience': 10,
            'education_requirements': [
                "Bachelor's degree in Computer Science or related field"
            ],
            'required_certifications': [
                'CISSP',
                'Security+'
            ],
            'preferred_certifications': [
                'CISSP Concentrations',
                'SABSA'
            ],
            'salary_min': 120000,
            'salary_max': 160000,
            'salary_currency': 'USD',
            'career_progression_from': [
                'security_engineer',
                'systems_security'
            ],
            'career_progression_to': [
                'security_architect',
                'principal_engineer'
            ],
            'demand_level': DemandLevelEnum.HIGH,
            'remote_friendly': True
        }
    
    @pytest.fixture
    def sample_career_path_data(self):
        """Create sample career path data."""
        return {
            'name': 'Security Engineering Career Path',
            'description': 'Career progression from junior security engineer to security architect',
            'security_area': SecurityAreaEnum.SECURITY_ENGINEERING,
            'entry_job_types': ['security_engineer'],
            'progression_stages': [
                {
                    'level': SeniorityLevelEnum.BEGINNER,
                    'duration_years': 2,
                    'key_skills': ['Network security', 'Operating systems'],
                    'certifications': ['Security+', 'Network+']
                },
                {
                    'level': SeniorityLevelEnum.INTERMEDIATE,
                    'duration_years': 3,
                    'key_skills': ['Security architecture', 'Risk assessment'],
                    'certifications': ['GSEC', 'CySA+']
                },
                {
                    'level': SeniorityLevelEnum.ADVANCED,
                    'duration_years': 3,
                    'key_skills': ['Enterprise security', 'Security frameworks'],
                    'certifications': ['CISSP', 'CISM']
                },
                {
                    'level': SeniorityLevelEnum.EXPERT,
                    'duration_years': 4,
                    'key_skills': ['Security strategy', 'Team leadership'],
                    'certifications': ['SABSA', 'CISSP Concentrations']
                }
            ],
            'terminal_job_types': ['security_architect', 'principal_engineer'],
            'typical_duration_years': 12,
            'required_certifications': ['Security+', 'CISSP', 'SABSA'],
            'recommended_education': [
                "Bachelor's degree in Computer Science",
                "Master's degree in Cybersecurity (preferred)"
            ],
            'success_rate': 0.75,
            'average_salary_growth': 120.0
        }
    
    @pytest.fixture
    def sample_skill_matrix_data(self):
        """Create sample skill matrix data."""
        return {
            'security_area': SecurityAreaEnum.SECURITY_ENGINEERING,
            'job_family': 'security_engineer',
            'seniority_level': SeniorityLevelEnum.SENIOR,
            'core_skills': [
                'Network security',
                'Security architecture',
                'Risk assessment',
                'Security frameworks'
            ],
            'advanced_skills': [
                'Cloud security',
                'DevSecOps',
                'Threat modeling',
                'Security automation'
            ],
            'leadership_skills': [
                'Team leadership',
                'Project management',
                'Mentoring',
                'Communication'
            ],
            'business_skills': [
                'Business analysis',
                'Risk management',
                'Vendor management',
                'Budget planning'
            ],
            'entry_certifications': [
                'Security+',
                'Network+'
            ],
            'intermediate_certifications': [
                'GSEC',
                'CySA+'
            ],
            'advanced_certifications': [
                'CISSP',
                'CISM'
            ],
            'expert_certifications': [
                'SABSA',
                'CISSP Concentrations'
            ],
            'required_tools': [
                'Firewalls',
                'SIEM',
                'Vulnerability scanners',
                'Network analyzers'
            ],
            'preferred_tools': [
                'Cloud security tools',
                'DevSecOps tools',
                'Threat intelligence platforms',
                'Security orchestration tools'
            ]
        }
    
    def test_security_job_type_creation(self, sample_job_type_data):
        """Test creating a security job type."""
        job_type_create = SecurityJobTypeCreate(**sample_job_type_data)
        
        assert job_type_create.title == 'Senior Security Engineer'
        assert job_type_create.security_area == SecurityAreaEnum.SECURITY_ENGINEERING
        assert job_type_create.seniority_level == SeniorityLevelEnum.SENIOR
        assert job_type_create.min_years_experience == 5
        assert job_type_create.max_years_experience == 10
        assert job_type_create.salary_min == 120000
        assert job_type_create.salary_max == 160000
        assert job_type_create.demand_level == DemandLevelEnum.HIGH
        assert job_type_create.remote_friendly is True
        assert len(job_type_create.responsibilities) == 4
        assert len(job_type_create.required_skills) == 4
        assert len(job_type_create.required_certifications) == 2
    
    def test_security_job_type_validation(self):
        """Test security job type validation."""
        # Test invalid experience range
        with pytest.raises(ValueError, match="max_years_experience must be greater than min_years_experience"):
            SecurityJobTypeCreate(
                title="Test Job",
                security_area=SecurityAreaEnum.SECURITY_ENGINEERING,
                job_family="security_engineer",
                seniority_level=SeniorityLevelEnum.SENIOR,
                min_years_experience=10,
                max_years_experience=5  # Invalid: max < min
            )
        
        # Test invalid salary range
        with pytest.raises(ValueError, match="salary_max must be greater than salary_min"):
            SecurityJobTypeCreate(
                title="Test Job",
                security_area=SecurityAreaEnum.SECURITY_ENGINEERING,
                job_family="security_engineer",
                seniority_level=SeniorityLevelEnum.SENIOR,
                salary_min=100000,
                salary_max=80000  # Invalid: max < min
            )
    
    def test_security_career_path_creation(self, sample_career_path_data):
        """Test creating a security career path."""
        career_path_create = SecurityCareerPathCreate(**sample_career_path_data)
        
        assert career_path_create.name == 'Security Engineering Career Path'
        assert career_path_create.security_area == SecurityAreaEnum.SECURITY_ENGINEERING
        assert career_path_create.typical_duration_years == 12
        assert career_path_create.success_rate == 0.75
        assert career_path_create.average_salary_growth == 120.0
        assert len(career_path_create.progression_stages) == 4
        assert len(career_path_create.required_certifications) == 3
    
    def test_security_skill_matrix_creation(self, sample_skill_matrix_data):
        """Test creating a security skill matrix."""
        skill_matrix_create = SecuritySkillMatrixCreate(**sample_skill_matrix_data)
        
        assert skill_matrix_create.security_area == SecurityAreaEnum.SECURITY_ENGINEERING
        assert skill_matrix_create.job_family == 'security_engineer'
        assert skill_matrix_create.seniority_level == SeniorityLevelEnum.SENIOR
        assert len(skill_matrix_create.core_skills) == 4
        assert len(skill_matrix_create.advanced_skills) == 4
        assert len(skill_matrix_create.leadership_skills) == 4
        assert len(skill_matrix_create.business_skills) == 4
        assert len(skill_matrix_create.entry_certifications) == 2
        assert len(skill_matrix_create.intermediate_certifications) == 2
        assert len(skill_matrix_create.advanced_certifications) == 2
        assert len(skill_matrix_create.expert_certifications) == 2
    
    def test_career_recommendation_request(self):
        """Test career recommendation request."""
        request = CareerRecommendationRequest(
            current_role="Security Analyst",
            security_area=SecurityAreaEnum.DEFENSIVE_SECURITY,
            target_seniority=SeniorityLevelEnum.SENIOR,
            years_experience=5,
            current_certifications=['Security+', 'CySA+'],
            preferred_skills=['SIEM', 'Incident Response', 'Threat Hunting'],
            salary_expectations={'min': 100000, 'max': 140000, 'currency': 'USD'},
            remote_work_preference=True
        )
        
        assert request.current_role == "Security Analyst"
        assert request.security_area == SecurityAreaEnum.DEFENSIVE_SECURITY
        assert request.target_seniority == SeniorityLevelEnum.SENIOR
        assert request.years_experience == 5
        assert len(request.current_certifications) == 2
        assert len(request.preferred_skills) == 3
        assert request.remote_work_preference is True
    
    def test_security_area_enum_values(self):
        """Test security area enum values match Paul Jerimy's 8 areas."""
        expected_areas = [
            "Security Architecture and Engineering",
            "Security Operations",
            "Security and Risk Management",
            "Communication and Network Security",
            "Identity and Access Management",
            "Asset Security",
            "Security Assessment and Testing",
            "Software Security"
        ]
        
        actual_areas = [area.value for area in SecurityAreaEnum]
        
        assert len(actual_areas) == 8
        for area in expected_areas:
            assert area in actual_areas
    
    def test_seniority_level_progression(self):
        """Test seniority level progression order."""
        expected_progression = [
            "beginner",
            "intermediate", 
            "advanced",
            "expert",
            "senior",
            "principal",
            "architect",
            "executive"
        ]
        
        actual_levels = [level.value for level in SeniorityLevelEnum]
        
        assert len(actual_levels) == 8
        for level in expected_progression:
            assert level in actual_levels
    
    def test_job_type_to_dict_conversion(self, sample_job_type_data):
        """Test job type to dictionary conversion."""
        # Create a mock SecurityJobType object
        job_type = SecurityJobType(
            id=1,
            title=sample_job_type_data['title'],
            security_area=sample_job_type_data['security_area'].value,
            job_family=sample_job_type_data['job_family'],
            seniority_level=sample_job_type_data['seniority_level'].value,
            description=sample_job_type_data['description'],
            responsibilities=sample_job_type_data['responsibilities'],
            required_skills=sample_job_type_data['required_skills'],
            preferred_skills=sample_job_type_data['preferred_skills'],
            min_years_experience=sample_job_type_data['min_years_experience'],
            max_years_experience=sample_job_type_data['max_years_experience'],
            education_requirements=sample_job_type_data['education_requirements'],
            required_certifications=sample_job_type_data['required_certifications'],
            preferred_certifications=sample_job_type_data['preferred_certifications'],
            salary_min=sample_job_type_data['salary_min'],
            salary_max=sample_job_type_data['salary_max'],
            salary_currency=sample_job_type_data['salary_currency'],
            career_progression_from=sample_job_type_data['career_progression_from'],
            career_progression_to=sample_job_type_data['career_progression_to'],
            demand_level=sample_job_type_data['demand_level'].value,
            remote_friendly=sample_job_type_data['remote_friendly'],
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        job_dict = job_type.to_dict()
        
        assert job_dict['id'] == 1
        assert job_dict['title'] == 'Senior Security Engineer'
        assert job_dict['security_area'] == SecurityAreaEnum.SECURITY_ENGINEERING.value
        assert job_dict['seniority_level'] == SeniorityLevelEnum.SENIOR.value
        assert job_dict['min_years_experience'] == 5
        assert job_dict['max_years_experience'] == 10
        assert job_dict['salary_range']['min'] == 120000
        assert job_dict['salary_range']['max'] == 160000
        assert job_dict['salary_range']['currency'] == 'USD'
        assert job_dict['demand_level'] == DemandLevelEnum.HIGH.value
        assert job_dict['remote_friendly'] is True
        assert len(job_dict['responsibilities']) == 4
        assert len(job_dict['required_skills']) == 4
        assert len(job_dict['career_progression']['from']) == 2
        assert len(job_dict['career_progression']['to']) == 2
    
    def test_skill_matrix_to_dict_conversion(self, sample_skill_matrix_data):
        """Test skill matrix to dictionary conversion."""
        # Create a mock SecuritySkillMatrix object
        skill_matrix = SecuritySkillMatrix(
            id=1,
            security_area=sample_skill_matrix_data['security_area'].value,
            job_family=sample_skill_matrix_data['job_family'],
            seniority_level=sample_skill_matrix_data['seniority_level'].value,
            core_skills=sample_skill_matrix_data['core_skills'],
            advanced_skills=sample_skill_matrix_data['advanced_skills'],
            leadership_skills=sample_skill_matrix_data['leadership_skills'],
            business_skills=sample_skill_matrix_data['business_skills'],
            entry_certifications=sample_skill_matrix_data['entry_certifications'],
            intermediate_certifications=sample_skill_matrix_data['intermediate_certifications'],
            advanced_certifications=sample_skill_matrix_data['advanced_certifications'],
            expert_certifications=sample_skill_matrix_data['expert_certifications'],
            required_tools=sample_skill_matrix_data['required_tools'],
            preferred_tools=sample_skill_matrix_data['preferred_tools'],
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        matrix_dict = skill_matrix.to_dict()
        
        assert matrix_dict['id'] == 1
        assert matrix_dict['security_area'] == SecurityAreaEnum.SECURITY_ENGINEERING.value
        assert matrix_dict['job_family'] == 'security_engineer'
        assert matrix_dict['seniority_level'] == SeniorityLevelEnum.SENIOR.value
        assert len(matrix_dict['skills']['core']) == 4
        assert len(matrix_dict['skills']['advanced']) == 4
        assert len(matrix_dict['skills']['leadership']) == 4
        assert len(matrix_dict['skills']['business']) == 4
        assert len(matrix_dict['certifications']['entry']) == 2
        assert len(matrix_dict['certifications']['intermediate']) == 2
        assert len(matrix_dict['certifications']['advanced']) == 2
        assert len(matrix_dict['certifications']['expert']) == 2
        assert len(matrix_dict['tools']['required']) == 4
        assert len(matrix_dict['tools']['preferred']) == 4
    
    def test_demand_level_enum(self):
        """Test demand level enumeration."""
        expected_levels = ['low', 'medium', 'high', 'critical']
        actual_levels = [level.value for level in DemandLevelEnum]
        
        assert len(actual_levels) == 4
        for level in expected_levels:
            assert level in actual_levels
    
    def test_job_family_mapping(self):
        """Test job family mapping to security areas."""
        from models.security_career_framework import get_job_types_by_area
        
        # Test Security Engineering area
        security_eng_jobs = get_job_types_by_area(SecurityArea.SECURITY_ENGINEERING)
        expected_jobs = ['security_architect', 'security_engineer', 'systems_security']
        
        assert len(security_eng_jobs) == 3
        for job in expected_jobs:
            assert job in security_eng_jobs
        
        # Test Defensive Security area
        defensive_jobs = get_job_types_by_area(SecurityArea.DEFENSIVE_SECURITY)
        expected_jobs = ['soc_analyst', 'incident_responder', 'threat_hunter', 'security_operations']
        
        assert len(defensive_jobs) == 4
        for job in expected_jobs:
            assert job in defensive_jobs
    
    def test_years_experience_mapping(self):
        """Test years of experience mapping for seniority levels."""
        from models.security_career_framework import get_years_experience_for_level
        
        # Test beginner level
        beginner_years = get_years_experience_for_level(SeniorityLevel.BEGINNER.value)
        assert beginner_years == (0, 1)
        
        # Test senior level
        senior_years = get_years_experience_for_level(SeniorityLevel.SENIOR.value)
        assert senior_years == (7, 10)
        
        # Test executive level
        executive_years = get_years_experience_for_level(SeniorityLevel.EXECUTIVE.value)
        assert executive_years == (15, 25)
    
    def test_seniority_progression_order(self):
        """Test seniority progression order."""
        from models.security_career_framework import get_seniority_progression
        
        progression = get_seniority_progression()
        expected_order = [
            'beginner', 'intermediate', 'advanced', 'expert',
            'senior', 'principal', 'architect', 'executive'
        ]
        
        assert progression == expected_order
        assert len(progression) == 8


if __name__ == "__main__":
    pytest.main([__file__])
