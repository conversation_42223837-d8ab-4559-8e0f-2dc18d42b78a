"""Comprehensive performance and load testing for Agent 3 Enterprise Analytics Engine.

This module provides performance testing for compliance automation, data intelligence,
authentication, and enterprise workflows under various load conditions.
"""

import pytest
import time
import threading
import concurrent.futures
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
import statistics

from models.compliance import ComplianceRequirement, ComplianceFramework, RiskLevel
from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole
from services.compliance_service import ComplianceService
from services.data_intelligence_service import DataIntelligenceService
from services.enterprise_auth_service import EnterpriseAuthService


class TestCompliancePerformance:
    """Test compliance service performance under load."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def compliance_service(self, mock_db):
        return ComplianceService(mock_db)
    
    def test_create_multiple_requirements_performance(self, compliance_service, mock_db):
        """Test performance of creating multiple compliance requirements."""
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Create 100 requirements and measure time
        start_time = time.time()
        
        with patch.object(compliance_service, 'audit_service'):
            for i in range(100):
                req_data = {
                    'organization_id': 1,
                    'framework': ComplianceFramework.GDPR,
                    'requirement_id': f'GDPR-Test-{i:03d}',
                    'title': f'Test Requirement {i}',
                    'description': f'Test requirement description {i}',
                    'risk_level': RiskLevel.MEDIUM
                }
                compliance_service.create_compliance_requirement(req_data)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should complete within reasonable time (5 seconds for 100 items)
        assert total_time < 5.0
        
        # Calculate average time per requirement
        avg_time = total_time / 100
        assert avg_time < 0.05  # Less than 50ms per requirement
        
        print(f"Created 100 requirements in {total_time:.2f}s (avg: {avg_time*1000:.1f}ms each)")
    
    def test_bulk_assessment_performance(self, compliance_service, mock_db):
        """Test performance of bulk compliance assessments."""
        mock_requirement = Mock()
        mock_requirement.check_frequency = 'monthly'
        
        mock_db.query.return_value.get.return_value = mock_requirement
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Perform 50 assessments and measure time
        start_time = time.time()
        
        with patch.object(compliance_service, 'audit_service'):
            for i in range(50):
                assessment_data = {
                    'assessment_date': datetime.now(),
                    'assessor_id': 'user_123',
                    'organization_id': 1,
                    'status': 'compliant',
                    'score': 90.0 + (i % 10),
                    'findings': f'Assessment findings {i}'
                }
                compliance_service.assess_compliance_requirement(i + 1, assessment_data)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should complete within reasonable time
        assert total_time < 3.0
        
        avg_time = total_time / 50
        assert avg_time < 0.06  # Less than 60ms per assessment
        
        print(f"Completed 50 assessments in {total_time:.2f}s (avg: {avg_time*1000:.1f}ms each)")
    
    def test_gdpr_report_generation_performance(self, compliance_service, mock_db):
        """Test GDPR report generation performance with large dataset."""
        # Mock large number of requirements
        mock_requirements = []
        for i in range(200):
            req = Mock()
            req.requirement_id = f'GDPR-{i:03d}'
            req.title = f'GDPR Requirement {i}'
            req.status = 'compliant' if i % 3 == 0 else 'non_compliant'
            req.risk_level = 'high' if i % 5 == 0 else 'medium'
            req.last_assessed = datetime.now()
            mock_requirements.append(req)
        
        compliance_service.get_organization_requirements = Mock(return_value=mock_requirements)
        
        # Mock database queries
        mock_db.query.return_value.filter.return_value.all.return_value = []
        mock_db.query.return_value.filter.return_value.count.return_value = 1000
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Generate report and measure time
        start_time = time.time()
        
        with patch.object(compliance_service, 'audit_service'):
            report = compliance_service.generate_gdpr_report(
                1, 
                datetime.now() - timedelta(days=30), 
                datetime.now()
            )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should complete within reasonable time even with 200 requirements
        assert total_time < 2.0
        
        print(f"Generated GDPR report with 200 requirements in {total_time:.2f}s")
    
    def test_concurrent_compliance_operations(self, compliance_service, mock_db):
        """Test concurrent compliance operations performance."""
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        def create_requirement(index):
            req_data = {
                'organization_id': 1,
                'framework': ComplianceFramework.GDPR,
                'requirement_id': f'GDPR-Concurrent-{index:03d}',
                'title': f'Concurrent Requirement {index}',
                'risk_level': RiskLevel.MEDIUM
            }
            
            with patch.object(compliance_service, 'audit_service'):
                start = time.time()
                compliance_service.create_compliance_requirement(req_data)
                return time.time() - start
        
        # Run 20 concurrent operations
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(create_requirement, i) for i in range(20)]
            operation_times = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Concurrent operations should complete faster than sequential
        assert total_time < 2.0
        
        # Check individual operation times
        avg_operation_time = statistics.mean(operation_times)
        max_operation_time = max(operation_times)
        
        assert avg_operation_time < 0.1  # Average under 100ms
        assert max_operation_time < 0.2  # Max under 200ms
        
        print(f"20 concurrent operations completed in {total_time:.2f}s")
        print(f"Avg operation time: {avg_operation_time*1000:.1f}ms, Max: {max_operation_time*1000:.1f}ms")


class TestDataIntelligencePerformance:
    """Test data intelligence service performance under load."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def intelligence_service(self, mock_db):
        return DataIntelligenceService(mock_db)
    
    def test_salary_intelligence_large_dataset(self, intelligence_service, mock_db):
        """Test salary intelligence performance with large user dataset."""
        # Create large mock user dataset
        mock_users = []
        for i in range(1000):
            user = Mock()
            user.user_id = f'user_{i:04d}'
            user.role = ['Security Engineer', 'Security Analyst', 'CISO', 'Security Architect'][i % 4]
            user.seniority_level = ['junior', 'mid', 'senior'][i % 3]
            user.location = ['San Francisco', 'New York', 'Austin', 'Denver'][i % 4]
            user.salary_range = f'{60000 + (i % 10) * 10000}-{80000 + (i % 10) * 10000}'
            user.certifications_completed = ['CISSP', 'Security+', 'CEH'][:(i % 3) + 1]
            user.hire_date = datetime.now() - timedelta(days=i * 30)
            mock_users.append(user)
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_users
        
        # Generate salary intelligence report and measure time
        start_time = time.time()
        
        with patch.object(intelligence_service, 'audit_service'):
            report = intelligence_service.generate_salary_intelligence()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should handle 1000 users within reasonable time
        assert total_time < 5.0
        
        # Verify report was generated
        assert report['sample_size'] == 1000
        assert 'salary_statistics' in report
        
        print(f"Processed salary intelligence for 1000 users in {total_time:.2f}s")
    
    def test_skills_gap_analysis_performance(self, intelligence_service, mock_db):
        """Test skills gap analysis performance."""
        # Mock certification data
        mock_cert_data = []
        for i in range(100):
            cert = Mock()
            cert.name = f'Certification {i}'
            cert.study_sessions = 50 + (i % 100)
            cert.practice_tests = 25 + (i % 50)
            cert.completions = 5 + (i % 20)
            mock_cert_data.append(cert)
        
        mock_db.query.return_value.outerjoin.return_value.outerjoin.return_value.outerjoin.return_value.group_by.return_value.all.return_value = mock_cert_data
        
        # Perform skills gap analysis and measure time
        start_time = time.time()
        
        report = intelligence_service.analyze_skills_gap(
            industry="Technology",
            location="San Francisco",
            organization_id=1
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should complete within reasonable time
        assert total_time < 3.0
        
        # Verify report structure
        assert 'identified_gaps' in report
        assert 'priority_certifications' in report
        
        print(f"Completed skills gap analysis with 100 certifications in {total_time:.2f}s")
    
    def test_concurrent_intelligence_operations(self, intelligence_service, mock_db):
        """Test concurrent data intelligence operations."""
        mock_users = [Mock() for _ in range(100)]
        for i, user in enumerate(mock_users):
            user.user_id = f'user_{i}'
            user.role = 'Security Engineer'
            user.salary_range = f'{70000 + i * 1000}-{90000 + i * 1000}'
            user.certifications_completed = ['CISSP']
            user.location = 'San Francisco'
            user.seniority_level = 'mid'
            user.hire_date = datetime.now()
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_users
        
        def generate_salary_report(filters):
            with patch.object(intelligence_service, 'audit_service'):
                start = time.time()
                intelligence_service.generate_salary_intelligence(filters)
                return time.time() - start
        
        # Run concurrent salary intelligence operations
        start_time = time.time()
        
        filter_sets = [
            {'location': 'San Francisco'},
            {'role': 'Security Engineer'},
            {'seniority_level': 'mid'},
            {},  # No filters
            {'location': 'New York'}
        ]
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(generate_salary_report, filters) for filters in filter_sets]
            operation_times = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Concurrent operations should complete efficiently
        assert total_time < 3.0
        
        avg_time = statistics.mean(operation_times)
        assert avg_time < 1.0
        
        print(f"5 concurrent salary intelligence operations completed in {total_time:.2f}s")


class TestAuthenticationPerformance:
    """Test authentication service performance under load."""
    
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def auth_service(self, mock_db):
        return EnterpriseAuthService(mock_db)
    
    def test_token_generation_performance(self, auth_service):
        """Test JWT token generation performance."""
        user = Mock()
        user.user_id = 'test_user'
        user.email = '<EMAIL>'
        user.role = UserRole.MANAGER
        user.organization_id = 1
        
        # Generate 1000 tokens and measure time
        start_time = time.time()
        
        tokens = []
        for i in range(1000):
            token = auth_service._generate_access_token(user)
            tokens.append(token)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should generate 1000 tokens quickly
        assert total_time < 2.0
        
        # Verify all tokens are unique and valid
        assert len(set(tokens)) == 1000  # All unique
        
        avg_time = total_time / 1000
        print(f"Generated 1000 JWT tokens in {total_time:.2f}s (avg: {avg_time*1000:.2f}ms each)")
    
    def test_token_verification_performance(self, auth_service, mock_db):
        """Test JWT token verification performance."""
        user = Mock()
        user.user_id = 'test_user'
        user.email = '<EMAIL>'
        user.role = UserRole.MANAGER
        user.organization_id = 1
        user.is_active = True
        
        # Generate token for verification
        token = auth_service._generate_access_token(user)
        
        # Mock user lookup
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        # Verify token 500 times and measure time
        start_time = time.time()
        
        for i in range(500):
            payload = auth_service.verify_access_token(token)
            assert payload['user_id'] == 'test_user'
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should verify 500 tokens quickly
        assert total_time < 1.0
        
        avg_time = total_time / 500
        print(f"Verified 500 JWT tokens in {total_time:.2f}s (avg: {avg_time*1000:.2f}ms each)")
    
    def test_permission_check_performance(self, auth_service, mock_db):
        """Test permission checking performance."""
        user = Mock()
        user.user_id = 'test_user'
        user.role = UserRole.MANAGER
        user.organization_id = 1
        user.is_active = True
        user.permissions = None
        
        mock_db.query.return_value.filter.return_value.first.return_value = user
        
        from services.enterprise_auth_service import Permission
        
        # Check 1000 permissions and measure time
        start_time = time.time()
        
        permissions_to_check = [
            Permission.ORG_VIEW,
            Permission.USER_VIEW,
            Permission.USER_EDIT,
            Permission.DEPT_VIEW,
            Permission.BUDGET_VIEW
        ]
        
        for i in range(1000):
            permission = permissions_to_check[i % len(permissions_to_check)]
            result = auth_service.check_permission(user.user_id, permission, organization_id=1)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should check 1000 permissions quickly
        assert total_time < 1.0
        
        avg_time = total_time / 1000
        print(f"Checked 1000 permissions in {total_time:.2f}s (avg: {avg_time*1000:.2f}ms each)")
    
    def test_concurrent_authentication(self, auth_service, mock_db):
        """Test concurrent authentication operations."""
        user = Mock()
        user.user_id = 'test_user'
        user.email = '<EMAIL>'
        user.role = UserRole.MANAGER
        user.organization_id = 1
        user.is_active = True
        user.last_login = None
        user.organization = Mock()
        
        mock_db.query.return_value.filter.return_value.first.return_value = user
        mock_db.commit.return_value = None
        
        def authenticate_user(user_id):
            with patch.object(auth_service, '_verify_password', return_value=True):
                with patch.object(auth_service, 'audit_service'):
                    start = time.time()
                    result = auth_service.authenticate_user(
                        email=f'user{user_id}@example.com',
                        password='password'
                    )
                    return time.time() - start, result
        
        # Run 20 concurrent authentications
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(authenticate_user, i) for i in range(20)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Extract operation times and verify results
        operation_times = [result[0] for result in results]
        auth_results = [result[1] for result in results]
        
        # All authentications should succeed
        assert len(auth_results) == 20
        for auth_result in auth_results:
            assert 'access_token' in auth_result
        
        # Should complete efficiently
        assert total_time < 2.0
        
        avg_time = statistics.mean(operation_times)
        max_time = max(operation_times)
        
        print(f"20 concurrent authentications completed in {total_time:.2f}s")
        print(f"Avg auth time: {avg_time*1000:.1f}ms, Max: {max_time*1000:.1f}ms")


class TestMemoryUsagePerformance:
    """Test memory usage and efficiency."""
    
    def test_large_dataset_memory_efficiency(self):
        """Test memory efficiency with large datasets."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create large dataset simulation
        large_dataset = []
        for i in range(10000):
            item = {
                'id': i,
                'data': f'Large data item {i}' * 10,
                'timestamp': datetime.now(),
                'metadata': {'key': f'value_{i}', 'index': i}
            }
            large_dataset.append(item)
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Clean up
        del large_dataset
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_increase = peak_memory - initial_memory
        memory_cleanup = peak_memory - final_memory
        
        print(f"Memory usage: Initial: {initial_memory:.1f}MB, Peak: {peak_memory:.1f}MB, Final: {final_memory:.1f}MB")
        print(f"Memory increase: {memory_increase:.1f}MB, Cleanup: {memory_cleanup:.1f}MB")
        
        # Memory increase should be reasonable for 10k items
        assert memory_increase < 100  # Less than 100MB for 10k items
        
        # Should clean up most memory
        assert memory_cleanup > memory_increase * 0.7  # At least 70% cleanup
