version: '3.8'

# Traefik Reverse Proxy for CertRats Platform
#
# This Docker Compose file sets up Traefik as a standalone reverse proxy
# that can be shared across multiple projects and environments.
#
# Features:
# - Automatic service discovery
# - SSL certificate management
# - Dashboard and monitoring
# - Security middleware
# - Rate limiting and access control
#
# Usage:
#   docker-compose -f docker-compose.traefik.yml up -d
#
# This should be started before the main CertRats services.

networks:
  traefik_network:
    name: traefik_network
    external: true

volumes:
  traefik_certificates:
    name: traefik_certificates
  traefik_acme:
    name: traefik_acme
  portainer_data:
    name: portainer_data

services:
  # =============================================================================
  # TRAEFIK REVERSE PROXY
  # =============================================================================

  traefik:
    image: traefik:v3.0
    container_name: traefik_proxy
    restart: unless-stopped
    command:
      # Enable Docker provider
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=traefik_network

      # Enable file provider for dynamic configuration
      - --providers.file.filename=/etc/traefik/dynamic.yml
      - --providers.file.watch=true

      # Entry points
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --entrypoints.traefik.address=:8080

      # Enable API and dashboard
      - --api.dashboard=true
      - --api.debug=true
      - --api.insecure=true  # Only for development

      # Certificate resolvers
      - --certificatesresolvers.letsencrypt.acme.httpchallenge=true
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/etc/traefik/acme.json

      # Logging
      - --log.level=INFO
      - --log.format=json
      - --accesslog=true
      - --accesslog.format=json

      # Metrics
      - --metrics.prometheus=true
      - --metrics.prometheus.addEntryPointsLabels=true
      - --metrics.prometheus.addServicesLabels=true

      # Ping endpoint
      - --ping=true

      # Global settings
      - --global.checknewversion=false
      - --global.sendanonymoususage=false

    ports:
      # HTTP
      - "80:80"
      # HTTPS
      - "443:443"
      # Dashboard
      - "8080:8080"

    volumes:
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro

      # Traefik configuration files
      - ./docker/traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - ./docker/traefik/dynamic.yml:/etc/traefik/dynamic.yml:ro

      # Certificate storage
      - traefik_acme:/etc/traefik
      - traefik_certificates:/etc/traefik/certs

      # Custom certificates (for development)
      - ./docker/traefik/certs:/etc/traefik/certs:ro

    networks:
      - traefik_network

    environment:
      # Cloudflare API credentials (for DNS challenge)
      # Uncomment and set these for production with DNS challenge
      # - CF_API_EMAIL=<EMAIL>
      # - CF_API_KEY=your-cloudflare-api-key

      # Or use API token (recommended)
      # - CF_DNS_API_TOKEN=your-cloudflare-dns-api-token

      # Traefik environment
      - TRAEFIK_LOG_LEVEL=INFO

    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"

      # Dashboard router
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.certrats.localhost`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=web"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"

      # Dashboard authentication (optional)
      - "traefik.http.routers.traefik-dashboard.middlewares=traefik-auth"
      - "traefik.http.middlewares.traefik-auth.basicauth.users=admin:$$2y$$10$$2b2cu2Fw1ZfqRkjQ1s3IuOe9TZQQaHBNjXAqCo6rjlb5cHRXFOxJO"

      # API router
      - "traefik.http.routers.traefik-api.rule=Host(`traefik.certrats.localhost`) && PathPrefix(`/api`)"
      - "traefik.http.routers.traefik-api.entrypoints=web"
      - "traefik.http.routers.traefik-api.service=api@internal"

      # Ping router
      - "traefik.http.routers.traefik-ping.rule=Host(`traefik.certrats.localhost`) && Path(`/ping`)"
      - "traefik.http.routers.traefik-ping.entrypoints=web"
      - "traefik.http.routers.traefik-ping.service=ping@internal"

    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # WHOAMI SERVICE (for testing)
  # =============================================================================

  whoami:
    image: traefik/whoami:v1.8
    container_name: traefik_whoami
    restart: unless-stopped
    networks:
      - traefik_network
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"

      # Router configuration
      - "traefik.http.routers.whoami.rule=Host(`whoami.certrats.localhost`)"
      - "traefik.http.routers.whoami.entrypoints=web"
      - "traefik.http.services.whoami.loadbalancer.server.port=80"

      # Middleware
      - "traefik.http.routers.whoami.middlewares=whoami-auth"
      - "traefik.http.middlewares.whoami-auth.basicauth.users=test:$$2y$$10$$2b2cu2Fw1ZfqRkjQ1s3IuOe9TZQQaHBNjXAqCo6rjlb5cHRXFOxJO"

  # =============================================================================
  # PORTAINER (Optional - for Docker management)
  # =============================================================================

  portainer:
    image: portainer/portainer-ce:latest
    container_name: traefik_portainer
    restart: unless-stopped
    command: -H unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data
    networks:
      - traefik_network
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"

      # Router configuration
      - "traefik.http.routers.portainer.rule=Host(`portainer.certrats.localhost`)"
      - "traefik.http.routers.portainer.entrypoints=web"
      - "traefik.http.services.portainer.loadbalancer.server.port=9000"

      # Security middleware
      - "traefik.http.routers.portainer.middlewares=portainer-headers"
      - "traefik.http.middlewares.portainer-headers.headers.customrequestheaders.X-Forwarded-Proto=http"
