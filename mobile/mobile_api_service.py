"""Mobile API Service for enterprise mobile applications.

This service provides comprehensive mobile-optimized API endpoints with
offline capabilities, synchronization, push notifications, and enterprise
mobile device management for iOS and Android applications.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
import json
import uuid
from enum import Enum

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserLicense
from models.progress_tracking import StudySession, PracticeTestResult, LearningGoal, Achievement
from models.certification import Certification
from services.enterprise_ai_service import EnterpriseAIService

logger = logging.getLogger(__name__)


class DeviceType(Enum):
    """Mobile device type enumeration."""
    IOS = "ios"
    ANDROID = "android"
    TABLET = "tablet"
    UNKNOWN = "unknown"


class SyncStatus(Enum):
    """Data synchronization status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CONFLICT = "conflict"


class NotificationType(Enum):
    """Push notification type enumeration."""
    STUDY_REMINDER = "study_reminder"
    ACHIEVEMENT = "achievement"
    DEADLINE_ALERT = "deadline_alert"
    SYSTEM_UPDATE = "system_update"
    SOCIAL_ACTIVITY = "social_activity"
    AI_INSIGHT = "ai_insight"
    EMERGENCY = "emergency"


class MobileAPIService:
    """Service for mobile application API and enterprise mobile management."""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = EnterpriseAIService(db)
    
    # Mobile Authentication and Device Management
    
    def register_mobile_device(self, user_id: str, device_data: Dict[str, Any]) -> Dict[str, Any]:
        """Register a mobile device for a user."""
        try:
            device_id = str(uuid.uuid4())
            
            # Extract device information
            device_info = {
                'device_id': device_id,
                'user_id': user_id,
                'device_type': device_data.get('device_type', DeviceType.UNKNOWN.value),
                'device_model': device_data.get('device_model', 'Unknown'),
                'os_version': device_data.get('os_version', 'Unknown'),
                'app_version': device_data.get('app_version', '1.0.0'),
                'push_token': device_data.get('push_token'),
                'device_name': device_data.get('device_name', 'Mobile Device'),
                'timezone': device_data.get('timezone', 'UTC'),
                'language': device_data.get('language', 'en'),
                'registered_at': datetime.utcnow(),
                'last_active': datetime.utcnow(),
                'is_active': True,
                'capabilities': device_data.get('capabilities', {}),
                'security_settings': device_data.get('security_settings', {})
            }
            
            # Store device registration (would be in a mobile_devices table)
            logger.info(f"Registered mobile device {device_id} for user {user_id}")
            
            return {
                'device_id': device_id,
                'registration_status': 'success',
                'sync_token': self._generate_sync_token(user_id, device_id),
                'offline_capabilities': self._get_offline_capabilities(device_info),
                'initial_sync_required': True,
                'server_time': datetime.utcnow().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error registering mobile device: {e}")
            return {'error': str(e)}
    
    def authenticate_mobile_user(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """Authenticate user for mobile application."""
        try:
            # Extract authentication data
            email = credentials.get('email')
            password = credentials.get('password')
            device_id = credentials.get('device_id')
            biometric_token = credentials.get('biometric_token')
            
            # Validate credentials (simplified for demo)
            user = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.email == email,
                EnterpriseUser.is_active == True
            ).first()
            
            if not user:
                return {'error': 'Invalid credentials', 'error_code': 'AUTH_FAILED'}
            
            # Generate mobile session token
            session_token = self._generate_session_token(user.user_id, device_id)
            
            # Get user permissions and capabilities
            permissions = self._get_mobile_permissions(user)
            
            # Get offline data package
            offline_data = self._prepare_offline_data_package(user.user_id)
            
            return {
                'authentication_status': 'success',
                'session_token': session_token,
                'user_profile': {
                    'user_id': user.user_id,
                    'email': user.email,
                    'display_name': user.display_name,
                    'role': user.role.value if user.role else 'learner',
                    'organization_id': user.organization_id,
                    'department_id': user.department_id
                },
                'permissions': permissions,
                'offline_data': offline_data,
                'session_expires_at': (datetime.utcnow() + timedelta(hours=24)).isoformat(),
                'requires_sync': True
            }
        
        except Exception as e:
            logger.error(f"Error authenticating mobile user: {e}")
            return {'error': str(e), 'error_code': 'AUTH_ERROR'}
    
    # Helper Methods
    
    def _generate_sync_token(self, user_id: str, device_id: str) -> str:
        """Generate secure sync token for mobile device."""
        import hashlib
        import secrets
        
        # Create unique token based on user, device, and timestamp
        token_data = f"{user_id}:{device_id}:{datetime.utcnow().isoformat()}:{secrets.token_hex(16)}"
        return hashlib.sha256(token_data.encode()).hexdigest()
    
    def _generate_session_token(self, user_id: str, device_id: str) -> str:
        """Generate secure session token for mobile authentication."""
        import hashlib
        import secrets
        
        # Create session token
        session_data = f"{user_id}:{device_id}:{datetime.utcnow().isoformat()}:{secrets.token_hex(32)}"
        return hashlib.sha256(session_data.encode()).hexdigest()
    
    def _get_offline_capabilities(self, device_info: Dict[str, Any]) -> Dict[str, Any]:
        """Determine offline capabilities based on device specifications."""
        capabilities = device_info.get('capabilities', {})
        
        return {
            'offline_ai': capabilities.get('ai_processing', False),
            'offline_storage_mb': capabilities.get('storage_mb', 100),
            'offline_sync': True,
            'offline_study_materials': True,
            'offline_practice_tests': True,
            'offline_progress_tracking': True,
            'background_sync': capabilities.get('background_processing', True)
        }
    
    def _get_mobile_permissions(self, user: EnterpriseUser) -> Dict[str, Any]:
        """Get mobile-specific permissions for user."""
        base_permissions = {
            'view_dashboard': True,
            'track_progress': True,
            'take_practice_tests': True,
            'view_study_materials': True,
            'sync_data': True,
            'receive_notifications': True
        }
        
        # Add role-specific permissions
        if user.role and user.role.value in ['org_admin', 'department_admin']:
            base_permissions.update({
                'view_team_progress': True,
                'manage_team_goals': True,
                'view_analytics': True
            })
        
        return base_permissions
    
    def _prepare_offline_data_package(self, user_id: str) -> Dict[str, Any]:
        """Prepare basic offline data package."""
        return {
            'dashboard_data': {'user_id': user_id, 'cached_at': datetime.utcnow().isoformat()},
            'study_materials': [],
            'ai_recommendations': [],
            'app_config': {'offline_enabled': True}
        }
