version: '3.8'

services:
  # CertRats API Service
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: production
    container_name: certrats_api
    restart: unless-stopped
    environment:
      - DATABASE_URL=sqlite:///./certpathfinder.db
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      # Mount the database file for persistence
      - ./certpathfinder.db:/app/certpathfinder.db
      # Mount source code for development (hot reload)
      - .:/app
    networks:
      - traefik
    labels:
      # Traefik configuration
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-api.rule=Host(`api.certrats.localhost`)"
      - "traefik.http.routers.certrats-api.entrypoints=web"
      - "traefik.http.services.certrats-api.loadbalancer.server.port=8000"
      
      # CORS headers for API
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolalloworiginlist=http://app.certrats.localhost,http://localhost:3000"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.api-cors.headers.addvaryheader=true"
      - "traefik.http.routers.certrats-api.middlewares=api-cors"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # CertRats Frontend Service
  frontend:
    build:
      context: ./frontend-next
      dockerfile: Dockerfile
      target: production
    container_name: certrats_frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://api.certrats.localhost/api/v1
    volumes:
      # Mount source code for development (hot reload)
      - ./frontend-next:/app
      - /app/node_modules
      - /app/.next
    networks:
      - traefik
    labels:
      # Traefik configuration
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-frontend.rule=Host(`app.certrats.localhost`)"
      - "traefik.http.routers.certrats-frontend.entrypoints=web"
      - "traefik.http.services.certrats-frontend.loadbalancer.server.port=3000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - api

networks:
  traefik:
    external: true
