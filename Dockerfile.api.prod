# Production-optimized multi-stage build for API
# Stage 1: Build dependencies and compile Python packages
FROM python:3.12-slim AS builder

# Set build-time environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    build-essential \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install wheel
RUN pip install --upgrade pip setuptools wheel

# Copy requirements and install dependencies
COPY requirements.txt requirements-prod.txt* ./
RUN pip install -r requirements.txt && \
    if [ -f requirements-prod.txt ]; then pip install -r requirements-prod.txt; fi

# Stage 2: Runtime dependencies
FROM python:3.12-slim AS runtime-deps

# Install runtime system dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && rm -rf /var/cache/apt/*

# Stage 3: Final production image
FROM runtime-deps AS production

# Set production environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PATH="/opt/venv/bin:$PATH" \
    ENVIRONMENT=production \
    WORKERS=4 \
    MAX_WORKERS=8 \
    WORKER_CLASS=uvicorn.workers.UvicornWorker \
    TIMEOUT=30 \
    KEEPALIVE=2 \
    MAX_REQUESTS=1000 \
    MAX_REQUESTS_JITTER=100

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Create application user and group
RUN groupadd -r appgroup && \
    useradd -r -g appgroup -u 1000 -m -s /bin/bash appuser

# Create application directory
WORKDIR /app

# Copy application code with proper ownership
COPY --chown=appuser:appgroup . .

# Remove development and test files to minimize image size
RUN rm -rf \
    tests/ \
    docs/ \
    frontend/ \
    .dockerwrapper/ \
    .github/ \
    *.md \
    .git* \
    .pytest_cache/ \
    __pycache__/ \
    .coverage \
    htmlcov/ \
    .env.example \
    docker-compose*.yml \
    Makefile \
    scripts/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp && \
    chown -R appuser:appgroup /app/logs /app/tmp

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Add labels for better container management
LABEL maintainer="CertPathFinder Team" \
      version="1.0.0" \
      description="CertPathFinder API - Production Build" \
      org.opencontainers.image.source="https://github.com/forkrul/replit-CertPathFinder"

# Health check with improved configuration
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production command with Gunicorn for better performance
CMD ["gunicorn", "api.app:app", \
     "--bind", "0.0.0.0:8000", \
     "--worker-class", "uvicorn.workers.UvicornWorker", \
     "--workers", "4", \
     "--max-requests", "1000", \
     "--max-requests-jitter", "100", \
     "--timeout", "30", \
     "--keepalive", "2", \
     "--access-logfile", "-", \
     "--error-logfile", "-", \
     "--log-level", "info"]
