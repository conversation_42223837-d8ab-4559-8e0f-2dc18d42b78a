#!/usr/bin/env python3
"""
Quick verification test for CertRats core functionality
Tests the main API endpoints and components we've implemented
"""

import sys
import os
import asyncio
from fastapi.testclient import TestClient

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_imports():
    """Test that all our API modules can be imported successfully"""
    print("🔍 Testing API imports...")
    
    try:
        from api.v1.auth import router as auth_router
        print("✅ Auth API imported successfully")
        
        from api.v1.dashboard import router as dashboard_router
        print("✅ Dashboard API imported successfully")
        
        from schemas.auth import UserRegistrationRequest, UserLoginRequest
        print("✅ Auth schemas imported successfully")
        
        from models.user import User
        print("✅ User model imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_schema_validation():
    """Test that our Pydantic schemas work correctly"""
    print("\n🔍 Testing schema validation...")
    
    try:
        from schemas.auth import UserRegistrationRequest, EnhancedUserRegistrationRequest
        
        # Test basic registration schema
        basic_data = {
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "name": "Test User"
        }
        basic_request = UserRegistrationRequest(**basic_data)
        print("✅ Basic registration schema validation works")
        
        # Test enhanced registration schema
        enhanced_data = {
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "confirm_password": "TestPass123!",
            "first_name": "Test",
            "last_name": "User",
            "accept_terms": True,
            "marketing_consent": False
        }
        enhanced_request = EnhancedUserRegistrationRequest(**enhanced_data)
        print("✅ Enhanced registration schema validation works")
        
        return True
    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        return False

def test_password_validation():
    """Test password strength validation"""
    print("\n🔍 Testing password validation...")
    
    try:
        from schemas.auth import EnhancedUserRegistrationRequest
        
        # Test weak password (should fail)
        try:
            weak_data = {
                "email": "<EMAIL>",
                "password": "weak",
                "confirm_password": "weak",
                "first_name": "Test",
                "last_name": "User",
                "accept_terms": True
            }
            EnhancedUserRegistrationRequest(**weak_data)
            print("❌ Weak password validation failed - should have been rejected")
            return False
        except Exception:
            print("✅ Weak password correctly rejected")
        
        # Test strong password (should pass)
        strong_data = {
            "email": "<EMAIL>",
            "password": "StrongPass123!",
            "confirm_password": "StrongPass123!",
            "first_name": "Test",
            "last_name": "User",
            "accept_terms": True
        }
        EnhancedUserRegistrationRequest(**strong_data)
        print("✅ Strong password correctly accepted")
        
        return True
    except Exception as e:
        print(f"❌ Password validation test failed: {e}")
        return False

def test_user_model():
    """Test User model functionality"""
    print("\n🔍 Testing User model...")
    
    try:
        from models.user import User
        
        # Create a user instance
        user = User(
            user_id="test_user_123",
            email="<EMAIL>",
            name="Test User",
            first_name="Test",
            last_name="User"
        )
        
        # Test password setting and verification
        user.set_password("TestPassword123!")
        print("✅ Password setting works")
        
        if user.verify_password("TestPassword123!"):
            print("✅ Password verification works")
        else:
            print("❌ Password verification failed")
            return False
        
        if not user.verify_password("WrongPassword"):
            print("✅ Wrong password correctly rejected")
        else:
            print("❌ Wrong password incorrectly accepted")
            return False
        
        # Test email verification token generation
        token = user.generate_email_verification_token()
        if token and len(token) > 10:
            print("✅ Email verification token generation works")
        else:
            print("❌ Email verification token generation failed")
            return False
        
        return True
    except Exception as e:
        print(f"❌ User model test failed: {e}")
        return False

def test_api_endpoints_structure():
    """Test that API routers are properly structured"""
    print("\n🔍 Testing API router structure...")

    try:
        from api.v1.auth import router as auth_router
        from api.v1.dashboard import router as dashboard_router

        # Test that routers have the expected routes
        auth_routes = [route.path for route in auth_router.routes]
        dashboard_routes = [route.path for route in dashboard_router.routes]

        expected_auth_routes = [
            "/register",
            "/login",
            "/check-email",
            "/verify-email",
            "/resend-verification"
        ]

        expected_dashboard_routes = [
            "/overview",
            "/quick-stats",
            "/learning-paths",
            "/recommendations",
            "/quick-action"
        ]

        # Check auth routes
        for expected_route in expected_auth_routes:
            if any(expected_route in route for route in auth_routes):
                print(f"✅ Auth route {expected_route} exists")
            else:
                print(f"❌ Auth route {expected_route} not found")
                print(f"Available routes: {auth_routes}")
                return False

        # Check dashboard routes
        for expected_route in expected_dashboard_routes:
            if any(expected_route in route for route in dashboard_routes):
                print(f"✅ Dashboard route {expected_route} exists")
            else:
                print(f"❌ Dashboard route {expected_route} not found")
                print(f"Available routes: {dashboard_routes}")
                return False

        return True
    except Exception as e:
        print(f"❌ API router structure test failed: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🚀 Starting CertRats Core Functionality Verification\n")
    
    tests = [
        ("API Imports", test_api_imports),
        ("Schema Validation", test_schema_validation),
        ("Password Validation", test_password_validation),
        ("User Model", test_user_model),
        ("API Endpoints Structure", test_api_endpoints_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"VERIFICATION SUMMARY")
    print('='*50)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Core functionality is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
