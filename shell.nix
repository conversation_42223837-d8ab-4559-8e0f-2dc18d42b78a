{ pkgs ? import <nixpkgs> {} }:

let
  # Python with all required packages
  pythonEnv = pkgs.python311.withPackages (ps: with ps; [
    # Core FastAPI and web framework
    fastapi
    uvicorn
    python-multipart
    jinja2
    
    # Database
    sqlalchemy
    alembic
    psycopg2
    
    # Data processing and visualization
    plotly
    pandas
    numpy
    scipy
    matplotlib
    seaborn
    
    # Authentication and security
    python-jose
    passlib
    python-decouple
    ldap3
    cryptography
    bcrypt
    
    # HTTP clients
    requests
    httpx
    aiohttp
    
    # Data validation
    pydantic
    pydantic-settings
    
    # Machine Learning and AI
    scikit-learn
    joblib
    xgboost
    lightgbm
    
    # Natural Language Processing
    nltk
    
    # Utilities
    python-dateutil
    pytz
    click
    
    # Development and testing
    pytest
    pytest-asyncio
    pytest-cov
    black
    flake8
    mypy
    
    # Logging
    structlog
    
    # File handling
    openpyxl
    xlsxwriter
    reportlab
    pillow
    
    # Async support
    aiofiles
    
    # Environment
    python-dotenv
    
    # Caching
    redis
    cachetools
    
    # Background tasks
    celery
    
    # Email
    # fastapi-mail  # Not available in nixpkgs, will use pip fallback
    
    # Cloud storage
    boto3
    
    # Monitoring
    prometheus-client
    
    # WebSocket
    websockets
    
    # JSON handling
    # orjson  # Not available in nixpkgs, will use pip fallback
    
    # Type hints
    typing-extensions
    
    # Additional useful packages
    ipython
    jupyter
    tqdm
    rich
    typer
  ]);

in pkgs.mkShell {
  buildInputs = with pkgs; [
    # Python environment
    pythonEnv
    
    # System dependencies
    libxcrypt
    freetype
    glibcLocales
    file
    
    # Databases
    postgresql
    redis
    sqlite
    
    # Development tools
    git
    nodejs_20
    nodePackages.npm
    docker
    docker-compose
    
    # Build tools
    gcc
    pkg-config
    gnumake
    
    # Libraries
    openssl
    libffi
    zlib
    libjpeg
    libpng
    freetype
    
    # Utilities
    curl
    wget
    unzip
    tree
    htop
    jq
    
    # Testing tools
    playwright-driver.browsers
  ];
  
  shellHook = ''
    echo "🚀 CertPathFinder Development Environment"
    echo "========================================="
    echo "Python: $(python --version)"
    echo "Node.js: $(node --version)"
    echo "PostgreSQL: $(postgres --version | head -n1)"
    echo "Redis: $(redis-server --version)"
    echo ""
    echo "📦 Available commands:"
    echo "  make dev          - Start development servers"
    echo "  make test         - Run test suite"
    echo "  make lint         - Run linting"
    echo "  make format       - Format code"
    echo "  make db-migrate   - Run database migrations"
    echo ""
    echo "🔧 Installing additional Python packages not in nixpkgs..."
    
    # Create a local pip environment for packages not available in nixpkgs
    export PIP_PREFIX="$PWD/.nix-pip"
    export PYTHONPATH="$PIP_PREFIX/lib/python3.11/site-packages:$PYTHONPATH"
    export PATH="$PIP_PREFIX/bin:$PATH"
    mkdir -p "$PIP_PREFIX"
    
    # Install packages not available in nixpkgs
    pip install --prefix="$PIP_PREFIX" --no-deps \
      fastapi-mail==1.4.1 \
      orjson==3.9.15 \
      slowapi==0.1.9 \
      fastapi-cors==0.0.6 \
      swagger-ui-bundle==0.0.9 \
      catboost==1.2.2 \
      2>/dev/null || echo "⚠️  Some packages may need manual installation"
    
    echo "✅ Environment ready!"
    echo ""
    echo "💡 To activate this environment, run: nix-shell"
    echo "💡 To exit, run: exit"
  '';
  
  # Environment variables
  NIX_SHELL_PRESERVE_PROMPT = 1;
  PYTHONPATH = ".";
  DATABASE_URL = "postgresql://localhost/certrats_dev";
  REDIS_URL = "redis://localhost:6379";
  SECRET_KEY = "dev-secret-key-change-in-production";
  ENVIRONMENT = "development";
}
