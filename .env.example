# CertRats Platform Environment Variables
#
# Copy this file to .env and update the values for your environment.
# Different environments (development, staging, production) may require
# different values for these variables.
#
# SECURITY WARNING: Never commit .env files with real credentials to version control!

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

# Environment name (development, staging, production)
CERTRATS_ENV=development

# Application settings
DEBUG=true
LOG_LEVEL=DEBUG
SECRET_KEY=your-secret-key-change-in-production-make-it-long-and-random

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
POSTGRES_DB=certrats_db
POSTGRES_USER=certrats
POSTGRES_PASSWORD=certrats_password_change_in_production

# Database URL (automatically constructed from above values)
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@database:5432/${POSTGRES_DB}

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Cache and Message Broker
REDIS_PASSWORD=certrats_redis_password_change_in_production
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0

# =============================================================================
# OBJECT STORAGE CONFIGURATION (MinIO)
# =============================================================================

# MinIO Object Storage
MINIO_ACCESS_KEY=certrats_minio_access_key
MINIO_SECRET_KEY=certrats_minio_secret_key_change_in_production
MINIO_ENDPOINT=storage:9000
MINIO_BUCKET_PREFIX=certrats

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS Origins (comma-separated list)
CORS_ORIGINS=http://app.certrats.docker.localhost,http://localhost:3000

# JWT Configuration
JWT_SECRET_KEY=jwt-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# API Rate Limiting
API_RATE_LIMIT_PER_MINUTE=100
API_RATE_LIMIT_BURST=200

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false

# Email Templates
FROM_EMAIL=<EMAIL>
FROM_NAME=CertRats Platform

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Grafana
GRAFANA_ADMIN_PASSWORD=certrats_grafana_admin_password

# Prometheus
PROMETHEUS_RETENTION_TIME=200h

# Flower (Celery Monitoring)
FLOWER_PASSWORD=certrats_flower_password

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

# Celery Settings
CELERY_BROKER_URL=${REDIS_URL}
CELERY_RESULT_BACKEND=${REDIS_URL}
CELERY_LOG_LEVEL=INFO
CELERY_WORKER_CONCURRENCY=4
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000

# =============================================================================
# EXTERNAL SERVICES CONFIGURATION
# =============================================================================

# OpenAI API (for AI features)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# Certification Provider APIs
COMPTIA_API_KEY=your-comptia-api-key
CISCO_API_KEY=your-cisco-api-key
AWS_API_KEY=your-aws-api-key
MICROSOFT_API_KEY=your-microsoft-api-key

# Analytics and Tracking
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================

# Let's Encrypt Email (for production SSL certificates)
LETSENCRYPT_EMAIL=<EMAIL>

# Cloudflare API (for DNS challenge)
CF_API_EMAIL=<EMAIL>
CF_API_KEY=your-cloudflare-api-key
CF_DNS_API_TOKEN=your-cloudflare-dns-api-token

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Backup Settings
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=certrats-backups
BACKUP_S3_REGION=us-east-1

# AWS S3 for Backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Tools
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=certrats_pgadmin

# Hot Reload Settings
CHOKIDAR_USEPOLLING=true
FAST_REFRESH=true

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# Production Domains
PRODUCTION_DOMAIN=certrats.com
STAGING_DOMAIN=staging.certrats.dev

# Resource Limits
API_WORKERS=4
API_MAX_WORKERS=8
CELERY_WORKER_REPLICAS=3

# Logging
LOG_FORMAT=json
LOG_FILE=/var/log/certrats/app.log
ACCESS_LOG=true

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles
ENABLE_AI_RECOMMENDATIONS=true
ENABLE_SOCIAL_LOGIN=true
ENABLE_ENTERPRISE_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true

# Beta Features
ENABLE_BETA_FEATURES=false
ENABLE_EXPERIMENTAL_UI=false
