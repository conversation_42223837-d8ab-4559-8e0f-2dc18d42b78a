"""Pydantic schemas for Mobile Enterprise API endpoints.

This module provides comprehensive request/response schemas for mobile
applications including device registration, authentication, offline sync,
push notifications, and mobile analytics.
"""

from pydantic import BaseModel, Field, validator, EmailStr
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class DeviceType(str, Enum):
    """Mobile device type enumeration."""
    IOS = "ios"
    ANDROID = "android"
    TABLET = "tablet"
    UNKNOWN = "unknown"


class SyncStatus(str, Enum):
    """Data synchronization status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CONFLICT = "conflict"


class NotificationType(str, Enum):
    """Push notification type enumeration."""
    STUDY_REMINDER = "study_reminder"
    ACHIEVEMENT = "achievement"
    DEADLINE_ALERT = "deadline_alert"
    SYSTEM_UPDATE = "system_update"
    SOCIAL_ACTIVITY = "social_activity"
    AI_INSIGHT = "ai_insight"
    EMERGENCY = "emergency"


class NotificationPriority(str, Enum):
    """Notification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


# Device Registration Schemas

class DeviceRegistrationRequest(BaseModel):
    """Schema for mobile device registration request."""
    user_id: str = Field(..., description="User ID")
    device_type: DeviceType = Field(..., description="Device type")
    device_model: str = Field(..., description="Device model")
    os_version: str = Field(..., description="Operating system version")
    app_version: str = Field(..., description="Application version")
    push_token: Optional[str] = Field(None, description="Push notification token")
    device_name: Optional[str] = Field(None, description="User-defined device name")
    timezone: Optional[str] = Field("UTC", description="Device timezone")
    language: Optional[str] = Field("en", description="Device language")
    capabilities: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Device capabilities")
    security_settings: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Security settings")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "user123",
                "device_type": "ios",
                "device_model": "iPhone 14 Pro",
                "os_version": "17.0",
                "app_version": "1.2.0",
                "push_token": "abc123def456",
                "device_name": "John's iPhone",
                "timezone": "America/New_York",
                "language": "en",
                "capabilities": {
                    "ai_processing": True,
                    "storage_mb": 1000,
                    "background_processing": True
                },
                "security_settings": {
                    "biometric_enabled": True,
                    "pin_required": True
                }
            }
        }


class DeviceRegistrationResponse(BaseModel):
    """Schema for device registration response."""
    device_id: str = Field(..., description="Unique device identifier")
    registration_status: str = Field(..., description="Registration status")
    sync_token: str = Field(..., description="Initial sync token")
    offline_capabilities: Dict[str, Any] = Field(..., description="Available offline capabilities")
    initial_sync_required: bool = Field(..., description="Whether initial sync is required")
    server_time: str = Field(..., description="Server timestamp")


# Authentication Schemas

class MobileAuthRequest(BaseModel):
    """Schema for mobile authentication request."""
    email: EmailStr = Field(..., description="User email")
    password: Optional[str] = Field(None, description="User password")
    device_id: str = Field(..., description="Device identifier")
    biometric_token: Optional[str] = Field(None, description="Biometric authentication token")
    remember_device: bool = Field(True, description="Remember this device")
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "secure_password",
                "device_id": "device_123",
                "biometric_token": None,
                "remember_device": True
            }
        }


class UserProfile(BaseModel):
    """Schema for user profile in mobile context."""
    user_id: str = Field(..., description="User identifier")
    email: EmailStr = Field(..., description="User email")
    display_name: Optional[str] = Field(None, description="Display name")
    role: str = Field(..., description="User role")
    organization_id: int = Field(..., description="Organization ID")
    department_id: Optional[int] = Field(None, description="Department ID")


class MobileAuthResponse(BaseModel):
    """Schema for mobile authentication response."""
    authentication_status: str = Field(..., description="Authentication status")
    session_token: str = Field(..., description="Session token")
    user_profile: UserProfile = Field(..., description="User profile information")
    permissions: Dict[str, bool] = Field(..., description="User permissions")
    offline_data: Dict[str, Any] = Field(..., description="Initial offline data")
    session_expires_at: str = Field(..., description="Session expiration time")
    requires_sync: bool = Field(..., description="Whether sync is required")


# Offline Data Schemas

class OfflineDataRequest(BaseModel):
    """Schema for offline data request."""
    sync_token: str = Field(..., description="Current sync token")
    data_types: Optional[List[str]] = Field(None, description="Specific data types to include")
    max_size_mb: Optional[int] = Field(100, description="Maximum package size in MB")
    include_ai_models: bool = Field(True, description="Include offline AI models")
    
    class Config:
        schema_extra = {
            "example": {
                "sync_token": "sync_token_123",
                "data_types": ["study_materials", "practice_tests", "progress_data"],
                "max_size_mb": 150,
                "include_ai_models": True
            }
        }


class OfflineDataResponse(BaseModel):
    """Schema for offline data response."""
    package_id: str = Field(..., description="Offline package identifier")
    user_id: str = Field(..., description="User identifier")
    generated_at: str = Field(..., description="Package generation time")
    sync_token: str = Field(..., description="Sync token")
    expires_at: str = Field(..., description="Package expiration time")
    user_profile: Dict[str, Any] = Field(..., description="User profile data")
    learning_goals: List[Dict[str, Any]] = Field(..., description="Learning goals")
    recent_sessions: List[Dict[str, Any]] = Field(..., description="Recent study sessions")
    test_results: List[Dict[str, Any]] = Field(..., description="Test results")
    ai_recommendations: Dict[str, Any] = Field(..., description="AI recommendations")
    study_materials: Dict[str, Any] = Field(..., description="Study materials")
    offline_ai_models: Dict[str, Any] = Field(..., description="Offline AI models")
    app_config: Dict[str, Any] = Field(..., description="App configuration")
    sync_settings: Dict[str, Any] = Field(..., description="Sync settings")
    package_size_mb: float = Field(..., description="Package size in MB")
    content_version: str = Field(..., description="Content version")
    requires_wifi: bool = Field(..., description="Whether WiFi is required")


# Synchronization Schemas

class SyncRequest(BaseModel):
    """Schema for data synchronization request."""
    sync_token: str = Field(..., description="Current sync token")
    last_sync_time: Optional[str] = Field(None, description="Last successful sync time")
    study_sessions: Optional[List[Dict[str, Any]]] = Field(None, description="Study sessions to sync")
    test_results: Optional[List[Dict[str, Any]]] = Field(None, description="Test results to sync")
    learning_goals: Optional[List[Dict[str, Any]]] = Field(None, description="Learning goals to sync")
    user_preferences: Optional[Dict[str, Any]] = Field(None, description="User preferences to sync")
    device_analytics: Optional[Dict[str, Any]] = Field(None, description="Device analytics data")
    
    class Config:
        schema_extra = {
            "example": {
                "sync_token": "sync_token_123",
                "last_sync_time": "2024-01-15T10:30:00Z",
                "study_sessions": [
                    {
                        "session_id": "session_1",
                        "duration_minutes": 45,
                        "topics_covered": ["networking", "security"]
                    }
                ],
                "test_results": [
                    {
                        "test_id": "test_1",
                        "score": 85,
                        "completed_at": "2024-01-15T11:00:00Z"
                    }
                ]
            }
        }


class SyncConflict(BaseModel):
    """Schema for sync conflict information."""
    conflict_id: str = Field(..., description="Conflict identifier")
    data_type: str = Field(..., description="Type of conflicting data")
    local_version: Dict[str, Any] = Field(..., description="Local version of data")
    server_version: Dict[str, Any] = Field(..., description="Server version of data")
    resolution_options: List[str] = Field(..., description="Available resolution options")


class SyncResponse(BaseModel):
    """Schema for synchronization response."""
    sync_id: str = Field(..., description="Sync operation identifier")
    sync_status: SyncStatus = Field(..., description="Synchronization status")
    sync_started: str = Field(..., description="Sync start time")
    sync_completed: str = Field(..., description="Sync completion time")
    sync_results: Dict[str, Dict[str, int]] = Field(..., description="Sync results by data type")
    conflicts: List[SyncConflict] = Field(..., description="Sync conflicts")
    updated_data: Dict[str, Any] = Field(..., description="Updated data from server")
    new_sync_token: str = Field(..., description="New sync token")
    next_sync_recommended: str = Field(..., description="Next recommended sync time")


# Push Notification Schemas

class PushNotificationRequest(BaseModel):
    """Schema for push notification request."""
    type: NotificationType = Field(..., description="Notification type")
    title: str = Field(..., description="Notification title")
    message: str = Field(..., description="Notification message")
    data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional notification data")
    priority: NotificationPriority = Field(NotificationPriority.NORMAL, description="Notification priority")
    scheduled_at: Optional[str] = Field(None, description="Scheduled delivery time")
    expires_at: Optional[str] = Field(None, description="Notification expiration time")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "study_reminder",
                "title": "Time to Study! 📚",
                "message": "Your daily study session is ready. Let's achieve your goals!",
                "data": {
                    "action": "open_study_session",
                    "certification_id": "cert_123"
                },
                "priority": "normal",
                "scheduled_at": "2024-01-15T14:00:00Z"
            }
        }


class PushNotificationResponse(BaseModel):
    """Schema for push notification response."""
    notification_id: str = Field(..., description="Notification identifier")
    delivery_status: str = Field(..., description="Delivery status")
    devices_targeted: int = Field(..., description="Number of devices targeted")
    delivery_results: List[Dict[str, Any]] = Field(..., description="Delivery results per device")
    sent_at: str = Field(..., description="Notification sent time")


# Mobile Analytics Schemas

class MobileAnalyticsRequest(BaseModel):
    """Schema for mobile analytics tracking request."""
    event_type: str = Field(..., description="Analytics event type")
    event_data: Dict[str, Any] = Field(..., description="Event data")
    session_id: Optional[str] = Field(None, description="Session identifier")
    app_version: Optional[str] = Field(None, description="App version")
    platform: Optional[str] = Field(None, description="Platform (iOS/Android)")
    network_type: Optional[str] = Field(None, description="Network type")
    battery_level: Optional[float] = Field(None, description="Battery level (0-1)")
    memory_usage: Optional[float] = Field(None, description="Memory usage in MB")
    
    class Config:
        schema_extra = {
            "example": {
                "event_type": "study_session_completed",
                "event_data": {
                    "duration_minutes": 30,
                    "topics_covered": 3,
                    "completion_rate": 0.85
                },
                "session_id": "session_123",
                "app_version": "1.2.0",
                "platform": "ios",
                "network_type": "wifi",
                "battery_level": 0.75,
                "memory_usage": 120.5
            }
        }


class MobileAnalyticsResponse(BaseModel):
    """Schema for mobile analytics response."""
    tracking_status: str = Field(..., description="Tracking status")
    event_id: str = Field(..., description="Event identifier")
    insights: List[str] = Field(..., description="Generated insights")
    recommendations: List[str] = Field(..., description="Recommendations")


# Dashboard Schemas

class QuickStat(BaseModel):
    """Schema for quick statistics."""
    label: str = Field(..., description="Statistic label")
    value: str = Field(..., description="Statistic value")
    change: Optional[str] = Field(None, description="Change indicator")
    trend: Optional[str] = Field(None, description="Trend direction")


class MobileDashboardResponse(BaseModel):
    """Schema for mobile dashboard response."""
    user_profile: Dict[str, Any] = Field(..., description="User profile summary")
    active_goals: List[Dict[str, Any]] = Field(..., description="Active learning goals")
    recent_achievements: List[Dict[str, Any]] = Field(..., description="Recent achievements")
    study_streak: Dict[str, Any] = Field(..., description="Study streak information")
    ai_recommendations: Dict[str, Any] = Field(..., description="AI recommendations")
    quick_stats: List[QuickStat] = Field(..., description="Quick statistics")
    next_actions: List[str] = Field(..., description="Recommended next actions")
    motivational_message: str = Field(..., description="Motivational message")
    last_updated: str = Field(..., description="Last update time")


# Health Check Schema

class MobileHealthResponse(BaseModel):
    """Schema for mobile health check response."""
    status: str = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    features: List[str] = Field(..., description="Available features")
    mobile_capabilities: Dict[str, bool] = Field(..., description="Mobile-specific capabilities")
    supported_platforms: List[str] = Field(..., description="Supported mobile platforms")
    api_version: str = Field(..., description="API version")
    timestamp: str = Field(..., description="Health check timestamp")


# Utility Schemas

class DeviceCapabilities(BaseModel):
    """Schema for device capabilities."""
    ai_processing: bool = Field(False, description="On-device AI processing capability")
    storage_mb: int = Field(100, description="Available storage in MB")
    background_processing: bool = Field(True, description="Background processing capability")
    biometric_auth: bool = Field(False, description="Biometric authentication support")
    push_notifications: bool = Field(True, description="Push notification support")
    offline_sync: bool = Field(True, description="Offline synchronization capability")


class SecuritySettings(BaseModel):
    """Schema for mobile security settings."""
    biometric_enabled: bool = Field(False, description="Biometric authentication enabled")
    pin_required: bool = Field(True, description="PIN requirement")
    auto_lock_minutes: int = Field(5, description="Auto-lock timeout in minutes")
    session_timeout_minutes: int = Field(1440, description="Session timeout in minutes")
    require_secure_storage: bool = Field(True, description="Require secure storage")


class OfflineAIModel(BaseModel):
    """Schema for offline AI model information."""
    model_id: str = Field(..., description="Model identifier")
    model_type: str = Field(..., description="Model type")
    version: str = Field(..., description="Model version")
    size_mb: float = Field(..., description="Model size in MB")
    capabilities: List[str] = Field(..., description="Model capabilities")
    accuracy: float = Field(..., description="Model accuracy")
    last_updated: str = Field(..., description="Last update time")


class StudyMaterial(BaseModel):
    """Schema for study material."""
    material_id: str = Field(..., description="Material identifier")
    title: str = Field(..., description="Material title")
    type: str = Field(..., description="Material type")
    size_mb: float = Field(..., description="Material size in MB")
    offline_available: bool = Field(..., description="Offline availability")
    last_updated: str = Field(..., description="Last update time")
    download_url: Optional[str] = Field(None, description="Download URL")


class MobileAppConfig(BaseModel):
    """Schema for mobile app configuration."""
    theme: str = Field("adaptive", description="App theme")
    offline_storage_limit_mb: int = Field(500, description="Offline storage limit")
    sync_frequency_hours: int = Field(6, description="Sync frequency in hours")
    notification_settings: Dict[str, bool] = Field(..., description="Notification preferences")
    ai_features: Dict[str, bool] = Field(..., description="AI feature toggles")
    performance_settings: Dict[str, Any] = Field(..., description="Performance settings")


# Error Schemas

class MobileErrorResponse(BaseModel):
    """Schema for mobile API error response."""
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Error message")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: str = Field(..., description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request identifier")
    support_contact: Optional[str] = Field(None, description="Support contact information")
