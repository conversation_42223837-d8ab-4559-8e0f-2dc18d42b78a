"""Pydantic schemas for Salary Intelligence API endpoints.

This module provides comprehensive request/response schemas for salary intelligence,
ROI analysis, market benchmarks, and certification impact analysis.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class LocationEnum(str, Enum):
    """Enumeration of supported locations."""
    SAN_FRANCISCO = "san_francisco"
    NEW_YORK = "new_york"
    SEATTLE = "seattle"
    AUSTIN = "austin"
    DENVER = "denver"
    ATLANTA = "atlanta"
    REMOTE = "remote"
    INTERNATIONAL = "international"


class MarketDemandEnum(str, Enum):
    """Enumeration of market demand levels."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class MarketOutlookEnum(str, Enum):
    """Enumeration of market outlook levels."""
    STRONG = "strong"
    STABLE = "stable"
    MODERATE = "moderate"
    DECLINING = "declining"


# Request Schemas

class ROICalculationRequest(BaseModel):
    """Request schema for ROI calculation."""
    
    certification_id: int = Field(..., description="Certification ID")
    current_role_id: int = Field(..., description="Current role ID")
    target_role_id: Optional[int] = Field(None, description="Target role ID (for career transition)")
    investment_cost: float = Field(..., ge=0, description="Total investment cost")
    location: str = Field("remote", description="Location for salary analysis")
    experience_years: int = Field(5, ge=0, le=50, description="Years of experience")
    
    class Config:
        schema_extra = {
            "example": {
                "certification_id": 1,
                "current_role_id": 5,
                "target_role_id": 8,
                "investment_cost": 3500.0,
                "location": "remote",
                "experience_years": 7
            }
        }


class SalaryAnalysisRequest(BaseModel):
    """Request schema for salary analysis."""
    
    role_id: int = Field(..., description="Role ID for analysis")
    location: str = Field("remote", description="Location")
    experience_years: int = Field(5, ge=0, le=50, description="Years of experience")
    include_certifications: bool = Field(True, description="Include certification impact")
    include_market_trends: bool = Field(True, description="Include market trend analysis")
    
    class Config:
        schema_extra = {
            "example": {
                "role_id": 5,
                "location": "san_francisco",
                "experience_years": 8,
                "include_certifications": True,
                "include_market_trends": True
            }
        }


# Response Schemas

class SalaryRangeResponse(BaseModel):
    """Response schema for salary ranges."""
    
    min: float = Field(..., description="Minimum salary")
    max: float = Field(..., description="Maximum salary")
    median: float = Field(..., description="Median salary")
    percentile_25: Optional[float] = Field(None, description="25th percentile")
    percentile_75: Optional[float] = Field(None, description="75th percentile")


class CertificationImpactDetail(BaseModel):
    """Detailed certification impact information."""
    
    salary_premium_percentage: float = Field(..., description="Salary premium percentage")
    estimated_increase: float = Field(..., description="Estimated salary increase")
    market_demand: MarketDemandEnum = Field(..., description="Market demand level")


class LocationAdjustment(BaseModel):
    """Location-based salary adjustment."""
    
    multiplier: float = Field(..., description="Location multiplier")
    description: str = Field(..., description="Adjustment description")


class GrowthProjection(BaseModel):
    """Salary growth projection."""
    
    one_year: float = Field(..., description="Projected salary in 1 year")
    three_year: float = Field(..., description="Projected salary in 3 years")
    five_year: float = Field(..., description="Projected salary in 5 years")
    ten_year: float = Field(..., description="Projected salary in 10 years")
    annual_growth_rate: float = Field(..., description="Annual growth rate percentage")


class MarketTrends(BaseModel):
    """Market trends information."""
    
    growth_rate: float = Field(..., description="Market growth rate percentage")
    market_outlook: MarketOutlookEnum = Field(..., description="Market outlook")
    demand_level: MarketDemandEnum = Field(..., description="Demand level")
    key_drivers: List[str] = Field(..., description="Key market drivers")
    emerging_skills: List[str] = Field(..., description="Emerging skills")
    risk_factors: List[str] = Field(..., description="Risk factors")


class SalaryIntelligenceResponse(BaseModel):
    """Response schema for comprehensive salary intelligence."""
    
    role_id: int = Field(..., description="Role ID")
    role_name: str = Field(..., description="Role name")
    current_salary_range: Dict[str, float] = Field(..., description="Current salary range")
    market_salary_range: Dict[str, float] = Field(..., description="Market salary range")
    certification_impact: Dict[str, Any] = Field(..., description="Certification impact analysis")
    location_adjustments: Dict[str, Any] = Field(..., description="Location-based adjustments")
    experience_multipliers: Dict[str, float] = Field(..., description="Experience multipliers")
    growth_projections: Dict[str, float] = Field(..., description="Salary growth projections")
    market_trends: Dict[str, Any] = Field(..., description="Market trends")
    analysis_date: str = Field(..., description="Analysis date")
    location: str = Field(..., description="Analysis location")
    experience_years: int = Field(..., description="Experience years")
    
    class Config:
        schema_extra = {
            "example": {
                "role_id": 5,
                "role_name": "Senior Security Engineer",
                "current_salary_range": {
                    "min": 120000,
                    "max": 180000,
                    "median": 150000
                },
                "market_salary_range": {
                    "min": 126000,
                    "max": 189000,
                    "median": 157500
                },
                "certification_impact": {
                    "CISSP": {
                        "salary_premium_percentage": 15.0,
                        "estimated_increase": 22500,
                        "market_demand": "high"
                    }
                },
                "analysis_date": "2024-01-15T10:30:00Z",
                "location": "remote",
                "experience_years": 7
            }
        }


class ROIAnalysisResponse(BaseModel):
    """Response schema for ROI analysis."""
    
    certification_id: int = Field(..., description="Certification ID")
    current_role_id: int = Field(..., description="Current role ID")
    target_role_id: Optional[int] = Field(None, description="Target role ID")
    investment_cost: float = Field(..., description="Total investment cost")
    expected_salary_increase: float = Field(..., description="Expected annual salary increase")
    payback_period_months: int = Field(..., description="Payback period in months")
    five_year_roi: float = Field(..., description="5-year ROI percentage")
    ten_year_roi: float = Field(..., description="10-year ROI percentage")
    risk_factors: List[str] = Field(..., description="Risk factors")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    market_conditions: Dict[str, Any] = Field(..., description="Market conditions")
    analysis_date: str = Field(..., description="Analysis date")
    location: str = Field(..., description="Analysis location")
    experience_years: int = Field(..., description="Experience years")
    
    class Config:
        schema_extra = {
            "example": {
                "certification_id": 1,
                "current_role_id": 5,
                "target_role_id": 8,
                "investment_cost": 3500.0,
                "expected_salary_increase": 18000.0,
                "payback_period_months": 2,
                "five_year_roi": 257.14,
                "ten_year_roi": 514.29,
                "risk_factors": [
                    "High certification difficulty may affect pass rate"
                ],
                "confidence_score": 0.85,
                "market_conditions": {
                    "market_health": "strong",
                    "job_availability": "high",
                    "salary_trend": "increasing"
                },
                "analysis_date": "2024-01-15T10:30:00Z",
                "location": "remote",
                "experience_years": 7
            }
        }


class RoleBenchmark(BaseModel):
    """Benchmark data for a specific role."""
    
    level: str = Field(..., description="Role level")
    salary_range: Dict[str, float] = Field(..., description="Salary range")
    market_position: str = Field(..., description="Market position")
    growth_potential: float = Field(..., description="Growth potential")


class MarketSummary(BaseModel):
    """Market summary statistics."""
    
    average_salary: float = Field(..., description="Average salary")
    median_salary: float = Field(..., description="Median salary")
    salary_range: Dict[str, float] = Field(..., description="Overall salary range")
    total_roles: int = Field(..., description="Total roles analyzed")
    market_health: str = Field(..., description="Market health indicator")


class SalaryBenchmarkResponse(BaseModel):
    """Response schema for salary benchmarks."""
    
    domain: str = Field(..., description="Career domain")
    location: str = Field(..., description="Location")
    location_multiplier: float = Field(..., description="Location multiplier")
    benchmarks: Dict[str, RoleBenchmark] = Field(..., description="Role benchmarks")
    market_summary: MarketSummary = Field(..., description="Market summary")
    generated_at: str = Field(..., description="Generation timestamp")


class RoleImpact(BaseModel):
    """Salary impact for a specific role."""
    
    base_salary: float = Field(..., description="Base salary without certification")
    with_certification: float = Field(..., description="Salary with certification")
    increase_amount: float = Field(..., description="Salary increase amount")
    increase_percentage: float = Field(..., description="Salary increase percentage")
    role_level: str = Field(..., description="Role level")


class CertificationImpactResponse(BaseModel):
    """Response schema for certification salary impact."""
    
    certification_id: int = Field(..., description="Certification ID")
    certification_name: str = Field(..., description="Certification name")
    average_salary_premium: float = Field(..., description="Average salary premium percentage")
    role_impacts: Dict[str, RoleImpact] = Field(..., description="Impact by role")
    market_demand: MarketDemandEnum = Field(..., description="Market demand level")
    investment_recommendation: str = Field(..., description="Investment recommendation")
    analysis_date: str = Field(..., description="Analysis date")
    
    class Config:
        schema_extra = {
            "example": {
                "certification_id": 1,
                "certification_name": "CISSP",
                "average_salary_premium": 15.0,
                "role_impacts": {
                    "Security Engineer": {
                        "base_salary": 120000,
                        "with_certification": 138000,
                        "increase_amount": 18000,
                        "increase_percentage": 15.0,
                        "role_level": "Senior"
                    }
                },
                "market_demand": "high",
                "investment_recommendation": "Highly recommended - strong ROI potential",
                "analysis_date": "2024-01-15T10:30:00Z"
            }
        }
