"""Pydantic schemas for Integration Hub API endpoints.

This module provides comprehensive request/response schemas for enterprise
integrations including SSO, LDAP, LMS, HR systems, and webhook integrations
with validation and documentation.
"""

from pydantic import BaseModel, Field, validator, HttpUrl, EmailStr
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum


class IntegrationType(str, Enum):
    """Integration type enumeration."""
    SSO_SAML = "sso_saml"
    SSO_OIDC = "sso_oidc"
    SSO_OAUTH2 = "sso_oauth2"
    LDAP = "ldap"
    ACTIVE_DIRECTORY = "active_directory"
    LMS_CANVAS = "lms_canvas"
    LMS_MOODLE = "lms_moodle"
    LMS_BLACKBOARD = "lms_blackboard"
    HR_WORKDAY = "hr_workday"
    HR_BAMBOO = "hr_bamboo"
    HR_ADPWORKFORCE = "hr_adpworkforce"
    CRM_SALESFORCE = "crm_salesforce"
    ERP_SAP = "erp_sap"
    SLACK = "slack"
    TEAMS = "teams"
    WEBHOOK = "webhook"


class SyncDirection(str, Enum):
    """Data synchronization direction."""
    INBOUND = "inbound"
    OUTBOUND = "outbound"
    BIDIRECTIONAL = "bidirectional"


class IntegrationStatus(str, Enum):
    """Integration status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"
    TESTING = "testing"


# SSO Integration Schemas

class SSOConfigRequest(BaseModel):
    """Schema for SSO configuration request."""
    type: IntegrationType = Field(..., description="SSO integration type")
    provider_name: str = Field(..., description="SSO provider name")
    entity_id: str = Field(..., description="SAML entity ID")
    sso_url: HttpUrl = Field(..., description="SSO login URL")
    slo_url: Optional[HttpUrl] = Field(None, description="Single logout URL")
    x509_certificate: str = Field(..., description="X.509 certificate for SAML")
    attribute_mapping: Dict[str, str] = Field(default_factory=dict, description="Attribute mapping")
    group_mapping: Dict[str, str] = Field(default_factory=dict, description="Group to role mapping")
    auto_provision_users: bool = Field(True, description="Auto-provision new users")
    default_role: str = Field("learner", description="Default role for new users")
    sync_frequency_hours: int = Field(24, ge=1, le=168, description="Sync frequency in hours")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "sso_saml",
                "provider_name": "Azure AD",
                "entity_id": "https://sts.windows.net/tenant-id/",
                "sso_url": "https://login.microsoftonline.com/tenant-id/saml2",
                "x509_certificate": "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----",
                "attribute_mapping": {
                    "email": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
                    "first_name": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname",
                    "last_name": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
                },
                "auto_provision_users": True,
                "default_role": "learner"
            }
        }


class SSOConfigResponse(BaseModel):
    """Schema for SSO configuration response."""
    integration_id: str = Field(..., description="Integration identifier")
    status: IntegrationStatus = Field(..., description="Integration status")
    provider_name: str = Field(..., description="SSO provider name")
    test_result: Dict[str, Any] = Field(..., description="Connection test result")
    metadata_url: str = Field(..., description="SAML metadata URL")
    acs_url: str = Field(..., description="Assertion Consumer Service URL")
    entity_id: str = Field(..., description="Service provider entity ID")
    auto_provision_enabled: bool = Field(..., description="Auto-provisioning status")


# LDAP Integration Schemas

class LDAPConfigRequest(BaseModel):
    """Schema for LDAP configuration request."""
    server_url: str = Field(..., description="LDAP server URL")
    bind_dn: str = Field(..., description="Bind DN for authentication")
    bind_password: str = Field(..., description="Bind password")
    user_base_dn: str = Field(..., description="User search base DN")
    group_base_dn: Optional[str] = Field(None, description="Group search base DN")
    user_filter: str = Field("(objectClass=person)", description="User search filter")
    group_filter: str = Field("(objectClass=group)", description="Group search filter")
    attribute_mapping: Dict[str, str] = Field(default_factory=dict, description="Attribute mapping")
    sync_direction: SyncDirection = Field(SyncDirection.INBOUND, description="Sync direction")
    sync_frequency_hours: int = Field(6, ge=1, le=168, description="Sync frequency in hours")
    auto_provision_users: bool = Field(True, description="Auto-provision new users")
    use_ssl: bool = Field(True, description="Use SSL/TLS connection")
    
    class Config:
        schema_extra = {
            "example": {
                "server_url": "ldaps://dc.company.com:636",
                "bind_dn": "CN=service-account,OU=Service Accounts,DC=company,DC=com",
                "bind_password": "secure_password",
                "user_base_dn": "OU=Users,DC=company,DC=com",
                "group_base_dn": "OU=Groups,DC=company,DC=com",
                "attribute_mapping": {
                    "email": "mail",
                    "first_name": "givenName",
                    "last_name": "sn",
                    "employee_id": "employeeID"
                },
                "auto_provision_users": True
            }
        }


class LDAPConfigResponse(BaseModel):
    """Schema for LDAP configuration response."""
    integration_id: str = Field(..., description="Integration identifier")
    status: IntegrationStatus = Field(..., description="Integration status")
    server_url: str = Field(..., description="LDAP server URL")
    test_result: Dict[str, Any] = Field(..., description="Connection test result")
    sync_direction: SyncDirection = Field(..., description="Sync direction")
    auto_provision_enabled: bool = Field(..., description="Auto-provisioning status")


# LMS Integration Schemas

class LMSConfigRequest(BaseModel):
    """Schema for LMS configuration request."""
    type: IntegrationType = Field(..., description="LMS integration type")
    lms_url: HttpUrl = Field(..., description="LMS base URL")
    api_key: str = Field(..., description="LMS API key")
    api_secret: Optional[str] = Field(None, description="LMS API secret")
    course_mapping: Dict[str, str] = Field(default_factory=dict, description="Course mapping")
    sync_direction: SyncDirection = Field(SyncDirection.BIDIRECTIONAL, description="Sync direction")
    sync_frequency_hours: int = Field(12, ge=1, le=168, description="Sync frequency in hours")
    grade_sync_enabled: bool = Field(True, description="Enable grade synchronization")
    enrollment_sync_enabled: bool = Field(True, description="Enable enrollment synchronization")
    assignment_sync_enabled: bool = Field(False, description="Enable assignment synchronization")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "lms_canvas",
                "lms_url": "https://company.instructure.com",
                "api_key": "canvas_api_key_here",
                "course_mapping": {
                    "cert_001": "canvas_course_123",
                    "cert_002": "canvas_course_456"
                },
                "grade_sync_enabled": True,
                "enrollment_sync_enabled": True
            }
        }


class LMSConfigResponse(BaseModel):
    """Schema for LMS configuration response."""
    integration_id: str = Field(..., description="Integration identifier")
    status: IntegrationStatus = Field(..., description="Integration status")
    lms_url: str = Field(..., description="LMS base URL")
    lms_type: IntegrationType = Field(..., description="LMS type")
    test_result: Dict[str, Any] = Field(..., description="Connection test result")
    sync_capabilities: Dict[str, bool] = Field(..., description="Sync capabilities")


# HR System Integration Schemas

class HRConfigRequest(BaseModel):
    """Schema for HR system configuration request."""
    type: IntegrationType = Field(..., description="HR system integration type")
    hr_system_url: HttpUrl = Field(..., description="HR system URL")
    username: str = Field(..., description="HR system username")
    password: str = Field(..., description="HR system password")
    tenant_id: Optional[str] = Field(None, description="Tenant ID (for cloud systems)")
    field_mapping: Dict[str, str] = Field(default_factory=dict, description="Field mapping")
    sync_direction: SyncDirection = Field(SyncDirection.INBOUND, description="Sync direction")
    sync_frequency_hours: int = Field(24, ge=1, le=168, description="Sync frequency in hours")
    employee_sync_enabled: bool = Field(True, description="Enable employee synchronization")
    org_structure_sync_enabled: bool = Field(True, description="Enable org structure sync")
    training_records_sync_enabled: bool = Field(False, description="Enable training records sync")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "hr_workday",
                "hr_system_url": "https://company.workday.com",
                "username": "integration_user",
                "password": "secure_password",
                "tenant_id": "company_tenant",
                "field_mapping": {
                    "employee_id": "Employee_ID",
                    "email": "Email_Address",
                    "department": "Cost_Center"
                },
                "employee_sync_enabled": True
            }
        }


class HRConfigResponse(BaseModel):
    """Schema for HR system configuration response."""
    integration_id: str = Field(..., description="Integration identifier")
    status: IntegrationStatus = Field(..., description="Integration status")
    hr_system_url: str = Field(..., description="HR system URL")
    hr_system_type: IntegrationType = Field(..., description="HR system type")
    test_result: Dict[str, Any] = Field(..., description="Connection test result")
    sync_capabilities: Dict[str, bool] = Field(..., description="Sync capabilities")


# Webhook Integration Schemas

class WebhookConfigRequest(BaseModel):
    """Schema for webhook configuration request."""
    webhook_url: HttpUrl = Field(..., description="Webhook endpoint URL")
    events: List[str] = Field(..., description="Events to subscribe to")
    secret_key: Optional[str] = Field(None, description="Webhook secret key")
    retry_attempts: int = Field(3, ge=0, le=10, description="Number of retry attempts")
    timeout_seconds: int = Field(30, ge=5, le=300, description="Request timeout in seconds")
    
    class Config:
        schema_extra = {
            "example": {
                "webhook_url": "https://company.com/webhooks/certpathfinder",
                "events": ["user.created", "user.updated", "certification.completed"],
                "secret_key": "webhook_secret_123",
                "retry_attempts": 3,
                "timeout_seconds": 30
            }
        }


class WebhookConfigResponse(BaseModel):
    """Schema for webhook configuration response."""
    webhook_id: str = Field(..., description="Webhook identifier")
    organization_id: int = Field(..., description="Organization ID")
    webhook_url: str = Field(..., description="Webhook endpoint URL")
    events: List[str] = Field(..., description="Subscribed events")
    status: str = Field(..., description="Webhook status")
    secret_key: str = Field(..., description="Webhook secret key")
    created_at: str = Field(..., description="Creation timestamp")


# Sync and Status Schemas

class SyncRequest(BaseModel):
    """Schema for synchronization request."""
    data_types: List[str] = Field(..., description="Data types to synchronize")
    force_full_sync: bool = Field(False, description="Force full synchronization")
    
    class Config:
        schema_extra = {
            "example": {
                "data_types": ["users", "groups", "courses"],
                "force_full_sync": False
            }
        }


class SyncResponse(BaseModel):
    """Schema for synchronization response."""
    sync_id: str = Field(..., description="Synchronization identifier")
    status: str = Field(..., description="Sync status")
    message: str = Field(..., description="Status message")
    started_at: str = Field(..., description="Sync start timestamp")


class IntegrationStatusResponse(BaseModel):
    """Schema for integration status response."""
    integration_id: str = Field(..., description="Integration identifier")
    status: IntegrationStatus = Field(..., description="Integration status")
    last_sync: Optional[str] = Field(None, description="Last sync timestamp")
    next_sync: Optional[str] = Field(None, description="Next scheduled sync")
    sync_frequency_hours: int = Field(..., description="Sync frequency in hours")
    total_syncs: int = Field(..., description="Total number of syncs")
    successful_syncs: int = Field(..., description="Number of successful syncs")
    failed_syncs: int = Field(..., description="Number of failed syncs")
    last_error: Optional[str] = Field(None, description="Last error message")
    health_score: float = Field(..., description="Integration health score (0-100)")
    performance_metrics: Dict[str, Any] = Field(..., description="Performance metrics")


class IntegrationSummary(BaseModel):
    """Schema for integration summary."""
    integration_id: str = Field(..., description="Integration identifier")
    integration_type: IntegrationType = Field(..., description="Integration type")
    provider_name: str = Field(..., description="Provider name")
    status: IntegrationStatus = Field(..., description="Integration status")
    last_sync: Optional[str] = Field(None, description="Last sync timestamp")
    created_at: str = Field(..., description="Creation timestamp")


class IntegrationListResponse(BaseModel):
    """Schema for integration list response."""
    organization_id: int = Field(..., description="Organization ID")
    integrations: List[IntegrationSummary] = Field(..., description="List of integrations")
    total_count: int = Field(..., description="Total number of integrations")


class IntegrationHealthResponse(BaseModel):
    """Schema for integration hub health response."""
    status: str = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    supported_integrations: List[str] = Field(..., description="Supported integration types")
    active_integrations: int = Field(..., description="Number of active integrations")
    total_syncs_today: int = Field(..., description="Total syncs performed today")
    successful_syncs_today: int = Field(..., description="Successful syncs today")
    failed_syncs_today: int = Field(..., description="Failed syncs today")
    avg_sync_duration_seconds: float = Field(..., description="Average sync duration")
    timestamp: str = Field(..., description="Health check timestamp")


# Error Schemas

class IntegrationErrorResponse(BaseModel):
    """Schema for integration error response."""
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Error message")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    integration_id: Optional[str] = Field(None, description="Integration identifier")
    timestamp: str = Field(..., description="Error timestamp")
    support_contact: Optional[str] = Field(None, description="Support contact information")


# Validation Schemas

class ConnectionTestResult(BaseModel):
    """Schema for connection test result."""
    success: bool = Field(..., description="Test success status")
    response_time_ms: Optional[int] = Field(None, description="Response time in milliseconds")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    test_details: Dict[str, Any] = Field(default_factory=dict, description="Detailed test results")
    timestamp: str = Field(..., description="Test timestamp")


class AttributeMapping(BaseModel):
    """Schema for attribute mapping configuration."""
    source_attribute: str = Field(..., description="Source system attribute name")
    target_attribute: str = Field(..., description="Target system attribute name")
    transformation: Optional[str] = Field(None, description="Data transformation rule")
    required: bool = Field(False, description="Whether attribute is required")


class SyncConfiguration(BaseModel):
    """Schema for synchronization configuration."""
    enabled: bool = Field(True, description="Whether sync is enabled")
    direction: SyncDirection = Field(..., description="Sync direction")
    frequency_hours: int = Field(24, ge=1, le=168, description="Sync frequency in hours")
    batch_size: int = Field(100, ge=1, le=1000, description="Sync batch size")
    retry_attempts: int = Field(3, ge=0, le=10, description="Number of retry attempts")
    timeout_seconds: int = Field(300, ge=30, le=3600, description="Sync timeout in seconds")
