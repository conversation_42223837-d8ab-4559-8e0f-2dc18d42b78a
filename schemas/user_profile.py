"""Pydantic schemas for user profile API"""
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, validator

class UserProfileBase(BaseModel):
    """Base schema for user profile data"""
    years_experience: int = Field(..., ge=0, le=50, description="Years of experience in security")
    user_role: str = Field(..., min_length=1, description="Current job role")
    desired_role: str = Field(..., min_length=1, description="Target career role")
    expertise_areas: List[str] = Field(..., min_items=1, description="List of expertise areas")
    preferred_learning_style: str = Field(..., description="Preferred learning style")
    study_time_available: int = Field(..., ge=1, le=168, description="Available study hours per week")
    tutorial_completed: Optional[bool] = Field(default=False, description="Whether tutorial is completed")

    @validator('preferred_learning_style')
    def validate_learning_style(cls, v):
        valid_styles = ['Visual', 'Reading/Writing', 'Auditory', 'Hands-on']
        if v not in valid_styles:
            raise ValueError(f'Learning style must be one of: {", ".join(valid_styles)}')
        return v

class UserProfileCreate(UserProfileBase):
    """Schema for creating a new user profile"""
    pass

class UserProfileUpdate(UserProfileBase):
    """Schema for updating an existing user profile"""
    years_experience: Optional[int] = None
    user_role: Optional[str] = None
    desired_role: Optional[str] = None
    expertise_areas: Optional[List[str]] = None
    preferred_learning_style: Optional[str] = None
    study_time_available: Optional[int] = None

class UserProfileSync(BaseModel):
    """Schema for profile synchronization"""
    last_sync: datetime
    changes: List[str] = Field(..., description="List of fields that were changed")
    force: bool = Field(default=False, description="Force sync even if conflict exists")

class UserProfileResponse(UserProfileBase):
    """Schema for profile response including metadata"""
    id: int
    user_id: str
    created_at: datetime
    last_updated: datetime
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None

    class Config:
        orm_mode = True
