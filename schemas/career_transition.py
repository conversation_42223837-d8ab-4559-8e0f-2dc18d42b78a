"""Pydantic schemas for career transition API endpoints.

This module provides comprehensive request/response schemas for career transition
planning, pathfinding, and budget-aware career planning functionality.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class DifficultyLevel(str, Enum):
    """Enumeration of difficulty levels for career transitions."""
    EASY = "Easy"
    MEDIUM = "Medium"
    HARD = "Hard"
    EXPERT = "Expert"


class LearningStyle(str, Enum):
    """Enumeration of learning styles."""
    VISUAL = "Visual"
    READING = "Reading"
    HANDS_ON = "Hands-on"
    MIXED = "Mixed"


class CareerLevel(str, Enum):
    """Enumeration of career levels."""
    ENTRY = "Entry"
    JUNIOR = "Junior"
    MID = "Mid"
    SENIOR = "Senior"
    LEAD = "Lead"
    PRINCIPAL = "Principal"
    EXECUTIVE = "Executive"


class MarketDemand(str, Enum):
    """Enumeration of market demand levels."""
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"
    VERY_HIGH = "Very High"


class GrowthOutlook(str, Enum):
    """Enumeration of growth outlook options."""
    DECLINING = "Declining"
    STABLE = "Stable"
    GROWING = "Growing"
    BOOMING = "Booming"


class TransitionStatus(str, Enum):
    """Enumeration of transition plan statuses."""
    PLANNING = "planning"
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class StepStatus(str, Enum):
    """Enumeration of transition step statuses."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"


# Request Schemas

class CareerPathfindingRequest(BaseModel):
    """Request schema for career pathfinding."""
    
    current_role_id: Optional[int] = Field(None, description="Current career role ID (null for entry-level)")
    target_role_id: int = Field(..., description="Target career role ID")
    
    # Budget constraints
    max_budget: Optional[float] = Field(None, ge=0, description="Maximum budget for transition")
    currency: str = Field("USD", description="Currency for budget calculations")
    
    # Timeline constraints
    max_timeline_months: Optional[int] = Field(None, ge=1, le=120, description="Maximum timeline in months")
    
    # Preferences
    max_difficulty: DifficultyLevel = Field(DifficultyLevel.EXPERT, description="Maximum acceptable difficulty")
    learning_style: LearningStyle = Field(LearningStyle.MIXED, description="Preferred learning style")
    study_hours_per_week: int = Field(10, ge=1, le=80, description="Available study hours per week")
    
    # Options
    max_paths: int = Field(5, ge=1, le=20, description="Maximum number of paths to return")
    include_indirect_paths: bool = Field(True, description="Include multi-step indirect paths")
    
    class Config:
        schema_extra = {
            "example": {
                "current_role_id": 1,
                "target_role_id": 5,
                "max_budget": 5000.0,
                "currency": "USD",
                "max_timeline_months": 18,
                "max_difficulty": "Hard",
                "learning_style": "Mixed",
                "study_hours_per_week": 15,
                "max_paths": 5,
                "include_indirect_paths": True
            }
        }


class CareerTransitionPlanCreate(BaseModel):
    """Request schema for creating a career transition plan."""
    
    name: str = Field(..., min_length=1, max_length=200, description="Plan name")
    description: Optional[str] = Field(None, max_length=1000, description="Plan description")
    
    current_role_id: Optional[int] = Field(None, description="Current career role ID")
    target_role_id: int = Field(..., description="Target career role ID")
    
    # Budget constraints
    budget_min: float = Field(0.0, ge=0, description="Minimum budget available")
    budget_max: Optional[float] = Field(None, ge=0, description="Maximum budget available")
    currency: str = Field("USD", description="Budget currency")
    
    # Timeline preferences
    desired_timeline_months: Optional[int] = Field(None, ge=1, le=120, description="Desired completion timeline")
    max_timeline_months: Optional[int] = Field(None, ge=1, le=120, description="Maximum acceptable timeline")
    
    # Learning preferences
    difficulty_preference: DifficultyLevel = Field(DifficultyLevel.MEDIUM, description="Preferred difficulty level")
    learning_style: LearningStyle = Field(LearningStyle.MIXED, description="Preferred learning style")
    study_hours_per_week: int = Field(10, ge=1, le=80, description="Available study hours per week")
    
    @validator('budget_max')
    def validate_budget_max(cls, v, values):
        if v is not None and 'budget_min' in values and v < values['budget_min']:
            raise ValueError('budget_max must be greater than or equal to budget_min')
        return v
    
    @validator('max_timeline_months')
    def validate_max_timeline(cls, v, values):
        if v is not None and 'desired_timeline_months' in values and values['desired_timeline_months'] is not None:
            if v < values['desired_timeline_months']:
                raise ValueError('max_timeline_months must be greater than or equal to desired_timeline_months')
        return v


class CareerTransitionPlanUpdate(BaseModel):
    """Request schema for updating a career transition plan."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    
    budget_max: Optional[float] = Field(None, ge=0)
    desired_timeline_months: Optional[int] = Field(None, ge=1, le=120)
    max_timeline_months: Optional[int] = Field(None, ge=1, le=120)
    
    difficulty_preference: Optional[DifficultyLevel] = None
    learning_style: Optional[LearningStyle] = None
    study_hours_per_week: Optional[int] = Field(None, ge=1, le=80)
    
    status: Optional[TransitionStatus] = None


class StepProgressUpdate(BaseModel):
    """Request schema for updating step progress."""
    
    progress_percentage: float = Field(..., ge=0, le=100, description="Progress percentage (0-100)")
    notes: Optional[str] = Field(None, max_length=500, description="Progress notes")


# Response Schemas

class SalaryRange(BaseModel):
    """Salary range information."""
    min: Optional[float] = None
    max: Optional[float] = None
    currency: str = "USD"


class CareerRoleResponse(BaseModel):
    """Response schema for career role information."""
    
    id: int
    title: str
    description: Optional[str] = None
    domain: str
    level: str
    
    min_years_experience: int = 0
    max_years_experience: Optional[int] = None
    
    salary_range: SalaryRange
    
    required_skills: List[str] = []
    preferred_skills: List[str] = []
    required_certifications: List[str] = []
    preferred_certifications: List[str] = []
    
    market_demand: str = "Medium"
    growth_outlook: str = "Stable"
    
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class CostEstimate(BaseModel):
    """Cost estimate information."""
    min: float = 0.0
    max: float = 0.0
    currency: str = "USD"


class DurationEstimate(BaseModel):
    """Duration estimate information."""
    estimated_months: int
    min_months: Optional[int] = None
    max_months: Optional[int] = None


class PathStepResponse(BaseModel):
    """Response schema for a transition path step."""
    
    name: str
    description: Optional[str] = None
    step_type: str
    duration_months: int
    cost: float
    certifications: List[int] = []
    prerequisites: List[str] = []


class CareerPathOptionResponse(BaseModel):
    """Response schema for a career path option."""
    
    path_id: str
    name: str
    description: Optional[str] = None
    
    total_cost: float
    total_duration_months: int
    difficulty_level: str
    success_probability: float
    
    steps: List[PathStepResponse]
    certifications_required: List[int]
    estimated_salary_increase: float
    
    # Ranking metrics
    cost_score: float
    time_score: float
    difficulty_score: float
    overall_score: float


class CareerPathfindingResponse(BaseModel):
    """Response schema for career pathfinding results."""
    
    source_role: Optional[CareerRoleResponse] = None
    target_role: CareerRoleResponse
    
    path_options: List[CareerPathOptionResponse]
    
    # Summary statistics
    total_paths_found: int
    cost_range: CostEstimate
    duration_range: DurationEstimate
    
    # Recommendations
    recommended_path_id: Optional[str] = None
    recommendations: List[str] = []
    
    generated_at: datetime


class TransitionStepResponse(BaseModel):
    """Response schema for a transition step."""
    
    id: int
    name: str
    description: Optional[str] = None
    step_type: str
    sequence: int
    
    certification_id: Optional[int] = None
    estimated_duration_weeks: Optional[int] = None
    estimated_cost: float = 0.0
    cost_currency: str = "USD"
    
    status: str = "pending"
    progress_percentage: float = 0.0
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    prerequisite_steps: List[int] = []
    
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class BudgetInfo(BaseModel):
    """Budget information."""
    min: float = 0.0
    max: Optional[float] = None
    currency: str = "USD"


class TimelineInfo(BaseModel):
    """Timeline information."""
    desired_months: Optional[int] = None
    max_months: Optional[int] = None


class LearningPreferences(BaseModel):
    """Learning preferences information."""
    difficulty: str = "Medium"
    learning_style: str = "Mixed"
    study_hours_per_week: int = 10


class CareerTransitionPlanResponse(BaseModel):
    """Response schema for a career transition plan."""
    
    id: int
    user_id: str
    name: str
    description: Optional[str] = None
    
    current_role_id: Optional[int] = None
    target_role_id: int
    
    budget: BudgetInfo
    timeline: TimelineInfo
    preferences: LearningPreferences
    
    status: str = "planning"
    progress_percentage: float = 0.0
    
    selected_path_id: Optional[int] = None
    alternative_paths: List[str] = []
    
    # Related data
    current_role: Optional[CareerRoleResponse] = None
    target_role: Optional[CareerRoleResponse] = None
    steps: List[TransitionStepResponse] = []
    
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class CareerTransitionSummary(BaseModel):
    """Summary information for career transitions."""
    
    total_plans: int = 0
    active_plans: int = 0
    completed_plans: int = 0
    
    total_budget_allocated: float = 0.0
    total_budget_spent: float = 0.0
    
    average_completion_time_months: Optional[float] = None
    success_rate: Optional[float] = None
    
    popular_target_roles: List[Dict[str, Any]] = []
    trending_certifications: List[Dict[str, Any]] = []
