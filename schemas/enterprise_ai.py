"""Pydantic schemas for Enterprise AI API endpoints.

This module provides comprehensive request/response schemas for enterprise AI
capabilities including predictive analytics, optimization, insights, and
machine learning-powered recommendations.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class RiskLevel(str, Enum):
    """Risk level enumeration."""
    VERY_LOW = "Very Low"
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"
    VERY_HIGH = "Very High"
    CRITICAL = "Critical"


class ChurnRiskCategory(str, Enum):
    """Churn risk category enumeration."""
    VERY_LOW = "Very Low"
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"
    CRITICAL = "Critical"


class TrendDirection(str, Enum):
    """Trend direction enumeration."""
    INCREASING = "increasing"
    DECREASING = "decreasing"
    STABLE = "stable"


class AnomalySeverity(str, Enum):
    """Anomaly severity enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# Request Schemas

class PredictionRequest(BaseModel):
    """Schema for prediction requests."""
    features: Dict[str, Any] = Field(..., description="Feature values for prediction")
    context: Optional[str] = Field(None, description="Additional context for prediction")
    
    class Config:
        schema_extra = {
            "example": {
                "features": {
                    "study_hours_per_week": 10,
                    "consistency_score": 0.8,
                    "previous_test_scores": [75, 82, 78],
                    "engagement_level": 0.7
                },
                "context": "certification_preparation"
            }
        }


class OptimizationRequest(BaseModel):
    """Schema for optimization requests."""
    optimization_type: str = Field(..., description="Type of optimization")
    constraints: Optional[Dict[str, Any]] = Field(None, description="Optimization constraints")
    objectives: Optional[List[str]] = Field(None, description="Optimization objectives")
    
    class Config:
        schema_extra = {
            "example": {
                "optimization_type": "license_allocation",
                "constraints": {
                    "max_budget": 50000,
                    "min_utilization": 0.8
                },
                "objectives": ["minimize_cost", "maximize_utilization"]
            }
        }


class InsightRequest(BaseModel):
    """Schema for insight generation requests."""
    categories: Optional[List[str]] = Field(None, description="Insight categories to generate")
    time_period_days: Optional[int] = Field(30, description="Time period for analysis")
    include_predictions: Optional[bool] = Field(True, description="Include predictive insights")
    
    class Config:
        schema_extra = {
            "example": {
                "categories": ["performance", "engagement", "efficiency"],
                "time_period_days": 30,
                "include_predictions": True
            }
        }


# Response Schemas

class UserSuccessPredictionResponse(BaseModel):
    """Schema for user success prediction response."""
    success_probability: float = Field(..., ge=0.0, le=1.0, description="Probability of success")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Prediction confidence")
    risk_level: RiskLevel = Field(..., description="Risk level category")
    insights: List[str] = Field(default_factory=list, description="AI-generated insights")
    recommendations: List[str] = Field(default_factory=list, description="Improvement recommendations")
    
    class Config:
        schema_extra = {
            "example": {
                "success_probability": 0.82,
                "confidence_score": 0.89,
                "risk_level": "Low",
                "insights": [
                    "User shows strong study consistency",
                    "Performance trend is positive"
                ],
                "recommendations": [
                    "Continue current study schedule",
                    "Focus on weak areas identified in practice tests"
                ]
            }
        }


class ChurnRiskPredictionResponse(BaseModel):
    """Schema for churn risk prediction response."""
    churn_risk: float = Field(..., ge=0.0, le=1.0, description="Churn risk probability")
    risk_category: ChurnRiskCategory = Field(..., description="Risk category")
    estimated_time_to_churn_days: int = Field(..., description="Estimated days until churn")
    key_risk_factors: List[str] = Field(default_factory=list, description="Key risk factors")
    intervention_recommendations: List[str] = Field(default_factory=list, description="Intervention recommendations")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Prediction confidence")
    
    class Config:
        schema_extra = {
            "example": {
                "churn_risk": 0.35,
                "risk_category": "Medium",
                "estimated_time_to_churn_days": 45,
                "key_risk_factors": [
                    "Declining session frequency",
                    "Lower engagement scores"
                ],
                "intervention_recommendations": [
                    "Send personalized re-engagement email",
                    "Offer additional support resources"
                ],
                "confidence_score": 0.76
            }
        }


class MetricForecast(BaseModel):
    """Schema for individual metric forecast."""
    metric: str = Field(..., description="Metric name")
    forecast_values: List[float] = Field(..., description="Forecasted values")
    forecast_dates: List[str] = Field(..., description="Forecast dates")
    trend: TrendDirection = Field(..., description="Trend direction")
    trend_strength: float = Field(..., description="Strength of trend")


class PerformanceForecastResponse(BaseModel):
    """Schema for performance forecast response."""
    forecasts: Dict[str, MetricForecast] = Field(..., description="Metric forecasts")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Forecast confidence")
    insights: List[str] = Field(default_factory=list, description="Forecast insights")
    recommendations: List[str] = Field(default_factory=list, description="Action recommendations")
    forecast_period_days: int = Field(..., description="Forecast period in days")
    generated_at: str = Field(..., description="Generation timestamp")


class OptimizationRecommendation(BaseModel):
    """Schema for optimization recommendation."""
    type: str = Field(..., description="Recommendation type")
    description: str = Field(..., description="Recommendation description")
    impact: str = Field(..., description="Expected impact")
    effort: str = Field(..., description="Implementation effort")
    priority: int = Field(..., ge=1, le=5, description="Priority level")


class LicenseOptimizationResponse(BaseModel):
    """Schema for license optimization response."""
    current_efficiency: float = Field(..., ge=0.0, le=1.0, description="Current allocation efficiency")
    optimization_recommendations: List[OptimizationRecommendation] = Field(..., description="Optimization recommendations")
    potential_cost_savings: float = Field(..., description="Potential cost savings")
    optimal_allocation: Dict[str, Any] = Field(..., description="Optimal license allocation")
    implementation_priority: List[str] = Field(..., description="Implementation priority order")
    roi_estimate: float = Field(..., description="Return on investment estimate")


class ResourceOptimizationResponse(BaseModel):
    """Schema for resource optimization response."""
    current_utilization: Dict[str, float] = Field(..., description="Current resource utilization")
    optimization_strategies: List[OptimizationRecommendation] = Field(..., description="Optimization strategies")
    efficiency_improvements: Dict[str, float] = Field(..., description="Efficiency improvement estimates")
    implementation_roadmap: List[Dict[str, Any]] = Field(..., description="Implementation roadmap")
    success_metrics: List[str] = Field(..., description="Success measurement metrics")


class InsightItem(BaseModel):
    """Schema for individual insight."""
    category: str = Field(..., description="Insight category")
    title: str = Field(..., description="Insight title")
    description: str = Field(..., description="Insight description")
    impact_score: float = Field(..., ge=0.0, le=1.0, description="Impact score")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence level")
    evidence: List[str] = Field(default_factory=list, description="Supporting evidence")
    recommendations: List[str] = Field(default_factory=list, description="Action recommendations")


class AutomatedInsightsResponse(BaseModel):
    """Schema for automated insights response."""
    insights: List[InsightItem] = Field(..., description="Generated insights")
    action_recommendations: List[str] = Field(..., description="Priority action recommendations")
    insight_summary: str = Field(..., description="Executive summary of insights")
    confidence_scores: Dict[str, float] = Field(..., description="Confidence scores by category")
    generated_at: str = Field(..., description="Generation timestamp")


class AnomalyRecord(BaseModel):
    """Schema for anomaly record."""
    record_index: int = Field(..., description="Record index")
    anomaly_score: float = Field(..., description="Anomaly score")
    data: Dict[str, Any] = Field(..., description="Anomalous data")
    severity: AnomalySeverity = Field(..., description="Anomaly severity")


class AnomalyDetectionResponse(BaseModel):
    """Schema for anomaly detection response."""
    anomalies_detected: int = Field(..., description="Number of anomalies detected")
    anomalous_records: List[AnomalyRecord] = Field(..., description="Anomalous records")
    explanations: List[str] = Field(..., description="Anomaly explanations")
    recommendations: List[str] = Field(..., description="Recommended actions")
    detection_confidence: float = Field(..., ge=0.0, le=1.0, description="Detection confidence")


class UserSegmentProfile(BaseModel):
    """Schema for user segment profile."""
    segment_id: int = Field(..., description="Segment identifier")
    segment_name: str = Field(..., description="Segment name")
    user_count: int = Field(..., description="Number of users in segment")
    characteristics: Dict[str, Any] = Field(..., description="Segment characteristics")
    performance_metrics: Dict[str, float] = Field(..., description="Performance metrics")
    recommended_strategies: List[str] = Field(..., description="Recommended strategies")


class UserSegmentationResponse(BaseModel):
    """Schema for user segmentation response."""
    total_segments: int = Field(..., description="Total number of segments")
    segment_profiles: List[UserSegmentProfile] = Field(..., description="Segment profiles")
    segment_strategies: Dict[str, List[str]] = Field(..., description="Strategies by segment")
    segmentation_quality: float = Field(..., ge=0.0, le=1.0, description="Segmentation quality score")
    user_assignments: Dict[str, int] = Field(..., description="User to segment assignments")


class ModelTrainingResult(BaseModel):
    """Schema for model training result."""
    model_name: str = Field(..., description="Model name")
    training_status: str = Field(..., description="Training status")
    accuracy: Optional[float] = Field(None, description="Model accuracy")
    training_time_seconds: Optional[float] = Field(None, description="Training time")
    data_points_used: Optional[int] = Field(None, description="Number of data points used")


class ModelTrainingResponse(BaseModel):
    """Schema for model training response."""
    training_results: Dict[str, ModelTrainingResult] = Field(..., description="Training results by model")
    overall_success_rate: float = Field(..., ge=0.0, le=1.0, description="Overall training success rate")
    models_updated: List[str] = Field(..., description="List of updated models")
    training_date: str = Field(..., description="Training completion date")
    next_training_recommended: str = Field(..., description="Next recommended training date")


class AIHealthResponse(BaseModel):
    """Schema for AI health check response."""
    status: str = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    features: List[str] = Field(..., description="Available features")
    model_status: Dict[str, str] = Field(..., description="Status of individual models")
    last_training_date: str = Field(..., description="Last training date")
    next_training_scheduled: str = Field(..., description="Next scheduled training")
    timestamp: str = Field(..., description="Health check timestamp")


# Validation Schemas

class FeatureValidation(BaseModel):
    """Schema for feature validation."""
    feature_name: str = Field(..., description="Feature name")
    feature_type: str = Field(..., description="Feature data type")
    min_value: Optional[float] = Field(None, description="Minimum allowed value")
    max_value: Optional[float] = Field(None, description="Maximum allowed value")
    required: bool = Field(True, description="Whether feature is required")


class ModelConfiguration(BaseModel):
    """Schema for model configuration."""
    model_type: str = Field(..., description="Model type")
    hyperparameters: Dict[str, Any] = Field(..., description="Model hyperparameters")
    training_config: Dict[str, Any] = Field(..., description="Training configuration")
    validation_config: Dict[str, Any] = Field(..., description="Validation configuration")


# Utility Schemas

class PredictionExplanation(BaseModel):
    """Schema for prediction explanation."""
    feature_contributions: Dict[str, float] = Field(..., description="Feature contributions to prediction")
    confidence_factors: List[str] = Field(..., description="Factors affecting confidence")
    similar_cases: List[Dict[str, Any]] = Field(..., description="Similar historical cases")
    uncertainty_sources: List[str] = Field(..., description="Sources of prediction uncertainty")


class OptimizationConstraint(BaseModel):
    """Schema for optimization constraint."""
    constraint_type: str = Field(..., description="Constraint type")
    constraint_value: Any = Field(..., description="Constraint value")
    constraint_operator: str = Field(..., description="Constraint operator (>=, <=, ==)")
    priority: int = Field(..., ge=1, le=5, description="Constraint priority")


class InsightEvidence(BaseModel):
    """Schema for insight evidence."""
    evidence_type: str = Field(..., description="Type of evidence")
    data_source: str = Field(..., description="Data source")
    statistical_significance: float = Field(..., description="Statistical significance")
    sample_size: int = Field(..., description="Sample size")
    confidence_interval: List[float] = Field(..., description="Confidence interval")


# Error Schemas

class AIErrorResponse(BaseModel):
    """Schema for AI service error response."""
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Error message")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: str = Field(..., description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request identifier")
