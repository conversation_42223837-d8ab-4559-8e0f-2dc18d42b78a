"""Pydantic schemas for AI Study Assistant API endpoints.

This module provides comprehensive request/response schemas for AI-powered
study recommendations, learning insights, adaptive paths, and intelligent assistance.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class RecommendationType(str, Enum):
    """Enumeration of recommendation types."""
    TOPIC = "topic"
    SESSION_LENGTH = "session_length"
    BREAK = "break"
    REVIEW = "review"
    PRACTICE = "practice"
    TECHNIQUE = "technique"
    CONSISTENCY = "consistency"


class InsightCategory(str, Enum):
    """Enumeration of insight categories."""
    PERFORMANCE = "performance"
    EFFICIENCY = "efficiency"
    CONSISTENCY = "consistency"
    OPTIMIZATION = "optimization"


class InsightType(str, Enum):
    """Enumeration of insight types."""
    STRENGTH = "strength"
    WEAKNESS = "weakness"
    OPPORTUNITY = "opportunity"
    TREND = "trend"


class DifficultyLevel(str, Enum):
    """Enumeration of difficulty levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"


class QuestionType(str, Enum):
    """Enumeration of question types."""
    MULTIPLE_CHOICE = "multiple_choice"
    TRUE_FALSE = "true_false"
    SCENARIO = "scenario"
    DEFINITION = "definition"
    FILL_BLANK = "fill_blank"


# Request Schemas

class AdaptivePathRequest(BaseModel):
    """Request schema for generating adaptive learning paths."""
    
    certification_id: int = Field(..., description="Target certification ID")
    target_date: Optional[str] = Field(None, description="Target completion date (ISO format)")
    preferred_difficulty: Optional[DifficultyLevel] = Field(None, description="Preferred difficulty level")
    study_hours_per_week: Optional[int] = Field(None, ge=1, le=40, description="Available study hours per week")
    learning_style: Optional[str] = Field(None, description="Preferred learning style")
    
    class Config:
        schema_extra = {
            "example": {
                "certification_id": 1,
                "target_date": "2024-06-01T00:00:00Z",
                "preferred_difficulty": "intermediate",
                "study_hours_per_week": 10,
                "learning_style": "visual"
            }
        }


class GenerateQuestionsRequest(BaseModel):
    """Request schema for generating practice questions."""
    
    topic: str = Field(..., min_length=1, max_length=200, description="Topic for questions")
    difficulty: DifficultyLevel = Field(DifficultyLevel.INTERMEDIATE, description="Question difficulty level")
    count: int = Field(5, ge=1, le=20, description="Number of questions to generate")
    question_types: Optional[List[QuestionType]] = Field(None, description="Preferred question types")
    
    class Config:
        schema_extra = {
            "example": {
                "topic": "Network Security",
                "difficulty": "intermediate",
                "count": 5,
                "question_types": ["multiple_choice", "scenario"]
            }
        }


class StudyFeedbackRequest(BaseModel):
    """Request schema for study session feedback."""
    
    session_data: Dict[str, Any] = Field(..., description="Study session data")
    
    @validator('session_data')
    def validate_session_data(cls, v):
        required_fields = ['session_type', 'duration_minutes']
        for field in required_fields:
            if field not in v:
                raise ValueError(f'Missing required field: {field}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "session_data": {
                    "session_type": "reading",
                    "duration_minutes": 60,
                    "focus_rating": 4,
                    "effectiveness_rating": 4,
                    "confidence_level": 3,
                    "difficulty_rating": 3,
                    "progress_before": 65.0,
                    "progress_after": 75.0,
                    "interruptions_count": 1,
                    "notes": "Studied network protocols"
                }
            }
        }


# Response Schemas

class StudyRecommendationResponse(BaseModel):
    """Response schema for study recommendations."""
    
    type: str = Field(..., description="Type of recommendation")
    priority: int = Field(..., ge=1, le=5, description="Priority level (1-5)")
    title: str = Field(..., description="Recommendation title")
    description: str = Field(..., description="Detailed description")
    reasoning: str = Field(..., description="AI reasoning behind recommendation")
    estimated_time_minutes: int = Field(..., ge=1, description="Estimated time to complete")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="AI confidence in recommendation")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "topic",
                "priority": 4,
                "title": "Focus on Network Security",
                "description": "Your recent test scores show weakness in network security concepts",
                "reasoning": "Scored below 70% on network security questions in last 3 tests",
                "estimated_time_minutes": 90,
                "confidence_score": 0.85,
                "metadata": {
                    "topic": "Network Security",
                    "avg_score": 65.5,
                    "test_count": 3
                }
            }
        }


class LearningInsightResponse(BaseModel):
    """Response schema for learning insights."""
    
    category: str = Field(..., description="Insight category")
    insight_type: str = Field(..., description="Type of insight")
    title: str = Field(..., description="Insight title")
    description: str = Field(..., description="Detailed description")
    evidence: List[str] = Field(..., description="Supporting evidence")
    actionable_steps: List[str] = Field(..., description="Recommended actions")
    impact_score: float = Field(..., ge=0.0, le=1.0, description="Potential impact score")
    
    class Config:
        schema_extra = {
            "example": {
                "category": "performance",
                "insight_type": "opportunity",
                "title": "Inconsistent Test Performance",
                "description": "Your test scores vary significantly, indicating knowledge gaps",
                "evidence": [
                    "Score range: 45% - 85%",
                    "Standard deviation: 18.5%"
                ],
                "actionable_steps": [
                    "Review fundamental concepts",
                    "Practice more consistently",
                    "Focus on weak areas"
                ],
                "impact_score": 0.8
            }
        }


class TopicResponse(BaseModel):
    """Response schema for learning path topics."""
    
    name: str = Field(..., description="Topic name")
    description: str = Field(..., description="Topic description")
    difficulty: str = Field(..., description="Difficulty level")
    estimated_hours: int = Field(..., ge=1, description="Estimated study hours")
    priority: int = Field(..., ge=1, le=5, description="Priority level")
    prerequisites: List[str] = Field(default_factory=list, description="Required prerequisites")
    learning_objectives: List[str] = Field(default_factory=list, description="Learning objectives")


class AdaptivePathResponse(BaseModel):
    """Response schema for adaptive learning paths."""
    
    path_id: str = Field(..., description="Unique path identifier")
    name: str = Field(..., description="Path name")
    description: str = Field(..., description="Path description")
    difficulty_level: str = Field(..., description="Overall difficulty level")
    estimated_duration_weeks: int = Field(..., ge=1, description="Estimated duration in weeks")
    topics: List[Dict[str, Any]] = Field(..., description="Ordered list of topics")
    prerequisites: List[str] = Field(default_factory=list, description="Path prerequisites")
    success_probability: float = Field(..., ge=0.0, le=1.0, description="Estimated success probability")
    personalization_factors: Dict[str, Any] = Field(default_factory=dict, description="Personalization details")
    
    class Config:
        schema_extra = {
            "example": {
                "path_id": "adaptive_user1_cert1_20240115",
                "name": "Personalized Security+ Path",
                "description": "Adaptive learning path tailored to your learning style and schedule",
                "difficulty_level": "intermediate",
                "estimated_duration_weeks": 8,
                "topics": [
                    {
                        "name": "Network Security",
                        "description": "Study network security concepts",
                        "difficulty": "intermediate",
                        "estimated_hours": 12,
                        "priority": 5
                    }
                ],
                "prerequisites": ["Basic networking knowledge"],
                "success_probability": 0.78,
                "personalization_factors": {
                    "learning_style": "visual",
                    "optimal_session_length": 60,
                    "best_study_times": ["Morning", "Evening"]
                }
            }
        }


class PracticeQuestionResponse(BaseModel):
    """Response schema for practice questions."""
    
    id: str = Field(..., description="Question identifier")
    question: str = Field(..., description="Question text")
    type: str = Field(..., description="Question type")
    difficulty: str = Field(..., description="Difficulty level")
    topic: str = Field(..., description="Related topic")
    options: List[str] = Field(default_factory=list, description="Answer options")
    correct_answer: int = Field(..., ge=0, description="Index of correct answer")
    explanation: str = Field(..., description="Answer explanation")
    
    class Config:
        schema_extra = {
            "example": {
                "id": "q_1234",
                "question": "Which protocol is used for secure web communication?",
                "type": "multiple_choice",
                "difficulty": "intermediate",
                "topic": "Network Security",
                "options": [
                    "HTTP",
                    "HTTPS",
                    "FTP",
                    "SMTP"
                ],
                "correct_answer": 1,
                "explanation": "HTTPS (HTTP Secure) uses SSL/TLS encryption for secure communication."
            }
        }


class StudyFeedbackResponse(BaseModel):
    """Response schema for study session feedback."""
    
    overall_rating: float = Field(..., ge=1.0, le=5.0, description="Overall session rating")
    strengths: List[str] = Field(..., description="Session strengths identified")
    improvements: List[str] = Field(..., description="Areas for improvement")
    next_session_suggestions: List[str] = Field(..., description="Suggestions for next session")
    technique_recommendations: List[str] = Field(..., description="Recommended study techniques")
    motivation_boost: str = Field(..., description="Motivational message")
    
    class Config:
        schema_extra = {
            "example": {
                "overall_rating": 4.2,
                "strengths": [
                    "Excellent focus throughout the session",
                    "Good session duration for deep learning"
                ],
                "improvements": [
                    "Consider taking more frequent breaks",
                    "Try active recall techniques"
                ],
                "next_session_suggestions": [
                    "Follow up with practice questions on this topic",
                    "Review your notes from this session"
                ],
                "technique_recommendations": [
                    "Try the Feynman Technique for complex concepts",
                    "Use spaced repetition for memorization"
                ],
                "motivation_boost": "Great progress! You're building valuable knowledge with each session."
            }
        }


class KnowledgeAssessmentResponse(BaseModel):
    """Response schema for knowledge level assessment."""
    
    certification_id: int = Field(..., description="Certification ID")
    certification_name: str = Field(..., description="Certification name")
    knowledge_level: float = Field(..., ge=0.0, le=1.0, description="Knowledge level (0-1)")
    knowledge_percentage: float = Field(..., ge=0.0, le=100.0, description="Knowledge percentage")
    difficulty_level: str = Field(..., description="Recommended difficulty level")
    level_description: str = Field(..., description="Knowledge level description")
    weak_areas: List[str] = Field(default_factory=list, description="Identified weak areas")
    strong_areas: List[str] = Field(default_factory=list, description="Identified strong areas")
    assessment_date: str = Field(..., description="Assessment date")
    recommendations: List[str] = Field(default_factory=list, description="Study recommendations")


class LearningStyleAnalysisResponse(BaseModel):
    """Response schema for learning style analysis."""
    
    learning_style: str = Field(..., description="Identified learning style")
    style_description: str = Field(..., description="Learning style description")
    optimal_session_length_minutes: int = Field(..., description="Optimal session length")
    best_study_times: List[str] = Field(..., description="Best study times")
    session_type_distribution: Dict[str, Dict[str, Any]] = Field(..., description="Session type distribution")
    total_sessions_analyzed: int = Field(..., description="Total sessions analyzed")
    analysis_date: str = Field(..., description="Analysis date")
    recommendations: List[str] = Field(..., description="Personalized recommendations")


class StudyTechniqueResponse(BaseModel):
    """Response schema for study techniques."""
    
    name: str = Field(..., description="Technique name")
    description: str = Field(..., description="Technique description")
    effectiveness: float = Field(..., ge=0.0, le=1.0, description="Effectiveness score")
    difficulty_levels: List[str] = Field(..., description="Suitable difficulty levels")
    time_requirements: str = Field(..., description="Time requirements")
    learning_styles: List[str] = Field(default_factory=list, description="Compatible learning styles")


class AIHealthResponse(BaseModel):
    """Response schema for AI service health check."""
    
    status: str = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    features: List[str] = Field(..., description="Available features")
    timestamp: str = Field(..., description="Health check timestamp")


class ModelTrainingResponse(BaseModel):
    """Response schema for model training."""
    
    message: str = Field(..., description="Training result message")
    user_id: str = Field(..., description="User ID trained on")
    training_date: str = Field(..., description="Training completion date")
    models_updated: List[str] = Field(..., description="List of updated models")


# Composite Response Schemas

class ComprehensiveAnalysisResponse(BaseModel):
    """Response schema for comprehensive user analysis."""
    
    recommendations: List[StudyRecommendationResponse] = Field(..., description="Study recommendations")
    insights: List[LearningInsightResponse] = Field(..., description="Learning insights")
    knowledge_assessment: Optional[KnowledgeAssessmentResponse] = Field(None, description="Knowledge assessment")
    learning_style: Optional[LearningStyleAnalysisResponse] = Field(None, description="Learning style analysis")
    suggested_techniques: List[StudyTechniqueResponse] = Field(default_factory=list, description="Recommended techniques")
    analysis_summary: Dict[str, Any] = Field(default_factory=dict, description="Analysis summary")


class PersonalizedDashboardResponse(BaseModel):
    """Response schema for personalized AI dashboard."""
    
    user_id: str = Field(..., description="User identifier")
    dashboard_date: str = Field(..., description="Dashboard generation date")
    top_recommendations: List[StudyRecommendationResponse] = Field(..., description="Top recommendations")
    key_insights: List[LearningInsightResponse] = Field(..., description="Key insights")
    current_knowledge_level: float = Field(..., description="Current knowledge level")
    study_efficiency_score: float = Field(..., description="Study efficiency score")
    consistency_score: float = Field(..., description="Study consistency score")
    next_milestones: List[str] = Field(..., description="Upcoming milestones")
    motivational_message: str = Field(..., description="Personalized motivational message")
