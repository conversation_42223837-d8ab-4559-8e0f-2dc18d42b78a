"""Schemas for organization management API"""
from pydantic import BaseModel, Field, EmailStr, HttpUrl
from typing import List, Optional
from datetime import datetime


class OrganizationBase(BaseModel):
    """Base organization schema"""
    name: str = Field(..., min_length=1, max_length=200, description="Organization name")
    description: Optional[str] = Field(None, description="Organization description")
    website: Optional[str] = Field(None, description="Organization website URL")
    category: Optional[str] = Field(None, max_length=100, description="Organization category")
    logo_url: Optional[str] = Field(None, description="Organization logo URL")
    contact_email: Optional[EmailStr] = Field(None, description="Contact email")
    contact_phone: Optional[str] = Field(None, max_length=50, description="Contact phone")
    address: Optional[str] = Field(None, description="Organization address")


class OrganizationCreate(OrganizationBase):
    """Schema for creating an organization"""
    pass


class OrganizationUpdate(BaseModel):
    """Schema for updating an organization"""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    website: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    logo_url: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = Field(None, max_length=50)
    address: Optional[str] = None


class OrganizationResponse(OrganizationBase):
    """Schema for organization response"""
    id: int
    is_verified: bool = Field(..., description="Whether organization is verified")
    created_by: Optional[str] = Field(None, description="User ID who created the organization")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    # Statistics (would be calculated from related data)
    certification_count: Optional[int] = Field(0, description="Number of certifications offered")
    user_count: Optional[int] = Field(0, description="Number of users associated")
    
    class Config:
        from_attributes = True


class OrganizationList(BaseModel):
    """Schema for organization list response with pagination"""
    organizations: List[OrganizationResponse]
    total_count: int = Field(..., description="Total number of organizations")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")


class TeamMemberResponse(BaseModel):
    """Schema for team member response"""
    user_id: str = Field(..., description="User ID")
    email: str = Field(..., description="User email")
    name: str = Field(..., description="User name")
    role: str = Field(..., description="Role in organization")
    joined_at: Optional[str] = Field(None, description="When user joined organization")
    is_active: bool = Field(True, description="Whether user is active in organization")
    last_activity: Optional[str] = Field(None, description="Last activity timestamp")


class TeamInviteRequest(BaseModel):
    """Schema for team invitation request"""
    email: EmailStr = Field(..., description="Email address to invite")
    role: str = Field("member", description="Role to assign to invited user")
    message: Optional[str] = Field(None, max_length=500, description="Optional invitation message")
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "role": "member",
                "message": "Welcome to our organization! We'd love to have you join our team."
            }
        }


class TeamInviteResponse(BaseModel):
    """Schema for team invitation response"""
    message: str = Field(..., description="Invitation status message")
    organization_id: int = Field(..., description="Organization ID")
    invited_email: str = Field(..., description="Email address that was invited")
    role: str = Field(..., description="Role assigned to invitation")
    invited_by: str = Field(..., description="Email of user who sent invitation")
    invitation_id: Optional[str] = Field(None, description="Invitation ID for tracking")
    expires_at: Optional[datetime] = Field(None, description="When invitation expires")


class OrganizationStats(BaseModel):
    """Schema for organization statistics"""
    total_organizations: int = Field(..., description="Total number of organizations")
    verified_organizations: int = Field(..., description="Number of verified organizations")
    categories: List[str] = Field(..., description="Available organization categories")
    top_categories: List[dict] = Field(..., description="Top categories by organization count")
    recent_organizations: List[OrganizationResponse] = Field(..., description="Recently created organizations")


class OrganizationSearchRequest(BaseModel):
    """Schema for organization search request"""
    query: str = Field(..., min_length=2, description="Search query")
    category: Optional[str] = Field(None, description="Filter by category")
    verified_only: bool = Field(False, description="Show only verified organizations")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(20, ge=1, le=100, description="Page size")


class OrganizationMembershipRequest(BaseModel):
    """Schema for organization membership request"""
    organization_id: int = Field(..., description="Organization ID to join")
    message: Optional[str] = Field(None, max_length=500, description="Optional message to organization admins")
    
    class Config:
        schema_extra = {
            "example": {
                "organization_id": 123,
                "message": "I would like to join your organization to collaborate on security certifications."
            }
        }


class OrganizationMembershipResponse(BaseModel):
    """Schema for organization membership response"""
    membership_id: str = Field(..., description="Membership ID")
    organization_id: int = Field(..., description="Organization ID")
    user_id: str = Field(..., description="User ID")
    role: str = Field(..., description="User role in organization")
    status: str = Field(..., description="Membership status (pending, active, inactive)")
    joined_at: Optional[datetime] = Field(None, description="When user joined")
    approved_by: Optional[str] = Field(None, description="User ID who approved membership")
    approved_at: Optional[datetime] = Field(None, description="When membership was approved")


class OrganizationPermissions(BaseModel):
    """Schema for organization permissions"""
    can_edit: bool = Field(False, description="Can edit organization details")
    can_invite: bool = Field(False, description="Can invite new members")
    can_manage_members: bool = Field(False, description="Can manage existing members")
    can_delete: bool = Field(False, description="Can delete organization")
    can_view_analytics: bool = Field(False, description="Can view organization analytics")
    is_admin: bool = Field(False, description="Is organization administrator")
    is_owner: bool = Field(False, description="Is organization owner")


class OrganizationWithPermissions(OrganizationResponse):
    """Schema for organization response with user permissions"""
    permissions: OrganizationPermissions = Field(..., description="User permissions for this organization")
    user_role: Optional[str] = Field(None, description="User's role in this organization")
    membership_status: Optional[str] = Field(None, description="User's membership status")
