"""Pydantic schemas for Enterprise Dashboard API endpoints.

This module provides comprehensive request/response schemas for enterprise
organization management, user administration, analytics, and licensing.
"""

from pydantic import BaseModel, Field, validator, EmailStr
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class OrganizationType(str, Enum):
    """Organization type enumeration."""
    EDUCATIONAL = "educational"
    CORPORATE = "corporate"
    GOVERNMENT = "government"
    NON_PROFIT = "non_profit"
    TRAINING_PROVIDER = "training_provider"


class SubscriptionTier(str, Enum):
    """Subscription tier enumeration."""
    BASIC = "basic"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


class UserRole(str, Enum):
    """User role enumeration."""
    SUPER_ADMIN = "super_admin"
    ORG_ADMIN = "org_admin"
    DEPARTMENT_ADMIN = "department_admin"
    INSTRUCTOR = "instructor"
    LEARNER = "learner"
    VIEWER = "viewer"


class LicenseStatus(str, Enum):
    """License status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"
    SUSPENDED = "suspended"
    PENDING = "pending"


# Organization Schemas

class OrganizationBase(BaseModel):
    """Base organization schema."""
    name: str = Field(..., min_length=1, max_length=200, description="Organization name")
    display_name: Optional[str] = Field(None, max_length=200, description="Display name")
    organization_type: OrganizationType = Field(OrganizationType.EDUCATIONAL, description="Organization type")
    industry: Optional[str] = Field(None, max_length=100, description="Industry")
    size_category: Optional[str] = Field(None, max_length=50, description="Organization size")
    employee_count: Optional[int] = Field(None, ge=1, description="Number of employees")
    
    # Contact information
    primary_contact_name: Optional[str] = Field(None, max_length=200, description="Primary contact name")
    primary_contact_email: Optional[EmailStr] = Field(None, description="Primary contact email")
    primary_contact_phone: Optional[str] = Field(None, max_length=50, description="Primary contact phone")
    
    # Address
    address_line1: Optional[str] = Field(None, max_length=200, description="Address line 1")
    address_line2: Optional[str] = Field(None, max_length=200, description="Address line 2")
    city: Optional[str] = Field(None, max_length=100, description="City")
    state_province: Optional[str] = Field(None, max_length=100, description="State/Province")
    postal_code: Optional[str] = Field(None, max_length=20, description="Postal code")
    country: Optional[str] = Field(None, max_length=100, description="Country")
    
    # Organization details
    domain: Optional[str] = Field(None, max_length=200, description="Email domain")
    website: Optional[str] = Field(None, max_length=500, description="Website URL")
    description: Optional[str] = Field(None, description="Organization description")


class OrganizationCreate(OrganizationBase):
    """Schema for creating an organization."""
    subscription_tier: SubscriptionTier = Field(SubscriptionTier.BASIC, description="Subscription tier")
    license_count: int = Field(10, ge=1, le=10000, description="Number of licenses")
    is_trial: bool = Field(False, description="Is trial organization")
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Acme University",
                "display_name": "Acme University - IT Department",
                "organization_type": "educational",
                "industry": "Education",
                "size_category": "large",
                "employee_count": 5000,
                "primary_contact_name": "John Smith",
                "primary_contact_email": "<EMAIL>",
                "domain": "acme.edu",
                "website": "https://www.acme.edu",
                "description": "Leading university in technology education",
                "subscription_tier": "professional",
                "license_count": 100
            }
        }


class OrganizationUpdate(BaseModel):
    """Schema for updating an organization."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    display_name: Optional[str] = Field(None, max_length=200)
    organization_type: Optional[OrganizationType] = None
    industry: Optional[str] = Field(None, max_length=100)
    size_category: Optional[str] = Field(None, max_length=50)
    employee_count: Optional[int] = Field(None, ge=1)
    primary_contact_name: Optional[str] = Field(None, max_length=200)
    primary_contact_email: Optional[EmailStr] = None
    primary_contact_phone: Optional[str] = Field(None, max_length=50)
    domain: Optional[str] = Field(None, max_length=200)
    website: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    subscription_tier: Optional[SubscriptionTier] = None
    license_count: Optional[int] = Field(None, ge=1, le=10000)
    is_active: Optional[bool] = None


class OrganizationResponse(OrganizationBase):
    """Schema for organization response."""
    id: int
    slug: str
    subscription_tier: SubscriptionTier
    license_count: int
    licenses_used: int
    subscription_start_date: Optional[str] = None
    subscription_end_date: Optional[str] = None
    is_active: bool
    is_trial: bool
    trial_end_date: Optional[str] = None
    settings: Dict[str, Any] = Field(default_factory=dict)
    features_enabled: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True


class OrganizationListResponse(BaseModel):
    """Schema for organization list response."""
    organizations: List[OrganizationResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int


# User Schemas

class EnterpriseUserBase(BaseModel):
    """Base enterprise user schema."""
    email: EmailStr = Field(..., description="User email address")
    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")
    display_name: Optional[str] = Field(None, max_length=200, description="Display name")
    role: UserRole = Field(UserRole.LEARNER, description="User role")
    employee_id: Optional[str] = Field(None, max_length=100, description="Employee ID")
    job_title: Optional[str] = Field(None, max_length=200, description="Job title")
    manager_user_id: Optional[str] = Field(None, description="Manager user ID")
    hire_date: Optional[datetime] = Field(None, description="Hire date")


class EnterpriseUserCreate(EnterpriseUserBase):
    """Schema for creating an enterprise user."""
    user_id: str = Field(..., min_length=1, max_length=100, description="Unique user ID")
    department_id: Optional[int] = Field(None, description="Department ID")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "john.smith",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Smith",
                "display_name": "John Smith",
                "role": "learner",
                "employee_id": "EMP001",
                "job_title": "IT Specialist",
                "department_id": 1
            }
        }


class EnterpriseUserUpdate(BaseModel):
    """Schema for updating an enterprise user."""
    email: Optional[EmailStr] = None
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    display_name: Optional[str] = Field(None, max_length=200)
    role: Optional[UserRole] = None
    department_id: Optional[int] = None
    employee_id: Optional[str] = Field(None, max_length=100)
    job_title: Optional[str] = Field(None, max_length=200)
    manager_user_id: Optional[str] = None
    hire_date: Optional[datetime] = None
    is_active: Optional[bool] = None


class EnterpriseUserResponse(EnterpriseUserBase):
    """Schema for enterprise user response."""
    id: int
    user_id: str
    organization_id: int
    department_id: Optional[int] = None
    permissions: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool
    last_login: Optional[str] = None
    learning_goals: Dict[str, Any] = Field(default_factory=dict)
    certifications_assigned: List[str] = Field(default_factory=list)
    certifications_completed: List[str] = Field(default_factory=list)
    preferences: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """Schema for user list response."""
    users: List[EnterpriseUserResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int


# Department Schemas

class DepartmentBase(BaseModel):
    """Base department schema."""
    name: str = Field(..., min_length=1, max_length=200, description="Department name")
    description: Optional[str] = Field(None, description="Department description")
    code: Optional[str] = Field(None, max_length=50, description="Department code")
    parent_department_id: Optional[int] = Field(None, description="Parent department ID")
    head_user_id: Optional[str] = Field(None, description="Department head user ID")
    budget_allocated: Optional[float] = Field(None, ge=0, description="Allocated budget")


class DepartmentCreate(DepartmentBase):
    """Schema for creating a department."""
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Information Technology",
                "description": "IT department responsible for technology infrastructure",
                "code": "IT",
                "budget_allocated": 50000.0
            }
        }


class DepartmentUpdate(BaseModel):
    """Schema for updating a department."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    code: Optional[str] = Field(None, max_length=50)
    parent_department_id: Optional[int] = None
    head_user_id: Optional[str] = None
    budget_allocated: Optional[float] = Field(None, ge=0)
    budget_used: Optional[float] = Field(None, ge=0)
    is_active: Optional[bool] = None


class DepartmentResponse(DepartmentBase):
    """Schema for department response."""
    id: int
    organization_id: int
    budget_used: float
    settings: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True


# License Schemas

class LicenseAssignment(BaseModel):
    """Schema for license assignment."""
    license_type: str = Field(..., description="License type")
    expiration_date: Optional[datetime] = Field(None, description="License expiration date")
    
    class Config:
        schema_extra = {
            "example": {
                "license_type": "professional",
                "expiration_date": "2024-12-31T23:59:59Z"
            }
        }


class LicenseResponse(BaseModel):
    """Schema for license response."""
    id: int
    organization_id: int
    user_id: str
    license_type: str
    status: LicenseStatus
    assigned_date: Optional[str] = None
    activation_date: Optional[str] = None
    expiration_date: Optional[str] = None
    last_used_date: Optional[str] = None
    features_used: Dict[str, Any] = Field(default_factory=dict)
    usage_stats: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True


# Analytics Schemas

class UserMetrics(BaseModel):
    """User metrics schema."""
    total_users: int
    active_users: int
    new_users: int
    user_retention_rate: float


class LearningMetrics(BaseModel):
    """Learning metrics schema."""
    total_study_hours: float
    average_study_hours_per_user: float
    total_certifications_completed: int
    certification_completion_rate: float


class EngagementMetrics(BaseModel):
    """Engagement metrics schema."""
    total_sessions: int
    average_session_duration: float
    feature_usage: Dict[str, Any]


class PerformanceMetrics(BaseModel):
    """Performance metrics schema."""
    average_test_scores: float
    goal_completion_rate: float
    user_satisfaction_score: float


class OrganizationAnalyticsResponse(BaseModel):
    """Schema for organization analytics response."""
    organization_id: int
    period_type: str
    period_start: str
    period_end: str
    user_metrics: UserMetrics
    learning_metrics: LearningMetrics
    engagement_metrics: EngagementMetrics
    performance_metrics: PerformanceMetrics
    department_breakdown: Dict[str, Any]
    certification_breakdown: Dict[str, Any]
    generated_at: str


# Dashboard Schemas

class LicenseUsageStats(BaseModel):
    """License usage statistics schema."""
    total_licenses: int
    active_licenses: int
    expired_licenses: int
    available_licenses: int
    utilization_rate: float
    license_breakdown: Dict[str, int]


class DepartmentSummary(BaseModel):
    """Department summary schema."""
    total_departments: int
    active_departments: int
    largest_department: str
    largest_department_users: int


class UserSummary(BaseModel):
    """User summary schema."""
    total_users: int
    active_users: int
    recent_logins: int
    role_distribution: Dict[str, int]


class ActivityItem(BaseModel):
    """Activity item schema."""
    type: str
    user_id: str
    description: str
    timestamp: Optional[str] = None


class AlertItem(BaseModel):
    """Alert item schema."""
    type: str
    title: str
    message: str
    priority: str


class DashboardResponse(BaseModel):
    """Schema for dashboard response."""
    organization: OrganizationResponse
    license_usage: LicenseUsageStats
    recent_analytics: OrganizationAnalyticsResponse
    department_summary: DepartmentSummary
    user_summary: UserSummary
    recent_activity: List[ActivityItem]
    alerts: List[AlertItem]


# Budget Optimization Schemas

class BudgetOptimizationRequest(BaseModel):
    """Schema for budget optimization request."""
    enterprise_id: int = Field(..., description="Enterprise ID")
    total_budget: float = Field(..., ge=1000, description="Total budget amount")
    strategic_priorities: List[str] = Field(..., description="Strategic priority areas")
    team_constraints: Optional[Dict[str, Any]] = Field(None, description="Team constraints")
    timeline_months: int = Field(12, ge=1, le=36, description="Timeline in months")

    class Config:
        schema_extra = {
            "example": {
                "enterprise_id": 1,
                "total_budget": 100000.0,
                "strategic_priorities": ["cybersecurity", "cloud_security"],
                "timeline_months": 12
            }
        }


class BudgetOptimizationResponse(BaseModel):
    """Schema for budget optimization response."""
    enterprise_id: int
    total_budget: float
    optimized_allocation: Dict[str, Any]
    projected_roi: float
    cost_savings: float
    efficiency_score: float
    recommendations: List[str]
    risk_assessment: Dict[str, Any]
    timeline_months: int
    optimization_date: str


class BudgetAllocationRequest(BaseModel):
    """Schema for budget allocation request."""
    enterprise_id: int = Field(..., description="Enterprise ID")
    department_allocations: Dict[str, float] = Field(..., description="Department budget allocations")
    certification_priorities: List[str] = Field(..., description="Certification priorities")


class BudgetAllocationResponse(BaseModel):
    """Schema for budget allocation response."""
    enterprise_id: int
    total_allocated: float
    department_allocations: Dict[str, float]
    certification_allocations: Dict[str, float]
    allocation_efficiency: float
    recommendations: List[str]
    allocation_date: str


class ROICalculationRequest(BaseModel):
    """Schema for ROI calculation request."""
    enterprise_id: int = Field(..., description="Enterprise ID")
    investment_amount: float = Field(..., ge=0, description="Investment amount")
    training_programs: List[str] = Field(..., description="Training programs")
    timeline_months: int = Field(12, ge=1, le=60, description="Timeline in months")
    success_metrics: Optional[Dict[str, Any]] = Field(None, description="Success metrics")


class ROICalculationResponse(BaseModel):
    """Schema for ROI calculation response."""
    enterprise_id: int
    investment_amount: float
    projected_return: float
    roi_percentage: float
    payback_period_months: int
    net_present_value: float
    risk_adjusted_roi: float
    confidence_interval: Dict[str, float]
    key_assumptions: List[str]
    sensitivity_analysis: Dict[str, Any]
    calculation_date: str


class BudgetAnalyticsResponse(BaseModel):
    """Schema for budget analytics response."""
    enterprise_id: int
    analysis_period_months: int
    total_budget_allocated: float
    total_budget_utilized: float
    utilization_rate: float
    cost_per_certification: float
    roi_by_program: Dict[str, float]
    efficiency_metrics: Dict[str, Any]
    trend_analysis: Dict[str, Any]
    benchmark_comparison: Dict[str, Any]
    optimization_opportunities: List[str]
    projections: Optional[Dict[str, Any]] = None
    analysis_date: str


class BudgetRecommendationResponse(BaseModel):
    """Schema for budget recommendation response."""
    enterprise_id: int
    budget_amount: float
    recommendations: List[str]
    priority_allocations: Dict[str, float]
    expected_outcomes: Dict[str, Any]
    implementation_timeline: Dict[str, str]
    success_metrics: List[str]
    generated_at: str


# Health Check Schema

class HealthCheckResponse(BaseModel):
    """Schema for health check response."""
    status: str
    service: str
    version: str
    features: List[str]
    timestamp: str


# Team Management Schemas

class TeamBase(BaseModel):
    """Base schema for team."""
    name: str = Field(..., min_length=1, max_length=200, description="Team name")
    description: Optional[str] = Field(None, description="Team description")
    code: Optional[str] = Field(None, max_length=50, description="Team code/abbreviation")
    team_lead_user_id: Optional[str] = Field(None, description="Team lead user ID")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Team settings")
    goals: List[str] = Field(default_factory=list, description="Team goals")


class TeamCreate(TeamBase):
    """Schema for creating a team."""
    organization_id: int = Field(..., description="Organization ID")

    class Config:
        schema_extra = {
            "example": {
                "name": "Security Team Alpha",
                "description": "Advanced security certification team",
                "code": "SEC-A",
                "organization_id": 1,
                "team_lead_user_id": "john.smith",
                "goals": ["Complete CISSP certification", "Achieve 90% pass rate"]
            }
        }


class TeamUpdate(BaseModel):
    """Schema for updating a team."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    code: Optional[str] = Field(None, max_length=50)
    team_lead_user_id: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None
    goals: Optional[List[str]] = None
    is_active: Optional[bool] = None


class TeamResponse(TeamBase):
    """Schema for team response."""
    id: int
    organization_id: int
    is_active: bool
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    member_count: Optional[int] = 0

    class Config:
        from_attributes = True


class TeamMembershipBase(BaseModel):
    """Base schema for team membership."""
    role: str = Field("member", description="Member role in team")


class TeamMembershipCreate(TeamMembershipBase):
    """Schema for creating team membership."""
    team_id: int = Field(..., description="Team ID")
    user_id: str = Field(..., description="User ID")


class TeamMembershipResponse(TeamMembershipBase):
    """Schema for team membership response."""
    id: int
    team_id: int
    user_id: str
    joined_date: Optional[str] = None
    is_active: bool
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    class Config:
        from_attributes = True


class BulkUserInvite(BaseModel):
    """Schema for bulk user invitation."""
    organization_id: int = Field(..., description="Organization ID")
    emails: List[str] = Field(..., min_items=1, max_items=100, description="List of email addresses")
    role: str = Field("learner", description="Default role for invited users")
    department_id: Optional[int] = Field(None, description="Default department ID")
    team_id: Optional[int] = Field(None, description="Default team ID")
    send_email: bool = Field(True, description="Send invitation emails")

    class Config:
        schema_extra = {
            "example": {
                "organization_id": 1,
                "emails": ["<EMAIL>", "<EMAIL>"],
                "role": "learner",
                "department_id": 1,
                "team_id": 1,
                "send_email": True
            }
        }


class BulkUserInviteResponse(BaseModel):
    """Schema for bulk user invitation response."""
    total_invited: int
    successful_invites: List[str]
    failed_invites: List[Dict[str, str]]
    invitation_id: str
