"""Pydantic schemas for progress tracking and learning analytics API endpoints.

This module provides comprehensive request/response schemas for progress tracking,
study sessions, practice tests, goals, achievements, and learning analytics.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class SessionType(str, Enum):
    """Enumeration of study session types."""
    READING = "reading"
    PRACTICE_TEST = "practice_test"
    VIDEO = "video"
    HANDS_ON = "hands_on"
    REVIEW = "review"
    DISCUSSION = "discussion"
    PROJECT = "project"


class TestType(str, Enum):
    """Enumeration of practice test types."""
    PRACTICE_EXAM = "practice_exam"
    QUIZ = "quiz"
    ASSESSMENT = "assessment"
    MOCK_EXAM = "mock_exam"
    DIAGNOSTIC = "diagnostic"


class GoalType(str, Enum):
    """Enumeration of learning goal types."""
    CERTIFICATION = "certification"
    SKILL = "skill"
    STUDY_TIME = "study_time"
    PRACTICE_SCORE = "practice_score"
    COMPLETION_DATE = "completion_date"


class GoalStatus(str, Enum):
    """Enumeration of goal statuses."""
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    ABANDONED = "abandoned"


class Priority(str, Enum):
    """Enumeration of priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AchievementType(str, Enum):
    """Enumeration of achievement types."""
    BADGE = "badge"
    MILESTONE = "milestone"
    STREAK = "streak"
    CERTIFICATION = "certification"
    MASTERY = "mastery"


class PeriodType(str, Enum):
    """Enumeration of analytics period types."""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


# Request Schemas

class StudySessionStart(BaseModel):
    """Request schema for starting a study session."""
    
    session_type: SessionType = Field(..., description="Type of study session")
    certification_id: Optional[int] = Field(None, description="Associated certification ID")
    learning_path_id: Optional[int] = Field(None, description="Associated learning path ID")
    transition_plan_id: Optional[int] = Field(None, description="Associated transition plan ID")
    topic: Optional[str] = Field(None, max_length=200, description="Study topic")
    subtopic: Optional[str] = Field(None, max_length=200, description="Study subtopic")
    planned_duration_minutes: Optional[int] = Field(None, ge=1, le=480, description="Planned session duration")
    
    class Config:
        schema_extra = {
            "example": {
                "session_type": "reading",
                "certification_id": 1,
                "topic": "Network Security",
                "subtopic": "Firewalls and VPNs",
                "planned_duration_minutes": 60
            }
        }


class StudySessionEnd(BaseModel):
    """Request schema for ending a study session."""
    
    progress_after: float = Field(..., ge=0, le=100, description="Progress percentage after session")
    confidence_level: Optional[int] = Field(None, ge=1, le=5, description="Confidence level (1-5)")
    difficulty_rating: Optional[int] = Field(None, ge=1, le=5, description="Difficulty rating (1-5)")
    focus_rating: Optional[int] = Field(None, ge=1, le=5, description="Focus rating (1-5)")
    effectiveness_rating: Optional[int] = Field(None, ge=1, le=5, description="Effectiveness rating (1-5)")
    interruptions_count: Optional[int] = Field(None, ge=0, description="Number of interruptions")
    notes: Optional[str] = Field(None, max_length=1000, description="Session notes")
    materials_used: Optional[List[str]] = Field(None, description="Study materials used")
    tags: Optional[List[str]] = Field(None, description="Session tags")
    
    class Config:
        schema_extra = {
            "example": {
                "progress_after": 75.5,
                "confidence_level": 4,
                "difficulty_rating": 3,
                "focus_rating": 4,
                "effectiveness_rating": 4,
                "notes": "Completed chapter on firewall configurations",
                "materials_used": ["Security+ Study Guide", "Practice Labs"],
                "tags": ["networking", "security"]
            }
        }


class PracticeTestResultCreate(BaseModel):
    """Request schema for recording practice test results."""
    
    certification_id: int = Field(..., description="Associated certification ID")
    test_name: str = Field(..., min_length=1, max_length=200, description="Test name")
    test_type: TestType = Field(..., description="Type of practice test")
    test_provider: Optional[str] = Field(None, max_length=100, description="Test provider")
    
    total_questions: int = Field(..., ge=1, description="Total number of questions")
    correct_answers: int = Field(..., ge=0, description="Number of correct answers")
    time_taken_minutes: Optional[int] = Field(None, ge=1, description="Time taken in minutes")
    time_limit_minutes: Optional[int] = Field(None, ge=1, description="Time limit in minutes")
    passing_score: Optional[float] = Field(None, ge=0, le=100, description="Passing score percentage")
    
    domain_scores: Optional[Dict[str, float]] = Field(None, description="Scores by domain")
    topic_scores: Optional[Dict[str, float]] = Field(None, description="Scores by topic")
    question_details: Optional[List[Dict[str, Any]]] = Field(None, description="Detailed question results")
    flagged_questions: Optional[List[int]] = Field(None, description="Question numbers flagged for review")
    
    difficulty_rating: Optional[int] = Field(None, ge=1, le=5, description="Test difficulty rating")
    confidence_level: Optional[int] = Field(None, ge=1, le=5, description="Confidence level")
    test_environment: Optional[str] = Field(None, description="Test environment")
    
    @validator('correct_answers')
    def validate_correct_answers(cls, v, values):
        if 'total_questions' in values and v > values['total_questions']:
            raise ValueError('correct_answers cannot exceed total_questions')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "certification_id": 1,
                "test_name": "Security+ Practice Exam 1",
                "test_type": "practice_exam",
                "test_provider": "CompTIA",
                "total_questions": 90,
                "correct_answers": 72,
                "time_taken_minutes": 85,
                "passing_score": 75.0,
                "domain_scores": {
                    "Network Security": 80.0,
                    "Compliance": 70.0,
                    "Threats": 85.0
                }
            }
        }


class LearningGoalCreate(BaseModel):
    """Request schema for creating learning goals."""
    
    title: str = Field(..., min_length=1, max_length=200, description="Goal title")
    description: Optional[str] = Field(None, max_length=1000, description="Goal description")
    goal_type: GoalType = Field(..., description="Type of learning goal")
    
    certification_id: Optional[int] = Field(None, description="Associated certification ID")
    target_value: Optional[float] = Field(None, ge=0, description="Target value")
    target_unit: Optional[str] = Field(None, max_length=20, description="Target unit")
    target_date: Optional[datetime] = Field(None, description="Target completion date")
    
    priority: Priority = Field(Priority.MEDIUM, description="Goal priority")
    is_public: bool = Field(False, description="Share goal with community")
    reminder_frequency: Optional[str] = Field(None, description="Reminder frequency")
    
    milestones: Optional[List[Dict[str, Any]]] = Field(None, description="Goal milestones")
    rewards: Optional[List[str]] = Field(None, description="Completion rewards")
    
    class Config:
        schema_extra = {
            "example": {
                "title": "Pass Security+ Certification",
                "description": "Achieve Security+ certification within 3 months",
                "goal_type": "certification",
                "certification_id": 1,
                "target_value": 85.0,
                "target_unit": "percentage",
                "target_date": "2024-06-01T00:00:00Z",
                "priority": "high"
            }
        }


class LearningGoalUpdate(BaseModel):
    """Request schema for updating learning goals."""
    
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    target_value: Optional[float] = Field(None, ge=0)
    target_date: Optional[datetime] = None
    priority: Optional[Priority] = None
    status: Optional[GoalStatus] = None
    is_public: Optional[bool] = None
    reminder_frequency: Optional[str] = None


class GoalProgressUpdate(BaseModel):
    """Request schema for updating goal progress."""
    
    current_value: float = Field(..., ge=0, description="Current progress value")
    notes: Optional[str] = Field(None, max_length=500, description="Progress notes")


# Response Schemas

class StudySessionResponse(BaseModel):
    """Response schema for study session information."""
    
    id: int
    user_id: str
    certification_id: Optional[int] = None
    learning_path_id: Optional[int] = None
    transition_plan_id: Optional[int] = None
    
    session_type: str
    topic: Optional[str] = None
    subtopic: Optional[str] = None
    
    started_at: datetime
    ended_at: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    planned_duration_minutes: Optional[int] = None
    
    progress_before: float = 0.0
    progress_after: float = 0.0
    confidence_level: Optional[int] = None
    difficulty_rating: Optional[int] = None
    focus_rating: Optional[int] = None
    effectiveness_rating: Optional[int] = None
    interruptions_count: int = 0
    
    materials_used: List[str] = []
    notes: Optional[str] = None
    tags: List[str] = []
    
    created_at: datetime
    updated_at: datetime


class PracticeTestResultResponse(BaseModel):
    """Response schema for practice test results."""
    
    id: int
    user_id: str
    certification_id: int
    
    test_name: str
    test_type: str
    test_provider: Optional[str] = None
    
    total_questions: int
    time_limit_minutes: Optional[int] = None
    passing_score: Optional[float] = None
    
    score: float
    percentage: float
    passed: bool
    time_taken_minutes: Optional[int] = None
    
    correct_answers: int
    incorrect_answers: int
    unanswered_questions: int = 0
    
    domain_scores: Dict[str, float] = {}
    topic_scores: Dict[str, float] = {}
    weak_areas: List[str] = []
    strong_areas: List[str] = []
    
    difficulty_rating: Optional[int] = None
    confidence_level: Optional[int] = None
    test_environment: Optional[str] = None
    
    created_at: datetime
    updated_at: datetime


class LearningGoalResponse(BaseModel):
    """Response schema for learning goals."""
    
    id: int
    user_id: str
    title: str
    description: Optional[str] = None
    goal_type: str
    
    certification_id: Optional[int] = None
    target_value: Optional[float] = None
    target_unit: Optional[str] = None
    target_date: Optional[datetime] = None
    
    current_value: float = 0.0
    progress_percentage: float = 0.0
    status: str = "active"
    
    priority: str = "medium"
    is_public: bool = False
    reminder_frequency: Optional[str] = None
    
    milestones: List[Dict[str, Any]] = []
    rewards: List[str] = []
    
    created_at: datetime
    updated_at: datetime


class AchievementResponse(BaseModel):
    """Response schema for achievements."""
    
    id: int
    user_id: str
    achievement_type: str
    title: str
    description: Optional[str] = None
    icon: Optional[str] = None
    
    criteria_type: str
    criteria_value: Optional[float] = None
    criteria_unit: Optional[str] = None
    
    earned_at: Optional[datetime] = None
    is_earned: bool = False
    progress_percentage: float = 0.0
    
    rarity: str = "common"
    points: int = 0
    category: Optional[str] = None
    
    certification_id: Optional[int] = None
    learning_goal_id: Optional[int] = None
    
    created_at: datetime
    updated_at: datetime


class ProgressSummaryResponse(BaseModel):
    """Response schema for progress summary."""
    
    total_study_hours: float
    current_streak: int
    longest_streak: int
    average_session_rating: float
    certifications_in_progress: int
    goals_completed: int
    achievements_earned: int
    overall_progress_percentage: float


class StudyInsightsResponse(BaseModel):
    """Response schema for study insights."""
    
    optimal_study_time: str
    recommended_session_length: int
    focus_improvement_tips: List[str]
    weak_areas: List[str]
    strong_areas: List[str]
    next_actions: List[str]


class LearningAnalyticsResponse(BaseModel):
    """Response schema for learning analytics."""
    
    id: int
    user_id: str
    period_type: str
    period_start: datetime
    period_end: datetime
    
    total_study_minutes: int = 0
    average_session_minutes: float = 0.0
    study_sessions_count: int = 0
    study_streak_days: int = 0
    
    practice_tests_taken: int = 0
    average_test_score: float = 0.0
    score_improvement: float = 0.0
    weak_areas_count: int = 0
    
    certifications_completed: int = 0
    goals_achieved: int = 0
    milestones_reached: int = 0
    overall_progress: float = 0.0
    
    study_efficiency_score: float = 0.0
    focus_score: float = 0.0
    consistency_score: float = 0.0
    
    topic_performance: Dict[str, float] = {}
    study_patterns: Dict[str, Any] = {}
    recommendations: List[str] = []
    
    created_at: datetime
    updated_at: datetime


class ProgressDashboardResponse(BaseModel):
    """Response schema for comprehensive progress dashboard."""
    
    summary: ProgressSummaryResponse
    insights: StudyInsightsResponse
    recent_sessions: List[StudySessionResponse]
    recent_test_results: List[PracticeTestResultResponse]
    active_goals: List[LearningGoalResponse]
    recent_achievements: List[AchievementResponse]
    analytics: Optional[LearningAnalyticsResponse] = None
