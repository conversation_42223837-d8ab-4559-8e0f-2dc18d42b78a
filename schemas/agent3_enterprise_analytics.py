"""Pydantic schemas for Agent 3: Enterprise & Analytics Engine API endpoints.

This module provides comprehensive request/response schemas for enterprise analytics,
compliance automation, data intelligence, and organizational insights.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class ComplianceType(str, Enum):
    """Compliance type enumeration."""
    GDPR = "GDPR"
    HIPAA = "HIPAA"
    SOX = "SOX"
    CMMC = "CMMC"
    ISO27001 = "ISO27001"


class ComplianceStatus(str, Enum):
    """Compliance status enumeration."""
    COMPLIANT = "compliant"
    NEEDS_ATTENTION = "needs_attention"
    NON_COMPLIANT = "non_compliant"
    UNDER_REVIEW = "under_review"


class SkillsGapSeverity(str, Enum):
    """Skills gap severity enumeration."""
    CRITICAL = "critical"
    MODERATE = "moderate"
    MINOR = "minor"


class TrendDirection(str, Enum):
    """Trend direction enumeration."""
    INCREASING = "increasing"
    DECREASING = "decreasing"
    STABLE = "stable"


# Request Schemas

class EnterpriseInsightRequest(BaseModel):
    """Schema for enterprise insight generation requests."""
    organization_id: int = Field(..., description="Organization ID")
    include_individual_insights: bool = Field(True, description="Include individual user insights")
    max_users: int = Field(50, description="Maximum number of users to analyze")
    
    class Config:
        schema_extra = {
            "example": {
                "organization_id": 1,
                "include_individual_insights": True,
                "max_users": 50
            }
        }


class SkillsGapAnalysisRequest(BaseModel):
    """Schema for skills gap analysis requests."""
    organization_id: int = Field(..., description="Organization ID")
    department_id: Optional[int] = Field(None, description="Filter by department ID")
    certification_focus: Optional[str] = Field(None, description="Focus on specific certification")
    
    class Config:
        schema_extra = {
            "example": {
                "organization_id": 1,
                "department_id": 5,
                "certification_focus": "Security+"
            }
        }


class ComplianceReportRequest(BaseModel):
    """Schema for compliance report generation requests."""
    organization_id: int = Field(..., description="Organization ID")
    compliance_type: ComplianceType = Field(..., description="Type of compliance report")
    period_start: str = Field(..., description="Report period start date (YYYY-MM-DD)")
    period_end: str = Field(..., description="Report period end date (YYYY-MM-DD)")
    include_recommendations: bool = Field(True, description="Include compliance recommendations")
    
    class Config:
        schema_extra = {
            "example": {
                "organization_id": 1,
                "compliance_type": "GDPR",
                "period_start": "2024-01-01",
                "period_end": "2024-03-31",
                "include_recommendations": True
            }
        }


# Response Schemas

class UserInsight(BaseModel):
    """Schema for individual user insights."""
    user_id: str = Field(..., description="User ID")
    department_id: Optional[int] = Field(None, description="Department ID")
    role: str = Field(..., description="User role")
    insights: List[Dict[str, Any]] = Field(..., description="AI-generated insights")
    recommendations: List[Dict[str, Any]] = Field(..., description="Personalized recommendations")


class AggregatedPatterns(BaseModel):
    """Schema for aggregated learning patterns."""
    common_learning_styles: Dict[str, int] = Field(..., description="Distribution of learning styles")
    average_study_duration: float = Field(..., description="Average study session duration")
    common_weak_areas: Dict[str, int] = Field(..., description="Most common weak areas")
    department_patterns: Dict[str, int] = Field(..., description="Patterns by department")
    performance_trends: Dict[str, Any] = Field(..., description="Performance trend analysis")


class EnterpriseStudyInsightsResponse(BaseModel):
    """Schema for enterprise study insights response."""
    organization_id: int = Field(..., description="Organization ID")
    total_users: int = Field(..., description="Total number of users analyzed")
    individual_insights: List[UserInsight] = Field(..., description="Individual user insights")
    aggregated_patterns: AggregatedPatterns = Field(..., description="Aggregated learning patterns")
    recommendations: List[str] = Field(..., description="Enterprise-level recommendations")
    generated_at: str = Field(..., description="Timestamp when insights were generated")
    
    class Config:
        schema_extra = {
            "example": {
                "organization_id": 1,
                "total_users": 45,
                "individual_insights": [],
                "aggregated_patterns": {
                    "common_learning_styles": {"visual": 20, "auditory": 15, "kinesthetic": 10},
                    "average_study_duration": 65.5,
                    "common_weak_areas": {"network_security": 12, "cryptography": 8},
                    "department_patterns": {"IT": 25, "Security": 20},
                    "performance_trends": {}
                },
                "recommendations": [
                    "Focus training materials on visual learning style",
                    "Provide additional training resources for network security"
                ],
                "generated_at": "2024-01-15T10:30:00Z"
            }
        }


class SkillsGap(BaseModel):
    """Schema for individual skills gap."""
    topic: str = Field(..., description="Skill/topic name")
    affected_users: int = Field(..., description="Number of users affected")
    total_training_hours: int = Field(..., description="Total training hours needed")
    severity: SkillsGapSeverity = Field(..., description="Gap severity level")


class SkillsGapAnalysisResponse(BaseModel):
    """Schema for skills gap analysis response."""
    organization_id: int = Field(..., description="Organization ID")
    department_id: Optional[int] = Field(None, description="Department ID if filtered")
    analysis_date: str = Field(..., description="Analysis timestamp")
    user_count: int = Field(..., description="Number of users analyzed")
    skills_gaps: Dict[str, List[SkillsGap]] = Field(..., description="Skills gaps by severity")
    certification_gaps: Dict[str, Any] = Field(..., description="Certification gap analysis")
    training_priorities: List[str] = Field(..., description="Prioritized training recommendations")
    budget_recommendations: Dict[str, Any] = Field(..., description="Training budget recommendations")
    
    class Config:
        schema_extra = {
            "example": {
                "organization_id": 1,
                "department_id": None,
                "analysis_date": "2024-01-15T10:30:00Z",
                "user_count": 45,
                "skills_gaps": {
                    "critical_gaps": [],
                    "moderate_gaps": [],
                    "minor_gaps": []
                },
                "certification_gaps": {},
                "training_priorities": [
                    "Network Security fundamentals",
                    "Cloud Security basics"
                ],
                "budget_recommendations": {
                    "total_estimated_cost": 25000,
                    "priority_training_cost": 15000
                }
            }
        }


class ComplianceFinding(BaseModel):
    """Schema for compliance findings."""
    category: str = Field(..., description="Compliance category")
    status: ComplianceStatus = Field(..., description="Compliance status")
    description: str = Field(..., description="Finding description")
    evidence: str = Field(..., description="Supporting evidence")


class AuditTrailEntry(BaseModel):
    """Schema for audit trail entries."""
    timestamp: str = Field(..., description="Entry timestamp")
    action: str = Field(..., description="Action performed")
    user: str = Field(..., description="User who performed action")
    details: str = Field(..., description="Additional details")


class ComplianceReportResponse(BaseModel):
    """Schema for compliance report response."""
    organization_id: int = Field(..., description="Organization ID")
    organization_name: str = Field(..., description="Organization name")
    compliance_type: str = Field(..., description="Type of compliance report")
    report_period: Dict[str, str] = Field(..., description="Report period start and end")
    generated_at: str = Field(..., description="Report generation timestamp")
    compliance_score: float = Field(..., ge=0.0, le=1.0, description="Overall compliance score")
    findings: List[ComplianceFinding] = Field(..., description="Compliance findings")
    recommendations: List[str] = Field(..., description="Compliance recommendations")
    audit_trail: List[AuditTrailEntry] = Field(..., description="Audit trail entries")
    
    class Config:
        schema_extra = {
            "example": {
                "organization_id": 1,
                "organization_name": "Example Corp",
                "compliance_type": "GDPR",
                "report_period": {
                    "start": "2024-01-01T00:00:00Z",
                    "end": "2024-03-31T23:59:59Z"
                },
                "generated_at": "2024-01-15T10:30:00Z",
                "compliance_score": 0.85,
                "findings": [],
                "recommendations": [
                    "Review and clean up data older than retention policy"
                ],
                "audit_trail": []
            }
        }


class SalaryRange(BaseModel):
    """Schema for salary range data."""
    min: int = Field(..., description="Minimum salary")
    max: int = Field(..., description="Maximum salary")
    median: int = Field(..., description="Median salary")


class SalaryIntelligenceResponse(BaseModel):
    """Schema for salary intelligence response."""
    filters: Dict[str, Optional[str]] = Field(..., description="Applied filters")
    salary_ranges: Dict[str, SalaryRange] = Field(..., description="Salary ranges by level")
    certification_premium: Dict[str, int] = Field(..., description="Salary premium by certification")
    market_trends: Dict[str, Any] = Field(..., description="Market trend indicators")
    generated_at: str = Field(..., description="Data generation timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "filters": {
                    "industry": "technology",
                    "location": "US",
                    "role": "security_analyst",
                    "certification": "Security+"
                },
                "salary_ranges": {
                    "entry_level": {"min": 65000, "max": 85000, "median": 75000},
                    "mid_level": {"min": 85000, "max": 120000, "median": 102500},
                    "senior_level": {"min": 120000, "max": 180000, "median": 150000}
                },
                "certification_premium": {
                    "Security+": 8500,
                    "CISSP": 15000
                },
                "market_trends": {
                    "growth_rate": 0.12,
                    "demand_level": "high"
                },
                "generated_at": "2024-01-15T10:30:00Z"
            }
        }


class MarketTrendsResponse(BaseModel):
    """Schema for market trends analysis response."""
    vertical: Optional[str] = Field(None, description="Industry vertical")
    time_period_months: int = Field(..., description="Analysis time period")
    certification_trends: Dict[str, List[str]] = Field(..., description="Certification trends")
    skills_evolution: Dict[str, List[str]] = Field(..., description="Skills evolution analysis")
    job_market_analysis: Dict[str, float] = Field(..., description="Job market metrics")
    future_predictions: Dict[str, str] = Field(..., description="Future predictions")
    generated_at: str = Field(..., description="Analysis timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "vertical": "healthcare",
                "time_period_months": 12,
                "certification_trends": {
                    "growing": ["Security+", "CISSP"],
                    "stable": ["CISM"],
                    "declining": ["Legacy certifications"]
                },
                "skills_evolution": {
                    "emerging_skills": ["Zero Trust", "Cloud Security"],
                    "stable_skills": ["Network Security"],
                    "declining_skills": ["Legacy firewall management"]
                },
                "job_market_analysis": {
                    "demand_growth": 0.15,
                    "supply_shortage": 0.23,
                    "salary_inflation": 0.08
                },
                "future_predictions": {
                    "next_12_months": "Continued high demand for cloud security skills"
                },
                "generated_at": "2024-01-15T10:30:00Z"
            }
        }
