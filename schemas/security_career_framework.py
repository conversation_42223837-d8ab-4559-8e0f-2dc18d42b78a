"""Pydantic schemas for Security Career Framework API endpoints.

This module provides comprehensive request/response schemas for security career
paths, job types, seniority levels, and skill matrices based on <PERSON>'s
8 security areas and industry standards.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum


class SecurityAreaEnum(str, Enum):
    """The 8 core security areas from <PERSON>'s roadmap."""
    SECURITY_ENGINEERING = "Security Architecture and Engineering"
    DEFENSIVE_SECURITY = "Security Operations"
    SECURITY_MANAGEMENT = "Security and Risk Management"
    NETWORK_SECURITY = "Communication and Network Security"
    IDENTITY_ACCESS = "Identity and Access Management"
    ASSET_SECURITY = "Asset Security"
    SECURITY_TESTING = "Security Assessment and Testing"
    SOFTWARE_SECURITY = "Software Security"


class SeniorityLevelEnum(str, Enum):
    """Comprehensive seniority levels for security careers."""
    BEGINNER = "beginner"           # 0-1 years
    INTERMEDIATE = "intermediate"   # 1-3 years
    ADVANCED = "advanced"          # 3-5 years
    EXPERT = "expert"              # 5-7 years
    SENIOR = "senior"              # 7-10 years
    PRINCIPAL = "principal"        # 10-12 years
    ARCHITECT = "architect"        # 12-15 years
    EXECUTIVE = "executive"        # 15+ years


class DemandLevelEnum(str, Enum):
    """Job demand levels in the market."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# Base schemas

class SalaryRange(BaseModel):
    """Salary range information."""
    min: Optional[float] = Field(None, description="Minimum salary")
    max: Optional[float] = Field(None, description="Maximum salary")
    currency: str = Field("USD", description="Currency code")


class CareerProgression(BaseModel):
    """Career progression information."""
    from_roles: List[str] = Field(default_factory=list, description="Previous roles")
    to_roles: List[str] = Field(default_factory=list, description="Next roles")


class SkillSet(BaseModel):
    """Skill set information."""
    core: List[str] = Field(default_factory=list, description="Core required skills")
    advanced: List[str] = Field(default_factory=list, description="Advanced skills")
    leadership: List[str] = Field(default_factory=list, description="Leadership skills")
    business: List[str] = Field(default_factory=list, description="Business skills")


class CertificationSet(BaseModel):
    """Certification set by level."""
    entry: List[str] = Field(default_factory=list, description="Entry-level certifications")
    intermediate: List[str] = Field(default_factory=list, description="Intermediate certifications")
    advanced: List[str] = Field(default_factory=list, description="Advanced certifications")
    expert: List[str] = Field(default_factory=list, description="Expert certifications")


class ToolSet(BaseModel):
    """Tool and technology set."""
    required: List[str] = Field(default_factory=list, description="Required tools")
    preferred: List[str] = Field(default_factory=list, description="Preferred tools")


# Security Job Type schemas

class SecurityJobTypeBase(BaseModel):
    """Base schema for security job types."""
    title: str = Field(..., description="Job title")
    security_area: SecurityAreaEnum = Field(..., description="Security area")
    job_family: str = Field(..., description="Job family")
    seniority_level: SeniorityLevelEnum = Field(..., description="Seniority level")
    description: Optional[str] = Field(None, description="Job description")
    
    # Skills and requirements
    responsibilities: List[str] = Field(default_factory=list, description="Key responsibilities")
    required_skills: List[str] = Field(default_factory=list, description="Required skills")
    preferred_skills: List[str] = Field(default_factory=list, description="Preferred skills")
    
    # Experience requirements
    min_years_experience: int = Field(0, ge=0, description="Minimum years of experience")
    max_years_experience: Optional[int] = Field(None, ge=0, description="Maximum years of experience")
    
    # Education and certifications
    education_requirements: List[str] = Field(default_factory=list, description="Education requirements")
    required_certifications: List[str] = Field(default_factory=list, description="Required certifications")
    preferred_certifications: List[str] = Field(default_factory=list, description="Preferred certifications")
    
    # Compensation
    salary_min: Optional[float] = Field(None, ge=0, description="Minimum salary")
    salary_max: Optional[float] = Field(None, ge=0, description="Maximum salary")
    salary_currency: str = Field("USD", description="Salary currency")
    
    # Career progression
    career_progression_from: List[str] = Field(default_factory=list, description="Previous roles")
    career_progression_to: List[str] = Field(default_factory=list, description="Next roles")
    
    # Metadata
    demand_level: DemandLevelEnum = Field(DemandLevelEnum.MEDIUM, description="Market demand level")
    remote_friendly: bool = Field(True, description="Remote work friendly")
    
    @validator('max_years_experience')
    def validate_experience_range(cls, v, values):
        """Validate that max experience is greater than min experience."""
        if v is not None and 'min_years_experience' in values:
            if v < values['min_years_experience']:
                raise ValueError('max_years_experience must be greater than min_years_experience')
        return v
    
    @validator('salary_max')
    def validate_salary_range(cls, v, values):
        """Validate that max salary is greater than min salary."""
        if v is not None and 'salary_min' in values and values['salary_min'] is not None:
            if v < values['salary_min']:
                raise ValueError('salary_max must be greater than salary_min')
        return v


class SecurityJobTypeCreate(SecurityJobTypeBase):
    """Schema for creating a security job type."""
    pass


class SecurityJobTypeUpdate(BaseModel):
    """Schema for updating a security job type."""
    title: Optional[str] = Field(None, description="Job title")
    description: Optional[str] = Field(None, description="Job description")
    responsibilities: Optional[List[str]] = Field(None, description="Key responsibilities")
    required_skills: Optional[List[str]] = Field(None, description="Required skills")
    preferred_skills: Optional[List[str]] = Field(None, description="Preferred skills")
    salary_min: Optional[float] = Field(None, ge=0, description="Minimum salary")
    salary_max: Optional[float] = Field(None, ge=0, description="Maximum salary")
    demand_level: Optional[DemandLevelEnum] = Field(None, description="Market demand level")
    remote_friendly: Optional[bool] = Field(None, description="Remote work friendly")


class SecurityJobTypeResponse(SecurityJobTypeBase):
    """Schema for security job type response."""
    id: int = Field(..., description="Job type ID")
    is_active: bool = Field(..., description="Whether job type is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    # Computed fields
    salary_range: SalaryRange = Field(..., description="Salary range information")
    career_progression: CareerProgression = Field(..., description="Career progression paths")
    
    class Config:
        from_attributes = True


class SecurityJobTypeListResponse(BaseModel):
    """Schema for security job type list response."""
    job_types: List[SecurityJobTypeResponse] = Field(..., description="List of job types")
    total_count: int = Field(..., description="Total number of job types")
    filters_applied: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")


# Security Career Path schemas

class CareerStage(BaseModel):
    """Career stage information."""
    level: SeniorityLevelEnum = Field(..., description="Seniority level")
    duration_years: int = Field(..., ge=0, description="Typical duration in years")
    key_skills: List[str] = Field(default_factory=list, description="Key skills for this stage")
    certifications: List[str] = Field(default_factory=list, description="Recommended certifications")


class SecurityCareerPathBase(BaseModel):
    """Base schema for security career paths."""
    name: str = Field(..., description="Career path name")
    description: Optional[str] = Field(None, description="Career path description")
    security_area: SecurityAreaEnum = Field(..., description="Security area")
    
    # Path definition
    entry_job_types: List[str] = Field(default_factory=list, description="Starting job types")
    progression_stages: List[CareerStage] = Field(default_factory=list, description="Career stages")
    terminal_job_types: List[str] = Field(default_factory=list, description="End job types")
    
    # Requirements
    typical_duration_years: int = Field(10, ge=1, description="Typical duration in years")
    required_certifications: List[str] = Field(default_factory=list, description="Required certifications")
    recommended_education: List[str] = Field(default_factory=list, description="Recommended education")
    
    # Success metrics
    success_rate: float = Field(0.0, ge=0.0, le=1.0, description="Success rate (0-1)")
    average_salary_growth: float = Field(0.0, ge=0.0, description="Average salary growth percentage")


class SecurityCareerPathCreate(SecurityCareerPathBase):
    """Schema for creating a security career path."""
    pass


class SecurityCareerPathResponse(SecurityCareerPathBase):
    """Schema for security career path response."""
    id: int = Field(..., description="Career path ID")
    is_active: bool = Field(..., description="Whether path is active")
    popularity_score: float = Field(..., description="Popularity score")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True


# Security Skill Matrix schemas

class SecuritySkillMatrixBase(BaseModel):
    """Base schema for security skill matrix."""
    security_area: SecurityAreaEnum = Field(..., description="Security area")
    job_family: str = Field(..., description="Job family")
    seniority_level: SeniorityLevelEnum = Field(..., description="Seniority level")
    
    # Skills
    core_skills: List[str] = Field(default_factory=list, description="Core skills")
    advanced_skills: List[str] = Field(default_factory=list, description="Advanced skills")
    leadership_skills: List[str] = Field(default_factory=list, description="Leadership skills")
    business_skills: List[str] = Field(default_factory=list, description="Business skills")
    
    # Certifications by level
    entry_certifications: List[str] = Field(default_factory=list, description="Entry certifications")
    intermediate_certifications: List[str] = Field(default_factory=list, description="Intermediate certifications")
    advanced_certifications: List[str] = Field(default_factory=list, description="Advanced certifications")
    expert_certifications: List[str] = Field(default_factory=list, description="Expert certifications")
    
    # Tools
    required_tools: List[str] = Field(default_factory=list, description="Required tools")
    preferred_tools: List[str] = Field(default_factory=list, description="Preferred tools")


class SecuritySkillMatrixCreate(SecuritySkillMatrixBase):
    """Schema for creating a security skill matrix."""
    pass


class SecuritySkillMatrixResponse(SecuritySkillMatrixBase):
    """Schema for security skill matrix response."""
    id: int = Field(..., description="Skill matrix ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    # Computed fields
    skills: SkillSet = Field(..., description="Organized skill set")
    certifications: CertificationSet = Field(..., description="Organized certification set")
    tools: ToolSet = Field(..., description="Organized tool set")
    
    class Config:
        from_attributes = True


# Security Market Data schemas

class LocationInfo(BaseModel):
    """Location information."""
    country: str = Field("United States", description="Country")
    region: Optional[str] = Field(None, description="Region/state")
    city: Optional[str] = Field(None, description="City")


class SalaryData(BaseModel):
    """Comprehensive salary data."""
    average: Optional[float] = Field(None, description="Average salary")
    median: Optional[float] = Field(None, description="Median salary")
    percentile_25: Optional[float] = Field(None, description="25th percentile salary")
    percentile_75: Optional[float] = Field(None, description="75th percentile salary")
    percentile_90: Optional[float] = Field(None, description="90th percentile salary")


class MarketMetrics(BaseModel):
    """Market metrics information."""
    job_openings: int = Field(0, ge=0, description="Number of job openings")
    demand_score: float = Field(0.0, ge=0.0, le=100.0, description="Demand score (0-100)")
    competition_score: float = Field(0.0, ge=0.0, le=100.0, description="Competition score (0-100)")
    growth_rate: float = Field(0.0, description="Annual growth rate percentage")


class CompensationDetails(BaseModel):
    """Compensation details."""
    typical_bonus_percentage: float = Field(0.0, ge=0.0, description="Typical bonus percentage")
    stock_options_common: bool = Field(False, description="Stock options commonly offered")
    remote_work_percentage: float = Field(0.0, ge=0.0, le=100.0, description="Remote work percentage")


class SecurityMarketDataBase(BaseModel):
    """Base schema for security market data."""
    security_area: SecurityAreaEnum = Field(..., description="Security area")
    job_family: str = Field(..., description="Job family")
    seniority_level: SeniorityLevelEnum = Field(..., description="Seniority level")
    
    # Location
    country: str = Field("United States", description="Country")
    region: Optional[str] = Field(None, description="Region/state")
    city: Optional[str] = Field(None, description="City")
    
    # Salary data
    average_salary: Optional[float] = Field(None, ge=0, description="Average salary")
    median_salary: Optional[float] = Field(None, ge=0, description="Median salary")
    salary_percentile_25: Optional[float] = Field(None, ge=0, description="25th percentile")
    salary_percentile_75: Optional[float] = Field(None, ge=0, description="75th percentile")
    salary_percentile_90: Optional[float] = Field(None, ge=0, description="90th percentile")
    
    # Market data
    job_openings: int = Field(0, ge=0, description="Job openings")
    demand_score: float = Field(0.0, ge=0.0, le=100.0, description="Demand score")
    competition_score: float = Field(0.0, ge=0.0, le=100.0, description="Competition score")
    growth_rate: float = Field(0.0, description="Growth rate percentage")
    
    # Compensation
    typical_bonus_percentage: float = Field(0.0, ge=0.0, description="Bonus percentage")
    stock_options_common: bool = Field(False, description="Stock options common")
    remote_work_percentage: float = Field(0.0, ge=0.0, le=100.0, description="Remote work percentage")
    
    # Data source
    data_source: Optional[str] = Field(None, description="Data source")


class SecurityMarketDataCreate(SecurityMarketDataBase):
    """Schema for creating security market data."""
    pass


class SecurityMarketDataResponse(SecurityMarketDataBase):
    """Schema for security market data response."""
    id: int = Field(..., description="Market data ID")
    data_collection_date: datetime = Field(..., description="Data collection date")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    # Computed fields
    location: LocationInfo = Field(..., description="Location information")
    salary_data: SalaryData = Field(..., description="Salary data")
    market_metrics: MarketMetrics = Field(..., description="Market metrics")
    compensation_details: CompensationDetails = Field(..., description="Compensation details")
    
    class Config:
        from_attributes = True


# Query and filter schemas

class SecurityAreaFilter(BaseModel):
    """Filter for security areas."""
    security_areas: Optional[List[SecurityAreaEnum]] = Field(None, description="Security areas to filter")
    seniority_levels: Optional[List[SeniorityLevelEnum]] = Field(None, description="Seniority levels to filter")
    job_families: Optional[List[str]] = Field(None, description="Job families to filter")
    demand_levels: Optional[List[DemandLevelEnum]] = Field(None, description="Demand levels to filter")
    remote_friendly: Optional[bool] = Field(None, description="Remote work friendly filter")
    min_salary: Optional[float] = Field(None, ge=0, description="Minimum salary filter")
    max_salary: Optional[float] = Field(None, ge=0, description="Maximum salary filter")
    min_experience: Optional[int] = Field(None, ge=0, description="Minimum experience filter")
    max_experience: Optional[int] = Field(None, ge=0, description="Maximum experience filter")


class CareerRecommendationRequest(BaseModel):
    """Request for career recommendations."""
    current_role: Optional[str] = Field(None, description="Current job role")
    security_area: Optional[SecurityAreaEnum] = Field(None, description="Preferred security area")
    target_seniority: Optional[SeniorityLevelEnum] = Field(None, description="Target seniority level")
    years_experience: int = Field(0, ge=0, description="Years of experience")
    current_certifications: List[str] = Field(default_factory=list, description="Current certifications")
    preferred_skills: List[str] = Field(default_factory=list, description="Preferred skills")
    salary_expectations: Optional[SalaryRange] = Field(None, description="Salary expectations")
    location_preferences: Optional[LocationInfo] = Field(None, description="Location preferences")
    remote_work_preference: bool = Field(True, description="Remote work preference")


class CareerRecommendationResponse(BaseModel):
    """Response for career recommendations."""
    recommended_roles: List[SecurityJobTypeResponse] = Field(..., description="Recommended job roles")
    career_paths: List[SecurityCareerPathResponse] = Field(..., description="Recommended career paths")
    skill_gaps: List[str] = Field(..., description="Identified skill gaps")
    certification_recommendations: List[str] = Field(..., description="Recommended certifications")
    market_insights: Dict[str, Any] = Field(..., description="Market insights")
    next_steps: List[str] = Field(..., description="Recommended next steps")


# Summary and analytics schemas

class SecurityAreaSummary(BaseModel):
    """Summary for a security area."""
    security_area: SecurityAreaEnum = Field(..., description="Security area")
    total_job_types: int = Field(..., description="Total job types")
    average_salary: Optional[float] = Field(None, description="Average salary")
    demand_level: DemandLevelEnum = Field(..., description="Overall demand level")
    growth_rate: float = Field(..., description="Growth rate")
    top_skills: List[str] = Field(..., description="Top required skills")
    top_certifications: List[str] = Field(..., description="Top certifications")
    remote_work_percentage: float = Field(..., description="Remote work percentage")


class SecurityCareerAnalytics(BaseModel):
    """Analytics for security careers."""
    total_job_types: int = Field(..., description="Total job types")
    total_career_paths: int = Field(..., description="Total career paths")
    security_area_summaries: List[SecurityAreaSummary] = Field(..., description="Area summaries")
    trending_skills: List[str] = Field(..., description="Trending skills")
    highest_demand_roles: List[str] = Field(..., description="Highest demand roles")
    highest_paying_roles: List[str] = Field(..., description="Highest paying roles")
    fastest_growing_areas: List[str] = Field(..., description="Fastest growing areas")
    certification_popularity: Dict[str, int] = Field(..., description="Certification popularity")
    market_trends: Dict[str, Any] = Field(..., description="Market trends")
    generated_at: datetime = Field(..., description="Analytics generation timestamp")
