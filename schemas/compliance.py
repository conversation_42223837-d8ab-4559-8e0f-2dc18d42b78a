"""Pydantic schemas for compliance and audit management.

This module provides comprehensive schemas for compliance requirements,
assessments, reports, audit logs, and regulatory data structures.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from models.compliance import ComplianceFramework, ComplianceStatus, AuditEventType, RiskLevel


# Base Schemas

class ComplianceBase(BaseModel):
    """Base schema for compliance-related models."""
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# Compliance Requirement Schemas

class ComplianceRequirementBase(ComplianceBase):
    """Base schema for compliance requirements."""
    framework: ComplianceFramework
    requirement_id: str = Field(..., max_length=100)
    title: str = Field(..., max_length=500)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    implementation_guidance: Optional[str] = None
    evidence_required: Optional[List[str]] = []
    automated_check: bool = False
    check_frequency: Optional[str] = Field(None, max_length=50)
    risk_level: RiskLevel = RiskLevel.MEDIUM
    assigned_to: Optional[str] = Field(None, max_length=255)
    tags: Optional[List[str]] = []
    custom_fields: Optional[Dict[str, Any]] = {}


class ComplianceRequirementCreate(ComplianceRequirementBase):
    """Schema for creating compliance requirements."""
    pass


class ComplianceRequirementUpdate(BaseModel):
    """Schema for updating compliance requirements."""
    title: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    implementation_guidance: Optional[str] = None
    evidence_required: Optional[List[str]] = None
    automated_check: Optional[bool] = None
    check_frequency: Optional[str] = Field(None, max_length=50)
    status: Optional[ComplianceStatus] = None
    risk_level: Optional[RiskLevel] = None
    assigned_to: Optional[str] = Field(None, max_length=255)
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class ComplianceRequirementResponse(ComplianceRequirementBase):
    """Schema for compliance requirement responses."""
    id: int
    organization_id: int
    status: ComplianceStatus
    last_assessed: Optional[datetime] = None
    next_assessment_due: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


# Compliance Assessment Schemas

class ComplianceAssessmentBase(ComplianceBase):
    """Base schema for compliance assessments."""
    assessment_date: datetime = Field(default_factory=datetime.utcnow)
    assessment_method: Optional[str] = Field(None, max_length=100)
    status: ComplianceStatus
    score: Optional[float] = Field(None, ge=0, le=100)
    findings: Optional[str] = None
    evidence_provided: Optional[List[str]] = []
    gaps_identified: Optional[List[str]] = []
    remediation_plan: Optional[str] = None
    remediation_due_date: Optional[datetime] = None
    remediation_status: Optional[str] = Field(None, max_length=50)
    notes: Optional[str] = None
    attachments: Optional[List[str]] = []


class ComplianceAssessmentCreate(ComplianceAssessmentBase):
    """Schema for creating compliance assessments."""
    pass


class ComplianceAssessmentResponse(ComplianceAssessmentBase):
    """Schema for compliance assessment responses."""
    id: int
    requirement_id: int
    organization_id: int
    assessor_id: str
    created_at: datetime
    updated_at: datetime


# Compliance Report Schemas

class ComplianceReportGenerate(ComplianceBase):
    """Schema for generating compliance reports."""
    framework: ComplianceFramework
    period_start: datetime
    period_end: datetime
    report_name: Optional[str] = None
    
    @validator('period_end')
    def validate_period_end(cls, v, values):
        if 'period_start' in values and v <= values['period_start']:
            raise ValueError('period_end must be after period_start')
        return v


class ComplianceReportResponse(ComplianceBase):
    """Schema for compliance report responses."""
    id: int
    organization_id: int
    report_type: ComplianceFramework
    report_name: str
    report_period_start: datetime
    report_period_end: datetime
    generated_by: str
    generated_at: datetime
    generation_method: str
    executive_summary: Optional[str] = None
    overall_status: ComplianceStatus
    overall_score: Optional[float] = None
    compliant_requirements: List[Dict[str, Any]] = []
    non_compliant_requirements: List[Dict[str, Any]] = []
    partially_compliant_requirements: List[Dict[str, Any]] = []
    high_risk_findings: List[Dict[str, Any]] = []
    recommendations: List[str] = []
    action_items: List[Dict[str, Any]] = []
    report_data: Dict[str, Any] = {}
    file_path: Optional[str] = None
    file_format: Optional[str] = None
    status: str
    submitted_at: Optional[datetime] = None
    submitted_to: Optional[str] = None


# Audit Log Schemas

class AuditLogResponse(ComplianceBase):
    """Schema for audit log responses."""
    id: int
    organization_id: int
    event_type: AuditEventType
    event_category: Optional[str] = None
    event_description: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    resource_name: Optional[str] = None
    before_state: Optional[Dict[str, Any]] = None
    after_state: Optional[Dict[str, Any]] = None
    changes_made: List[str] = []
    risk_level: RiskLevel
    compliance_relevant: bool
    retention_period_days: int
    additional_data: Dict[str, Any] = {}
    tags: List[str] = []
    created_at: datetime


# Data Processing Activity Schemas (GDPR)

class DataProcessingActivityBase(ComplianceBase):
    """Base schema for GDPR data processing activities."""
    activity_name: str = Field(..., max_length=200)
    activity_description: str
    purpose_of_processing: str
    legal_basis: str = Field(..., max_length=100)
    data_categories: List[str] = []
    data_subjects: List[str] = []
    data_sources: Optional[List[str]] = []
    processing_methods: Optional[List[str]] = []
    automated_decision_making: bool = False
    profiling_activities: bool = False
    recipients: Optional[List[str]] = []
    third_country_transfers: Optional[List[str]] = []
    safeguards_applied: Optional[List[str]] = []
    retention_period: Optional[str] = Field(None, max_length=100)
    security_measures: Optional[List[str]] = []
    dpia_required: bool = False
    dpia_completed: bool = False


class DataProcessingActivityCreate(DataProcessingActivityBase):
    """Schema for creating data processing activities."""
    pass


class DataProcessingActivityUpdate(BaseModel):
    """Schema for updating data processing activities."""
    activity_name: Optional[str] = Field(None, max_length=200)
    activity_description: Optional[str] = None
    purpose_of_processing: Optional[str] = None
    legal_basis: Optional[str] = Field(None, max_length=100)
    data_categories: Optional[List[str]] = None
    data_subjects: Optional[List[str]] = None
    data_sources: Optional[List[str]] = None
    processing_methods: Optional[List[str]] = None
    automated_decision_making: Optional[bool] = None
    profiling_activities: Optional[bool] = None
    recipients: Optional[List[str]] = None
    third_country_transfers: Optional[List[str]] = None
    safeguards_applied: Optional[List[str]] = None
    retention_period: Optional[str] = Field(None, max_length=100)
    security_measures: Optional[List[str]] = None
    dpia_required: Optional[bool] = None
    dpia_completed: Optional[bool] = None


class DataProcessingActivityResponse(DataProcessingActivityBase):
    """Schema for data processing activity responses."""
    id: int
    organization_id: int
    last_reviewed: Optional[datetime] = None
    next_review_due: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


# Compliance Dashboard Schemas

class ComplianceDashboardResponse(ComplianceBase):
    """Schema for compliance dashboard data."""
    organization_id: int
    overall_compliance_score: float
    frameworks_summary: Dict[str, Dict[str, Any]]
    recent_assessments: List[ComplianceAssessmentResponse]
    pending_actions: List[Dict[str, Any]]
    high_risk_findings: List[Dict[str, Any]]
    compliance_trends: Dict[str, List[float]]
    audit_activity_summary: Dict[str, int]
    upcoming_deadlines: List[Dict[str, Any]]


# Risk Assessment Schemas

class RiskAssessmentResponse(ComplianceBase):
    """Schema for risk assessment responses."""
    organization_id: int
    assessment_date: datetime
    overall_risk_score: float
    risk_categories: Dict[str, float]
    high_risk_areas: List[Dict[str, Any]]
    risk_trends: Dict[str, List[float]]
    mitigation_recommendations: List[str]
    next_assessment_due: datetime


# Compliance Metrics Schemas

class ComplianceMetricsResponse(ComplianceBase):
    """Schema for compliance metrics and KPIs."""
    organization_id: int
    reporting_period: Dict[str, datetime]
    compliance_scores: Dict[str, float]
    assessment_completion_rate: float
    remediation_completion_rate: float
    average_remediation_time_days: float
    audit_findings_count: int
    high_risk_findings_count: int
    compliance_training_completion_rate: float
    policy_acknowledgment_rate: float
    incident_response_time_hours: float
