"""Pydantic models for certification API"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class CertificationBase(BaseModel):
    """Base certification fields"""
    name: str
    category: str
    domain: str
    level: str
    focus: str
    difficulty: int
    cost: Optional[float] = None
    description: Optional[str] = None
    prerequisites: Optional[str] = None
    validity_period: Optional[int] = None
    exam_code: Optional[str] = None
    url: Optional[str] = None

class CertificationCreate(CertificationBase):
    """Fields required for creating a certification"""
    pass

class CertificationResponse(CertificationBase):
    """Full certification response including database fields"""
    id: int
    organization_id: Optional[int] = None
    custom_hours: Optional[int] = None
    study_notes: Optional[str] = None
    current_version: int
    last_updated: datetime
    created_at: datetime

    model_config = {"from_attributes": True}

class CertificationList(BaseModel):
    """List of certifications response with pagination"""
    certifications: List[CertificationResponse]
    total_count: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None
    total_pages: Optional[int] = None

class CertificationSearchRequest(BaseModel):
    """Request model for certification search"""
    query: str = Field(..., min_length=2, description="Search query")
    domain: Optional[str] = None
    level: Optional[str] = None
    difficulty: Optional[int] = Field(None, ge=1, le=4)
    min_cost: Optional[float] = Field(None, ge=0)
    max_cost: Optional[float] = Field(None, ge=0)
    organization: Optional[str] = None


class CertificationComparisonRequest(BaseModel):
    """Request model for certification comparison"""
    certification_ids: List[int] = Field(..., min_items=2, max_items=5, description="List of certification IDs to compare")


class CertificationComparisonResponse(BaseModel):
    """Response model for certification comparison"""
    certifications: List[CertificationResponse]
    comparison_matrix: Dict[str, Dict[int, Any]]
    summary: Dict[str, Any]


class StudyPlanResponse(BaseModel):
    """Study plan response model"""
    status: str
    total_hours_required: int
    weeks_required: float
    suggested_target: Optional[datetime] = None
    message: Optional[str] = None
    weekly_schedule: List[Dict[str, Any]]
    study_tips: List[str]
