"""Pydantic schemas for CertRatsAgent4 Unified API.

This module provides comprehensive request/response schemas for the unified
AI Study Assistant and Career & Cost Intelligence integration.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

from schemas.ai_study_assistant import StudyRecommendationResponse, LearningInsightResponse, AdaptivePathResponse
from schemas.career_transition import CareerPathfindingResponse
from schemas.salary_intelligence import ROIAnalysisResponse


class DifficultyLevel(str, Enum):
    """Enumeration of difficulty levels."""
    EASY = "Easy"
    MEDIUM = "Medium"
    HARD = "Hard"
    EXPERT = "Expert"


class LearningStyle(str, Enum):
    """Enumeration of learning styles."""
    VISUAL = "visual"
    AUDITORY = "auditory"
    KINESTHETIC = "kinesthetic"
    MIXED = "mixed"


class Currency(str, Enum):
    """Enumeration of supported currencies."""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    CAD = "CAD"


# Request Schemas

class ComprehensivePlanRequest(BaseModel):
    """Request schema for comprehensive certification and career planning."""
    
    target_certification_id: int = Field(..., description="Target certification ID")
    current_role_id: Optional[int] = Field(None, description="Current role ID")
    target_role_id: Optional[int] = Field(None, description="Target role ID for career transition")
    max_budget: Optional[float] = Field(None, ge=0, description="Maximum budget for investment")
    max_timeline_months: Optional[int] = Field(None, ge=1, le=60, description="Maximum timeline in months")
    max_difficulty: DifficultyLevel = Field(DifficultyLevel.MEDIUM, description="Maximum difficulty level")
    learning_style: LearningStyle = Field(LearningStyle.MIXED, description="Preferred learning style")
    study_hours_per_week: int = Field(10, ge=1, le=40, description="Available study hours per week")
    currency: Currency = Field(Currency.USD, description="Currency for cost calculations")
    location: str = Field("remote", description="Location for salary analysis")
    experience_years: int = Field(5, ge=0, le=50, description="Years of experience")
    target_date: Optional[datetime] = Field(None, description="Target completion date")
    
    class Config:
        schema_extra = {
            "example": {
                "target_certification_id": 1,
                "current_role_id": 5,
                "target_role_id": 8,
                "max_budget": 5000.0,
                "max_timeline_months": 12,
                "max_difficulty": "Medium",
                "learning_style": "visual",
                "study_hours_per_week": 15,
                "currency": "USD",
                "location": "remote",
                "experience_years": 7,
                "target_date": "2024-12-31T00:00:00Z"
            }
        }


class EnterpriseAnalysisRequest(BaseModel):
    """Request schema for enterprise training needs analysis."""
    
    enterprise_id: int = Field(..., description="Enterprise ID")
    total_budget: float = Field(..., ge=0, description="Total training budget")
    budget_period_months: int = Field(12, ge=1, le=36, description="Budget period in months")
    strategic_priorities: Optional[List[str]] = Field(None, description="Strategic priority areas")
    include_roi_analysis: bool = Field(True, description="Include ROI analysis")
    include_risk_assessment: bool = Field(True, description="Include risk assessment")
    
    class Config:
        schema_extra = {
            "example": {
                "enterprise_id": 1,
                "total_budget": 100000.0,
                "budget_period_months": 12,
                "strategic_priorities": ["cybersecurity", "cloud_security", "compliance"],
                "include_roi_analysis": True,
                "include_risk_assessment": True
            }
        }


# Response Schemas

class KnowledgeAssessmentSummary(BaseModel):
    """Summary of knowledge assessment."""
    
    knowledge_level: float = Field(..., ge=0.0, le=1.0, description="Knowledge level (0-1)")
    knowledge_percentage: float = Field(..., ge=0.0, le=100.0, description="Knowledge percentage")
    difficulty_level: str = Field(..., description="Recommended difficulty level")
    weak_areas: List[str] = Field(default_factory=list, description="Identified weak areas")
    strong_areas: List[str] = Field(default_factory=list, description="Identified strong areas")


class ComprehensivePlanResponse(BaseModel):
    """Response schema for comprehensive certification and career plan."""
    
    user_id: str = Field(..., description="User ID")
    plan_id: str = Field(..., description="Unique plan identifier")
    study_recommendations: List[StudyRecommendationResponse] = Field(..., description="AI study recommendations")
    adaptive_learning_path: AdaptivePathResponse = Field(..., description="Adaptive learning path")
    career_transition_paths: List[Dict[str, Any]] = Field(..., description="Career transition options")
    roi_analysis: ROIAnalysisResponse = Field(..., description="ROI analysis")
    learning_insights: List[LearningInsightResponse] = Field(..., description="Learning insights")
    knowledge_assessment: Optional[KnowledgeAssessmentSummary] = Field(None, description="Knowledge assessment")
    integrated_recommendations: List[str] = Field(..., description="Integrated recommendations")
    success_probability: float = Field(..., ge=0.0, le=1.0, description="Overall success probability")
    estimated_timeline_weeks: int = Field(..., description="Estimated timeline in weeks")
    total_investment_estimate: float = Field(..., description="Total investment estimate")
    expected_salary_increase: float = Field(..., description="Expected salary increase")
    plan_created_at: str = Field(..., description="Plan creation timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "test_user_1",
                "plan_id": "comprehensive_test_user_1_20240115_143000",
                "success_probability": 0.82,
                "estimated_timeline_weeks": 16,
                "total_investment_estimate": 4500.0,
                "expected_salary_increase": 18000.0,
                "plan_created_at": "2024-01-15T14:30:00Z"
            }
        }


class SalaryInsights(BaseModel):
    """Salary insights summary."""
    
    current_salary_range: Dict[str, float] = Field(..., description="Current salary range")
    market_position: str = Field(..., description="Market position")
    growth_potential: float = Field(..., description="Growth potential")


class PersonalizedDashboardResponse(BaseModel):
    """Response schema for personalized dashboard."""
    
    user_id: str = Field(..., description="User ID")
    dashboard_date: str = Field(..., description="Dashboard generation date")
    top_recommendations: List[StudyRecommendationResponse] = Field(..., description="Top recommendations")
    key_insights: List[LearningInsightResponse] = Field(..., description="Key insights")
    current_knowledge_level: float = Field(..., ge=0.0, le=1.0, description="Current knowledge level")
    study_efficiency_score: float = Field(..., ge=0.0, le=1.0, description="Study efficiency score")
    consistency_score: float = Field(..., ge=0.0, le=1.0, description="Study consistency score")
    learning_style: str = Field(..., description="Identified learning style")
    salary_insights: Optional[SalaryInsights] = Field(None, description="Salary insights")
    market_trends: Optional[Dict[str, Any]] = Field(None, description="Market trends")
    next_milestones: List[str] = Field(..., description="Next milestones")
    motivational_message: str = Field(..., description="Personalized motivational message")
    quick_actions: List[str] = Field(..., description="Quick action suggestions")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "test_user_1",
                "dashboard_date": "2024-01-15T14:30:00Z",
                "current_knowledge_level": 0.65,
                "study_efficiency_score": 0.78,
                "consistency_score": 0.82,
                "learning_style": "visual",
                "motivational_message": "Great work! You're building strong study habits.",
                "next_milestones": [
                    "Complete next practice test",
                    "Review weak areas from last session"
                ]
            }
        }


class EnterpriseMetrics(BaseModel):
    """Enterprise analysis metrics."""
    
    total_employees_estimated: int = Field(..., description="Estimated total employees")
    teams_analyzed: int = Field(..., description="Number of teams analyzed")
    average_roi_projection: float = Field(..., description="Average ROI projection")
    budget_efficiency_score: float = Field(..., description="Budget efficiency score")
    strategic_alignment_score: float = Field(..., description="Strategic alignment score")


class EnterpriseAnalysisResponse(BaseModel):
    """Response schema for enterprise training needs analysis."""
    
    enterprise_id: int = Field(..., description="Enterprise ID")
    analysis_date: str = Field(..., description="Analysis date")
    total_budget: float = Field(..., description="Total budget")
    optimized_allocation: Dict[str, float] = Field(..., description="Optimized budget allocation")
    projected_cost_savings: float = Field(..., description="Projected cost savings")
    roi_projections: Dict[str, float] = Field(..., description="ROI projections")
    team_priorities: List[Dict[str, Any]] = Field(..., description="Team priorities")
    recommended_certifications: List[Dict[str, Any]] = Field(..., description="Recommended certifications")
    implementation_timeline: Dict[str, Any] = Field(..., description="Implementation timeline")
    risk_assessment: Dict[str, Any] = Field(..., description="Risk assessment")
    enterprise_metrics: EnterpriseMetrics = Field(..., description="Enterprise metrics")
    key_insights: List[str] = Field(..., description="Key insights")
    next_steps: List[str] = Field(..., description="Recommended next steps")
    
    class Config:
        schema_extra = {
            "example": {
                "enterprise_id": 1,
                "analysis_date": "2024-01-15T14:30:00Z",
                "total_budget": 100000.0,
                "projected_cost_savings": 15000.0,
                "enterprise_metrics": {
                    "total_employees_estimated": 50,
                    "teams_analyzed": 5,
                    "average_roi_projection": 22.5,
                    "budget_efficiency_score": 85.0,
                    "strategic_alignment_score": 90.0
                }
            }
        }


class UnifiedHealthResponse(BaseModel):
    """Response schema for unified service health check."""
    
    status: str = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    agents: Dict[str, str] = Field(..., description="Agent status")
    features: List[str] = Field(..., description="Available features")
    timestamp: str = Field(..., description="Health check timestamp")
