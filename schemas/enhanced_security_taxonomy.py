"""Enhanced Security Taxonomy Schemas.

This module provides comprehensive Pydantic schemas for the enhanced security
taxonomy that captures real-world job market data, skills, and career progression.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class SecurityDomainEnum(str, Enum):
    """Enhanced security domains based on industry analysis."""
    DEFENSIVE_SECURITY = "defensive_security"
    OFFENSIVE_SECURITY = "offensive_security"
    SECURITY_ENGINEERING = "security_engineering"
    SECURITY_MANAGEMENT = "security_management"
    NETWORK_SECURITY = "network_security"
    IDENTITY_ACCESS_MANAGEMENT = "identity_access_management"
    ASSET_SECURITY = "asset_security"
    SECURITY_TESTING = "security_testing"
    SOFTWARE_DEVELOPMENT_SECURITY = "software_development_security"
    CLOUD_SECURITY = "cloud_security"
    COMPLIANCE_GOVERNANCE = "compliance_governance"
    INCIDENT_RESPONSE = "incident_response"


class SkillCategoryEnum(str, Enum):
    """Categories of security skills."""
    TECHNICAL_CORE = "technical_core"
    TECHNICAL_ADVANCED = "technical_advanced"
    LEADERSHIP = "leadership"
    BUSINESS = "business"
    COMMUNICATION = "communication"
    ANALYTICAL = "analytical"
    COMPLIANCE = "compliance"
    VENDOR_SPECIFIC = "vendor_specific"


class SkillLevelEnum(str, Enum):
    """Skill proficiency levels."""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"
    THOUGHT_LEADER = "thought_leader"


class CertificationLevelEnum(str, Enum):
    """Certification difficulty and recognition levels."""
    ENTRY = "entry"
    ASSOCIATE = "associate"
    PROFESSIONAL = "professional"
    EXPERT = "expert"
    ARCHITECT = "architect"
    SPECIALIST = "specialist"


# Base schemas for common data structures

class MarketData(BaseModel):
    """Market data for skills, certifications, or job titles."""
    demand_score: float = Field(0.0, ge=0.0, le=100.0, description="Market demand score (0-100)")
    salary_impact: float = Field(0.0, description="Salary impact percentage")
    job_frequency: int = Field(0, ge=0, description="Frequency in job postings")


class LearningResources(BaseModel):
    """Learning resources for skills or certifications."""
    official_training: List[str] = Field(default_factory=list, description="Official training courses")
    study_materials: List[str] = Field(default_factory=list, description="Books, videos, articles")
    practice_platforms: List[str] = Field(default_factory=list, description="Labs, simulators")
    hands_on_labs: List[str] = Field(default_factory=list, description="Hands-on practice labs")


class CareerProgression(BaseModel):
    """Career progression information."""
    entry_level_roles: List[str] = Field(default_factory=list, description="Entry-level roles")
    advancement_roles: List[str] = Field(default_factory=list, description="Advancement opportunities")
    lateral_move_roles: List[str] = Field(default_factory=list, description="Lateral move options")


# Security Skill Taxonomy schemas

class SecuritySkillTaxonomyBase(BaseModel):
    """Base schema for security skill taxonomy."""
    skill_name: str = Field(..., description="Name of the skill")
    skill_category: SkillCategoryEnum = Field(..., description="Category of the skill")
    security_domains: List[SecurityDomainEnum] = Field(default_factory=list, description="Applicable security domains")
    description: Optional[str] = Field(None, description="Skill description")
    aliases: List[str] = Field(default_factory=list, description="Alternative names")
    related_skills: List[str] = Field(default_factory=list, description="Related skills")
    demand_score: float = Field(0.0, ge=0.0, le=100.0, description="Market demand score")
    salary_impact: float = Field(0.0, description="Salary impact percentage")
    job_frequency: int = Field(0, ge=0, description="Job posting frequency")
    learning_resources: List[str] = Field(default_factory=list, description="Learning resources")
    practice_platforms: List[str] = Field(default_factory=list, description="Practice platforms")
    is_trending: bool = Field(False, description="Is currently trending")
    is_emerging: bool = Field(False, description="Is an emerging skill")
    obsolescence_risk: str = Field("low", description="Risk of becoming obsolete")


class SecuritySkillTaxonomyCreate(SecuritySkillTaxonomyBase):
    """Schema for creating security skill taxonomy."""
    pass


class SecuritySkillTaxonomyResponse(SecuritySkillTaxonomyBase):
    """Schema for security skill taxonomy response."""
    id: int = Field(..., description="Skill ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True


# Security Certification Taxonomy schemas

class ExamDetails(BaseModel):
    """Exam details for certifications."""
    cost: Optional[float] = Field(None, description="Exam cost")
    duration_hours: Optional[float] = Field(None, description="Exam duration in hours")
    passing_score: Optional[int] = Field(None, description="Passing score percentage")
    format: Optional[str] = Field(None, description="Exam format")


class MaintenanceRequirements(BaseModel):
    """Certification maintenance requirements."""
    validity_years: int = Field(3, description="Certification validity in years")
    cpe_requirements: int = Field(0, description="Continuing education hours")
    renewal_cost: Optional[float] = Field(None, description="Renewal cost")


class CertificationMarketData(BaseModel):
    """Market data for certifications."""
    holder_count: int = Field(0, description="Estimated number of holders")
    salary_premium: float = Field(0.0, description="Average salary increase")
    job_requirement_frequency: float = Field(0.0, description="Percentage of jobs requiring it")


class SecurityCertificationTaxonomyBase(BaseModel):
    """Base schema for security certification taxonomy."""
    certification_name: str = Field(..., description="Certification name")
    certification_code: str = Field(..., description="Certification code/abbreviation")
    issuing_organization: str = Field(..., description="Issuing organization")
    certification_level: CertificationLevelEnum = Field(..., description="Certification level")
    description: Optional[str] = Field(None, description="Certification description")
    security_domains: List[SecurityDomainEnum] = Field(default_factory=list, description="Applicable domains")
    prerequisites: List[str] = Field(default_factory=list, description="Prerequisites")
    exam_cost: Optional[float] = Field(None, description="Exam cost")
    exam_duration_hours: Optional[float] = Field(None, description="Exam duration")
    passing_score: Optional[int] = Field(None, description="Passing score")
    exam_format: Optional[str] = Field(None, description="Exam format")
    validity_years: int = Field(3, description="Validity period")
    cpe_requirements: int = Field(0, description="CPE requirements")
    renewal_cost: Optional[float] = Field(None, description="Renewal cost")
    holder_count: int = Field(0, description="Number of holders")
    salary_premium: float = Field(0.0, description="Salary premium")
    job_requirement_frequency: float = Field(0.0, description="Job requirement frequency")
    career_paths: List[str] = Field(default_factory=list, description="Career paths enabled")
    next_certifications: List[str] = Field(default_factory=list, description="Next certifications")
    official_training: List[str] = Field(default_factory=list, description="Official training")
    study_materials: List[str] = Field(default_factory=list, description="Study materials")
    practice_exams: List[str] = Field(default_factory=list, description="Practice exams")


class SecurityCertificationTaxonomyCreate(SecurityCertificationTaxonomyBase):
    """Schema for creating security certification taxonomy."""
    pass


class SecurityCertificationTaxonomyResponse(SecurityCertificationTaxonomyBase):
    """Schema for security certification taxonomy response."""
    id: int = Field(..., description="Certification ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    # Computed fields
    exam_details: ExamDetails = Field(..., description="Exam details")
    maintenance: MaintenanceRequirements = Field(..., description="Maintenance requirements")
    market_data: CertificationMarketData = Field(..., description="Market data")
    learning_resources: LearningResources = Field(..., description="Learning resources")
    
    class Config:
        from_attributes = True


# Job Title Taxonomy schemas

class JobRequirements(BaseModel):
    """Job requirements information."""
    key_responsibilities: List[str] = Field(default_factory=list, description="Key responsibilities")
    required_skills: List[str] = Field(default_factory=list, description="Required skills")
    preferred_skills: List[str] = Field(default_factory=list, description="Preferred skills")
    required_certifications: List[str] = Field(default_factory=list, description="Required certifications")
    preferred_certifications: List[str] = Field(default_factory=list, description="Preferred certifications")


class SalaryRange(BaseModel):
    """Salary range information."""
    min: Optional[float] = Field(None, description="Minimum salary")
    max: Optional[float] = Field(None, description="Maximum salary")
    average: Optional[float] = Field(None, description="Average salary")


class JobMarketData(BaseModel):
    """Job market data."""
    job_posting_frequency: int = Field(0, description="Monthly job postings")
    salary_range: SalaryRange = Field(..., description="Salary range")
    remote_work_percentage: float = Field(0.0, description="Remote work percentage")


class GeographicData(BaseModel):
    """Geographic distribution data."""
    top_locations: List[str] = Field(default_factory=list, description="Top hiring locations")
    industry_distribution: List[str] = Field(default_factory=list, description="Industry distribution")


class JobTitleTaxonomyBase(BaseModel):
    """Base schema for job title taxonomy."""
    job_title: str = Field(..., description="Job title")
    normalized_title: str = Field(..., description="Normalized title")
    security_domain: SecurityDomainEnum = Field(..., description="Primary security domain")
    seniority_level: str = Field(..., description="Seniority level")
    title_variations: List[str] = Field(default_factory=list, description="Title variations")
    company_specific_titles: List[str] = Field(default_factory=list, description="Company-specific titles")
    description: Optional[str] = Field(None, description="Job description")
    key_responsibilities: List[str] = Field(default_factory=list, description="Key responsibilities")
    required_skills: List[str] = Field(default_factory=list, description="Required skills")
    preferred_skills: List[str] = Field(default_factory=list, description="Preferred skills")
    required_certifications: List[str] = Field(default_factory=list, description="Required certifications")
    preferred_certifications: List[str] = Field(default_factory=list, description="Preferred certifications")
    job_posting_frequency: int = Field(0, description="Job posting frequency")
    average_salary: Optional[float] = Field(None, description="Average salary")
    salary_range_min: Optional[float] = Field(None, description="Salary range minimum")
    salary_range_max: Optional[float] = Field(None, description="Salary range maximum")
    remote_work_percentage: float = Field(0.0, description="Remote work percentage")
    entry_level_roles: List[str] = Field(default_factory=list, description="Entry-level roles")
    advancement_roles: List[str] = Field(default_factory=list, description="Advancement roles")
    lateral_move_roles: List[str] = Field(default_factory=list, description="Lateral move roles")
    top_locations: List[str] = Field(default_factory=list, description="Top locations")
    industry_distribution: List[str] = Field(default_factory=list, description="Industry distribution")


class JobTitleTaxonomyCreate(JobTitleTaxonomyBase):
    """Schema for creating job title taxonomy."""
    pass


class JobTitleTaxonomyResponse(JobTitleTaxonomyBase):
    """Schema for job title taxonomy response."""
    id: int = Field(..., description="Job title ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    # Computed fields
    requirements: JobRequirements = Field(..., description="Job requirements")
    market_data: JobMarketData = Field(..., description="Market data")
    career_progression: CareerProgression = Field(..., description="Career progression")
    geographic_data: GeographicData = Field(..., description="Geographic data")
    
    class Config:
        from_attributes = True


# Security Technology Taxonomy schemas

class TechnologyMarketData(BaseModel):
    """Technology market data."""
    market_share: float = Field(0.0, description="Market share percentage")
    adoption_rate: float = Field(0.0, description="Adoption growth rate")
    job_demand: int = Field(0, description="Jobs requiring this technology")


class CompetitiveLandscape(BaseModel):
    """Competitive landscape information."""
    competitors: List[str] = Field(default_factory=list, description="Competitor technologies")
    integration_partners: List[str] = Field(default_factory=list, description="Integration partners")


class SecurityTechnologyTaxonomyBase(BaseModel):
    """Base schema for security technology taxonomy."""
    technology_name: str = Field(..., description="Technology name")
    technology_category: str = Field(..., description="Technology category")
    vendor: Optional[str] = Field(None, description="Vendor name")
    security_domains: List[SecurityDomainEnum] = Field(default_factory=list, description="Applicable domains")
    description: Optional[str] = Field(None, description="Technology description")
    use_cases: List[str] = Field(default_factory=list, description="Use cases")
    deployment_models: List[str] = Field(default_factory=list, description="Deployment models")
    market_share: float = Field(0.0, description="Market share")
    adoption_rate: float = Field(0.0, description="Adoption rate")
    job_demand: int = Field(0, description="Job demand")
    official_training: List[str] = Field(default_factory=list, description="Official training")
    certifications: List[str] = Field(default_factory=list, description="Related certifications")
    hands_on_labs: List[str] = Field(default_factory=list, description="Hands-on labs")
    competitors: List[str] = Field(default_factory=list, description="Competitors")
    integration_partners: List[str] = Field(default_factory=list, description="Integration partners")


class SecurityTechnologyTaxonomyCreate(SecurityTechnologyTaxonomyBase):
    """Schema for creating security technology taxonomy."""
    pass


class SecurityTechnologyTaxonomyResponse(SecurityTechnologyTaxonomyBase):
    """Schema for security technology taxonomy response."""
    id: int = Field(..., description="Technology ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    # Computed fields
    market_data: TechnologyMarketData = Field(..., description="Market data")
    learning_resources: LearningResources = Field(..., description="Learning resources")
    competitive_landscape: CompetitiveLandscape = Field(..., description="Competitive landscape")
    
    class Config:
        from_attributes = True


# Comprehensive taxonomy query schemas

class TaxonomyFilter(BaseModel):
    """Filter for taxonomy queries."""
    security_domains: Optional[List[SecurityDomainEnum]] = Field(None, description="Security domains filter")
    skill_categories: Optional[List[SkillCategoryEnum]] = Field(None, description="Skill categories filter")
    certification_levels: Optional[List[CertificationLevelEnum]] = Field(None, description="Certification levels filter")
    seniority_levels: Optional[List[str]] = Field(None, description="Seniority levels filter")
    is_trending: Optional[bool] = Field(None, description="Trending items filter")
    is_emerging: Optional[bool] = Field(None, description="Emerging items filter")
    min_demand_score: Optional[float] = Field(None, description="Minimum demand score")
    min_salary_impact: Optional[float] = Field(None, description="Minimum salary impact")


class TaxonomyAnalytics(BaseModel):
    """Comprehensive taxonomy analytics."""
    total_skills: int = Field(..., description="Total number of skills")
    total_certifications: int = Field(..., description="Total number of certifications")
    total_job_titles: int = Field(..., description="Total number of job titles")
    total_technologies: int = Field(..., description="Total number of technologies")
    trending_skills: List[str] = Field(..., description="Trending skills")
    emerging_technologies: List[str] = Field(..., description="Emerging technologies")
    high_demand_certifications: List[str] = Field(..., description="High-demand certifications")
    top_paying_roles: List[str] = Field(..., description="Top-paying roles")
    skill_gaps: List[str] = Field(..., description="Identified skill gaps")
    market_trends: Dict[str, Any] = Field(..., description="Market trends")
    generated_at: datetime = Field(..., description="Analytics generation timestamp")


# User interaction schemas for capturing taxonomy data

class UserSkillAssessment(BaseModel):
    """User skill assessment for taxonomy interaction."""
    user_id: int = Field(..., description="User ID")
    skill_id: int = Field(..., description="Skill ID")
    skill_level: SkillLevelEnum = Field(..., description="User's skill level")
    years_experience: float = Field(0.0, description="Years of experience with skill")
    last_used: Optional[datetime] = Field(None, description="When skill was last used")
    confidence_level: int = Field(1, ge=1, le=5, description="Confidence level (1-5)")
    learning_interest: int = Field(1, ge=1, le=5, description="Interest in learning more (1-5)")
    assessment_date: datetime = Field(default_factory=datetime.utcnow, description="Assessment date")


class UserCertificationGoal(BaseModel):
    """User certification goals for taxonomy interaction."""
    user_id: int = Field(..., description="User ID")
    certification_id: int = Field(..., description="Certification ID")
    target_date: Optional[datetime] = Field(None, description="Target completion date")
    priority_level: int = Field(1, ge=1, le=5, description="Priority level (1-5)")
    current_progress: float = Field(0.0, ge=0.0, le=100.0, description="Current progress percentage")
    study_hours_per_week: float = Field(0.0, description="Planned study hours per week")
    budget_allocated: Optional[float] = Field(None, description="Budget allocated for certification")
    motivation: Optional[str] = Field(None, description="Motivation for pursuing certification")


class UserCareerGoal(BaseModel):
    """User career goals for taxonomy interaction."""
    user_id: int = Field(..., description="User ID")
    target_job_title_id: int = Field(..., description="Target job title ID")
    target_timeframe_months: int = Field(12, description="Target timeframe in months")
    current_role_similarity: float = Field(0.0, ge=0.0, le=100.0, description="Similarity to current role")
    salary_expectation: Optional[float] = Field(None, description="Salary expectation")
    location_preference: Optional[str] = Field(None, description="Location preference")
    remote_work_preference: bool = Field(True, description="Remote work preference")
    career_change_reason: Optional[str] = Field(None, description="Reason for career change")


class TaxonomyRecommendation(BaseModel):
    """Personalized recommendations based on taxonomy."""
    user_id: int = Field(..., description="User ID")
    recommendation_type: str = Field(..., description="Type of recommendation")
    item_id: int = Field(..., description="Recommended item ID")
    item_name: str = Field(..., description="Recommended item name")
    relevance_score: float = Field(0.0, ge=0.0, le=100.0, description="Relevance score")
    reasoning: str = Field(..., description="Reasoning for recommendation")
    estimated_impact: str = Field(..., description="Estimated impact on career")
    time_investment: Optional[str] = Field(None, description="Required time investment")
    cost_estimate: Optional[float] = Field(None, description="Estimated cost")
    prerequisites: List[str] = Field(default_factory=list, description="Prerequisites")
    next_steps: List[str] = Field(default_factory=list, description="Recommended next steps")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Generation timestamp")
