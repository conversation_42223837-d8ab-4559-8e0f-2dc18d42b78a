"""Marketplace schemas for API request/response models.

This module defines Pydantic schemas for the Agent 5 Marketplace & Integration Hub,
including vendor management, course marketplace, partnerships, and international operations.
"""

from pydantic import BaseModel, Field, validator, EmailStr
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from decimal import Decimal
from enum import Enum

from models.marketplace import VendorStatus, PartnershipTier, CourseStatus, CommissionStatus


# Enums for API schemas
class VendorStatusEnum(str, Enum):
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"


class PartnershipTierEnum(str, Enum):
    TIER_1 = "tier_1"
    TIER_2 = "tier_2"
    TIER_3 = "tier_3"


class CourseStatusEnum(str, Enum):
    DRAFT = "draft"
    PENDING_REVIEW = "pending_review"
    APPROVED = "approved"
    PUBLISHED = "published"
    SUSPENDED = "suspended"
    ARCHIVED = "archived"


class CommissionStatusEnum(str, Enum):
    PENDING = "pending"
    CALCULATED = "calculated"
    APPROVED = "approved"
    PAID = "paid"
    DISPUTED = "disputed"


# Base schemas
class BaseMarketplaceSchema(BaseModel):
    """Base schema for marketplace models."""
    
    class Config:
        from_attributes = True
        use_enum_values = True


# Vendor schemas
class VendorContactInfo(BaseModel):
    """Vendor contact information schema."""
    contact_email: EmailStr = Field(..., description="Primary contact email")
    contact_phone: Optional[str] = Field(None, description="Contact phone number")
    website_url: Optional[str] = Field(None, description="Vendor website URL")


class VendorBusinessInfo(BaseModel):
    """Vendor business information schema."""
    business_name: Optional[str] = Field(None, description="Legal business name")
    tax_id: Optional[str] = Field(None, description="Tax identification number")
    business_address: Optional[Dict[str, Any]] = Field(None, description="Business address")


class MarketplaceVendorCreate(BaseMarketplaceSchema):
    """Schema for creating a marketplace vendor."""
    vendor_name: str = Field(..., min_length=2, max_length=200, description="Vendor display name")
    vendor_slug: str = Field(..., min_length=2, max_length=100, description="URL-friendly vendor identifier")
    contact_email: EmailStr = Field(..., description="Primary contact email")
    contact_phone: Optional[str] = Field(None, description="Contact phone number")
    website_url: Optional[str] = Field(None, description="Vendor website URL")
    business_name: Optional[str] = Field(None, description="Legal business name")
    tax_id: Optional[str] = Field(None, description="Tax identification number")
    business_address: Optional[Dict[str, Any]] = Field(None, description="Business address")
    content_categories: List[str] = Field(default_factory=list, description="Content categories")
    specializations: List[str] = Field(default_factory=list, description="Vendor specializations")
    commission_rate: float = Field(0.25, ge=0.0, le=1.0, description="Commission rate (0-1)")
    payment_terms: str = Field("net_30", description="Payment terms")
    minimum_payout: Decimal = Field(Decimal('100.00'), ge=0, description="Minimum payout amount")
    vendor_config: Optional[Dict[str, Any]] = Field(None, description="Vendor configuration")
    
    @validator('vendor_slug')
    def validate_slug(cls, v):
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('Slug must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower()


class MarketplaceVendorUpdate(BaseMarketplaceSchema):
    """Schema for updating a marketplace vendor."""
    vendor_name: Optional[str] = Field(None, min_length=2, max_length=200)
    contact_email: Optional[EmailStr] = Field(None)
    contact_phone: Optional[str] = Field(None)
    website_url: Optional[str] = Field(None)
    business_name: Optional[str] = Field(None)
    tax_id: Optional[str] = Field(None)
    business_address: Optional[Dict[str, Any]] = Field(None)
    status: Optional[VendorStatusEnum] = Field(None)
    content_categories: Optional[List[str]] = Field(None)
    specializations: Optional[List[str]] = Field(None)
    commission_rate: Optional[float] = Field(None, ge=0.0, le=1.0)
    payment_terms: Optional[str] = Field(None)
    minimum_payout: Optional[Decimal] = Field(None, ge=0)
    vendor_config: Optional[Dict[str, Any]] = Field(None)


class MarketplaceVendorResponse(BaseMarketplaceSchema):
    """Schema for marketplace vendor response."""
    id: int
    vendor_name: str
    vendor_slug: str
    contact_email: str
    contact_phone: Optional[str]
    website_url: Optional[str]
    business_name: Optional[str]
    status: VendorStatusEnum
    verification_status: str
    verification_date: Optional[datetime]
    quality_score: float
    average_rating: Optional[float]
    total_reviews: int
    commission_rate: float
    payment_terms: str
    minimum_payout: Decimal
    content_categories: List[str]
    specializations: List[str]
    total_courses: int
    total_enrollments: int
    total_revenue: Decimal
    created_at: datetime
    updated_at: datetime


# Course schemas
class MarketplaceCourseCreate(BaseMarketplaceSchema):
    """Schema for creating a marketplace course."""
    vendor_id: int = Field(..., description="Vendor ID")
    course_title: str = Field(..., min_length=5, max_length=300, description="Course title")
    course_slug: str = Field(..., min_length=5, max_length=150, description="URL-friendly course identifier")
    course_description: Optional[str] = Field(None, description="Detailed course description")
    short_description: Optional[str] = Field(None, max_length=500, description="Short course description")
    course_level: str = Field(..., description="Course difficulty level")
    duration_hours: Optional[float] = Field(None, ge=0, description="Course duration in hours")
    language: str = Field("en", description="Course language code")
    price: Decimal = Field(..., ge=0, description="Course price")
    currency: str = Field("USD", description="Price currency")
    discount_price: Optional[Decimal] = Field(None, ge=0, description="Discounted price")
    course_content: Optional[Dict[str, Any]] = Field(None, description="Course content structure")
    learning_objectives: List[str] = Field(default_factory=list, description="Learning objectives")
    prerequisites: List[str] = Field(default_factory=list, description="Course prerequisites")
    target_certifications: List[str] = Field(default_factory=list, description="Target certifications")
    certification_domains: List[str] = Field(default_factory=list, description="Certification domains")
    tags: List[str] = Field(default_factory=list, description="Course tags")
    promotional_text: Optional[str] = Field(None, max_length=500, description="Promotional text")
    thumbnail_url: Optional[str] = Field(None, description="Course thumbnail URL")
    preview_video_url: Optional[str] = Field(None, description="Preview video URL")
    course_materials: List[Dict[str, Any]] = Field(default_factory=list, description="Course materials")
    
    @validator('course_slug')
    def validate_course_slug(cls, v):
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('Course slug must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower()
    
    @validator('course_level')
    def validate_course_level(cls, v):
        valid_levels = ['beginner', 'intermediate', 'advanced', 'expert']
        if v.lower() not in valid_levels:
            raise ValueError(f'Course level must be one of: {", ".join(valid_levels)}')
        return v.lower()


class MarketplaceCourseUpdate(BaseMarketplaceSchema):
    """Schema for updating a marketplace course."""
    course_title: Optional[str] = Field(None, min_length=5, max_length=300)
    course_description: Optional[str] = Field(None)
    short_description: Optional[str] = Field(None, max_length=500)
    course_level: Optional[str] = Field(None)
    duration_hours: Optional[float] = Field(None, ge=0)
    language: Optional[str] = Field(None)
    price: Optional[Decimal] = Field(None, ge=0)
    currency: Optional[str] = Field(None)
    discount_price: Optional[Decimal] = Field(None, ge=0)
    status: Optional[CourseStatusEnum] = Field(None)
    course_content: Optional[Dict[str, Any]] = Field(None)
    learning_objectives: Optional[List[str]] = Field(None)
    prerequisites: Optional[List[str]] = Field(None)
    target_certifications: Optional[List[str]] = Field(None)
    certification_domains: Optional[List[str]] = Field(None)
    tags: Optional[List[str]] = Field(None)
    featured: Optional[bool] = Field(None)
    promotional_text: Optional[str] = Field(None, max_length=500)
    thumbnail_url: Optional[str] = Field(None)
    preview_video_url: Optional[str] = Field(None)
    course_materials: Optional[List[Dict[str, Any]]] = Field(None)


class MarketplaceCourseResponse(BaseMarketplaceSchema):
    """Schema for marketplace course response."""
    id: int
    vendor_id: int
    course_title: str
    course_slug: str
    course_description: Optional[str]
    short_description: Optional[str]
    course_level: str
    duration_hours: Optional[float]
    language: str
    price: Decimal
    currency: str
    discount_price: Optional[Decimal]
    status: CourseStatusEnum
    approval_date: Optional[datetime]
    publication_date: Optional[datetime]
    learning_objectives: List[str]
    prerequisites: List[str]
    target_certifications: List[str]
    certification_domains: List[str]
    enrollment_count: int
    completion_rate: Optional[float]
    average_rating: Optional[float]
    review_count: int
    success_rate: Optional[float]
    tags: List[str]
    featured: bool
    promotional_text: Optional[str]
    thumbnail_url: Optional[str]
    preview_video_url: Optional[str]
    created_at: datetime
    updated_at: datetime


# Partnership schemas
class PartnershipAgreementCreate(BaseMarketplaceSchema):
    """Schema for creating a partnership agreement."""
    vendor_id: int = Field(..., description="Vendor ID")
    partner_name: str = Field(..., min_length=2, max_length=200, description="Partner name")
    partnership_tier: PartnershipTierEnum = Field(..., description="Partnership tier")
    partnership_type: str = Field(..., description="Partnership type")
    revenue_share: float = Field(..., ge=0.0, le=1.0, description="Revenue share percentage")
    minimum_annual_volume: Optional[Decimal] = Field(None, ge=0, description="Minimum annual volume")
    contract_duration_months: int = Field(..., ge=1, description="Contract duration in months")
    exclusive_benefits: List[str] = Field(default_factory=list, description="Exclusive benefits")
    co_branding_rights: bool = Field(False, description="Co-branding rights")
    early_access: bool = Field(False, description="Early access to features")
    joint_marketing: bool = Field(False, description="Joint marketing opportunities")
    agreement_terms: Optional[Dict[str, Any]] = Field(None, description="Agreement terms")


class PartnershipAgreementResponse(BaseMarketplaceSchema):
    """Schema for partnership agreement response."""
    id: int
    vendor_id: int
    partner_name: str
    partnership_tier: PartnershipTierEnum
    partnership_type: str
    revenue_share: float
    minimum_annual_volume: Optional[Decimal]
    contract_duration_months: int
    exclusive_benefits: List[str]
    co_branding_rights: bool
    early_access: bool
    joint_marketing: bool
    status: str
    signed_date: Optional[datetime]
    effective_date: Optional[datetime]
    expiration_date: Optional[datetime]
    current_volume: Decimal
    total_commissions_paid: Decimal
    created_at: datetime
    updated_at: datetime


# Commission schemas
class CommissionRecordCreate(BaseMarketplaceSchema):
    """Schema for creating a commission record."""
    vendor_id: int = Field(..., description="Vendor ID")
    course_id: Optional[int] = Field(None, description="Course ID")
    transaction_id: str = Field(..., description="Transaction ID")
    transaction_type: str = Field(..., description="Transaction type")
    gross_amount: Decimal = Field(..., ge=0, description="Gross transaction amount")
    commission_rate: float = Field(..., ge=0.0, le=1.0, description="Commission rate")
    commission_amount: Decimal = Field(..., ge=0, description="Commission amount")
    currency: str = Field("USD", description="Currency code")
    base_commission: float = Field(..., ge=0.0, description="Base commission rate")
    quality_bonus: float = Field(0.0, ge=0.0, description="Quality bonus")
    volume_bonus: float = Field(0.0, ge=0.0, description="Volume bonus")
    success_bonus: float = Field(0.0, ge=0.0, description="Success bonus")
    period_start: datetime = Field(..., description="Commission period start")
    period_end: datetime = Field(..., description="Commission period end")
    customer_id: Optional[str] = Field(None, description="Customer ID")
    notes: Optional[str] = Field(None, description="Additional notes")


class CommissionRecordResponse(BaseMarketplaceSchema):
    """Schema for commission record response."""
    id: int
    vendor_id: int
    course_id: Optional[int]
    transaction_id: str
    transaction_type: str
    gross_amount: Decimal
    commission_rate: float
    commission_amount: Decimal
    currency: str
    base_commission: float
    quality_bonus: float
    volume_bonus: float
    success_bonus: float
    status: CommissionStatusEnum
    calculation_date: Optional[datetime]
    payment_date: Optional[datetime]
    payment_reference: Optional[str]
    period_start: datetime
    period_end: datetime
    customer_id: Optional[str]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime


# Enrollment schemas
class CourseEnrollmentCreate(BaseMarketplaceSchema):
    """Schema for creating a course enrollment."""
    course_id: int = Field(..., description="Course ID")
    user_id: str = Field(..., description="User ID")
    purchase_price: Decimal = Field(..., ge=0, description="Purchase price")
    currency: str = Field("USD", description="Currency code")
    payment_method: Optional[str] = Field(None, description="Payment method")
    transaction_id: Optional[str] = Field(None, description="Transaction ID")


class CourseEnrollmentResponse(BaseMarketplaceSchema):
    """Schema for course enrollment response."""
    id: int
    course_id: int
    user_id: str
    enrollment_date: datetime
    completion_date: Optional[datetime]
    progress_percentage: float
    purchase_price: Decimal
    currency: str
    payment_method: Optional[str]
    transaction_id: Optional[str]
    current_module: Optional[str]
    time_spent_minutes: int
    last_accessed: Optional[datetime]
    is_completed: bool
    completion_certificate_url: Optional[str]
    final_score: Optional[float]
    status: str
    created_at: datetime
    updated_at: datetime


# Review schemas
class CourseReviewCreate(BaseMarketplaceSchema):
    """Schema for creating a course review."""
    course_id: int = Field(..., description="Course ID")
    user_id: str = Field(..., description="User ID")
    rating: int = Field(..., ge=1, le=5, description="Rating (1-5 stars)")
    title: Optional[str] = Field(None, max_length=200, description="Review title")
    review_text: Optional[str] = Field(None, description="Review text")
    is_verified_purchase: bool = Field(False, description="Verified purchase")


class CourseReviewResponse(BaseMarketplaceSchema):
    """Schema for course review response."""
    id: int
    course_id: int
    user_id: str
    rating: int
    title: Optional[str]
    review_text: Optional[str]
    is_verified_purchase: bool
    is_featured: bool
    helpful_votes: int
    is_approved: bool
    moderation_notes: Optional[str]
    created_at: datetime
    updated_at: datetime


# International schemas
class CurrencyRateCreate(BaseMarketplaceSchema):
    """Schema for creating a currency rate."""
    from_currency: str = Field(..., min_length=3, max_length=3, description="From currency code")
    to_currency: str = Field(..., min_length=3, max_length=3, description="To currency code")
    rate: Decimal = Field(..., gt=0, description="Exchange rate")
    source: str = Field("manual", description="Rate source")


class CurrencyRateResponse(BaseMarketplaceSchema):
    """Schema for currency rate response."""
    id: int
    from_currency: str
    to_currency: str
    rate: Decimal
    rate_date: datetime
    source: str
    is_active: bool
    created_at: datetime
    updated_at: datetime


class InternationalMarketCreate(BaseMarketplaceSchema):
    """Schema for creating an international market."""
    country_code: str = Field(..., min_length=2, max_length=2, description="ISO 3166-1 alpha-2 country code")
    country_name: str = Field(..., min_length=2, max_length=100, description="Country name")
    region: Optional[str] = Field(None, description="Geographic region")
    default_currency: str = Field(..., min_length=3, max_length=3, description="Default currency code")
    supported_languages: List[str] = Field(default_factory=list, description="Supported language codes")
    tax_rate: float = Field(0.0, ge=0.0, le=1.0, description="Tax rate")
    date_format: str = Field("YYYY-MM-DD", description="Date format")
    number_format: str = Field("1,234.56", description="Number format")
    timezone: Optional[str] = Field(None, description="Timezone")
    legal_entity: Optional[str] = Field(None, description="Legal entity")
    tax_id: Optional[str] = Field(None, description="Tax ID")
    compliance_requirements: Optional[Dict[str, Any]] = Field(None, description="Compliance requirements")


class InternationalMarketResponse(BaseMarketplaceSchema):
    """Schema for international market response."""
    id: int
    country_code: str
    country_name: str
    region: Optional[str]
    default_currency: str
    supported_languages: List[str]
    tax_rate: float
    is_active: bool
    launch_date: Optional[datetime]
    date_format: str
    number_format: str
    timezone: Optional[str]
    legal_entity: Optional[str]
    tax_id: Optional[str]
    compliance_requirements: Optional[Dict[str, Any]]
    total_users: int
    total_revenue: Decimal
    created_at: datetime
    updated_at: datetime


# List response schemas
class MarketplaceVendorListResponse(BaseMarketplaceSchema):
    """Schema for marketplace vendor list response."""
    vendors: List[MarketplaceVendorResponse]
    total: int
    page: int
    per_page: int
    total_pages: int


class MarketplaceCourseListResponse(BaseMarketplaceSchema):
    """Schema for marketplace course list response."""
    courses: List[MarketplaceCourseResponse]
    total: int
    page: int
    per_page: int
    total_pages: int


# Analytics schemas
class VendorAnalytics(BaseMarketplaceSchema):
    """Schema for vendor analytics."""
    vendor_id: int
    total_courses: int
    total_enrollments: int
    total_revenue: Decimal
    average_rating: Optional[float]
    completion_rate: Optional[float]
    commission_earned: Decimal
    top_performing_courses: List[Dict[str, Any]]
    monthly_metrics: List[Dict[str, Any]]


class MarketplaceAnalytics(BaseMarketplaceSchema):
    """Schema for marketplace analytics."""
    total_vendors: int
    total_courses: int
    total_enrollments: int
    total_revenue: Decimal
    average_course_rating: Optional[float]
    top_vendors: List[Dict[str, Any]]
    top_courses: List[Dict[str, Any]]
    revenue_by_category: List[Dict[str, Any]]
    international_breakdown: List[Dict[str, Any]]


# Search and filter schemas
class MarketplaceSearchRequest(BaseMarketplaceSchema):
    """Schema for marketplace search request."""
    query: Optional[str] = Field(None, description="Search query")
    categories: Optional[List[str]] = Field(None, description="Filter by categories")
    level: Optional[str] = Field(None, description="Filter by course level")
    price_min: Optional[Decimal] = Field(None, ge=0, description="Minimum price")
    price_max: Optional[Decimal] = Field(None, ge=0, description="Maximum price")
    rating_min: Optional[float] = Field(None, ge=1, le=5, description="Minimum rating")
    language: Optional[str] = Field(None, description="Filter by language")
    vendor_id: Optional[int] = Field(None, description="Filter by vendor")
    featured_only: Optional[bool] = Field(None, description="Featured courses only")
    sort_by: Optional[str] = Field("relevance", description="Sort by field")
    sort_order: Optional[str] = Field("desc", description="Sort order")
    page: int = Field(1, ge=1, description="Page number")
    per_page: int = Field(20, ge=1, le=100, description="Items per page")


# Currency conversion schemas
class CurrencyConversionRequest(BaseMarketplaceSchema):
    """Schema for currency conversion request."""
    amount: Decimal = Field(..., gt=0, description="Amount to convert")
    from_currency: str = Field(..., min_length=3, max_length=3, description="From currency code")
    to_currency: str = Field(..., min_length=3, max_length=3, description="To currency code")


class CurrencyConversionResponse(BaseMarketplaceSchema):
    """Schema for currency conversion response."""
    original_amount: Decimal
    converted_amount: Decimal
    from_currency: str
    to_currency: str
    exchange_rate: Decimal
    conversion_date: datetime
