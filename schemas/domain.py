"""Pydantic models for security domain API"""
from typing import List
from datetime import datetime
from pydantic import BaseModel

class DomainBase(BaseModel):
    """Base security domain fields"""
    name: str
    description: str | None = None

class DomainResponse(DomainBase):
    """Full domain response including database fields"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class DomainList(BaseModel):
    """List of domains response"""
    domains: List[DomainResponse]
