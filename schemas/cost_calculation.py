"""Pydantic schemas for cost calculation API endpoints.

This module defines the request and response schemas for cost calculation management,
including validation rules and serialization formats.
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class CurrencyCode(str, Enum):
    """Enumeration of supported currency codes."""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    CAD = "CAD"
    AUD = "AUD"
    JPY = "JPY"
    CHF = "CHF"
    CNY = "CNY"
    INR = "INR"
    NZD = "NZD"


class ScenarioType(str, Enum):
    """Enumeration of cost scenario types."""
    SELF_STUDY = "self_study"
    BOOTCAMP = "bootcamp"
    UNIVERSITY = "university"
    CORPORATE = "corporate"
    HYBRID = "hybrid"


class CostSource(str, Enum):
    """Enumeration of cost data sources."""
    MANUAL = "manual"
    API = "api"
    CALCULATED = "calculated"
    VENDOR = "vendor"


# Currency Rate Schemas
class CurrencyRateCreate(BaseModel):
    """Schema for creating a new currency rate."""
    base_currency: CurrencyCode = Field(CurrencyCode.USD, description="Base currency code")
    target_currency: CurrencyCode = Field(..., description="Target currency code")
    rate: float = Field(..., gt=0, description="Exchange rate")
    source: CostSource = Field(CostSource.MANUAL, description="Source of the rate")
    valid_from: Optional[datetime] = Field(None, description="Rate validity start date")
    valid_until: Optional[datetime] = Field(None, description="Rate validity end date")

    @field_validator('target_currency')
    @classmethod
    def validate_different_currencies(cls, v: CurrencyCode, info) -> CurrencyCode:
        """Validate that base and target currencies are different."""
        if hasattr(info, 'data') and 'base_currency' in info.data and v == info.data['base_currency']:
            raise ValueError('Base and target currencies must be different')
        return v

    @field_validator('valid_until')
    @classmethod
    def validate_date_range(cls, v: Optional[datetime], info) -> Optional[datetime]:
        """Validate that valid_until is after valid_from."""
        if v and hasattr(info, 'data') and 'valid_from' in info.data and info.data['valid_from'] and v <= info.data['valid_from']:
            raise ValueError('valid_until must be after valid_from')
        return v

    model_config = {
        "json_schema_extra": {
            "example": {
                "base_currency": "USD",
                "target_currency": "EUR",
                "rate": 0.92,
                "source": "api",
                "valid_from": "2024-01-15T00:00:00Z"
            }
        }
    }


class CurrencyRateResponse(BaseModel):
    """Schema for currency rate response."""
    id: int
    base_currency: str
    target_currency: str
    rate: float
    source: str
    is_active: bool
    valid_from: Optional[datetime]
    valid_until: Optional[datetime]
    is_valid: bool
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


# Cost Scenario Schemas
class CostScenarioCreate(BaseModel):
    """Schema for creating a new cost scenario."""
    name: str = Field(..., min_length=1, max_length=100, description="Scenario name")
    description: Optional[str] = Field(None, max_length=1000, description="Scenario description")
    scenario_type: ScenarioType = Field(..., description="Type of scenario")
    materials_multiplier: float = Field(1.0, ge=0, le=5, description="Materials cost multiplier")
    training_multiplier: float = Field(1.0, ge=0, le=10, description="Training cost multiplier")
    retake_probability: float = Field(0.2, ge=0, le=1, description="Probability of needing retake")
    includes_training: bool = Field(False, description="Whether scenario includes training")
    includes_mentoring: bool = Field(False, description="Whether scenario includes mentoring")
    includes_practice_exams: bool = Field(True, description="Whether scenario includes practice exams")
    study_time_multiplier: float = Field(1.0, ge=0.1, le=5, description="Study time multiplier")
    preparation_weeks: Optional[int] = Field(None, ge=1, le=104, description="Preparation time in weeks")

    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "Self-Study Path",
                "description": "Independent study with minimal external resources",
                "scenario_type": "self_study",
                "materials_multiplier": 1.0,
                "training_multiplier": 0.0,
                "retake_probability": 0.25,
                "includes_training": False,
                "includes_mentoring": False,
                "includes_practice_exams": True,
                "study_time_multiplier": 1.2,
                "preparation_weeks": 12
            }
        }
    }


class CostScenarioUpdate(BaseModel):
    """Schema for updating a cost scenario."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)
    materials_multiplier: Optional[float] = Field(None, ge=0, le=5)
    training_multiplier: Optional[float] = Field(None, ge=0, le=10)
    retake_probability: Optional[float] = Field(None, ge=0, le=1)
    includes_training: Optional[bool] = None
    includes_mentoring: Optional[bool] = None
    includes_practice_exams: Optional[bool] = None
    study_time_multiplier: Optional[float] = Field(None, ge=0.1, le=5)
    preparation_weeks: Optional[int] = Field(None, ge=1, le=104)
    is_active: Optional[bool] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "Updated Scenario Name",
                "retake_probability": 0.15,
                "preparation_weeks": 16
            }
        }
    }


class CostScenarioResponse(BaseModel):
    """Schema for cost scenario response."""
    id: int
    name: str
    description: Optional[str]
    scenario_type: str
    materials_multiplier: float
    training_multiplier: float
    retake_probability: float
    includes_training: bool
    includes_mentoring: bool
    includes_practice_exams: bool
    study_time_multiplier: float
    preparation_weeks: Optional[int]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


# Cost Calculation Schemas
class CostCalculationCreate(BaseModel):
    """Schema for creating a new cost calculation."""
    name: str = Field(..., min_length=1, max_length=200, description="Calculation name")
    description: Optional[str] = Field(None, max_length=1000, description="Calculation description")
    certification_ids: List[int] = Field(..., min_items=1, description="List of certification IDs")
    base_currency: CurrencyCode = Field(CurrencyCode.USD, description="Base currency for calculation")
    target_currency: CurrencyCode = Field(CurrencyCode.USD, description="Target currency for display")
    scenario_id: Optional[int] = Field(None, description="Cost scenario to apply")
    materials_cost: float = Field(0.0, ge=0, description="Additional materials cost")
    additional_costs: float = Field(0.0, ge=0, description="Other additional costs")
    is_saved: bool = Field(False, description="Whether to save this calculation")

    @field_validator('certification_ids')
    @classmethod
    def validate_certification_ids(cls, v: List[int]) -> List[int]:
        """Validate certification IDs are unique."""
        if len(v) != len(set(v)):
            raise ValueError('Certification IDs must be unique')
        return v

    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "Security+ and CISSP Path",
                "description": "Cost calculation for entry to advanced security path",
                "certification_ids": [1, 2],
                "base_currency": "USD",
                "target_currency": "EUR",
                "scenario_id": 1,
                "materials_cost": 500.0,
                "additional_costs": 200.0,
                "is_saved": True
            }
        }
    }


class CostCalculationUpdate(BaseModel):
    """Schema for updating a cost calculation."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    target_currency: Optional[CurrencyCode] = None
    scenario_id: Optional[int] = None
    materials_cost: Optional[float] = Field(None, ge=0)
    additional_costs: Optional[float] = Field(None, ge=0)
    is_saved: Optional[bool] = None
    is_shared: Optional[bool] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "Updated Calculation Name",
                "target_currency": "GBP",
                "materials_cost": 600.0
            }
        }
    }


class CostBreakdown(BaseModel):
    """Schema for cost breakdown details."""
    exam_fees: float
    materials: float
    training: float
    retakes: float
    additional: float

    model_config = {"from_attributes": True}


class CostTotals(BaseModel):
    """Schema for cost totals."""
    base_currency: float
    target_currency: float
    exchange_rate: float

    model_config = {"from_attributes": True}


class TimeEstimates(BaseModel):
    """Schema for time estimates."""
    study_hours: Optional[int]
    weeks: Optional[int]

    model_config = {"from_attributes": True}


class CostCalculationResponse(BaseModel):
    """Schema for cost calculation response."""
    id: int
    user_id: str
    name: str
    description: Optional[str]
    base_currency: str
    target_currency: str
    scenario_id: Optional[int]
    certification_ids: List[int]
    certification_count: int
    cost_breakdown: CostBreakdown
    totals: CostTotals
    time_estimates: TimeEstimates
    average_cost_per_certification: float
    calculation_date: datetime
    is_saved: bool
    is_shared: bool
    scenario: Optional[CostScenarioResponse]
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


# Bulk Operations Schemas
class BulkCostCalculationRequest(BaseModel):
    """Schema for bulk cost calculation request."""
    calculations: List[CostCalculationCreate] = Field(..., min_items=1, max_items=10)
    apply_bulk_discount: bool = Field(False, description="Apply bulk discount if applicable")
    bulk_discount_percentage: float = Field(0.0, ge=0, le=50, description="Bulk discount percentage")

    model_config = {
        "json_schema_extra": {
            "example": {
                "calculations": [
                    {
                        "name": "Security Path 1",
                        "certification_ids": [1, 2],
                        "materials_cost": 500.0
                    },
                    {
                        "name": "Security Path 2",
                        "certification_ids": [3, 4],
                        "materials_cost": 600.0
                    }
                ],
                "apply_bulk_discount": True,
                "bulk_discount_percentage": 10.0
            }
        }
    }


# Comparison Schemas
class CostComparisonRequest(BaseModel):
    """Schema for comparing multiple cost calculations."""
    calculation_ids: List[int] = Field(..., min_items=2, max_items=5, description="Calculation IDs to compare")
    comparison_currency: CurrencyCode = Field(CurrencyCode.USD, description="Currency for comparison")
    include_time_analysis: bool = Field(True, description="Include time-based analysis")

    model_config = {
        "json_schema_extra": {
            "example": {
                "calculation_ids": [1, 2, 3],
                "comparison_currency": "USD",
                "include_time_analysis": True
            }
        }
    }


class CostComparisonResponse(BaseModel):
    """Schema for cost comparison response."""
    calculations: List[CostCalculationResponse]
    comparison_currency: str
    summary: Dict[str, Any]
    recommendations: List[str]

    model_config = {
        "json_schema_extra": {
            "example": {
                "calculations": [],
                "comparison_currency": "USD",
                "summary": {
                    "lowest_cost": 1500.0,
                    "highest_cost": 3500.0,
                    "average_cost": 2500.0,
                    "cost_range": 2000.0
                },
                "recommendations": [
                    "Consider the self-study path for cost savings",
                    "Factor in retake probability for budget planning"
                ]
            }
        }
    }


# List Response Schemas
class CostCalculationListResponse(BaseModel):
    """Schema for paginated cost calculation list response."""
    calculations: List[CostCalculationResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int

    model_config = {"from_attributes": True}


class CostScenarioListResponse(BaseModel):
    """Schema for paginated cost scenario list response."""
    scenarios: List[CostScenarioResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int

    model_config = {"from_attributes": True}
