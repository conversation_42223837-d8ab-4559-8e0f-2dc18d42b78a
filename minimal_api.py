#!/usr/bin/env python3
"""
Minimal API server for certifications - just what we need to get the frontend working
"""
import os
import sys
import uvicorn
from pathlib import Path
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, Depends, HTTPException, Query, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, func, text
from pydantic import BaseModel
import logging

# Force SQLite database
os.environ['DATABASE_URL'] = 'sqlite:///./certpathfinder.db'

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import get_db, init_db
from models.certification import Certification, Organization

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="CertRats API",
    description="Certification management API",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://app.certrats.localhost", "http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Response models
class CertificationResponse(BaseModel):
    id: int
    name: str
    description: str
    level: str
    difficulty: str
    focus: str
    domain: str
    category: str
    organization: Dict[str, Any]
    cost: Optional[float]
    currency: str
    exam_code: Optional[str]
    validity_period: Optional[int]
    is_active: bool
    is_deleted: bool
    created_at: str
    updated_at: str

class CertificationList(BaseModel):
    items: List[CertificationResponse]
    total: int
    page: int
    limit: int
    pages: int

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    logger.info("Starting minimal CertRats API server")
    try:
        init_db()
        # Test database connection
        db = next(get_db())
        try:
            result = db.execute(text("SELECT COUNT(*) FROM certifications")).scalar()
            logger.info(f"Database connected successfully. Found {result} certifications.")
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "CertRats Minimal API",
        "version": "1.0.0",
        "documentation_url": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        db = next(get_db())
        try:
            db.execute(text("SELECT 1"))
            return {"status": "healthy", "database": "connected"}
        finally:
            db.close()
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Database error: {str(e)}")

@app.get("/api/v1/certifications", response_model=CertificationList)
async def list_certifications(
    search: Optional[str] = Query(None, description="Search in name and description"),
    organizations: Optional[str] = Query(None, description="Filter by organization names (comma-separated)"),
    difficulties: Optional[str] = Query(None, description="Filter by difficulties (comma-separated)"),
    levels: Optional[str] = Query(None, description="Filter by levels (comma-separated)"),
    domains: Optional[str] = Query(None, description="Filter by domains (comma-separated)"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
) -> CertificationList:
    """Get all certifications with filtering"""
    try:
        logger.info(f"Retrieving certifications - page: {page}, size: {size}")
        
        # Build query
        query = db.query(Certification).filter(Certification.is_active == True)
        
        # Apply search filter
        if search:
            search_filter = or_(
                Certification.name.ilike(f"%{search}%"),
                Certification.description.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Apply organization filter
        if organizations:
            org_list = [org.strip() for org in organizations.split(',')]
            query = query.join(Organization).filter(
                Organization.name.in_(org_list)
            )
        
        # Apply difficulty filter
        if difficulties:
            diff_list = [diff.strip() for diff in difficulties.split(',')]
            query = query.filter(Certification.difficulty.in_(diff_list))
        
        # Apply level filter
        if levels:
            level_list = [level.strip() for level in levels.split(',')]
            query = query.filter(Certification.level.in_(level_list))
        
        # Apply domain filter
        if domains:
            domain_list = [domain.strip() for domain in domains.split(',')]
            query = query.filter(Certification.domain.in_(domain_list))
        
        # Get total count for pagination
        total_count = query.count()
        
        # Apply pagination
        offset = (page - 1) * size
        certifications = query.order_by(Certification.name).offset(offset).limit(size).all()
        
        # Convert to response format
        items = []
        for cert in certifications:
            org_data = {
                "id": cert.organization.id if cert.organization else None,
                "name": cert.organization.name if cert.organization else "Unknown",
                "website": cert.organization.website if cert.organization else "",
                "description": cert.organization.description if cert.organization else ""
            }
            
            item = CertificationResponse(
                id=cert.id,
                name=cert.name,
                description=cert.description or "",
                level=cert.level or "Associate",
                difficulty=str(cert.difficulty) if cert.difficulty else "2",
                focus=cert.focus or "Technical",
                domain=cert.domain or "Cybersecurity",
                category=cert.category or "General",
                organization=org_data,
                cost=cert.cost,
                currency=cert.currency or "USD",
                exam_code=cert.exam_code,
                validity_period=cert.validity_period,
                is_active=cert.is_active,
                is_deleted=cert.is_deleted,
                created_at=cert.created_at.isoformat() if cert.created_at else "",
                updated_at=cert.last_updated.isoformat() if cert.last_updated else ""
            )
            items.append(item)
        
        total_pages = (total_count + size - 1) // size
        
        result = CertificationList(
            items=items,
            total=total_count,
            page=page,
            limit=size,
            pages=total_pages
        )
        
        logger.info(f"Returning {len(items)} certifications out of {total_count} total")
        return result
        
    except Exception as e:
        logger.error(f"Error retrieving certifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve certifications: {str(e)}"
        )

@app.get("/api/v1/certifications/{cert_id}")
async def get_certification_details(
    cert_id: int,
    db: Session = Depends(get_db)
):
    """Get detailed certification information"""
    try:
        certification = db.query(Certification).filter(
            Certification.id == cert_id,
            Certification.is_active == True
        ).first()
        
        if not certification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Certification not found"
            )
        
        org_data = {
            "id": certification.organization.id if certification.organization else None,
            "name": certification.organization.name if certification.organization else "Unknown",
            "website": certification.organization.website if certification.organization else "",
            "description": certification.organization.description if certification.organization else ""
        }
        
        return CertificationResponse(
            id=certification.id,
            name=certification.name,
            description=certification.description or "",
            level=certification.level or "Associate",
            difficulty=str(certification.difficulty) if certification.difficulty else "2",
            focus=certification.focus or "Technical",
            domain=certification.domain or "Cybersecurity",
            category=certification.category or "General",
            organization=org_data,
            cost=certification.cost,
            currency=certification.currency or "USD",
            exam_code=certification.exam_code,
            validity_period=certification.validity_period,
            is_active=certification.is_active,
            is_deleted=certification.is_deleted,
            created_at=certification.created_at.isoformat() if certification.created_at else "",
            updated_at=certification.last_updated.isoformat() if certification.last_updated else ""
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving certification details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve certification details"
        )

if __name__ == "__main__":
    print("🚀 Starting CertRats Minimal API Server")
    print("=======================================")
    print("Database: SQLite (certpathfinder.db)")
    print("API will be available at: http://localhost:8000")
    print("API docs will be available at: http://localhost:8000/docs")
    print("Certifications endpoint: http://localhost:8000/api/v1/certifications")
    print("")
    
    uvicorn.run(
        "minimal_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
