# CertRats Frontend Dockerfile
# Multi-stage build for Next.js application

# =============================================================================
# BASE STAGE - Common dependencies
# =============================================================================
FROM node:20-alpine AS base

# Install dependencies only when needed
RUN apk add --no-cache libc6-compat curl
WORKDIR /app

# Copy package files
COPY package*.json ./

# =============================================================================
# DEPENDENCIES STAGE - Install all dependencies
# =============================================================================
FROM base AS deps

# Install dependencies
RUN npm ci

# =============================================================================
# DEVELOPMENT STAGE - For development with hot reload
# =============================================================================
FROM base AS development

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Expose port
EXPOSE 3000

# Set environment
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start development server
CMD ["npm", "run", "dev"]

# =============================================================================
# BUILD STAGE - Build the application
# =============================================================================
FROM base AS builder

# Copy dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Set environment for build
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Build the application
RUN npm run build

# =============================================================================
# PRODUCTION STAGE - Development mode for now
# =============================================================================
FROM base AS production

# Copy dependencies first
COPY --from=deps /app/node_modules ./node_modules

# Copy source files
COPY package*.json ./
COPY . .

# Copy built application
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public

# Set environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000/ || exit 1

# Start the application
CMD ["npm", "start"]
