import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Zap,
  Shield,
  Database,
  Code,
  Users,
  BarChart3,
  ArrowRight,
  CheckCircle,
  Star,
  Award,
  Target,
  TrendingUp
} from 'lucide-react';

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <Zap className="h-6 w-6 text-blue-600" />
              <span className="font-bold text-xl">CertRats</span>
            </Link>
          </div>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link href="/certifications" className="transition-colors hover:text-foreground/80">
                Certifications
              </Link>
              <Link href="/dashboard" className="transition-colors hover:text-foreground/80">
                Dashboard
              </Link>
              <Link href="/docs" className="transition-colors hover:text-foreground/80">
                Docs
              </Link>
            </nav>
            <div className="flex items-center space-x-2">
              <Link href="/login">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link href="/register">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container flex flex-col items-center justify-center space-y-8 py-24 md:py-32">
        <div className="flex flex-col items-center space-y-4 text-center">
          <Badge variant="secondary" className="px-3 py-1">
            AI-Powered Certification Discovery
          </Badge>
          <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl">
            Your Cybersecurity
            <span className="gradient-text block text-blue-600">Certification Journey</span>
          </h1>
          <p className="max-w-[700px] text-lg text-muted-foreground sm:text-xl">
            Discover, plan, and track your cybersecurity certifications with AI-powered
            recommendations and personalized learning paths.
          </p>
        </div>
        <div className="flex flex-col gap-4 sm:flex-row">
          <Link href="/dashboard">
            <Button size="lg">
              Start Your Journey
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Link href="/certifications">
            <Button variant="outline" size="lg">
              Explore Certifications
            </Button>
          </Link>
        </div>
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <div className="flex items-center space-x-1">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span>500+ Certifications</span>
          </div>
          <div className="flex items-center space-x-1">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span>AI-Powered Recommendations</span>
          </div>
          <div className="flex items-center space-x-1">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span>Personalized Learning Paths</span>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container py-24">
        <div className="flex flex-col items-center space-y-4 text-center">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            Accelerate Your Cybersecurity Career
          </h2>
          <p className="max-w-[900px] text-lg text-muted-foreground">
            Discover the perfect certifications for your career goals with AI-powered
            recommendations and comprehensive learning path optimization.
          </p>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <Target className="h-8 w-8 text-blue-600" />
              <CardTitle>Smart Recommendations</CardTitle>
              <CardDescription>
                AI-powered certification recommendations based on your experience,
                career goals, and industry trends.
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader>
              <Award className="h-8 w-8 text-blue-600" />
              <CardTitle>500+ Certifications</CardTitle>
              <CardDescription>
                Comprehensive database of cybersecurity certifications from
                top providers like CompTIA, CISSP, AWS, and more.
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader>
              <TrendingUp className="h-8 w-8 text-blue-600" />
              <CardTitle>Learning Path Optimization</CardTitle>
              <CardDescription>
                Personalized learning paths that optimize your study time
                and maximize career advancement opportunities.
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader>
              <BarChart3 className="h-8 w-8 text-blue-600" />
              <CardTitle>Progress Tracking</CardTitle>
              <CardDescription>
                Track your certification progress, study hours, and
                achievements with detailed analytics and insights.
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader>
              <Shield className="h-8 w-8 text-blue-600" />
              <CardTitle>Industry Insights</CardTitle>
              <CardDescription>
                Stay updated with the latest cybersecurity trends,
                salary data, and job market insights.
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader>
              <Users className="h-8 w-8 text-blue-600" />
              <CardTitle>Community Support</CardTitle>
              <CardDescription>
                Connect with other cybersecurity professionals and
                share experiences in our supportive community.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-24">
        <Card className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-blue-800">
          <CardContent className="p-12 text-center">
            <div className="flex flex-col items-center space-y-4">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl text-white">
                Ready to Advance Your Cybersecurity Career?
              </h2>
              <p className="max-w-[600px] text-lg text-blue-100">
                Join thousands of cybersecurity professionals who trust CertRats
                to guide their certification journey and career advancement.
              </p>
              <div className="flex flex-col gap-4 sm:flex-row">
                <Link href="/register">
                  <Button size="lg" variant="secondary">
                    Start Free Today
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="/certifications">
                  <Button variant="outline" size="lg" className="text-white border-white hover:bg-white hover:text-blue-600">
                    Explore Certifications
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t py-12">
        <div className="container flex flex-col items-center justify-between gap-4 md:flex-row">
          <div className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-blue-600" />
            <span className="font-semibold">CertRats</span>
          </div>
          <p className="text-sm text-muted-foreground">
            © 2025 CertRats. Empowering cybersecurity professionals worldwide.
          </p>
          <div className="flex items-center space-x-4 text-sm">
            <Link href="/docs" className="hover:underline">
              Documentation
            </Link>
            <Link href="/privacy" className="hover:underline">
              Privacy Policy
            </Link>
            <Link href="/terms" className="hover:underline">
              Terms of Service
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
