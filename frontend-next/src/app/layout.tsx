import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/providers';
import { Toaster } from '@/components/ui/toaster';
import { Navigation } from '@/components/layout/navigation';
import './globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: {
    default: 'CertRats - Certification Path Finder',
    template: '%s | CertRats',
  },
  description: 'AI-powered certification discovery and learning path optimization platform for cybersecurity professionals',
  keywords: ['Cybersecurity', 'Certifications', 'Learning Paths', 'AI Assistant', 'Career Development', 'CISSP', 'CompTIA', 'AWS'],
  authors: [{ name: 'CertRats Team' }],
  creator: 'CertRats Team',
  publisher: 'CertRats',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    title: 'CertRats - Certification Path Finder',
    description: 'AI-powered certification discovery and learning path optimization platform for cybersecurity professionals',
    siteName: 'CertRats',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CertRats - Certification Path Finder',
    description: 'AI-powered certification discovery and learning path optimization platform for cybersecurity professionals',
    creator: '@certrats',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#3b82f6" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            <Navigation />
            <main className="flex-1">
              {children}
            </main>
          </div>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
