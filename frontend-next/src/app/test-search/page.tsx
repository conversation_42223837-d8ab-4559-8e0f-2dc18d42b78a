'use client';

import React, { useState, useCallback } from 'react';
import { Input } from '@/components/ui/input';

export default function TestSearchPage() {
  const [searchValue, setSearchValue] = useState('');

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
    console.log('Search value:', e.target.value);
  }, []);

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Search Input Test</h1>
      <div className="max-w-md">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Test Search Input
        </label>
        <Input
          key="test-search-input"
          type="text"
          placeholder="Type here to test focus..."
          value={searchValue}
          onChange={handleSearchChange}
        />
        <p className="mt-2 text-sm text-gray-600">
          Current value: {searchValue}
        </p>
      </div>
    </div>
  );
}
