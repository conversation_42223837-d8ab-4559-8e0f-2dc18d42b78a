'use client';

import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCertifications, getDifficultyBadgeVariant, getLevelBadgeVariant } from '@/hooks/useCertifications';

interface LocalFilters {
  search: string;
  organization: string;
  difficulty: string;
  level: string;
  domain: string;
}

export default function CertificationsPage() {
  const [localFilters, setLocalFilters] = useState<LocalFilters>({
    search: '',
    organization: '',
    difficulty: '',
    level: '',
    domain: ''
  });

  const {
    certifications,
    total,
    page,
    limit,
    pages,
    isLoading,
    filters,
    updateFilters,
    clearFilters,
    getFilterOptions,
    addToLearningPath
  } = useCertifications();

  const filterOptions = getFilterOptions();

  const handleLocalFilterChange = (key: keyof LocalFilters, value: string) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));

    // Update the actual filters used by the hook
    const newFilters: any = {};
    if (key === 'search') newFilters.search = value;
    if (key === 'organization') newFilters.organizations = value ? [value] : [];
    if (key === 'difficulty') newFilters.difficulties = value ? [value] : [];
    if (key === 'level') newFilters.levels = value ? [value] : [];
    if (key === 'domain') newFilters.domains = value ? [value] : [];

    updateFilters(newFilters);
  };

  const handleClearFilters = () => {
    setLocalFilters({
      search: '',
      organization: '',
      difficulty: '',
      level: '',
      domain: ''
    });
    clearFilters();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Certification Explorer</h1>
        <p className="mt-2 text-gray-600">
          Discover and explore certifications to advance your career
        </p>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search
              </label>
              <Input
                type="text"
                placeholder="Search certifications..."
                value={localFilters.search}
                onChange={(e) => handleLocalFilterChange('search', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Organization
              </label>
              <select
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={localFilters.organization}
                onChange={(e) => handleLocalFilterChange('organization', e.target.value)}
              >
                <option value="">All Organizations</option>
                {filterOptions.organizations.map(org => (
                  <option key={org} value={org}>{org}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Difficulty
              </label>
              <select
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={localFilters.difficulty}
                onChange={(e) => handleLocalFilterChange('difficulty', e.target.value)}
              >
                <option value="">All Difficulties</option>
                {filterOptions.difficulties.map(difficulty => (
                  <option key={difficulty} value={difficulty}>{difficulty}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Domain
              </label>
              <select
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={localFilters.domain}
                onChange={(e) => handleLocalFilterChange('domain', e.target.value)}
              >
                <option value="">All Domains</option>
                {filterOptions.domains.map(domain => (
                  <option key={domain} value={domain}>{domain}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="mt-4 flex justify-between items-center">
            <p className="text-sm text-gray-600">
              Showing {certifications.length} of {total} certifications
            </p>
            <Button variant="outline" onClick={handleClearFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Certifications Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {certifications.map((cert) => (
          <Card key={cert.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <CardTitle className="text-lg">{cert.name}</CardTitle>
                <div className="flex flex-col gap-1">
                  <Badge variant={getDifficultyBadgeVariant(cert.difficulty) as any}>
                    {cert.difficulty}
                  </Badge>
                  <Badge variant={getLevelBadgeVariant(cert.level) as any}>
                    {cert.level}
                  </Badge>
                </div>
              </div>
              <p className="text-sm text-gray-600">{cert.organization.name}</p>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-700 mb-4">{cert.description}</p>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Cost:</span>
                  <span className="font-medium">{cert.cost} {cert.currency}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Exam Code:</span>
                  <span className="font-medium">{cert.exam_code}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Valid for:</span>
                  <span className="font-medium">{cert.validity_period} months</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Focus:</span>
                  <span className="font-medium">{cert.focus}</span>
                </div>
              </div>

              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Domain & Category:</p>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary" className="text-xs">
                    {cert.domain}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {cert.category}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  className="w-full"
                  onClick={() => addToLearningPath(cert.id)}
                >
                  Add to Learning Path
                </Button>
                <Button variant="outline" className="w-full">
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {certifications.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No certifications found
          </h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your filters to see more results
          </p>
          <Button onClick={handleClearFilters}>Clear All Filters</Button>
        </div>
      )}
    </div>
  );
}
