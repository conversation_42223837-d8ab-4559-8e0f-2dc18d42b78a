/**
 * AI Study Assistant and Progress Tracking type definitions
 */

// AI Message Types
export interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  recommendations?: StudyRecommendation[];
  metadata?: Record<string, any>;
}

// Study Plan Types
export interface StudyPlan {
  id: string;
  title: string;
  description: string;
  certification_id: number;
  user_id: string;
  duration_weeks: number;
  hours_per_week: number;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  learning_style: 'visual' | 'auditory' | 'kinesthetic' | 'reading';
  phases: StudyPhase[];
  milestones: StudyMilestone[];
  created_at: string;
  updated_at: string;
}

export interface StudyPhase {
  id: string;
  title: string;
  description: string;
  duration_weeks: number;
  order: number;
  topics: string[];
  resources: StudyResource[];
  assessments: Assessment[];
}

export interface StudyMilestone {
  id: string;
  title: string;
  description: string;
  target_date: string;
  completed: boolean;
  completed_at?: string;
}

export interface StudyResource {
  id: string;
  title: string;
  type: 'book' | 'video' | 'article' | 'practice_test' | 'lab' | 'course';
  url?: string;
  description?: string;
  estimated_hours: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  cost?: number;
  currency?: string;
}

export interface Assessment {
  id: string;
  title: string;
  type: 'quiz' | 'practice_exam' | 'lab' | 'project';
  questions_count: number;
  time_limit_minutes: number;
  passing_score: number;
  description?: string;
}

// Study Recommendation Types
export interface StudyRecommendation {
  id: string;
  title: string;
  description: string;
  type: 'resource' | 'strategy' | 'schedule' | 'focus_area' | 'practice';
  priority: 'low' | 'medium' | 'high';
  category: 'study_material' | 'time_management' | 'exam_prep' | 'skill_building';
  certification_id?: number;
  user_id: string;
  confidence_score: number; // 0-1
  reasoning: string;
  resources?: StudyResource[];
  estimated_impact: 'low' | 'medium' | 'high';
  time_investment_hours: number;
  user_feedback?: AIFeedback;
  created_at: string;
  expires_at?: string;
}

// AI Feedback Types
export interface AIFeedback {
  id: string;
  recommendation_id: string;
  user_id: string;
  is_helpful: boolean;
  rating?: number; // 1-5
  comment?: string;
  follow_up_action?: 'implemented' | 'ignored' | 'modified';
  created_at: string;
}

// Progress Tracking Types
export interface ProgressStats {
  user_id: string;
  time_range: string;
  total_study_time: number; // minutes
  study_sessions_count: number;
  certifications_completed: number;
  certifications_in_progress: number;
  average_score: number;
  current_streak: number;
  longest_streak: number;
  achievements_earned: number;
  total_points: number;
  rank: number;
  percentile: number;
  domain_distribution: DomainDistribution[];
  weekly_progress: WeeklyProgress[];
  monthly_summary: MonthlyProgress[];
}

export interface DomainDistribution {
  domain: string;
  hours: number;
  percentage: number;
  certifications_count: number;
}

export interface WeeklyProgress {
  week_start: string;
  study_hours: number;
  sessions_count: number;
  average_session_duration: number;
  goals_met: number;
  goals_total: number;
}

export interface MonthlyProgress {
  month: string;
  study_hours: number;
  certifications_completed: number;
  achievements_earned: number;
  average_score: number;
  improvement_percentage: number;
}

// Study Session Types
export interface StudySession {
  id: string;
  user_id: string;
  certification_id?: number;
  topic?: string;
  start_time: string;
  end_time?: string;
  duration: number; // minutes
  session_type: 'reading' | 'video' | 'practice' | 'lab' | 'review' | 'exam_prep';
  focus_areas: string[];
  notes?: string;
  mood_before?: 'poor' | 'fair' | 'good' | 'excellent';
  mood_after?: 'poor' | 'fair' | 'good' | 'excellent';
  productivity_rating?: number; // 1-5
  difficulty_rating?: number; // 1-5
  goals_achieved: string[];
  challenges_faced: string[];
  next_session_plan?: string;
  resources_used: StudyResource[];
  created_at: string;
  updated_at: string;
}

// Certification Progress Types
export interface CertificationProgress {
  id: number;
  name: string;
  user_id: string;
  certification_id: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'expired' | 'failed';
  progress_percentage: number;
  start_date?: string;
  target_completion_date?: string;
  actual_completion_date?: string;
  exam_date?: string;
  exam_score?: number;
  passing_score: number;
  study_hours_logged: number;
  study_hours_estimated: number;
  current_phase?: string;
  completed_phases: string[];
  strengths: string[];
  weaknesses: string[];
  recommendations: StudyRecommendation[];
  study_plan_id?: string;
  created_at: string;
  updated_at: string;
}

// Goal Types
export interface StudyGoal {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  type: 'daily' | 'weekly' | 'monthly' | 'certification' | 'custom';
  target_value: number;
  current_value: number;
  unit: 'hours' | 'sessions' | 'points' | 'certifications' | 'percentage';
  target_date: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  certification_id?: number;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

// Learning Path Types
export interface LearningPath {
  id: string;
  title: string;
  description: string;
  category: 'beginner' | 'intermediate' | 'advanced' | 'specialized';
  domain: string;
  estimated_duration_weeks: number;
  estimated_hours: number;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  prerequisites: string[];
  certifications: number[]; // certification IDs in order
  skills_gained: string[];
  career_outcomes: string[];
  popularity_score: number;
  success_rate: number;
  created_at: string;
  updated_at: string;
}

// Analytics Types
export interface LearningAnalytics {
  user_id: string;
  learning_velocity: number; // hours per week
  retention_rate: number; // percentage
  engagement_score: number; // 0-100
  preferred_learning_times: TimeSlot[];
  optimal_session_duration: number; // minutes
  strength_areas: string[];
  improvement_areas: string[];
  learning_patterns: LearningPattern[];
  predictive_insights: PredictiveInsight[];
}

export interface TimeSlot {
  day_of_week: number; // 0-6
  hour: number; // 0-23
  engagement_score: number;
  session_count: number;
}

export interface LearningPattern {
  pattern_type: 'time_preference' | 'session_length' | 'topic_sequence' | 'difficulty_progression';
  description: string;
  confidence: number; // 0-1
  impact_score: number; // 0-100
  recommendations: string[];
}

export interface PredictiveInsight {
  insight_type: 'completion_prediction' | 'difficulty_forecast' | 'optimal_schedule' | 'risk_assessment';
  title: string;
  description: string;
  confidence: number; // 0-1
  time_horizon_days: number;
  actionable_recommendations: string[];
  created_at: string;
}
