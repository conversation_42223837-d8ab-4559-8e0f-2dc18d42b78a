/**
 * Type definitions barrel export
 */

// API types
export type {
  ApiResponse,
  ApiError,
  PaginationParams,
  PaginatedResponse,
  HealthCheckResponse,
  RequestConfig,
  ResponseConfig,
} from './api';

// Certification types
export type {
  Organization,
  SecurityDomain,
  CertificationLevel,
  CertificationDifficulty,
  CertificationFocus,
  Certification,
  CertificationFilters,
  CertificationStats,
  CertificationComparison,
  StudyProgress,
} from './certification';

// User types
export type {
  UserRole,
  ExperienceLevel,
  LearningStyle,
  User,
  UserProfileUpdate,
  UserPreferences,
  UserStats,
  Achievement,
  UserActivity,
} from './user';

// AI and Progress types
export type {
  AIMessage,
  StudyPlan,
  StudyPhase,
  StudyMilestone,
  StudyResource,
  Assessment,
  StudyRecommendation,
  AIFeedback,
  ProgressStats,
  DomainDistribution,
  WeeklyProgress,
  MonthlyProgress,
  StudySession,
  CertificationProgress,
  StudyGoal,
  LearningPath,
  LearningAnalytics,
  TimeSlot,
  LearningPattern,
  PredictiveInsight,
} from './ai-progress';

// Common utility types
export interface SelectOption<T = string> {
  value: T;
  label: string;
  disabled?: boolean;
}

export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  width?: string | number;
  render?: (value: any, record: T) => React.ReactNode;
}

export interface FilterOption {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'range' | 'search' | 'date';
  options?: SelectOption[];
  min?: number;
  max?: number;
  placeholder?: string;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'multiselect' | 'textarea' | 'checkbox' | 'radio' | 'date';
  required?: boolean;
  placeholder?: string;
  options?: SelectOption[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

// Navigation types
export interface NavItem {
  key: string;
  label: string;
  icon?: string;
  path: string;
  children?: NavItem[];
  badge?: string | number;
  disabled?: boolean;
}

// Theme types
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}
