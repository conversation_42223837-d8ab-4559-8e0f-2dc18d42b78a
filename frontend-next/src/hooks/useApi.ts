/**
 * Custom hook for API calls with loading, error, and data state management
 */
import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '../lib/api';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

export function useApi<T = any>(
  endpoint: string,
  options: UseApiOptions = {}
) {
  const { immediate = true, onSuccess, onError } = options;
  
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async (params?: Record<string, any>) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await apiClient.get(endpoint, { params });
      const data = response.data;
      
      setState({
        data,
        loading: false,
        error: null,
      });
      
      onSuccess?.(data);
      return data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'An error occurred';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });
      
      onError?.(errorMessage);
      throw error;
    }
  }, [endpoint, onSuccess, onError]);

  const post = useCallback(async (data: any) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await apiClient.post(endpoint, data);
      const responseData = response.data;
      
      setState({
        data: responseData,
        loading: false,
        error: null,
      });
      
      onSuccess?.(responseData);
      return responseData;
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'An error occurred';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });
      
      onError?.(errorMessage);
      throw error;
    }
  }, [endpoint, onSuccess, onError]);

  const put = useCallback(async (data: any) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await apiClient.put(endpoint, data);
      const responseData = response.data;
      
      setState({
        data: responseData,
        loading: false,
        error: null,
      });
      
      onSuccess?.(responseData);
      return responseData;
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'An error occurred';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });
      
      onError?.(errorMessage);
      throw error;
    }
  }, [endpoint, onSuccess, onError]);

  const remove = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await apiClient.delete(endpoint);
      const responseData = response.data;
      
      setState({
        data: responseData,
        loading: false,
        error: null,
      });
      
      onSuccess?.(responseData);
      return responseData;
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'An error occurred';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });
      
      onError?.(errorMessage);
      throw error;
    }
  }, [endpoint, onSuccess, onError]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return {
    ...state,
    execute,
    post,
    put,
    delete: remove,
    reset,
  };
}

export default useApi;
