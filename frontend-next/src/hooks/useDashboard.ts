/**
 * Custom hook for dashboard data management
 */
import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { certificationApi, userApi } from '@/lib/api';
import { useNotifications } from './use-notifications';
import type { Certification, User } from '@/types';

interface DashboardStats {
  total_certifications: number;
  in_progress: number;
  completed: number;
  total_paths: number;
  completion_percentage: number;
  completed_this_month: number;
  study_streak_days: number;
  next_exam_in_days: number;
  total_study_hours: number;
}

interface RecentActivity {
  id: number;
  certification_name: string;
  status: string;
  updated_at: string;
  type: 'certification' | 'study_session' | 'achievement';
}

interface LearningPath {
  id: number;
  name: string;
  description: string;
  progress: number;
  created_at: string;
  certifications: Certification[];
  estimated_completion_date: string;
}

interface DashboardData {
  user: User;
  statistics: DashboardStats;
  recent_activity: RecentActivity[];
  learning_paths: LearningPath[];
  recommended_certifications: Certification[];
}

export function useDashboard() {
  const { showError } = useNotifications();
  const queryClient = useQueryClient();

  // Fetch dashboard statistics
  const {
    data: stats,
    isLoading: statsLoading,
    error: statsError
  } = useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: async (): Promise<DashboardStats> => {
      try {
        const response = await userApi.getStats();
        return response;
      } catch (error) {
        // Return mock data if API fails
        return {
          total_certifications: 5,
          in_progress: 2,
          completed: 3,
          total_paths: 2,
          completion_percentage: 65,
          completed_this_month: 1,
          study_streak_days: 15,
          next_exam_in_days: 30,
          total_study_hours: 45
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });

  // Fetch user profile
  const {
    data: user,
    isLoading: userLoading,
    error: userError
  } = useQuery({
    queryKey: ['user', 'profile'],
    queryFn: async (): Promise<User> => {
      try {
        const response = await userApi.getProfile();
        return response;
      } catch (error) {
        // Return mock user if API fails
        return {
          id: '1',
          email: '<EMAIL>',
          name: 'Demo User',
          role: 'professional',
          years_experience: 5,
          experience_level: 'mid_level',
          expertise_areas: ['Security', 'Risk Management'],
          learning_style: 'mixed',
          study_time_available: 10,
          email_notifications: true,
          push_notifications: true,
          privacy_level: 'private',
          tutorial_completed: true,
          onboarding_step: 5,
          is_active: true,
          is_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 1
  });

  // Fetch recent activity
  const {
    data: recentActivity,
    isLoading: activityLoading
  } = useQuery({
    queryKey: ['dashboard', 'activity'],
    queryFn: async (): Promise<RecentActivity[]> => {
      try {
        // This would be a real API call
        // const response = await userApi.getRecentActivity();
        // return response;
        
        // Mock data for now
        return [
          {
            id: 1,
            certification_name: 'CISSP',
            status: 'in_progress',
            updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            type: 'certification'
          },
          {
            id: 2,
            certification_name: 'CompTIA Security+',
            status: 'completed',
            updated_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            type: 'certification'
          },
          {
            id: 3,
            certification_name: 'Study Session: Network Security',
            status: 'completed',
            updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            type: 'study_session'
          }
        ];
      } catch (error) {
        console.error('Failed to fetch recent activity:', error);
        return [];
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1
  });

  // Fetch learning paths
  const {
    data: learningPaths,
    isLoading: pathsLoading
  } = useQuery({
    queryKey: ['dashboard', 'learning-paths'],
    queryFn: async (): Promise<LearningPath[]> => {
      try {
        // This would be a real API call
        // const response = await userApi.getLearningPaths();
        // return response;
        
        // Mock data for now
        return [
          {
            id: 1,
            name: 'Cybersecurity Fundamentals',
            description: 'Build a strong foundation in cybersecurity principles and practices',
            progress: 75,
            created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            certifications: [],
            estimated_completion_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 2,
            name: 'Advanced Security Management',
            description: 'Master advanced security management concepts and leadership skills',
            progress: 45,
            created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            certifications: [],
            estimated_completion_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString()
          }
        ];
      } catch (error) {
        console.error('Failed to fetch learning paths:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });

  // Fetch recommended certifications
  const {
    data: recommendedCertifications,
    isLoading: recommendationsLoading
  } = useQuery({
    queryKey: ['dashboard', 'recommendations'],
    queryFn: async (): Promise<Certification[]> => {
      try {
        const response = await certificationApi.getAll({});
        return response.items?.slice(0, 6) || [];
      } catch (error) {
        // Mock data if API fails
        return [
          {
            id: 1,
            name: 'CISM',
            description: 'Certified Information Security Manager',
            level: 'Professional' as const,
            difficulty: 'Advanced' as const,
            focus: 'Management' as const,
            domain: 'Security Management',
            category: 'Information Security',
            organization: {
              id: 1,
              name: 'ISACA',
              website: 'https://www.isaca.org',
              description: 'Information Systems Audit and Control Association'
            },
            cost: 749,
            currency: 'USD',
            exam_code: 'CISM',
            validity_period: 36,
            is_active: true,
            is_deleted: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 2,
            name: 'CEH',
            description: 'Certified Ethical Hacker',
            level: 'Professional' as const,
            difficulty: 'Intermediate' as const,
            focus: 'Technical' as const,
            domain: 'Ethical Hacking',
            category: 'Penetration Testing',
            organization: {
              id: 2,
              name: 'EC-Council',
              website: 'https://www.eccouncil.org',
              description: 'International Council of Electronic Commerce Consultants'
            },
            cost: 1199,
            currency: 'USD',
            exam_code: 'CEH',
            validity_period: 36,
            is_active: true,
            is_deleted: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ];
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 1
  });

  // Refresh dashboard data
  const refreshDashboard = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    await queryClient.invalidateQueries({ queryKey: ['user'] });
  }, [queryClient]);

  // Add certification to learning path
  const addToLearningPath = useCallback(async (certificationId: number, pathId?: number) => {
    try {
      // This would be a real API call
      // await userApi.addCertificationToPath(certificationId, pathId);
      
      // Mock success for now
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Refresh data
      await refreshDashboard();
      
      return { success: true };
    } catch (error) {
      console.error('Failed to add certification to learning path:', error);
      showError('Error', 'Failed to add certification to learning path');
      return { success: false, error };
    }
  }, [refreshDashboard, showError]);

  const isLoading = statsLoading || userLoading || activityLoading || pathsLoading || recommendationsLoading;

  return {
    // Data
    user,
    stats,
    recentActivity: recentActivity || [],
    learningPaths: learningPaths || [],
    recommendedCertifications: recommendedCertifications || [],
    
    // Loading states
    isLoading,
    statsLoading,
    userLoading,
    activityLoading,
    pathsLoading,
    recommendationsLoading,
    
    // Actions
    refreshDashboard,
    addToLearningPath,
    
    // Errors
    error: statsError || userError
  };
}
