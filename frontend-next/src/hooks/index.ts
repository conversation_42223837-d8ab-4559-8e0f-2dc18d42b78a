/**
 * Custom hooks barrel export
 */
export { useApi } from './useApi';
export { useAuth, useAuthState, AuthContext } from './useAuth';
export { useLocalStorage } from './useLocalStorage';

// Re-export types (commented out to avoid conflicts)
// export type { default as useApi } from './useApi';
// export type { default as useAuth } from './useAuth';
// export type { default as useLocalStorage } from './useLocalStorage';
