/**
 * Custom hook for user profile management
 */
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useNotifications } from './use-notifications';
import { useAuth } from './useAuth';
import type { User } from '@/types';

interface ProfileUpdateData {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  bio?: string;
  website?: string;
}

interface PreferencesData {
  emailNotifications?: boolean;
  pushNotifications?: boolean;
  marketingEmails?: boolean;
  studyReminders?: boolean;
  weeklyReports?: boolean;
  publicProfile?: boolean;
  showProgress?: boolean;
  showAchievements?: boolean;
}

interface ExtendedUser extends User {
  phone?: string;
  avatar_url?: string;
  marketing_emails?: boolean;
  study_reminders?: boolean;
  weekly_reports?: boolean;
  public_profile?: boolean;
  show_progress?: boolean;
  show_achievements?: boolean;
}

export function useProfile() {
  const { showError, showSuccess } = useNotifications();
  const { user, updateUser } = useAuth();
  const queryClient = useQueryClient();

  // Fetch user profile
  const {
    data: profile,
    isLoading,
    error
  } = useQuery({
    queryKey: ['profile', user?.id],
    queryFn: async (): Promise<ExtendedUser> => {
      try {
        // This would be a real API call
        // const response = await userApi.getProfile();
        // return response;
        
        // Mock extended profile data - provide all required fields
        const mockProfile = {
          // Basic user info from AuthUser
          id: user!.id,
          email: user!.email,
          name: user!.name,
          role: user!.role as any, // Cast to avoid type issues
          avatar: user!.avatar,

          // Profile information with defaults
          title: 'Senior Security Engineer',
          company: 'TechCorp Inc.',
          location: 'San Francisco, CA',
          bio: 'Cybersecurity professional passionate about continuous learning and certification advancement.',
          website: 'https://johndoe.dev',
          linkedin_url: 'https://linkedin.com/in/johndoe',
          github_url: 'https://github.com/johndoe',

          // Experience and preferences with defaults
          years_experience: 5,
          experience_level: 'intermediate' as any,
          current_role: 'Security Engineer',
          desired_role: 'Security Architect',
          expertise_areas: ['cybersecurity', 'cloud', 'compliance'],
          learning_style: 'visual' as any,
          study_time_available: 10,

          // Settings with defaults
          email_notifications: true,
          push_notifications: true,
          privacy_level: 'private' as any,

          // Onboarding with defaults
          tutorial_completed: false,
          onboarding_step: 0,

          // Metadata with defaults
          is_active: true,
          is_verified: false,
          last_login: new Date().toISOString(),
          created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date().toISOString(),

          // Extended fields
          phone: '+****************',
          avatar_url: undefined,
          marketing_emails: false,
          study_reminders: true,
          weekly_reports: true,
          public_profile: false,
          show_progress: true,
          show_achievements: true
        } as ExtendedUser;
        return mockProfile;
      } catch (error) {
        console.error('Failed to fetch profile:', error);
        throw error;
      }
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileUpdateData): Promise<ExtendedUser> => {
      // This would be a real API call
      // const response = await userApi.updateProfile(data);
      // return response;
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedProfile = {
        ...profile!,
        ...data,
        updated_at: new Date().toISOString()
      };
      
      return updatedProfile;
    },
    onSuccess: (updatedProfile) => {
      queryClient.setQueryData(['profile', user?.id], updatedProfile);
      // Update auth user data
      updateUser({
        name: updatedProfile.name,
        email: updatedProfile.email
      });
      showSuccess('Success', 'Profile updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update profile:', error);
      showError('Error', 'Failed to update profile');
    }
  });

  // Update password mutation
  const updatePasswordMutation = useMutation({
    mutationFn: async ({ currentPassword, newPassword }: { currentPassword: string; newPassword: string }): Promise<void> => {
      // This would be a real API call
      // await userApi.updatePassword(currentPassword, newPassword);
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (currentPassword !== 'password123') {
        throw new Error('Current password is incorrect');
      }
    },
    onSuccess: () => {
      showSuccess('Success', 'Password updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update password:', error);
      showError('Error', error instanceof Error ? error.message : 'Failed to update password');
    }
  });

  // Update preferences mutation
  const updatePreferencesMutation = useMutation({
    mutationFn: async (data: PreferencesData): Promise<ExtendedUser> => {
      // This would be a real API call
      // const response = await userApi.updatePreferences(data);
      // return response;
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedProfile = {
        ...profile!,
        email_notifications: data.emailNotifications ?? profile!.email_notifications,
        push_notifications: data.pushNotifications ?? profile!.push_notifications,
        marketing_emails: data.marketingEmails,
        study_reminders: data.studyReminders,
        weekly_reports: data.weeklyReports,
        public_profile: data.publicProfile,
        show_progress: data.showProgress,
        show_achievements: data.showAchievements,
        updated_at: new Date().toISOString()
      };
      
      return updatedProfile;
    },
    onSuccess: (updatedProfile) => {
      queryClient.setQueryData(['profile', user?.id], updatedProfile);
      showSuccess('Success', 'Preferences updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update preferences:', error);
      showError('Error', 'Failed to update preferences');
    }
  });

  // Delete account mutation
  const deleteAccountMutation = useMutation({
    mutationFn: async (): Promise<void> => {
      // This would be a real API call
      // await userApi.deleteAccount();
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
    },
    onSuccess: () => {
      showSuccess('Account Deleted', 'Your account has been successfully deleted');
      // This would typically redirect to a goodbye page or logout
    },
    onError: (error) => {
      console.error('Failed to delete account:', error);
      showError('Error', 'Failed to delete account');
    }
  });

  // Upload avatar mutation
  const uploadAvatarMutation = useMutation({
    mutationFn: async (file: File): Promise<string> => {
      // This would be a real API call
      // const formData = new FormData();
      // formData.append('avatar', file);
      // const response = await userApi.uploadAvatar(formData);
      // return response.avatar_url;
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 2000));
      return URL.createObjectURL(file);
    },
    onSuccess: (avatarUrl) => {
      if (profile) {
        const updatedProfile = {
          ...profile,
          avatar_url: avatarUrl,
          updated_at: new Date().toISOString()
        };
        queryClient.setQueryData(['profile', user?.id], updatedProfile);
      }
      showSuccess('Success', 'Avatar updated successfully');
    },
    onError: (error) => {
      console.error('Failed to upload avatar:', error);
      showError('Error', 'Failed to upload avatar');
    }
  });

  // Refresh profile data
  const refreshProfile = async () => {
    await queryClient.invalidateQueries({ queryKey: ['profile', user?.id] });
  };

  return {
    // Data
    profile,
    
    // Loading states
    isLoading,
    isUpdatingProfile: updateProfileMutation.isPending,
    isUpdatingPassword: updatePasswordMutation.isPending,
    isUpdatingPreferences: updatePreferencesMutation.isPending,
    isDeletingAccount: deleteAccountMutation.isPending,
    isUploadingAvatar: uploadAvatarMutation.isPending,
    
    // Actions
    updateProfile: (data: ProfileUpdateData) => updateProfileMutation.mutateAsync(data),
    updatePassword: (currentPassword: string, newPassword: string) => 
      updatePasswordMutation.mutateAsync({ currentPassword, newPassword }),
    updatePreferences: (data: PreferencesData) => updatePreferencesMutation.mutateAsync(data),
    deleteAccount: () => deleteAccountMutation.mutateAsync(),
    uploadAvatar: (file: File) => uploadAvatarMutation.mutateAsync(file),
    refreshProfile,
    
    // Error
    error
  };
}
