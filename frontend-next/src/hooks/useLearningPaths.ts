/**
 * Custom hook for learning path management
 */
import { useState, useCallback } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useNotifications } from './use-notifications';
import type { Certification } from '@/types';

interface LearningPath {
  id: number;
  name: string;
  description: string;
  progress: number;
  status: 'not_started' | 'in_progress' | 'completed';
  created_at: string;
  updated_at: string;
  estimated_completion_date?: string;
  certifications?: Certification[];
  user_id: number;
}

interface CreateLearningPathData {
  name: string;
  description: string;
  certification_ids?: number[];
}

interface UpdateLearningPathData {
  name?: string;
  description?: string;
  status?: 'not_started' | 'in_progress' | 'completed';
  estimated_completion_date?: string;
}

export function useLearningPaths() {
  const { showError, showSuccess } = useNotifications();
  const queryClient = useQueryClient();

  // Fetch learning paths
  const {
    data: learningPaths,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['learning-paths'],
    queryFn: async (): Promise<LearningPath[]> => {
      try {
        // This would be a real API call
        // const response = await userApi.getLearningPaths();
        // return response;
        
        // Mock data for now
        return [
          {
            id: 1,
            name: 'Cybersecurity Fundamentals',
            description: 'Build a strong foundation in cybersecurity principles and practices',
            progress: 75,
            status: 'in_progress',
            created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            updated_at: new Date().toISOString(),
            estimated_completion_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            user_id: 1,
            certifications: []
          },
          {
            id: 2,
            name: 'Advanced Security Management',
            description: 'Master advanced security management concepts and leadership skills',
            progress: 45,
            status: 'in_progress',
            created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            updated_at: new Date().toISOString(),
            estimated_completion_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
            user_id: 1,
            certifications: []
          },
          {
            id: 3,
            name: 'Cloud Security Specialist',
            description: 'Comprehensive cloud security certification path',
            progress: 100,
            status: 'completed',
            created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            estimated_completion_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            user_id: 1,
            certifications: []
          }
        ];
      } catch (error) {
        console.error('Failed to fetch learning paths:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });

  // Create learning path mutation
  const createMutation = useMutation({
    mutationFn: async (data: CreateLearningPathData): Promise<LearningPath> => {
      // This would be a real API call
      // const response = await userApi.createLearningPath(data);
      // return response;
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newPath: LearningPath = {
        id: Date.now(),
        name: data.name,
        description: data.description,
        progress: 0,
        status: 'not_started',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 1,
        certifications: []
      };
      
      return newPath;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] });
      showSuccess('Success', 'Learning path created successfully');
    },
    onError: (error) => {
      console.error('Failed to create learning path:', error);
      showError('Error', 'Failed to create learning path');
    }
  });

  // Update learning path mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateLearningPathData }): Promise<LearningPath> => {
      // This would be a real API call
      // const response = await userApi.updateLearningPath(id, data);
      // return response;
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const existingPath = learningPaths?.find(p => p.id === id);
      if (!existingPath) {
        throw new Error('Learning path not found');
      }
      
      return {
        ...existingPath,
        ...data,
        updated_at: new Date().toISOString()
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] });
      showSuccess('Success', 'Learning path updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update learning path:', error);
      showError('Error', 'Failed to update learning path');
    }
  });

  // Delete learning path mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number): Promise<void> => {
      // This would be a real API call
      // await userApi.deleteLearningPath(id);
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] });
      showSuccess('Success', 'Learning path deleted successfully');
    },
    onError: (error) => {
      console.error('Failed to delete learning path:', error);
      showError('Error', 'Failed to delete learning path');
    }
  });

  // Add certification to path mutation
  const addCertificationMutation = useMutation({
    mutationFn: async ({ pathId, certificationId }: { pathId: number; certificationId: number }): Promise<void> => {
      // This would be a real API call
      // await userApi.addCertificationToPath(pathId, certificationId);
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] });
      showSuccess('Success', 'Certification added to learning path');
    },
    onError: (error) => {
      console.error('Failed to add certification to path:', error);
      showError('Error', 'Failed to add certification to learning path');
    }
  });

  // Remove certification from path mutation
  const removeCertificationMutation = useMutation({
    mutationFn: async ({ pathId, certificationId }: { pathId: number; certificationId: number }): Promise<void> => {
      // This would be a real API call
      // await userApi.removeCertificationFromPath(pathId, certificationId);
      
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] });
      showSuccess('Success', 'Certification removed from learning path');
    },
    onError: (error) => {
      console.error('Failed to remove certification from path:', error);
      showError('Error', 'Failed to remove certification from learning path');
    }
  });

  // Wrapper functions
  const createLearningPath = useCallback((data: CreateLearningPathData) => {
    return createMutation.mutateAsync(data);
  }, [createMutation]);

  const updateLearningPath = useCallback((id: number, data: UpdateLearningPathData) => {
    return updateMutation.mutateAsync({ id, data });
  }, [updateMutation]);

  const deleteLearningPath = useCallback((id: number) => {
    return deleteMutation.mutateAsync(id);
  }, [deleteMutation]);

  const addCertificationToPath = useCallback((pathId: number, certificationId: number) => {
    return addCertificationMutation.mutateAsync({ pathId, certificationId });
  }, [addCertificationMutation]);

  const removeCertificationFromPath = useCallback((pathId: number, certificationId: number) => {
    return removeCertificationMutation.mutateAsync({ pathId, certificationId });
  }, [removeCertificationMutation]);

  // Refresh learning paths
  const refreshLearningPaths = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['learning-paths'] });
  }, [queryClient]);

  return {
    // Data
    learningPaths: learningPaths || [],
    
    // Loading states
    isLoading,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    
    // Actions
    createLearningPath,
    updateLearningPath,
    deleteLearningPath,
    addCertificationToPath,
    removeCertificationFromPath,
    refreshLearningPaths,
    refetch,
    
    // Error
    error
  };
}
