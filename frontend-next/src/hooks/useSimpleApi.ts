import { useCallback } from 'react';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  status?: number;
}

export const useSimpleApi = () => {
  const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

  const getAuthHeaders = (): Record<string, string> => {
    const token = localStorage.getItem('access_token') || localStorage.getItem('auth_token');
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  };

  // Mock data for development
  const getMockData = useCallback((endpoint: string): any => {
    if (endpoint === '/dashboard/overview') {
      return {
        user: {
          id: 1,
          name: 'Demo User',
          email: '<EMAIL>'
        },
        statistics: {
          total_certifications: 3,
          in_progress: 2,
          total_paths: 2,
          completion_percentage: 65.5
        },
        recent_activity: [
          {
            id: 1,
            certification_name: 'AWS Solutions Architect',
            status: 'completed',
            updated_at: new Date().toISOString()
          },
          {
            id: 2,
            certification_name: 'Azure Fundamentals',
            status: 'in_progress',
            updated_at: new Date().toISOString()
          }
        ],
        learning_paths: [
          {
            id: 1,
            name: 'Cloud Architecture Path',
            description: 'Master cloud architecture with AWS and Azure',
            progress: 75,
            created_at: new Date().toISOString()
          },
          {
            id: 2,
            name: 'DevOps Engineer Path',
            description: 'Complete DevOps certification journey',
            progress: 45,
            created_at: new Date().toISOString()
          }
        ],
        recommended_certifications: [
          {
            id: 1,
            name: 'AWS Solutions Architect Professional',
            provider: 'AWS',
            difficulty: 'Advanced',
            estimated_hours: 80
          },
          {
            id: 2,
            name: 'Kubernetes Administrator',
            provider: 'CNCF',
            difficulty: 'Intermediate',
            estimated_hours: 60
          }
        ]
      };
    }

    if (endpoint === '/dashboard/quick-stats') {
      return {
        completed_this_month: 2,
        study_streak_days: 12,
        next_exam_in_days: 21,
        total_study_hours: 156
      };
    }

    return null;
  }, []);

  const get = useCallback(
    async <T>(endpoint: string): Promise<ApiResponse<T>> => {
      try {
        // For development, return mock data immediately
        if (process.env.NODE_ENV === 'development') {
          const mockData = getMockData(endpoint);
          if (mockData) {
            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 500));
            return {
              success: true,
              data: mockData,
              status: 200,
            };
          }
        }

        const response = await fetch(`${baseURL}${endpoint}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const data = await response.json();

        if (response.ok) {
          return {
            success: true,
            data,
            status: response.status,
          };
        } else {
          return {
            success: false,
            error: data.detail || data.message || 'An error occurred',
            status: response.status,
          };
        }
      } catch (error) {
        // Fallback to mock data in development
        if (process.env.NODE_ENV === 'development') {
          const mockData = getMockData(endpoint);
          if (mockData) {
            return {
              success: true,
              data: mockData,
              status: 200,
            };
          }
        }

        return {
          success: false,
          error: error instanceof Error ? error.message : 'Network error',
        };
      }
    },
    [baseURL, getMockData]
  );

  const post = useCallback(
    async <T>(endpoint: string, data?: any): Promise<ApiResponse<T>> => {
      try {
        const response = await fetch(`${baseURL}${endpoint}`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: data ? JSON.stringify(data) : undefined,
        });

        const responseData = await response.json();

        if (response.ok) {
          return {
            success: true,
            data: responseData,
            status: response.status,
          };
        } else {
          return {
            success: false,
            error: responseData.detail || responseData.message || 'An error occurred',
            status: response.status,
          };
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Network error',
        };
      }
    },
    [baseURL]
  );

  return {
    get,
    post,
  };
};
