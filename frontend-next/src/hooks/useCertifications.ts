/**
 * Custom hook for certification management
 */
import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { certificationApi } from '@/lib/api';
import { useNotifications } from './use-notifications';
import type { Certification, CertificationFilters } from '@/types';

// Comprehensive mock certification data generator
function generateMockCertifications(filters: CertificationSearchParams = {}): CertificationResponse {
  const allCertifications: Certification[] = [
    // AWS Certifications
    {
      id: 1,
      name: "AWS Certified Solutions Architect - Associate",
      description: "Validates technical expertise in designing distributed systems on AWS",
      level: 'Professional',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Cloud Computing",
      category: "Architecture",
      organization: { id: 1, name: "Amazon Web Services", website: "https://aws.amazon.com", description: "Amazon Web Services" },
      cost: 150, currency: "USD", exam_code: "SAA-C03", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 2,
      name: "AWS Certified Cloud Practitioner",
      description: "Foundational understanding of AWS Cloud services and concepts",
      level: 'Entry Level',
      difficulty: 'Beginner',
      focus: 'General',
      domain: "Cloud Computing",
      category: "Fundamentals",
      organization: { id: 1, name: "Amazon Web Services", website: "https://aws.amazon.com", description: "Amazon Web Services" },
      cost: 100, currency: "USD", exam_code: "CLF-C01", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 3,
      name: "AWS Certified Security - Specialty",
      description: "Validates expertise in securing AWS workloads and applications",
      level: 'Professional',
      difficulty: 'Advanced',
      focus: 'Technical',
      domain: "Cybersecurity",
      category: "Security",
      organization: { id: 1, name: "Amazon Web Services", website: "https://aws.amazon.com", description: "Amazon Web Services" },
      cost: 300, currency: "USD", exam_code: "SCS-C01", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // Microsoft Azure Certifications
    {
      id: 4,
      name: "Microsoft Azure Fundamentals",
      description: "Demonstrates foundational knowledge of cloud services and Microsoft Azure",
      level: 'Entry Level',
      difficulty: 'Beginner',
      focus: 'General',
      domain: "Cloud Computing",
      category: "Fundamentals",
      organization: { id: 2, name: "Microsoft", website: "https://microsoft.com", description: "Microsoft Corporation" },
      cost: 99, currency: "USD", exam_code: "AZ-900", validity_period: 24,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 5,
      name: "Microsoft Azure Security Engineer Associate",
      description: "Implements security controls and threat protection on Azure",
      level: 'Professional',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Cybersecurity",
      category: "Security",
      organization: { id: 2, name: "Microsoft", website: "https://microsoft.com", description: "Microsoft Corporation" },
      cost: 165, currency: "USD", exam_code: "AZ-500", validity_period: 24,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // CompTIA Certifications
    {
      id: 6,
      name: "CompTIA Security+",
      description: "Establishes core knowledge required of any cybersecurity role",
      level: 'Professional',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Cybersecurity",
      category: "Security",
      organization: { id: 3, name: "CompTIA", website: "https://comptia.org", description: "Computing Technology Industry Association" },
      cost: 370, currency: "USD", exam_code: "SY0-601", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 7,
      name: "CompTIA Network+",
      description: "Validates networking skills and knowledge for IT infrastructure",
      level: 'Associate',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Network Security",
      category: "Networking",
      organization: { id: 3, name: "CompTIA", website: "https://comptia.org", description: "Computing Technology Industry Association" },
      cost: 358, currency: "USD", exam_code: "N10-008", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 8,
      name: "CompTIA A+",
      description: "Entry-level certification for IT support professionals",
      level: 'Entry Level',
      difficulty: 'Beginner',
      focus: 'Technical',
      domain: "IT Support",
      category: "Hardware",
      organization: { id: 3, name: "CompTIA", website: "https://comptia.org", description: "Computing Technology Industry Association" },
      cost: 253, currency: "USD", exam_code: "220-1101", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // ISC2 Certifications
    {
      id: 9,
      name: "Certified Information Systems Security Professional (CISSP)",
      description: "Advanced cybersecurity certification for experienced professionals",
      level: 'Expert',
      difficulty: 'Advanced',
      focus: 'Management',
      domain: "Cybersecurity",
      category: "Management",
      organization: { id: 4, name: "ISC2", website: "https://isc2.org", description: "International Information System Security Certification Consortium" },
      cost: 749, currency: "USD", exam_code: "CISSP", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 10,
      name: "Systems Security Certified Practitioner (SSCP)",
      description: "Hands-on security skills for IT administrators and security professionals",
      level: 'Professional',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Cybersecurity",
      category: "Security",
      organization: { id: 4, name: "ISC2", website: "https://isc2.org", description: "International Information System Security Certification Consortium" },
      cost: 249, currency: "USD", exam_code: "SSCP", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // Google Cloud Certifications
    {
      id: 11,
      name: "Google Cloud Professional Cloud Architect",
      description: "Validates ability to design, develop, and manage robust, secure, scalable solutions",
      level: 'Professional',
      difficulty: 'Advanced',
      focus: 'Technical',
      domain: "Cloud Computing",
      category: "Architecture",
      organization: { id: 5, name: "Google Cloud", website: "https://cloud.google.com", description: "Google Cloud Platform" },
      cost: 200, currency: "USD", exam_code: "PCA", validity_period: 24,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 12,
      name: "Google Cloud Professional Cloud Security Engineer",
      description: "Demonstrates ability to configure access, configure network security, and manage operations",
      level: 'Professional',
      difficulty: 'Advanced',
      focus: 'Technical',
      domain: "Cybersecurity",
      category: "Security",
      organization: { id: 5, name: "Google Cloud", website: "https://cloud.google.com", description: "Google Cloud Platform" },
      cost: 200, currency: "USD", exam_code: "PCSE", validity_period: 24,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // Cisco Certifications
    {
      id: 13,
      name: "Cisco Certified Network Associate (CCNA)",
      description: "Foundation-level networking certification covering network fundamentals",
      level: 'Associate',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Network Security",
      category: "Networking",
      organization: { id: 6, name: "Cisco", website: "https://cisco.com", description: "Cisco Systems" },
      cost: 300, currency: "USD", exam_code: "200-301", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 14,
      name: "Cisco Certified CyberOps Associate",
      description: "Entry-level cybersecurity certification for security operations center analysts",
      level: 'Associate',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Cybersecurity",
      category: "Security Operations",
      organization: { id: 6, name: "Cisco", website: "https://cisco.com", description: "Cisco Systems" },
      cost: 300, currency: "USD", exam_code: "200-201", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // EC-Council Certifications
    {
      id: 15,
      name: "Certified Ethical Hacker (CEH)",
      description: "Validates skills in ethical hacking and penetration testing",
      level: 'Professional',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Offensive Security",
      category: "Penetration Testing",
      organization: { id: 7, name: "EC-Council", website: "https://eccouncil.org", description: "International Council of Electronic Commerce Consultants" },
      cost: 1199, currency: "USD", exam_code: "312-50", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 16,
      name: "Computer Hacking Forensic Investigator (CHFI)",
      description: "Validates skills in digital forensics and incident response",
      level: 'Professional',
      difficulty: 'Advanced',
      focus: 'Technical',
      domain: "Digital Forensics",
      category: "Incident Response",
      organization: { id: 7, name: "EC-Council", website: "https://eccouncil.org", description: "International Council of Electronic Commerce Consultants" },
      cost: 1199, currency: "USD", exam_code: "312-49", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // SANS/GIAC Certifications
    {
      id: 17,
      name: "GIAC Security Essentials (GSEC)",
      description: "Validates hands-on security skills and knowledge across multiple security domains",
      level: 'Professional',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Cybersecurity",
      category: "Security",
      organization: { id: 8, name: "SANS/GIAC", website: "https://giac.org", description: "Global Information Assurance Certification" },
      cost: 7000, currency: "USD", exam_code: "GSEC", validity_period: 48,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 18,
      name: "GIAC Certified Incident Handler (GCIH)",
      description: "Validates skills in detecting, responding to, and resolving computer security incidents",
      level: 'Professional',
      difficulty: 'Advanced',
      focus: 'Technical',
      domain: "Incident Response",
      category: "Incident Response",
      organization: { id: 8, name: "SANS/GIAC", website: "https://giac.org", description: "Global Information Assurance Certification" },
      cost: 7000, currency: "USD", exam_code: "GCIH", validity_period: 48,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // Offensive Security Certifications
    {
      id: 19,
      name: "Offensive Security Certified Professional (OSCP)",
      description: "Hands-on penetration testing certification requiring practical exploitation skills",
      level: 'Professional',
      difficulty: 'Advanced',
      focus: 'Technical',
      domain: "Offensive Security",
      category: "Penetration Testing",
      organization: { id: 9, name: "Offensive Security", website: "https://offensive-security.com", description: "Offensive Security" },
      cost: 1499, currency: "USD", exam_code: "OSCP", validity_period: undefined,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 20,
      name: "Offensive Security Certified Expert (OSCE)",
      description: "Advanced exploitation techniques and custom exploit development",
      level: 'Expert',
      difficulty: 'Expert',
      focus: 'Technical',
      domain: "Offensive Security",
      category: "Exploit Development",
      organization: { id: 9, name: "Offensive Security", website: "https://offensive-security.com", description: "Offensive Security" },
      cost: 1499, currency: "USD", exam_code: "OSCE", validity_period: undefined,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // ISACA Certifications
    {
      id: 21,
      name: "Certified Information Security Manager (CISM)",
      description: "Management-level certification for information security professionals",
      level: 'Expert',
      difficulty: 'Advanced',
      focus: 'Management',
      domain: "Security Management",
      category: "Management",
      organization: { id: 10, name: "ISACA", website: "https://isaca.org", description: "Information Systems Audit and Control Association" },
      cost: 760, currency: "USD", exam_code: "CISM", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    {
      id: 22,
      name: "Certified Information Systems Auditor (CISA)",
      description: "Globally recognized certification for information systems audit, control and assurance",
      level: 'Professional',
      difficulty: 'Advanced',
      focus: 'Management',
      domain: "Audit and Compliance",
      category: "Audit",
      organization: { id: 10, name: "ISACA", website: "https://isaca.org", description: "Information Systems Audit and Control Association" },
      cost: 760, currency: "USD", exam_code: "CISA", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // Cloud Security Alliance
    {
      id: 23,
      name: "Certificate of Cloud Security Knowledge (CCSK)",
      description: "Foundation-level understanding of cloud security concepts and practices",
      level: 'Associate',
      difficulty: 'Beginner',
      focus: 'General',
      domain: "Cloud Security",
      category: "Cloud Security",
      organization: { id: 11, name: "Cloud Security Alliance", website: "https://cloudsecurityalliance.org", description: "Cloud Security Alliance" },
      cost: 395, currency: "USD", exam_code: "CCSK", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // VMware Certifications
    {
      id: 24,
      name: "VMware Certified Professional - Data Center Virtualization",
      description: "Validates skills in installing, configuring, and administering vSphere environments",
      level: 'Professional',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "Virtualization",
      category: "Infrastructure",
      organization: { id: 12, name: "VMware", website: "https://vmware.com", description: "VMware Inc." },
      cost: 250, currency: "USD", exam_code: "2V0-21.20", validity_period: 24,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    },
    // Red Hat Certifications
    {
      id: 25,
      name: "Red Hat Certified System Administrator (RHCSA)",
      description: "Validates core system administration skills on Red Hat Enterprise Linux",
      level: 'Associate',
      difficulty: 'Intermediate',
      focus: 'Technical',
      domain: "System Administration",
      category: "Linux",
      organization: { id: 13, name: "Red Hat", website: "https://redhat.com", description: "Red Hat Inc." },
      cost: 400, currency: "USD", exam_code: "EX200", validity_period: 36,
      is_active: true, is_deleted: false, created_at: new Date().toISOString(), updated_at: new Date().toISOString()
    }
  ];

  // Apply filters
  let filteredCerts = allCertifications;

  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filteredCerts = filteredCerts.filter(cert =>
      cert.name.toLowerCase().includes(searchTerm) ||
      (cert.description && cert.description.toLowerCase().includes(searchTerm)) ||
      cert.organization.name.toLowerCase().includes(searchTerm)
    );
  }

  if (filters.organizations && filters.organizations.length > 0) {
    filteredCerts = filteredCerts.filter(cert =>
      filters.organizations!.includes(cert.organization.name)
    );
  }

  if (filters.difficulties && filters.difficulties.length > 0) {
    filteredCerts = filteredCerts.filter(cert =>
      filters.difficulties!.includes(cert.difficulty)
    );
  }

  if (filters.levels && filters.levels.length > 0) {
    filteredCerts = filteredCerts.filter(cert =>
      filters.levels!.includes(cert.level)
    );
  }

  if (filters.domains && filters.domains.length > 0) {
    filteredCerts = filteredCerts.filter(cert =>
      filters.domains!.includes(cert.domain)
    );
  }

  // Pagination
  const page = filters.page || 1;
  const limit = filters.size || 20;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedCerts = filteredCerts.slice(startIndex, endIndex);

  return {
    items: paginatedCerts,
    total: filteredCerts.length,
    page: page,
    limit: limit,
    pages: Math.ceil(filteredCerts.length / limit)
  };
}

interface CertificationSearchParams {
  search?: string;
  domains?: string[];
  levels?: string[];
  difficulties?: string[];
  organizations?: string[];
  cost_min?: number;
  cost_max?: number;
  page?: number;
  size?: number;
}

interface CertificationResponse {
  items: Certification[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export function useCertifications(initialFilters: CertificationSearchParams = {}) {
  const [filters, setFilters] = useState<CertificationSearchParams>(initialFilters);
  const { showError, showSuccess } = useNotifications();
  const queryClient = useQueryClient();

  // Fetch certifications with filters
  const {
    data: certificationsResponse,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['certifications', filters],
    queryFn: async (): Promise<CertificationResponse> => {
      try {
        const response = await certificationApi.getAll(filters as CertificationFilters);
        return response;
      } catch (error) {
        // Return comprehensive mock data if API fails
        return generateMockCertifications(filters);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });

  // Update filters with debouncing for search
  const updateFilters = useCallback((newFilters: Partial<CertificationSearchParams>) => {
    setFilters(prev => {
      const updated = { ...prev, ...newFilters, page: 1 };
      // Only update if filters actually changed
      if (JSON.stringify(updated) !== JSON.stringify(prev)) {
        return updated;
      }
      return prev;
    });
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Add certification to learning path
  const addToLearningPath = useCallback(async (certificationId: number, pathId?: number) => {
    try {
      // This would be a real API call
      // await userApi.addCertificationToPath(certificationId, pathId);
      
      // Mock success for now
      await new Promise(resolve => setTimeout(resolve, 500));
      
      showSuccess('Success', 'Certification added to learning path');
      
      return { success: true };
    } catch (error) {
      console.error('Failed to add certification to learning path:', error);
      showError('Error', 'Failed to add certification to learning path');
      return { success: false, error };
    }
  }, [showSuccess, showError]);

  // Get unique values for filter options (memoized)
  const getFilterOptions = useCallback(() => {
    const certifications = certificationsResponse?.items || [];

    return {
      organizations: Array.from(new Set(certifications.map(cert => cert.organization.name))),
      difficulties: Array.from(new Set(certifications.map(cert => cert.difficulty))),
      levels: Array.from(new Set(certifications.map(cert => cert.level))),
      domains: Array.from(new Set(certifications.map(cert => cert.domain))),
      categories: Array.from(new Set(certifications.map(cert => cert.category)))
    };
  }, [certificationsResponse]);

  // Memoize filter options to prevent recalculation on every render
  const filterOptions = useMemo(() => getFilterOptions(), [getFilterOptions]);

  // Refresh certifications
  const refreshCertifications = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['certifications'] });
  }, [queryClient]);

  return {
    // Data
    certifications: certificationsResponse?.items || [],
    total: certificationsResponse?.total || 0,
    page: certificationsResponse?.page || 1,
    limit: certificationsResponse?.limit || 20,
    pages: certificationsResponse?.pages || 1,

    // Loading and error states
    isLoading,
    error,

    // Filters
    filters,
    updateFilters,
    clearFilters,
    getFilterOptions,
    filterOptions, // Add memoized filter options

    // Actions
    addToLearningPath,
    refreshCertifications,
    refetch
  };
}

// Helper function for difficulty badge variants
export function getDifficultyBadgeVariant(difficulty: string) {
  switch (difficulty.toLowerCase()) {
    case 'beginner':
      return 'default';
    case 'intermediate':
      return 'secondary';
    case 'advanced':
      return 'destructive';
    case 'expert':
      return 'outline';
    default:
      return 'outline';
  }
}

// Helper function for level badge variants
export function getLevelBadgeVariant(level: string) {
  switch (level.toLowerCase()) {
    case 'entry level':
      return 'default';
    case 'associate':
      return 'secondary';
    case 'professional':
      return 'destructive';
    case 'expert':
      return 'outline';
    case 'specialist':
      return 'outline';
    default:
      return 'outline';
  }
}
