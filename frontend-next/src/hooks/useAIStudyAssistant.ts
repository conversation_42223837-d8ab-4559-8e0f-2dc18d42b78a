/**
 * Custom hook for AI Study Assistant functionality
 */
import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { StudyPlan, StudyRecommendation, AIMessage, AIFeedback } from '@/types';

interface SendMessageOptions {
  certification_id?: number;
  user_id?: string;
  context?: string;
}

interface StudyPlanOptions {
  study_hours_per_week: number;
  target_date?: string | null;
  learning_style: string;
  experience_level: string;
}

interface UseAIStudyAssistantReturn {
  // State
  loading: boolean;
  error: string | null;
  messages: AIMessage[];
  studyPlan: StudyPlan | null;
  recommendations: StudyRecommendation[];
  
  // Actions
  sendMessage: (message: string, options?: SendMessageOptions) => Promise<void>;
  getStudyPlan: (certificationId: number, userId: string, options: StudyPlanOptions) => Promise<void>;
  getRecommendations: (certificationId: number, userId: string) => Promise<void>;
  provideFeedback: (recommendationId: string, feedback: Partial<AIFeedback>) => Promise<void>;
  clearMessages: () => void;
  clearError: () => void;
}

export const useAIStudyAssistant = (): UseAIStudyAssistantReturn => {
  const { execute } = useApi('/ai-assistant', { immediate: false });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [studyPlan, setStudyPlan] = useState<StudyPlan | null>(null);
  const [recommendations, setRecommendations] = useState<StudyRecommendation[]>([]);

  const sendMessage = useCallback(async (message: string, options: SendMessageOptions = {}) => {
    if (!message.trim()) return;

    setLoading(true);
    setError(null);

    // Add user message to chat
    const userMessage: AIMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date().toISOString(),
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      const response = await execute({
        method: 'POST',
        url: '/api/v1/ai-assistant/chat',
        data: {
          message,
          ...options,
        },
      });

      if (response.data) {
        const aiMessage: AIMessage = {
          id: response.data.id || (Date.now() + 1).toString(),
          role: 'assistant',
          content: response.data.content,
          timestamp: response.data.timestamp || new Date().toISOString(),
          recommendations: response.data.recommendations,
        };
        setMessages(prev => [...prev, aiMessage]);

        // Update recommendations if provided
        if (response.data.recommendations) {
          setRecommendations(prev => [...prev, ...response.data.recommendations]);
        }
      }
    } catch (err: any) {
      setError(err.message || 'Failed to send message');
      console.error('AI chat error:', err);
    } finally {
      setLoading(false);
    }
  }, [execute]);

  const getStudyPlan = useCallback(async (
    certificationId: number,
    userId: string,
    options: StudyPlanOptions
  ) => {
    setLoading(true);
    setError(null);

    try {
      const response = await execute({
        method: 'POST',
        url: '/api/v1/ai-assistant/study-plan',
        data: {
          certification_id: certificationId,
          user_id: userId,
          ...options,
        },
      });

      if (response.data) {
        setStudyPlan(response.data);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to generate study plan');
      console.error('Study plan generation error:', err);
    } finally {
      setLoading(false);
    }
  }, [execute]);

  const getRecommendations = useCallback(async (certificationId: number, userId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await execute({
        method: 'GET',
        url: `/api/v1/ai-assistant/recommendations`,
        params: {
          certification_id: certificationId,
          user_id: userId,
        },
      });

      if (response.data) {
        setRecommendations(response.data);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to get recommendations');
      console.error('Recommendations error:', err);
    } finally {
      setLoading(false);
    }
  }, [execute]);

  const provideFeedback = useCallback(async (
    recommendationId: string,
    feedback: Partial<AIFeedback>
  ) => {
    try {
      await execute({
        method: 'POST',
        url: `/api/v1/ai-assistant/feedback`,
        data: {
          recommendation_id: recommendationId,
          ...feedback,
        },
      });

      // Update local recommendations with feedback
      setRecommendations(prev =>
        prev.map(rec =>
          rec.id === recommendationId
            ? { ...rec, user_feedback: feedback as any }
            : rec
        )
      );
    } catch (err: any) {
      console.error('Feedback error:', err);
      // Don't show error for feedback - it's not critical
    }
  }, [execute]);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    loading,
    error,
    messages,
    studyPlan,
    recommendations,
    
    // Actions
    sendMessage,
    getStudyPlan,
    getRecommendations,
    provideFeedback,
    clearMessages,
    clearError,
  };
};
