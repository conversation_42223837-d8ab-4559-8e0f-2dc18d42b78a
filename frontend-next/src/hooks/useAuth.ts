/**
 * Custom hook for authentication state management
 */
import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { useLocalStorage } from './useLocalStorage';
import { authApi, type LoginRequest, type RegistrationRequest } from '@/lib/api';

interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: string;
  avatar?: string;
}

interface AuthState {
  user: AuthUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; user?: any; error?: string }>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
  updateUser: (userData: Partial<AuthUser>) => void;
}

interface RegisterData {
  email: string;
  password: string;
  name: string;
}

// Create Auth Context
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Auth hook implementation
export function useAuthState(): AuthContextType {
  const [token, setToken, removeToken] = useLocalStorage<string | null>('auth_token', null);
  const [user, setUser, removeUser] = useLocalStorage<AuthUser | null>('auth_user', null);
  const [isLoading, setIsLoading] = useState(false);

  const isAuthenticated = Boolean(token && user);

  const login = useCallback(async (email: string, password: string, rememberMe: boolean = false) => {
    setIsLoading(true);
    try {
      // For demo purposes, simulate successful login with demo credentials
      if (email === '<EMAIL>' && password === 'password123') {
        const mockUser = {
          id: '1',
          email: '<EMAIL>',
          name: 'Demo User',
          role: 'user'
        };

        const mockToken = 'mock_access_token_' + Date.now();
        setToken(mockToken);
        setUser(mockUser);

        return { success: true, user: mockUser };
      }

      // Use real API for authentication
      const loginData: LoginRequest = {
        email,
        password,
        rememberMe
      };

      const response = await authApi.login(loginData);

      if (response.success && response.accessToken && response.user) {
        setToken(response.accessToken);
        setUser({
          id: response.user.id.toString(),
          email: response.user.email,
          name: response.user.name,
          role: response.user.role || 'user'
        });

        // Store refresh token if provided
        if (response.refreshToken) {
          localStorage.setItem('refresh_token', response.refreshToken);
        }

        return { success: true, user: response.user };
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [setToken, setUser]);

  const logout = useCallback(async (logoutAllDevices: boolean = false) => {
    try {
      // Call API logout if we have a token
      if (token) {
        await authApi.logout(logoutAllDevices);
      }
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with local logout even if API call fails
    } finally {
      // Always clear local storage
      removeToken();
      removeUser();
      localStorage.removeItem('refresh_token');
    }
  }, [token, removeToken, removeUser]);

  const register = useCallback(async (userData: RegisterData) => {
    setIsLoading(true);
    try {
      const registrationData: RegistrationRequest = {
        email: userData.email,
        password: userData.password,
        confirmPassword: userData.password,
        firstName: userData.name.split(' ')[0] || userData.name,
        lastName: userData.name.split(' ').slice(1).join(' ') || '',
        acceptTerms: true,
        marketingConsent: false
      };

      const response = await authApi.register(registrationData);

      if (response.success && response.accessToken && response.user) {
        setToken(response.accessToken);
        setUser({
          id: response.user.id.toString(),
          email: response.user.email,
          name: response.user.name,
          role: response.user.role || 'user'
        });

        // Store refresh token if provided
        if (response.refreshToken) {
          localStorage.setItem('refresh_token', response.refreshToken);
        }
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setToken, setUser]);

  const updateUser = useCallback((userData: Partial<AuthUser>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  }, [user, setUser]);

  // Validate token on mount
  useEffect(() => {
    if (token && !user) {
      // TODO: Validate token with API and get user data
      console.log('Validating token...');
    }
  }, [token, user]);

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    login,
    logout,
    register,
    updateUser,
  };
}

export default useAuth;
