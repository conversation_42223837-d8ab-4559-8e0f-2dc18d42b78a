{"name": "certrats-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.80.10", "@tanstack/react-query-devtools": "^5.80.10", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "framer-motion": "^11.18.2", "lucide-react": "^0.513.0", "next": "14.2.30", "next-themes": "^0.4.6", "react": "^18", "react-dom": "^18", "react-chartjs-2": "^5.3.0", "react-hook-form": "^7.49.2", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "web-vitals": "^2.1.4", "zod": "^3.25.67", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.41.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.11", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.3.0", "typescript": "^5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}