"""Playwright configuration for cost calculator E2E tests.

This configuration provides comprehensive E2E testing setup with
multiple browsers, devices, and test environments.
"""

from playwright.sync_api import Playwright
import os


# Test configuration
TEST_CONFIG = {
    'base_url': os.getenv('E2E_BASE_URL', 'http://localhost:3000'),
    'api_base_url': os.getenv('API_BASE_URL', 'http://localhost:8000'),
    'timeout': 30000,  # 30 seconds
    'expect_timeout': 10000,  # 10 seconds
    'navigation_timeout': 30000,  # 30 seconds
    'headless': os.getenv('HEADLESS', 'true').lower() == 'true',
    'slow_mo': int(os.getenv('SLOW_MO', '0')),  # Slow down operations
    'video': os.getenv('VIDEO', 'retain-on-failure'),
    'screenshot': os.getenv('SCREENSHOT', 'only-on-failure'),
    'trace': os.getenv('TRACE', 'retain-on-failure')
}

# Browser configurations
BROWSER_CONFIGS = [
    {
        'name': 'chromium',
        'use': {
            'viewport': {'width': 1280, 'height': 720},
            'ignore_https_errors': True,
            'video': TEST_CONFIG['video'],
            'screenshot': TEST_CONFIG['screenshot'],
            'trace': TEST_CONFIG['trace']
        }
    },
    {
        'name': 'firefox',
        'use': {
            'viewport': {'width': 1280, 'height': 720},
            'ignore_https_errors': True,
            'video': TEST_CONFIG['video'],
            'screenshot': TEST_CONFIG['screenshot'],
            'trace': TEST_CONFIG['trace']
        }
    },
    {
        'name': 'webkit',
        'use': {
            'viewport': {'width': 1280, 'height': 720},
            'ignore_https_errors': True,
            'video': TEST_CONFIG['video'],
            'screenshot': TEST_CONFIG['screenshot'],
            'trace': TEST_CONFIG['trace']
        }
    }
]

# Mobile device configurations
MOBILE_CONFIGS = [
    {
        'name': 'Mobile Chrome',
        'use': {
            'device_scale_factor': 2,
            'has_touch': True,
            'is_mobile': True,
            'viewport': {'width': 375, 'height': 667},
            'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
        }
    },
    {
        'name': 'Mobile Safari',
        'use': {
            'device_scale_factor': 2,
            'has_touch': True,
            'is_mobile': True,
            'viewport': {'width': 375, 'height': 667},
            'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
        }
    },
    {
        'name': 'Tablet',
        'use': {
            'device_scale_factor': 2,
            'has_touch': True,
            'is_mobile': False,
            'viewport': {'width': 768, 'height': 1024},
            'user_agent': 'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
        }
    }
]

# Test data setup
def setup_test_data():
    """Set up test data for E2E tests."""
    return {
        'test_user': {
            'id': 'test_user_1',
            'email': '<EMAIL>',
            'name': 'Test User'
        },
        'test_certifications': [
            {
                'name': 'CompTIA Security+',
                'cost': 349.0,
                'difficulty': 2,
                'domain': 'General Security'
            },
            {
                'name': 'CISSP',
                'cost': 749.0,
                'difficulty': 5,
                'domain': 'Security Management'
            },
            {
                'name': 'CEH',
                'cost': 1199.0,
                'difficulty': 3,
                'domain': 'Ethical Hacking'
            }
        ],
        'test_scenarios': [
            {
                'name': 'Self-Study',
                'type': 'self_study',
                'materials_multiplier': 1.0,
                'training_multiplier': 0.0,
                'retake_probability': 0.25
            },
            {
                'name': 'Bootcamp',
                'type': 'bootcamp',
                'materials_multiplier': 1.5,
                'training_multiplier': 3.0,
                'retake_probability': 0.10
            }
        ],
        'test_currency_rates': [
            {'base': 'USD', 'target': 'EUR', 'rate': 0.92},
            {'base': 'USD', 'target': 'GBP', 'rate': 0.79},
            {'base': 'USD', 'target': 'JPY', 'rate': 150.0}
        ]
    }

# Page object model base class
class BasePage:
    """Base page object for common functionality."""
    
    def __init__(self, page):
        self.page = page
        self.base_url = TEST_CONFIG['base_url']
    
    def navigate_to(self, path=''):
        """Navigate to a specific path."""
        url = f"{self.base_url}{path}"
        self.page.goto(url)
        self.page.wait_for_load_state('networkidle')
    
    def wait_for_element(self, selector, timeout=None):
        """Wait for element to be visible."""
        timeout = timeout or TEST_CONFIG['expect_timeout']
        return self.page.wait_for_selector(selector, timeout=timeout)
    
    def click_and_wait(self, selector, wait_for=None):
        """Click element and wait for navigation or specific element."""
        self.page.click(selector)
        if wait_for:
            self.wait_for_element(wait_for)
        else:
            self.page.wait_for_load_state('networkidle')
    
    def fill_and_validate(self, selector, value, validation_selector=None):
        """Fill input and optionally validate the value."""
        self.page.fill(selector, value)
        if validation_selector:
            self.page.wait_for_selector(validation_selector)
    
    def take_screenshot(self, name):
        """Take screenshot for debugging."""
        screenshot_path = f"test-results/screenshots/{name}.png"
        self.page.screenshot(path=screenshot_path)
        return screenshot_path

# Cost Calculator specific page objects
class CostCalculatorPage(BasePage):
    """Page object for cost calculator functionality."""
    
    def navigate_to_calculator(self):
        """Navigate to cost calculator page."""
        self.navigate_to('/cost-calculator')
        self.wait_for_element('[data-testid="cost-calculator-page"]')
    
    def create_new_calculation(self, calculation_data):
        """Create a new cost calculation."""
        self.click_and_wait('[data-testid="create-calculation-btn"]', 
                           '[data-testid="calculation-form"]')
        
        # Fill basic information
        self.fill_and_validate('[data-testid="calculation-name"]', 
                              calculation_data['name'])
        
        if 'description' in calculation_data:
            self.fill_and_validate('[data-testid="calculation-description"]', 
                                  calculation_data['description'])
        
        # Select certifications
        self.page.click('[data-testid="certification-selector"]')
        for cert_id in calculation_data['certification_ids']:
            self.page.click(f'[data-testid="certification-option-{cert_id}"]')
        self.page.click('[data-testid="certification-selector-close"]')
        
        # Select scenario if provided
        if 'scenario_id' in calculation_data:
            self.page.click('[data-testid="scenario-selector"]')
            self.page.click(f'[data-testid="scenario-option-{calculation_data["scenario_id"]}"]')
        
        # Set currency if provided
        if 'target_currency' in calculation_data:
            self.page.click('[data-testid="target-currency-selector"]')
            self.page.click(f'[data-testid="currency-option-{calculation_data["target_currency"].lower()}"]')
        
        # Fill cost fields
        if 'materials_cost' in calculation_data:
            self.fill_and_validate('[data-testid="materials-cost"]', 
                                  str(calculation_data['materials_cost']))
        
        if 'additional_costs' in calculation_data:
            self.fill_and_validate('[data-testid="additional-costs"]', 
                                  str(calculation_data['additional_costs']))
        
        # Save if requested
        if calculation_data.get('is_saved', False):
            self.page.check('[data-testid="save-calculation"]')
        
        # Submit form
        self.click_and_wait('[data-testid="create-calculation-submit"]', 
                           '[data-testid="calculation-created-success"]')
    
    def edit_calculation(self, calculation_id, updates):
        """Edit an existing calculation."""
        self.page.click(f'[data-testid="edit-calculation-{calculation_id}"]')
        self.wait_for_element('[data-testid="calculation-edit-form"]')
        
        for field, value in updates.items():
            if field == 'name':
                self.fill_and_validate('[data-testid="calculation-name"]', value)
            elif field == 'materials_cost':
                self.fill_and_validate('[data-testid="materials-cost"]', str(value))
            elif field == 'target_currency':
                self.page.click('[data-testid="target-currency-selector"]')
                self.page.click(f'[data-testid="currency-option-{value.lower()}"]')
        
        self.click_and_wait('[data-testid="save-calculation-changes"]', 
                           '[data-testid="calculation-updated-success"]')
    
    def delete_calculation(self, calculation_id):
        """Delete a calculation."""
        self.page.click(f'[data-testid="delete-calculation-{calculation_id}"]')
        self.wait_for_element('[data-testid="delete-confirmation-modal"]')
        self.click_and_wait('[data-testid="confirm-delete-btn"]', 
                           '[data-testid="calculation-deleted-success"]')
    
    def get_calculation_cost(self, calculation_id=None):
        """Get the total cost of a calculation."""
        selector = '[data-testid="total-cost"]'
        if calculation_id:
            selector = f'[data-testid="total-cost-{calculation_id}"]'
        
        element = self.wait_for_element(selector)
        return element.text_content()

class CostComparisonPage(BasePage):
    """Page object for cost comparison functionality."""
    
    def navigate_to_comparison(self):
        """Navigate to cost comparison page."""
        self.navigate_to('/cost-comparison')
        self.wait_for_element('[data-testid="comparison-page"]')
    
    def compare_calculations(self, calculation_ids, comparison_currency='USD'):
        """Compare multiple calculations."""
        # Select calculations
        for calc_id in calculation_ids:
            self.page.check(f'[data-testid="compare-checkbox-{calc_id}"]')
        
        # Set comparison currency
        self.page.click('[data-testid="comparison-currency-selector"]')
        self.page.click(f'[data-testid="currency-option-{comparison_currency.lower()}"]')
        
        # Start comparison
        self.click_and_wait('[data-testid="start-comparison-btn"]', 
                           '[data-testid="comparison-results"]')
    
    def get_comparison_results(self):
        """Get comparison results."""
        self.wait_for_element('[data-testid="comparison-table"]')
        
        results = {
            'calculations': [],
            'summary': {},
            'recommendations': []
        }
        
        # Get calculation rows
        rows = self.page.query_selector_all('[data-testid="comparison-row"]')
        for row in rows:
            name = row.query_selector('[data-testid="calc-name"]').text_content()
            cost = row.query_selector('[data-testid="calc-cost"]').text_content()
            results['calculations'].append({'name': name, 'cost': cost})
        
        # Get summary statistics
        summary_element = self.page.query_selector('[data-testid="comparison-summary"]')
        if summary_element:
            lowest = summary_element.query_selector('[data-testid="lowest-cost"]').text_content()
            highest = summary_element.query_selector('[data-testid="highest-cost"]').text_content()
            average = summary_element.query_selector('[data-testid="average-cost"]').text_content()
            
            results['summary'] = {
                'lowest_cost': lowest,
                'highest_cost': highest,
                'average_cost': average
            }
        
        # Get recommendations
        rec_elements = self.page.query_selector_all('[data-testid="recommendation-item"]')
        for rec in rec_elements:
            results['recommendations'].append(rec.text_content())
        
        return results

# Test utilities
class TestUtils:
    """Utility functions for E2E tests."""
    
    @staticmethod
    def setup_api_data(page, data_type, data):
        """Set up test data via API calls."""
        api_endpoints = {
            'certifications': '/api/v1/certifications',
            'scenarios': '/api/v1/cost-calculator/scenarios',
            'currency_rates': '/api/v1/cost-calculator/currency-rates'
        }
        
        endpoint = api_endpoints.get(data_type)
        if not endpoint:
            raise ValueError(f"Unknown data type: {data_type}")
        
        # Use page.evaluate to make API call from browser context
        result = page.evaluate(f"""
            fetch('{TEST_CONFIG["api_base_url"]}{endpoint}', {{
                method: 'POST',
                headers: {{ 'Content-Type': 'application/json' }},
                body: JSON.stringify({data})
            }}).then(response => response.json())
        """)
        
        return result
    
    @staticmethod
    def cleanup_test_data(page, data_type, data_id):
        """Clean up test data after tests."""
        api_endpoints = {
            'calculations': f'/api/v1/cost-calculator/calculations/{data_id}',
            'scenarios': f'/api/v1/cost-calculator/scenarios/{data_id}'
        }
        
        endpoint = api_endpoints.get(data_type)
        if endpoint:
            page.evaluate(f"""
                fetch('{TEST_CONFIG["api_base_url"]}{endpoint}', {{
                    method: 'DELETE'
                }})
            """)
    
    @staticmethod
    def wait_for_api_response(page, api_path, timeout=10000):
        """Wait for specific API response."""
        return page.wait_for_response(
            lambda response: api_path in response.url and response.status == 200,
            timeout=timeout
        )
    
    @staticmethod
    def mock_api_response(page, api_path, mock_data):
        """Mock API response for testing."""
        page.route(f"**/api/**{api_path}", 
                  lambda route: route.fulfill(json=mock_data))

# Export configuration for pytest-playwright
def pytest_configure(config):
    """Configure pytest for Playwright tests."""
    config.addinivalue_line(
        "markers", "e2e: mark test as end-to-end test"
    )
    config.addinivalue_line(
        "markers", "mobile: mark test as mobile-specific test"
    )
    config.addinivalue_line(
        "markers", "accessibility: mark test as accessibility test"
    )

# Pytest fixtures for page objects
import pytest

@pytest.fixture
def cost_calculator_page(page):
    """Provide CostCalculatorPage instance."""
    return CostCalculatorPage(page)

@pytest.fixture
def cost_comparison_page(page):
    """Provide CostComparisonPage instance."""
    return CostComparisonPage(page)

@pytest.fixture
def test_utils():
    """Provide TestUtils instance."""
    return TestUtils()

@pytest.fixture
def test_data():
    """Provide test data."""
    return setup_test_data()
