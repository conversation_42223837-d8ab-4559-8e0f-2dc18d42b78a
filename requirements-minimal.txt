# Minimal requirements for the API server
fastapi>=0.109.0
uvicorn[standard]>=0.27.0
python-multipart>=0.0.6
jinja2>=3.1.3

# Database
sqlalchemy==2.0.25
alembic==1.13.1

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-decouple>=3.8

# HTTP clients
requests>=2.31.0
httpx==0.26.0

# Data validation
pydantic==2.5.3
pydantic-settings==2.1.0

# Utilities
python-dateutil>=2.8.2
pytz>=2023.3

# Development and testing
pytest>=7.4.4
pytest-asyncio>=0.23.2

# Logging
structlog>=23.2.0

# Environment
python-dotenv>=1.0.0
