version: '3.8'

# CertRats Frontend-Only Development Setup
# 
# This compose file starts only the frontend service for demonstration
# and development purposes when the backend is not yet ready.

networks:
  traefik_network:
    external: true

services:
  # =============================================================================
  # FRONTEND SERVICE
  # =============================================================================
  
  frontend:
    build:
      context: ./frontend-next
      dockerfile: Dockerfile
      target: development
    container_name: certrats_frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://api.certrats.localhost
      - REACT_APP_DEBUG=true
      - FAST_REFRESH=true
      - CHOKIDAR_USEPOLLING=true
    command: npm run dev
    volumes:
      - ./frontend-next:/app
      - /app/node_modules
      - /app/.next
    networks:
      - traefik_network
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"
      
      # Router configuration
      - "traefik.http.routers.certrats-frontend.rule=Host(`app.certrats.localhost`)"
      - "traefik.http.routers.certrats-frontend.entrypoints=web"
      - "traefik.http.routers.certrats-frontend.service=certrats-frontend"
      - "traefik.http.services.certrats-frontend.loadbalancer.server.port=3000"
      
      # Network specification
      - "traefik.docker.network=traefik_network"
      
      # Development middleware
      - "traefik.http.routers.certrats-frontend.middlewares=cors"
      - "traefik.http.middlewares.cors.headers.accesscontrolalloworiginlist=*"
      - "traefik.http.middlewares.cors.headers.accesscontrolallowmethods=GET,POST,PUT,DELETE,OPTIONS"
      - "traefik.http.middlewares.cors.headers.accesscontrolallowheaders=*"
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    stdin_open: true
    tty: true
