#!/bin/bash

echo "Attempting to kill server processes..."

# Kill Streamlit process
if pgrep -f "streamlit run main.py" > /dev/null; then
    echo "Killing Streamlit server..."
    pkill -f "streamlit run main.py"
    sleep 1
    if ! pgrep -f "streamlit run main.py" > /dev/null; then
        echo "✓ Streamlit server stopped"
    else
        echo "! Failed to stop Streamlit server"
    fi
else
    echo "No Streamlit server running"
fi

# Kill FastAPI process
if pgrep -f "python run_api.py" > /dev/null; then
    echo "Killing FastAPI server..."
    pkill -f "python run_api.py"
    sleep 1
    if ! pgrep -f "python run_api.py" > /dev/null; then
        echo "✓ FastAPI server stopped"
    else
        echo "! Failed to stop FastAPI server"
    fi
else
    echo "No FastAPI server running"
fi

echo "Process cleanup completed"
