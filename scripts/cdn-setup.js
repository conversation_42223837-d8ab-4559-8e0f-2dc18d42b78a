#!/usr/bin/env node

/**
 * CDN Setup and Asset Optimization Script
 * Configures CDN for static assets and optimizes delivery
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class CDNSetup {
  constructor(options = {}) {
    this.cdnUrl = options.cdnUrl || process.env.CDN_URL || 'https://cdn.yourdomain.com';
    this.buildDir = options.buildDir || path.join(__dirname, '../frontend/build');
    this.staticDir = path.join(this.buildDir, 'static');
    this.manifestPath = path.join(this.buildDir, 'asset-manifest.json');
    this.config = {
      enableCDN: process.env.ENABLE_CDN === 'true',
      enableCompression: true,
      enableCaching: true,
      cacheMaxAge: 31536000, // 1 year
      ...options
    };
  }

  /**
   * Main setup function
   */
  async setup() {
    console.log('🚀 Starting CDN setup and asset optimization...');
    
    try {
      if (this.config.enableCDN) {
        await this.updateAssetUrls();
      }
      
      await this.generateAssetManifest();
      await this.createCacheHeaders();
      await this.optimizeAssets();
      await this.generatePreloadHints();
      
      console.log('✅ CDN setup completed successfully!');
      
    } catch (error) {
      console.error('❌ CDN setup failed:', error);
      process.exit(1);
    }
  }

  /**
   * Update asset URLs to use CDN
   */
  async updateAssetUrls() {
    console.log('📝 Updating asset URLs for CDN...');
    
    const indexPath = path.join(this.buildDir, 'index.html');
    if (!fs.existsSync(indexPath)) {
      throw new Error('index.html not found in build directory');
    }
    
    let indexContent = fs.readFileSync(indexPath, 'utf8');
    
    // Replace static asset URLs with CDN URLs
    indexContent = indexContent.replace(
      /href="\/static\//g,
      `href="${this.cdnUrl}/static/`
    );
    
    indexContent = indexContent.replace(
      /src="\/static\//g,
      `src="${this.cdnUrl}/static/`
    );
    
    // Update manifest.json URL
    indexContent = indexContent.replace(
      /href="\/manifest\.json"/g,
      `href="${this.cdnUrl}/manifest.json"`
    );
    
    fs.writeFileSync(indexPath, indexContent);
    console.log('✅ Asset URLs updated for CDN');
  }

  /**
   * Generate comprehensive asset manifest
   */
  async generateAssetManifest() {
    console.log('📋 Generating asset manifest...');
    
    const manifest = {
      version: process.env.npm_package_version || '1.0.0',
      buildDate: new Date().toISOString(),
      cdnUrl: this.cdnUrl,
      assets: {},
      integrity: {},
      preload: [],
      prefetch: []
    };
    
    // Scan static directory for assets
    if (fs.existsSync(this.staticDir)) {
      await this.scanDirectory(this.staticDir, manifest);
    }
    
    // Add main HTML file
    const indexPath = path.join(this.buildDir, 'index.html');
    if (fs.existsSync(indexPath)) {
      const indexStats = fs.statSync(indexPath);
      const indexContent = fs.readFileSync(indexPath);
      manifest.assets['index.html'] = {
        size: indexStats.size,
        hash: this.generateHash(indexContent),
        lastModified: indexStats.mtime.toISOString()
      };
    }
    
    // Write manifest
    fs.writeFileSync(
      this.manifestPath,
      JSON.stringify(manifest, null, 2)
    );
    
    console.log(`✅ Asset manifest generated with ${Object.keys(manifest.assets).length} assets`);
  }

  /**
   * Recursively scan directory for assets
   */
  async scanDirectory(dir, manifest, relativePath = '') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativeFilePath = path.join(relativePath, item).replace(/\\/g, '/');
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        await this.scanDirectory(fullPath, manifest, relativeFilePath);
      } else {
        const content = fs.readFileSync(fullPath);
        const hash = this.generateHash(content);
        
        manifest.assets[relativeFilePath] = {
          size: stats.size,
          hash: hash,
          lastModified: stats.mtime.toISOString(),
          contentType: this.getContentType(item)
        };
        
        // Generate integrity hash for critical assets
        if (this.isCriticalAsset(item)) {
          manifest.integrity[relativeFilePath] = `sha384-${crypto
            .createHash('sha384')
            .update(content)
            .digest('base64')}`;
        }
        
        // Determine preload/prefetch candidates
        if (this.shouldPreload(item)) {
          manifest.preload.push(relativeFilePath);
        } else if (this.shouldPrefetch(item)) {
          manifest.prefetch.push(relativeFilePath);
        }
      }
    }
  }

  /**
   * Create cache headers configuration
   */
  async createCacheHeaders() {
    console.log('🗂️ Creating cache headers configuration...');
    
    const cacheConfig = {
      // Long-term caching for versioned assets
      longTerm: {
        pattern: /\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp|avif)$/,
        maxAge: this.config.cacheMaxAge,
        headers: {
          'Cache-Control': `public, max-age=${this.config.cacheMaxAge}, immutable`,
          'Expires': new Date(Date.now() + this.config.cacheMaxAge * 1000).toUTCString()
        }
      },
      
      // Short-term caching for HTML
      shortTerm: {
        pattern: /\.html$/,
        maxAge: 300, // 5 minutes
        headers: {
          'Cache-Control': 'public, max-age=300, must-revalidate',
          'ETag': true
        }
      },
      
      // No cache for service worker
      noCache: {
        pattern: /sw\.js$/,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    };
    
    // Write cache configuration
    fs.writeFileSync(
      path.join(this.buildDir, 'cache-config.json'),
      JSON.stringify(cacheConfig, null, 2)
    );
    
    console.log('✅ Cache headers configuration created');
  }

  /**
   * Optimize assets for delivery
   */
  async optimizeAssets() {
    console.log('⚡ Optimizing assets for delivery...');
    
    const optimizations = {
      totalFiles: 0,
      compressedFiles: 0,
      totalSizeReduction: 0
    };
    
    // Check if gzip/brotli files exist (created by webpack)
    if (fs.existsSync(this.staticDir)) {
      await this.checkCompressionFiles(this.staticDir, optimizations);
    }
    
    console.log(`✅ Asset optimization complete:`);
    console.log(`   - Total files: ${optimizations.totalFiles}`);
    console.log(`   - Compressed files: ${optimizations.compressedFiles}`);
    console.log(`   - Size reduction: ${this.formatBytes(optimizations.totalSizeReduction)}`);
  }

  /**
   * Check for compressed files
   */
  async checkCompressionFiles(dir, optimizations, relativePath = '') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        await this.checkCompressionFiles(fullPath, optimizations, path.join(relativePath, item));
      } else {
        optimizations.totalFiles++;
        
        // Check for gzip version
        const gzipPath = fullPath + '.gz';
        if (fs.existsSync(gzipPath)) {
          const gzipStats = fs.statSync(gzipPath);
          optimizations.compressedFiles++;
          optimizations.totalSizeReduction += (stats.size - gzipStats.size);
        }
        
        // Check for brotli version
        const brotliPath = fullPath + '.br';
        if (fs.existsSync(brotliPath)) {
          const brotliStats = fs.statSync(brotliPath);
          if (!fs.existsSync(gzipPath)) {
            optimizations.compressedFiles++;
          }
          optimizations.totalSizeReduction += (stats.size - brotliStats.size);
        }
      }
    }
  }

  /**
   * Generate preload hints for critical resources
   */
  async generatePreloadHints() {
    console.log('🔗 Generating preload hints...');
    
    const manifest = JSON.parse(fs.readFileSync(this.manifestPath, 'utf8'));
    const preloadHints = [];
    
    // Generate preload hints
    for (const asset of manifest.preload) {
      const assetInfo = manifest.assets[asset];
      if (assetInfo) {
        const hint = {
          href: this.config.enableCDN ? `${this.cdnUrl}/${asset}` : `/${asset}`,
          rel: 'preload',
          as: this.getAssetType(asset),
          crossorigin: this.config.enableCDN ? 'anonymous' : undefined
        };
        
        if (manifest.integrity[asset]) {
          hint.integrity = manifest.integrity[asset];
        }
        
        preloadHints.push(hint);
      }
    }
    
    // Write preload hints
    fs.writeFileSync(
      path.join(this.buildDir, 'preload-hints.json'),
      JSON.stringify(preloadHints, null, 2)
    );
    
    console.log(`✅ Generated ${preloadHints.length} preload hints`);
  }

  /**
   * Utility functions
   */
  generateHash(content) {
    return crypto.createHash('md5').update(content).digest('hex');
  }

  getContentType(filename) {
    const ext = path.extname(filename).toLowerCase();
    const types = {
      '.js': 'application/javascript',
      '.css': 'text/css',
      '.html': 'text/html',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.webp': 'image/webp',
      '.avif': 'image/avif',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject'
    };
    return types[ext] || 'application/octet-stream';
  }

  isCriticalAsset(filename) {
    return /\.(js|css)$/.test(filename) && !filename.includes('chunk');
  }

  shouldPreload(filename) {
    return /\.(js|css)$/.test(filename) && !filename.includes('chunk');
  }

  shouldPrefetch(filename) {
    return filename.includes('chunk') || /\.(woff|woff2)$/.test(filename);
  }

  getAssetType(filename) {
    if (/\.css$/.test(filename)) return 'style';
    if (/\.js$/.test(filename)) return 'script';
    if (/\.(woff|woff2|ttf|eot)$/.test(filename)) return 'font';
    if (/\.(png|jpg|jpeg|gif|svg|webp|avif)$/.test(filename)) return 'image';
    return 'fetch';
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// CLI execution
if (require.main === module) {
  const setup = new CDNSetup({
    cdnUrl: process.argv[2] || process.env.CDN_URL,
    enableCDN: process.env.ENABLE_CDN === 'true'
  });
  
  setup.setup().catch(console.error);
}

module.exports = CDNSetup;
