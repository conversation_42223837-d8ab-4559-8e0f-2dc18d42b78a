#!/bin/bash
# 🚀 Branch Analysis Quick Start Script
# =====================================
# 
# Quick wrapper for the Branch Integration Manager
# Provides easy access to common branch analysis tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo -e "${BLUE}🚀 CertPathFinder Branch Integration Manager${NC}"
echo -e "${BLUE}=============================================${NC}"
echo

# Function to print usage
print_usage() {
    echo -e "${CYAN}Usage:${NC}"
    echo "  $0 [COMMAND] [OPTIONS]"
    echo
    echo -e "${CYAN}Commands:${NC}"
    echo "  analyze-all          Analyze all remote branches"
    echo "  analyze <branch>     Analyze specific branch"
    echo "  list-branches        List all remote branches"
    echo "  quick-status         Quick status of all branches"
    echo "  help                 Show this help message"
    echo
    echo -e "${CYAN}Examples:${NC}"
    echo "  $0 analyze-all"
    echo "  $0 analyze feature/skills-assessment-1.1"
    echo "  $0 list-branches"
    echo "  $0 quick-status"
}

# Function to list remote branches
list_branches() {
    echo -e "${YELLOW}📋 Remote Branches:${NC}"
    echo "==================="
    
    git branch -r --no-merged master | grep -v 'origin/HEAD' | while read branch; do
        branch_name=$(echo "$branch" | sed 's/origin\///')
        if [ "$branch_name" != "master" ]; then
            # Get commit count
            commit_count=$(git rev-list --count master..origin/$branch_name 2>/dev/null || echo "0")
            
            # Get last commit date
            last_commit=$(git log -1 --format="%cr" origin/$branch_name 2>/dev/null || echo "unknown")
            
            echo -e "  ${GREEN}$branch_name${NC} - ${PURPLE}$commit_count commits${NC} - ${CYAN}$last_commit${NC}"
        fi
    done
    echo
}

# Function to show quick status
quick_status() {
    echo -e "${YELLOW}⚡ Quick Branch Status:${NC}"
    echo "======================="
    
    total_branches=0
    active_branches=0
    stale_branches=0
    
    git branch -r --no-merged master | grep -v 'origin/HEAD' | while read branch; do
        branch_name=$(echo "$branch" | sed 's/origin\///')
        if [ "$branch_name" != "master" ]; then
            total_branches=$((total_branches + 1))
            
            # Check if branch has recent activity (last 30 days)
            last_commit_timestamp=$(git log -1 --format="%ct" origin/$branch_name 2>/dev/null || echo "0")
            current_timestamp=$(date +%s)
            days_since_commit=$(( (current_timestamp - last_commit_timestamp) / 86400 ))
            
            if [ $days_since_commit -lt 30 ]; then
                active_branches=$((active_branches + 1))
                echo -e "  ${GREEN}✅ $branch_name${NC} - Active (${days_since_commit} days ago)"
            else
                stale_branches=$((stale_branches + 1))
                echo -e "  ${RED}⚠️  $branch_name${NC} - Stale (${days_since_commit} days ago)"
            fi
        fi
    done
    
    echo
    echo -e "${CYAN}Summary:${NC}"
    echo "  Total branches: $total_branches"
    echo "  Active branches: $active_branches"
    echo "  Stale branches: $stale_branches"
    echo
}

# Function to run comprehensive analysis
analyze_all() {
    echo -e "${YELLOW}🔍 Running Comprehensive Branch Analysis...${NC}"
    echo "==========================================="
    echo
    
    # Check if Python script exists
    if [ ! -f "$SCRIPT_DIR/branch_integration_manager.py" ]; then
        echo -e "${RED}❌ Error: branch_integration_manager.py not found${NC}"
        exit 1
    fi
    
    # Run the Python analysis tool
    cd "$REPO_ROOT"
    python3 "$SCRIPT_DIR/branch_integration_manager.py" --analyze-all
    
    echo
    echo -e "${GREEN}✅ Analysis complete! Check the reports/ directory for detailed results.${NC}"
}

# Function to analyze specific branch
analyze_branch() {
    local branch_name="$1"
    
    if [ -z "$branch_name" ]; then
        echo -e "${RED}❌ Error: Branch name required${NC}"
        echo "Usage: $0 analyze <branch-name>"
        exit 1
    fi
    
    echo -e "${YELLOW}🔍 Analyzing Branch: $branch_name${NC}"
    echo "================================="
    echo
    
    # Check if branch exists
    if ! git show-ref --verify --quiet refs/remotes/origin/$branch_name; then
        echo -e "${RED}❌ Error: Branch 'origin/$branch_name' does not exist${NC}"
        exit 1
    fi
    
    # Run the Python analysis tool
    cd "$REPO_ROOT"
    python3 "$SCRIPT_DIR/branch_integration_manager.py" --branch "$branch_name"
    
    echo
    echo -e "${GREEN}✅ Analysis complete!${NC}"
}

# Main script logic
case "${1:-help}" in
    "analyze-all")
        analyze_all
        ;;
    "analyze")
        analyze_branch "$2"
        ;;
    "list-branches")
        list_branches
        ;;
    "quick-status")
        quick_status
        ;;
    "help"|"--help"|"-h")
        print_usage
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo
        print_usage
        exit 1
        ;;
esac
