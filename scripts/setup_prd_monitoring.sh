#!/bin/bash

# Setup PRD Documentation Monitoring System
# This script sets up the monitoring system for automatic PRD documentation updates

set -e

echo "🚀 Setting up PRD Documentation Monitoring System..."

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 Project root: $PROJECT_ROOT"

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Error: Not in a git repository"
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is required but not installed"
    exit 1
fi

# Check if required directories exist
DOCS_DIR="$PROJECT_ROOT/docs/sphinx/prds"
PRD_DIR="$PROJECT_ROOT/.prd"

if [ ! -d "$DOCS_DIR" ]; then
    echo "❌ Error: Sphinx PRD docs directory not found: $DOCS_DIR"
    exit 1
fi

if [ ! -d "$PRD_DIR" ]; then
    echo "❌ Error: PRD source directory not found: $PRD_DIR"
    exit 1
fi

echo "✅ Directory structure verified"

# Make monitoring script executable
chmod +x "$SCRIPT_DIR/prd_docs_monitor.py"
echo "✅ Made monitoring script executable"

# Test the monitoring script
echo "🧪 Testing monitoring script..."
if python3 "$SCRIPT_DIR/prd_docs_monitor.py" --check; then
    echo "✅ Monitoring script test successful"
else
    echo "⚠️  Monitoring script test completed (may be normal if no updates needed)"
fi

# Create systemd service file (optional, for Linux systems)
if command -v systemctl &> /dev/null; then
    echo "🔧 Creating systemd service file..."
    
    SERVICE_FILE="/tmp/prd-docs-monitor.service"
    cat > "$SERVICE_FILE" << EOF
[Unit]
Description=PRD Documentation Monitor
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$PROJECT_ROOT
ExecStart=/usr/bin/python3 $SCRIPT_DIR/prd_docs_monitor.py --daemon --interval 300
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    echo "📄 Systemd service file created at: $SERVICE_FILE"
    echo "   To install: sudo cp $SERVICE_FILE /etc/systemd/system/"
    echo "   To enable:  sudo systemctl enable prd-docs-monitor.service"
    echo "   To start:   sudo systemctl start prd-docs-monitor.service"
fi

# Create cron job setup (alternative to systemd)
echo "⏰ Creating cron job setup..."
CRON_COMMAND="*/5 * * * * cd $PROJECT_ROOT && python3 $SCRIPT_DIR/prd_docs_monitor.py --check >> /tmp/prd-monitor.log 2>&1"

echo "   To setup cron job (check every 5 minutes):"
echo "   crontab -e"
echo "   Add this line:"
echo "   $CRON_COMMAND"

# Create a simple wrapper script for manual runs
WRAPPER_SCRIPT="$PROJECT_ROOT/update-prd-docs.sh"
cat > "$WRAPPER_SCRIPT" << EOF
#!/bin/bash
# Simple wrapper to update PRD documentation

cd "$PROJECT_ROOT"
python3 "$SCRIPT_DIR/prd_docs_monitor.py" --check
EOF

chmod +x "$WRAPPER_SCRIPT"
echo "✅ Created wrapper script: $WRAPPER_SCRIPT"

# Create git hook (optional)
GIT_HOOKS_DIR="$PROJECT_ROOT/.git/hooks"
if [ -d "$GIT_HOOKS_DIR" ]; then
    POST_COMMIT_HOOK="$GIT_HOOKS_DIR/post-commit"
    
    # Create or append to post-commit hook
    if [ ! -f "$POST_COMMIT_HOOK" ]; then
        cat > "$POST_COMMIT_HOOK" << EOF
#!/bin/bash
# Auto-update PRD documentation after commits

cd "$PROJECT_ROOT"
python3 "$SCRIPT_DIR/prd_docs_monitor.py" --check
EOF
        chmod +x "$POST_COMMIT_HOOK"
        echo "✅ Created git post-commit hook"
    else
        echo "⚠️  Git post-commit hook already exists, skipping"
    fi
fi

# Test Sphinx build
echo "📚 Testing Sphinx documentation build..."
if [ -f "$PROJECT_ROOT/docs/sphinx/conf.py" ]; then
    cd "$PROJECT_ROOT/docs/sphinx"
    if command -v sphinx-build &> /dev/null; then
        if sphinx-build -b html . _build/html -q; then
            echo "✅ Sphinx build successful"
        else
            echo "⚠️  Sphinx build had warnings/errors"
        fi
    else
        echo "⚠️  Sphinx not installed, skipping build test"
        echo "   Install with: pip install sphinx sphinx-rtd-theme myst-parser"
    fi
    cd "$PROJECT_ROOT"
fi

echo ""
echo "🎉 PRD Documentation Monitoring Setup Complete!"
echo ""
echo "📋 Summary:"
echo "   ✅ Monitoring script: $SCRIPT_DIR/prd_docs_monitor.py"
echo "   ✅ Wrapper script: $WRAPPER_SCRIPT"
echo "   ✅ Documentation: $DOCS_DIR"
echo ""
echo "🚀 Usage:"
echo "   Manual check:    ./update-prd-docs.sh"
echo "   Daemon mode:     python3 scripts/prd_docs_monitor.py --daemon"
echo "   Force update:    python3 scripts/prd_docs_monitor.py --update"
echo ""
echo "📖 The system will automatically:"
echo "   • Monitor git commits for agent-related changes"
echo "   • Update PRD documentation based on commit messages"
echo "   • Track implementation progress and status"
echo "   • Maintain documentation freshness"
echo ""
echo "🔄 Next steps:"
echo "   1. Test with: ./update-prd-docs.sh"
echo "   2. Set up automated monitoring (cron or systemd)"
echo "   3. Build Sphinx docs: cd docs/sphinx && sphinx-build -b html . _build/html"
echo ""
EOF
