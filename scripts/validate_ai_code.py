#!/usr/bin/env python3
"""
AI Code Validation Script

This script validates AI-generated code against project standards:
- PEP-8 compliance
- PEP-257 docstring conventions
- PEP-484 type hints
- Test coverage requirements
- Security best practices
"""

import ast
import os
import sys
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import subprocess
import json


class AICodeValidator:
    """Validates AI-generated code against project standards."""
    
    def __init__(self, project_root: str = "."):
        """Initialize the validator.
        
        Args:
            project_root: Path to the project root directory
        """
        self.project_root = Path(project_root)
        self.errors: List[str] = []
        self.warnings: List[str] = []
        
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate a single Python file.
        
        Args:
            file_path: Path to the Python file to validate
            
        Returns:
            Dictionary containing validation results
        """
        results = {
            "file": str(file_path),
            "errors": [],
            "warnings": [],
            "passed": True
        }
        
        if not file_path.exists() or not file_path.suffix == ".py":
            return results
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Parse AST for analysis
            tree = ast.parse(content)
            
            # Run validation checks
            self._check_docstrings(tree, content, results)
            self._check_type_hints(tree, results)
            self._check_function_complexity(tree, results)
            self._check_naming_conventions(tree, results)
            self._check_imports(tree, results)
            
        except SyntaxError as e:
            results["errors"].append(f"Syntax error: {e}")
            results["passed"] = False
        except Exception as e:
            results["errors"].append(f"Validation error: {e}")
            results["passed"] = False
            
        return results
    
    def _check_docstrings(self, tree: ast.AST, content: str, results: Dict[str, Any]) -> None:
        """Check PEP-257 docstring compliance.
        
        Args:
            tree: AST tree of the file
            content: File content as string
            results: Results dictionary to update
        """
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.AsyncFunctionDef)):
                # Skip private methods and test methods for docstring requirements
                if node.name.startswith('_') and not node.name.startswith('__'):
                    continue
                if node.name.startswith('test_'):
                    continue
                    
                docstring = ast.get_docstring(node)
                if not docstring:
                    results["errors"].append(
                        f"Missing docstring for {type(node).__name__.lower()} '{node.name}' "
                        f"at line {node.lineno}"
                    )
                    results["passed"] = False
                elif len(docstring.strip()) < 10:
                    results["warnings"].append(
                        f"Short docstring for {type(node).__name__.lower()} '{node.name}' "
                        f"at line {node.lineno}"
                    )
    
    def _check_type_hints(self, tree: ast.AST, results: Dict[str, Any]) -> None:
        """Check PEP-484 type hint compliance.
        
        Args:
            tree: AST tree of the file
            results: Results dictionary to update
        """
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Skip test methods and private methods
                if node.name.startswith('test_') or node.name.startswith('_'):
                    continue
                    
                # Check function arguments
                for arg in node.args.args:
                    if arg.arg != 'self' and arg.arg != 'cls' and not arg.annotation:
                        results["warnings"].append(
                            f"Missing type hint for parameter '{arg.arg}' in function "
                            f"'{node.name}' at line {node.lineno}"
                        )
                
                # Check return type annotation
                if not node.returns and node.name != '__init__':
                    results["warnings"].append(
                        f"Missing return type hint for function '{node.name}' "
                        f"at line {node.lineno}"
                    )
    
    def _check_function_complexity(self, tree: ast.AST, results: Dict[str, Any]) -> None:
        """Check function complexity (cyclomatic complexity).
        
        Args:
            tree: AST tree of the file
            results: Results dictionary to update
        """
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                complexity = self._calculate_complexity(node)
                if complexity > 10:
                    results["warnings"].append(
                        f"High complexity ({complexity}) in function '{node.name}' "
                        f"at line {node.lineno}. Consider refactoring."
                    )
    
    def _calculate_complexity(self, node: ast.AST) -> int:
        """Calculate cyclomatic complexity of a function.
        
        Args:
            node: AST node representing a function
            
        Returns:
            Cyclomatic complexity score
        """
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
                
        return complexity
    
    def _check_naming_conventions(self, tree: ast.AST, results: Dict[str, Any]) -> None:
        """Check PEP-8 naming conventions.
        
        Args:
            tree: AST tree of the file
            results: Results dictionary to update
        """
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if not re.match(r'^[a-z_][a-z0-9_]*$', node.name):
                    results["warnings"].append(
                        f"Function name '{node.name}' doesn't follow snake_case convention "
                        f"at line {node.lineno}"
                    )
            elif isinstance(node, ast.ClassDef):
                if not re.match(r'^[A-Z][a-zA-Z0-9]*$', node.name):
                    results["warnings"].append(
                        f"Class name '{node.name}' doesn't follow PascalCase convention "
                        f"at line {node.lineno}"
                    )
    
    def _check_imports(self, tree: ast.AST, results: Dict[str, Any]) -> None:
        """Check import organization and best practices.
        
        Args:
            tree: AST tree of the file
            results: Results dictionary to update
        """
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                imports.append(node)
        
        # Check for unused imports (basic check)
        # This is a simplified check - a full implementation would need more analysis
        pass
    
    def validate_test_coverage(self, file_path: Path) -> Dict[str, Any]:
        """Check if the file has corresponding tests.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            Dictionary containing test coverage information
        """
        results = {
            "file": str(file_path),
            "has_tests": False,
            "test_files": []
        }
        
        # Look for corresponding test files
        relative_path = file_path.relative_to(self.project_root)
        test_patterns = [
            f"tests/test_{relative_path.stem}.py",
            f"tests/{relative_path.parent}/test_{relative_path.stem}.py",
            f"test_{relative_path.stem}.py"
        ]
        
        for pattern in test_patterns:
            test_file = self.project_root / pattern
            if test_file.exists():
                results["has_tests"] = True
                results["test_files"].append(str(test_file))
        
        return results
    
    def run_security_check(self, file_path: Path) -> Dict[str, Any]:
        """Run security checks on the file using bandit.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            Dictionary containing security check results
        """
        results = {
            "file": str(file_path),
            "security_issues": [],
            "passed": True
        }
        
        try:
            # Run bandit on the specific file
            cmd = ["bandit", "-f", "json", str(file_path)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0 and result.stdout:
                bandit_output = json.loads(result.stdout)
                if "results" in bandit_output:
                    for issue in bandit_output["results"]:
                        results["security_issues"].append({
                            "test_id": issue.get("test_id"),
                            "issue_severity": issue.get("issue_severity"),
                            "issue_text": issue.get("issue_text"),
                            "line_number": issue.get("line_number")
                        })
                        results["passed"] = False
                        
        except (subprocess.SubprocessError, json.JSONDecodeError, FileNotFoundError):
            # Bandit not available or other error
            pass
            
        return results
    
    def validate_project(self, changed_files: Optional[List[str]] = None) -> Dict[str, Any]:
        """Validate the entire project or specific files.
        
        Args:
            changed_files: List of changed files to validate (optional)
            
        Returns:
            Dictionary containing overall validation results
        """
        results = {
            "total_files": 0,
            "passed_files": 0,
            "failed_files": 0,
            "files": [],
            "summary": {
                "errors": 0,
                "warnings": 0
            }
        }
        
        # Determine files to validate
        if changed_files:
            files_to_check = [Path(f) for f in changed_files if f.endswith('.py')]
        else:
            files_to_check = list(self.project_root.rglob("*.py"))
            # Exclude common directories
            files_to_check = [
                f for f in files_to_check 
                if not any(part.startswith('.') for part in f.parts)
                and 'venv' not in f.parts
                and '__pycache__' not in f.parts
            ]
        
        for file_path in files_to_check:
            file_results = self.validate_file(file_path)
            test_results = self.validate_test_coverage(file_path)
            security_results = self.run_security_check(file_path)
            
            # Combine results
            combined_results = {
                **file_results,
                "test_coverage": test_results,
                "security": security_results
            }
            
            results["files"].append(combined_results)
            results["total_files"] += 1
            
            if file_results["passed"]:
                results["passed_files"] += 1
            else:
                results["failed_files"] += 1
            
            results["summary"]["errors"] += len(file_results["errors"])
            results["summary"]["warnings"] += len(file_results["warnings"])
        
        return results
    
    def print_results(self, results: Dict[str, Any]) -> None:
        """Print validation results in a readable format.
        
        Args:
            results: Validation results dictionary
        """
        print(f"\n🔍 AI Code Validation Results")
        print(f"{'='*50}")
        print(f"Total files checked: {results['total_files']}")
        print(f"Passed: {results['passed_files']}")
        print(f"Failed: {results['failed_files']}")
        print(f"Errors: {results['summary']['errors']}")
        print(f"Warnings: {results['summary']['warnings']}")
        
        if results['failed_files'] > 0:
            print(f"\n❌ Files with issues:")
            for file_result in results['files']:
                if not file_result['passed']:
                    print(f"\n📁 {file_result['file']}")
                    for error in file_result['errors']:
                        print(f"  ❌ {error}")
                    for warning in file_result['warnings']:
                        print(f"  ⚠️  {warning}")
        
        if results['summary']['warnings'] > 0:
            print(f"\n⚠️  Files with warnings:")
            for file_result in results['files']:
                if file_result['warnings'] and file_result['passed']:
                    print(f"\n📁 {file_result['file']}")
                    for warning in file_result['warnings']:
                        print(f"  ⚠️  {warning}")


def main():
    """Main entry point for the validation script."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate AI-generated code")
    parser.add_argument(
        "--files", 
        nargs="*", 
        help="Specific files to validate"
    )
    parser.add_argument(
        "--project-root", 
        default=".", 
        help="Project root directory"
    )
    parser.add_argument(
        "--json-output", 
        action="store_true", 
        help="Output results in JSON format"
    )
    
    args = parser.parse_args()
    
    validator = AICodeValidator(args.project_root)
    results = validator.validate_project(args.files)
    
    if args.json_output:
        print(json.dumps(results, indent=2))
    else:
        validator.print_results(results)
    
    # Exit with error code if validation failed
    if results['failed_files'] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
