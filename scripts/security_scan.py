#!/usr/bin/env python3
"""
Local Security Scanning Script for CertPathFinder

This script performs comprehensive security scans including:
- Dependency vulnerability scanning
- Code security analysis
- Configuration security checks
- Docker security scanning
"""

import subprocess
import sys
import json
import os
from pathlib import Path
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SecurityScanner:
    """Local security scanner for CertPathFinder."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.results = {
            'vulnerabilities': [],
            'security_issues': [],
            'recommendations': [],
            'summary': {}
        }
    
    def run_safety_check(self) -> Dict[str, Any]:
        """Run safety check for Python dependencies."""
        logger.info("Running safety check for Python dependencies...")
        
        try:
            # Install safety if not present
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'safety'], 
                         capture_output=True, check=True)
            
            # Run safety check
            result = subprocess.run([
                sys.executable, '-m', 'safety', 'check', '--json'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                logger.info("✅ No known vulnerabilities found in dependencies")
                return {'status': 'clean', 'vulnerabilities': []}
            else:
                vulnerabilities = json.loads(result.stdout) if result.stdout else []
                logger.warning(f"⚠️  Found {len(vulnerabilities)} vulnerabilities")
                return {'status': 'vulnerabilities_found', 'vulnerabilities': vulnerabilities}
                
        except Exception as e:
            logger.error(f"Error running safety check: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def run_bandit_scan(self) -> Dict[str, Any]:
        """Run Bandit security linting for Python code."""
        logger.info("Running Bandit security scan...")
        
        try:
            # Install bandit if not present
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'bandit'], 
                         capture_output=True, check=True)
            
            # Run bandit scan
            result = subprocess.run([
                sys.executable, '-m', 'bandit', '-r', '.', 
                '-f', 'json', '-x', './venv,./docs,./tests'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.stdout:
                bandit_results = json.loads(result.stdout)
                issues = bandit_results.get('results', [])
                logger.info(f"Bandit scan completed. Found {len(issues)} security issues")
                return {'status': 'completed', 'issues': issues}
            else:
                logger.info("✅ Bandit scan completed with no issues")
                return {'status': 'clean', 'issues': []}
                
        except Exception as e:
            logger.error(f"Error running Bandit scan: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def check_secrets(self) -> Dict[str, Any]:
        """Check for exposed secrets in code."""
        logger.info("Scanning for exposed secrets...")
        
        secret_patterns = [
            r'password\s*=\s*["\'][^"\']+["\']',
            r'api_key\s*=\s*["\'][^"\']+["\']',
            r'secret_key\s*=\s*["\'][^"\']+["\']',
            r'token\s*=\s*["\'][^"\']+["\']',
            r'aws_access_key_id\s*=\s*["\'][^"\']+["\']',
            r'aws_secret_access_key\s*=\s*["\'][^"\']+["\']',
        ]
        
        issues = []
        
        try:
            for pattern in secret_patterns:
                result = subprocess.run([
                    'grep', '-r', '-n', '-i', pattern, '.', 
                    '--exclude-dir=venv', '--exclude-dir=.git', 
                    '--exclude-dir=docs', '--exclude=*.pyc'
                ], capture_output=True, text=True, cwd=self.project_root)
                
                if result.stdout:
                    for line in result.stdout.strip().split('\n'):
                        if line and not line.startswith('.env.example'):
                            issues.append({
                                'type': 'potential_secret',
                                'location': line.split(':')[0],
                                'line': line.split(':')[1] if ':' in line else 'unknown',
                                'pattern': pattern
                            })
            
            if issues:
                logger.warning(f"⚠️  Found {len(issues)} potential secret exposures")
            else:
                logger.info("✅ No exposed secrets found")
                
            return {'status': 'completed', 'issues': issues}
            
        except Exception as e:
            logger.error(f"Error checking for secrets: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def check_docker_security(self) -> Dict[str, Any]:
        """Check Docker configuration security."""
        logger.info("Checking Docker security configuration...")
        
        issues = []
        recommendations = []
        
        # Check Dockerfile security
        dockerfile_path = self.project_root / 'Dockerfile.api'
        if dockerfile_path.exists():
            with open(dockerfile_path, 'r') as f:
                content = f.read()
                
                # Check for security best practices
                if 'USER root' in content:
                    issues.append({
                        'type': 'docker_security',
                        'severity': 'medium',
                        'issue': 'Running as root user in Docker container',
                        'file': 'Dockerfile.api'
                    })
                
                if 'ADD' in content and 'http' in content:
                    issues.append({
                        'type': 'docker_security',
                        'severity': 'low',
                        'issue': 'Using ADD with URLs can be insecure',
                        'file': 'Dockerfile.api'
                    })
                
                if '--no-cache-dir' not in content:
                    recommendations.append({
                        'type': 'docker_optimization',
                        'recommendation': 'Use --no-cache-dir with pip install to reduce image size'
                    })
        
        # Check docker-compose security
        compose_path = self.project_root / 'docker-compose.yml'
        if compose_path.exists():
            with open(compose_path, 'r') as f:
                content = f.read()
                
                if 'privileged: true' in content:
                    issues.append({
                        'type': 'docker_security',
                        'severity': 'high',
                        'issue': 'Privileged mode enabled in docker-compose',
                        'file': 'docker-compose.yml'
                    })
                
                if 'network_mode: host' in content:
                    issues.append({
                        'type': 'docker_security',
                        'severity': 'medium',
                        'issue': 'Host network mode can be insecure',
                        'file': 'docker-compose.yml'
                    })
        
        logger.info(f"Docker security check completed. Found {len(issues)} issues")
        return {
            'status': 'completed',
            'issues': issues,
            'recommendations': recommendations
        }
    
    def check_environment_security(self) -> Dict[str, Any]:
        """Check environment configuration security."""
        logger.info("Checking environment security...")
        
        issues = []
        recommendations = []
        
        # Check .env file security
        env_file = self.project_root / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                content = f.read()
                
                # Check for weak configurations
                if 'SECRET_KEY=changeme' in content or 'SECRET_KEY=secret' in content:
                    issues.append({
                        'type': 'weak_secret',
                        'severity': 'high',
                        'issue': 'Weak or default secret key detected'
                    })
                
                if 'DEBUG=True' in content or 'DEBUG=true' in content:
                    issues.append({
                        'type': 'debug_enabled',
                        'severity': 'medium',
                        'issue': 'Debug mode enabled in production'
                    })
                
                if 'password=password' in content.lower():
                    issues.append({
                        'type': 'weak_password',
                        'severity': 'high',
                        'issue': 'Weak default password detected'
                    })
        
        # Check for .env.example
        env_example = self.project_root / '.env.example'
        if not env_example.exists():
            recommendations.append({
                'type': 'configuration',
                'recommendation': 'Create .env.example file with safe default values'
            })
        
        logger.info(f"Environment security check completed. Found {len(issues)} issues")
        return {
            'status': 'completed',
            'issues': issues,
            'recommendations': recommendations
        }
    
    def generate_report(self) -> str:
        """Generate comprehensive security report."""
        logger.info("Generating security report...")
        
        # Run all security checks
        safety_results = self.run_safety_check()
        bandit_results = self.run_bandit_scan()
        secrets_results = self.check_secrets()
        docker_results = self.check_docker_security()
        env_results = self.check_environment_security()
        
        # Compile results
        total_vulnerabilities = len(safety_results.get('vulnerabilities', []))
        total_code_issues = len(bandit_results.get('issues', []))
        total_secret_issues = len(secrets_results.get('issues', []))
        total_docker_issues = len(docker_results.get('issues', []))
        total_env_issues = len(env_results.get('issues', []))
        
        total_issues = (total_vulnerabilities + total_code_issues + 
                       total_secret_issues + total_docker_issues + total_env_issues)
        
        # Generate report
        report = f"""
🔒 CertPathFinder Security Scan Report
=====================================

📊 Summary:
-----------
Total Issues Found: {total_issues}
- Dependency Vulnerabilities: {total_vulnerabilities}
- Code Security Issues: {total_code_issues}
- Potential Secret Exposures: {total_secret_issues}
- Docker Security Issues: {total_docker_issues}
- Environment Security Issues: {total_env_issues}

🔍 Detailed Results:
-------------------

1. Dependency Vulnerabilities ({total_vulnerabilities}):
"""
        
        if safety_results.get('vulnerabilities'):
            for vuln in safety_results['vulnerabilities']:
                report += f"   - {vuln.get('package', 'Unknown')}: {vuln.get('vulnerability', 'Unknown vulnerability')}\n"
        else:
            report += "   ✅ No known vulnerabilities found\n"
        
        report += f"\n2. Code Security Issues ({total_code_issues}):\n"
        if bandit_results.get('issues'):
            for issue in bandit_results['issues'][:5]:  # Show first 5
                report += f"   - {issue.get('filename', 'Unknown')}: {issue.get('issue_text', 'Unknown issue')}\n"
            if len(bandit_results['issues']) > 5:
                report += f"   ... and {len(bandit_results['issues']) - 5} more issues\n"
        else:
            report += "   ✅ No code security issues found\n"
        
        report += f"\n3. Potential Secret Exposures ({total_secret_issues}):\n"
        if secrets_results.get('issues'):
            for issue in secrets_results['issues']:
                report += f"   - {issue.get('location', 'Unknown')}: Line {issue.get('line', 'Unknown')}\n"
        else:
            report += "   ✅ No exposed secrets found\n"
        
        report += f"\n4. Docker Security Issues ({total_docker_issues}):\n"
        if docker_results.get('issues'):
            for issue in docker_results['issues']:
                report += f"   - {issue.get('file', 'Unknown')}: {issue.get('issue', 'Unknown issue')}\n"
        else:
            report += "   ✅ No Docker security issues found\n"
        
        report += f"\n5. Environment Security Issues ({total_env_issues}):\n"
        if env_results.get('issues'):
            for issue in env_results['issues']:
                report += f"   - {issue.get('issue', 'Unknown issue')} (Severity: {issue.get('severity', 'Unknown')})\n"
        else:
            report += "   ✅ No environment security issues found\n"
        
        # Add recommendations
        all_recommendations = []
        all_recommendations.extend(docker_results.get('recommendations', []))
        all_recommendations.extend(env_results.get('recommendations', []))
        
        if all_recommendations:
            report += f"\n💡 Recommendations ({len(all_recommendations)}):\n"
            for rec in all_recommendations:
                report += f"   - {rec.get('recommendation', 'Unknown recommendation')}\n"
        
        # Overall security score
        max_score = 100
        deductions = min(total_issues * 5, 80)  # Max 80 point deduction
        security_score = max(max_score - deductions, 20)  # Min score of 20
        
        report += f"\n🎯 Security Score: {security_score}/100\n"
        
        if security_score >= 90:
            report += "   🟢 Excellent security posture\n"
        elif security_score >= 70:
            report += "   🟡 Good security posture with room for improvement\n"
        elif security_score >= 50:
            report += "   🟠 Moderate security concerns that should be addressed\n"
        else:
            report += "   🔴 Significant security issues requiring immediate attention\n"
        
        report += f"\n📅 Scan completed at: {subprocess.run(['date'], capture_output=True, text=True).stdout.strip()}\n"
        
        return report

def main():
    """Main function to run security scan."""
    scanner = SecurityScanner()
    
    print("🔒 Starting CertPathFinder Security Scan...")
    print("=" * 50)
    
    report = scanner.generate_report()
    
    # Save report to file
    report_file = Path('security_report.txt')
    with open(report_file, 'w') as f:
        f.write(report)
    
    print(report)
    print(f"\n📄 Full report saved to: {report_file.absolute()}")

if __name__ == "__main__":
    main()
