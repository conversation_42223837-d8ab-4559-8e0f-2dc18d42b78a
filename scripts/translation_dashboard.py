#!/usr/bin/env python3
"""
Translation Dashboard for CertPathFinder

This script creates a comprehensive dashboard showing translation status,
coverage, and quality metrics for all supported languages.
"""

import os
import re
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TranslationDashboard:
    """Creates a comprehensive translation dashboard."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.translations_dir = self.project_root / 'translations'
        
        self.languages = {
            'es': {'name': 'Español', 'flag': '🇪🇸', 'region': 'Spain/Latin America'},
            'fr': {'name': 'Français', 'flag': '🇫🇷', 'region': 'France/Francophone'},
            'de': {'name': 'Deutsch', 'flag': '🇩🇪', 'region': 'Germany/DACH'},
            'pt': {'name': 'Português', 'flag': '🇵🇹', 'region': 'Portugal/Brazil'},
            'it': {'name': 'Italiano', 'flag': '🇮🇹', 'region': 'Italy'},
            'ja': {'name': '日本語', 'flag': '🇯🇵', 'region': 'Japan'},
            'ko': {'name': '한국어', 'flag': '🇰🇷', 'region': 'South Korea'},
            'zh': {'name': '中文', 'flag': '🇨🇳', 'region': 'China'},
            'ar': {'name': 'العربية', 'flag': '🇸🇦', 'region': 'Middle East'},
            'hi': {'name': 'हिन्दी', 'flag': '🇮🇳', 'region': 'India'},
            'ru': {'name': 'Русский', 'flag': '🇷🇺', 'region': 'Russia/CIS'},
            'nl': {'name': 'Nederlands', 'flag': '🇳🇱', 'region': 'Netherlands'},
            'sv': {'name': 'Svenska', 'flag': '🇸🇪', 'region': 'Sweden'},
            'no': {'name': 'Norsk', 'flag': '🇳🇴', 'region': 'Norway'},
            'da': {'name': 'Dansk', 'flag': '🇩🇰', 'region': 'Denmark'}
        }
    
    def analyze_po_file(self, language: str) -> Dict:
        """Analyze a .po file for completeness and quality."""
        po_file = self.translations_dir / language / 'LC_MESSAGES' / 'messages.po'
        
        if not po_file.exists():
            return {
                'exists': False,
                'total_entries': 0,
                'translated_entries': 0,
                'completion_percentage': 0,
                'quality_score': 0,
                'file_size': 0,
                'last_modified': None
            }
        
        # Get file stats
        file_stats = po_file.stat()
        file_size = file_stats.st_size
        last_modified = datetime.fromtimestamp(file_stats.st_mtime)
        
        # Read and analyze content
        with open(po_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count entries
        msgid_pattern = r'msgid\s+"([^"]*(?:"[^"]*)*)"'
        msgstr_pattern = r'msgstr\s+"([^"]*(?:"[^"]*)*)"'
        
        msgids = re.findall(msgid_pattern, content)
        msgstrs = re.findall(msgstr_pattern, content)
        
        # Filter out empty msgids (headers)
        valid_entries = [(mid, mstr) for mid, mstr in zip(msgids, msgstrs) if mid.strip()]
        
        total_entries = len(valid_entries)
        translated_entries = sum(1 for _, mstr in valid_entries if mstr.strip())
        
        completion_percentage = (translated_entries / total_entries * 100) if total_entries > 0 else 0
        
        # Calculate quality score based on various factors
        quality_score = self.calculate_quality_score(valid_entries, content)
        
        return {
            'exists': True,
            'total_entries': total_entries,
            'translated_entries': translated_entries,
            'completion_percentage': completion_percentage,
            'quality_score': quality_score,
            'file_size': file_size,
            'last_modified': last_modified
        }
    
    def calculate_quality_score(self, entries: List[Tuple[str, str]], content: str) -> float:
        """Calculate a quality score for translations."""
        if not entries:
            return 0.0
        
        score = 0.0
        max_score = 100.0
        
        # Completion score (40% of total)
        translated_count = sum(1 for _, mstr in entries if mstr.strip())
        completion_score = (translated_count / len(entries)) * 40
        score += completion_score
        
        # Length consistency score (20% of total)
        length_scores = []
        for msgid, msgstr in entries:
            if msgstr.strip():
                id_len = len(msgid)
                str_len = len(msgstr)
                if id_len > 0:
                    ratio = min(str_len / id_len, 2.0)  # Cap at 2x length
                    length_scores.append(min(ratio, 1.0))
        
        if length_scores:
            avg_length_score = sum(length_scores) / len(length_scores)
            score += avg_length_score * 20
        
        # Character encoding score (10% of total)
        try:
            content.encode('utf-8')
            score += 10
        except UnicodeEncodeError:
            score += 5
        
        # Header completeness score (10% of total)
        required_headers = ['Language:', 'Content-Type:', 'Content-Transfer-Encoding:']
        header_score = sum(1 for header in required_headers if header in content)
        score += (header_score / len(required_headers)) * 10
        
        # Consistency score (20% of total)
        # Check for consistent terminology
        consistency_score = 20  # Default full score, reduce for inconsistencies
        
        # Check for common inconsistencies
        common_terms = ['Login', 'Admin', 'FAQ', 'Error']
        for term in common_terms:
            translations = [mstr for mid, mstr in entries if term.lower() in mid.lower() and mstr.strip()]
            if len(set(translations)) > 1:  # Multiple different translations for same term
                consistency_score -= 2
        
        score += max(consistency_score, 0)
        
        return min(score, max_score)
    
    def check_mo_file(self, language: str) -> bool:
        """Check if compiled .mo file exists and is up to date."""
        po_file = self.translations_dir / language / 'LC_MESSAGES' / 'messages.po'
        mo_file = self.translations_dir / language / 'LC_MESSAGES' / 'messages.mo'
        
        if not mo_file.exists():
            return False
        
        if not po_file.exists():
            return False
        
        # Check if .mo is newer than .po
        po_mtime = po_file.stat().st_mtime
        mo_mtime = mo_file.stat().st_mtime
        
        return mo_mtime >= po_mtime
    
    def generate_dashboard_report(self) -> str:
        """Generate a comprehensive dashboard report."""
        report = f"""
🌍 CertPathFinder Translation Dashboard
=====================================
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 OVERALL STATISTICS
--------------------
"""
        
        # Analyze all languages
        language_stats = {}
        for lang_code in self.languages.keys():
            language_stats[lang_code] = self.analyze_po_file(lang_code)
        
        # Calculate overall statistics
        total_languages = len(self.languages)
        existing_languages = sum(1 for stats in language_stats.values() if stats['exists'])
        fully_complete = sum(1 for stats in language_stats.values() 
                           if stats['completion_percentage'] == 100)
        
        avg_completion = sum(stats['completion_percentage'] for stats in language_stats.values()) / total_languages
        avg_quality = sum(stats['quality_score'] for stats in language_stats.values() if stats['exists']) / max(existing_languages, 1)
        
        report += f"Total Languages Supported: {total_languages}\n"
        report += f"Languages with Files: {existing_languages}\n"
        report += f"Fully Complete Languages: {fully_complete}\n"
        report += f"Average Completion: {avg_completion:.1f}%\n"
        report += f"Average Quality Score: {avg_quality:.1f}/100\n\n"
        
        # Language details
        report += "📋 LANGUAGE DETAILS\n"
        report += "-------------------\n"
        
        # Sort by completion percentage
        sorted_languages = sorted(
            language_stats.items(),
            key=lambda x: (x[1]['completion_percentage'], x[1]['quality_score']),
            reverse=True
        )
        
        for lang_code, stats in sorted_languages:
            lang_info = self.languages[lang_code]
            
            if not stats['exists']:
                status_emoji = "❌"
                status_text = "Not Created"
            elif stats['completion_percentage'] == 100:
                status_emoji = "✅"
                status_text = "Complete"
            elif stats['completion_percentage'] >= 80:
                status_emoji = "🟢"
                status_text = "Nearly Complete"
            elif stats['completion_percentage'] >= 50:
                status_emoji = "🟡"
                status_text = "In Progress"
            else:
                status_emoji = "🔴"
                status_text = "Started"
            
            # Check compilation status
            mo_status = "✅" if self.check_mo_file(lang_code) else "❌"
            
            report += f"{status_emoji} {lang_info['flag']} {lang_info['name']} ({lang_code})\n"
            report += f"   Region: {lang_info['region']}\n"
            report += f"   Completion: {stats['completion_percentage']:.1f}% ({stats['translated_entries']}/{stats['total_entries']})\n"
            report += f"   Quality Score: {stats['quality_score']:.1f}/100\n"
            report += f"   Compiled: {mo_status}\n"
            
            if stats['exists']:
                report += f"   File Size: {stats['file_size']:,} bytes\n"
                if stats['last_modified']:
                    report += f"   Last Modified: {stats['last_modified'].strftime('%Y-%m-%d %H:%M')}\n"
            
            report += "\n"
        
        # Priority recommendations
        report += "🎯 PRIORITY RECOMMENDATIONS\n"
        report += "---------------------------\n"
        
        # High priority languages (major markets)
        high_priority = ['es', 'fr', 'de', 'pt', 'it', 'ja', 'zh']
        
        for lang_code in high_priority:
            stats = language_stats[lang_code]
            lang_info = self.languages[lang_code]
            
            if not stats['exists']:
                report += f"🔴 HIGH: Create {lang_info['name']} translation files\n"
            elif stats['completion_percentage'] < 100:
                missing = stats['total_entries'] - stats['translated_entries']
                report += f"🟡 MEDIUM: Complete {missing} missing translations for {lang_info['name']}\n"
            elif not self.check_mo_file(lang_code):
                report += f"🟠 LOW: Compile {lang_info['name']} translations\n"
        
        # Quality improvements
        for lang_code, stats in sorted_languages:
            if stats['exists'] and stats['quality_score'] < 80:
                lang_info = self.languages[lang_code]
                report += f"🔧 QUALITY: Improve translation quality for {lang_info['name']} (Score: {stats['quality_score']:.1f})\n"
        
        # Market coverage analysis
        report += "\n🌐 MARKET COVERAGE ANALYSIS\n"
        report += "--------------------------\n"
        
        market_regions = {
            'Europe': ['es', 'fr', 'de', 'it', 'nl', 'sv', 'no', 'da'],
            'Asia-Pacific': ['ja', 'ko', 'zh', 'hi'],
            'Americas': ['es', 'pt'],
            'Middle East': ['ar'],
            'Eastern Europe': ['ru']
        }
        
        for region, lang_codes in market_regions.items():
            region_completion = []
            for lang_code in lang_codes:
                if lang_code in language_stats:
                    region_completion.append(language_stats[lang_code]['completion_percentage'])
            
            if region_completion:
                avg_region_completion = sum(region_completion) / len(region_completion)
                coverage_emoji = "🟢" if avg_region_completion >= 80 else "🟡" if avg_region_completion >= 50 else "🔴"
                report += f"{coverage_emoji} {region}: {avg_region_completion:.1f}% average completion\n"
        
        # Technical status
        report += "\n🔧 TECHNICAL STATUS\n"
        report += "------------------\n"
        
        # Check template file
        pot_file = self.translations_dir / 'messages.pot'
        if pot_file.exists():
            pot_stats = pot_file.stat()
            report += f"✅ Template file: {pot_stats.st_size:,} bytes, modified {datetime.fromtimestamp(pot_stats.st_mtime).strftime('%Y-%m-%d %H:%M')}\n"
        else:
            report += "❌ Template file: Missing\n"
        
        # Check translation tools
        try:
            subprocess.run(['msgfmt', '--version'], capture_output=True, check=True)
            report += "✅ msgfmt: Available\n"
        except:
            report += "❌ msgfmt: Not available\n"
        
        try:
            subprocess.run(['msginit', '--version'], capture_output=True, check=True)
            report += "✅ msginit: Available\n"
        except:
            report += "❌ msginit: Not available\n"
        
        report += f"\n📈 NEXT STEPS\n"
        report += "-------------\n"
        
        if fully_complete < 3:
            report += "1. Focus on completing Spanish, French, and German translations\n"
        elif fully_complete < 6:
            report += "1. Expand to Portuguese, Italian, and Japanese translations\n"
        else:
            report += "1. Add support for Chinese, Arabic, and other major languages\n"
        
        report += "2. Implement automated translation quality checks\n"
        report += "3. Set up continuous integration for translation updates\n"
        report += "4. Create translation contributor guidelines\n"
        report += "5. Implement translation memory for consistency\n"
        
        return report
    
    def save_dashboard(self) -> str:
        """Save dashboard to file and return the report."""
        report = self.generate_dashboard_report()
        
        # Save to file
        dashboard_file = self.project_root / 'translation_dashboard.md'
        with open(dashboard_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"Dashboard saved to {dashboard_file}")
        return report

def main():
    """Main function to generate translation dashboard."""
    dashboard = TranslationDashboard()
    
    print("🌍 Generating CertPathFinder Translation Dashboard")
    print("=" * 50)
    
    report = dashboard.save_dashboard()
    print(report)

if __name__ == "__main__":
    main()
