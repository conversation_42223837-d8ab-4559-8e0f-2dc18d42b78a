#!/usr/bin/env python3
"""
CertRats Service Status and URL Display Script

This script shows the current status of CertRats services and their accessible URLs
using the ServiceURLManager for consistent URL generation.
"""

import sys
import os
import subprocess
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.service_urls import ServiceURLManager

def get_running_containers():
    """Get list of running Docker containers related to CertRats."""
    try:
        result = subprocess.run(
            ['docker', 'ps', '--format', 'json'],
            capture_output=True,
            text=True,
            check=True
        )
        
        containers = []
        for line in result.stdout.strip().split('\n'):
            if line:
                container = json.loads(line)
                if 'certrats' in container.get('Names', '').lower() or 'traefik' in container.get('Names', '').lower():
                    containers.append(container)
        
        return containers
    except subprocess.CalledProcessError:
        return []

def check_service_health(url):
    """Check if a service is responding."""
    try:
        import urllib.request
        import urllib.error
        
        req = urllib.request.Request(url, headers={'User-Agent': 'CertRats-HealthCheck'})
        with urllib.request.urlopen(req, timeout=5) as response:
            return response.status == 200
    except:
        return False

def main():
    print("🐳 CertRats Service Status Dashboard")
    print("=" * 60)
    
    # Get environment
    environment = os.getenv('CERTRATS_ENV', 'development')
    print(f"📍 Environment: {environment}")
    
    # Initialize URL manager
    try:
        manager = ServiceURLManager(environment)
        env_info = manager.get_environment_info()
        print(f"🌐 Domain: {env_info['domain']}")
        print(f"🔒 Protocol: {env_info['protocol']}")
        print()
    except Exception as e:
        print(f"❌ Error initializing URL manager: {e}")
        return 1
    
    # Get running containers
    containers = get_running_containers()
    
    if not containers:
        print("⚠️  No CertRats or Traefik containers found running")
        print()
    else:
        print("🔄 Running Containers:")
        print("-" * 40)
        for container in containers:
            name = container.get('Names', 'Unknown')
            image = container.get('Image', 'Unknown')
            status = container.get('Status', 'Unknown')
            ports = container.get('Ports', '')
            
            print(f"  📦 {name}")
            print(f"     Image: {image}")
            print(f"     Status: {status}")
            if ports:
                print(f"     Ports: {ports}")
            print()
    
    # Show service URLs
    print("🌐 Service URLs:")
    print("-" * 40)
    
    try:
        # Get all service URLs
        service_urls = manager.get_all_service_urls()
        
        for service_name, url in service_urls.items():
            # Check if service is responding
            health_status = "🟢" if check_service_health(url) else "🔴"
            
            print(f"  {health_status} {service_name:20} {url}")
        
        print()
        
        # Show key API endpoints
        print("🔗 Key API Endpoints:")
        print("-" * 40)
        
        api_endpoints = [
            ('Authentication', 'auth', 'login'),
            ('User Registration', 'auth', 'register'),
            ('Certifications List', 'certifications', 'list'),
            ('Career Paths', 'career_paths', 'list'),
            ('User Profile', 'users', 'profile'),
            ('Health Check', 'health', 'status')
        ]
        
        for name, category, endpoint in api_endpoints:
            try:
                endpoint_url = manager.get_api_endpoint(category, endpoint)
                health_status = "🟢" if check_service_health(endpoint_url) else "🔴"
                print(f"  {health_status} {name:20} {endpoint_url}")
            except ValueError:
                print(f"  ⚪ {name:20} Not configured")
        
        print()
        
        # Show health check URLs
        health_urls = manager.health_check_urls()
        if health_urls:
            print("🏥 Health Check URLs:")
            print("-" * 40)
            for service_name, health_url in health_urls.items():
                health_status = "🟢" if check_service_health(health_url) else "🔴"
                print(f"  {health_status} {service_name:20} {health_url}")
            print()
        
    except Exception as e:
        print(f"❌ Error getting service URLs: {e}")
        return 1
    
    # Show quick access commands
    print("⚡ Quick Access:")
    print("-" * 40)
    print("  Frontend App:     http://app.certrats.localhost")
    print("  API Docs:         http://api.certrats.localhost/docs")
    print("  Health Check:     http://api.certrats.localhost/health")
    print()
    
    # Show management commands
    print("🛠️  Management Commands:")
    print("-" * 40)
    print("  View logs:        docker-compose -f docker-compose.frontend-only.yml logs -f")
    print("  Stop services:    docker-compose -f docker-compose.frontend-only.yml down")
    print("  Restart:          docker-compose -f docker-compose.frontend-only.yml restart")
    print("  Status:           docker-compose -f docker-compose.frontend-only.yml ps")
    print()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
