#!/usr/bin/env python3
"""
🚀 Branch Integration Manager
============================

Comprehensive tool for analyzing, evaluating, and managing branch integration
according to the PRD Branch Integration Strategy.

Features:
- Automated branch analysis and scoring
- Integration feasibility assessment
- Feature extraction and documentation
- Stale branch identification and management
- Quality metrics and reporting

Usage:
    python scripts/branch_integration_manager.py --analyze-all
    python scripts/branch_integration_manager.py --branch feature/skills-assessment-1.1
    python scripts/branch_integration_manager.py --extract-features refactor/security
    python scripts/branch_integration_manager.py --mark-stale feature/react-migration
"""

import argparse
import json
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import os

class BranchIntegrationManager:
    """Comprehensive branch integration management system."""
    
    def __init__(self, repo_path: str = "."):
        self.repo_path = Path(repo_path)
        self.reports_dir = self.repo_path / "reports" / "branch_analysis"
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        
        # Integration scoring weights
        self.scoring_weights = {
            "code_quality": 0.3,
            "business_value": 0.4,
            "technical_risk": 0.3
        }
        
        # Decision thresholds
        self.thresholds = {
            "merge": {"quality": 80, "value": 70, "risk": 30},
            "refactor_merge": {"quality": 60, "value": 70, "risk": 50},
            "capture_local": {"quality": 0, "value": 50, "risk": 100},
            "mark_stale": {"quality": 0, "value": 0, "risk": 100}
        }

    def get_remote_branches(self) -> List[str]:
        """Get list of all remote branches."""
        try:
            result = subprocess.run(
                ["git", "branch", "-r", "--no-merged", "master"],
                capture_output=True, text=True, cwd=self.repo_path
            )
            branches = []
            for line in result.stdout.strip().split('\n'):
                if line.strip() and not line.strip().startswith('origin/HEAD'):
                    branch = line.strip().replace('origin/', '')
                    if branch != 'master':
                        branches.append(branch)
            return branches
        except Exception as e:
            print(f"❌ Error getting remote branches: {e}")
            return []

    def analyze_branch_commits(self, branch: str) -> Dict:
        """Analyze commit history and changes for a branch."""
        try:
            # Get commit count
            commit_count_result = subprocess.run(
                ["git", "rev-list", "--count", f"master..origin/{branch}"],
                capture_output=True, text=True, cwd=self.repo_path
            )
            commit_count = int(commit_count_result.stdout.strip()) if commit_count_result.stdout.strip() else 0
            
            # Get file changes
            diff_stat_result = subprocess.run(
                ["git", "diff", "--stat", f"master...origin/{branch}"],
                capture_output=True, text=True, cwd=self.repo_path
            )
            
            # Parse diff stats
            lines = diff_stat_result.stdout.strip().split('\n')
            files_changed = 0
            insertions = 0
            deletions = 0
            
            for line in lines:
                if 'files changed' in line or 'file changed' in line:
                    parts = line.split(',')
                    for part in parts:
                        if 'file' in part and 'changed' in part:
                            files_changed = int(part.split()[0])
                        elif 'insertion' in part:
                            insertions = int(part.split()[0])
                        elif 'deletion' in part:
                            deletions = int(part.split()[0])
            
            # Get recent commit messages
            recent_commits_result = subprocess.run(
                ["git", "log", "--oneline", "-10", f"master..origin/{branch}"],
                capture_output=True, text=True, cwd=self.repo_path
            )
            recent_commits = recent_commits_result.stdout.strip().split('\n') if recent_commits_result.stdout.strip() else []
            
            return {
                "commit_count": commit_count,
                "files_changed": files_changed,
                "insertions": insertions,
                "deletions": deletions,
                "recent_commits": recent_commits,
                "change_magnitude": files_changed + (insertions + deletions) / 100
            }
            
        except Exception as e:
            print(f"❌ Error analyzing branch {branch}: {e}")
            return {
                "commit_count": 0,
                "files_changed": 0,
                "insertions": 0,
                "deletions": 0,
                "recent_commits": [],
                "change_magnitude": 0
            }

    def calculate_code_quality_score(self, branch: str, commit_data: Dict) -> int:
        """Calculate code quality score (0-100)."""
        score = 0
        
        # Commit quality (25 points)
        if commit_data["commit_count"] > 0:
            # Good commit frequency
            if 5 <= commit_data["commit_count"] <= 50:
                score += 25
            elif commit_data["commit_count"] <= 100:
                score += 20
            else:
                score += 10
        
        # Change magnitude assessment (25 points)
        magnitude = commit_data["change_magnitude"]
        if magnitude < 100:  # Small, focused changes
            score += 25
        elif magnitude < 500:  # Medium changes
            score += 20
        elif magnitude < 1000:  # Large changes
            score += 15
        else:  # Very large changes
            score += 10
        
        # Commit message quality (25 points)
        good_commits = 0
        for commit in commit_data["recent_commits"]:
            if any(keyword in commit.lower() for keyword in ['feat:', 'fix:', 'docs:', 'refactor:', 'test:']):
                good_commits += 1
        
        if commit_data["recent_commits"]:
            commit_quality_ratio = good_commits / len(commit_data["recent_commits"])
            score += int(25 * commit_quality_ratio)
        
        # File organization (25 points)
        if commit_data["files_changed"] > 0:
            # Reasonable file change ratio
            if commit_data["files_changed"] <= 50:
                score += 25
            elif commit_data["files_changed"] <= 200:
                score += 20
            else:
                score += 10
        
        return min(score, 100)

    def calculate_business_value_score(self, branch: str, commit_data: Dict) -> int:
        """Calculate business value score (0-100)."""
        score = 0
        
        # Feature completeness (30 points)
        if commit_data["commit_count"] >= 10:  # Substantial development
            score += 30
        elif commit_data["commit_count"] >= 5:
            score += 20
        else:
            score += 10
        
        # User impact assessment (30 points)
        branch_lower = branch.lower()
        if any(keyword in branch_lower for keyword in ['feature', 'enhancement', 'improvement']):
            score += 30
        elif any(keyword in branch_lower for keyword in ['fix', 'bug', 'security']):
            score += 25
        elif any(keyword in branch_lower for keyword in ['refactor', 'optimization']):
            score += 20
        else:
            score += 15
        
        # Technical innovation (20 points)
        if 'ai' in branch_lower or 'ml' in branch_lower or 'intelligence' in branch_lower:
            score += 20
        elif 'api' in branch_lower or 'service' in branch_lower:
            score += 15
        elif 'ui' in branch_lower or 'frontend' in branch_lower:
            score += 10
        else:
            score += 5
        
        # Strategic alignment (20 points)
        if any(keyword in branch_lower for keyword in ['enterprise', 'scale', 'performance']):
            score += 20
        elif any(keyword in branch_lower for keyword in ['user', 'experience', 'interface']):
            score += 15
        else:
            score += 10
        
        return min(score, 100)

    def calculate_technical_risk_score(self, branch: str, commit_data: Dict) -> int:
        """Calculate technical risk score (0-100, lower is better)."""
        risk = 0
        
        # Merge complexity (25 points)
        if commit_data["change_magnitude"] > 1000:
            risk += 25
        elif commit_data["change_magnitude"] > 500:
            risk += 20
        elif commit_data["change_magnitude"] > 100:
            risk += 15
        else:
            risk += 5
        
        # Breaking changes potential (25 points)
        if commit_data["files_changed"] > 500:
            risk += 25
        elif commit_data["files_changed"] > 200:
            risk += 20
        elif commit_data["files_changed"] > 50:
            risk += 15
        else:
            risk += 5
        
        # Age and staleness (25 points)
        if commit_data["commit_count"] == 0:
            risk += 25  # No commits = stale
        elif commit_data["commit_count"] > 100:
            risk += 20  # Too many commits = complex
        else:
            risk += 5
        
        # Dependency conflicts (25 points)
        # This would require more sophisticated analysis
        # For now, estimate based on change size
        if commit_data["insertions"] + commit_data["deletions"] > 10000:
            risk += 25
        elif commit_data["insertions"] + commit_data["deletions"] > 5000:
            risk += 20
        else:
            risk += 10
        
        return min(risk, 100)

    def determine_integration_strategy(self, quality: int, value: int, risk: int) -> str:
        """Determine integration strategy based on scores."""
        if (quality >= self.thresholds["merge"]["quality"] and 
            value >= self.thresholds["merge"]["value"] and 
            risk <= self.thresholds["merge"]["risk"]):
            return "MERGE"
        elif (quality >= self.thresholds["refactor_merge"]["quality"] and 
              value >= self.thresholds["refactor_merge"]["value"] and 
              risk <= self.thresholds["refactor_merge"]["risk"]):
            return "REFACTOR_THEN_MERGE"
        elif value >= self.thresholds["capture_local"]["value"]:
            return "CAPTURE_LOCALLY"
        else:
            return "MARK_STALE"

    def analyze_branch(self, branch: str) -> Dict:
        """Comprehensive branch analysis."""
        print(f"🔍 Analyzing branch: {branch}")
        
        # Get commit data
        commit_data = self.analyze_branch_commits(branch)
        
        # Calculate scores
        quality_score = self.calculate_code_quality_score(branch, commit_data)
        value_score = self.calculate_business_value_score(branch, commit_data)
        risk_score = self.calculate_technical_risk_score(branch, commit_data)
        
        # Determine strategy
        strategy = self.determine_integration_strategy(quality_score, value_score, risk_score)
        
        # Calculate overall score
        overall_score = (
            quality_score * self.scoring_weights["code_quality"] +
            value_score * self.scoring_weights["business_value"] +
            (100 - risk_score) * self.scoring_weights["technical_risk"]
        )
        
        analysis = {
            "branch": branch,
            "timestamp": datetime.now().isoformat(),
            "commit_data": commit_data,
            "scores": {
                "code_quality": quality_score,
                "business_value": value_score,
                "technical_risk": risk_score,
                "overall": round(overall_score, 2)
            },
            "strategy": strategy,
            "priority": self.get_priority_from_strategy(strategy),
            "recommendations": self.generate_recommendations(strategy, quality_score, value_score, risk_score)
        }
        
        return analysis

    def get_priority_from_strategy(self, strategy: str) -> str:
        """Get priority level from integration strategy."""
        priority_map = {
            "MERGE": "P0 - Critical",
            "REFACTOR_THEN_MERGE": "P1 - High",
            "CAPTURE_LOCALLY": "P2 - Medium",
            "MARK_STALE": "P3 - Low"
        }
        return priority_map.get(strategy, "P3 - Low")

    def generate_recommendations(self, strategy: str, quality: int, value: int, risk: int) -> List[str]:
        """Generate specific recommendations based on analysis."""
        recommendations = []
        
        if strategy == "MERGE":
            recommendations.extend([
                "✅ Ready for immediate merge",
                "🧪 Conduct comprehensive code review",
                "📊 Run full test suite before merge",
                "📚 Update documentation for new features"
            ])
        elif strategy == "REFACTOR_THEN_MERGE":
            if quality < 80:
                recommendations.append("🔧 Improve code quality before merge")
            if risk > 30:
                recommendations.append("⚠️ Address technical risks and complexity")
            recommendations.extend([
                "🔄 Refactor code to meet quality standards",
                "🧪 Add comprehensive test coverage",
                "📋 Plan staged integration approach"
            ])
        elif strategy == "CAPTURE_LOCALLY":
            recommendations.extend([
                "📦 Extract valuable features and code snippets",
                "📚 Document functionality for future reference",
                "🔍 Consider reimplementation with better architecture",
                "🗂️ Archive branch with comprehensive documentation"
            ])
        else:  # MARK_STALE
            recommendations.extend([
                "🗑️ Mark branch as stale",
                "📋 Document any lessons learned",
                "🧹 Clean up repository references",
                "📝 Update project documentation"
            ])
        
        return recommendations

    def save_analysis_report(self, analysis: Dict) -> str:
        """Save analysis report to file."""
        report_file = self.reports_dir / f"branch_analysis_{analysis['branch'].replace('/', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w') as f:
            json.dump(analysis, f, indent=2)
        
        return str(report_file)

    def generate_summary_report(self, analyses: List[Dict]) -> str:
        """Generate comprehensive summary report."""
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_branches": len(analyses),
            "strategies": {},
            "priorities": {},
            "recommendations": [],
            "branches": analyses
        }
        
        # Count strategies and priorities
        for analysis in analyses:
            strategy = analysis["strategy"]
            priority = analysis["priority"]
            
            summary["strategies"][strategy] = summary["strategies"].get(strategy, 0) + 1
            summary["priorities"][priority] = summary["priorities"].get(priority, 0) + 1
        
        # Generate overall recommendations
        merge_candidates = [a for a in analyses if a["strategy"] == "MERGE"]
        refactor_candidates = [a for a in analyses if a["strategy"] == "REFACTOR_THEN_MERGE"]
        
        if merge_candidates:
            summary["recommendations"].append(f"🚀 {len(merge_candidates)} branches ready for immediate merge")
        if refactor_candidates:
            summary["recommendations"].append(f"🔧 {len(refactor_candidates)} branches need refactoring before merge")
        
        # Save summary report
        summary_file = self.reports_dir / f"integration_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        return str(summary_file)

    def print_analysis_summary(self, analysis: Dict):
        """Print formatted analysis summary."""
        print(f"\n📊 Branch Analysis: {analysis['branch']}")
        print("=" * 50)
        print(f"📈 Overall Score: {analysis['scores']['overall']}/100")
        print(f"🎯 Strategy: {analysis['strategy']}")
        print(f"⭐ Priority: {analysis['priority']}")
        print(f"\n📊 Detailed Scores:")
        print(f"  Code Quality: {analysis['scores']['code_quality']}/100")
        print(f"  Business Value: {analysis['scores']['business_value']}/100")
        print(f"  Technical Risk: {analysis['scores']['technical_risk']}/100")
        print(f"\n📋 Commit Data:")
        print(f"  Commits: {analysis['commit_data']['commit_count']}")
        print(f"  Files Changed: {analysis['commit_data']['files_changed']}")
        print(f"  Insertions: {analysis['commit_data']['insertions']}")
        print(f"  Deletions: {analysis['commit_data']['deletions']}")
        print(f"\n💡 Recommendations:")
        for rec in analysis['recommendations']:
            print(f"  {rec}")
        print()

def main():
    parser = argparse.ArgumentParser(description="Branch Integration Manager")
    parser.add_argument("--analyze-all", action="store_true", help="Analyze all remote branches")
    parser.add_argument("--branch", help="Analyze specific branch")
    parser.add_argument("--extract-features", help="Extract features from branch")
    parser.add_argument("--mark-stale", help="Mark branch as stale")
    parser.add_argument("--repo-path", default=".", help="Repository path")
    
    args = parser.parse_args()
    
    manager = BranchIntegrationManager(args.repo_path)
    
    if args.analyze_all:
        print("🚀 Analyzing all remote branches...")
        branches = manager.get_remote_branches()
        analyses = []
        
        for branch in branches:
            analysis = manager.analyze_branch(branch)
            analyses.append(analysis)
            manager.print_analysis_summary(analysis)
            manager.save_analysis_report(analysis)
        
        summary_file = manager.generate_summary_report(analyses)
        print(f"📋 Summary report saved: {summary_file}")
        
    elif args.branch:
        analysis = manager.analyze_branch(args.branch)
        manager.print_analysis_summary(analysis)
        report_file = manager.save_analysis_report(analysis)
        print(f"📄 Report saved: {report_file}")
        
    elif args.extract_features:
        print(f"📦 Feature extraction for {args.extract_features} not yet implemented")
        
    elif args.mark_stale:
        print(f"🗑️ Marking {args.mark_stale} as stale not yet implemented")
        
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
