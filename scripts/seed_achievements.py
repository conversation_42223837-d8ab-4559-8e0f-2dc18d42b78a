#!/usr/bin/env python3
"""Seed achievement system with comprehensive badges and milestones.

This script populates the database with a variety of achievements that users
can earn through study sessions, practice tests, goal completion, and consistency.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from sqlalchemy.orm import Session
from database import SessionLocal
from models.progress_tracking import Achievement
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_study_achievements(db: Session) -> list:
    """Create study-related achievements."""
    achievements = []
    
    # Study time achievements
    study_time_achievements = [
        {
            'title': 'First Steps',
            'description': 'Complete your first study session',
            'achievement_type': 'milestone',
            'criteria_type': 'study_sessions',
            'criteria_value': 1,
            'criteria_unit': 'sessions',
            'rarity': 'common',
            'points': 10,
            'category': 'study',
            'icon': '🎯'
        },
        {
            'title': 'Study Warrior',
            'description': 'Complete 10 study sessions',
            'achievement_type': 'milestone',
            'criteria_type': 'study_sessions',
            'criteria_value': 10,
            'criteria_unit': 'sessions',
            'rarity': 'common',
            'points': 50,
            'category': 'study',
            'icon': '⚔️'
        },
        {
            'title': 'Study Master',
            'description': 'Complete 50 study sessions',
            'achievement_type': 'milestone',
            'criteria_type': 'study_sessions',
            'criteria_value': 50,
            'criteria_unit': 'sessions',
            'rarity': 'uncommon',
            'points': 200,
            'category': 'study',
            'icon': '🏆'
        },
        {
            'title': 'Study Legend',
            'description': 'Complete 100 study sessions',
            'achievement_type': 'milestone',
            'criteria_type': 'study_sessions',
            'criteria_value': 100,
            'criteria_unit': 'sessions',
            'rarity': 'rare',
            'points': 500,
            'category': 'study',
            'icon': '👑'
        },
        {
            'title': 'Hour Power',
            'description': 'Study for 10 hours total',
            'achievement_type': 'milestone',
            'criteria_type': 'study_hours',
            'criteria_value': 10,
            'criteria_unit': 'hours',
            'rarity': 'common',
            'points': 25,
            'category': 'study',
            'icon': '⏰'
        },
        {
            'title': 'Century Scholar',
            'description': 'Study for 100 hours total',
            'achievement_type': 'milestone',
            'criteria_type': 'study_hours',
            'criteria_value': 100,
            'criteria_unit': 'hours',
            'rarity': 'uncommon',
            'points': 250,
            'category': 'study',
            'icon': '📚'
        },
        {
            'title': 'Marathon Learner',
            'description': 'Study for 500 hours total',
            'achievement_type': 'milestone',
            'criteria_type': 'study_hours',
            'criteria_value': 500,
            'criteria_unit': 'hours',
            'rarity': 'epic',
            'points': 1000,
            'category': 'study',
            'icon': '🏃‍♂️'
        }
    ]
    
    # Consistency achievements (streaks)
    consistency_achievements = [
        {
            'title': 'Getting Started',
            'description': 'Study for 3 consecutive days',
            'achievement_type': 'streak',
            'criteria_type': 'study_streak',
            'criteria_value': 3,
            'criteria_unit': 'days',
            'rarity': 'common',
            'points': 30,
            'category': 'consistency',
            'icon': '🔥'
        },
        {
            'title': 'Week Warrior',
            'description': 'Study for 7 consecutive days',
            'achievement_type': 'streak',
            'criteria_type': 'study_streak',
            'criteria_value': 7,
            'criteria_unit': 'days',
            'rarity': 'common',
            'points': 70,
            'category': 'consistency',
            'icon': '🔥'
        },
        {
            'title': 'Consistency Champion',
            'description': 'Study for 30 consecutive days',
            'achievement_type': 'streak',
            'criteria_type': 'study_streak',
            'criteria_value': 30,
            'criteria_unit': 'days',
            'rarity': 'uncommon',
            'points': 300,
            'category': 'consistency',
            'icon': '🔥'
        },
        {
            'title': 'Unstoppable Force',
            'description': 'Study for 100 consecutive days',
            'achievement_type': 'streak',
            'criteria_type': 'study_streak',
            'criteria_value': 100,
            'criteria_unit': 'days',
            'rarity': 'rare',
            'points': 1000,
            'category': 'consistency',
            'icon': '🔥'
        },
        {
            'title': 'Legendary Dedication',
            'description': 'Study for 365 consecutive days',
            'achievement_type': 'streak',
            'criteria_type': 'study_streak',
            'criteria_value': 365,
            'criteria_unit': 'days',
            'rarity': 'legendary',
            'points': 3650,
            'category': 'consistency',
            'icon': '🔥'
        }
    ]
    
    achievements.extend(study_time_achievements)
    achievements.extend(consistency_achievements)
    
    return achievements


def create_testing_achievements(db: Session) -> list:
    """Create practice test related achievements."""
    achievements = []
    
    # Practice test achievements
    testing_achievements = [
        {
            'title': 'Test Taker',
            'description': 'Complete your first practice test',
            'achievement_type': 'milestone',
            'criteria_type': 'practice_tests',
            'criteria_value': 1,
            'criteria_unit': 'tests',
            'rarity': 'common',
            'points': 15,
            'category': 'testing',
            'icon': '📝'
        },
        {
            'title': 'Practice Makes Perfect',
            'description': 'Complete 10 practice tests',
            'achievement_type': 'milestone',
            'criteria_type': 'practice_tests',
            'criteria_value': 10,
            'criteria_unit': 'tests',
            'rarity': 'common',
            'points': 100,
            'category': 'testing',
            'icon': '📋'
        },
        {
            'title': 'Test Master',
            'description': 'Complete 50 practice tests',
            'achievement_type': 'milestone',
            'criteria_type': 'practice_tests',
            'criteria_value': 50,
            'criteria_unit': 'tests',
            'rarity': 'uncommon',
            'points': 400,
            'category': 'testing',
            'icon': '🎓'
        },
        {
            'title': 'First Success',
            'description': 'Pass your first practice test',
            'achievement_type': 'milestone',
            'criteria_type': 'tests_passed',
            'criteria_value': 1,
            'criteria_unit': 'tests',
            'rarity': 'common',
            'points': 25,
            'category': 'testing',
            'icon': '✅'
        },
        {
            'title': 'High Achiever',
            'description': 'Score 90% or higher on a practice test',
            'achievement_type': 'milestone',
            'criteria_type': 'test_score',
            'criteria_value': 90,
            'criteria_unit': 'percentage',
            'rarity': 'uncommon',
            'points': 150,
            'category': 'testing',
            'icon': '⭐'
        },
        {
            'title': 'Perfect Score',
            'description': 'Score 100% on a practice test',
            'achievement_type': 'milestone',
            'criteria_type': 'test_score',
            'criteria_value': 100,
            'criteria_unit': 'percentage',
            'rarity': 'rare',
            'points': 500,
            'category': 'testing',
            'icon': '💯'
        },
        {
            'title': 'Improvement Streak',
            'description': 'Improve your score on 5 consecutive tests',
            'achievement_type': 'streak',
            'criteria_type': 'score_improvement',
            'criteria_value': 5,
            'criteria_unit': 'tests',
            'rarity': 'uncommon',
            'points': 200,
            'category': 'testing',
            'icon': '📈'
        }
    ]
    
    achievements.extend(testing_achievements)
    
    return achievements


def create_goal_achievements(db: Session) -> list:
    """Create goal-related achievements."""
    achievements = []
    
    # Goal achievements
    goal_achievements = [
        {
            'title': 'Goal Setter',
            'description': 'Create your first learning goal',
            'achievement_type': 'milestone',
            'criteria_type': 'goals_created',
            'criteria_value': 1,
            'criteria_unit': 'goals',
            'rarity': 'common',
            'points': 20,
            'category': 'goals',
            'icon': '🎯'
        },
        {
            'title': 'Goal Achiever',
            'description': 'Complete your first learning goal',
            'achievement_type': 'milestone',
            'criteria_type': 'goals_completed',
            'criteria_value': 1,
            'criteria_unit': 'goals',
            'rarity': 'common',
            'points': 50,
            'category': 'goals',
            'icon': '🏁'
        },
        {
            'title': 'Overachiever',
            'description': 'Complete 5 learning goals',
            'achievement_type': 'milestone',
            'criteria_type': 'goals_completed',
            'criteria_value': 5,
            'criteria_unit': 'goals',
            'rarity': 'uncommon',
            'points': 250,
            'category': 'goals',
            'icon': '🏆'
        },
        {
            'title': 'Goal Crusher',
            'description': 'Complete 10 learning goals',
            'achievement_type': 'milestone',
            'criteria_type': 'goals_completed',
            'criteria_value': 10,
            'criteria_unit': 'goals',
            'rarity': 'rare',
            'points': 500,
            'category': 'goals',
            'icon': '💪'
        }
    ]
    
    achievements.extend(goal_achievements)
    
    return achievements


def create_certification_achievements(db: Session) -> list:
    """Create certification-related achievements."""
    achievements = []
    
    # Certification achievements
    cert_achievements = [
        {
            'title': 'Certified Professional',
            'description': 'Earn your first certification',
            'achievement_type': 'certification',
            'criteria_type': 'certifications_earned',
            'criteria_value': 1,
            'criteria_unit': 'certifications',
            'rarity': 'uncommon',
            'points': 500,
            'category': 'mastery',
            'icon': '🏅'
        },
        {
            'title': 'Multi-Certified',
            'description': 'Earn 3 certifications',
            'achievement_type': 'certification',
            'criteria_type': 'certifications_earned',
            'criteria_value': 3,
            'criteria_unit': 'certifications',
            'rarity': 'rare',
            'points': 1500,
            'category': 'mastery',
            'icon': '🎖️'
        },
        {
            'title': 'Certification Master',
            'description': 'Earn 5 certifications',
            'achievement_type': 'certification',
            'criteria_type': 'certifications_earned',
            'criteria_value': 5,
            'criteria_unit': 'certifications',
            'rarity': 'epic',
            'points': 2500,
            'category': 'mastery',
            'icon': '👑'
        }
    ]
    
    achievements.extend(cert_achievements)
    
    return achievements


def create_special_achievements(db: Session) -> list:
    """Create special and fun achievements."""
    achievements = []
    
    # Special achievements
    special_achievements = [
        {
            'title': 'Night Owl',
            'description': 'Complete a study session after 10 PM',
            'achievement_type': 'badge',
            'criteria_type': 'late_night_study',
            'criteria_value': 1,
            'criteria_unit': 'sessions',
            'rarity': 'common',
            'points': 25,
            'category': 'special',
            'icon': '🦉'
        },
        {
            'title': 'Early Bird',
            'description': 'Complete a study session before 6 AM',
            'achievement_type': 'badge',
            'criteria_type': 'early_morning_study',
            'criteria_value': 1,
            'criteria_unit': 'sessions',
            'rarity': 'common',
            'points': 25,
            'category': 'special',
            'icon': '🐦'
        },
        {
            'title': 'Weekend Warrior',
            'description': 'Study on both Saturday and Sunday',
            'achievement_type': 'badge',
            'criteria_type': 'weekend_study',
            'criteria_value': 1,
            'criteria_unit': 'weekends',
            'rarity': 'common',
            'points': 30,
            'category': 'special',
            'icon': '⚔️'
        },
        {
            'title': 'Focus Master',
            'description': 'Complete a 2+ hour study session with 5/5 focus rating',
            'achievement_type': 'badge',
            'criteria_type': 'focused_long_session',
            'criteria_value': 1,
            'criteria_unit': 'sessions',
            'rarity': 'uncommon',
            'points': 100,
            'category': 'special',
            'icon': '🧘'
        },
        {
            'title': 'Speed Demon',
            'description': 'Complete a practice test in under 60% of the time limit',
            'achievement_type': 'badge',
            'criteria_type': 'fast_test_completion',
            'criteria_value': 1,
            'criteria_unit': 'tests',
            'rarity': 'uncommon',
            'points': 75,
            'category': 'special',
            'icon': '⚡'
        }
    ]
    
    achievements.extend(special_achievements)
    
    return achievements


def main():
    """Main function to seed achievement data."""
    logger.info("Starting achievement data seeding...")
    
    db = SessionLocal()
    try:
        # Create all achievement categories
        logger.info("Creating study achievements...")
        study_achievements = create_study_achievements(db)
        
        logger.info("Creating testing achievements...")
        testing_achievements = create_testing_achievements(db)
        
        logger.info("Creating goal achievements...")
        goal_achievements = create_goal_achievements(db)
        
        logger.info("Creating certification achievements...")
        cert_achievements = create_certification_achievements(db)
        
        logger.info("Creating special achievements...")
        special_achievements = create_special_achievements(db)
        
        # Combine all achievements
        all_achievements = (
            study_achievements + 
            testing_achievements + 
            goal_achievements + 
            cert_achievements + 
            special_achievements
        )
        
        # Create achievement objects and add to database
        for achievement_data in all_achievements:
            achievement = Achievement(
                user_id='template',  # Template achievements for all users
                **achievement_data
            )
            db.add(achievement)
        
        # Commit all changes
        db.commit()
        
        logger.info(f"Successfully seeded {len(all_achievements)} achievements")
        logger.info("Achievement data seeding completed successfully!")
        
    except Exception as e:
        logger.error(f"Error seeding achievement data: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
