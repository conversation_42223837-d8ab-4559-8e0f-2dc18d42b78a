#!/bin/bash

# CertPathFinder - Start Services for Traefik Environment
# This script starts all services configured for external Traefik management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Traefik network exists
if ! docker network ls | grep -q "traefik"; then
    print_warning "Traefik network not found. Creating it..."
    docker network create traefik
    print_success "Traefik network created"
fi

print_status "Starting CertPathFinder services for Traefik environment..."

# Set environment variables
export COMPOSE_PROJECT_NAME=certpathfinder
export COMPOSE_FILE=docker-compose.traefik.yml

# Build and start services
print_status "Building and starting services..."
docker-compose -f docker-compose.traefik.yml up --build -d

# Wait for services to be healthy
print_status "Waiting for services to be healthy..."

# Function to wait for service health
wait_for_service() {
    local service_name=$1
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be healthy..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f docker-compose.traefik.yml ps $service_name | grep -q "healthy\|Up"; then
            print_success "$service_name is healthy"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to become healthy within timeout"
    return 1
}

# Wait for core services
wait_for_service "db"
wait_for_service "redis"

# Run database migrations (commented out for now)
print_status "Skipping database migrations..."
# docker-compose -f docker-compose.traefik.yml exec -T api alembic upgrade head
print_success "Database migrations skipped"

# Seed initial data (commented out for now)
print_status "Skipping initial data seeding..."
# docker-compose -f docker-compose.traefik.yml exec -T api python scripts/seed_data.py
print_success "Initial data seeding skipped"

# Wait for application services
wait_for_service "api"
wait_for_service "frontend"

# Display service status
print_status "Service Status:"
docker-compose -f docker-compose.traefik.yml ps

# Display access URLs
print_success "CertPathFinder services are now running!"
echo ""
echo "🌐 Access URLs (via Traefik):"
echo "   📱 Frontend App:     https://app.certrats.localhost"
echo "   🔧 Admin Dashboard:  https://admin.certrats.localhost"
echo "   📚 Documentation:    https://docs.certrats.localhost"
echo "   🚀 API:              https://api.certrats.localhost"
echo "   📊 Grafana:          https://grafana.certrats.localhost"
echo "   📈 Prometheus:       https://prometheus.certrats.localhost"
echo ""
echo "🔧 Development Access:"
echo "   🗄️  PostgreSQL:       localhost:5432"
echo "   🔴 Redis:            localhost:6379"
echo ""
echo "📋 Default Credentials:"
echo "   🔧 Grafana:          admin / admin"
echo "   🗄️  PostgreSQL:       postgres / password"
echo ""

# Display logs command
echo "📝 To view logs:"
echo "   docker-compose -f docker-compose.traefik.yml logs -f [service_name]"
echo ""
echo "🛑 To stop services:"
echo "   docker-compose -f docker-compose.traefik.yml down"
echo ""

print_success "All services started successfully!"

# Optional: Run health checks
if [ "$1" = "--health-check" ]; then
    print_status "Running health checks..."
    
    # Check API health
    if curl -f -s https://api.certrats.localhost/health > /dev/null; then
        print_success "API health check passed"
    else
        print_warning "API health check failed"
    fi
    
    # Check frontend
    if curl -f -s https://app.certrats.localhost/health > /dev/null; then
        print_success "Frontend health check passed"
    else
        print_warning "Frontend health check failed"
    fi
fi

# Optional: Run tests
if [ "$1" = "--run-tests" ]; then
    print_status "Running test suite..."
    ./scripts/run-all-tests.sh
fi
