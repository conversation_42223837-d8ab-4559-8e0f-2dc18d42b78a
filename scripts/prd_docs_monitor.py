#!/usr/bin/env python3
"""
PRD Documentation Monitor

Monitors git commits and automatically updates Sphinx PRD documentation
based on commit messages and changes to agent-related files.

Usage:
    python scripts/prd_docs_monitor.py --check
    python scripts/prd_docs_monitor.py --update
    python scripts/prd_docs_monitor.py --daemon
"""

import os
import sys
import json
import time
import argparse
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

class PRDDocumentationMonitor:
    """Monitor git commits and update PRD documentation accordingly"""
    
    def __init__(self, repo_root: str = None):
        self.repo_root = Path(repo_root) if repo_root else Path(__file__).parent.parent
        self.docs_dir = self.repo_root / "docs" / "sphinx" / "prds"
        self.prd_dir = self.repo_root / ".prd"
        self.state_file = self.repo_root / ".prd_monitor_state.json"
        
        # Agent mapping
        self.agents = {
            "agent-1": {
                "name": "Core Platform Engine",
                "prd_file": "agent-1-core-platform-engine.md",
                "doc_file": "agent-1-core-platform-engine.rst",
                "keywords": ["core", "platform", "foundation", "api", "user", "auth", "certification"]
            },
            "agent-2": {
                "name": "AI Study Assistant", 
                "prd_file": "agent-2-ai-study-assistant.md",
                "doc_file": "agent-2-ai-study-assistant.rst",
                "keywords": ["ai", "ml", "study", "recommendation", "prediction", "assistant"]
            },
            "agent-3": {
                "name": "Enterprise & Analytics Engine",
                "prd_file": "agent-3-enterprise-analytics-engine.md", 
                "doc_file": "agent-3-enterprise-analytics-engine.rst",
                "keywords": ["enterprise", "analytics", "compliance", "team", "organization"]
            },
            "agent-4": {
                "name": "Career & Cost Intelligence",
                "prd_file": "agent-4-career-cost-intelligence.md",
                "doc_file": "agent-4-career-cost-intelligence.rst", 
                "keywords": ["career", "cost", "salary", "transition", "pathfinding"]
            },
            "agent-5": {
                "name": "Marketplace & Integration Hub",
                "prd_file": "agent-5-marketplace-integration-hub.md",
                "doc_file": "agent-5-marketplace-integration-hub.rst",
                "keywords": ["marketplace", "integration", "partnership", "api", "hub"]
            }
        }
        
    def load_state(self) -> Dict:
        """Load the last known state from file"""
        if self.state_file.exists():
            try:
                with open(self.state_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                pass
        return {"last_commit": None, "last_check": None}
    
    def save_state(self, state: Dict) -> None:
        """Save current state to file"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
        except IOError as e:
            print(f"Warning: Could not save state: {e}")
    
    def get_latest_commit(self) -> Optional[str]:
        """Get the latest commit hash"""
        try:
            result = subprocess.run(
                ["git", "rev-parse", "HEAD"],
                cwd=self.repo_root,
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            return None
    
    def get_commits_since(self, since_commit: str = None) -> List[Dict]:
        """Get commits since the specified commit"""
        cmd = ["git", "log", "--oneline", "--pretty=format:%H|%s|%an|%ad", "--date=iso"]
        
        if since_commit:
            cmd.append(f"{since_commit}..HEAD")
        else:
            cmd.extend(["-10"])  # Last 10 commits if no since_commit
            
        try:
            result = subprocess.run(
                cmd,
                cwd=self.repo_root,
                capture_output=True,
                text=True,
                check=True
            )
            
            commits = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    parts = line.split('|', 3)
                    if len(parts) == 4:
                        commits.append({
                            "hash": parts[0],
                            "message": parts[1],
                            "author": parts[2],
                            "date": parts[3]
                        })
            return commits
        except subprocess.CalledProcessError:
            return []
    
    def get_changed_files(self, commit_hash: str) -> List[str]:
        """Get files changed in a specific commit"""
        try:
            result = subprocess.run(
                ["git", "show", "--name-only", "--pretty=format:", commit_hash],
                cwd=self.repo_root,
                capture_output=True,
                text=True,
                check=True
            )
            return [f for f in result.stdout.strip().split('\n') if f]
        except subprocess.CalledProcessError:
            return []
    
    def analyze_commit_relevance(self, commit: Dict) -> List[str]:
        """Analyze which agents are affected by a commit"""
        affected_agents = []
        message = commit["message"].lower()
        
        # Get changed files
        changed_files = self.get_changed_files(commit["hash"])
        
        for agent_id, agent_info in self.agents.items():
            # Check if PRD file was directly modified
            if any(agent_info["prd_file"] in f for f in changed_files):
                affected_agents.append(agent_id)
                continue
                
            # Check commit message for agent keywords
            if any(keyword in message for keyword in agent_info["keywords"]):
                affected_agents.append(agent_id)
                continue
                
            # Check changed files for agent-related paths
            agent_paths = [
                f"api/v1/{agent_id.replace('-', '_')}",
                f"services/{agent_id.replace('-', '_')}",
                f"models/{agent_id.replace('-', '_')}",
                f"tests/test_{agent_id.replace('-', '_')}",
                f"schemas/{agent_id.replace('-', '_')}"
            ]
            
            if any(any(path in f for path in agent_paths) for f in changed_files):
                affected_agents.append(agent_id)
        
        return list(set(affected_agents))  # Remove duplicates
    
    def update_agent_documentation(self, agent_id: str, commits: List[Dict]) -> bool:
        """Update documentation for a specific agent based on commits"""
        agent_info = self.agents[agent_id]
        doc_file = self.docs_dir / agent_info["doc_file"]
        
        if not doc_file.exists():
            print(f"Warning: Documentation file {doc_file} does not exist")
            return False
        
        try:
            # Read current documentation
            with open(doc_file, 'r') as f:
                content = f.read()
            
            # Update status section with latest commit info
            latest_commit = commits[0] if commits else None
            if latest_commit:
                # Find and update the status line
                status_pattern = "**📊 Current Status**:"
                if status_pattern in content:
                    # Update the status section
                    new_status = self.generate_status_update(agent_id, latest_commit)
                    # This is a simplified update - in practice, you'd want more sophisticated parsing
                    content = self.update_status_section(content, new_status)
                
                # Write updated content
                with open(doc_file, 'w') as f:
                    f.write(content)
                
                print(f"Updated documentation for {agent_info['name']}")
                return True
                
        except IOError as e:
            print(f"Error updating documentation for {agent_id}: {e}")
            return False
        
        return False
    
    def generate_status_update(self, agent_id: str, commit: Dict) -> str:
        """Generate status update text based on commit"""
        agent_name = self.agents[agent_id]["name"]
        commit_short = commit["hash"][:7]
        
        # Determine status based on commit message
        message = commit["message"].lower()
        if "feat:" in message or "feature" in message:
            status = "🟢 Feature Development"
        elif "fix:" in message or "bug" in message:
            status = "🟡 Bug Fixes"
        elif "test:" in message or "testing" in message:
            status = "🔵 Testing Phase"
        elif "docs:" in message or "documentation" in message:
            status = "📚 Documentation Update"
        else:
            status = "🟡 In Development"
        
        return f"""
---

**📊 Current Status**: {status}  
**🔄 Last Updated**: Auto-updated based on commit ``{commit_short}`` ({commit["message"]})  
**📅 Next Milestone**: Continuing development based on latest changes"""
    
    def update_status_section(self, content: str, new_status: str) -> str:
        """Update the status section in the documentation"""
        # Find the last occurrence of "---" and replace everything after it
        lines = content.split('\n')
        last_separator_idx = -1
        
        for i in range(len(lines) - 1, -1, -1):
            if lines[i].strip() == "---":
                last_separator_idx = i
                break
        
        if last_separator_idx >= 0:
            # Replace everything after the last separator
            lines = lines[:last_separator_idx] + new_status.split('\n')
        else:
            # Append new status if no separator found
            lines.extend(new_status.split('\n'))
        
        return '\n'.join(lines)
    
    def check_for_updates(self) -> bool:
        """Check if there are new commits that require documentation updates"""
        state = self.load_state()
        current_commit = self.get_latest_commit()
        
        if not current_commit:
            print("Error: Could not get current commit")
            return False
        
        if state["last_commit"] == current_commit:
            print("No new commits since last check")
            return False
        
        # Get new commits
        commits = self.get_commits_since(state["last_commit"])
        
        if not commits:
            print("No new commits found")
            return False
        
        print(f"Found {len(commits)} new commits")
        
        # Analyze commits and update documentation
        updated_agents = set()
        
        for commit in commits:
            affected_agents = self.analyze_commit_relevance(commit)
            
            if affected_agents:
                print(f"Commit {commit['hash'][:7]}: {commit['message']}")
                print(f"  Affects agents: {', '.join(affected_agents)}")
                
                for agent_id in affected_agents:
                    if self.update_agent_documentation(agent_id, [commit]):
                        updated_agents.add(agent_id)
        
        # Update state
        state["last_commit"] = current_commit
        state["last_check"] = datetime.now().isoformat()
        self.save_state(state)
        
        if updated_agents:
            print(f"Updated documentation for agents: {', '.join(updated_agents)}")
            return True
        else:
            print("No documentation updates required")
            return False
    
    def run_daemon(self, interval: int = 300) -> None:
        """Run as daemon, checking for updates every interval seconds"""
        print(f"Starting PRD documentation monitor daemon (checking every {interval} seconds)")
        
        while True:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking for updates...")
                self.check_for_updates()
                time.sleep(interval)
            except KeyboardInterrupt:
                print("\nDaemon stopped by user")
                break
            except Exception as e:
                print(f"Error in daemon: {e}")
                time.sleep(interval)

def main():
    parser = argparse.ArgumentParser(description="PRD Documentation Monitor")
    parser.add_argument("--check", action="store_true", help="Check for updates once")
    parser.add_argument("--update", action="store_true", help="Force update all documentation")
    parser.add_argument("--daemon", action="store_true", help="Run as daemon")
    parser.add_argument("--interval", type=int, default=300, help="Daemon check interval in seconds")
    
    args = parser.parse_args()
    
    monitor = PRDDocumentationMonitor()
    
    if args.daemon:
        monitor.run_daemon(args.interval)
    elif args.check:
        monitor.check_for_updates()
    elif args.update:
        # Force update all agents with recent commits
        commits = monitor.get_commits_since()
        for agent_id in monitor.agents.keys():
            monitor.update_agent_documentation(agent_id, commits)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
