"""
<PERSON>ript to populate security jobs from the provided data file
"""
import os
import sys
import logging
from pathlib import Path

# Add parent directory to Python path
parent_dir = str(Path(__file__).parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from models.job_role import JobRole
from database import init_db, get_db
import traceback

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define security domains
SECURITY_DOMAINS = [
    "Defensive Security",
    "Asset Security",
    "Identity & Access Management",
    "Network Security", 
    "Offensive Security",
    "Security Engineering",
    "Security Operations",
    "Security Management",
    "Security Testing"
]

def clean_job_title(title: str) -> str:
    """Clean and normalize job title"""
    # Remove trailing numbers and whitespace
    while title and title[-1].isdigit():
        title = title[:-1]
    return title.strip()

def is_valid_job_title(title: str) -> bool:
    """Validate job title format and content"""
    # Skip entries that look like URLs, Reddit posts, or other invalid formats
    invalid_markers = [
        "http", "www.", "reddit", "accessed on",
        ".com", ".org", ".net",
        "?", ":", "...", "Based on"
    ]

    # Explicitly handle empty or None input
    if not title or not isinstance(title, str):
        return False

    cleaned_title = clean_job_title(title)
    return (
        len(cleaned_title) >= 3 and
        len(cleaned_title) <= 100 and
        not any(marker in cleaned_title.lower() for marker in invalid_markers) and
        not cleaned_title.startswith(("The", "A ", "An ")) and
        not cleaned_title.isupper() and  # Skip all uppercase titles
        not cleaned_title.islower()  # Skip all lowercase titles
    )

def parse_job_titles(content: str) -> list:
    """Parse job titles and domains from the content"""
    jobs = []
    current_domain = None
    lines = content.split('\n')

    logger.info("Starting to parse job titles...")
    total_lines = len(lines)
    parsed_count = 0

    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue

        # Check if line matches a known security domain
        if line in SECURITY_DOMAINS:
            current_domain = line
            logger.info(f"Processing domain: {current_domain}")
            continue

        # Skip header sections and informational text
        if (not current_domain or
            line.startswith(('Cybersecurity Job Titles', 'Based on', 'These titles'))):
            continue

        # Skip domain descriptions
        if any(marker in line for marker in [
            'focuses on', 'protects', 'This report', 'include:', 'such as'
        ]):
            continue

        # Process potential job title
        title = clean_job_title(line)
        if title and is_valid_job_title(line):
            logger.debug(f"Found valid job title: {title} in {current_domain}")
            jobs.append({
                'title': title,
                'domain': current_domain,
                'description': f"Position in {current_domain} domain focusing on specialized security tasks and responsibilities."
            })
            parsed_count += 1

            if parsed_count % 10 == 0:
                logger.info(f"Found {parsed_count} valid jobs so far...")

    logger.info(f"Parsing completed. Total valid jobs found: {len(jobs)}")
    return jobs

def populate_jobs():
    """Populate the database with security jobs"""
    logger.info("Initializing database...")
    init_db()
    db = next(get_db())

    try:
        # Add initial security job roles
        initial_jobs = [
            {
                'title': 'Security Analyst',
                'domain': 'Security Operations',
                'description': 'Analyze security threats and incidents'
            },
            {
                'title': 'Security Engineer',
                'domain': 'Security Engineering',
                'description': 'Design and implement security solutions'
            },
            {
                'title': 'Network Security Specialist',
                'domain': 'Network Security',
                'description': 'Secure network infrastructure'
            },
            {
                'title': 'Security Architect',
                'domain': 'Security Engineering',
                'description': 'Design security architecture'
            },
            {
                'title': 'Application Security Engineer',
                'domain': 'Security Engineering',
                'description': 'Secure application development'
            }
        ]

        logger.info("Adding initial job roles...")
        for job_data in initial_jobs:
            try:
                job = JobRole(
                    title=job_data['title'],
                    domain=job_data['domain'],
                    description=job_data['description']
                )
                db.add(job)
                logger.info(f"Added initial job: {job_data['title']}")
            except Exception as e:
                logger.error(f"Error adding initial job {job_data['title']}: {str(e)}")
                logger.error(traceback.format_exc())

        db.commit()
        logger.info("Initial jobs committed successfully")

        # Read the job titles file
        file_path = os.path.join(parent_dir, 'attached_assets', 
            'Pasted-Cybersecurity-Job-Titles-Based-on-an-exhaustive-search-of-job-boards-and-websites-like-Indeed-Linke-1740515734230.txt')
        logger.info(f"Reading job titles from: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse and add jobs from file
            jobs = parse_job_titles(content)
            logger.info(f"Found {len(jobs)} jobs in file")

            success_count = 0
            error_count = 0

            # Batch process jobs
            batch_size = 50
            for i in range(0, len(jobs), batch_size):
                batch = jobs[i:i + batch_size]
                for job_data in batch:
                    try:
                        job = JobRole(
                            title=job_data['title'],
                            domain=job_data['domain'],
                            description=job_data['description']
                        )
                        db.add(job)
                        success_count += 1
                    except Exception as e:
                        logger.error(f"Error adding job {job_data['title']}: {str(e)}")
                        logger.error(traceback.format_exc())
                        error_count += 1

                # Commit each batch
                try:
                    db.commit()
                    logger.info(f"Committed batch of {len(batch)} jobs. Total success: {success_count}")
                except Exception as e:
                    logger.error(f"Error committing batch: {str(e)}")
                    logger.error(traceback.format_exc())
                    db.rollback()

            logger.info(f"Successfully added {success_count} jobs with {error_count} errors")
            return success_count, error_count

        except FileNotFoundError:
            logger.error(f"Job titles file not found: {file_path}")
            return 0, 0

    except Exception as e:
        logger.error(f"Critical error during job seeding: {str(e)}")
        logger.error(traceback.format_exc())
        db.rollback()
        return 0, 0
    finally:
        db.close()

if __name__ == "__main__":
    success, errors = populate_jobs()
    logger.info(f"Job seeding completed. Added {success} jobs with {errors} errors")