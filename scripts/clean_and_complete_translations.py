#!/usr/bin/env python3
"""
Clean and Complete Translations Script for CertPathFinder

This script cleans up translation files and completes all missing translations.
"""

import os
import re
import subprocess
from pathlib import Path
from typing import Dict, List, Set
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TranslationCleaner:
    """Cleans and completes translations for CertPathFinder."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.translations_dir = self.project_root / 'translations'
        
        # Professional translations
        self.translations = {
            'es': {
                'Login with:': 'Iniciar sesión con:',
                'Select Language': 'Seleccionar idioma',
                'Visualization': 'Visualización',
                'Listings': 'Listados',
                'Study Time': 'Tiempo de estudio',
                'Cost Calculator': 'Calculadora de costos',
                'AI Career Path': 'Ruta profesional con IA',
                'CertRat CareerPath': 'Ruta profesional CertRat',
                'Admin': 'Administración',
                'FAQ': 'Preguntas frecuentes',
                'Certification Path Visualization': 'Visualización de rutas de certificación',
                'Visualization Filters': 'Filtros de visualización',
                'Focus Domain': 'Dominio de enfoque',
                'Select a domain to visualize its certification paths.': 'Seleccione un dominio para visualizar sus rutas de certificación.',
                'Select Certifications': 'Seleccionar certificaciones',
                'Choose the certifications you want to include in the cost calculation': 'Elija las certificaciones que desea incluir en el cálculo de costos',
                'Study Materials Cost ($)': 'Costo de materiales de estudio ($)',
                'Estimated cost of books, online courses, practice exams, etc.': 'Costo estimado de libros, cursos en línea, exámenes de práctica, etc.',
                'Select Currency': 'Seleccionar moneda',
                'Select your preferred currency': 'Seleccione su moneda preferida',
                '📚 Exam Retake Analysis': '📚 Análisis de repetición de examen',
                'Minimum Days Between Attempts': 'Días mínimos entre intentos',
                'Typical waiting period required between exam attempts': 'Período de espera típico requerido entre intentos de examen',
                'Retake Discount (%)': 'Descuento por repetición (%)',
                'Some certifications offer discounts on retakes': 'Algunas certificaciones ofrecen descuentos en repeticiones',
                'Maximum Attempts Considered': 'Intentos máximos considerados',
                'Number of potential attempts to include in cost analysis': 'Número de intentos potenciales a incluir en el análisis de costos',
                'Cost Breakdown': 'Desglose de costos',
                'Base Exam Fees': 'Tarifas base del examen',
                'Study Materials': 'Materiales de estudio',
                'Potential Retake Costs': 'Costos potenciales de repetición',
                'Total Investment': 'Inversión total',
                'Total cost including all certifications, materials, and potential retakes': 'Costo total incluyendo todas las certificaciones, materiales y posibles repeticiones',
                '### 📊 Cost Distribution': '### 📊 Distribución de costos',
                'Potential Retakes': 'Posibles repeticiones',
                '### 📋 Selected Certifications': '### 📋 Certificaciones seleccionadas',
                'Retake Cost Analysis': 'Análisis de costos de repetición',
                'Select certifications to see cost breakdown': 'Seleccione certificaciones para ver el desglose de costos',
                'Service offline, please try again later': 'Servicio fuera de línea, inténtelo más tarde',
                'An unexpected error occurred': 'Ocurrió un error inesperado',
                'error': 'error',
                'Please fill in all required fields': 'Complete todos los campos obligatorios',
                'Unable to generate PDF report': 'No se pudo generar el informe PDF',
                'Unable to save your progress': 'No se pudo guardar su progreso',
                'Unable to display visualization': 'No se pudo mostrar la visualización',
                'Welcome to CertRat CareerPath - your AI-powered certification advisor.': 'Bienvenido a CertRat CareerPath - su asesor de certificación impulsado por IA.',
                'Get personalized certification recommendations based on your experience, interests, and career goals.': 'Obtenga recomendaciones de certificación personalizadas basadas en su experiencia, intereses y objetivos profesionales.',
                'Certification Cost Breakdown': 'Desglose de costos de certificación',
                'Total Investment Summary': 'Resumen de inversión total'
            },
            'fr': {
                'Login with:': 'Se connecter avec :',
                'Select Language': 'Sélectionner la langue',
                'Visualization': 'Visualisation',
                'Listings': 'Listes',
                'Study Time': 'Temps d\'étude',
                'Cost Calculator': 'Calculateur de coûts',
                'AI Career Path': 'Parcours professionnel IA',
                'CertRat CareerPath': 'Parcours professionnel CertRat',
                'Admin': 'Administration',
                'FAQ': 'FAQ',
                'Certification Path Visualization': 'Visualisation des parcours de certification',
                'Visualization Filters': 'Filtres de visualisation',
                'Focus Domain': 'Domaine de focus',
                'Select a domain to visualize its certification paths.': 'Sélectionnez un domaine pour visualiser ses parcours de certification.',
                'Select Certifications': 'Sélectionner les certifications',
                'Choose the certifications you want to include in the cost calculation': 'Choisissez les certifications que vous souhaitez inclure dans le calcul des coûts',
                'Study Materials Cost ($)': 'Coût des matériaux d\'étude ($)',
                'Estimated cost of books, online courses, practice exams, etc.': 'Coût estimé des livres, cours en ligne, examens pratiques, etc.',
                'Select Currency': 'Sélectionner la devise',
                'Select your preferred currency': 'Sélectionnez votre devise préférée',
                '📚 Exam Retake Analysis': '📚 Analyse de reprise d\'examen',
                'Minimum Days Between Attempts': 'Jours minimum entre les tentatives',
                'Typical waiting period required between exam attempts': 'Période d\'attente typique requise entre les tentatives d\'examen',
                'Retake Discount (%)': 'Remise de reprise (%)',
                'Some certifications offer discounts on retakes': 'Certaines certifications offrent des remises sur les reprises',
                'Maximum Attempts Considered': 'Tentatives maximum considérées',
                'Number of potential attempts to include in cost analysis': 'Nombre de tentatives potentielles à inclure dans l\'analyse des coûts',
                'Cost Breakdown': 'Répartition des coûts',
                'Base Exam Fees': 'Frais d\'examen de base',
                'Study Materials': 'Matériel d\'étude',
                'Potential Retake Costs': 'Coûts potentiels de reprise',
                'Total Investment': 'Investissement total',
                'Total cost including all certifications, materials, and potential retakes': 'Coût total incluant toutes les certifications, matériaux et reprises potentielles',
                '### 📊 Cost Distribution': '### 📊 Répartition des coûts',
                'Potential Retakes': 'Reprises potentielles',
                '### 📋 Selected Certifications': '### 📋 Certifications sélectionnées',
                'Retake Cost Analysis': 'Analyse des coûts de reprise',
                'Select certifications to see cost breakdown': 'Sélectionnez les certifications pour voir la répartition des coûts',
                'Service offline, please try again later': 'Service hors ligne, veuillez réessayer plus tard',
                'An unexpected error occurred': 'Une erreur inattendue s\'est produite',
                'error': 'erreur',
                'Please fill in all required fields': 'Veuillez remplir tous les champs obligatoires',
                'Unable to generate PDF report': 'Impossible de générer le rapport PDF',
                'Unable to save your progress': 'Impossible de sauvegarder vos progrès',
                'Unable to display visualization': 'Impossible d\'afficher la visualisation',
                'Welcome to CertRat CareerPath - your AI-powered certification advisor.': 'Bienvenue sur CertRat CareerPath - votre conseiller en certification alimenté par l\'IA.',
                'Get personalized certification recommendations based on your experience, interests, and career goals.': 'Obtenez des recommandations de certification personnalisées basées sur votre expérience, vos intérêts et vos objectifs de carrière.',
                'Certification Cost Breakdown': 'Répartition des coûts de certification',
                'Total Investment Summary': 'Résumé de l\'investissement total'
            },
            'de': {
                'Login with:': 'Anmelden mit:',
                'Select Language': 'Sprache auswählen',
                'Visualization': 'Visualisierung',
                'Listings': 'Auflistungen',
                'Study Time': 'Lernzeit',
                'Cost Calculator': 'Kostenrechner',
                'AI Career Path': 'KI-Karriereweg',
                'CertRat CareerPath': 'CertRat Karriereweg',
                'Admin': 'Verwaltung',
                'FAQ': 'FAQ',
                'Certification Path Visualization': 'Zertifizierungsweg-Visualisierung',
                'Visualization Filters': 'Visualisierungsfilter',
                'Focus Domain': 'Fokusdomäne',
                'Select a domain to visualize its certification paths.': 'Wählen Sie eine Domäne aus, um ihre Zertifizierungswege zu visualisieren.',
                'Select Certifications': 'Zertifizierungen auswählen',
                'Choose the certifications you want to include in the cost calculation': 'Wählen Sie die Zertifizierungen aus, die Sie in die Kostenberechnung einbeziehen möchten',
                'Study Materials Cost ($)': 'Kosten für Lernmaterialien ($)',
                'Estimated cost of books, online courses, practice exams, etc.': 'Geschätzte Kosten für Bücher, Online-Kurse, Übungsprüfungen usw.',
                'Select Currency': 'Währung auswählen',
                'Select your preferred currency': 'Wählen Sie Ihre bevorzugte Währung',
                '📚 Exam Retake Analysis': '📚 Prüfungswiederholungsanalyse',
                'Minimum Days Between Attempts': 'Mindestanzahl Tage zwischen Versuchen',
                'Typical waiting period required between exam attempts': 'Typische Wartezeit zwischen Prüfungsversuchen',
                'Retake Discount (%)': 'Wiederholungsrabatt (%)',
                'Some certifications offer discounts on retakes': 'Einige Zertifizierungen bieten Rabatte auf Wiederholungen',
                'Maximum Attempts Considered': 'Maximale berücksichtigte Versuche',
                'Number of potential attempts to include in cost analysis': 'Anzahl potenzieller Versuche für die Kostenanalyse',
                'Cost Breakdown': 'Kostenaufschlüsselung',
                'Base Exam Fees': 'Grundprüfungsgebühren',
                'Study Materials': 'Lernmaterialien',
                'Potential Retake Costs': 'Potenzielle Wiederholungskosten',
                'Total Investment': 'Gesamtinvestition',
                'Total cost including all certifications, materials, and potential retakes': 'Gesamtkosten einschließlich aller Zertifizierungen, Materialien und potenzieller Wiederholungen',
                '### 📊 Cost Distribution': '### 📊 Kostenverteilung',
                'Potential Retakes': 'Potenzielle Wiederholungen',
                '### 📋 Selected Certifications': '### 📋 Ausgewählte Zertifizierungen',
                'Retake Cost Analysis': 'Wiederholungskostenanalyse',
                'Select certifications to see cost breakdown': 'Wählen Sie Zertifizierungen aus, um die Kostenaufschlüsselung zu sehen',
                'Service offline, please try again later': 'Service offline, bitte versuchen Sie es später erneut',
                'An unexpected error occurred': 'Ein unerwarteter Fehler ist aufgetreten',
                'error': 'Fehler',
                'Please fill in all required fields': 'Bitte füllen Sie alle Pflichtfelder aus',
                'Unable to generate PDF report': 'PDF-Bericht konnte nicht erstellt werden',
                'Unable to save your progress': 'Fortschritt konnte nicht gespeichert werden',
                'Unable to display visualization': 'Visualisierung konnte nicht angezeigt werden',
                'Welcome to CertRat CareerPath - your AI-powered certification advisor.': 'Willkommen bei CertRat CareerPath - Ihrem KI-gestützten Zertifizierungsberater.',
                'Get personalized certification recommendations based on your experience, interests, and career goals.': 'Erhalten Sie personalisierte Zertifizierungsempfehlungen basierend auf Ihrer Erfahrung, Ihren Interessen und Karrierezielen.',
                'Certification Cost Breakdown': 'Zertifizierungskostenaufschlüsselung',
                'Total Investment Summary': 'Zusammenfassung der Gesamtinvestition'
            }
        }
    
    def clean_po_file(self, language: str) -> bool:
        """Clean a .po file by removing duplicates and updating translations."""
        po_file = self.translations_dir / language / 'LC_MESSAGES' / 'messages.po'
        
        if not po_file.exists():
            logger.warning(f"PO file not found: {po_file}")
            return False
        
        # Read the template file to get the structure
        pot_file = self.translations_dir / 'messages.pot'
        if not pot_file.exists():
            logger.error("Template file messages.pot not found")
            return False
        
        # Read template
        with open(pot_file, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Create new content based on template
        new_content = template_content.replace('PROJECT VERSION', 'CertPathFinder 1.0')
        new_content = new_content.replace('ORGANIZATION', 'CertPathFinder Team')
        new_content = new_content.replace('EMAIL@ADDRESS', '<EMAIL>')
        new_content = new_content.replace('YEAR-MO-DA HO:MI+ZONE', '2025-06-07 13:55+0000')
        new_content = new_content.replace('FULL NAME <EMAIL@ADDRESS>', 'CertPathFinder Team <<EMAIL>>')
        new_content = new_content.replace('LANGUAGE <<EMAIL>>', f'{language.title()} Team')
        
        # Add language-specific headers
        if language == 'es':
            new_content = new_content.replace('LANGUAGE', 'es')
            new_content = new_content.replace('Language-Team: es Team', 'Language-Team: Spanish')
            new_content = new_content.replace('"MIME-Version: 1.0\\n"', '"Language: es\\n"\n"Plural-Forms: nplurals=2; plural=(n != 1);\\n"\n"MIME-Version: 1.0\\n"')
        elif language == 'fr':
            new_content = new_content.replace('LANGUAGE', 'fr')
            new_content = new_content.replace('Language-Team: fr Team', 'Language-Team: French')
            new_content = new_content.replace('"MIME-Version: 1.0\\n"', '"Language: fr\\n"\n"Plural-Forms: nplurals=2; plural=(n > 1);\\n"\n"MIME-Version: 1.0\\n"')
        elif language == 'de':
            new_content = new_content.replace('LANGUAGE', 'de')
            new_content = new_content.replace('Language-Team: de Team', 'Language-Team: German')
            new_content = new_content.replace('"MIME-Version: 1.0\\n"', '"Language: de\\n"\n"Plural-Forms: nplurals=2; plural=(n != 1);\\n"\n"MIME-Version: 1.0\\n"')
        
        # Apply translations
        if language in self.translations:
            for english_text, translated_text in self.translations[language].items():
                # Escape special characters for regex
                escaped_english = re.escape(english_text)
                
                # Pattern to match msgid followed by empty msgstr
                pattern = f'(msgid "{escaped_english}"\\s*\\n\\s*msgstr ")(")'
                replacement = f'\\1{translated_text}\\2'
                
                new_content = re.sub(pattern, replacement, new_content)
        
        # Write the cleaned content
        with open(po_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info(f"Cleaned and updated translations for {language}")
        return True
    
    def compile_translations(self) -> bool:
        """Compile all .po files to .mo files."""
        try:
            for language in self.translations.keys():
                po_file = self.translations_dir / language / 'LC_MESSAGES' / 'messages.po'
                mo_file = self.translations_dir / language / 'LC_MESSAGES' / 'messages.mo'
                
                if po_file.exists():
                    result = subprocess.run([
                        'msgfmt', str(po_file), '-o', str(mo_file)
                    ], capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        logger.info(f"Compiled translations for {language}")
                    else:
                        logger.error(f"Failed to compile {language}: {result.stderr}")
                        return False
                else:
                    logger.warning(f"PO file not found for {language}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error compiling translations: {e}")
            return False
    
    def process_all_languages(self) -> bool:
        """Process all languages."""
        success = True
        
        for language in self.translations.keys():
            if not self.clean_po_file(language):
                success = False
        
        if success:
            success = self.compile_translations()
        
        return success

def main():
    """Main function to clean and complete all translations."""
    cleaner = TranslationCleaner()
    
    print("🧹 Cleaning and Completing CertPathFinder Translations")
    print("=" * 55)
    
    if cleaner.process_all_languages():
        print("✅ All translations cleaned, completed, and compiled successfully!")
    else:
        print("❌ Some translations failed to process")

if __name__ == "__main__":
    main()
