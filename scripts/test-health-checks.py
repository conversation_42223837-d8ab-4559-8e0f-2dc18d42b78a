#!/usr/bin/env python3
"""
CertRats Health Check Testing Script

This script tests all health check endpoints and service availability
for the CertRats platform. It provides comprehensive validation of
service health, response times, and system status.

Features:
- Automated health check testing
- Service availability validation
- Response time measurement
- Detailed reporting and logging
- Integration with monitoring systems
- Support for different environments

Usage:
    python scripts/test-health-checks.py --environment development
    python scripts/test-health-checks.py --environment production --format json
    python scripts/test-health-checks.py --service api --verbose
"""

import argparse
import asyncio
import json
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.health_checker import HealthChecker, HealthCheckResult
from utils.service_urls import ServiceURLManager


class HealthCheckTester:
    """
    Comprehensive health check testing for CertRats platform.
    
    Provides automated testing of all service health endpoints,
    performance validation, and detailed reporting capabilities.
    """

    def __init__(self, environment: str = "development", verbose: bool = False):
        """
        Initialize the health check tester.

        Args:
            environment: Environment to test (development, staging, production)
            verbose: Enable verbose logging
        """
        self.environment = environment
        self.verbose = verbose
        self.url_manager = ServiceURLManager(environment)
        self.health_checker = HealthChecker(environment)
        self.test_results = []

    def log(self, message: str, level: str = "INFO"):
        """Log a message with timestamp."""
        if self.verbose or level in ["ERROR", "WARNING"]:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] {level}: {message}")

    async def test_individual_service(self, service_name: str) -> HealthCheckResult:
        """
        Test health check for a specific service.

        Args:
            service_name: Name of the service to test

        Returns:
            HealthCheckResult: Result of the health check
        """
        self.log(f"Testing health check for service: {service_name}")
        
        start_time = time.time()
        result = self.health_checker.check_service(service_name)
        end_time = time.time()

        # Add additional test metadata
        result.test_duration = end_time - start_time
        result.test_timestamp = time.time()

        if result.status == "healthy":
            self.log(f"✅ {service_name}: Healthy ({result.response_time:.0f}ms)")
        else:
            self.log(f"❌ {service_name}: {result.status} - {result.error_message}", "ERROR")

        return result

    async def test_all_services(self) -> List[HealthCheckResult]:
        """
        Test health checks for all configured services.

        Returns:
            List[HealthCheckResult]: Results for all services
        """
        self.log("Starting comprehensive health check testing...")
        
        services = self.url_manager.list_services()
        self.log(f"Testing {len(services)} services: {', '.join(services)}")

        results = []
        for service in services:
            try:
                result = await self.test_individual_service(service)
                results.append(result)
            except Exception as e:
                self.log(f"Failed to test {service}: {e}", "ERROR")
                # Create a failed result
                failed_result = HealthCheckResult(
                    service_name=service,
                    status="unhealthy",
                    response_time=0,
                    error_message=str(e)
                )
                results.append(failed_result)

        return results

    def test_service_dependencies(self) -> Dict[str, List[str]]:
        """
        Test service dependencies and connectivity.

        Returns:
            dict: Dependency test results
        """
        self.log("Testing service dependencies...")
        
        dependencies = {
            "api": ["database", "redis", "storage"],
            "frontend": ["api"],
            "celery_worker": ["database", "redis"],
            "celery_beat": ["database", "redis"],
            "flower": ["redis"],
            "prometheus": [],
            "grafana": ["prometheus"]
        }

        dependency_results = {}
        for service, deps in dependencies.items():
            dependency_results[service] = []
            for dep in deps:
                try:
                    result = self.health_checker.check_service(dep)
                    dependency_results[service].append({
                        "dependency": dep,
                        "status": result.status,
                        "response_time": result.response_time
                    })
                except Exception as e:
                    dependency_results[service].append({
                        "dependency": dep,
                        "status": "error",
                        "error": str(e)
                    })

        return dependency_results

    def test_performance_thresholds(self, results: List[HealthCheckResult]) -> Dict[str, bool]:
        """
        Test if services meet performance thresholds.

        Args:
            results: Health check results to analyze

        Returns:
            dict: Performance test results
        """
        self.log("Testing performance thresholds...")
        
        thresholds = {
            "api": 500,  # 500ms max response time
            "frontend": 1000,  # 1s max response time
            "database": 100,  # 100ms max response time
            "redis": 50,  # 50ms max response time
            "storage": 200,  # 200ms max response time
        }

        performance_results = {}
        for result in results:
            service = result.service_name
            if service in thresholds:
                threshold = thresholds[service]
                meets_threshold = result.response_time <= threshold
                performance_results[service] = {
                    "meets_threshold": meets_threshold,
                    "response_time": result.response_time,
                    "threshold": threshold,
                    "status": "pass" if meets_threshold else "fail"
                }
                
                if meets_threshold:
                    self.log(f"✅ {service}: Performance OK ({result.response_time:.0f}ms <= {threshold}ms)")
                else:
                    self.log(f"⚠️ {service}: Performance warning ({result.response_time:.0f}ms > {threshold}ms)", "WARNING")

        return performance_results

    def generate_report(self, results: List[HealthCheckResult], 
                       dependencies: Dict[str, List[str]], 
                       performance: Dict[str, bool]) -> Dict:
        """
        Generate a comprehensive test report.

        Args:
            results: Health check results
            dependencies: Dependency test results
            performance: Performance test results

        Returns:
            dict: Comprehensive test report
        """
        summary = self.health_checker.generate_summary(results)
        
        report = {
            "test_metadata": {
                "environment": self.environment,
                "timestamp": time.time(),
                "test_duration": sum(getattr(r, 'test_duration', 0) for r in results),
                "total_services": len(results)
            },
            "summary": summary,
            "service_results": [result.to_dict() for result in results],
            "dependency_tests": dependencies,
            "performance_tests": performance,
            "recommendations": self._generate_recommendations(results, performance)
        }

        return report

    def _generate_recommendations(self, results: List[HealthCheckResult], 
                                performance: Dict[str, bool]) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []

        # Check for unhealthy services
        unhealthy_services = [r.service_name for r in results if r.status == "unhealthy"]
        if unhealthy_services:
            recommendations.append(f"Investigate unhealthy services: {', '.join(unhealthy_services)}")

        # Check for performance issues
        slow_services = [s for s, data in performance.items() if not data.get("meets_threshold", True)]
        if slow_services:
            recommendations.append(f"Optimize performance for slow services: {', '.join(slow_services)}")

        # Check overall health percentage
        health_percentage = sum(1 for r in results if r.status == "healthy") / len(results) * 100
        if health_percentage < 90:
            recommendations.append(f"Overall health is {health_percentage:.1f}% - investigate system issues")

        # Check for high response times
        high_response_times = [r for r in results if r.response_time > 1000]
        if high_response_times:
            services = [r.service_name for r in high_response_times]
            recommendations.append(f"Services with high response times (>1s): {', '.join(services)}")

        if not recommendations:
            recommendations.append("All services are healthy and performing well!")

        return recommendations

    def print_report(self, report: Dict, format_type: str = "human"):
        """
        Print the test report in the specified format.

        Args:
            report: Test report data
            format_type: Output format (human, json, prometheus)
        """
        if format_type == "json":
            print(json.dumps(report, indent=2))
        elif format_type == "prometheus":
            self._print_prometheus_format(report)
        else:
            self._print_human_format(report)

    def _print_human_format(self, report: Dict):
        """Print report in human-readable format."""
        print("\n🏥 CertRats Health Check Test Report")
        print("=" * 50)
        
        metadata = report["test_metadata"]
        print(f"Environment: {metadata['environment']}")
        print(f"Test Duration: {metadata['test_duration']:.2f}s")
        print(f"Total Services: {metadata['total_services']}")
        
        summary = report["summary"]
        print(f"\n📊 Summary:")
        print(f"   Healthy: {summary['healthy_services']}")
        print(f"   Unhealthy: {summary['unhealthy_services']}")
        print(f"   Unknown: {summary['unknown_services']}")
        print(f"   Health Percentage: {summary['health_percentage']:.1f}%")
        print(f"   Average Response Time: {summary['average_response_time_ms']:.0f}ms")
        
        print(f"\n🔍 Service Details:")
        for result in report["service_results"]:
            status_icon = "✅" if result["status"] == "healthy" else "❌"
            print(f"   {status_icon} {result['service_name']}: {result['status']} ({result['response_time_ms']:.0f}ms)")
        
        print(f"\n💡 Recommendations:")
        for rec in report["recommendations"]:
            print(f"   • {rec}")

    def _print_prometheus_format(self, report: Dict):
        """Print report in Prometheus metrics format."""
        print("# HELP certrats_health_test_result Health check test results")
        print("# TYPE certrats_health_test_result gauge")
        
        for result in report["service_results"]:
            value = 1 if result["status"] == "healthy" else 0
            print(f'certrats_health_test_result{{service="{result["service_name"]}",environment="{self.environment}"}} {value}')
        
        print("\n# HELP certrats_health_test_response_time Health check response times")
        print("# TYPE certrats_health_test_response_time gauge")
        
        for result in report["service_results"]:
            print(f'certrats_health_test_response_time{{service="{result["service_name"]}",environment="{self.environment}"}} {result["response_time_ms"]}')


async def main():
    """Main function for the health check tester."""
    parser = argparse.ArgumentParser(description="CertRats Health Check Tester")
    parser.add_argument("--environment", "-e", default="development",
                       help="Environment to test (development, staging, production)")
    parser.add_argument("--service", "-s", help="Test specific service only")
    parser.add_argument("--format", "-f", choices=["human", "json", "prometheus"], 
                       default="human", help="Output format")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--timeout", "-t", type=int, default=10, help="Request timeout")

    args = parser.parse_args()

    # Create tester instance
    tester = HealthCheckTester(environment=args.environment, verbose=args.verbose)

    try:
        if args.service:
            # Test specific service
            result = await tester.test_individual_service(args.service)
            results = [result]
            dependencies = {}
            performance = {}
        else:
            # Test all services
            results = await tester.test_all_services()
            dependencies = tester.test_service_dependencies()
            performance = tester.test_performance_thresholds(results)

        # Generate and print report
        report = tester.generate_report(results, dependencies, performance)
        tester.print_report(report, args.format)

        # Exit with appropriate code
        unhealthy_count = sum(1 for r in results if r.status == "unhealthy")
        sys.exit(1 if unhealthy_count > 0 else 0)

    except Exception as e:
        print(f"❌ Health check testing failed: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
