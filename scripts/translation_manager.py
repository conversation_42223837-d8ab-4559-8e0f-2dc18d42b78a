#!/usr/bin/env python3
"""
Translation Manager for CertPathFinder

This script manages translations for the CertPathFinder project, providing:
- Translation completeness analysis
- Automatic translation generation using AI
- Translation validation and quality checks
- Batch translation updates
"""

import os
import re
import json
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any
import logging
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TranslationEntry:
    """Represents a single translation entry."""
    msgid: str
    msgstr: str
    locations: List[str]
    is_translated: bool
    
@dataclass
class LanguageStats:
    """Statistics for a language's translation completeness."""
    language: str
    total_entries: int
    translated_entries: int
    completion_percentage: float
    missing_translations: List[str]

class TranslationManager:
    """Manages translations for the CertPathFinder project."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.translations_dir = self.project_root / 'translations'
        self.languages = {
            'en': 'English',
            'es': 'Español', 
            'fr': 'Français',
            'de': 'Deutsch',
            'af': 'Afrikaans',
            'zu': 'isiZulu',
            'ro': 'Română',
            'pt': 'Português',
            'it': 'Italiano',
            'ja': '日本語',
            'ko': '한국어',
            'zh': '中文',
            'ar': 'العربية',
            'hi': 'हिन्दी',
            'ru': 'Русский'
        }
        
        # Professional translations for common terms
        self.base_translations = {
            'es': {
                'Login with:': 'Iniciar sesión con:',
                'Select Language': 'Seleccionar idioma',
                'Visualization': 'Visualización',
                'Listings': 'Listados',
                'Study Time': 'Tiempo de estudio',
                'Cost Calculator': 'Calculadora de costos',
                'AI Career Path': 'Ruta profesional con IA',
                'Admin': 'Administración',
                'FAQ': 'Preguntas frecuentes',
                'Certification Path Visualization': 'Visualización de rutas de certificación',
                'Select Certifications': 'Seleccionar certificaciones',
                'Study Materials Cost ($)': 'Costo de materiales de estudio ($)',
                'Select Currency': 'Seleccionar moneda',
                'Cost Breakdown': 'Desglose de costos',
                'Base Exam Fees': 'Tarifas base del examen',
                'Study Materials': 'Materiales de estudio',
                'Total Investment': 'Inversión total',
                'Service offline, please try again later': 'Servicio fuera de línea, inténtelo más tarde',
                'An unexpected error occurred': 'Ocurrió un error inesperado',
                'Please fill in all required fields': 'Complete todos los campos obligatorios',
                'Unable to generate PDF report': 'No se pudo generar el informe PDF',
                'Unable to save your progress': 'No se pudo guardar su progreso',
                'Certification Cost Breakdown': 'Desglose de costos de certificación',
                'Total Investment Summary': 'Resumen de inversión total'
            },
            'fr': {
                'Login with:': 'Se connecter avec :',
                'Select Language': 'Sélectionner la langue',
                'Visualization': 'Visualisation',
                'Listings': 'Listes',
                'Study Time': 'Temps d\'étude',
                'Cost Calculator': 'Calculateur de coûts',
                'AI Career Path': 'Parcours professionnel IA',
                'Admin': 'Administration',
                'FAQ': 'FAQ',
                'Certification Path Visualization': 'Visualisation des parcours de certification',
                'Select Certifications': 'Sélectionner les certifications',
                'Study Materials Cost ($)': 'Coût des matériaux d\'étude ($)',
                'Select Currency': 'Sélectionner la devise',
                'Cost Breakdown': 'Répartition des coûts',
                'Base Exam Fees': 'Frais d\'examen de base',
                'Study Materials': 'Matériel d\'étude',
                'Total Investment': 'Investissement total',
                'Service offline, please try again later': 'Service hors ligne, veuillez réessayer plus tard',
                'An unexpected error occurred': 'Une erreur inattendue s\'est produite',
                'Please fill in all required fields': 'Veuillez remplir tous les champs obligatoires',
                'Unable to generate PDF report': 'Impossible de générer le rapport PDF',
                'Unable to save your progress': 'Impossible de sauvegarder vos progrès',
                'Certification Cost Breakdown': 'Répartition des coûts de certification',
                'Total Investment Summary': 'Résumé de l\'investissement total'
            },
            'de': {
                'Login with:': 'Anmelden mit:',
                'Select Language': 'Sprache auswählen',
                'Visualization': 'Visualisierung',
                'Listings': 'Auflistungen',
                'Study Time': 'Lernzeit',
                'Cost Calculator': 'Kostenrechner',
                'AI Career Path': 'KI-Karriereweg',
                'Admin': 'Verwaltung',
                'FAQ': 'FAQ',
                'Certification Path Visualization': 'Zertifizierungsweg-Visualisierung',
                'Select Certifications': 'Zertifizierungen auswählen',
                'Study Materials Cost ($)': 'Kosten für Lernmaterialien ($)',
                'Select Currency': 'Währung auswählen',
                'Cost Breakdown': 'Kostenaufschlüsselung',
                'Base Exam Fees': 'Grundprüfungsgebühren',
                'Study Materials': 'Lernmaterialien',
                'Total Investment': 'Gesamtinvestition',
                'Service offline, please try again later': 'Service offline, bitte versuchen Sie es später erneut',
                'An unexpected error occurred': 'Ein unerwarteter Fehler ist aufgetreten',
                'Please fill in all required fields': 'Bitte füllen Sie alle Pflichtfelder aus',
                'Unable to generate PDF report': 'PDF-Bericht konnte nicht erstellt werden',
                'Unable to save your progress': 'Fortschritt konnte nicht gespeichert werden',
                'Certification Cost Breakdown': 'Zertifizierungskostenaufschlüsselung',
                'Total Investment Summary': 'Zusammenfassung der Gesamtinvestition'
            }
        }
    
    def parse_po_file(self, po_file_path: Path) -> List[TranslationEntry]:
        """Parse a .po file and extract translation entries."""
        entries = []
        
        if not po_file_path.exists():
            return entries
        
        with open(po_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Regular expressions to match msgid and msgstr
        msgid_pattern = r'msgid\s+"([^"]*(?:"[^"]*)*)"'
        msgstr_pattern = r'msgstr\s+"([^"]*(?:"[^"]*)*)"'
        location_pattern = r'#:\s*([^\n]+)'
        
        # Split content into blocks
        blocks = re.split(r'\n\s*\n', content)
        
        for block in blocks:
            if 'msgid' in block and 'msgstr' in block:
                # Extract locations
                locations = re.findall(location_pattern, block)
                
                # Extract msgid
                msgid_match = re.search(msgid_pattern, block, re.DOTALL)
                msgstr_match = re.search(msgstr_pattern, block, re.DOTALL)
                
                if msgid_match and msgstr_match:
                    msgid = msgid_match.group(1).replace('\\"', '"')
                    msgstr = msgstr_match.group(1).replace('\\"', '"')
                    
                    # Skip empty msgid (header)
                    if msgid:
                        entries.append(TranslationEntry(
                            msgid=msgid,
                            msgstr=msgstr,
                            locations=locations,
                            is_translated=bool(msgstr.strip())
                        ))
        
        return entries
    
    def analyze_language_completeness(self, language: str) -> LanguageStats:
        """Analyze translation completeness for a specific language."""
        po_file = self.translations_dir / language / 'LC_MESSAGES' / 'messages.po'
        entries = self.parse_po_file(po_file)
        
        total_entries = len(entries)
        translated_entries = sum(1 for entry in entries if entry.is_translated)
        completion_percentage = (translated_entries / total_entries * 100) if total_entries > 0 else 0
        
        missing_translations = [entry.msgid for entry in entries if not entry.is_translated]
        
        return LanguageStats(
            language=language,
            total_entries=total_entries,
            translated_entries=translated_entries,
            completion_percentage=completion_percentage,
            missing_translations=missing_translations
        )
    
    def generate_completeness_report(self) -> Dict[str, LanguageStats]:
        """Generate a completeness report for all languages."""
        report = {}
        
        for lang_code in self.languages.keys():
            if lang_code == 'en':  # Skip English as it's the source language
                continue
                
            stats = self.analyze_language_completeness(lang_code)
            report[lang_code] = stats
            
        return report
    
    def update_translations_with_base(self, language: str) -> bool:
        """Update translations using base translations."""
        if language not in self.base_translations:
            logger.warning(f"No base translations available for {language}")
            return False
        
        po_file = self.translations_dir / language / 'LC_MESSAGES' / 'messages.po'
        if not po_file.exists():
            logger.error(f"PO file not found: {po_file}")
            return False
        
        # Read the current file
        with open(po_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update translations
        base_trans = self.base_translations[language]
        updated_content = content
        
        for english_text, translated_text in base_trans.items():
            # Escape quotes for regex
            escaped_english = re.escape(english_text)
            
            # Pattern to match msgid followed by empty msgstr
            pattern = f'(msgid\\s+"{escaped_english}"\\s*\\n\\s*msgstr\\s+")(")'
            replacement = f'\\1{translated_text}\\2'
            
            updated_content = re.sub(pattern, replacement, updated_content)
        
        # Write back the updated content
        with open(po_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"Updated translations for {language}")
        return True
    
    def compile_translations(self, language: str = None) -> bool:
        """Compile .po files to .mo files."""
        try:
            if language:
                languages_to_compile = [language]
            else:
                languages_to_compile = [lang for lang in self.languages.keys() if lang != 'en']
            
            for lang in languages_to_compile:
                po_file = self.translations_dir / lang / 'LC_MESSAGES' / 'messages.po'
                mo_file = self.translations_dir / lang / 'LC_MESSAGES' / 'messages.mo'
                
                if po_file.exists():
                    result = subprocess.run([
                        'msgfmt', str(po_file), '-o', str(mo_file)
                    ], capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        logger.info(f"Compiled translations for {lang}")
                    else:
                        logger.error(f"Failed to compile {lang}: {result.stderr}")
                        return False
                else:
                    logger.warning(f"PO file not found for {lang}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error compiling translations: {e}")
            return False
    
    def create_missing_language_files(self, language: str) -> bool:
        """Create missing language directory and files."""
        lang_dir = self.translations_dir / language / 'LC_MESSAGES'
        lang_dir.mkdir(parents=True, exist_ok=True)
        
        po_file = lang_dir / 'messages.po'
        pot_file = self.translations_dir / 'messages.pot'
        
        if not po_file.exists() and pot_file.exists():
            # Copy template to create new language file
            subprocess.run([
                'msginit', '--input', str(pot_file), 
                '--output', str(po_file), '--locale', language, '--no-translator'
            ], capture_output=True)
            
            logger.info(f"Created language files for {language}")
            return True
        
        return po_file.exists()
    
    def generate_comprehensive_report(self) -> str:
        """Generate a comprehensive translation report."""
        report = self.generate_completeness_report()
        
        output = """
🌍 CertPathFinder Translation Completeness Report
===============================================

📊 Overall Statistics:
---------------------
"""
        
        total_languages = len(report)
        fully_complete = sum(1 for stats in report.values() if stats.completion_percentage == 100)
        avg_completion = sum(stats.completion_percentage for stats in report.values()) / total_languages if total_languages > 0 else 0
        
        output += f"Total Languages: {total_languages}\n"
        output += f"Fully Complete: {fully_complete}\n"
        output += f"Average Completion: {avg_completion:.1f}%\n\n"
        
        output += "📋 Language Details:\n"
        output += "-------------------\n"
        
        # Sort by completion percentage (descending)
        sorted_languages = sorted(report.items(), key=lambda x: x[1].completion_percentage, reverse=True)
        
        for lang_code, stats in sorted_languages:
            lang_name = self.languages.get(lang_code, lang_code)
            status_emoji = "✅" if stats.completion_percentage == 100 else "⚠️" if stats.completion_percentage >= 50 else "❌"
            
            output += f"{status_emoji} {lang_name} ({lang_code}): {stats.completion_percentage:.1f}% "
            output += f"({stats.translated_entries}/{stats.total_entries})\n"
            
            if stats.completion_percentage < 100:
                missing_count = len(stats.missing_translations)
                output += f"   Missing: {missing_count} translations\n"
        
        output += "\n💡 Recommendations:\n"
        output += "------------------\n"
        
        for lang_code, stats in sorted_languages:
            if stats.completion_percentage < 100:
                lang_name = self.languages.get(lang_code, lang_code)
                missing_count = len(stats.missing_translations)
                output += f"- Complete {missing_count} missing translations for {lang_name}\n"
        
        if fully_complete == total_languages:
            output += "🎉 All languages are 100% complete!\n"
        
        return output

def main():
    """Main function to run translation management."""
    manager = TranslationManager()
    
    print("🌍 CertPathFinder Translation Manager")
    print("=" * 40)
    
    # Generate and display report
    report = manager.generate_comprehensive_report()
    print(report)
    
    # Update translations with base translations
    print("\n🔄 Updating translations with base translations...")
    for lang in ['es', 'fr', 'de']:
        if manager.create_missing_language_files(lang):
            manager.update_translations_with_base(lang)
    
    # Compile translations
    print("\n📦 Compiling translations...")
    if manager.compile_translations():
        print("✅ All translations compiled successfully!")
    else:
        print("❌ Some translations failed to compile")
    
    # Generate final report
    print("\n" + manager.generate_comprehensive_report())

if __name__ == "__main__":
    main()
