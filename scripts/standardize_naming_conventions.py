#!/usr/bin/env python3
"""
Standardize naming conventions across the codebase.

This script enforces:
- Python: snake_case for functions, variables, modules
- Python: PascalCase for classes
- TypeScript: camelCase for functions, variables
- TypeScript: PascalCase for classes, interfaces, types
- File names: snake_case for Python, kebab-case for TypeScript
"""

import os
import re
import ast
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Set
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NamingConventionStandardizer:
    """Standardize naming conventions across the codebase."""
    
    def __init__(self, root_dir: str = "."):
        self.root_dir = Path(root_dir)
        self.issues_found = []
        self.fixes_applied = []
        
    def run_standardization(self) -> Dict[str, List[str]]:
        """Run complete naming convention standardization."""
        logger.info("🔧 Starting naming convention standardization...")
        
        # Check Python files
        self._standardize_python_files()
        
        # Check TypeScript files
        self._standardize_typescript_files()
        
        # Check file names
        self._standardize_file_names()
        
        # Generate report
        return self._generate_report()
    
    def _standardize_python_files(self):
        """Standardize Python naming conventions."""
        logger.info("📝 Checking Python files...")
        
        python_files = list(self.root_dir.rglob("*.py"))
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
                
            try:
                self._check_python_file(file_path)
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
    
    def _standardize_typescript_files(self):
        """Standardize TypeScript naming conventions."""
        logger.info("📝 Checking TypeScript files...")
        
        ts_files = list(self.root_dir.rglob("*.ts")) + list(self.root_dir.rglob("*.tsx"))
        for file_path in ts_files:
            if self._should_skip_file(file_path):
                continue
                
            try:
                self._check_typescript_file(file_path)
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
    
    def _check_python_file(self, file_path: Path):
        """Check naming conventions in a Python file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            tree = ast.parse(content)
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
            return
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                self._check_python_function_name(node.name, file_path, node.lineno)
            elif isinstance(node, ast.ClassDef):
                self._check_python_class_name(node.name, file_path, node.lineno)
            elif isinstance(node, ast.Name):
                if isinstance(node.ctx, ast.Store):
                    self._check_python_variable_name(node.id, file_path, getattr(node, 'lineno', 0))
    
    def _check_python_function_name(self, name: str, file_path: Path, line_no: int):
        """Check if Python function name follows snake_case."""
        if not re.match(r'^[a-z_][a-z0-9_]*$', name) and not name.startswith('__'):
            self.issues_found.append({
                'type': 'python_function_naming',
                'file': str(file_path),
                'line': line_no,
                'current': name,
                'expected': self._to_snake_case(name),
                'message': f"Function '{name}' should use snake_case"
            })
    
    def _check_python_class_name(self, name: str, file_path: Path, line_no: int):
        """Check if Python class name follows PascalCase."""
        if not re.match(r'^[A-Z][a-zA-Z0-9]*$', name):
            self.issues_found.append({
                'type': 'python_class_naming',
                'file': str(file_path),
                'line': line_no,
                'current': name,
                'expected': self._to_pascal_case(name),
                'message': f"Class '{name}' should use PascalCase"
            })
    
    def _check_python_variable_name(self, name: str, file_path: Path, line_no: int):
        """Check if Python variable name follows snake_case."""
        # Skip constants (ALL_CAPS), private variables, and common patterns
        if (name.isupper() or name.startswith('_') or
            name in ['Base', 'SessionLocal', 'TestingSessionLocal'] or  # SQLAlchemy conventions
            name.endswith('Type') or  # Type annotations
            name.startswith('Mock')):  # Test mocks
            return

        if not re.match(r'^[a-z_][a-z0-9_]*$', name):
            self.issues_found.append({
                'type': 'python_variable_naming',
                'file': str(file_path),
                'line': line_no,
                'current': name,
                'expected': self._to_snake_case(name),
                'message': f"Variable '{name}' should use snake_case"
            })
    
    def _check_typescript_file(self, file_path: Path):
        """Check naming conventions in a TypeScript file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check function names (camelCase) - but skip React components
        function_pattern = r'(?:function\s+|const\s+|let\s+|var\s+)([A-Z][a-zA-Z0-9]*)\s*[=:]'
        for match in re.finditer(function_pattern, content):
            name = match.group(1)
            # Skip React components (PascalCase is correct) and Context objects
            if (file_path.suffix == '.tsx' or
                name.endswith('Context') or
                name.endswith('Component') or
                'export default' in content[max(0, match.start()-50):match.end()+50]):
                continue

            line_no = content[:match.start()].count('\n') + 1
            self.issues_found.append({
                'type': 'typescript_function_naming',
                'file': str(file_path),
                'line': line_no,
                'current': name,
                'expected': self._to_camel_case(name),
                'message': f"Function '{name}' should use camelCase"
            })
        
        # Check interface/type names (PascalCase)
        interface_pattern = r'(?:interface|type)\s+([a-z][a-zA-Z0-9]*)'
        for match in re.finditer(interface_pattern, content):
            name = match.group(1)
            line_no = content[:match.start()].count('\n') + 1
            self.issues_found.append({
                'type': 'typescript_interface_naming',
                'file': str(file_path),
                'line': line_no,
                'current': name,
                'expected': self._to_pascal_case(name),
                'message': f"Interface/Type '{name}' should use PascalCase"
            })
    
    def _standardize_file_names(self):
        """Check and standardize file names."""
        logger.info("📁 Checking file names...")
        
        # Python files should use snake_case
        python_files = list(self.root_dir.rglob("*.py"))
        for file_path in python_files:
            if self._should_skip_file(file_path):
                continue
                
            name = file_path.stem
            if not re.match(r'^[a-z_][a-z0-9_]*$', name) and name != '__init__':
                expected = self._to_snake_case(name)
                self.issues_found.append({
                    'type': 'python_file_naming',
                    'file': str(file_path),
                    'line': 0,
                    'current': name,
                    'expected': expected,
                    'message': f"Python file '{name}' should use snake_case"
                })
        
        # TypeScript files should use kebab-case or PascalCase for components
        ts_files = list(self.root_dir.rglob("*.ts")) + list(self.root_dir.rglob("*.tsx"))
        for file_path in ts_files:
            if self._should_skip_file(file_path):
                continue

            name = file_path.stem
            # Skip common patterns that are correct
            if (name in ['index', 'App.test', 'setupTests', 'reportWebVitals'] or
                name.endswith('.config') or name.endswith('.spec') or name.endswith('.e2e') or
                name.startswith('use')):  # React hooks
                continue

            # React components can use PascalCase, others should use kebab-case
            if file_path.suffix == '.tsx':
                if not re.match(r'^[A-Z][a-zA-Z0-9]*$', name) and name != 'index':
                    expected = self._to_pascal_case(name)
                    self.issues_found.append({
                        'type': 'react_component_naming',
                        'file': str(file_path),
                        'line': 0,
                        'current': name,
                        'expected': expected,
                        'message': f"React component '{name}' should use PascalCase"
                    })
            else:
                if not re.match(r'^[a-z][a-z0-9-]*$', name):
                    expected = self._to_kebab_case(name)
                    self.issues_found.append({
                        'type': 'typescript_file_naming',
                        'file': str(file_path),
                        'line': 0,
                        'current': name,
                        'expected': expected,
                        'message': f"TypeScript file '{name}' should use kebab-case"
                    })
    
    def _should_skip_file(self, file_path: Path) -> bool:
        """Check if file should be skipped."""
        skip_patterns = [
            'node_modules', '.git', '__pycache__', '.pytest_cache',
            'venv', 'env', '.venv', 'migrations', 'alembic',
            'build', 'dist', '.next', 'coverage'
        ]
        
        return any(pattern in str(file_path) for pattern in skip_patterns)
    
    def _to_snake_case(self, name: str) -> str:
        """Convert name to snake_case."""
        # Handle camelCase and PascalCase
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    def _to_camel_case(self, name: str) -> str:
        """Convert name to camelCase."""
        components = name.split('_')
        return components[0].lower() + ''.join(word.capitalize() for word in components[1:])
    
    def _to_pascal_case(self, name: str) -> str:
        """Convert name to PascalCase."""
        return ''.join(word.capitalize() for word in name.split('_'))
    
    def _to_kebab_case(self, name: str) -> str:
        """Convert name to kebab-case."""
        # Handle camelCase and PascalCase
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1-\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1-\2', s1).lower()
    
    def _generate_report(self) -> Dict[str, List[str]]:
        """Generate standardization report."""
        report = {
            'issues_found': len(self.issues_found),
            'fixes_applied': len(self.fixes_applied),
            'summary': {}
        }
        
        # Group issues by type
        for issue in self.issues_found:
            issue_type = issue['type']
            if issue_type not in report['summary']:
                report['summary'][issue_type] = 0
            report['summary'][issue_type] += 1
        
        # Save detailed report
        with open('naming_convention_report.json', 'w') as f:
            json.dump({
                'issues': self.issues_found,
                'fixes': self.fixes_applied,
                'summary': report
            }, f, indent=2)
        
        logger.info(f"📊 Found {len(self.issues_found)} naming convention issues")
        logger.info(f"📊 Applied {len(self.fixes_applied)} fixes")
        logger.info("📄 Detailed report saved to naming_convention_report.json")
        
        return report

def main():
    """Main function."""
    standardizer = NamingConventionStandardizer()
    report = standardizer.run_standardization()
    
    if report['issues_found'] > 0:
        logger.warning(f"⚠️  Found {report['issues_found']} naming convention issues")
        logger.info("📄 Check naming_convention_report.json for details")
        return 1
    else:
        logger.info("✅ All naming conventions are standardized!")
        return 0

if __name__ == "__main__":
    exit(main())
