#!/usr/bin/env python3
"""
Database optimization script for CertPathFinder.

This script creates indexes, analyzes query performance, and optimizes
database settings for better performance.
"""

import logging
import time
from sqlalchemy import text, Index, inspect
from sqlalchemy.orm import Session
from database import engine, SessionLocal
from models.certification import Certification
from models.job import Job
from models.user_experience import UserExperience
from models.progress_tracking import ProgressTracking
from models.study_session import StudySession

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    """Database optimization utilities."""
    
    def __init__(self):
        self.session = SessionLocal()
        self.inspector = inspect(engine)
    
    def create_performance_indexes(self):
        """Create indexes for better query performance."""
        logger.info("Creating performance indexes...")
        
        indexes_to_create = [
            # Certification indexes
            Index('idx_certification_category', Certification.category),
            Index('idx_certification_difficulty', Certification.difficulty_level),
            Index('idx_certification_provider', Certification.provider),
            Index('idx_certification_active', Certification.is_active),
            Index('idx_certification_search', Certification.name, Certification.description),
            
            # Job indexes
            Index('idx_job_title', Job.title),
            Index('idx_job_location', Job.location),
            Index('idx_job_type', Job.job_type),
            Index('idx_job_salary', Job.salary_min, Job.salary_max),
            Index('idx_job_posted_date', Job.posted_date),
            Index('idx_job_active', Job.is_active),
            
            # User experience indexes
            Index('idx_user_experience_user', UserExperience.user_id),
            Index('idx_user_experience_cert', UserExperience.certification_id),
            Index('idx_user_experience_status', UserExperience.status),
            Index('idx_user_experience_created', UserExperience.created_at),
            
            # Progress tracking indexes
            Index('idx_progress_user', ProgressTracking.user_id),
            Index('idx_progress_cert', ProgressTracking.certification_id),
            Index('idx_progress_status', ProgressTracking.status),
            Index('idx_progress_updated', ProgressTracking.updated_at),
            
            # Study session indexes
            Index('idx_study_session_user', StudySession.user_id),
            Index('idx_study_session_subject', StudySession.subject),
            Index('idx_study_session_date', StudySession.completed_at),
            Index('idx_study_session_type', StudySession.session_type),
            
            # Composite indexes for common queries
            Index('idx_cert_category_difficulty', Certification.category, Certification.difficulty_level),
            Index('idx_job_type_location', Job.job_type, Job.location),
            Index('idx_user_cert_status', UserExperience.user_id, UserExperience.certification_id, UserExperience.status),
            Index('idx_progress_user_status', ProgressTracking.user_id, ProgressTracking.status),
        ]
        
        created_count = 0
        for index in indexes_to_create:
            try:
                # Check if index already exists
                existing_indexes = self.inspector.get_indexes(index.table.name)
                index_exists = any(
                    existing_idx['name'] == index.name 
                    for existing_idx in existing_indexes
                )
                
                if not index_exists:
                    index.create(engine)
                    logger.info(f"Created index: {index.name}")
                    created_count += 1
                else:
                    logger.debug(f"Index already exists: {index.name}")
                    
            except Exception as e:
                logger.error(f"Failed to create index {index.name}: {e}")
        
        logger.info(f"Created {created_count} new indexes")
    
    def analyze_query_performance(self):
        """Analyze and log slow queries."""
        logger.info("Analyzing query performance...")
        
        # Test common queries and measure performance
        test_queries = [
            ("Certification search by category", 
             "SELECT * FROM certifications WHERE category = 'Security' LIMIT 10"),
            
            ("Job search by location", 
             "SELECT * FROM jobs WHERE location LIKE '%New York%' LIMIT 10"),
            
            ("User progress tracking", 
             "SELECT * FROM progress_tracking WHERE user_id = 1 ORDER BY updated_at DESC LIMIT 10"),
            
            ("Study sessions by user", 
             "SELECT * FROM study_sessions WHERE user_id = 1 ORDER BY completed_at DESC LIMIT 10"),
            
            ("Complex join query", 
             """SELECT c.name, ue.status, pt.progress_percentage 
                FROM certifications c 
                JOIN user_experiences ue ON c.id = ue.certification_id 
                LEFT JOIN progress_tracking pt ON c.id = pt.certification_id 
                WHERE ue.user_id = 1 LIMIT 10"""),
        ]
        
        for query_name, query_sql in test_queries:
            start_time = time.time()
            try:
                result = self.session.execute(text(query_sql))
                rows = result.fetchall()
                execution_time = time.time() - start_time
                
                logger.info(f"{query_name}: {execution_time:.4f}s ({len(rows)} rows)")
                
                if execution_time > 1.0:  # Log slow queries
                    logger.warning(f"Slow query detected: {query_name} took {execution_time:.4f}s")
                    
            except Exception as e:
                logger.error(f"Query failed - {query_name}: {e}")
    
    def optimize_database_settings(self):
        """Apply database-specific optimizations."""
        logger.info("Applying database optimizations...")
        
        if engine.url.drivername == 'sqlite':
            self._optimize_sqlite()
        elif engine.url.drivername.startswith('postgresql'):
            self._optimize_postgresql()
    
    def _optimize_sqlite(self):
        """Apply SQLite-specific optimizations."""
        logger.info("Applying SQLite optimizations...")
        
        optimizations = [
            "PRAGMA optimize",  # Analyze and optimize
            "PRAGMA wal_checkpoint(TRUNCATE)",  # Checkpoint WAL file
            "VACUUM",  # Rebuild database file
            "ANALYZE",  # Update query planner statistics
        ]
        
        for optimization in optimizations:
            try:
                self.session.execute(text(optimization))
                self.session.commit()
                logger.info(f"Applied: {optimization}")
            except Exception as e:
                logger.error(f"Failed to apply {optimization}: {e}")
    
    def _optimize_postgresql(self):
        """Apply PostgreSQL-specific optimizations."""
        logger.info("Applying PostgreSQL optimizations...")
        
        optimizations = [
            "VACUUM ANALYZE",  # Update statistics and reclaim space
            "REINDEX DATABASE certpathfinder",  # Rebuild indexes
        ]
        
        for optimization in optimizations:
            try:
                self.session.execute(text(optimization))
                self.session.commit()
                logger.info(f"Applied: {optimization}")
            except Exception as e:
                logger.error(f"Failed to apply {optimization}: {e}")
    
    def generate_performance_report(self):
        """Generate a performance analysis report."""
        logger.info("Generating performance report...")
        
        report = {
            "database_type": engine.url.drivername,
            "connection_pool_size": getattr(engine.pool, 'size', 'N/A'),
            "connection_pool_overflow": getattr(engine.pool, 'overflow', 'N/A'),
            "indexes": {},
            "table_stats": {}
        }
        
        # Get index information
        for table_name in ['certifications', 'jobs', 'user_experiences', 'progress_tracking', 'study_sessions']:
            try:
                indexes = self.inspector.get_indexes(table_name)
                report["indexes"][table_name] = len(indexes)
            except Exception as e:
                logger.error(f"Failed to get indexes for {table_name}: {e}")
                report["indexes"][table_name] = "Error"
        
        # Get table statistics
        for table_name in ['certifications', 'jobs', 'user_experiences', 'progress_tracking', 'study_sessions']:
            try:
                result = self.session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                report["table_stats"][table_name] = count
            except Exception as e:
                logger.error(f"Failed to get stats for {table_name}: {e}")
                report["table_stats"][table_name] = "Error"
        
        # Log report
        logger.info("=== Performance Report ===")
        logger.info(f"Database Type: {report['database_type']}")
        logger.info(f"Pool Size: {report['connection_pool_size']}")
        logger.info(f"Pool Overflow: {report['connection_pool_overflow']}")
        
        logger.info("Index Counts:")
        for table, count in report["indexes"].items():
            logger.info(f"  {table}: {count}")
        
        logger.info("Table Row Counts:")
        for table, count in report["table_stats"].items():
            logger.info(f"  {table}: {count}")
        
        return report
    
    def run_full_optimization(self):
        """Run complete database optimization."""
        logger.info("Starting full database optimization...")
        
        start_time = time.time()
        
        try:
            # Create performance indexes
            self.create_performance_indexes()
            
            # Optimize database settings
            self.optimize_database_settings()
            
            # Analyze query performance
            self.analyze_query_performance()
            
            # Generate performance report
            self.generate_performance_report()
            
            total_time = time.time() - start_time
            logger.info(f"Database optimization completed in {total_time:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
            raise
        finally:
            self.session.close()

def main():
    """Main function."""
    optimizer = DatabaseOptimizer()
    optimizer.run_full_optimization()

if __name__ == "__main__":
    main()
