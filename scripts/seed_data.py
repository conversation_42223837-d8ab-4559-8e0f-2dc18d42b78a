#!/usr/bin/env python3
"""
CertPathFinder - Database Seeding Script
Seeds the database with initial data for development and testing.
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database import get_db_session
from models.security_domain import SecurityDomain
from models.certification import Certification, Organization
from models.job import SecurityJob
from data.seed import seed_certifications, seed_security_domains, seed_organizations

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def seed_security_domains_data(session):
    """Seed security domains."""
    logger.info("Seeding security domains...")
    
    domains = [
        "Network Security",
        "Application Security", 
        "Cloud Security",
        "Identity and Access Management",
        "Incident Response",
        "Risk Management",
        "Compliance and Governance",
        "Cryptography",
        "Penetration Testing",
        "Security Operations"
    ]
    
    for domain_name in domains:
        existing = session.query(SecurityDomain).filter_by(name=domain_name).first()
        if not existing:
            domain = SecurityDomain(name=domain_name)
            session.add(domain)
            logger.info(f"Added security domain: {domain_name}")
    
    session.commit()
    logger.info("Security domains seeded successfully")

def seed_organizations_data(session):
    """Seed certification organizations."""
    logger.info("Seeding organizations...")
    
    organizations = [
        {"name": "CompTIA", "website": "https://www.comptia.org"},
        {"name": "(ISC)²", "website": "https://www.isc2.org"},
        {"name": "EC-Council", "website": "https://www.eccouncil.org"},
        {"name": "SANS", "website": "https://www.sans.org"},
        {"name": "Cisco", "website": "https://www.cisco.com"},
        {"name": "Microsoft", "website": "https://www.microsoft.com"},
        {"name": "Amazon Web Services", "website": "https://aws.amazon.com"},
        {"name": "Google Cloud", "website": "https://cloud.google.com"},
        {"name": "ISACA", "website": "https://www.isaca.org"},
        {"name": "PMI", "website": "https://www.pmi.org"}
    ]
    
    for org_data in organizations:
        existing = session.query(Organization).filter_by(name=org_data["name"]).first()
        if not existing:
            org = Organization(
                name=org_data["name"],
                website=org_data["website"]
            )
            session.add(org)
            logger.info(f"Added organization: {org_data['name']}")
    
    session.commit()
    logger.info("Organizations seeded successfully")

def seed_sample_certifications(session):
    """Seed sample certifications."""
    logger.info("Seeding sample certifications...")
    
    # Get organizations
    comptia = session.query(Organization).filter_by(name="CompTIA").first()
    isc2 = session.query(Organization).filter_by(name="(ISC)²").first()
    
    if not comptia or not isc2:
        logger.warning("Required organizations not found, skipping certification seeding")
        return
    
    certifications = [
        {
            "name": "Security+",
            "category": "Entry Level",
            "domain": "Network Security",
            "level": "Associate",
            "difficulty": 2,
            "cost": 370.0,
            "currency": "USD",
            "organization_id": comptia.id,
            "description": "Entry-level cybersecurity certification",
            "validity_period": 36,
            "is_active": True
        },
        {
            "name": "CISSP",
            "category": "Advanced",
            "domain": "Risk Management", 
            "level": "Expert",
            "difficulty": 4,
            "cost": 749.0,
            "currency": "USD",
            "organization_id": isc2.id,
            "description": "Advanced security management certification",
            "validity_period": 36,
            "is_active": True
        }
    ]
    
    for cert_data in certifications:
        existing = session.query(Certification).filter_by(
            name=cert_data["name"],
            organization_id=cert_data["organization_id"]
        ).first()
        
        if not existing:
            cert = Certification(**cert_data)
            session.add(cert)
            logger.info(f"Added certification: {cert_data['name']}")
    
    session.commit()
    logger.info("Sample certifications seeded successfully")

def seed_sample_jobs(session):
    """Seed sample security jobs."""
    logger.info("Seeding sample security jobs...")
    
    jobs = [
        {
            "title": "Security Analyst",
            "domain": "Security Operations",
            "experience_level": "Entry Level",
            "description": "Monitor and analyze security events",
            "salary_range_min": 50000,
            "salary_range_max": 75000
        },
        {
            "title": "Security Engineer", 
            "domain": "Network Security",
            "experience_level": "Mid Level",
            "description": "Design and implement security solutions",
            "salary_range_min": 75000,
            "salary_range_max": 110000
        },
        {
            "title": "Security Architect",
            "domain": "Risk Management",
            "experience_level": "Senior",
            "description": "Design enterprise security architecture",
            "salary_range_min": 120000,
            "salary_range_max": 180000
        }
    ]
    
    for job_data in jobs:
        existing = session.query(SecurityJob).filter_by(
            title=job_data["title"],
            domain=job_data["domain"]
        ).first()
        
        if not existing:
            job = SecurityJob(**job_data)
            session.add(job)
            logger.info(f"Added job: {job_data['title']}")
    
    session.commit()
    logger.info("Sample jobs seeded successfully")

def main():
    """Main seeding function."""
    logger.info("Starting database seeding...")
    
    try:
        # Get database session
        session = next(get_db_session())
        
        # Seed data in order
        seed_security_domains_data(session)
        seed_organizations_data(session)
        seed_sample_certifications(session)
        seed_sample_jobs(session)
        
        logger.info("Database seeding completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during seeding: {e}")
        if 'session' in locals():
            session.rollback()
        sys.exit(1)
    
    finally:
        if 'session' in locals():
            session.close()

if __name__ == "__main__":
    main()
