#!/bin/bash

# CertPathFinder - Comprehensive Test Runner
# Runs all test categories in parallel for maximum efficiency

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# Create test results directory
mkdir -p test-results

# Start time
start_time=$(date +%s)

print_status "🧪 Starting Comprehensive Test Suite for CertPathFinder"
print_status "⏰ Started at: $(date)"
echo ""

# Function to run tests in parallel
run_test_category() {
    local category=$1
    local command=$2
    local log_file="test-results/${category}.log"
    
    print_test "Starting $category tests..."
    
    # Run test and capture output
    if eval "$command" > "$log_file" 2>&1; then
        print_success "$category tests PASSED ✅"
        return 0
    else
        print_error "$category tests FAILED ❌"
        echo "  📄 Log: $log_file"
        return 1
    fi
}

# Array to store background process IDs
declare -a test_pids=()
declare -a test_categories=()
declare -a test_results=()

# Backend Tests
print_test "🐍 Starting Backend Test Suite..."

# Unit Tests
(
    print_test "Running Python unit tests..."
    python scripts/run_tests.py unit
) > test-results/backend-unit.log 2>&1 &
test_pids+=($!)
test_categories+=("Backend Unit Tests")

# Integration Tests
(
    print_test "Running Python integration tests..."
    python scripts/run_tests.py integration
) > test-results/backend-integration.log 2>&1 &
test_pids+=($!)
test_categories+=("Backend Integration Tests")

# API Tests
(
    print_test "Running API tests..."
    python scripts/run_tests.py api
) > test-results/backend-api.log 2>&1 &
test_pids+=($!)
test_categories+=("Backend API Tests")

# Security Tests
(
    print_test "Running security tests..."
    python scripts/run_tests.py security
) > test-results/backend-security.log 2>&1 &
test_pids+=($!)
test_categories+=("Backend Security Tests")

# Frontend Tests
print_test "⚛️ Starting Frontend Test Suite..."

# Frontend Unit Tests
(
    print_test "Running React unit tests..."
    cd frontend && npm test -- --coverage --watchAll=false --verbose
) > test-results/frontend-unit.log 2>&1 &
test_pids+=($!)
test_categories+=("Frontend Unit Tests")

# Frontend Component Tests
(
    print_test "Running React component tests..."
    cd frontend && npm test -- --coverage --watchAll=false --testPathPattern=components
) > test-results/frontend-components.log 2>&1 &
test_pids+=($!)
test_categories+=("Frontend Component Tests")

# E2E Tests
print_test "🎭 Starting E2E Test Suite..."

# Playwright E2E Tests
(
    print_test "Running Playwright E2E tests..."
    cd tests/e2e && npx playwright test --reporter=json --output-dir=../../test-results/e2e-artifacts
) > test-results/e2e-playwright.log 2>&1 &
test_pids+=($!)
test_categories+=("E2E Playwright Tests")

# Code Quality Tests
print_test "🔍 Starting Code Quality Checks..."

# Python Linting
(
    print_test "Running Python linting..."
    python scripts/run_tests.py lint
) > test-results/python-lint.log 2>&1 &
test_pids+=($!)
test_categories+=("Python Code Quality")

# TypeScript Linting
(
    print_test "Running TypeScript linting..."
    cd frontend && npm run lint
) > test-results/typescript-lint.log 2>&1 &
test_pids+=($!)
test_categories+=("TypeScript Code Quality")

# Performance Tests
print_test "⚡ Starting Performance Tests..."

# Backend Performance
(
    print_test "Running backend performance tests..."
    python scripts/run_tests.py performance
) > test-results/backend-performance.log 2>&1 &
test_pids+=($!)
test_categories+=("Backend Performance Tests")

# Wait for all tests to complete
print_status "⏳ Waiting for all test categories to complete..."
echo ""

# Monitor test progress
for i in "${!test_pids[@]}"; do
    pid=${test_pids[$i]}
    category=${test_categories[$i]}
    
    print_status "⏳ Waiting for: $category (PID: $pid)"
    
    if wait $pid; then
        test_results+=("PASS")
        print_success "$category completed successfully ✅"
    else
        test_results+=("FAIL")
        print_error "$category failed ❌"
    fi
done

echo ""
print_status "📊 Generating Test Summary Report..."

# Calculate results
total_tests=${#test_categories[@]}
passed_tests=0
failed_tests=0

for result in "${test_results[@]}"; do
    if [ "$result" = "PASS" ]; then
        ((passed_tests++))
    else
        ((failed_tests++))
    fi
done

# End time and duration
end_time=$(date +%s)
duration=$((end_time - start_time))
duration_formatted=$(printf '%02d:%02d:%02d' $((duration/3600)) $((duration%3600/60)) $((duration%60)))

# Generate summary report
cat > test-results/test-summary.txt << EOF
CertPathFinder Test Suite Summary
================================

Execution Time: $(date)
Duration: $duration_formatted
Total Test Categories: $total_tests
Passed: $passed_tests
Failed: $failed_tests
Success Rate: $(( passed_tests * 100 / total_tests ))%

Test Results by Category:
EOF

for i in "${!test_categories[@]}"; do
    category=${test_categories[$i]}
    result=${test_results[$i]}
    status_icon="❌"
    if [ "$result" = "PASS" ]; then
        status_icon="✅"
    fi
    echo "  $status_icon $category: $result" >> test-results/test-summary.txt
done

cat >> test-results/test-summary.txt << EOF

Log Files:
$(ls -la test-results/*.log | awk '{print "  " $9 " (" $5 " bytes)"}')

Coverage Reports:
  Backend: htmlcov/index.html
  Frontend: frontend/coverage/lcov-report/index.html
  E2E: test-results/e2e-artifacts/

Detailed Reports:
  Test Results: test-results/
  Performance: test-results/backend-performance.log
  Security: test-results/backend-security.log
EOF

# Display summary
echo ""
echo "🎉 ==============================================="
echo "📊 TEST SUITE SUMMARY"
echo "🎉 ==============================================="
echo ""
cat test-results/test-summary.txt
echo ""

# Generate badges/status
if [ $failed_tests -eq 0 ]; then
    print_success "🎉 ALL TESTS PASSED! 🎉"
    echo "✅ Build Status: PASSING"
    echo "🏆 Quality Gate: PASSED"
    exit_code=0
else
    print_error "❌ SOME TESTS FAILED"
    echo "❌ Build Status: FAILING"
    echo "🚫 Quality Gate: FAILED"
    echo ""
    echo "📋 Failed Test Categories:"
    for i in "${!test_categories[@]}"; do
        if [ "${test_results[$i]}" = "FAIL" ]; then
            echo "  ❌ ${test_categories[$i]}"
            echo "     📄 Log: test-results/$(echo "${test_categories[$i]}" | tr ' ' '-' | tr '[:upper:]' '[:lower:]').log"
        fi
    done
    exit_code=1
fi

echo ""
echo "📁 All test results saved to: test-results/"
echo "🌐 View detailed reports:"
echo "   Backend Coverage: file://$(pwd)/htmlcov/index.html"
echo "   Frontend Coverage: file://$(pwd)/frontend/coverage/lcov-report/index.html"
echo ""

# Optional: Open coverage reports
if command -v xdg-open > /dev/null; then
    read -p "Open coverage reports in browser? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open htmlcov/index.html 2>/dev/null &
        xdg-open frontend/coverage/lcov-report/index.html 2>/dev/null &
    fi
fi

print_status "🏁 Test suite completed in $duration_formatted"
exit $exit_code
