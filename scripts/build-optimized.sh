#!/bin/bash

# Optimized Build Script for CertPathFinder
# Builds production-ready containers with all optimizations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_REGISTRY=${DOCKER_REGISTRY:-""}
IMAGE_TAG=${IMAGE_TAG:-"latest"}
BUILD_ENV=${BUILD_ENV:-"production"}
ENABLE_CDN=${ENABLE_CDN:-"false"}
CDN_URL=${CDN_URL:-""}
ANALYZE_BUNDLE=${ANALYZE_BUNDLE:-"false"}

echo -e "${BLUE}🚀 Starting optimized build process...${NC}"
echo -e "${BLUE}Configuration:${NC}"
echo -e "  - Build Environment: ${BUILD_ENV}"
echo -e "  - Image Tag: ${IMAGE_TAG}"
echo -e "  - CDN Enabled: ${ENABLE_CDN}"
echo -e "  - Bundle Analysis: ${ANALYZE_BUNDLE}"

# Function to print step headers
print_step() {
    echo -e "\n${YELLOW}📋 $1${NC}"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
print_step "Checking prerequisites..."

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed or not in PATH"
    exit 1
fi

print_success "Prerequisites check passed"

# Clean previous builds
print_step "Cleaning previous builds..."
docker system prune -f --filter "label=project=certpathfinder" || true
rm -rf frontend/build/ || true
rm -rf frontend/dist/ || true
print_success "Cleanup completed"

# Install frontend dependencies
print_step "Installing frontend dependencies..."
cd frontend
npm ci --only=production
if [ "$BUILD_ENV" = "production" ]; then
    npm install --only=dev
fi
cd ..
print_success "Frontend dependencies installed"

# Build frontend with optimizations
print_step "Building optimized frontend..."
cd frontend

# Set environment variables for build
export NODE_ENV=production
export GENERATE_SOURCEMAP=false
export INLINE_RUNTIME_CHUNK=false
export ANALYZE=${ANALYZE_BUNDLE}

if [ "$ENABLE_CDN" = "true" ] && [ -n "$CDN_URL" ]; then
    export PUBLIC_URL="$CDN_URL"
fi

# Run production build
if [ -f "webpack.config.prod.js" ]; then
    npx webpack --config webpack.config.prod.js --mode production
else
    npm run build
fi

# Run CDN setup if enabled
if [ "$ENABLE_CDN" = "true" ]; then
    print_step "Setting up CDN configuration..."
    node ../scripts/cdn-setup.js "$CDN_URL"
    print_success "CDN configuration completed"
fi

cd ..
print_success "Frontend build completed"

# Build API with production optimizations
print_step "Building optimized API container..."

# Create production requirements if it doesn't exist
if [ ! -f "requirements-prod.txt" ]; then
    cat > requirements-prod.txt << EOF
gunicorn==21.2.0
uvicorn[standard]==0.24.0
psycopg2-binary==2.9.9
redis==5.0.1
celery==5.3.4
EOF
fi

# Build API container
docker build \
    -f Dockerfile.api.prod \
    -t certpathfinder-api:${IMAGE_TAG} \
    --build-arg ENVIRONMENT=production \
    --label "project=certpathfinder" \
    --label "component=api" \
    --label "version=${IMAGE_TAG}" \
    .

print_success "API container built successfully"

# Build frontend container
print_step "Building optimized frontend container..."

cd frontend
docker build \
    -f Dockerfile \
    -t certpathfinder-frontend:${IMAGE_TAG} \
    --build-arg NODE_ENV=production \
    --build-arg GENERATE_SOURCEMAP=false \
    --label "project=certpathfinder" \
    --label "component=frontend" \
    --label "version=${IMAGE_TAG}" \
    .
cd ..

print_success "Frontend container built successfully"

# Security scanning (if tools are available)
print_step "Running security scans..."

if command -v trivy &> /dev/null; then
    echo "Scanning API container..."
    trivy image --severity HIGH,CRITICAL certpathfinder-api:${IMAGE_TAG} || true
    
    echo "Scanning frontend container..."
    trivy image --severity HIGH,CRITICAL certpathfinder-frontend:${IMAGE_TAG} || true
else
    echo "Trivy not available, skipping security scan"
fi

print_success "Security scanning completed"

# Image optimization
print_step "Optimizing container images..."

# Get image sizes
API_SIZE=$(docker images certpathfinder-api:${IMAGE_TAG} --format "table {{.Size}}" | tail -n 1)
FRONTEND_SIZE=$(docker images certpathfinder-frontend:${IMAGE_TAG} --format "table {{.Size}}" | tail -n 1)

echo "Image sizes:"
echo "  - API: ${API_SIZE}"
echo "  - Frontend: ${FRONTEND_SIZE}"

print_success "Image optimization completed"

# Tag images for registry if specified
if [ -n "$DOCKER_REGISTRY" ]; then
    print_step "Tagging images for registry..."
    
    docker tag certpathfinder-api:${IMAGE_TAG} ${DOCKER_REGISTRY}/certpathfinder-api:${IMAGE_TAG}
    docker tag certpathfinder-frontend:${IMAGE_TAG} ${DOCKER_REGISTRY}/certpathfinder-frontend:${IMAGE_TAG}
    
    print_success "Images tagged for registry"
fi

# Generate deployment manifests
print_step "Generating deployment manifests..."

# Create deployment directory
mkdir -p deploy/

# Generate docker-compose for production
cat > deploy/docker-compose.prod.yml << EOF
version: '3.8'

services:
  api:
    image: ${DOCKER_REGISTRY:+$DOCKER_REGISTRY/}certpathfinder-api:${IMAGE_TAG}
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - WORKERS=4
    depends_on:
      - postgres
      - redis
    networks:
      - internal
      - web

  frontend:
    image: ${DOCKER_REGISTRY:+$DOCKER_REGISTRY/}certpathfinder-frontend:${IMAGE_TAG}
    restart: unless-stopped
    depends_on:
      - api
    networks:
      - web

  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: certpathfinder
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: \${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - internal

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - internal

volumes:
  postgres_data:
  redis_data:

networks:
  internal:
    driver: bridge
    internal: true
  web:
    driver: bridge
EOF

print_success "Deployment manifests generated"

# Generate build report
print_step "Generating build report..."

cat > deploy/build-report.md << EOF
# Build Report

**Build Date:** $(date)
**Build Environment:** ${BUILD_ENV}
**Image Tag:** ${IMAGE_TAG}
**CDN Enabled:** ${ENABLE_CDN}

## Container Images

| Component | Image | Size |
|-----------|-------|------|
| API | certpathfinder-api:${IMAGE_TAG} | ${API_SIZE} |
| Frontend | certpathfinder-frontend:${IMAGE_TAG} | ${FRONTEND_SIZE} |

## Optimizations Applied

- ✅ Multi-stage Docker builds
- ✅ Production webpack configuration
- ✅ Asset compression (gzip + brotli)
- ✅ Bundle splitting and tree shaking
- ✅ Image optimization
- ✅ Security headers
- ✅ Cache optimization
$([ "$ENABLE_CDN" = "true" ] && echo "- ✅ CDN configuration" || echo "- ⏸️ CDN configuration (disabled)")

## Deployment

Use the generated \`docker-compose.prod.yml\` for production deployment.

\`\`\`bash
cd deploy/
docker-compose -f docker-compose.prod.yml up -d
\`\`\`

## Environment Variables

Make sure to set the following environment variables:

- \`POSTGRES_PASSWORD\`: Database password
- \`SECRET_KEY\`: Application secret key
- \`CORS_ORIGINS\`: Allowed CORS origins

EOF

print_success "Build report generated"

# Final summary
echo -e "\n${GREEN}🎉 Optimized build completed successfully!${NC}"
echo -e "\n${BLUE}Summary:${NC}"
echo -e "  - API container: certpathfinder-api:${IMAGE_TAG} (${API_SIZE})"
echo -e "  - Frontend container: certpathfinder-frontend:${IMAGE_TAG} (${FRONTEND_SIZE})"
echo -e "  - Deployment files: deploy/"
echo -e "  - Build report: deploy/build-report.md"

if [ "$ANALYZE_BUNDLE" = "true" ]; then
    echo -e "\n${YELLOW}📊 Bundle analysis report available at: frontend/bundle-report.html${NC}"
fi

if [ -n "$DOCKER_REGISTRY" ]; then
    echo -e "\n${BLUE}To push to registry:${NC}"
    echo -e "  docker push ${DOCKER_REGISTRY}/certpathfinder-api:${IMAGE_TAG}"
    echo -e "  docker push ${DOCKER_REGISTRY}/certpathfinder-frontend:${IMAGE_TAG}"
fi

echo -e "\n${GREEN}Build process completed! 🚀${NC}"
