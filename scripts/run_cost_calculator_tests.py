#!/usr/bin/env python3
"""Comprehensive test runner for cost calculator functionality.

This script runs all cost calculator tests with coverage reporting
and generates detailed test reports for achieving 95%+ coverage.
"""

import os
import sys
import subprocess
import argparse
import json
from pathlib import Path
from datetime import datetime


def setup_environment():
    """Set up test environment variables."""
    os.environ.setdefault('DATABASE_URL', 'postgresql://test_user:test_pass@localhost:5432/test_certrats')
    os.environ.setdefault('TESTING', 'true')
    os.environ.setdefault('ANTHROPIC_API_KEY', 'test_key')


def run_unit_tests(verbose=False, coverage=True):
    """Run unit tests for cost calculator models and services."""
    print("🧪 Running Cost Calculator Unit Tests...")
    
    cmd = [
        'python', '-m', 'pytest',
        'tests/test_cost_calculator/test_cost_calculation_models.py',
        'tests/test_cost_calculator/test_cost_calculator_service.py',
        '-v' if verbose else '-q',
        '--tb=short',
        '-x',  # Stop on first failure
        '--durations=10',  # Show 10 slowest tests
        '-m', 'not e2e'  # Exclude E2E tests
    ]
    
    if coverage:
        cmd.extend([
            '--cov=models.cost_calculation',
            '--cov=services.cost_calculator',
            '--cov-report=term-missing',
            '--cov-report=html:test-reports/cost-calculator-unit-coverage',
            '--cov-fail-under=95'
        ])
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Unit tests passed!")
    else:
        print("❌ Unit tests failed!")
        print(result.stdout)
        print(result.stderr)
    
    return result.returncode == 0


def run_api_tests(verbose=False, coverage=True):
    """Run API integration tests for cost calculator endpoints."""
    print("🌐 Running Cost Calculator API Tests...")
    
    cmd = [
        'python', '-m', 'pytest',
        'tests/test_cost_calculator/test_cost_calculator_api.py',
        '-v' if verbose else '-q',
        '--tb=short',
        '-x',
        '--durations=10',
        '-m', 'not e2e'
    ]
    
    if coverage:
        cmd.extend([
            '--cov=api.v1.cost_calculator',
            '--cov=schemas.cost_calculation',
            '--cov-report=term-missing',
            '--cov-report=html:test-reports/cost-calculator-api-coverage',
            '--cov-fail-under=95'
        ])
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ API tests passed!")
    else:
        print("❌ API tests failed!")
        print(result.stdout)
        print(result.stderr)
    
    return result.returncode == 0


def run_e2e_tests(verbose=False, headless=True):
    """Run end-to-end tests using Playwright."""
    print("🎭 Running Cost Calculator E2E Tests...")
    
    # Install Playwright browsers if needed
    subprocess.run(['python', '-m', 'playwright', 'install'], 
                  capture_output=True, check=False)
    
    cmd = [
        'python', '-m', 'pytest',
        'tests/e2e/test_cost_calculator_e2e.py',
        '-v' if verbose else '-q',
        '--tb=short',
        '-m', 'e2e',
        '--browser=chromium',
        '--browser=firefox',
        '--browser=webkit'
    ]
    
    if headless:
        cmd.append('--headless')
    
    # Set Playwright specific environment variables
    env = os.environ.copy()
    env['PLAYWRIGHT_BROWSERS_PATH'] = '0'
    
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode == 0:
        print("✅ E2E tests passed!")
    else:
        print("❌ E2E tests failed!")
        print(result.stdout)
        print(result.stderr)
    
    return result.returncode == 0


def run_performance_tests(verbose=False):
    """Run performance tests for cost calculator operations."""
    print("⚡ Running Cost Calculator Performance Tests...")
    
    cmd = [
        'python', '-m', 'pytest',
        'tests/test_cost_calculator/',
        '-v' if verbose else '-q',
        '--tb=short',
        '-m', 'performance',
        '--benchmark-only',
        '--benchmark-sort=mean',
        '--benchmark-json=test-reports/cost-calculator-performance.json'
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Performance tests passed!")
    else:
        print("❌ Performance tests failed!")
        print(result.stdout)
        print(result.stderr)
    
    return result.returncode == 0


def run_security_tests(verbose=False):
    """Run security tests for cost calculator functionality."""
    print("🔒 Running Cost Calculator Security Tests...")
    
    cmd = [
        'python', '-m', 'pytest',
        'tests/test_cost_calculator/',
        '-v' if verbose else '-q',
        '--tb=short',
        '-m', 'security'
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Security tests passed!")
    else:
        print("❌ Security tests failed!")
        print(result.stdout)
        print(result.stderr)
    
    return result.returncode == 0


def run_comprehensive_coverage():
    """Run comprehensive coverage analysis."""
    print("📊 Running Comprehensive Coverage Analysis...")
    
    cmd = [
        'python', '-m', 'pytest',
        'tests/test_cost_calculator/',
        '--cov=models.cost_calculation',
        '--cov=services.cost_calculator',
        '--cov=api.v1.cost_calculator',
        '--cov=schemas.cost_calculation',
        '--cov-report=term-missing',
        '--cov-report=html:test-reports/cost-calculator-comprehensive-coverage',
        '--cov-report=xml:test-reports/cost-calculator-coverage.xml',
        '--cov-report=json:test-reports/cost-calculator-coverage.json',
        '--cov-fail-under=95',
        '-m', 'not e2e'  # Exclude E2E tests from coverage
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Coverage target achieved (95%+)!")
        
        # Parse coverage report
        try:
            with open('test-reports/cost-calculator-coverage.json', 'r') as f:
                coverage_data = json.load(f)
                total_coverage = coverage_data['totals']['percent_covered']
                print(f"📈 Total Coverage: {total_coverage:.2f}%")
                
                # Show per-file coverage
                print("\n📁 Per-File Coverage:")
                for file_path, file_data in coverage_data['files'].items():
                    if 'cost_calculation' in file_path or 'cost_calculator' in file_path:
                        coverage_pct = file_data['summary']['percent_covered']
                        print(f"  {file_path}: {coverage_pct:.2f}%")
        except (FileNotFoundError, KeyError, json.JSONDecodeError):
            print("⚠️  Could not parse coverage report")
    else:
        print("❌ Coverage target not met!")
        print(result.stdout)
        print(result.stderr)
    
    return result.returncode == 0


def generate_test_report():
    """Generate comprehensive test report."""
    print("📋 Generating Test Report...")
    
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'test_suite': 'Cost Calculator',
        'coverage_target': '95%',
        'test_categories': {
            'unit_tests': 'models and services',
            'api_tests': 'FastAPI endpoints',
            'e2e_tests': 'Playwright browser tests',
            'performance_tests': 'benchmark tests',
            'security_tests': 'security validation'
        },
        'files_tested': [
            'models/cost_calculation.py',
            'services/cost_calculator.py',
            'api/v1/cost_calculator.py',
            'schemas/cost_calculation.py'
        ]
    }
    
    # Create reports directory
    Path('test-reports').mkdir(exist_ok=True)
    
    # Write test report
    with open('test-reports/cost-calculator-test-report.json', 'w') as f:
        json.dump(report_data, f, indent=2)
    
    # Generate HTML report
    html_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Cost Calculator Test Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .header {{ background: #f0f8ff; padding: 20px; border-radius: 8px; }}
            .section {{ margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; }}
            .success {{ color: #28a745; }}
            .warning {{ color: #ffc107; }}
            .error {{ color: #dc3545; }}
            .coverage {{ background: #e8f5e9; padding: 10px; border-radius: 4px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🧮 Cost Calculator Test Report</h1>
            <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>Coverage Target:</strong> 95%+</p>
        </div>
        
        <div class="section">
            <h2>📊 Test Coverage Summary</h2>
            <div class="coverage">
                <p>✅ <strong>Models:</strong> models/cost_calculation.py</p>
                <p>✅ <strong>Services:</strong> services/cost_calculator.py</p>
                <p>✅ <strong>API:</strong> api/v1/cost_calculator.py</p>
                <p>✅ <strong>Schemas:</strong> schemas/cost_calculation.py</p>
            </div>
        </div>
        
        <div class="section">
            <h2>🧪 Test Categories</h2>
            <ul>
                <li><strong>Unit Tests:</strong> Model validation, business logic, edge cases</li>
                <li><strong>API Tests:</strong> Endpoint behavior, validation, error handling</li>
                <li><strong>E2E Tests:</strong> Complete user workflows, UI interactions</li>
                <li><strong>Performance Tests:</strong> Response times, throughput</li>
                <li><strong>Security Tests:</strong> Input validation, authorization</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>📈 Coverage Reports</h2>
            <p><a href="cost-calculator-comprehensive-coverage/index.html">📊 HTML Coverage Report</a></p>
            <p><a href="cost-calculator-coverage.xml">📄 XML Coverage Report</a></p>
            <p><a href="cost-calculator-coverage.json">📋 JSON Coverage Report</a></p>
        </div>
        
        <div class="section">
            <h2>🎯 Test Execution</h2>
            <p>Run tests with: <code>python scripts/run_cost_calculator_tests.py --all</code></p>
            <p>View coverage: <code>python scripts/run_cost_calculator_tests.py --coverage-only</code></p>
            <p>E2E tests: <code>python scripts/run_cost_calculator_tests.py --e2e-only</code></p>
        </div>
    </body>
    </html>
    """
    
    with open('test-reports/cost-calculator-test-report.html', 'w') as f:
        f.write(html_report)
    
    print("✅ Test report generated!")
    print("📊 View HTML report: test-reports/cost-calculator-test-report.html")
    print("📋 View JSON report: test-reports/cost-calculator-test-report.json")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description='Cost Calculator Test Runner')
    parser.add_argument('--unit', action='store_true', help='Run unit tests only')
    parser.add_argument('--api', action='store_true', help='Run API tests only')
    parser.add_argument('--e2e', action='store_true', help='Run E2E tests only')
    parser.add_argument('--performance', action='store_true', help='Run performance tests only')
    parser.add_argument('--security', action='store_true', help='Run security tests only')
    parser.add_argument('--coverage-only', action='store_true', help='Run coverage analysis only')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--no-coverage', action='store_true', help='Skip coverage reporting')
    parser.add_argument('--headful', action='store_true', help='Run E2E tests with browser UI')
    
    args = parser.parse_args()
    
    # Set up environment
    setup_environment()
    
    # Create reports directory
    Path('test-reports').mkdir(exist_ok=True)
    
    success = True
    coverage = not args.no_coverage
    
    print("🧮 Cost Calculator Test Suite")
    print("=" * 50)
    
    if args.unit or args.all:
        success &= run_unit_tests(args.verbose, coverage)
    
    if args.api or args.all:
        success &= run_api_tests(args.verbose, coverage)
    
    if args.e2e or args.all:
        success &= run_e2e_tests(args.verbose, not args.headful)
    
    if args.performance or args.all:
        success &= run_performance_tests(args.verbose)
    
    if args.security or args.all:
        success &= run_security_tests(args.verbose)
    
    if args.coverage_only or args.all:
        success &= run_comprehensive_coverage()
    
    # Generate test report
    generate_test_report()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! Coverage target achieved.")
        sys.exit(0)
    else:
        print("💥 Some tests failed or coverage target not met.")
        sys.exit(1)


if __name__ == '__main__':
    main()
