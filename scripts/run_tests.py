#!/usr/bin/env python3
"""
Comprehensive test runner for CertPathFinder
Supports different test categories and reporting options
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path
from typing import List, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """Test runner with support for different test categories"""
    
    def __init__(self):
        self.project_root = project_root
        self.tests_dir = self.project_root / "tests"
        
    def run_command(self, cmd: List[str], cwd: Optional[Path] = None) -> int:
        """Run a command and return exit code"""
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=cwd or self.project_root)
        return result.returncode
    
    def run_unit_tests(self, verbose: bool = False, coverage: bool = True) -> int:
        """Run unit tests"""
        cmd = ["python", "-m", "pytest", "tests/unit", "tests/features"]
        
        if verbose:
            cmd.append("-v")
        
        if coverage:
            cmd.extend([
                "--cov=services",
                "--cov=models", 
                "--cov=api",
                "--cov=utils",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov/unit"
            ])
        
        cmd.extend(["-m", "unit or not integration and not e2e"])
        
        return self.run_command(cmd)
    
    def run_integration_tests(self, verbose: bool = False) -> int:
        """Run integration tests"""
        cmd = ["python", "-m", "pytest", "tests/integration", "-m", "integration"]
        
        if verbose:
            cmd.append("-v")
        
        return self.run_command(cmd)
    
    def run_e2e_tests(self, verbose: bool = False) -> int:
        """Run end-to-end tests"""
        cmd = ["python", "-m", "pytest", "tests/e2e", "-m", "e2e"]
        
        if verbose:
            cmd.append("-v")
        
        return self.run_command(cmd)
    
    def run_feature_tests(self, feature: str, verbose: bool = False) -> int:
        """Run tests for a specific feature"""
        feature_dir = self.tests_dir / "features" / feature
        
        if not feature_dir.exists():
            print(f"Feature '{feature}' not found in {feature_dir}")
            return 1
        
        cmd = ["python", "-m", "pytest", str(feature_dir)]
        
        if verbose:
            cmd.append("-v")
        
        return self.run_command(cmd)
    
    def run_api_tests(self, verbose: bool = False) -> int:
        """Run API tests"""
        cmd = ["python", "-m", "pytest", "-m", "api"]
        
        if verbose:
            cmd.append("-v")
        
        return self.run_command(cmd)
    
    def run_security_tests(self, verbose: bool = False) -> int:
        """Run security tests"""
        cmd = ["python", "-m", "pytest", "-m", "security"]
        
        if verbose:
            cmd.append("-v")
        
        return self.run_command(cmd)
    
    def run_performance_tests(self, verbose: bool = False) -> int:
        """Run performance tests"""
        cmd = ["python", "-m", "pytest", "-m", "performance"]
        
        if verbose:
            cmd.append("-v")
        
        return self.run_command(cmd)
    
    def run_all_tests(self, verbose: bool = False, coverage: bool = True) -> int:
        """Run all tests"""
        cmd = ["python", "-m", "pytest", "tests/"]
        
        if verbose:
            cmd.append("-v")
        
        if coverage:
            cmd.extend([
                "--cov=.",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov",
                "--cov-report=xml",
                "--cov-fail-under=80"
            ])
        
        return self.run_command(cmd)
    
    def run_smoke_tests(self, verbose: bool = False) -> int:
        """Run smoke tests"""
        cmd = ["python", "-m", "pytest", "-m", "smoke"]
        
        if verbose:
            cmd.append("-v")
        
        return self.run_command(cmd)
    
    def lint_code(self) -> int:
        """Run code linting"""
        print("Running code linting...")
        
        # Run flake8
        flake8_result = self.run_command([
            "python", "-m", "flake8", 
            "api/", "services/", "models/", "utils/", "tests/",
            "--max-line-length=100",
            "--ignore=E203,W503"
        ])
        
        # Run mypy
        mypy_result = self.run_command([
            "python", "-m", "mypy", 
            "api/", "services/", "models/", "utils/",
            "--ignore-missing-imports"
        ])
        
        return max(flake8_result, mypy_result)
    
    def format_code(self) -> int:
        """Format code with black"""
        print("Formatting code with black...")
        return self.run_command([
            "python", "-m", "black",
            "api/", "services/", "models/", "utils/", "tests/",
            "--line-length=100"
        ])
    
    def check_security(self) -> int:
        """Run security checks"""
        print("Running security checks...")
        return self.run_command([
            "python", "-m", "bandit", 
            "-r", "api/", "services/", "models/", "utils/",
            "-f", "json", "-o", "security-report.json"
        ])


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="CertPathFinder Test Runner")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--no-coverage", action="store_true", help="Disable coverage reporting")
    
    subparsers = parser.add_subparsers(dest="command", help="Test commands")
    
    # Unit tests
    subparsers.add_parser("unit", help="Run unit tests")
    
    # Integration tests
    subparsers.add_parser("integration", help="Run integration tests")
    
    # E2E tests
    subparsers.add_parser("e2e", help="Run end-to-end tests")
    
    # Feature tests
    feature_parser = subparsers.add_parser("feature", help="Run feature tests")
    feature_parser.add_argument("name", help="Feature name")
    
    # API tests
    subparsers.add_parser("api", help="Run API tests")
    
    # Security tests
    subparsers.add_parser("security", help="Run security tests")
    
    # Performance tests
    subparsers.add_parser("performance", help="Run performance tests")
    
    # All tests
    subparsers.add_parser("all", help="Run all tests")
    
    # Smoke tests
    subparsers.add_parser("smoke", help="Run smoke tests")
    
    # Code quality
    subparsers.add_parser("lint", help="Run code linting")
    subparsers.add_parser("format", help="Format code")
    subparsers.add_parser("security-check", help="Run security checks")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    coverage = not args.no_coverage
    
    if args.command == "unit":
        return runner.run_unit_tests(args.verbose, coverage)
    elif args.command == "integration":
        return runner.run_integration_tests(args.verbose)
    elif args.command == "e2e":
        return runner.run_e2e_tests(args.verbose)
    elif args.command == "feature":
        return runner.run_feature_tests(args.name, args.verbose)
    elif args.command == "api":
        return runner.run_api_tests(args.verbose)
    elif args.command == "security":
        return runner.run_security_tests(args.verbose)
    elif args.command == "performance":
        return runner.run_performance_tests(args.verbose)
    elif args.command == "all":
        return runner.run_all_tests(args.verbose, coverage)
    elif args.command == "smoke":
        return runner.run_smoke_tests(args.verbose)
    elif args.command == "lint":
        return runner.lint_code()
    elif args.command == "format":
        return runner.format_code()
    elif args.command == "security-check":
        return runner.check_security()
    else:
        parser.print_help()
        return 1


if __name__ == "__main__":
    sys.exit(main())
