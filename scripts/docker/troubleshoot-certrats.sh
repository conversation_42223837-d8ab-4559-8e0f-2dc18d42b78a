#!/bin/bash

# CertRats Platform Troubleshooting Script
#
# This script provides comprehensive troubleshooting and diagnostic
# capabilities for the CertRats certification platform.
#
# Features:
# - Automated problem detection
# - Service dependency analysis
# - Log analysis and error detection
# - Network connectivity testing
# - Resource usage monitoring
# - Common issue resolution
#
# Usage:
#   ./scripts/docker/troubleshoot-certrats.sh [environment] [options]
#
# Examples:
#   ./scripts/docker/troubleshoot-certrats.sh dev
#   ./scripts/docker/troubleshoot-certrats.sh prod --fix
#   ./scripts/docker/troubleshoot-certrats.sh staging --verbose

set -euo pipefail

# =============================================================================
# CONFIGURATION AND CONSTANTS
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
COMPOSE_PROJECT_NAME="certrats"

# Default values
ENVIRONMENT="${1:-development}"
VERBOSE=false
AUTO_FIX=false
GENERATE_REPORT=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Issue tracking
ISSUES_FOUND=()
FIXES_APPLIED=()

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${GREEN}[${timestamp}] INFO:${NC} $message" ;;
        "WARN")  echo -e "${YELLOW}[${timestamp}] WARN:${NC} $message" ;;
        "ERROR") echo -e "${RED}[${timestamp}] ERROR:${NC} $message" ;;
        "DEBUG") [[ "$VERBOSE" == "true" ]] && echo -e "${BLUE}[${timestamp}] DEBUG:${NC} $message" ;;
        "FIX")   echo -e "${PURPLE}[${timestamp}] FIX:${NC} $message" ;;
        *)       echo -e "${CYAN}[${timestamp}] $level:${NC} $message" ;;
    esac
}

add_issue() {
    local issue="$1"
    ISSUES_FOUND+=("$issue")
    log "ERROR" "Issue detected: $issue"
}

add_fix() {
    local fix="$1"
    FIXES_APPLIED+=("$fix")
    log "FIX" "Applied fix: $fix"
}

show_usage() {
    cat << EOF
🔧 CertRats Platform Troubleshooting Script

Usage: $0 [environment] [options]

Environments:
  dev, development    Troubleshoot development environment
  staging            Troubleshoot staging environment
  prod, production   Troubleshoot production environment

Options:
  --verbose, -v      Enable verbose logging
  --fix, -f          Automatically apply fixes where possible
  --report, -r       Generate detailed troubleshooting report
  --help, -h         Show this help message

Examples:
  $0 dev                    # Basic troubleshooting
  $0 prod --fix             # Troubleshoot and auto-fix issues
  $0 staging --report       # Generate detailed report

Environment Variables:
  CERTRATS_ENV              Override environment detection
  COMPOSE_PROJECT_NAME      Override Docker Compose project name

EOF
}

setup_environment() {
    # Validate environment
    case "$ENVIRONMENT" in
        "dev"|"development")
            ENVIRONMENT="development"
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.dev.yml"
            ;;
        "staging")
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.staging.yml"
            ;;
        "prod"|"production")
            ENVIRONMENT="production"
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.prod.yml"
            ;;
        *)
            log "ERROR" "Invalid environment: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    # Set environment variables
    export CERTRATS_ENV="$ENVIRONMENT"
    export COMPOSE_PROJECT_NAME="$COMPOSE_PROJECT_NAME"
}

check_docker_daemon() {
    log "INFO" "🐳 Checking Docker daemon..."
    
    if ! docker info &>/dev/null; then
        add_issue "Docker daemon is not running"
        if [[ "$AUTO_FIX" == "true" ]]; then
            log "FIX" "Attempting to start Docker daemon..."
            sudo systemctl start docker &>/dev/null || {
                log "ERROR" "Failed to start Docker daemon automatically"
            }
        fi
        return 1
    fi
    
    # Check Docker version
    local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    log "DEBUG" "Docker version: $docker_version"
    
    # Check available disk space
    local disk_usage=$(df /var/lib/docker 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//' || echo "0")
    if [[ $disk_usage -gt 90 ]]; then
        add_issue "Docker disk usage is high: ${disk_usage}%"
        if [[ "$AUTO_FIX" == "true" ]]; then
            log "FIX" "Cleaning up Docker system..."
            docker system prune -f &>/dev/null
            add_fix "Cleaned up Docker system"
        fi
    fi
    
    log "INFO" "✅ Docker daemon is healthy"
}

check_compose_files() {
    log "INFO" "📄 Checking Docker Compose files..."
    
    cd "$PROJECT_ROOT"
    
    # Check if compose files exist
    local compose_files=("docker-compose.yml")
    case "$ENVIRONMENT" in
        "development") compose_files+=("docker-compose.dev.yml") ;;
        "staging") compose_files+=("docker-compose.staging.yml") ;;
        "production") compose_files+=("docker-compose.prod.yml") ;;
    esac
    
    for file in "${compose_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            add_issue "Missing Docker Compose file: $file"
            return 1
        fi
    done
    
    # Validate compose file syntax
    if ! docker-compose $COMPOSE_FILES config &>/dev/null; then
        add_issue "Invalid Docker Compose configuration"
        log "DEBUG" "Running compose config validation..."
        docker-compose $COMPOSE_FILES config 2>&1 | head -10
        return 1
    fi
    
    log "INFO" "✅ Docker Compose files are valid"
}

check_environment_variables() {
    log "INFO" "🔧 Checking environment variables..."
    
    # Check for .env file
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        add_issue "Missing .env file"
        if [[ "$AUTO_FIX" == "true" ]] && [[ -f "$PROJECT_ROOT/.env.example" ]]; then
            log "FIX" "Creating .env file from template..."
            cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/.env"
            add_fix "Created .env file from template"
        fi
    fi
    
    # Check critical environment variables
    local required_vars=("SECRET_KEY" "POSTGRES_PASSWORD" "REDIS_PASSWORD")
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$PROJECT_ROOT/.env" 2>/dev/null; then
            add_issue "Missing environment variable: $var"
        fi
    done
    
    log "INFO" "✅ Environment variables checked"
}

check_networks() {
    log "INFO" "🌐 Checking Docker networks..."
    
    # Check Traefik network
    local traefik_network="traefik_network"
    if ! docker network ls | grep -q "$traefik_network"; then
        add_issue "Missing Traefik network: $traefik_network"
        if [[ "$AUTO_FIX" == "true" ]]; then
            log "FIX" "Creating Traefik network..."
            docker network create "$traefik_network" &>/dev/null
            add_fix "Created Traefik network"
        fi
    fi
    
    # Check project networks
    local project_networks=$(docker network ls --filter name=certrats --format "{{.Name}}")
    log "DEBUG" "Project networks: $project_networks"
    
    log "INFO" "✅ Networks checked"
}

check_services() {
    log "INFO" "🔍 Checking service status..."
    
    cd "$PROJECT_ROOT"
    
    local services=($(docker-compose $COMPOSE_FILES config --services 2>/dev/null || echo ""))
    local unhealthy_services=()
    
    for service in "${services[@]}"; do
        local container_name="certrats_$service"
        
        if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
            local status=$(docker inspect --format='{{.State.Status}}' "$container_name" 2>/dev/null || echo "unknown")
            local health=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")
            
            if [[ "$status" != "running" ]] || [[ "$health" == "unhealthy" ]]; then
                unhealthy_services+=("$service")
                add_issue "Service $service is not healthy (status: $status, health: $health)"
            fi
        else
            unhealthy_services+=("$service")
            add_issue "Service $service is not running"
        fi
    done
    
    if [[ ${#unhealthy_services[@]} -gt 0 ]] && [[ "$AUTO_FIX" == "true" ]]; then
        log "FIX" "Attempting to restart unhealthy services..."
        for service in "${unhealthy_services[@]}"; do
            docker-compose $COMPOSE_FILES restart "$service" &>/dev/null || {
                log "WARN" "Failed to restart service: $service"
            }
        done
        add_fix "Restarted unhealthy services"
    fi
    
    log "INFO" "✅ Service status checked"
}

check_connectivity() {
    log "INFO" "🔗 Checking service connectivity..."
    
    # Test internal connectivity
    local api_container="certrats_api"
    local db_container="certrats_database"
    
    if docker ps --format "{{.Names}}" | grep -q "^${api_container}$"; then
        # Test database connection from API
        if ! docker exec "$api_container" nc -z database 5432 &>/dev/null; then
            add_issue "API cannot connect to database"
        fi
        
        # Test Redis connection from API
        if ! docker exec "$api_container" nc -z redis 6379 &>/dev/null; then
            add_issue "API cannot connect to Redis"
        fi
    fi
    
    # Test external connectivity
    if [[ "$ENVIRONMENT" != "development" ]]; then
        # Test SSL certificates
        local frontend_url="https://app.${ENVIRONMENT}.certrats.dev"
        if ! curl -s --max-time 10 "$frontend_url" &>/dev/null; then
            add_issue "Frontend URL is not accessible: $frontend_url"
        fi
    fi
    
    log "INFO" "✅ Connectivity checked"
}

analyze_logs() {
    log "INFO" "📝 Analyzing service logs..."
    
    cd "$PROJECT_ROOT"
    
    local services=($(docker-compose $COMPOSE_FILES config --services 2>/dev/null || echo ""))
    
    for service in "${services[@]}"; do
        local container_name="certrats_$service"
        
        if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
            log "DEBUG" "Analyzing logs for $service..."
            
            # Check for common error patterns
            local error_patterns=("ERROR" "FATAL" "Exception" "failed" "timeout" "connection refused")
            
            for pattern in "${error_patterns[@]}"; do
                local error_count=$(docker logs "$container_name" --since 1h 2>&1 | grep -i "$pattern" | wc -l)
                if [[ $error_count -gt 10 ]]; then
                    add_issue "High error count in $service logs: $error_count instances of '$pattern'"
                fi
            done
            
            # Check for specific issues
            if docker logs "$container_name" --since 1h 2>&1 | grep -q "Out of memory"; then
                add_issue "Service $service is running out of memory"
            fi
            
            if docker logs "$container_name" --since 1h 2>&1 | grep -q "Connection refused"; then
                add_issue "Service $service has connection issues"
            fi
        fi
    done
    
    log "INFO" "✅ Log analysis completed"
}

check_resources() {
    log "INFO" "💾 Checking resource usage..."
    
    # Check system resources
    local memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [[ $memory_usage -gt 90 ]]; then
        add_issue "High system memory usage: ${memory_usage}%"
    fi
    
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 90 ]]; then
        add_issue "High disk usage: ${disk_usage}%"
    fi
    
    # Check container resources
    local containers=$(docker ps --filter name=certrats_ --format "{{.Names}}")
    for container in $containers; do
        local stats=$(docker stats --no-stream --format "{{.CPUPerc}},{{.MemPerc}}" "$container" 2>/dev/null || echo "0%,0%")
        local cpu=$(echo "$stats" | cut -d',' -f1 | sed 's/%//')
        local mem=$(echo "$stats" | cut -d',' -f2 | sed 's/%//')
        
        if [[ $(echo "$cpu > 90" | bc -l 2>/dev/null || echo 0) -eq 1 ]]; then
            add_issue "High CPU usage in $container: ${cpu}%"
        fi
        
        if [[ $(echo "$mem > 90" | bc -l 2>/dev/null || echo 0) -eq 1 ]]; then
            add_issue "High memory usage in $container: ${mem}%"
        fi
    done
    
    log "INFO" "✅ Resource usage checked"
}

generate_report() {
    if [[ "$GENERATE_REPORT" != "true" ]]; then
        return
    fi
    
    local report_file="$PROJECT_ROOT/troubleshooting-report-$(date +%Y%m%d_%H%M%S).txt"
    
    log "INFO" "📊 Generating troubleshooting report: $report_file"
    
    {
        echo "CertRats Platform Troubleshooting Report"
        echo "========================================"
        echo "Environment: $ENVIRONMENT"
        echo "Timestamp: $(date)"
        echo "Generated by: $0"
        echo
        
        echo "Issues Found (${#ISSUES_FOUND[@]}):"
        echo "-------------------"
        for issue in "${ISSUES_FOUND[@]}"; do
            echo "  ❌ $issue"
        done
        echo
        
        echo "Fixes Applied (${#FIXES_APPLIED[@]}):"
        echo "--------------------"
        for fix in "${FIXES_APPLIED[@]}"; do
            echo "  ✅ $fix"
        done
        echo
        
        echo "System Information:"
        echo "------------------"
        echo "Docker Version: $(docker --version)"
        echo "Docker Compose Version: $(docker-compose --version)"
        echo "System Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
        echo "Disk Usage: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 ")"}')"
        echo
        
        echo "Running Containers:"
        echo "------------------"
        docker ps --filter name=certrats_ --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo
        
        echo "Recent Logs (last 10 lines per service):"
        echo "---------------------------------------"
        local services=($(docker-compose $COMPOSE_FILES config --services 2>/dev/null || echo ""))
        for service in "${services[@]}"; do
            local container_name="certrats_$service"
            if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
                echo "=== $service ==="
                docker logs --tail 10 "$container_name" 2>&1
                echo
            fi
        done
        
    } > "$report_file"
    
    log "INFO" "✅ Report generated: $report_file"
}

show_summary() {
    echo
    echo -e "${CYAN}🔧 Troubleshooting Summary${NC}"
    echo "=========================="
    
    echo -e "${RED}Issues Found: ${#ISSUES_FOUND[@]}${NC}"
    for issue in "${ISSUES_FOUND[@]}"; do
        echo "  ❌ $issue"
    done
    
    if [[ ${#FIXES_APPLIED[@]} -gt 0 ]]; then
        echo
        echo -e "${GREEN}Fixes Applied: ${#FIXES_APPLIED[@]}${NC}"
        for fix in "${FIXES_APPLIED[@]}"; do
            echo "  ✅ $fix"
        done
    fi
    
    echo
    if [[ ${#ISSUES_FOUND[@]} -eq 0 ]]; then
        echo -e "${GREEN}🎉 No issues detected! CertRats platform appears to be healthy.${NC}"
    else
        echo -e "${YELLOW}⚠️  Issues detected. Please review and address the problems above.${NC}"
        if [[ "$AUTO_FIX" != "true" ]]; then
            echo -e "${BLUE}💡 Run with --fix to automatically apply fixes where possible.${NC}"
        fi
    fi
}

# =============================================================================
# ARGUMENT PARSING
# =============================================================================

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose|-v)
                VERBOSE=true
                shift
                ;;
            --fix|-f)
                AUTO_FIX=true
                shift
                ;;
            --report|-r)
                GENERATE_REPORT=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                if [[ -z "${ENVIRONMENT_SET:-}" ]]; then
                    ENVIRONMENT="$1"
                    ENVIRONMENT_SET=true
                else
                    log "ERROR" "Unknown option: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    # Parse command line arguments
    parse_arguments "$@"
    
    # Show banner
    echo -e "${PURPLE}"
    cat << "EOF"
   ____          _   ____       _       
  / ___|___ _ __| |_|  _ \ __ _| |_ ___ 
 | |   / _ \ '__| __| |_) / _` | __/ __|
 | |__|  __/ |  | |_|  _ < (_| | |_\__ \
  \____\___|_|   \__|_| \_\__,_|\__|___/
                                       
  Certification Platform Troubleshooter
EOF
    echo -e "${NC}"
    
    log "INFO" "🔧 Starting troubleshooting for $ENVIRONMENT environment..."
    
    # Setup environment
    setup_environment
    
    # Run diagnostic checks
    check_docker_daemon
    check_compose_files
    check_environment_variables
    check_networks
    check_services
    check_connectivity
    analyze_logs
    check_resources
    
    # Generate report if requested
    generate_report
    
    # Show summary
    show_summary
    
    # Exit with appropriate code
    if [[ ${#ISSUES_FOUND[@]} -gt 0 ]]; then
        exit 1
    else
        exit 0
    fi
}

# Run main function with all arguments
main "$@"
