#!/bin/bash

# CertRats Platform Stop Script
#
# This script provides a comprehensive shutdown solution for the CertRats
# certification platform with proper cleanup and data preservation.
#
# Features:
# - Graceful service shutdown
# - Data backup before stopping (optional)
# - Volume and network cleanup options
# - Environment-specific handling
# - Comprehensive logging
#
# Usage:
#   ./scripts/docker/stop-certrats.sh [environment] [options]
#
# Examples:
#   ./scripts/docker/stop-certrats.sh dev
#   ./scripts/docker/stop-certrats.sh prod --backup
#   ./scripts/docker/stop-certrats.sh staging --cleanup --verbose

set -euo pipefail

# =============================================================================
# CONFIGURATION AND CONSTANTS
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
COMPOSE_PROJECT_NAME="certrats"

# Default values
ENVIRONMENT="${1:-development}"
VERBOSE=false
BACKUP_DATA=false
CLEANUP_VOLUMES=false
CLEANUP_NETWORKS=false
FORCE_STOP=false
STOP_TRAEFIK=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${GREEN}[${timestamp}] INFO:${NC} $message" ;;
        "WARN")  echo -e "${YELLOW}[${timestamp}] WARN:${NC} $message" ;;
        "ERROR") echo -e "${RED}[${timestamp}] ERROR:${NC} $message" ;;
        "DEBUG") [[ "$VERBOSE" == "true" ]] && echo -e "${BLUE}[${timestamp}] DEBUG:${NC} $message" ;;
        *)       echo -e "${CYAN}[${timestamp}] $level:${NC} $message" ;;
    esac
}

show_usage() {
    cat << EOF
🛑 CertRats Platform Stop Script

Usage: $0 [environment] [options]

Environments:
  dev, development    Stop development environment
  staging            Stop staging environment
  prod, production   Stop production environment
  all                Stop all environments

Options:
  --verbose, -v      Enable verbose logging
  --backup           Backup data before stopping
  --cleanup          Remove volumes and networks
  --force            Force stop containers (kill instead of graceful stop)
  --stop-traefik     Also stop Traefik proxy
  --help, -h         Show this help message

Examples:
  $0 dev                    # Stop development environment
  $0 prod --backup          # Stop production with data backup
  $0 all --cleanup          # Stop all and cleanup everything

Environment Variables:
  CERTRATS_ENV              Override environment detection
  COMPOSE_PROJECT_NAME      Override Docker Compose project name

EOF
}

setup_environment() {
    log "INFO" "🔧 Setting up environment: $ENVIRONMENT"
    
    # Validate environment
    case "$ENVIRONMENT" in
        "dev"|"development")
            ENVIRONMENT="development"
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.dev.yml"
            ;;
        "staging")
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.staging.yml"
            ;;
        "prod"|"production")
            ENVIRONMENT="production"
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.prod.yml"
            ;;
        "all")
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.staging.yml -f docker-compose.prod.yml"
            ;;
        *)
            log "ERROR" "Invalid environment: $ENVIRONMENT"
            log "INFO" "Valid environments: dev, development, staging, prod, production, all"
            exit 1
            ;;
    esac
    
    # Set environment variables
    export CERTRATS_ENV="$ENVIRONMENT"
    export COMPOSE_PROJECT_NAME="$COMPOSE_PROJECT_NAME"
    
    log "INFO" "✅ Environment setup complete"
}

backup_data() {
    if [[ "$BACKUP_DATA" != "true" ]]; then
        return
    fi
    
    log "INFO" "💾 Creating data backup..."
    
    local backup_dir="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    cd "$PROJECT_ROOT"
    
    # Backup database
    if docker-compose $COMPOSE_FILES ps database | grep -q "Up"; then
        log "DEBUG" "Backing up PostgreSQL database..."
        docker-compose $COMPOSE_FILES exec -T database pg_dumpall -U certrats > "$backup_dir/database.sql" || {
            log "WARN" "Failed to backup database"
        }
    fi
    
    # Backup Redis data
    if docker-compose $COMPOSE_FILES ps redis | grep -q "Up"; then
        log "DEBUG" "Backing up Redis data..."
        docker-compose $COMPOSE_FILES exec -T redis redis-cli --rdb - > "$backup_dir/redis.rdb" || {
            log "WARN" "Failed to backup Redis data"
        }
    fi
    
    # Backup MinIO data (if accessible)
    if docker-compose $COMPOSE_FILES ps storage | grep -q "Up"; then
        log "DEBUG" "Backing up MinIO data..."
        # Note: This would require MinIO client (mc) to be configured
        log "DEBUG" "MinIO backup requires manual configuration with mc client"
    fi
    
    # Backup volumes
    log "DEBUG" "Backing up Docker volumes..."
    local volumes=$(docker volume ls --filter name=certrats_ --format "{{.Name}}")
    for volume in $volumes; do
        log "DEBUG" "Backing up volume: $volume"
        docker run --rm -v "$volume":/data -v "$backup_dir":/backup alpine tar czf "/backup/${volume}.tar.gz" -C /data . || {
            log "WARN" "Failed to backup volume: $volume"
        }
    done
    
    log "INFO" "✅ Backup completed: $backup_dir"
}

stop_services() {
    log "INFO" "🛑 Stopping CertRats services..."
    
    cd "$PROJECT_ROOT"
    
    # Check if any services are running
    local running_services=$(docker-compose $COMPOSE_FILES ps --services --filter "status=running" 2>/dev/null || echo "")
    
    if [[ -z "$running_services" ]]; then
        log "INFO" "No running services found"
        return
    fi
    
    log "DEBUG" "Running services: $running_services"
    
    if [[ "$FORCE_STOP" == "true" ]]; then
        log "WARN" "Force stopping services..."
        docker-compose $COMPOSE_FILES kill || {
            log "ERROR" "Failed to force stop services"
            exit 1
        }
    else
        log "INFO" "Gracefully stopping services..."
        docker-compose $COMPOSE_FILES down --timeout 30 || {
            log "ERROR" "Failed to stop services gracefully"
            exit 1
        }
    fi
    
    log "INFO" "✅ Services stopped successfully"
}

stop_traefik() {
    if [[ "$STOP_TRAEFIK" != "true" ]]; then
        return
    fi
    
    log "INFO" "🌐 Stopping Traefik proxy..."
    
    cd "$PROJECT_ROOT"
    
    if docker-compose -f docker-compose.traefik.yml ps traefik | grep -q "Up"; then
        docker-compose -f docker-compose.traefik.yml down || {
            log "WARN" "Failed to stop Traefik proxy"
        }
    else
        log "DEBUG" "Traefik proxy is not running"
    fi
    
    log "INFO" "✅ Traefik proxy stopped"
}

cleanup_volumes() {
    if [[ "$CLEANUP_VOLUMES" != "true" ]]; then
        return
    fi
    
    log "WARN" "🗑️  Cleaning up volumes..."
    log "WARN" "This will permanently delete all data!"
    
    # Confirm deletion in production
    if [[ "$ENVIRONMENT" == "production" ]]; then
        read -p "Are you sure you want to delete production data? (yes/no): " confirm
        if [[ "$confirm" != "yes" ]]; then
            log "INFO" "Volume cleanup cancelled"
            return
        fi
    fi
    
    # Remove project volumes
    local volumes=$(docker volume ls --filter name=certrats_ --format "{{.Name}}")
    for volume in $volumes; do
        log "DEBUG" "Removing volume: $volume"
        docker volume rm "$volume" 2>/dev/null || {
            log "WARN" "Failed to remove volume: $volume"
        }
    done
    
    log "INFO" "✅ Volumes cleaned up"
}

cleanup_networks() {
    if [[ "$CLEANUP_NETWORKS" != "true" ]]; then
        return
    fi
    
    log "INFO" "🌐 Cleaning up networks..."
    
    # Remove project networks
    local networks=$(docker network ls --filter name=certrats_ --format "{{.Name}}")
    for network in $networks; do
        log "DEBUG" "Removing network: $network"
        docker network rm "$network" 2>/dev/null || {
            log "WARN" "Failed to remove network: $network"
        }
    done
    
    log "INFO" "✅ Networks cleaned up"
}

cleanup_images() {
    if [[ "$CLEANUP_VOLUMES" != "true" ]]; then
        return
    fi
    
    log "INFO" "🖼️  Cleaning up images..."
    
    # Remove project images
    local images=$(docker images --filter reference="certrats*" --format "{{.Repository}}:{{.Tag}}")
    for image in $images; do
        log "DEBUG" "Removing image: $image"
        docker rmi "$image" 2>/dev/null || {
            log "WARN" "Failed to remove image: $image"
        }
    done
    
    # Clean up dangling images
    docker image prune -f &>/dev/null || true
    
    log "INFO" "✅ Images cleaned up"
}

show_status() {
    log "INFO" "📊 Final status check..."
    
    cd "$PROJECT_ROOT"
    
    # Check running containers
    local running_containers=$(docker ps --filter name=certrats_ --format "{{.Names}}" | wc -l)
    log "INFO" "Running CertRats containers: $running_containers"
    
    # Check volumes
    local volumes=$(docker volume ls --filter name=certrats_ --format "{{.Name}}" | wc -l)
    log "INFO" "CertRats volumes: $volumes"
    
    # Check networks
    local networks=$(docker network ls --filter name=certrats_ --format "{{.Name}}" | wc -l)
    log "INFO" "CertRats networks: $networks"
    
    if [[ $running_containers -eq 0 ]]; then
        log "INFO" "✅ All CertRats services are stopped"
    else
        log "WARN" "Some containers are still running"
        docker ps --filter name=certrats_ --format "table {{.Names}}\t{{.Status}}"
    fi
}

# =============================================================================
# ARGUMENT PARSING
# =============================================================================

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose|-v)
                VERBOSE=true
                shift
                ;;
            --backup)
                BACKUP_DATA=true
                shift
                ;;
            --cleanup)
                CLEANUP_VOLUMES=true
                CLEANUP_NETWORKS=true
                shift
                ;;
            --force)
                FORCE_STOP=true
                shift
                ;;
            --stop-traefik)
                STOP_TRAEFIK=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                if [[ -z "${ENVIRONMENT_SET:-}" ]]; then
                    ENVIRONMENT="$1"
                    ENVIRONMENT_SET=true
                else
                    log "ERROR" "Unknown option: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    # Parse command line arguments
    parse_arguments "$@"
    
    # Show banner
    echo -e "${RED}"
    cat << "EOF"
   ____          _   ____       _       
  / ___|___ _ __| |_|  _ \ __ _| |_ ___ 
 | |   / _ \ '__| __| |_) / _` | __/ __|
 | |__|  __/ |  | |_|  _ < (_| | |_\__ \
  \____\___|_|   \__|_| \_\__,_|\__|___/
                                       
  Certification Platform Docker Manager
EOF
    echo -e "${NC}"
    
    log "INFO" "🛑 Stopping CertRats platform ($ENVIRONMENT)..."
    
    # Execute shutdown sequence
    setup_environment
    backup_data
    stop_services
    stop_traefik
    cleanup_volumes
    cleanup_networks
    cleanup_images
    show_status
    
    log "INFO" "🎉 CertRats platform stopped successfully!"
    log "INFO" "📝 Use './scripts/docker/start-certrats.sh' to start the platform again"
}

# Run main function with all arguments
main "$@"
