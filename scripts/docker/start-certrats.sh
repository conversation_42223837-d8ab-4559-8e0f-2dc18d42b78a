#!/bin/bash

# CertRats Platform Startup Script
#
# This script provides a comprehensive startup solution for the CertRats
# certification platform with Docker and Traefik integration.
#
# Features:
# - Environment-specific deployment (development, staging, production)
# - Dependency checking and validation
# - Service health monitoring
# - Automatic service discovery
# - Comprehensive logging and error handling
# - Integration with ServiceURLManager
#
# Usage:
#   ./scripts/docker/start-certrats.sh [environment] [options]
#
# Examples:
#   ./scripts/docker/start-certrats.sh dev
#   ./scripts/docker/start-certrats.sh prod --no-build
#   ./scripts/docker/start-certrats.sh staging --verbose

set -euo pipefail

# =============================================================================
# CONFIGURATION AND CONSTANTS
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
COMPOSE_PROJECT_NAME="certrats"

# Default values
ENVIRONMENT="${1:-development}"
VERBOSE=false
NO_BUILD=false
FORCE_RECREATE=false
SKIP_HEALTH_CHECK=false
TIMEOUT=300

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${GREEN}[${timestamp}] INFO:${NC} $message" ;;
        "WARN")  echo -e "${YELLOW}[${timestamp}] WARN:${NC} $message" ;;
        "ERROR") echo -e "${RED}[${timestamp}] ERROR:${NC} $message" ;;
        "DEBUG") [[ "$VERBOSE" == "true" ]] && echo -e "${BLUE}[${timestamp}] DEBUG:${NC} $message" ;;
        *)       echo -e "${CYAN}[${timestamp}] $level:${NC} $message" ;;
    esac
}

show_usage() {
    cat << EOF
🐳 CertRats Platform Startup Script

Usage: $0 [environment] [options]

Environments:
  dev, development    Start development environment with hot reload
  staging            Start staging environment with SSL
  prod, production   Start production environment with full security

Options:
  --verbose, -v      Enable verbose logging
  --no-build         Skip building images
  --force-recreate   Force recreate all containers
  --skip-health      Skip health checks
  --timeout SECONDS  Health check timeout (default: 300)
  --help, -h         Show this help message

Examples:
  $0 dev                    # Start development environment
  $0 prod --no-build        # Start production without rebuilding
  $0 staging --verbose      # Start staging with verbose output

Environment Variables:
  CERTRATS_ENV              Override environment detection
  COMPOSE_PROJECT_NAME      Override Docker Compose project name
  TRAEFIK_NETWORK          Override Traefik network name

EOF
}

check_dependencies() {
    log "INFO" "🔍 Checking dependencies..."
    
    local missing_deps=()
    
    # Check required commands
    local required_commands=("docker" "docker-compose" "curl" "jq")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log "ERROR" "Missing required dependencies: ${missing_deps[*]}"
        log "INFO" "Please install the missing dependencies and try again."
        exit 1
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        log "ERROR" "Docker daemon is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check Docker Compose version
    local compose_version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log "DEBUG" "Docker Compose version: $compose_version"
    
    log "INFO" "✅ All dependencies are available"
}

setup_environment() {
    log "INFO" "🔧 Setting up environment: $ENVIRONMENT"
    
    # Validate environment
    case "$ENVIRONMENT" in
        "dev"|"development")
            ENVIRONMENT="development"
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.dev.yml"
            ;;
        "staging")
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.staging.yml"
            ;;
        "prod"|"production")
            ENVIRONMENT="production"
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.prod.yml"
            ;;
        *)
            log "ERROR" "Invalid environment: $ENVIRONMENT"
            log "INFO" "Valid environments: dev, development, staging, prod, production"
            exit 1
            ;;
    esac
    
    # Set environment variables
    export CERTRATS_ENV="$ENVIRONMENT"
    export COMPOSE_PROJECT_NAME="$COMPOSE_PROJECT_NAME"
    
    # Check for .env file
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        log "WARN" "No .env file found. Creating from .env.example..."
        if [[ -f "$PROJECT_ROOT/.env.example" ]]; then
            cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/.env"
            log "INFO" "✅ Created .env file from template"
            log "WARN" "⚠️  Please review and update .env file with your configuration"
        else
            log "ERROR" "No .env.example file found. Cannot create .env file."
            exit 1
        fi
    fi
    
    log "INFO" "✅ Environment setup complete"
}

check_traefik() {
    log "INFO" "🌐 Checking Traefik network..."

    local traefik_network="${TRAEFIK_NETWORK:-traefik_network}"

    # Check if Traefik network exists
    if ! docker network ls | grep -q "$traefik_network"; then
        log "INFO" "Creating Traefik network for service registration..."
        docker network create "$traefik_network" || {
            log "ERROR" "Failed to create Traefik network"
            exit 1
        }
    fi

    # Assume Traefik is running on host - just verify network connectivity
    log "INFO" "Assuming Traefik is running on host - registering services with existing Traefik"
    log "INFO" "✅ Traefik network ready for service registration"
}

build_images() {
    if [[ "$NO_BUILD" == "true" ]]; then
        log "INFO" "⏭️  Skipping image build"
        return
    fi
    
    log "INFO" "🔨 Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    local build_args=""
    if [[ "$FORCE_RECREATE" == "true" ]]; then
        build_args="--force-recreate"
    fi
    
    docker-compose $COMPOSE_FILES build $build_args || {
        log "ERROR" "Failed to build Docker images"
        exit 1
    }
    
    log "INFO" "✅ Docker images built successfully"
}

start_services() {
    log "INFO" "🚀 Starting CertRats services..."
    
    cd "$PROJECT_ROOT"
    
    local up_args="-d"
    if [[ "$FORCE_RECREATE" == "true" ]]; then
        up_args="$up_args --force-recreate"
    fi
    
    docker-compose $COMPOSE_FILES up $up_args || {
        log "ERROR" "Failed to start services"
        exit 1
    }
    
    log "INFO" "✅ Services started successfully"
}

wait_for_services() {
    if [[ "$SKIP_HEALTH_CHECK" == "true" ]]; then
        log "INFO" "⏭️  Skipping health checks"
        return
    fi
    
    log "INFO" "🏥 Waiting for services to be healthy..."
    
    local start_time=$(date +%s)
    local healthy_services=()
    local unhealthy_services=()
    
    # Get list of services
    local services=($(docker-compose $COMPOSE_FILES config --services))
    
    for service in "${services[@]}"; do
        log "DEBUG" "Checking health of service: $service"
        
        local service_healthy=false
        local attempts=0
        local max_attempts=$((TIMEOUT / 5))
        
        while [[ $attempts -lt $max_attempts ]]; do
            if docker-compose $COMPOSE_FILES ps "$service" | grep -q "healthy\|Up"; then
                service_healthy=true
                break
            fi
            
            sleep 5
            ((attempts++))
            
            local elapsed=$(($(date +%s) - start_time))
            if [[ $elapsed -gt $TIMEOUT ]]; then
                log "WARN" "Health check timeout reached"
                break
            fi
        done
        
        if [[ "$service_healthy" == "true" ]]; then
            healthy_services+=("$service")
            log "DEBUG" "✅ $service is healthy"
        else
            unhealthy_services+=("$service")
            log "WARN" "❌ $service is not healthy"
        fi
    done
    
    # Report results
    log "INFO" "Health check results:"
    log "INFO" "  Healthy services (${#healthy_services[@]}): ${healthy_services[*]}"
    
    if [[ ${#unhealthy_services[@]} -gt 0 ]]; then
        log "WARN" "  Unhealthy services (${#unhealthy_services[@]}): ${unhealthy_services[*]}"
        log "WARN" "Some services may not be fully ready. Check logs for details."
    else
        log "INFO" "✅ All services are healthy"
    fi
}

run_health_checks() {
    if [[ "$SKIP_HEALTH_CHECK" == "true" ]]; then
        return
    fi
    
    log "INFO" "🔍 Running comprehensive health checks..."
    
    # Run health check script if available
    local health_script="$PROJECT_ROOT/scripts/test-health-checks.py"
    if [[ -f "$health_script" ]]; then
        log "DEBUG" "Running health check script..."
        python3 "$health_script" --environment "$ENVIRONMENT" --format human || {
            log "WARN" "Health check script reported issues"
        }
    else
        log "DEBUG" "Health check script not found, skipping detailed checks"
    fi
}

show_service_urls() {
    log "INFO" "🌐 Service URLs for $ENVIRONMENT environment:"
    
    # Use ServiceURLManager if available
    local url_script="$PROJECT_ROOT/utils/service_urls.py"
    if [[ -f "$url_script" ]]; then
        python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from utils.service_urls import ServiceURLManager

try:
    manager = ServiceURLManager('$ENVIRONMENT')
    urls = manager.get_all_service_urls()
    
    print()
    for service, url in urls.items():
        print(f'  {service:20} {url}')
    print()
except Exception as e:
    print(f'Error getting service URLs: {e}')
"
    else
        # Fallback to basic URLs
        case "$ENVIRONMENT" in
            "development")
                echo "  Frontend:            http://app.certrats.localhost"
                echo "  API:                 http://api.certrats.localhost"
                echo "  Traefik Dashboard:   http://traefik.certrats.localhost"
                echo "  Grafana:             http://grafana.certrats.localhost"
                echo "  Prometheus:          http://metrics.certrats.localhost"
                ;;
            "staging")
                echo "  Frontend:            https://app.staging.certrats.dev"
                echo "  API:                 https://api.staging.certrats.dev"
                ;;
            "production")
                echo "  Frontend:            https://app.certrats.com"
                echo "  API:                 https://api.certrats.com"
                ;;
        esac
    fi
}

cleanup_on_exit() {
    log "INFO" "🧹 Cleaning up..."
    # Add any cleanup tasks here
}

# =============================================================================
# ARGUMENT PARSING
# =============================================================================

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose|-v)
                VERBOSE=true
                shift
                ;;
            --no-build)
                NO_BUILD=true
                shift
                ;;
            --force-recreate)
                FORCE_RECREATE=true
                shift
                ;;
            --skip-health)
                SKIP_HEALTH_CHECK=true
                shift
                ;;
            --timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                if [[ -z "${ENVIRONMENT_SET:-}" ]]; then
                    ENVIRONMENT="$1"
                    ENVIRONMENT_SET=true
                else
                    log "ERROR" "Unknown option: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    # Set up signal handlers
    trap cleanup_on_exit EXIT
    
    # Parse command line arguments
    parse_arguments "$@"
    
    # Show banner
    echo -e "${PURPLE}"
    cat << "EOF"
   ____          _   ____       _       
  / ___|___ _ __| |_|  _ \ __ _| |_ ___ 
 | |   / _ \ '__| __| |_) / _` | __/ __|
 | |__|  __/ |  | |_|  _ < (_| | |_\__ \
  \____\___|_|   \__|_| \_\__,_|\__|___/
                                       
  Certification Platform Docker Manager
EOF
    echo -e "${NC}"
    
    log "INFO" "🚀 Starting CertRats platform in $ENVIRONMENT mode..."
    
    # Execute startup sequence
    check_dependencies
    setup_environment
    check_traefik
    build_images
    start_services
    wait_for_services
    run_health_checks
    show_service_urls
    
    log "INFO" "🎉 CertRats platform is ready!"
    log "INFO" "📝 Use './scripts/docker/stop-certrats.sh' to stop the platform"
    log "INFO" "📊 Use './scripts/docker/status-certrats.sh' to check status"
}

# Run main function with all arguments
main "$@"
