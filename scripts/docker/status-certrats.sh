#!/bin/bash

# CertRats Platform Status Script
#
# This script provides comprehensive status monitoring for the CertRats
# certification platform with detailed service information and health checks.
#
# Features:
# - Real-time service status monitoring
# - Resource usage statistics
# - Health check validation
# - Service URL display
# - Log analysis and error detection
# - Performance metrics
#
# Usage:
#   ./scripts/docker/status-certrats.sh [environment] [options]
#
# Examples:
#   ./scripts/docker/status-certrats.sh dev
#   ./scripts/docker/status-certrats.sh prod --detailed
#   ./scripts/docker/status-certrats.sh staging --watch

set -euo pipefail

# =============================================================================
# CONFIGURATION AND CONSTANTS
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
COMPOSE_PROJECT_NAME="certrats"

# Default values
ENVIRONMENT="${1:-development}"
VERBOSE=false
DETAILED=false
WATCH_MODE=false
SHOW_LOGS=false
JSON_OUTPUT=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${GREEN}[${timestamp}] INFO:${NC} $message" ;;
        "WARN")  echo -e "${YELLOW}[${timestamp}] WARN:${NC} $message" ;;
        "ERROR") echo -e "${RED}[${timestamp}] ERROR:${NC} $message" ;;
        "DEBUG") [[ "$VERBOSE" == "true" ]] && echo -e "${BLUE}[${timestamp}] DEBUG:${NC} $message" ;;
        *)       echo -e "${CYAN}[${timestamp}] $level:${NC} $message" ;;
    esac
}

show_usage() {
    cat << EOF
📊 CertRats Platform Status Script

Usage: $0 [environment] [options]

Environments:
  dev, development    Check development environment
  staging            Check staging environment
  prod, production   Check production environment
  all                Check all environments

Options:
  --verbose, -v      Enable verbose logging
  --detailed, -d     Show detailed service information
  --watch, -w        Watch mode (refresh every 5 seconds)
  --logs, -l         Show recent logs for each service
  --json, -j         Output in JSON format
  --help, -h         Show this help message

Examples:
  $0 dev                    # Basic status check
  $0 prod --detailed        # Detailed production status
  $0 staging --watch        # Watch staging environment

Environment Variables:
  CERTRATS_ENV              Override environment detection
  COMPOSE_PROJECT_NAME      Override Docker Compose project name

EOF
}

setup_environment() {
    # Validate environment
    case "$ENVIRONMENT" in
        "dev"|"development")
            ENVIRONMENT="development"
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.dev.yml"
            ;;
        "staging")
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.staging.yml"
            ;;
        "prod"|"production")
            ENVIRONMENT="production"
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.prod.yml"
            ;;
        "all")
            COMPOSE_FILES="-f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.staging.yml -f docker-compose.prod.yml"
            ;;
        *)
            log "ERROR" "Invalid environment: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    # Set environment variables
    export CERTRATS_ENV="$ENVIRONMENT"
    export COMPOSE_PROJECT_NAME="$COMPOSE_PROJECT_NAME"
}

get_service_status() {
    local service="$1"
    local container_name="certrats_$service"
    
    if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        local status=$(docker inspect --format='{{.State.Status}}' "$container_name" 2>/dev/null || echo "unknown")
        local health=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")
        
        case "$status" in
            "running")
                if [[ "$health" == "healthy" ]]; then
                    echo "🟢 healthy"
                elif [[ "$health" == "unhealthy" ]]; then
                    echo "🔴 unhealthy"
                elif [[ "$health" == "starting" ]]; then
                    echo "🟡 starting"
                else
                    echo "🟢 running"
                fi
                ;;
            "exited")
                echo "🔴 stopped"
                ;;
            "restarting")
                echo "🟡 restarting"
                ;;
            *)
                echo "⚪ $status"
                ;;
        esac
    else
        echo "⚫ not found"
    fi
}

get_service_uptime() {
    local service="$1"
    local container_name="certrats_$service"
    
    if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        local started=$(docker inspect --format='{{.State.StartedAt}}' "$container_name" 2>/dev/null || echo "")
        if [[ -n "$started" ]]; then
            local start_timestamp=$(date -d "$started" +%s 2>/dev/null || echo "0")
            local current_timestamp=$(date +%s)
            local uptime_seconds=$((current_timestamp - start_timestamp))
            
            if [[ $uptime_seconds -gt 86400 ]]; then
                echo "$((uptime_seconds / 86400))d $((uptime_seconds % 86400 / 3600))h"
            elif [[ $uptime_seconds -gt 3600 ]]; then
                echo "$((uptime_seconds / 3600))h $((uptime_seconds % 3600 / 60))m"
            else
                echo "$((uptime_seconds / 60))m $((uptime_seconds % 60))s"
            fi
        else
            echo "unknown"
        fi
    else
        echo "n/a"
    fi
}

get_service_resources() {
    local service="$1"
    local container_name="certrats_$service"
    
    if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        local stats=$(docker stats --no-stream --format "{{.CPUPerc}},{{.MemUsage}}" "$container_name" 2>/dev/null || echo "n/a,n/a")
        echo "$stats"
    else
        echo "n/a,n/a"
    fi
}

show_basic_status() {
    echo -e "${CYAN}📊 CertRats Platform Status - $ENVIRONMENT${NC}"
    echo "============================================================"
    
    cd "$PROJECT_ROOT"
    
    # Get list of services
    local services=($(docker-compose $COMPOSE_FILES config --services 2>/dev/null || echo ""))
    
    if [[ ${#services[@]} -eq 0 ]]; then
        echo "No services configured for environment: $ENVIRONMENT"
        return
    fi
    
    printf "%-20s %-15s %-10s\n" "SERVICE" "STATUS" "UPTIME"
    echo "----------------------------------------"
    
    for service in "${services[@]}"; do
        local status=$(get_service_status "$service")
        local uptime=$(get_service_uptime "$service")
        
        printf "%-20s %-15s %-10s\n" "$service" "$status" "$uptime"
    done
    
    echo
}

show_detailed_status() {
    echo -e "${CYAN}📊 CertRats Platform Detailed Status - $ENVIRONMENT${NC}"
    echo "================================================================================"
    
    cd "$PROJECT_ROOT"
    
    # Get list of services
    local services=($(docker-compose $COMPOSE_FILES config --services 2>/dev/null || echo ""))
    
    if [[ ${#services[@]} -eq 0 ]]; then
        echo "No services configured for environment: $ENVIRONMENT"
        return
    fi
    
    printf "%-15s %-12s %-10s %-15s %-15s\n" "SERVICE" "STATUS" "UPTIME" "CPU" "MEMORY"
    echo "------------------------------------------------------------------------"
    
    for service in "${services[@]}"; do
        local status=$(get_service_status "$service")
        local uptime=$(get_service_uptime "$service")
        local resources=$(get_service_resources "$service")
        local cpu=$(echo "$resources" | cut -d',' -f1)
        local memory=$(echo "$resources" | cut -d',' -f2)
        
        printf "%-15s %-12s %-10s %-15s %-15s\n" "$service" "$status" "$uptime" "$cpu" "$memory"
    done
    
    echo
    
    # Show additional information
    show_network_status
    show_volume_status
    show_service_urls
}

show_network_status() {
    echo -e "${BLUE}🌐 Network Status${NC}"
    echo "----------------"
    
    local networks=$(docker network ls --filter name=certrats --format "{{.Name}}")
    for network in $networks; do
        local containers=$(docker network inspect "$network" --format '{{range .Containers}}{{.Name}} {{end}}' 2>/dev/null || echo "")
        echo "  $network: $containers"
    done
    echo
}

show_volume_status() {
    echo -e "${PURPLE}💾 Volume Status${NC}"
    echo "---------------"
    
    local volumes=$(docker volume ls --filter name=certrats --format "{{.Name}}")
    for volume in $volumes; do
        local size=$(docker system df -v | grep "$volume" | awk '{print $3}' || echo "unknown")
        echo "  $volume: $size"
    done
    echo
}

show_service_urls() {
    echo -e "${GREEN}🌐 Service URLs${NC}"
    echo "--------------"
    
    # Use ServiceURLManager if available
    local url_script="$PROJECT_ROOT/utils/service_urls.py"
    if [[ -f "$url_script" ]]; then
        python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from utils.service_urls import ServiceURLManager

try:
    manager = ServiceURLManager('$ENVIRONMENT')
    urls = manager.get_all_service_urls()
    
    for service, url in urls.items():
        print(f'  {service:20} {url}')
except Exception as e:
    print(f'  Error getting service URLs: {e}')
"
    else
        echo "  ServiceURLManager not available"
    fi
    echo
}

show_recent_logs() {
    if [[ "$SHOW_LOGS" != "true" ]]; then
        return
    fi
    
    echo -e "${YELLOW}📝 Recent Logs${NC}"
    echo "-------------"
    
    cd "$PROJECT_ROOT"
    
    local services=($(docker-compose $COMPOSE_FILES config --services 2>/dev/null || echo ""))
    
    for service in "${services[@]}"; do
        local container_name="certrats_$service"
        
        if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
            echo -e "${CYAN}Logs for $service:${NC}"
            docker logs --tail 5 "$container_name" 2>&1 | sed 's/^/  /'
            echo
        fi
    done
}

run_health_checks() {
    echo -e "${GREEN}🏥 Health Checks${NC}"
    echo "---------------"
    
    # Run health check script if available
    local health_script="$PROJECT_ROOT/scripts/test-health-checks.py"
    if [[ -f "$health_script" ]]; then
        python3 "$health_script" --environment "$ENVIRONMENT" --format human 2>/dev/null || {
            echo "  Health check script failed"
        }
    else
        echo "  Health check script not available"
    fi
    echo
}

show_json_status() {
    cd "$PROJECT_ROOT"
    
    local services=($(docker-compose $COMPOSE_FILES config --services 2>/dev/null || echo ""))
    
    echo "{"
    echo "  \"environment\": \"$ENVIRONMENT\","
    echo "  \"timestamp\": \"$(date -Iseconds)\","
    echo "  \"services\": {"
    
    local first=true
    for service in "${services[@]}"; do
        if [[ "$first" != "true" ]]; then
            echo ","
        fi
        first=false
        
        local status=$(get_service_status "$service" | sed 's/[🟢🔴🟡⚪⚫] //')
        local uptime=$(get_service_uptime "$service")
        local resources=$(get_service_resources "$service")
        local cpu=$(echo "$resources" | cut -d',' -f1)
        local memory=$(echo "$resources" | cut -d',' -f2)
        
        echo -n "    \"$service\": {"
        echo -n "\"status\": \"$status\", "
        echo -n "\"uptime\": \"$uptime\", "
        echo -n "\"cpu\": \"$cpu\", "
        echo -n "\"memory\": \"$memory\""
        echo -n "}"
    done
    
    echo ""
    echo "  }"
    echo "}"
}

watch_status() {
    while true; do
        clear
        if [[ "$DETAILED" == "true" ]]; then
            show_detailed_status
        else
            show_basic_status
        fi
        
        echo "Press Ctrl+C to exit watch mode"
        sleep 5
    done
}

# =============================================================================
# ARGUMENT PARSING
# =============================================================================

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose|-v)
                VERBOSE=true
                shift
                ;;
            --detailed|-d)
                DETAILED=true
                shift
                ;;
            --watch|-w)
                WATCH_MODE=true
                shift
                ;;
            --logs|-l)
                SHOW_LOGS=true
                shift
                ;;
            --json|-j)
                JSON_OUTPUT=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                if [[ -z "${ENVIRONMENT_SET:-}" ]]; then
                    ENVIRONMENT="$1"
                    ENVIRONMENT_SET=true
                else
                    log "ERROR" "Unknown option: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    # Parse command line arguments
    parse_arguments "$@"
    
    # Setup environment
    setup_environment
    
    if [[ "$JSON_OUTPUT" == "true" ]]; then
        show_json_status
    elif [[ "$WATCH_MODE" == "true" ]]; then
        watch_status
    else
        if [[ "$DETAILED" == "true" ]]; then
            show_detailed_status
        else
            show_basic_status
        fi
        
        show_recent_logs
        run_health_checks
    fi
}

# Run main function with all arguments
main "$@"
