#!/usr/bin/env python3
"""Seed career transition data with comprehensive role definitions and pathways.

This script populates the database with realistic career roles and transition paths
for cybersecurity, cloud computing, and related technology domains.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from sqlalchemy.orm import Session
from database import SessionLocal
from models.career_transition import CareerRole, CareerTransitionPath
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_cybersecurity_roles(db: Session) -> dict:
    """Create cybersecurity career roles."""
    roles = {}
    
    # Entry Level Roles
    roles['security_analyst_entry'] = CareerRole(
        title="Junior Security Analyst",
        description="Entry-level cybersecurity role focusing on monitoring and incident response",
        domain="Cybersecurity",
        level="Entry",
        min_years_experience=0,
        max_years_experience=2,
        salary_min=45000,
        salary_max=65000,
        required_skills=["Network Security", "Incident Response", "SIEM Tools", "Basic Scripting"],
        preferred_skills=["Python", "Linux", "Windows Security", "Threat Intelligence"],
        required_certifications=["Security+"],
        preferred_certifications=["Network+", "CySA+"],
        market_demand="High",
        growth_outlook="Growing"
    )
    
    roles['it_support_specialist'] = CareerRole(
        title="IT Support Specialist",
        description="Technical support role with security awareness responsibilities",
        domain="Information Technology",
        level="Entry",
        min_years_experience=0,
        max_years_experience=3,
        salary_min=35000,
        salary_max=50000,
        required_skills=["Help Desk", "Windows Administration", "Network Troubleshooting"],
        preferred_skills=["Active Directory", "Basic Security", "Hardware Troubleshooting"],
        required_certifications=["A+"],
        preferred_certifications=["Network+", "Security+"],
        market_demand="Medium",
        growth_outlook="Stable"
    )
    
    # Mid-Level Roles
    roles['security_analyst_mid'] = CareerRole(
        title="Security Analyst",
        description="Mid-level security analyst with specialized skills in threat detection",
        domain="Cybersecurity",
        level="Mid",
        min_years_experience=2,
        max_years_experience=5,
        salary_min=65000,
        salary_max=85000,
        required_skills=["Advanced SIEM", "Threat Hunting", "Malware Analysis", "Scripting"],
        preferred_skills=["Python", "PowerShell", "Forensics", "Vulnerability Assessment"],
        required_certifications=["Security+", "CySA+"],
        preferred_certifications=["GCIH", "GSEC", "CEH"],
        market_demand="Very High",
        growth_outlook="Booming"
    )
    
    roles['network_security_engineer'] = CareerRole(
        title="Network Security Engineer",
        description="Specialized role focusing on network security architecture and implementation",
        domain="Cybersecurity",
        level="Mid",
        min_years_experience=3,
        max_years_experience=7,
        salary_min=70000,
        salary_max=95000,
        required_skills=["Firewall Management", "VPN", "Network Architecture", "Security Protocols"],
        preferred_skills=["Cisco Security", "Palo Alto", "Network Automation", "Cloud Security"],
        required_certifications=["Security+", "Network+"],
        preferred_certifications=["CCNA Security", "CISSP"],
        market_demand="High",
        growth_outlook="Growing"
    )
    
    roles['penetration_tester'] = CareerRole(
        title="Penetration Tester",
        description="Ethical hacker specializing in vulnerability assessment and penetration testing",
        domain="Cybersecurity",
        level="Mid",
        min_years_experience=2,
        max_years_experience=6,
        salary_min=75000,
        salary_max=110000,
        required_skills=["Penetration Testing", "Vulnerability Assessment", "Exploit Development"],
        preferred_skills=["Metasploit", "Burp Suite", "Custom Scripting", "Web Application Security"],
        required_certifications=["CEH", "Security+"],
        preferred_certifications=["OSCP", "GPEN", "CISSP"],
        market_demand="Very High",
        growth_outlook="Booming"
    )
    
    # Senior Level Roles
    roles['security_architect'] = CareerRole(
        title="Security Architect",
        description="Senior role designing and implementing enterprise security architecture",
        domain="Cybersecurity",
        level="Senior",
        min_years_experience=5,
        max_years_experience=10,
        salary_min=100000,
        salary_max=140000,
        required_skills=["Security Architecture", "Risk Assessment", "Compliance", "Leadership"],
        preferred_skills=["Cloud Security", "Zero Trust", "DevSecOps", "Enterprise Architecture"],
        required_certifications=["CISSP", "Security+"],
        preferred_certifications=["SABSA", "TOGAF", "CISSP"],
        market_demand="High",
        growth_outlook="Growing"
    )
    
    roles['incident_response_manager'] = CareerRole(
        title="Incident Response Manager",
        description="Senior role leading incident response teams and security operations",
        domain="Cybersecurity",
        level="Senior",
        min_years_experience=5,
        max_years_experience=12,
        salary_min=95000,
        salary_max=130000,
        required_skills=["Incident Response", "Team Leadership", "Crisis Management", "Forensics"],
        preferred_skills=["DFIR", "Threat Intelligence", "Security Orchestration", "Communication"],
        required_certifications=["CISSP", "GCIH"],
        preferred_certifications=["GCFA", "GNFA", "CISM"],
        market_demand="High",
        growth_outlook="Growing"
    )
    
    # Executive Level Roles
    roles['ciso'] = CareerRole(
        title="Chief Information Security Officer (CISO)",
        description="Executive role responsible for enterprise-wide cybersecurity strategy",
        domain="Cybersecurity",
        level="Executive",
        min_years_experience=10,
        max_years_experience=20,
        salary_min=150000,
        salary_max=300000,
        required_skills=["Strategic Planning", "Risk Management", "Executive Communication", "Budget Management"],
        preferred_skills=["Board Reporting", "Regulatory Compliance", "Vendor Management", "Crisis Leadership"],
        required_certifications=["CISSP", "CISM"],
        preferred_certifications=["CISSP", "CISM", "CRISC"],
        market_demand="Medium",
        growth_outlook="Stable"
    )
    
    # Add all roles to database
    for role_key, role in roles.items():
        db.add(role)
    
    db.flush()  # Get IDs
    return roles


def create_cloud_computing_roles(db: Session) -> dict:
    """Create cloud computing career roles."""
    roles = {}
    
    # Entry Level
    roles['cloud_support_associate'] = CareerRole(
        title="Cloud Support Associate",
        description="Entry-level cloud support role with basic AWS/Azure knowledge",
        domain="Cloud Computing",
        level="Entry",
        min_years_experience=0,
        max_years_experience=2,
        salary_min=40000,
        salary_max=60000,
        required_skills=["Cloud Basics", "Customer Support", "Basic Networking", "Troubleshooting"],
        preferred_skills=["AWS", "Azure", "Linux", "Scripting"],
        required_certifications=["Cloud Practitioner"],
        preferred_certifications=["AWS Solutions Architect Associate", "Azure Fundamentals"],
        market_demand="High",
        growth_outlook="Booming"
    )
    
    # Mid Level
    roles['cloud_engineer'] = CareerRole(
        title="Cloud Engineer",
        description="Mid-level role implementing and managing cloud infrastructure",
        domain="Cloud Computing",
        level="Mid",
        min_years_experience=2,
        max_years_experience=5,
        salary_min=70000,
        salary_max=100000,
        required_skills=["Cloud Architecture", "Infrastructure as Code", "DevOps", "Automation"],
        preferred_skills=["Terraform", "Kubernetes", "CI/CD", "Monitoring"],
        required_certifications=["AWS Solutions Architect Associate"],
        preferred_certifications=["AWS DevOps Professional", "Azure Solutions Architect"],
        market_demand="Very High",
        growth_outlook="Booming"
    )
    
    roles['devops_engineer'] = CareerRole(
        title="DevOps Engineer",
        description="Role bridging development and operations with cloud-native practices",
        domain="Cloud Computing",
        level="Mid",
        min_years_experience=3,
        max_years_experience=7,
        salary_min=75000,
        salary_max=110000,
        required_skills=["CI/CD", "Containerization", "Infrastructure as Code", "Monitoring"],
        preferred_skills=["Docker", "Kubernetes", "Jenkins", "Git", "Cloud Platforms"],
        required_certifications=["AWS DevOps Professional"],
        preferred_certifications=["CKA", "Azure DevOps Engineer"],
        market_demand="Very High",
        growth_outlook="Booming"
    )
    
    # Senior Level
    roles['cloud_architect'] = CareerRole(
        title="Cloud Solutions Architect",
        description="Senior role designing enterprise cloud solutions and migrations",
        domain="Cloud Computing",
        level="Senior",
        min_years_experience=5,
        max_years_experience=12,
        salary_min=110000,
        salary_max=160000,
        required_skills=["Cloud Architecture", "Solution Design", "Migration Planning", "Cost Optimization"],
        preferred_skills=["Multi-cloud", "Hybrid Cloud", "Enterprise Architecture", "Security"],
        required_certifications=["AWS Solutions Architect Professional"],
        preferred_certifications=["Azure Solutions Architect Expert", "GCP Professional Cloud Architect"],
        market_demand="High",
        growth_outlook="Growing"
    )
    
    # Add all roles to database
    for role_key, role in roles.items():
        db.add(role)
    
    db.flush()  # Get IDs
    return roles


def create_transition_paths(db: Session, cyber_roles: dict, cloud_roles: dict) -> None:
    """Create career transition paths between roles."""
    
    # Entry to Mid-level transitions in Cybersecurity
    paths = [
        # IT Support to Security Analyst
        CareerTransitionPath(
            source_role_id=cyber_roles['it_support_specialist'].id,
            target_role_id=cyber_roles['security_analyst_entry'].id,
            name="IT Support to Security Analyst",
            description="Transition from general IT support to cybersecurity focus",
            difficulty_level="Medium",
            estimated_duration_months=8,
            min_duration_months=6,
            max_duration_months=12,
            estimated_cost_min=2000,
            estimated_cost_max=4000,
            required_certifications=[1, 2],  # Security+, Network+
            success_rate=0.75,
            average_salary_increase=25.0
        ),
        
        # Junior to Mid-level Security Analyst
        CareerTransitionPath(
            source_role_id=cyber_roles['security_analyst_entry'].id,
            target_role_id=cyber_roles['security_analyst_mid'].id,
            name="Junior to Mid-level Security Analyst",
            description="Advancement within security analyst career track",
            difficulty_level="Medium",
            estimated_duration_months=18,
            min_duration_months=12,
            max_duration_months=24,
            estimated_cost_min=3000,
            estimated_cost_max=6000,
            required_certifications=[3],  # CySA+
            recommended_certifications=[4, 5],  # GCIH, CEH
            success_rate=0.85,
            average_salary_increase=20.0
        ),
        
        # Security Analyst to Penetration Tester
        CareerTransitionPath(
            source_role_id=cyber_roles['security_analyst_mid'].id,
            target_role_id=cyber_roles['penetration_tester'].id,
            name="Security Analyst to Penetration Tester",
            description="Specialization into offensive security and penetration testing",
            difficulty_level="Hard",
            estimated_duration_months=12,
            min_duration_months=8,
            max_duration_months=18,
            estimated_cost_min=4000,
            estimated_cost_max=8000,
            required_certifications=[4],  # CEH
            recommended_certifications=[6],  # OSCP
            success_rate=0.65,
            average_salary_increase=15.0
        ),
        
        # Security Analyst to Network Security Engineer
        CareerTransitionPath(
            source_role_id=cyber_roles['security_analyst_mid'].id,
            target_role_id=cyber_roles['network_security_engineer'].id,
            name="Security Analyst to Network Security Engineer",
            description="Specialization into network security and infrastructure",
            difficulty_level="Medium",
            estimated_duration_months=10,
            min_duration_months=8,
            max_duration_months=15,
            estimated_cost_min=3500,
            estimated_cost_max=7000,
            required_certifications=[2],  # Network+
            recommended_certifications=[7],  # CCNA Security
            success_rate=0.70,
            average_salary_increase=12.0
        ),
        
        # Mid-level to Senior transitions
        CareerTransitionPath(
            source_role_id=cyber_roles['security_analyst_mid'].id,
            target_role_id=cyber_roles['security_architect'].id,
            name="Security Analyst to Security Architect",
            description="Advancement to senior architecture role",
            difficulty_level="Hard",
            estimated_duration_months=24,
            min_duration_months=18,
            max_duration_months=36,
            estimated_cost_min=5000,
            estimated_cost_max=10000,
            required_certifications=[8],  # CISSP
            success_rate=0.60,
            average_salary_increase=35.0
        ),
        
        # Cloud Computing Transitions
        CareerTransitionPath(
            source_role_id=cloud_roles['cloud_support_associate'].id,
            target_role_id=cloud_roles['cloud_engineer'].id,
            name="Cloud Support to Cloud Engineer",
            description="Advancement from support to engineering role",
            difficulty_level="Medium",
            estimated_duration_months=12,
            min_duration_months=9,
            max_duration_months=18,
            estimated_cost_min=2500,
            estimated_cost_max=5000,
            required_certifications=[9],  # AWS Solutions Architect Associate
            success_rate=0.80,
            average_salary_increase=30.0
        ),
        
        CareerTransitionPath(
            source_role_id=cloud_roles['cloud_engineer'].id,
            target_role_id=cloud_roles['devops_engineer'].id,
            name="Cloud Engineer to DevOps Engineer",
            description="Specialization into DevOps practices and automation",
            difficulty_level="Medium",
            estimated_duration_months=8,
            min_duration_months=6,
            max_duration_months=12,
            estimated_cost_min=2000,
            estimated_cost_max=4000,
            required_certifications=[10],  # AWS DevOps Professional
            success_rate=0.75,
            average_salary_increase=10.0
        ),
        
        CareerTransitionPath(
            source_role_id=cloud_roles['cloud_engineer'].id,
            target_role_id=cloud_roles['cloud_architect'].id,
            name="Cloud Engineer to Cloud Architect",
            description="Advancement to senior cloud architecture role",
            difficulty_level="Hard",
            estimated_duration_months=18,
            min_duration_months=12,
            max_duration_months=24,
            estimated_cost_min=4000,
            estimated_cost_max=8000,
            required_certifications=[11],  # AWS Solutions Architect Professional
            success_rate=0.65,
            average_salary_increase=25.0
        ),
        
        # Cross-domain transitions
        CareerTransitionPath(
            source_role_id=cyber_roles['security_analyst_mid'].id,
            target_role_id=cloud_roles['cloud_engineer'].id,
            name="Security Analyst to Cloud Engineer",
            description="Cross-domain transition from cybersecurity to cloud computing",
            difficulty_level="Hard",
            estimated_duration_months=15,
            min_duration_months=12,
            max_duration_months=20,
            estimated_cost_min=4000,
            estimated_cost_max=7000,
            required_certifications=[9],  # AWS Solutions Architect Associate
            success_rate=0.55,
            average_salary_increase=20.0
        ),
        
        CareerTransitionPath(
            source_role_id=cloud_roles['cloud_engineer'].id,
            target_role_id=cyber_roles['security_analyst_mid'].id,
            name="Cloud Engineer to Security Analyst",
            description="Cross-domain transition from cloud to cybersecurity",
            difficulty_level="Medium",
            estimated_duration_months=10,
            min_duration_months=8,
            max_duration_months=15,
            estimated_cost_min=3000,
            estimated_cost_max=5000,
            required_certifications=[1, 3],  # Security+, CySA+
            success_rate=0.70,
            average_salary_increase=5.0
        )
    ]
    
    # Add all paths to database
    for path in paths:
        db.add(path)


def main():
    """Main function to seed career data."""
    logger.info("Starting career data seeding...")
    
    db = SessionLocal()
    try:
        # Create career roles
        logger.info("Creating cybersecurity roles...")
        cyber_roles = create_cybersecurity_roles(db)
        
        logger.info("Creating cloud computing roles...")
        cloud_roles = create_cloud_computing_roles(db)
        
        # Create transition paths
        logger.info("Creating transition paths...")
        create_transition_paths(db, cyber_roles, cloud_roles)
        
        # Commit all changes
        db.commit()
        
        logger.info(f"Successfully seeded {len(cyber_roles) + len(cloud_roles)} career roles")
        logger.info("Career data seeding completed successfully!")
        
    except Exception as e:
        logger.error(f"Error seeding career data: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
