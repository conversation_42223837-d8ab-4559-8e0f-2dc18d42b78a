#!/usr/bin/env python3
"""
Local CI/CD Pipeline for CertPathFinder

This script runs a complete local CI/CD pipeline including:
- Code quality checks
- Security scanning
- Testing
- Documentation building
- Docker image building
"""

import subprocess
import sys
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LocalCICD:
    """Local CI/CD pipeline for CertPathFinder."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.start_time = time.time()
        self.results = {
            'stages': {},
            'overall_status': 'pending',
            'duration': 0
        }
    
    def run_command(self, command: List[str], description: str, 
                   cwd: Path = None, check: bool = True) -> Tuple[bool, str]:
        """Run a command and return success status and output."""
        logger.info(f"Running: {description}")
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd=cwd or self.project_root,
                check=check
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {description} - SUCCESS")
                return True, result.stdout
            else:
                logger.error(f"❌ {description} - FAILED")
                logger.error(f"Error: {result.stderr}")
                return False, result.stderr
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {description} - FAILED with exit code {e.returncode}")
            logger.error(f"Error: {e.stderr}")
            return False, str(e)
        except Exception as e:
            logger.error(f"❌ {description} - FAILED with exception: {e}")
            return False, str(e)
    
    def stage_setup(self) -> bool:
        """Setup stage - install dependencies and prepare environment."""
        logger.info("🚀 Stage 1: Setup and Dependencies")
        
        success = True
        
        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 10):
            logger.error(f"❌ Python 3.10+ required, found {python_version.major}.{python_version.minor}")
            success = False
        else:
            logger.info(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # Install/upgrade pip
        pip_success, _ = self.run_command(
            [sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'],
            "Upgrading pip"
        )
        success = success and pip_success
        
        # Install dependencies
        deps_success, _ = self.run_command(
            [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
            "Installing dependencies"
        )
        success = success and deps_success
        
        # Install development dependencies
        dev_deps = [
            'pytest', 'pytest-cov', 'pytest-asyncio', 'black', 'flake8', 
            'mypy', 'safety', 'bandit', 'pre-commit'
        ]
        
        for dep in dev_deps:
            dep_success, _ = self.run_command(
                [sys.executable, '-m', 'pip', 'install', dep],
                f"Installing {dep}",
                check=False  # Don't fail if optional deps fail
            )
        
        self.results['stages']['setup'] = {
            'status': 'passed' if success else 'failed',
            'duration': time.time() - self.start_time
        }
        
        return success
    
    def stage_code_quality(self) -> bool:
        """Code quality stage - linting and formatting."""
        logger.info("🔍 Stage 2: Code Quality Checks")
        
        stage_start = time.time()
        success = True
        
        # Black formatting check
        black_success, _ = self.run_command(
            [sys.executable, '-m', 'black', '--check', '--diff', '.'],
            "Black formatting check",
            check=False
        )
        
        # Flake8 linting
        flake8_success, _ = self.run_command(
            [sys.executable, '-m', 'flake8', '.', '--max-line-length=100', 
             '--exclude=venv,docs,migrations'],
            "Flake8 linting",
            check=False
        )
        
        # MyPy type checking
        mypy_success, _ = self.run_command(
            [sys.executable, '-m', 'mypy', '.', '--ignore-missing-imports'],
            "MyPy type checking",
            check=False
        )
        
        # Count as success if at least 2 out of 3 pass
        passed_checks = sum([black_success, flake8_success, mypy_success])
        success = passed_checks >= 2
        
        if not success:
            logger.warning("⚠️  Code quality checks have issues but continuing...")
        
        self.results['stages']['code_quality'] = {
            'status': 'passed' if success else 'warning',
            'duration': time.time() - stage_start,
            'details': {
                'black': 'passed' if black_success else 'failed',
                'flake8': 'passed' if flake8_success else 'failed',
                'mypy': 'passed' if mypy_success else 'failed'
            }
        }
        
        return True  # Don't fail pipeline on code quality issues
    
    def stage_security(self) -> bool:
        """Security stage - vulnerability and security scanning."""
        logger.info("🔒 Stage 3: Security Scanning")
        
        stage_start = time.time()
        success = True
        
        # Run our custom security scanner
        security_success, _ = self.run_command(
            [sys.executable, 'scripts/security_scan.py'],
            "Custom security scan",
            check=False
        )
        
        # Safety check for dependencies
        safety_success, _ = self.run_command(
            [sys.executable, '-m', 'safety', 'check'],
            "Safety dependency check",
            check=False
        )
        
        # Bandit security linting
        bandit_success, _ = self.run_command(
            [sys.executable, '-m', 'bandit', '-r', '.', '-x', './venv,./docs,./tests'],
            "Bandit security linting",
            check=False
        )
        
        # Security is critical, but we'll continue with warnings
        critical_issues = not (security_success and safety_success)
        
        self.results['stages']['security'] = {
            'status': 'passed' if not critical_issues else 'warning',
            'duration': time.time() - stage_start,
            'details': {
                'custom_scan': 'passed' if security_success else 'failed',
                'safety': 'passed' if safety_success else 'failed',
                'bandit': 'passed' if bandit_success else 'failed'
            }
        }
        
        if critical_issues:
            logger.warning("⚠️  Security issues found - review security_report.txt")
        
        return True  # Continue pipeline but flag issues
    
    def stage_testing(self) -> bool:
        """Testing stage - run all tests with coverage."""
        logger.info("🧪 Stage 4: Testing")
        
        stage_start = time.time()
        
        # Run tests with coverage
        test_success, test_output = self.run_command(
            [sys.executable, '-m', 'pytest', 'tests/', '-v', '--cov=.', 
             '--cov-report=html', '--cov-report=term'],
            "Running tests with coverage",
            check=False
        )
        
        # Parse coverage from output
        coverage_percentage = 0
        if test_output:
            for line in test_output.split('\n'):
                if 'TOTAL' in line and '%' in line:
                    try:
                        coverage_percentage = int(line.split('%')[0].split()[-1])
                    except:
                        pass
        
        self.results['stages']['testing'] = {
            'status': 'passed' if test_success else 'failed',
            'duration': time.time() - stage_start,
            'details': {
                'coverage_percentage': coverage_percentage,
                'tests_passed': test_success
            }
        }
        
        if not test_success:
            logger.error("❌ Tests failed - this is critical!")
            return False
        
        logger.info(f"✅ Tests passed with {coverage_percentage}% coverage")
        return True
    
    def stage_documentation(self) -> bool:
        """Documentation stage - build documentation."""
        logger.info("📚 Stage 5: Documentation")
        
        stage_start = time.time()
        
        # Build Sphinx documentation
        docs_success = True
        sphinx_path = self.project_root / 'docs' / 'sphinx'
        
        if sphinx_path.exists():
            docs_success, _ = self.run_command(
                ['make', 'html'],
                "Building Sphinx documentation",
                cwd=sphinx_path,
                check=False
            )
        
        self.results['stages']['documentation'] = {
            'status': 'passed' if docs_success else 'warning',
            'duration': time.time() - stage_start
        }
        
        return True  # Don't fail pipeline on docs issues
    
    def stage_build(self) -> bool:
        """Build stage - create Docker images and packages."""
        logger.info("🐳 Stage 6: Build")
        
        stage_start = time.time()
        success = True
        
        # Check if Docker is available
        docker_available, _ = self.run_command(
            ['docker', '--version'],
            "Checking Docker availability",
            check=False
        )
        
        if docker_available:
            # Build API Docker image
            api_build_success, _ = self.run_command(
                ['docker', 'build', '-f', 'Dockerfile.api', '-t', 'certpathfinder-api:latest', '.'],
                "Building API Docker image",
                check=False
            )
            
            # Build Frontend Docker image
            frontend_build_success, _ = self.run_command(
                ['docker', 'build', '-f', 'Dockerfile.frontend', '-t', 'certpathfinder-frontend:latest', '.'],
                "Building Frontend Docker image",
                check=False
            )
            
            success = api_build_success and frontend_build_success
        else:
            logger.warning("⚠️  Docker not available - skipping image builds")
            success = True  # Don't fail if Docker isn't available
        
        self.results['stages']['build'] = {
            'status': 'passed' if success else 'warning',
            'duration': time.time() - stage_start,
            'details': {
                'docker_available': docker_available,
                'api_image': 'built' if docker_available and success else 'skipped',
                'frontend_image': 'built' if docker_available and success else 'skipped'
            }
        }
        
        return True
    
    def generate_report(self) -> str:
        """Generate CI/CD pipeline report."""
        total_duration = time.time() - self.start_time
        self.results['duration'] = total_duration
        
        # Determine overall status
        failed_stages = [name for name, stage in self.results['stages'].items() 
                        if stage['status'] == 'failed']
        warning_stages = [name for name, stage in self.results['stages'].items() 
                         if stage['status'] == 'warning']
        
        if failed_stages:
            self.results['overall_status'] = 'failed'
        elif warning_stages:
            self.results['overall_status'] = 'warning'
        else:
            self.results['overall_status'] = 'passed'
        
        # Generate report
        report = f"""
🚀 CertPathFinder Local CI/CD Pipeline Report
============================================

📊 Overall Status: {self.results['overall_status'].upper()}
⏱️  Total Duration: {total_duration:.2f} seconds

📋 Stage Results:
----------------
"""
        
        for stage_name, stage_data in self.results['stages'].items():
            status_emoji = {
                'passed': '✅',
                'warning': '⚠️ ',
                'failed': '❌'
            }.get(stage_data['status'], '❓')
            
            report += f"{status_emoji} {stage_name.title()}: {stage_data['status'].upper()} "
            report += f"({stage_data['duration']:.2f}s)\n"
            
            if 'details' in stage_data:
                for key, value in stage_data['details'].items():
                    report += f"   - {key}: {value}\n"
        
        # Add recommendations
        report += "\n💡 Recommendations:\n"
        
        if failed_stages:
            report += f"   🔴 Critical: Fix failed stages: {', '.join(failed_stages)}\n"
        
        if warning_stages:
            report += f"   🟡 Review warnings in: {', '.join(warning_stages)}\n"
        
        if self.results['overall_status'] == 'passed':
            report += "   🎉 All stages passed! Ready for deployment.\n"
        
        # Add next steps
        report += "\n🎯 Next Steps:\n"
        if self.results['overall_status'] == 'passed':
            report += "   1. Review any warnings\n"
            report += "   2. Deploy to staging environment\n"
            report += "   3. Run integration tests\n"
            report += "   4. Deploy to production\n"
        else:
            report += "   1. Fix critical issues\n"
            report += "   2. Re-run pipeline\n"
            report += "   3. Review security report if applicable\n"
        
        return report
    
    def run_pipeline(self) -> bool:
        """Run the complete CI/CD pipeline."""
        logger.info("🚀 Starting CertPathFinder Local CI/CD Pipeline")
        logger.info("=" * 60)
        
        stages = [
            ('Setup', self.stage_setup),
            ('Code Quality', self.stage_code_quality),
            ('Security', self.stage_security),
            ('Testing', self.stage_testing),
            ('Documentation', self.stage_documentation),
            ('Build', self.stage_build)
        ]
        
        pipeline_success = True
        
        for stage_name, stage_func in stages:
            try:
                stage_success = stage_func()
                if not stage_success:
                    logger.error(f"❌ Stage '{stage_name}' failed!")
                    pipeline_success = False
                    break
            except Exception as e:
                logger.error(f"❌ Stage '{stage_name}' crashed: {e}")
                pipeline_success = False
                break
        
        # Generate and save report
        report = self.generate_report()
        
        report_file = Path('cicd_report.txt')
        with open(report_file, 'w') as f:
            f.write(report)
        
        print("\n" + "=" * 60)
        print(report)
        print(f"📄 Full report saved to: {report_file.absolute()}")
        
        return pipeline_success

def main():
    """Main function to run CI/CD pipeline."""
    pipeline = LocalCICD()
    success = pipeline.run_pipeline()
    
    if success:
        logger.info("🎉 Pipeline completed successfully!")
        sys.exit(0)
    else:
        logger.error("💥 Pipeline failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
