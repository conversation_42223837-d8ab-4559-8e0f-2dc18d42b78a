-- Initialize CertPathFinder database
-- This script creates the main database and sets up basic configuration

-- Create the main database if it doesn't exist
SELECT 'CREATE DATABASE certpathfinder'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'certpathfinder')\gexec

-- Connect to the certpathfinder database
\c certpathfinder;

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set timezone
SET timezone = 'UTC';

-- Basic database setup complete
