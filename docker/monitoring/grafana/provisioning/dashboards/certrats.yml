# Grafana Dashboard Provisioning for CertRats Platform
#
# This configuration automatically loads dashboards for monitoring
# the CertRats certification platform components.

apiVersion: 1

providers:
  # CertRats Application Dashboards
  - name: 'certrats-dashboards'
    orgId: 1
    folder: 'CertRats'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards

  # Infrastructure Dashboards
  - name: 'infrastructure-dashboards'
    orgId: 1
    folder: 'Infrastructure'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/infrastructure

  # Development Dashboards
  - name: 'development-dashboards'
    orgId: 1
    folder: 'Development'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/development

  # Business Metrics Dashboards
  - name: 'business-dashboards'
    orgId: 1
    folder: 'Business'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/business
