# Grafana Datasource Configuration for CertRats Platform
#
# This configuration automatically provisions Prometheus as a datasource
# in Grafana for monitoring the CertRats certification platform.

apiVersion: 1

datasources:
  # Primary Prometheus Datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://certrats_prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
      queryTimeout: 60s
      timeInterval: 15s
    secureJsonData: {}

  # Loki for Logs (if enabled)
  - name: Loki
    type: loki
    access: proxy
    url: http://certrats_loki:3100
    isDefault: false
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: prometheus
          matcherRegex: "traceID=(\\w+)"
          name: TraceID
          url: "$${__value.raw}"

  # Jaeger for Tracing (if enabled)
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    isDefault: false
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: loki
        tags: ['job', 'instance', 'pod', 'namespace']
        mappedTags: [{ key: 'service.name', value: 'service' }]
        mapTagNamesEnabled: false
        spanStartTimeShift: '1h'
        spanEndTimeShift: '1h'
        filterByTraceID: false
        filterBySpanID: false

  # TestData for Development
  - name: TestData
    type: testdata
    access: proxy
    isDefault: false
    editable: true
    jsonData: {}
