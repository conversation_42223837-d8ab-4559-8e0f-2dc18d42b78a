# Prometheus Configuration for CertRats Platform
#
# This configuration defines scraping targets, alerting rules, and
# monitoring settings for the CertRats certification platform.
#
# Features:
# - Service discovery for Docker containers
# - Application metrics collection
# - Infrastructure monitoring
# - Custom alerting rules
# - Performance and health metrics

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'certrats-monitor'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load alerting rules
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 15s

  # CertRats API Service
  - job_name: 'certrats-api'
    static_configs:
      - targets: ['certrats_api:8000']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # CertRats Frontend (if it exposes metrics)
  - job_name: 'certrats-frontend'
    static_configs:
      - targets: ['certrats_frontend:3000']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL Database Metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['certrats_database:5432']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['certrats_redis:6379']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # MinIO Object Storage Metrics
  - job_name: 'minio'
    static_configs:
      - targets: ['certrats_storage:9000']
    metrics_path: /minio/v2/metrics/cluster
    scrape_interval: 30s
    scrape_timeout: 10s

  # Celery Worker Metrics
  - job_name: 'celery-worker'
    static_configs:
      - targets: ['certrats_celery_worker:8000']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # Celery Flower Metrics
  - job_name: 'celery-flower'
    static_configs:
      - targets: ['certrats_celery_flower:5555']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # Traefik Reverse Proxy Metrics
  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik_proxy:8080']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter (if deployed)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # cAdvisor for Container Metrics (if deployed)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # Custom Health Check Endpoints
  - job_name: 'certrats-health'
    static_configs:
      - targets: 
        - 'certrats_api:8000'
        - 'certrats_frontend:3000'
        - 'certrats_database:5432'
        - 'certrats_redis:6379'
        - 'certrats_storage:9000'
    metrics_path: /health/metrics
    scrape_interval: 15s
    scrape_timeout: 5s

  # Service Discovery for Docker Containers
  - job_name: 'docker-containers'
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 30s
        port: 8080
    relabel_configs:
      # Only scrape containers with the prometheus.scrape label
      - source_labels: [__meta_docker_container_label_prometheus_scrape]
        action: keep
        regex: true
      
      # Use custom metrics path if specified
      - source_labels: [__meta_docker_container_label_prometheus_path]
        target_label: __metrics_path__
        regex: (.+)
      
      # Use custom port if specified
      - source_labels: [__address__, __meta_docker_container_label_prometheus_port]
        target_label: __address__
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
      
      # Set job name from container name
      - source_labels: [__meta_docker_container_name]
        target_label: job
        regex: /(.*)
        replacement: $1
      
      # Set instance name from container name
      - source_labels: [__meta_docker_container_name]
        target_label: instance
        regex: /(.*)
        replacement: $1
      
      # Add container labels as metrics labels
      - regex: __meta_docker_container_label_(.+)
        action: labelmap
        replacement: $1

# Storage configuration
storage:
  tsdb:
    path: /prometheus
    retention.time: 200h
    retention.size: 10GB
    wal-compression: true

# Remote write configuration (for external storage)
# remote_write:
#   - url: "https://your-remote-prometheus.com/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Remote read configuration (for external storage)
# remote_read:
#   - url: "https://your-remote-prometheus.com/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
