# This directory contains SSL certificates for development
# 
# For development, you can generate self-signed certificates:
# 
# Generate a self-signed certificate for *.certrats.docker.localhost:
# openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
#   -keyout certrats.key \
#   -out certrats.crt \
#   -subj "/C=US/ST=State/L=City/O=Organization/CN=*.certrats.docker.localhost"
#
# Or use mkcert for better browser compatibility:
# mkcert -install
# mkcert "*.certrats.docker.localhost" "certrats.docker.localhost"
