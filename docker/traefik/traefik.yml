# Traefik Configuration for CertRats Platform
# 
# This configuration sets up <PERSON><PERSON><PERSON><PERSON> as a reverse proxy for the CertRats
# certification platform with automatic service discovery, SSL termination,
# and comprehensive routing rules.
#
# Features:
# - Automatic Docker service discovery
# - SSL certificate management with Let's Encrypt
# - Dashboard and API access
# - Health checks and monitoring
# - Security headers and middleware
# - Rate limiting and access control
#
# Domain Pattern: servicename.certrats.docker.localhost (development)
# Production domains: servicename.certrats.com

# =============================================================================
# GLOBAL CONFIGURATION
# =============================================================================

global:
  checkNewVersion: false
  sendAnonymousUsage: false

# =============================================================================
# API AND DASHBOARD
# =============================================================================

api:
  dashboard: true
  debug: true
  insecure: true  # Only for development - disable in production

# =============================================================================
# ENTRY POINTS
# =============================================================================

entryPoints:
  # HTTP Entry Point
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
          permanent: true

  # HTTPS Entry Point
  websecure:
    address: ":443"
    http:
      tls:
        options: default

  # Traefik Dashboard
  traefik:
    address: ":8080"

# =============================================================================
# PROVIDERS
# =============================================================================

providers:
  # Docker Provider for Service Discovery
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: traefik_network
    watch: true

  # File Provider for Static Configuration
  file:
    filename: /etc/traefik/dynamic.yml
    watch: true

# =============================================================================
# CERTIFICATE RESOLVERS
# =============================================================================

certificatesResolvers:
  # Let's Encrypt for Production SSL Certificates
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme.json
      httpChallenge:
        entryPoint: web
      # Uncomment for staging/testing
      # caServer: https://acme-staging-v02.api.letsencrypt.org/directory

  # Let's Encrypt DNS Challenge (for wildcard certificates)
  letsencrypt-dns:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme-dns.json
      dnsChallenge:
        provider: cloudflare
        delayBeforeCheck: 30
        resolvers:
          - "1.1.1.1:53"
          - "*******:53"

# =============================================================================
# MIDDLEWARE DEFINITIONS
# =============================================================================

# Security Headers Middleware
middlewares:
  # Security Headers
  security-headers:
    headers:
      accessControlAllowMethods:
        - GET
        - OPTIONS
        - PUT
        - POST
        - DELETE
        - PATCH
      accessControlAllowOriginList:
        - "https://app.certrats.docker.localhost"
        - "https://app.certrats.com"
        - "http://localhost:3000"
      accessControlMaxAge: 100
      addVaryHeader: true
      browserXssFilter: true
      contentTypeNosniff: true
      forceSTSHeader: true
      frameDeny: true
      stsIncludeSubdomains: true
      stsPreload: true
      stsSeconds: 31536000
      customRequestHeaders:
        X-Forwarded-Proto: "https"

  # Rate Limiting
  rate-limit:
    rateLimit:
      average: 100
      burst: 200
      period: 1m

  # API Rate Limiting (more restrictive)
  api-rate-limit:
    rateLimit:
      average: 50
      burst: 100
      period: 1m

  # Authentication Middleware (for protected services)
  auth:
    basicAuth:
      users:
        - "admin:$2y$10$2b2cu2Fw1ZfqRkjQ1s3IuOe9TZQQaHBNjXAqCo6rjlb5cHRXFOxJO"  # admin:certrats_admin

  # Compression
  compression:
    compress: {}

  # CORS for API
  cors:
    headers:
      accessControlAllowCredentials: true
      accessControlAllowHeaders:
        - "Content-Type"
        - "Authorization"
        - "X-Requested-With"
        - "Accept"
        - "Origin"
      accessControlAllowMethods:
        - "GET"
        - "POST"
        - "PUT"
        - "DELETE"
        - "PATCH"
        - "OPTIONS"
      accessControlAllowOriginList:
        - "https://app.certrats.docker.localhost"
        - "https://app.certrats.com"
        - "http://localhost:3000"
      accessControlMaxAge: 100
      addVaryHeader: true

# =============================================================================
# LOGGING
# =============================================================================

log:
  level: INFO
  format: json

accessLog:
  format: json
  fields:
    defaultMode: keep
    names:
      ClientUsername: drop
    headers:
      defaultMode: keep
      names:
        User-Agent: redact
        Authorization: drop
        Content-Type: keep

# =============================================================================
# METRICS
# =============================================================================

metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true
    addRoutersLabels: true
    buckets:
      - 0.1
      - 0.3
      - 1.2
      - 5.0

# =============================================================================
# PING ENDPOINT
# =============================================================================

ping:
  entryPoint: traefik

# =============================================================================
# TRACING (Optional)
# =============================================================================

# tracing:
#   jaeger:
#     samplingServerURL: http://jaeger:14268/api/sampling
#     localAgentHostPort: jaeger:6831

# =============================================================================
# EXPERIMENTAL FEATURES
# =============================================================================

experimental:
  plugins:
    # Add any experimental plugins here
    # Example: rate limiting, authentication, etc.

# =============================================================================
# DEVELOPMENT SPECIFIC SETTINGS
# =============================================================================

# For development, we disable SSL redirect and use HTTP
# This section should be removed or modified for production

serversTransport:
  insecureSkipVerify: true  # Only for development with self-signed certificates
