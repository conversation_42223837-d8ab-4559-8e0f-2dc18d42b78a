# Traefik Dynamic Configuration for CertRats Platform
#
# This file contains dynamic routing rules, middleware configurations,
# and service definitions that can be updated without restarting Traefik.
#
# Features:
# - Dynamic routing rules for CertRats services
# - SSL/TLS configuration and certificates
# - Middleware chains for security and performance
# - Service-specific configurations
# - Health check endpoints

# =============================================================================
# HTTP ROUTERS
# =============================================================================

http:
  routers:
    # Traefik Dashboard Router
    traefik-dashboard:
      rule: "Host(`traefik.certrats.docker.localhost`)"
      service: api@internal
      middlewares:
        - auth
        - security-headers
      tls:
        certResolver: letsencrypt

    # API Router with Enhanced Security
    certrats-api-secure:
      rule: "Host(`api.certrats.docker.localhost`) && PathPrefix(`/api/`)"
      service: certrats-api
      middlewares:
        - cors
        - api-rate-limit
        - security-headers
        - compression
      tls:
        certResolver: letsencrypt

    # Frontend Router
    certrats-frontend-secure:
      rule: "Host(`app.certrats.docker.localhost`)"
      service: certrats-frontend
      middlewares:
        - security-headers
        - compression
      tls:
        certResolver: letsencrypt

    # Documentation Router
    certrats-docs-secure:
      rule: "Host(`docs.certrats.docker.localhost`)"
      service: certrats-docs
      middlewares:
        - security-headers
        - compression
      tls:
        certResolver: letsencrypt

    # Status Dashboard Router
    certrats-status-secure:
      rule: "Host(`status.certrats.docker.localhost`)"
      service: certrats-status
      middlewares:
        - auth
        - security-headers
      tls:
        certResolver: letsencrypt

    # Monitoring Services (Protected)
    certrats-prometheus-secure:
      rule: "Host(`metrics.certrats.docker.localhost`)"
      service: certrats-prometheus
      middlewares:
        - auth
        - security-headers
      tls:
        certResolver: letsencrypt

    certrats-grafana-secure:
      rule: "Host(`grafana.certrats.docker.localhost`)"
      service: certrats-grafana
      middlewares:
        - security-headers
      tls:
        certResolver: letsencrypt

    # Flower (Celery Monitoring) - Protected
    certrats-flower-secure:
      rule: "Host(`flower.certrats.docker.localhost`)"
      service: certrats-flower
      middlewares:
        - auth
        - security-headers
      tls:
        certResolver: letsencrypt

    # MinIO Storage - Protected
    certrats-storage-secure:
      rule: "Host(`storage.certrats.docker.localhost`)"
      service: certrats-storage
      middlewares:
        - auth
        - security-headers
      tls:
        certResolver: letsencrypt

    certrats-storage-console-secure:
      rule: "Host(`storage-console.certrats.docker.localhost`)"
      service: certrats-storage-console
      middlewares:
        - auth
        - security-headers
      tls:
        certResolver: letsencrypt

  # =============================================================================
  # SERVICES
  # =============================================================================

  services:
    # API Service
    certrats-api:
      loadBalancer:
        servers:
          - url: "http://certrats_api:8000"
        healthCheck:
          path: "/health"
          interval: "30s"
          timeout: "10s"

    # Frontend Service
    certrats-frontend:
      loadBalancer:
        servers:
          - url: "http://certrats_frontend:3000"
        healthCheck:
          path: "/"
          interval: "30s"
          timeout: "10s"

    # Documentation Service
    certrats-docs:
      loadBalancer:
        servers:
          - url: "http://certrats_docs:8080"

    # Status Dashboard Service
    certrats-status:
      loadBalancer:
        servers:
          - url: "http://certrats_status:8081"

    # Prometheus Service
    certrats-prometheus:
      loadBalancer:
        servers:
          - url: "http://certrats_prometheus:9090"
        healthCheck:
          path: "/-/healthy"
          interval: "30s"
          timeout: "10s"

    # Grafana Service
    certrats-grafana:
      loadBalancer:
        servers:
          - url: "http://certrats_grafana:3000"
        healthCheck:
          path: "/api/health"
          interval: "30s"
          timeout: "10s"

    # Flower Service
    certrats-flower:
      loadBalancer:
        servers:
          - url: "http://certrats_celery_flower:5555"

    # MinIO Storage Service
    certrats-storage:
      loadBalancer:
        servers:
          - url: "http://certrats_storage:9000"
        healthCheck:
          path: "/minio/health/live"
          interval: "30s"
          timeout: "10s"

    # MinIO Console Service
    certrats-storage-console:
      loadBalancer:
        servers:
          - url: "http://certrats_storage:9001"

  # =============================================================================
  # MIDDLEWARE DEFINITIONS
  # =============================================================================

  middlewares:
    # Enhanced Security Headers
    security-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
          - POST
          - DELETE
          - PATCH
        accessControlAllowOriginList:
          - "https://app.certrats.docker.localhost"
          - "https://app.certrats.com"
          - "http://localhost:3000"
        accessControlMaxAge: 100
        addVaryHeader: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        frameDeny: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          X-Robots-Tag: "noindex,nofollow,nosnippet,noarchive,notranslate,noimageindex"
          server: ""

    # CORS for API
    cors:
      headers:
        accessControlAllowCredentials: true
        accessControlAllowHeaders:
          - "Content-Type"
          - "Authorization"
          - "X-Requested-With"
          - "Accept"
          - "Origin"
          - "X-CSRF-Token"
        accessControlAllowMethods:
          - "GET"
          - "POST"
          - "PUT"
          - "DELETE"
          - "PATCH"
          - "OPTIONS"
        accessControlAllowOriginList:
          - "https://app.certrats.docker.localhost"
          - "https://app.certrats.com"
          - "http://localhost:3000"
        accessControlMaxAge: 100
        addVaryHeader: true

    # Rate Limiting
    rate-limit:
      rateLimit:
        average: 100
        burst: 200
        period: 1m

    # API Rate Limiting (more restrictive)
    api-rate-limit:
      rateLimit:
        average: 50
        burst: 100
        period: 1m

    # Authentication for Protected Services
    auth:
      basicAuth:
        users:
          - "admin:$2y$10$2b2cu2Fw1ZfqRkjQ1s3IuOe9TZQQaHBNjXAqCo6rjlb5cHRXFOxJO"  # admin:certrats_admin

    # Compression
    compression:
      compress: {}

    # Redirect to HTTPS
    redirect-to-https:
      redirectScheme:
        scheme: https
        permanent: true

    # Strip Prefix for API versioning
    api-strip-prefix:
      stripPrefix:
        prefixes:
          - "/api/v1"

# =============================================================================
# TLS CONFIGURATION
# =============================================================================

tls:
  options:
    default:
      sslProtocols:
        - "TLSv1.2"
        - "TLSv1.3"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"
      curvePreferences:
        - "CurveP521"
        - "CurveP384"
      minVersion: "VersionTLS12"

  certificates:
    # Development self-signed certificate
    - certFile: /etc/traefik/certs/certrats.crt
      keyFile: /etc/traefik/certs/certrats.key
      stores:
        - default

# =============================================================================
# TCP ROUTERS (for non-HTTP services)
# =============================================================================

tcp:
  routers:
    # PostgreSQL Database (for external access)
    postgres-router:
      rule: "HostSNI(`*`)"
      service: postgres-service
      entryPoints:
        - postgres

    # Redis (for external access)
    redis-router:
      rule: "HostSNI(`*`)"
      service: redis-service
      entryPoints:
        - redis

  services:
    # PostgreSQL Service
    postgres-service:
      loadBalancer:
        servers:
          - address: "certrats_database:5432"

    # Redis Service
    redis-service:
      loadBalancer:
        servers:
          - address: "certrats_redis:6379"
