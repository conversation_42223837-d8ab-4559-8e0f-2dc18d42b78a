-- CertRats Development Database Initialization
-- This script sets up the development database with proper permissions and extensions

-- Create development database if it doesn't exist
SELECT 'CREATE DATABASE certrats_dev'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'certrats_dev')\gexec

-- Connect to the development database
\c certrats_dev;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Create development user with appropriate permissions
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'certrats_dev') THEN
        CREATE ROLE certrats_dev WITH LOGIN PASSWORD 'certrats_dev_password';
    END IF;
END
$$;

-- Grant permissions to development user
GRANT ALL PRIVILEGES ON DATABASE certrats_dev TO certrats_dev;
GRANT ALL ON SCHEMA public TO certrats_dev;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO certrats_dev;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO certrats_dev;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO certrats_dev;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO certrats_dev;

-- Create a test database for running tests
CREATE DATABASE certrats_test OWNER certrats_dev;

-- Connect to test database and set up extensions
\c certrats_test;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Grant permissions on test database
GRANT ALL PRIVILEGES ON DATABASE certrats_test TO certrats_dev;
GRANT ALL ON SCHEMA public TO certrats_dev;

-- Set default privileges for test database
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO certrats_dev;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO certrats_dev;

-- Switch back to development database
\c certrats_dev;

-- Create development-specific configurations
-- Enable logging for development
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 0;
ALTER SYSTEM SET log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h ';

-- Reload configuration
SELECT pg_reload_conf();

-- Create development schemas if needed
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS audit;

-- Grant permissions on additional schemas
GRANT ALL ON SCHEMA analytics TO certrats_dev;
GRANT ALL ON SCHEMA audit TO certrats_dev;

-- Create development-specific functions
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a function to reset sequences (useful for testing)
CREATE OR REPLACE FUNCTION public.reset_sequences()
RETURNS void AS $$
DECLARE
    seq_record RECORD;
BEGIN
    FOR seq_record IN 
        SELECT sequence_name 
        FROM information_schema.sequences 
        WHERE sequence_schema = 'public'
    LOOP
        EXECUTE 'ALTER SEQUENCE public.' || seq_record.sequence_name || ' RESTART WITH 1';
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create development data cleanup function
CREATE OR REPLACE FUNCTION public.cleanup_dev_data()
RETURNS void AS $$
BEGIN
    -- Disable foreign key checks temporarily
    SET session_replication_role = replica;
    
    -- Truncate all tables (add your table names here)
    TRUNCATE TABLE IF EXISTS 
        user_certifications,
        learning_paths,
        study_sessions,
        user_profiles,
        users,
        certifications,
        organizations
    RESTART IDENTITY CASCADE;
    
    -- Re-enable foreign key checks
    SET session_replication_role = DEFAULT;
    
    RAISE NOTICE 'Development data cleaned up successfully';
END;
$$ LANGUAGE plpgsql;

-- Create sample data insertion function for development
CREATE OR REPLACE FUNCTION public.insert_sample_data()
RETURNS void AS $$
BEGIN
    -- This function can be used to insert sample data for development
    -- Implementation will be added based on your data models
    RAISE NOTICE 'Sample data insertion function created (implementation pending)';
END;
$$ LANGUAGE plpgsql;

-- Create indexes for common development queries
-- These will be created by Alembic migrations, but having them here as reference

-- Performance monitoring view for development
CREATE OR REPLACE VIEW public.dev_query_stats AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 20;

-- Development logging
\echo 'Development database initialization completed successfully!'
\echo 'Database: certrats_dev'
\echo 'Test Database: certrats_test'
\echo 'User: certrats_dev'
\echo 'Extensions: uuid-ossp, pg_trgm, unaccent'
\echo 'Development functions created: update_updated_at_column, reset_sequences, cleanup_dev_data'
