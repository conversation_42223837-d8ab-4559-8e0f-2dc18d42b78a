#: Translations template for PROJECT.
#: Copyright (C) 2025 CertPathFinder Team
#: This file is distributed under the same license as the PROJECT project.
#: FIRST AUTHOR <<EMAIL>>, 2025.
#:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CertPathFinder 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-24 18:14+0000\n"
"PO-Revision-Date: 2025-06-07 14:00+0000\n"
"Last-Translator: FULL NAME <<EMAIL>>\n"
"Language-Team: Es Team\n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: main.py:100
msgid "Login with:"
msgstr "Iniciar sesión con:"

#: main.py:113 main.py:137
msgid "Select Language"
msgstr "Seleccionar idioma"

#: main.py:127 main.py:146
msgid "Visualization"
msgstr "Visualización"

#: main.py:127
msgid "Listings"
msgstr "Listados"

#: main.py:127 main.py:152
msgid "Study Time"
msgstr "Tiempo de estudio"

#: main.py:127 main.py:154
msgid "Cost Calculator"
msgstr "Calculadora de costos"

#: main.py:128 main.py:156
msgid "CertRat CareerPath"
msgstr "Ruta profesional CertRat"

#: main.py:128 main.py:148
msgid "Admin"
msgstr "Administración"

#: main.py:128 main.py:150
msgid "FAQ"
msgstr "Preguntas frecuentes"

#: pages/1_visualization.py:22
msgid "Certification Path Visualization"
msgstr "Visualización de rutas de certificación"

#: pages/1_visualization.py:23
msgid ""
"\n"
"    Explore certification paths and relationships through an interactive "
"visualization.\n"
"    The graph shows certification progression paths and domain "
"relationships.\n"
"    "
msgstr ""

#: pages/1_visualization.py:29
msgid "Visualization Filters"
msgstr "Filtros de visualización"

#: pages/1_visualization.py:43
msgid "Focus Domain"
msgstr "Dominio de enfoque"

#: pages/1_visualization.py:83
msgid "Select a domain to visualize its certification paths."
msgstr "Seleccione un dominio para visualizar sus rutas de certificación."

#: pages/6_cost_calculator.py:26
msgid ""
"\n"
"    Calculate the total investment needed for your certification journey,"
" including:\n"
"    - Exam fees and study materials\n"
"    - Potential retake costs\n"
"    - Currency conversion\n"
"    - Regional price variations\n"
"    "
msgstr ""

#: pages/6_cost_calculator.py:47
msgid "Select Certifications"
msgstr "Seleccionar certificaciones"

#: pages/6_cost_calculator.py:49
msgid "Choose the certifications you want to include in the cost calculation"
msgstr "Elija las certificaciones que desea incluir en el cálculo de costos"

#: pages/6_cost_calculator.py:54
msgid "Study Materials Cost ($)"
msgstr "Costo de materiales de estudio ($)"

#: pages/6_cost_calculator.py:58
msgid "Estimated cost of books, online courses, practice exams, etc."
msgstr "Costo estimado de libros, cursos en línea, exámenes de práctica, etc."

#: pages/6_cost_calculator.py:67
msgid "Select Currency"
msgstr "Seleccionar moneda"

#: pages/6_cost_calculator.py:69
msgid "Select your preferred currency"
msgstr "Seleccione su moneda preferida"

#: pages/6_cost_calculator.py:73
msgid "📚 Exam Retake Analysis"
msgstr "📚 Análisis de repetición de examen"

#: pages/6_cost_calculator.py:77
msgid "Minimum Days Between Attempts"
msgstr "Días mínimos entre intentos"

#: pages/6_cost_calculator.py:81
msgid "Typical waiting period required between exam attempts"
msgstr "Período de espera típico requerido entre intentos de examen"

#: pages/6_cost_calculator.py:84
msgid "Retake Discount (%)"
msgstr "Descuento por repetición (%)"

#: pages/6_cost_calculator.py:88
msgid "Some certifications offer discounts on retakes"
msgstr "Algunas certificaciones ofrecen descuentos en repeticiones"

#: pages/6_cost_calculator.py:91
msgid "Maximum Attempts Considered"
msgstr "Intentos máximos considerados"

#: pages/6_cost_calculator.py:95
msgid "Number of potential attempts to include in cost analysis"
msgstr "Número de intentos potenciales a incluir en el análisis de costos"

#: pages/6_cost_calculator.py:127
msgid "Cost Breakdown"
msgstr "Desglose de costos"

#: pages/6_cost_calculator.py:133 pages/6_cost_calculator.py:161
msgid "Base Exam Fees"
msgstr "Tarifas base del examen"

#: pages/6_cost_calculator.py:138 pages/6_cost_calculator.py:162
msgid "Study Materials"
msgstr "Materiales de estudio"

#: pages/6_cost_calculator.py:143
msgid "Potential Retake Costs"
msgstr "Costos potenciales de repetición"

#: pages/6_cost_calculator.py:148
msgid "Total Investment"
msgstr "Inversión total"

#: pages/6_cost_calculator.py:150
msgid "Total cost including all certifications, materials, and potential retakes"
msgstr "Costo total incluyendo todas las certificaciones, materiales y posibles repeticiones"

#: pages/6_cost_calculator.py:156
msgid "### 📊 Cost Distribution"
msgstr "### 📊 Distribución de costos"

#: pages/6_cost_calculator.py:163
msgid "Potential Retakes"
msgstr "Posibles repeticiones"

#: pages/6_cost_calculator.py:174
msgid "### 📋 Selected Certifications"
msgstr "### 📋 Certificaciones seleccionadas"

#: pages/6_cost_calculator.py:191
msgid "Retake Cost Analysis"
msgstr "Análisis de costos de repetición"

#: pages/6_cost_calculator.py:199
msgid "Select certifications to see cost breakdown"
msgstr "Seleccione certificaciones para ver el desglose de costos"

#: pages/7_ai_career_path.py:234 pages/7_ai_career_path.py:240
#: pages/7_ai_career_path.py:262 pages/7_ai_career_path.py:355
#: pages/7_ai_career_path.py:503 pages/7_ai_career_path.py:507
msgid "Service offline, please try again later"
msgstr "Servicio fuera de línea, inténtelo más tarde"

#: pages/7_ai_career_path.py:348
msgid "An unexpected error occurred"
msgstr "Ocurrió un error inesperado"

#: pages/7_ai_career_path.py:351 pages/7_ai_career_path.py:383
msgid "error"
msgstr "error"

#: pages/7_ai_career_path.py:367
msgid "Please fill in all required fields"
msgstr "Complete todos los campos obligatorios"

#: pages/7_ai_career_path.py:460 pages/7_ai_career_path.py:464
msgid "Unable to generate PDF report"
msgstr "No se pudo generar el informe PDF"

#: pages/7_ai_career_path.py:468
msgid "Unable to save your progress"
msgstr "No se pudo guardar su progreso"

#: pages/7_ai_career_path.py:479
msgid "Unable to display visualization"
msgstr "No se pudo mostrar la visualización"

#: pages/7_certrat_career_path.py:9
msgid "Welcome to CertRat CareerPath - your AI-powered certification advisor."
msgstr "Bienvenido a CertRat CareerPath - su asesor de certificación impulsado por IA."

#: pages/7_certrat_career_path.py:10
msgid "Get personalized certification recommendations based on your experience, interests, and career goals."
msgstr "Obtenga recomendaciones de certificación personalizadas basadas en su experiencia, intereses y objetivos profesionales."

#: utils/pdf_report.py:179
msgid "Certification Cost Breakdown"
msgstr "Desglose de costos de certificación"

#: utils/pdf_report.py:231
msgid "Total Investment Summary"
msgstr "Resumen de inversión total"