#: Translations template for PROJECT.
#: Copyright (C) 2025 CertPathFinder Team
#: This file is distributed under the same license as the PROJECT project.
#: FIRST AUTHOR <<EMAIL>>, 2025.
#:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CertPathFinder 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-24 18:14+0000\n"
"PO-Revision-Date: 2025-06-07 14:00+0000\n"
"Last-Translator: FULL NAME <<EMAIL>>\n"
"Language-Team: De Team\n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: main.py:100
msgid "Login with:"
msgstr "Anmelden mit:"

#: main.py:113 main.py:137
msgid "Select Language"
msgstr "Sprache auswählen"

#: main.py:127 main.py:146
msgid "Visualization"
msgstr "Visualisierung"

#: main.py:127
msgid "Listings"
msgstr "Auflistungen"

#: main.py:127 main.py:152
msgid "Study Time"
msgstr "Lernzeit"

#: main.py:127 main.py:154
msgid "Cost Calculator"
msgstr "Kostenrechner"

#: main.py:128 main.py:156
msgid "CertRat CareerPath"
msgstr "CertRat Karriereweg"

#: main.py:128 main.py:148
msgid "Admin"
msgstr "Verwaltung"

#: main.py:128 main.py:150
msgid "FAQ"
msgstr "FAQ"

#: pages/1_visualization.py:22
msgid "Certification Path Visualization"
msgstr "Zertifizierungsweg-Visualisierung"

#: pages/1_visualization.py:23
msgid ""
"\n"
"    Explore certification paths and relationships through an interactive "
"visualization.\n"
"    The graph shows certification progression paths and domain "
"relationships.\n"
"    "
msgstr ""

#: pages/1_visualization.py:29
msgid "Visualization Filters"
msgstr "Visualisierungsfilter"

#: pages/1_visualization.py:43
msgid "Focus Domain"
msgstr "Fokusdomäne"

#: pages/1_visualization.py:83
msgid "Select a domain to visualize its certification paths."
msgstr "Wählen Sie eine Domäne aus, um ihre Zertifizierungswege zu visualisieren."

#: pages/6_cost_calculator.py:26
msgid ""
"\n"
"    Calculate the total investment needed for your certification journey,"
" including:\n"
"    - Exam fees and study materials\n"
"    - Potential retake costs\n"
"    - Currency conversion\n"
"    - Regional price variations\n"
"    "
msgstr ""

#: pages/6_cost_calculator.py:47
msgid "Select Certifications"
msgstr "Zertifizierungen auswählen"

#: pages/6_cost_calculator.py:49
msgid "Choose the certifications you want to include in the cost calculation"
msgstr "Wählen Sie die Zertifizierungen aus, die Sie in die Kostenberechnung einbeziehen möchten"

#: pages/6_cost_calculator.py:54
msgid "Study Materials Cost ($)"
msgstr "Kosten für Lernmaterialien ($)"

#: pages/6_cost_calculator.py:58
msgid "Estimated cost of books, online courses, practice exams, etc."
msgstr "Geschätzte Kosten für Bücher, Online-Kurse, Übungsprüfungen usw."

#: pages/6_cost_calculator.py:67
msgid "Select Currency"
msgstr "Währung auswählen"

#: pages/6_cost_calculator.py:69
msgid "Select your preferred currency"
msgstr "Wählen Sie Ihre bevorzugte Währung"

#: pages/6_cost_calculator.py:73
msgid "📚 Exam Retake Analysis"
msgstr "📚 Prüfungswiederholungsanalyse"

#: pages/6_cost_calculator.py:77
msgid "Minimum Days Between Attempts"
msgstr "Mindestanzahl Tage zwischen Versuchen"

#: pages/6_cost_calculator.py:81
msgid "Typical waiting period required between exam attempts"
msgstr "Typische Wartezeit zwischen Prüfungsversuchen"

#: pages/6_cost_calculator.py:84
msgid "Retake Discount (%)"
msgstr "Wiederholungsrabatt (%)"

#: pages/6_cost_calculator.py:88
msgid "Some certifications offer discounts on retakes"
msgstr "Einige Zertifizierungen bieten Rabatte auf Wiederholungen"

#: pages/6_cost_calculator.py:91
msgid "Maximum Attempts Considered"
msgstr "Maximale berücksichtigte Versuche"

#: pages/6_cost_calculator.py:95
msgid "Number of potential attempts to include in cost analysis"
msgstr "Anzahl potenzieller Versuche für die Kostenanalyse"

#: pages/6_cost_calculator.py:127
msgid "Cost Breakdown"
msgstr "Kostenaufschlüsselung"

#: pages/6_cost_calculator.py:133 pages/6_cost_calculator.py:161
msgid "Base Exam Fees"
msgstr "Grundprüfungsgebühren"

#: pages/6_cost_calculator.py:138 pages/6_cost_calculator.py:162
msgid "Study Materials"
msgstr "Lernmaterialien"

#: pages/6_cost_calculator.py:143
msgid "Potential Retake Costs"
msgstr "Potenzielle Wiederholungskosten"

#: pages/6_cost_calculator.py:148
msgid "Total Investment"
msgstr "Gesamtinvestition"

#: pages/6_cost_calculator.py:150
msgid "Total cost including all certifications, materials, and potential retakes"
msgstr "Gesamtkosten einschließlich aller Zertifizierungen, Materialien und potenzieller Wiederholungen"

#: pages/6_cost_calculator.py:156
msgid "### 📊 Cost Distribution"
msgstr "### 📊 Kostenverteilung"

#: pages/6_cost_calculator.py:163
msgid "Potential Retakes"
msgstr "Potenzielle Wiederholungen"

#: pages/6_cost_calculator.py:174
msgid "### 📋 Selected Certifications"
msgstr "### 📋 Ausgewählte Zertifizierungen"

#: pages/6_cost_calculator.py:191
msgid "Retake Cost Analysis"
msgstr "Wiederholungskostenanalyse"

#: pages/6_cost_calculator.py:199
msgid "Select certifications to see cost breakdown"
msgstr "Wählen Sie Zertifizierungen aus, um die Kostenaufschlüsselung zu sehen"

#: pages/7_ai_career_path.py:234 pages/7_ai_career_path.py:240
#: pages/7_ai_career_path.py:262 pages/7_ai_career_path.py:355
#: pages/7_ai_career_path.py:503 pages/7_ai_career_path.py:507
msgid "Service offline, please try again later"
msgstr "Service offline, bitte versuchen Sie es später erneut"

#: pages/7_ai_career_path.py:348
msgid "An unexpected error occurred"
msgstr "Ein unerwarteter Fehler ist aufgetreten"

#: pages/7_ai_career_path.py:351 pages/7_ai_career_path.py:383
msgid "error"
msgstr "Fehler"

#: pages/7_ai_career_path.py:367
msgid "Please fill in all required fields"
msgstr "Bitte füllen Sie alle Pflichtfelder aus"

#: pages/7_ai_career_path.py:460 pages/7_ai_career_path.py:464
msgid "Unable to generate PDF report"
msgstr "PDF-Bericht konnte nicht erstellt werden"

#: pages/7_ai_career_path.py:468
msgid "Unable to save your progress"
msgstr "Fortschritt konnte nicht gespeichert werden"

#: pages/7_ai_career_path.py:479
msgid "Unable to display visualization"
msgstr "Visualisierung konnte nicht angezeigt werden"

#: pages/7_certrat_career_path.py:9
msgid "Welcome to CertRat CareerPath - your AI-powered certification advisor."
msgstr "Willkommen bei CertRat CareerPath - Ihrem KI-gestützten Zertifizierungsberater."

#: pages/7_certrat_career_path.py:10
msgid "Get personalized certification recommendations based on your experience, interests, and career goals."
msgstr "Erhalten Sie personalisierte Zertifizierungsempfehlungen basierend auf Ihrer Erfahrung, Ihren Interessen und Karrierezielen."

#: utils/pdf_report.py:179
msgid "Certification Cost Breakdown"
msgstr "Zertifizierungskostenaufschlüsselung"

#: utils/pdf_report.py:231
msgid "Total Investment Summary"
msgstr "Zusammenfassung der Gesamtinvestition"