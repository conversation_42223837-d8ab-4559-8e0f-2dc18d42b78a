#: Translations template for PROJECT.
#: Copyright (C) 2025 CertPathFinder Team
#: This file is distributed under the same license as the PROJECT project.
#: FIRST AUTHOR <<EMAIL>>, 2025.
#:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CertPathFinder 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-24 18:14+0000\n"
"PO-Revision-Date: 2025-06-07 14:00+0000\n"
"Last-Translator: FULL NAME <<EMAIL>>\n"
"Language-Team: Fr Team\n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: main.py:100
msgid "Login with:"
msgstr "Se connecter avec :"

#: main.py:113 main.py:137
msgid "Select Language"
msgstr "Sélectionner la langue"

#: main.py:127 main.py:146
msgid "Visualization"
msgstr "Visualisation"

#: main.py:127
msgid "Listings"
msgstr "Listes"

#: main.py:127 main.py:152
msgid "Study Time"
msgstr "Temps d'étude"

#: main.py:127 main.py:154
msgid "Cost Calculator"
msgstr "Calculateur de coûts"

#: main.py:128 main.py:156
msgid "CertRat CareerPath"
msgstr "Parcours professionnel CertRat"

#: main.py:128 main.py:148
msgid "Admin"
msgstr "Administration"

#: main.py:128 main.py:150
msgid "FAQ"
msgstr "FAQ"

#: pages/1_visualization.py:22
msgid "Certification Path Visualization"
msgstr "Visualisation des parcours de certification"

#: pages/1_visualization.py:23
msgid ""
"\n"
"    Explore certification paths and relationships through an interactive "
"visualization.\n"
"    The graph shows certification progression paths and domain "
"relationships.\n"
"    "
msgstr ""

#: pages/1_visualization.py:29
msgid "Visualization Filters"
msgstr "Filtres de visualisation"

#: pages/1_visualization.py:43
msgid "Focus Domain"
msgstr "Domaine de focus"

#: pages/1_visualization.py:83
msgid "Select a domain to visualize its certification paths."
msgstr "Sélectionnez un domaine pour visualiser ses parcours de certification."

#: pages/6_cost_calculator.py:26
msgid ""
"\n"
"    Calculate the total investment needed for your certification journey,"
" including:\n"
"    - Exam fees and study materials\n"
"    - Potential retake costs\n"
"    - Currency conversion\n"
"    - Regional price variations\n"
"    "
msgstr ""

#: pages/6_cost_calculator.py:47
msgid "Select Certifications"
msgstr "Sélectionner les certifications"

#: pages/6_cost_calculator.py:49
msgid "Choose the certifications you want to include in the cost calculation"
msgstr "Choisissez les certifications que vous souhaitez inclure dans le calcul des coûts"

#: pages/6_cost_calculator.py:54
msgid "Study Materials Cost ($)"
msgstr "Coût des matériaux d'étude ($)"

#: pages/6_cost_calculator.py:58
msgid "Estimated cost of books, online courses, practice exams, etc."
msgstr "Coût estimé des livres, cours en ligne, examens pratiques, etc."

#: pages/6_cost_calculator.py:67
msgid "Select Currency"
msgstr "Sélectionner la devise"

#: pages/6_cost_calculator.py:69
msgid "Select your preferred currency"
msgstr "Sélectionnez votre devise préférée"

#: pages/6_cost_calculator.py:73
msgid "📚 Exam Retake Analysis"
msgstr "📚 Analyse de reprise d'examen"

#: pages/6_cost_calculator.py:77
msgid "Minimum Days Between Attempts"
msgstr "Jours minimum entre les tentatives"

#: pages/6_cost_calculator.py:81
msgid "Typical waiting period required between exam attempts"
msgstr "Période d'attente typique requise entre les tentatives d'examen"

#: pages/6_cost_calculator.py:84
msgid "Retake Discount (%)"
msgstr "Remise de reprise (%)"

#: pages/6_cost_calculator.py:88
msgid "Some certifications offer discounts on retakes"
msgstr "Certaines certifications offrent des remises sur les reprises"

#: pages/6_cost_calculator.py:91
msgid "Maximum Attempts Considered"
msgstr "Tentatives maximum considérées"

#: pages/6_cost_calculator.py:95
msgid "Number of potential attempts to include in cost analysis"
msgstr "Nombre de tentatives potentielles à inclure dans l'analyse des coûts"

#: pages/6_cost_calculator.py:127
msgid "Cost Breakdown"
msgstr "Répartition des coûts"

#: pages/6_cost_calculator.py:133 pages/6_cost_calculator.py:161
msgid "Base Exam Fees"
msgstr "Frais d'examen de base"

#: pages/6_cost_calculator.py:138 pages/6_cost_calculator.py:162
msgid "Study Materials"
msgstr "Matériel d'étude"

#: pages/6_cost_calculator.py:143
msgid "Potential Retake Costs"
msgstr "Coûts potentiels de reprise"

#: pages/6_cost_calculator.py:148
msgid "Total Investment"
msgstr "Investissement total"

#: pages/6_cost_calculator.py:150
msgid "Total cost including all certifications, materials, and potential retakes"
msgstr "Coût total incluant toutes les certifications, matériaux et reprises potentielles"

#: pages/6_cost_calculator.py:156
msgid "### 📊 Cost Distribution"
msgstr "### 📊 Répartition des coûts"

#: pages/6_cost_calculator.py:163
msgid "Potential Retakes"
msgstr "Reprises potentielles"

#: pages/6_cost_calculator.py:174
msgid "### 📋 Selected Certifications"
msgstr "### 📋 Certifications sélectionnées"

#: pages/6_cost_calculator.py:191
msgid "Retake Cost Analysis"
msgstr "Analyse des coûts de reprise"

#: pages/6_cost_calculator.py:199
msgid "Select certifications to see cost breakdown"
msgstr "Sélectionnez les certifications pour voir la répartition des coûts"

#: pages/7_ai_career_path.py:234 pages/7_ai_career_path.py:240
#: pages/7_ai_career_path.py:262 pages/7_ai_career_path.py:355
#: pages/7_ai_career_path.py:503 pages/7_ai_career_path.py:507
msgid "Service offline, please try again later"
msgstr "Service hors ligne, veuillez réessayer plus tard"

#: pages/7_ai_career_path.py:348
msgid "An unexpected error occurred"
msgstr "Une erreur inattendue s'est produite"

#: pages/7_ai_career_path.py:351 pages/7_ai_career_path.py:383
msgid "error"
msgstr "erreur"

#: pages/7_ai_career_path.py:367
msgid "Please fill in all required fields"
msgstr "Veuillez remplir tous les champs obligatoires"

#: pages/7_ai_career_path.py:460 pages/7_ai_career_path.py:464
msgid "Unable to generate PDF report"
msgstr "Impossible de générer le rapport PDF"

#: pages/7_ai_career_path.py:468
msgid "Unable to save your progress"
msgstr "Impossible de sauvegarder vos progrès"

#: pages/7_ai_career_path.py:479
msgid "Unable to display visualization"
msgstr "Impossible d'afficher la visualisation"

#: pages/7_certrat_career_path.py:9
msgid "Welcome to CertRat CareerPath - your AI-powered certification advisor."
msgstr "Bienvenue sur CertRat CareerPath - votre conseiller en certification alimenté par l'IA."

#: pages/7_certrat_career_path.py:10
msgid "Get personalized certification recommendations based on your experience, interests, and career goals."
msgstr "Obtenez des recommandations de certification personnalisées basées sur votre expérience, vos intérêts et vos objectifs de carrière."

#: utils/pdf_report.py:179
msgid "Certification Cost Breakdown"
msgstr "Répartition des coûts de certification"

#: utils/pdf_report.py:231
msgid "Total Investment Summary"
msgstr "Résumé de l'investissement total"