#: Translations template for PROJECT.
#: Copyright (C) 2025 CertPathFinder Team
#: This file is distributed under the same license as the PROJECT project.
#: FIRST AUTHOR <<EMAIL>>, 2025.
#:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CertPathFinder 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-24 18:14+0000\n"
"PO-Revision-Date: 2025-06-07 14:05+0000\n"
"Last-Translator: FULL NAME <<EMAIL>>\n"
"Language-Team: Ko Team\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: main.py:100
msgid "Login with:"
msgstr "로그인:"

#: main.py:113 main.py:137
msgid "Select Language"
msgstr "언어 선택"

#: main.py:127 main.py:146
msgid "Visualization"
msgstr "시각화"

#: main.py:127
msgid "Listings"
msgstr "목록"

#: main.py:127 main.py:152
msgid "Study Time"
msgstr "학습 시간"

#: main.py:127 main.py:154
msgid "Cost Calculator"
msgstr "비용 계산기"

#: main.py:128 main.py:156
msgid "CertRat CareerPath"
msgstr "CertRat 커리어패스"

#: main.py:128 main.py:148
msgid "Admin"
msgstr "관리자"

#: main.py:128 main.py:150
msgid "FAQ"
msgstr "자주 묻는 질문"

#: pages/1_visualization.py:22
msgid "Certification Path Visualization"
msgstr "인증 경로 시각화"

#: pages/1_visualization.py:23
msgid ""
"\n"
"    Explore certification paths and relationships through an interactive "
"visualization.\n"
"    The graph shows certification progression paths and domain "
"relationships.\n"
"    "
msgstr ""

#: pages/1_visualization.py:29
msgid "Visualization Filters"
msgstr "시각화 필터"

#: pages/1_visualization.py:43
msgid "Focus Domain"
msgstr "포커스 도메인"

#: pages/1_visualization.py:83
msgid "Select a domain to visualize its certification paths."
msgstr "인증 경로를 시각화할 도메인을 선택하세요."

#: pages/6_cost_calculator.py:26
msgid ""
"\n"
"    Calculate the total investment needed for your certification journey,"
" including:\n"
"    - Exam fees and study materials\n"
"    - Potential retake costs\n"
"    - Currency conversion\n"
"    - Regional price variations\n"
"    "
msgstr ""

#: pages/6_cost_calculator.py:47
msgid "Select Certifications"
msgstr "인증 선택"

#: pages/6_cost_calculator.py:49
msgid "Choose the certifications you want to include in the cost calculation"
msgstr "비용 계산에 포함할 인증을 선택하세요"

#: pages/6_cost_calculator.py:54
msgid "Study Materials Cost ($)"
msgstr "학습 자료 비용 ($)"

#: pages/6_cost_calculator.py:58
msgid "Estimated cost of books, online courses, practice exams, etc."
msgstr "책, 온라인 강의, 모의시험 등의 예상 비용"

#: pages/6_cost_calculator.py:67
msgid "Select Currency"
msgstr "통화 선택"

#: pages/6_cost_calculator.py:69
msgid "Select your preferred currency"
msgstr "선호하는 통화를 선택하세요"

#: pages/6_cost_calculator.py:73
msgid "📚 Exam Retake Analysis"
msgstr "📚 시험 재응시 분석"

#: pages/6_cost_calculator.py:77
msgid "Minimum Days Between Attempts"
msgstr "응시 간 최소 일수"

#: pages/6_cost_calculator.py:81
msgid "Typical waiting period required between exam attempts"
msgstr "시험 응시 간 일반적인 대기 기간"

#: pages/6_cost_calculator.py:84
msgid "Retake Discount (%)"
msgstr "재응시 할인 (%)"

#: pages/6_cost_calculator.py:88
msgid "Some certifications offer discounts on retakes"
msgstr "일부 인증은 재응시 시 할인을 제공합니다"

#: pages/6_cost_calculator.py:91
msgid "Maximum Attempts Considered"
msgstr "고려되는 최대 응시 횟수"

#: pages/6_cost_calculator.py:95
msgid "Number of potential attempts to include in cost analysis"
msgstr "비용 분석에 포함할 잠재적 응시 횟수"

#: pages/6_cost_calculator.py:127
msgid "Cost Breakdown"
msgstr "비용 분석"

#: pages/6_cost_calculator.py:133 pages/6_cost_calculator.py:161
msgid "Base Exam Fees"
msgstr "기본 시험 수수료"

#: pages/6_cost_calculator.py:138 pages/6_cost_calculator.py:162
msgid "Study Materials"
msgstr "학습 자료"

#: pages/6_cost_calculator.py:143
msgid "Potential Retake Costs"
msgstr "잠재적 재응시 비용"

#: pages/6_cost_calculator.py:148
msgid "Total Investment"
msgstr "총 투자"

#: pages/6_cost_calculator.py:150
msgid "Total cost including all certifications, materials, and potential retakes"
msgstr "모든 인증, 자료 및 잠재적 재응시를 포함한 총 비용"

#: pages/6_cost_calculator.py:156
msgid "### 📊 Cost Distribution"
msgstr "### 📊 비용 분배"

#: pages/6_cost_calculator.py:163
msgid "Potential Retakes"
msgstr "잠재적 재응시"

#: pages/6_cost_calculator.py:174
msgid "### 📋 Selected Certifications"
msgstr "### 📋 선택된 인증"

#: pages/6_cost_calculator.py:191
msgid "Retake Cost Analysis"
msgstr "재응시 비용 분석"

#: pages/6_cost_calculator.py:199
msgid "Select certifications to see cost breakdown"
msgstr "비용 분석을 보려면 인증을 선택하세요"

#: pages/7_ai_career_path.py:234 pages/7_ai_career_path.py:240
#: pages/7_ai_career_path.py:262 pages/7_ai_career_path.py:355
#: pages/7_ai_career_path.py:503 pages/7_ai_career_path.py:507
msgid "Service offline, please try again later"
msgstr "서비스가 오프라인입니다. 나중에 다시 시도해주세요"

#: pages/7_ai_career_path.py:348
msgid "An unexpected error occurred"
msgstr "예상치 못한 오류가 발생했습니다"

#: pages/7_ai_career_path.py:351 pages/7_ai_career_path.py:383
msgid "error"
msgstr "오류"

#: pages/7_ai_career_path.py:367
msgid "Please fill in all required fields"
msgstr "모든 필수 필드를 입력해주세요"

#: pages/7_ai_career_path.py:460 pages/7_ai_career_path.py:464
msgid "Unable to generate PDF report"
msgstr "PDF 보고서를 생성할 수 없습니다"

#: pages/7_ai_career_path.py:468
msgid "Unable to save your progress"
msgstr "진행 상황을 저장할 수 없습니다"

#: pages/7_ai_career_path.py:479
msgid "Unable to display visualization"
msgstr "시각화를 표시할 수 없습니다"

#: pages/7_certrat_career_path.py:9
msgid "Welcome to CertRat CareerPath - your AI-powered certification advisor."
msgstr "CertRat CareerPath에 오신 것을 환영합니다 - AI 기반 인증 어드바이저입니다."

#: pages/7_certrat_career_path.py:10
msgid "Get personalized certification recommendations based on your experience, interests, and career goals."
msgstr "경험, 관심사 및 커리어 목표를 바탕으로 개인화된 인증 추천을 받으세요."

#: utils/pdf_report.py:179
msgid "Certification Cost Breakdown"
msgstr "인증 비용 분석"

#: utils/pdf_report.py:231
msgid "Total Investment Summary"
msgstr "총 투자 요약"