#: Translations template for PROJECT.
#: Copyright (C) 2025 CertPathFinder Team
#: This file is distributed under the same license as the PROJECT project.
#: FIRST AUTHOR <<EMAIL>>, 2025.
#:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CertPathFinder 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-24 18:14+0000\n"
"PO-Revision-Date: 2025-06-07 14:05+0000\n"
"Last-Translator: FULL NAME <<EMAIL>>\n"
"Language-Team: Ar Team\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: main.py:100
msgid "Login with:"
msgstr "تسجيل الدخول بـ:"

#: main.py:113 main.py:137
msgid "Select Language"
msgstr "اختر اللغة"

#: main.py:127 main.py:146
msgid "Visualization"
msgstr "التصور"

#: main.py:127
msgid "Listings"
msgstr "القوائم"

#: main.py:127 main.py:152
msgid "Study Time"
msgstr "وقت الدراسة"

#: main.py:127 main.py:154
msgid "Cost Calculator"
msgstr "حاسبة التكلفة"

#: main.py:128 main.py:156
msgid "CertRat CareerPath"
msgstr "مسار CertRat المهني"

#: main.py:128 main.py:148
msgid "Admin"
msgstr "المدير"

#: main.py:128 main.py:150
msgid "FAQ"
msgstr "الأسئلة الشائعة"

#: pages/1_visualization.py:22
msgid "Certification Path Visualization"
msgstr "تصور مسار الشهادات"

#: pages/1_visualization.py:23
msgid ""
"\n"
"    Explore certification paths and relationships through an interactive "
"visualization.\n"
"    The graph shows certification progression paths and domain "
"relationships.\n"
"    "
msgstr ""

#: pages/1_visualization.py:29
msgid "Visualization Filters"
msgstr "مرشحات التصور"

#: pages/1_visualization.py:43
msgid "Focus Domain"
msgstr "المجال المحوري"

#: pages/1_visualization.py:83
msgid "Select a domain to visualize its certification paths."
msgstr "اختر مجالاً لتصور مسارات الشهادات الخاصة به."

#: pages/6_cost_calculator.py:26
msgid ""
"\n"
"    Calculate the total investment needed for your certification journey,"
" including:\n"
"    - Exam fees and study materials\n"
"    - Potential retake costs\n"
"    - Currency conversion\n"
"    - Regional price variations\n"
"    "
msgstr ""

#: pages/6_cost_calculator.py:47
msgid "Select Certifications"
msgstr "اختر الشهادات"

#: pages/6_cost_calculator.py:49
msgid "Choose the certifications you want to include in the cost calculation"
msgstr "اختر الشهادات التي تريد تضمينها في حساب التكلفة"

#: pages/6_cost_calculator.py:54
msgid "Study Materials Cost ($)"
msgstr "تكلفة مواد الدراسة ($)"

#: pages/6_cost_calculator.py:58
msgid "Estimated cost of books, online courses, practice exams, etc."
msgstr "التكلفة المقدرة للكتب والدورات الإلكترونية والامتحانات التدريبية وغيرها"

#: pages/6_cost_calculator.py:67
msgid "Select Currency"
msgstr "اختر العملة"

#: pages/6_cost_calculator.py:69
msgid "Select your preferred currency"
msgstr "اختر عملتك المفضلة"

#: pages/6_cost_calculator.py:73
msgid "📚 Exam Retake Analysis"
msgstr "📚 تحليل إعادة الامتحان"

#: pages/6_cost_calculator.py:77
msgid "Minimum Days Between Attempts"
msgstr "الحد الأدنى للأيام بين المحاولات"

#: pages/6_cost_calculator.py:81
msgid "Typical waiting period required between exam attempts"
msgstr "فترة الانتظار المعتادة المطلوبة بين محاولات الامتحان"

#: pages/6_cost_calculator.py:84
msgid "Retake Discount (%)"
msgstr "خصم الإعادة (%)"

#: pages/6_cost_calculator.py:88
msgid "Some certifications offer discounts on retakes"
msgstr "بعض الشهادات تقدم خصومات على الإعادة"

#: pages/6_cost_calculator.py:91
msgid "Maximum Attempts Considered"
msgstr "الحد الأقصى للمحاولات المعتبرة"

#: pages/6_cost_calculator.py:95
msgid "Number of potential attempts to include in cost analysis"
msgstr "عدد المحاولات المحتملة لتضمينها في تحليل التكلفة"

#: pages/6_cost_calculator.py:127
msgid "Cost Breakdown"
msgstr "تفصيل التكلفة"

#: pages/6_cost_calculator.py:133 pages/6_cost_calculator.py:161
msgid "Base Exam Fees"
msgstr "رسوم الامتحان الأساسية"

#: pages/6_cost_calculator.py:138 pages/6_cost_calculator.py:162
msgid "Study Materials"
msgstr "مواد الدراسة"

#: pages/6_cost_calculator.py:143
msgid "Potential Retake Costs"
msgstr "تكاليف الإعادة المحتملة"

#: pages/6_cost_calculator.py:148
msgid "Total Investment"
msgstr "إجمالي الاستثمار"

#: pages/6_cost_calculator.py:150
msgid "Total cost including all certifications, materials, and potential retakes"
msgstr "التكلفة الإجمالية شاملة جميع الشهادات والمواد والإعادات المحتملة"

#: pages/6_cost_calculator.py:156
msgid "### 📊 Cost Distribution"
msgstr "### 📊 توزيع التكلفة"

#: pages/6_cost_calculator.py:163
msgid "Potential Retakes"
msgstr "الإعادات المحتملة"

#: pages/6_cost_calculator.py:174
msgid "### 📋 Selected Certifications"
msgstr "### 📋 الشهادات المختارة"

#: pages/6_cost_calculator.py:191
msgid "Retake Cost Analysis"
msgstr "تحليل تكلفة الإعادة"

#: pages/6_cost_calculator.py:199
msgid "Select certifications to see cost breakdown"
msgstr "اختر الشهادات لرؤية تفصيل التكلفة"

#: pages/7_ai_career_path.py:234 pages/7_ai_career_path.py:240
#: pages/7_ai_career_path.py:262 pages/7_ai_career_path.py:355
#: pages/7_ai_career_path.py:503 pages/7_ai_career_path.py:507
msgid "Service offline, please try again later"
msgstr "الخدمة غير متاحة، يرجى المحاولة لاحقاً"

#: pages/7_ai_career_path.py:348
msgid "An unexpected error occurred"
msgstr "حدث خطأ غير متوقع"

#: pages/7_ai_career_path.py:351 pages/7_ai_career_path.py:383
msgid "error"
msgstr "خطأ"

#: pages/7_ai_career_path.py:367
msgid "Please fill in all required fields"
msgstr "يرجى ملء جميع الحقول المطلوبة"

#: pages/7_ai_career_path.py:460 pages/7_ai_career_path.py:464
msgid "Unable to generate PDF report"
msgstr "غير قادر على إنشاء تقرير PDF"

#: pages/7_ai_career_path.py:468
msgid "Unable to save your progress"
msgstr "غير قادر على حفظ تقدمك"

#: pages/7_ai_career_path.py:479
msgid "Unable to display visualization"
msgstr "غير قادر على عرض التصور"

#: pages/7_certrat_career_path.py:9
msgid "Welcome to CertRat CareerPath - your AI-powered certification advisor."
msgstr "مرحباً بك في CertRat CareerPath - مستشارك للشهادات المدعوم بالذكاء الاصطناعي."

#: pages/7_certrat_career_path.py:10
msgid "Get personalized certification recommendations based on your experience, interests, and career goals."
msgstr "احصل على توصيات شهادات مخصصة بناءً على خبرتك واهتماماتك وأهدافك المهنية."

#: utils/pdf_report.py:179
msgid "Certification Cost Breakdown"
msgstr "تفصيل تكلفة الشهادة"

#: utils/pdf_report.py:231
msgid "Total Investment Summary"
msgstr "ملخص إجمالي الاستثمار"