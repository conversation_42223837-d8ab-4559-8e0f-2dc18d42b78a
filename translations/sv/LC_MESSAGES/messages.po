#: Translations template for PROJECT.
#: Copyright (C) 2025 CertPathFinder Team
#: This file is distributed under the same license as the PROJECT project.
#: FIRST AUTHOR <<EMAIL>>, 2025.
#:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CertPathFinder 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-24 18:14+0000\n"
"PO-Revision-Date: 2025-06-07 14:05+0000\n"
"Last-Translator: FULL NAME <<EMAIL>>\n"
"Language-Team: Sv Team\n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: main.py:100
msgid "Login with:"
msgstr "Logga in med:"

#: main.py:113 main.py:137
msgid "Select Language"
msgstr "Välj språk"

#: main.py:127 main.py:146
msgid "Visualization"
msgstr "Visualisering"

#: main.py:127
msgid "Listings"
msgstr "Listor"

#: main.py:127 main.py:152
msgid "Study Time"
msgstr "Studietid"

#: main.py:127 main.py:154
msgid "Cost Calculator"
msgstr "Kostnadskalkylator"

#: main.py:128 main.py:156
msgid "CertRat CareerPath"
msgstr "CertRat Karriärväg"

#: main.py:128 main.py:148
msgid "Admin"
msgstr "Administratör"

#: main.py:128 main.py:150
msgid "FAQ"
msgstr "Vanliga frågor"

#: pages/1_visualization.py:22
msgid "Certification Path Visualization"
msgstr "Certifieringsväg visualisering"

#: pages/1_visualization.py:23
msgid ""
"\n"
"    Explore certification paths and relationships through an interactive "
"visualization.\n"
"    The graph shows certification progression paths and domain "
"relationships.\n"
"    "
msgstr ""

#: pages/1_visualization.py:29
msgid "Visualization Filters"
msgstr "Visualiseringsfilter"

#: pages/1_visualization.py:43
msgid "Focus Domain"
msgstr "Fokusområde"

#: pages/1_visualization.py:83
msgid "Select a domain to visualize its certification paths."
msgstr "Välj ett område för att visualisera dess certifieringsvägar."

#: pages/6_cost_calculator.py:26
msgid ""
"\n"
"    Calculate the total investment needed for your certification journey,"
" including:\n"
"    - Exam fees and study materials\n"
"    - Potential retake costs\n"
"    - Currency conversion\n"
"    - Regional price variations\n"
"    "
msgstr ""

#: pages/6_cost_calculator.py:47
msgid "Select Certifications"
msgstr "Välj certifieringar"

#: pages/6_cost_calculator.py:49
msgid "Choose the certifications you want to include in the cost calculation"
msgstr "Välj de certifieringar du vill inkludera i kostnadsberäkningen"

#: pages/6_cost_calculator.py:54
msgid "Study Materials Cost ($)"
msgstr "Studiematerial kostnad ($)"

#: pages/6_cost_calculator.py:58
msgid "Estimated cost of books, online courses, practice exams, etc."
msgstr "Uppskattad kostnad för böcker, onlinekurser, övningsexamina, etc."

#: pages/6_cost_calculator.py:67
msgid "Select Currency"
msgstr "Välj valuta"

#: pages/6_cost_calculator.py:69
msgid "Select your preferred currency"
msgstr "Välj din föredragna valuta"

#: pages/6_cost_calculator.py:73
msgid "📚 Exam Retake Analysis"
msgstr "📚 Examen omtagning analys"

#: pages/6_cost_calculator.py:77
msgid "Minimum Days Between Attempts"
msgstr ""

#: pages/6_cost_calculator.py:81
msgid "Typical waiting period required between exam attempts"
msgstr ""

#: pages/6_cost_calculator.py:84
msgid "Retake Discount (%)"
msgstr ""

#: pages/6_cost_calculator.py:88
msgid "Some certifications offer discounts on retakes"
msgstr ""

#: pages/6_cost_calculator.py:91
msgid "Maximum Attempts Considered"
msgstr ""

#: pages/6_cost_calculator.py:95
msgid "Number of potential attempts to include in cost analysis"
msgstr ""

#: pages/6_cost_calculator.py:127
msgid "Cost Breakdown"
msgstr ""

#: pages/6_cost_calculator.py:133 pages/6_cost_calculator.py:161
msgid "Base Exam Fees"
msgstr ""

#: pages/6_cost_calculator.py:138 pages/6_cost_calculator.py:162
msgid "Study Materials"
msgstr ""

#: pages/6_cost_calculator.py:143
msgid "Potential Retake Costs"
msgstr ""

#: pages/6_cost_calculator.py:148
msgid "Total Investment"
msgstr ""

#: pages/6_cost_calculator.py:150
msgid "Total cost including all certifications, materials, and potential retakes"
msgstr ""

#: pages/6_cost_calculator.py:156
msgid "### 📊 Cost Distribution"
msgstr ""

#: pages/6_cost_calculator.py:163
msgid "Potential Retakes"
msgstr ""

#: pages/6_cost_calculator.py:174
msgid "### 📋 Selected Certifications"
msgstr ""

#: pages/6_cost_calculator.py:191
msgid "Retake Cost Analysis"
msgstr ""

#: pages/6_cost_calculator.py:199
msgid "Select certifications to see cost breakdown"
msgstr ""

#: pages/7_ai_career_path.py:234 pages/7_ai_career_path.py:240
#: pages/7_ai_career_path.py:262 pages/7_ai_career_path.py:355
#: pages/7_ai_career_path.py:503 pages/7_ai_career_path.py:507
msgid "Service offline, please try again later"
msgstr "Tjänsten är offline, försök igen senare"

#: pages/7_ai_career_path.py:348
msgid "An unexpected error occurred"
msgstr "Ett oväntat fel inträffade"

#: pages/7_ai_career_path.py:351 pages/7_ai_career_path.py:383
msgid "error"
msgstr "fel"

#: pages/7_ai_career_path.py:367
msgid "Please fill in all required fields"
msgstr "Vänligen fyll i alla obligatoriska fält"

#: pages/7_ai_career_path.py:460 pages/7_ai_career_path.py:464
msgid "Unable to generate PDF report"
msgstr ""

#: pages/7_ai_career_path.py:468
msgid "Unable to save your progress"
msgstr ""

#: pages/7_ai_career_path.py:479
msgid "Unable to display visualization"
msgstr ""

#: pages/7_certrat_career_path.py:9
msgid "Welcome to CertRat CareerPath - your AI-powered certification advisor."
msgstr "Välkommen till CertRat CareerPath - din AI-drivna certifieringsrådgivare."

#: pages/7_certrat_career_path.py:10
msgid "Get personalized certification recommendations based on your experience, interests, and career goals."
msgstr "Få personliga certifieringsrekommendationer baserat på din erfarenhet, intressen och karriärmål."

#: utils/pdf_report.py:179
msgid "Certification Cost Breakdown"
msgstr ""

#: utils/pdf_report.py:231
msgid "Total Investment Summary"
msgstr ""