#: Translations template for PROJECT.
#: Copyright (C) 2025 CertPathFinder Team
#: This file is distributed under the same license as the PROJECT project.
#: FIRST AUTHOR <<EMAIL>>, 2025.
#:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CertPathFinder 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-24 18:14+0000\n"
"PO-Revision-Date: 2025-06-07 14:05+0000\n"
"Last-Translator: FULL NAME <<EMAIL>>\n"
"Language-Team: Zh Team\n"
"Language: zh\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: main.py:100
msgid "Login with:"
msgstr "登录方式："

#: main.py:113 main.py:137
msgid "Select Language"
msgstr "选择语言"

#: main.py:127 main.py:146
msgid "Visualization"
msgstr "可视化"

#: main.py:127
msgid "Listings"
msgstr "列表"

#: main.py:127 main.py:152
msgid "Study Time"
msgstr "学习时间"

#: main.py:127 main.py:154
msgid "Cost Calculator"
msgstr "成本计算器"

#: main.py:128 main.py:156
msgid "CertRat CareerPath"
msgstr "CertRat职业路径"

#: main.py:128 main.py:148
msgid "Admin"
msgstr "管理员"

#: main.py:128 main.py:150
msgid "FAQ"
msgstr "常见问题"

#: pages/1_visualization.py:22
msgid "Certification Path Visualization"
msgstr "认证路径可视化"

#: pages/1_visualization.py:23
msgid ""
"\n"
"    Explore certification paths and relationships through an interactive "
"visualization.\n"
"    The graph shows certification progression paths and domain "
"relationships.\n"
"    "
msgstr ""

#: pages/1_visualization.py:29
msgid "Visualization Filters"
msgstr "可视化过滤器"

#: pages/1_visualization.py:43
msgid "Focus Domain"
msgstr "重点领域"

#: pages/1_visualization.py:83
msgid "Select a domain to visualize its certification paths."
msgstr "选择一个领域来可视化其认证路径。"

#: pages/6_cost_calculator.py:26
msgid ""
"\n"
"    Calculate the total investment needed for your certification journey,"
" including:\n"
"    - Exam fees and study materials\n"
"    - Potential retake costs\n"
"    - Currency conversion\n"
"    - Regional price variations\n"
"    "
msgstr ""

#: pages/6_cost_calculator.py:47
msgid "Select Certifications"
msgstr "选择认证"

#: pages/6_cost_calculator.py:49
msgid "Choose the certifications you want to include in the cost calculation"
msgstr "选择您想要包含在成本计算中的认证"

#: pages/6_cost_calculator.py:54
msgid "Study Materials Cost ($)"
msgstr "学习材料成本 ($)"

#: pages/6_cost_calculator.py:58
msgid "Estimated cost of books, online courses, practice exams, etc."
msgstr "书籍、在线课程、模拟考试等的预估成本"

#: pages/6_cost_calculator.py:67
msgid "Select Currency"
msgstr "选择货币"

#: pages/6_cost_calculator.py:69
msgid "Select your preferred currency"
msgstr "选择您的首选货币"

#: pages/6_cost_calculator.py:73
msgid "📚 Exam Retake Analysis"
msgstr "📚 考试重考分析"

#: pages/6_cost_calculator.py:77
msgid "Minimum Days Between Attempts"
msgstr "考试间隔最少天数"

#: pages/6_cost_calculator.py:81
msgid "Typical waiting period required between exam attempts"
msgstr "考试重考之间的典型等待期"

#: pages/6_cost_calculator.py:84
msgid "Retake Discount (%)"
msgstr "重考折扣 (%)"

#: pages/6_cost_calculator.py:88
msgid "Some certifications offer discounts on retakes"
msgstr "某些认证提供重考折扣"

#: pages/6_cost_calculator.py:91
msgid "Maximum Attempts Considered"
msgstr "考虑的最大尝试次数"

#: pages/6_cost_calculator.py:95
msgid "Number of potential attempts to include in cost analysis"
msgstr "成本分析中包含的潜在尝试次数"

#: pages/6_cost_calculator.py:127
msgid "Cost Breakdown"
msgstr "成本明细"

#: pages/6_cost_calculator.py:133 pages/6_cost_calculator.py:161
msgid "Base Exam Fees"
msgstr "基础考试费用"

#: pages/6_cost_calculator.py:138 pages/6_cost_calculator.py:162
msgid "Study Materials"
msgstr "学习材料"

#: pages/6_cost_calculator.py:143
msgid "Potential Retake Costs"
msgstr "潜在重考成本"

#: pages/6_cost_calculator.py:148
msgid "Total Investment"
msgstr "总投资"

#: pages/6_cost_calculator.py:150
msgid "Total cost including all certifications, materials, and potential retakes"
msgstr "包括所有认证、材料和潜在重考的总成本"

#: pages/6_cost_calculator.py:156
msgid "### 📊 Cost Distribution"
msgstr "### 📊 成本分布"

#: pages/6_cost_calculator.py:163
msgid "Potential Retakes"
msgstr "潜在重考"

#: pages/6_cost_calculator.py:174
msgid "### 📋 Selected Certifications"
msgstr "### 📋 选定的认证"

#: pages/6_cost_calculator.py:191
msgid "Retake Cost Analysis"
msgstr "重考成本分析"

#: pages/6_cost_calculator.py:199
msgid "Select certifications to see cost breakdown"
msgstr "选择认证以查看成本明细"

#: pages/7_ai_career_path.py:234 pages/7_ai_career_path.py:240
#: pages/7_ai_career_path.py:262 pages/7_ai_career_path.py:355
#: pages/7_ai_career_path.py:503 pages/7_ai_career_path.py:507
msgid "Service offline, please try again later"
msgstr "服务离线，请稍后重试"

#: pages/7_ai_career_path.py:348
msgid "An unexpected error occurred"
msgstr "发生了意外错误"

#: pages/7_ai_career_path.py:351 pages/7_ai_career_path.py:383
msgid "error"
msgstr "错误"

#: pages/7_ai_career_path.py:367
msgid "Please fill in all required fields"
msgstr "请填写所有必填字段"

#: pages/7_ai_career_path.py:460 pages/7_ai_career_path.py:464
msgid "Unable to generate PDF report"
msgstr "无法生成PDF报告"

#: pages/7_ai_career_path.py:468
msgid "Unable to save your progress"
msgstr "无法保存您的进度"

#: pages/7_ai_career_path.py:479
msgid "Unable to display visualization"
msgstr "无法显示可视化"

#: pages/7_certrat_career_path.py:9
msgid "Welcome to CertRat CareerPath - your AI-powered certification advisor."
msgstr "欢迎使用CertRat CareerPath - 您的AI驱动认证顾问。"

#: pages/7_certrat_career_path.py:10
msgid "Get personalized certification recommendations based on your experience, interests, and career goals."
msgstr "根据您的经验、兴趣和职业目标获得个性化认证推荐。"

#: utils/pdf_report.py:179
msgid "Certification Cost Breakdown"
msgstr "认证成本明细"

#: utils/pdf_report.py:231
msgid "Total Investment Summary"
msgstr "总投资摘要"