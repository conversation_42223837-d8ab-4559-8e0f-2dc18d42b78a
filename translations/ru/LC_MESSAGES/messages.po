#: Translations template for PROJECT.
#: Copyright (C) 2025 CertPathFinder Team
#: This file is distributed under the same license as the PROJECT project.
#: FIRST AUTHOR <<EMAIL>>, 2025.
#:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: CertPathFinder 1.0\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-24 18:14+0000\n"
"PO-Revision-Date: 2025-06-07 14:05+0000\n"
"Last-Translator: FULL NAME <<EMAIL>>\n"
"Language-Team: Ru Team\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: main.py:100
msgid "Login with:"
msgstr "Войти через:"

#: main.py:113 main.py:137
msgid "Select Language"
msgstr "Выбрать язык"

#: main.py:127 main.py:146
msgid "Visualization"
msgstr "Визуализация"

#: main.py:127
msgid "Listings"
msgstr "Списки"

#: main.py:127 main.py:152
msgid "Study Time"
msgstr "Время обучения"

#: main.py:127 main.py:154
msgid "Cost Calculator"
msgstr "Калькулятор стоимости"

#: main.py:128 main.py:156
msgid "CertRat CareerPath"
msgstr "Карьерный путь CertRat"

#: main.py:128 main.py:148
msgid "Admin"
msgstr "Администратор"

#: main.py:128 main.py:150
msgid "FAQ"
msgstr "Часто задаваемые вопросы"

#: pages/1_visualization.py:22
msgid "Certification Path Visualization"
msgstr "Визуализация путей сертификации"

#: pages/1_visualization.py:23
msgid ""
"\n"
"    Explore certification paths and relationships through an interactive "
"visualization.\n"
"    The graph shows certification progression paths and domain "
"relationships.\n"
"    "
msgstr ""

#: pages/1_visualization.py:29
msgid "Visualization Filters"
msgstr "Фильтры визуализации"

#: pages/1_visualization.py:43
msgid "Focus Domain"
msgstr "Основная область"

#: pages/1_visualization.py:83
msgid "Select a domain to visualize its certification paths."
msgstr "Выберите область для визуализации её путей сертификации."

#: pages/6_cost_calculator.py:26
msgid ""
"\n"
"    Calculate the total investment needed for your certification journey,"
" including:\n"
"    - Exam fees and study materials\n"
"    - Potential retake costs\n"
"    - Currency conversion\n"
"    - Regional price variations\n"
"    "
msgstr ""

#: pages/6_cost_calculator.py:47
msgid "Select Certifications"
msgstr "Выбрать сертификации"

#: pages/6_cost_calculator.py:49
msgid "Choose the certifications you want to include in the cost calculation"
msgstr "Выберите сертификации для включения в расчёт стоимости"

#: pages/6_cost_calculator.py:54
msgid "Study Materials Cost ($)"
msgstr "Стоимость учебных материалов ($)"

#: pages/6_cost_calculator.py:58
msgid "Estimated cost of books, online courses, practice exams, etc."
msgstr "Предполагаемая стоимость книг, онлайн-курсов, пробных экзаменов и т.д."

#: pages/6_cost_calculator.py:67
msgid "Select Currency"
msgstr "Выбрать валюту"

#: pages/6_cost_calculator.py:69
msgid "Select your preferred currency"
msgstr "Выберите предпочитаемую валюту"

#: pages/6_cost_calculator.py:73
msgid "📚 Exam Retake Analysis"
msgstr "📚 Анализ пересдачи экзамена"

#: pages/6_cost_calculator.py:77
msgid "Minimum Days Between Attempts"
msgstr ""

#: pages/6_cost_calculator.py:81
msgid "Typical waiting period required between exam attempts"
msgstr ""

#: pages/6_cost_calculator.py:84
msgid "Retake Discount (%)"
msgstr ""

#: pages/6_cost_calculator.py:88
msgid "Some certifications offer discounts on retakes"
msgstr ""

#: pages/6_cost_calculator.py:91
msgid "Maximum Attempts Considered"
msgstr ""

#: pages/6_cost_calculator.py:95
msgid "Number of potential attempts to include in cost analysis"
msgstr ""

#: pages/6_cost_calculator.py:127
msgid "Cost Breakdown"
msgstr ""

#: pages/6_cost_calculator.py:133 pages/6_cost_calculator.py:161
msgid "Base Exam Fees"
msgstr ""

#: pages/6_cost_calculator.py:138 pages/6_cost_calculator.py:162
msgid "Study Materials"
msgstr ""

#: pages/6_cost_calculator.py:143
msgid "Potential Retake Costs"
msgstr ""

#: pages/6_cost_calculator.py:148
msgid "Total Investment"
msgstr ""

#: pages/6_cost_calculator.py:150
msgid "Total cost including all certifications, materials, and potential retakes"
msgstr ""

#: pages/6_cost_calculator.py:156
msgid "### 📊 Cost Distribution"
msgstr ""

#: pages/6_cost_calculator.py:163
msgid "Potential Retakes"
msgstr ""

#: pages/6_cost_calculator.py:174
msgid "### 📋 Selected Certifications"
msgstr ""

#: pages/6_cost_calculator.py:191
msgid "Retake Cost Analysis"
msgstr ""

#: pages/6_cost_calculator.py:199
msgid "Select certifications to see cost breakdown"
msgstr ""

#: pages/7_ai_career_path.py:234 pages/7_ai_career_path.py:240
#: pages/7_ai_career_path.py:262 pages/7_ai_career_path.py:355
#: pages/7_ai_career_path.py:503 pages/7_ai_career_path.py:507
msgid "Service offline, please try again later"
msgstr "Сервис недоступен, попробуйте позже"

#: pages/7_ai_career_path.py:348
msgid "An unexpected error occurred"
msgstr "Произошла неожиданная ошибка"

#: pages/7_ai_career_path.py:351 pages/7_ai_career_path.py:383
msgid "error"
msgstr "ошибка"

#: pages/7_ai_career_path.py:367
msgid "Please fill in all required fields"
msgstr "Пожалуйста, заполните все обязательные поля"

#: pages/7_ai_career_path.py:460 pages/7_ai_career_path.py:464
msgid "Unable to generate PDF report"
msgstr ""

#: pages/7_ai_career_path.py:468
msgid "Unable to save your progress"
msgstr ""

#: pages/7_ai_career_path.py:479
msgid "Unable to display visualization"
msgstr ""

#: pages/7_certrat_career_path.py:9
msgid "Welcome to CertRat CareerPath - your AI-powered certification advisor."
msgstr "Добро пожаловать в CertRat CareerPath - ваш консультант по сертификации на основе ИИ."

#: pages/7_certrat_career_path.py:10
msgid "Get personalized certification recommendations based on your experience, interests, and career goals."
msgstr "Получите персональные рекомендации по сертификации на основе вашего опыта, интересов и карьерных целей."

#: utils/pdf_report.py:179
msgid "Certification Cost Breakdown"
msgstr ""

#: utils/pdf_report.py:231
msgid "Total Investment Summary"
msgstr ""