# CertPathFinder API Specification

## 📋 API Overview

**Base URL**: `http://localhost:8000/api/v1`  
**Authentication**: JWT <PERSON> (planned)  
**Content-Type**: `application/json`  
**API Version**: v1  

## 🔐 Authentication

### Endpoints
```
POST /api/v1/auth/login     # User login
POST /api/v1/auth/register  # User registration
POST /api/v1/auth/refresh   # Refresh token
POST /api/v1/auth/logout    # User logout
```

### Request/Response Examples
```json
// Login Request
{
  "email": "<EMAIL>",
  "password": "securepassword"
}

// Login Response
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "name": "John Doe"
    }
  }
}
```

## 🎓 Certification Management

### Core Endpoints
```
GET    /api/v1/certifications              # List certifications
GET    /api/v1/certifications/{id}         # Get certification by ID
POST   /api/v1/certifications              # Create certification (admin)
PUT    /api/v1/certifications/{id}         # Update certification (admin)
DELETE /api/v1/certifications/{id}         # Delete certification (admin)
GET    /api/v1/certifications/search       # Search certifications
```

### Query Parameters
```
# GET /api/v1/certifications
?domain=Security                    # Filter by domain
?level=Entry                       # Filter by level (Entry/Mid/Senior/Expert)
?focus=Network                     # Filter by focus area
?difficulty=1-4                    # Filter by difficulty rating
?cost_min=0&cost_max=1000         # Filter by cost range
?organization=CompTIA              # Filter by organization
?page=1&limit=20                  # Pagination
?sort_by=name&sort_order=asc      # Sorting
```

### Response Schema
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "CompTIA Security+",
        "category": "Entry-Level Security Certifications",
        "domain": "Security",
        "level": "Entry",
        "focus": "General Security",
        "difficulty": 2,
        "cost": 349.00,
        "currency": "USD",
        "description": "Entry-level security certification...",
        "prerequisites": "Basic networking knowledge",
        "validity_period": 36,
        "exam_code": "SY0-601",
        "organization": {
          "id": 1,
          "name": "CompTIA",
          "website": "https://comptia.org"
        },
        "url": "https://comptia.org/certifications/security",
        "study_hours": 120,
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z",
        "last_updated": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 465,
    "page": 1,
    "limit": 20,
    "pages": 24,
    "has_next": true,
    "has_prev": false
  }
}
```

## 👤 User Profile Management

### Endpoints
```
GET    /api/v1/user/profile                # Get user profile
POST   /api/v1/user/profile                # Create user profile
PUT    /api/v1/user/profile                # Update user profile
DELETE /api/v1/user/profile                # Delete user profile
POST   /api/v1/user/profile/sync           # Sync profile data
```

### Profile Schema
```json
{
  "user_id": "user123",
  "years_experience": 5,
  "user_role": "Security Analyst",
  "desired_role": "Senior Security Engineer",
  "expertise_areas": ["Network Security", "Cloud Security", "Incident Response"],
  "preferred_learning_style": "Hands-on",
  "study_time_available": 10,
  "tutorial_completed": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

## 💰 Cost Calculator

### Endpoints
```
POST   /api/v1/cost-calculator/calculate   # Calculate certification costs
GET    /api/v1/cost-calculator/scenarios   # Get cost scenarios
POST   /api/v1/cost-calculator/scenarios   # Create cost scenario
GET    /api/v1/cost-calculator/history     # Get calculation history
POST   /api/v1/cost-calculator/compare     # Compare multiple certifications
GET    /api/v1/cost-calculator/currencies  # Get supported currencies
```

### Calculate Request/Response
```json
// Calculate Request
{
  "certification_ids": [1, 2, 3],
  "scenario_id": 1,
  "location": "United States",
  "currency": "USD",
  "include_training": true,
  "include_materials": true,
  "include_retakes": false,
  "bulk_discount": 0.1
}

// Calculate Response
{
  "success": true,
  "data": {
    "calculation_id": "calc_123",
    "total_cost": 1247.50,
    "currency": "USD",
    "breakdown": {
      "exam_costs": 897.00,
      "training_costs": 300.00,
      "materials_costs": 50.50,
      "retake_costs": 0.00
    },
    "certifications": [
      {
        "certification_id": 1,
        "name": "CompTIA Security+",
        "base_cost": 349.00,
        "training_cost": 100.00,
        "materials_cost": 25.00,
        "total_cost": 474.00
      }
    ],
    "scenario": {
      "id": 1,
      "name": "Self-Study with Materials",
      "description": "Independent study with official materials"
    },
    "calculation_date": "2024-01-15T10:30:00Z"
  }
}
```

## 🚀 Career Transition

### Endpoints
```
GET    /api/v1/career-transition/roles     # Get career roles
GET    /api/v1/career-transition/paths     # Find transition paths
POST   /api/v1/career-transition/plan      # Create transition plan
GET    /api/v1/career-transition/plan/{id} # Get transition plan
PUT    /api/v1/career-transition/plan/{id} # Update transition plan
POST   /api/v1/career-transition/analyze   # Analyze career options
```

### Find Paths Request/Response
```json
// Find Paths Request
{
  "current_role_id": 1,
  "target_role_id": 5,
  "constraints": {
    "max_budget": 5000.00,
    "currency": "USD",
    "max_timeline_months": 24,
    "max_difficulty": "Hard",
    "preferred_learning_style": "Mixed",
    "study_hours_per_week": 10
  }
}

// Find Paths Response
{
  "success": true,
  "data": {
    "paths": [
      {
        "path_id": "path_123",
        "name": "Security Analyst to Senior Security Engineer",
        "description": "Direct progression path with cloud focus",
        "difficulty_level": "Medium",
        "total_duration_months": 18,
        "total_cost": 3500.00,
        "success_rate": 0.85,
        "required_certifications": [
          {
            "certification_id": 15,
            "name": "AWS Certified Security - Specialty",
            "order": 1,
            "estimated_study_months": 6
          },
          {
            "certification_id": 22,
            "name": "CISSP",
            "order": 2,
            "estimated_study_months": 12
          }
        ],
        "milestones": [
          {
            "month": 6,
            "description": "Complete AWS Security certification",
            "cost": 300.00
          },
          {
            "month": 18,
            "description": "Complete CISSP certification",
            "cost": 749.00
          }
        ]
      }
    ],
    "analysis": {
      "total_paths_found": 5,
      "recommended_path_id": "path_123",
      "budget_utilization": 0.70,
      "timeline_efficiency": 0.75
    }
  }
}
```

## 📊 Progress Tracking

### Endpoints
```
GET    /api/v1/progress/sessions           # Get study sessions
POST   /api/v1/progress/sessions           # Log study session
GET    /api/v1/progress/goals              # Get learning goals
POST   /api/v1/progress/goals              # Create learning goal
PUT    /api/v1/progress/goals/{id}         # Update learning goal
GET    /api/v1/progress/analytics          # Get progress analytics
GET    /api/v1/progress/achievements       # Get achievements
```

### Study Session Schema
```json
{
  "session_id": "session_123",
  "user_id": "user123",
  "certification_id": 1,
  "session_date": "2024-01-15T10:30:00Z",
  "duration_minutes": 120,
  "topics_covered": ["Network Security", "Cryptography"],
  "progress_percentage": 0.65,
  "session_type": "Study",
  "notes": "Focused on symmetric vs asymmetric encryption",
  "effectiveness_rating": 4
}
```

## 🤖 AI Study Assistant

### Endpoints
```
POST   /api/v1/ai-assistant/recommendations # Get study recommendations
POST   /api/v1/ai-assistant/adaptive-path   # Generate adaptive learning path
POST   /api/v1/ai-assistant/questions       # Generate practice questions
POST   /api/v1/ai-assistant/feedback        # Submit study feedback
GET    /api/v1/ai-assistant/insights        # Get learning insights
POST   /api/v1/ai-assistant/chat           # Chat with AI assistant
```

### Recommendations Request/Response
```json
// Recommendations Request
{
  "user_id": "user123",
  "certification_id": 1,
  "context": "struggling_with_cryptography",
  "current_progress": 0.45,
  "study_time_available": 2
}

// Recommendations Response
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "type": "study_technique",
        "priority": "high",
        "title": "Focus on Practical Cryptography",
        "description": "Based on your progress, spend more time on hands-on crypto labs",
        "estimated_time_hours": 4,
        "resources": [
          {
            "type": "video",
            "title": "Cryptography Fundamentals",
            "url": "https://example.com/crypto-video",
            "duration_minutes": 45
          }
        ]
      }
    ],
    "adaptive_schedule": {
      "next_7_days": [
        {
          "day": 1,
          "topics": ["Symmetric Encryption"],
          "duration_minutes": 60,
          "activities": ["Reading", "Practice Labs"]
        }
      ]
    },
    "confidence_score": 0.78,
    "generated_at": "2024-01-15T10:30:00Z"
  }
}
```

## 🏢 Enterprise Features

### Endpoints
```
GET    /api/v1/enterprise/organizations    # Get organizations
POST   /api/v1/enterprise/organizations    # Create organization
GET    /api/v1/enterprise/teams            # Get teams
POST   /api/v1/enterprise/teams            # Create team
GET    /api/v1/enterprise/analytics        # Get enterprise analytics
GET    /api/v1/enterprise/reports          # Generate reports
POST   /api/v1/enterprise/bulk-import      # Bulk import users
```

### Organization Schema
```json
{
  "organization_id": "org_123",
  "name": "Acme Security Corp",
  "industry": "Financial Services",
  "size": "1000-5000",
  "subscription_tier": "Enterprise",
  "settings": {
    "require_manager_approval": true,
    "budget_tracking_enabled": true,
    "compliance_reporting": true
  },
  "teams": [
    {
      "team_id": "team_456",
      "name": "SOC Team",
      "manager_id": "user_789",
      "member_count": 15,
      "budget_allocated": 50000.00
    }
  ],
  "analytics": {
    "total_users": 150,
    "active_learners": 89,
    "certifications_in_progress": 45,
    "certifications_completed": 23,
    "total_budget_spent": 125000.00
  }
}
```

## 🔍 Search & Filtering

### Advanced Search
```
GET /api/v1/search?q=security&type=certification
GET /api/v1/search/suggestions?q=comp
```

### Filter Options
```json
{
  "domains": ["Security", "Cloud", "Network", "Governance"],
  "levels": ["Entry", "Mid", "Senior", "Expert"],
  "organizations": ["CompTIA", "ISC2", "EC-Council", "SANS"],
  "cost_ranges": [
    {"min": 0, "max": 500, "label": "Under $500"},
    {"min": 500, "max": 1000, "label": "$500 - $1000"},
    {"min": 1000, "max": 2000, "label": "$1000 - $2000"},
    {"min": 2000, "max": null, "label": "Over $2000"}
  ],
  "difficulty_levels": [1, 2, 3, 4]
}
```

## 📈 Analytics & Reporting

### Endpoints
```
GET    /api/v1/analytics/dashboard         # Dashboard metrics
GET    /api/v1/analytics/trends            # Certification trends
GET    /api/v1/analytics/user-progress     # User progress analytics
GET    /api/v1/analytics/cost-analysis     # Cost analysis reports
POST   /api/v1/reports/generate            # Generate custom reports
GET    /api/v1/reports/{id}/download       # Download report
```

## ⚡ Performance & Caching

### Response Times
- **List endpoints**: < 200ms
- **Detail endpoints**: < 100ms
- **Search endpoints**: < 300ms
- **Complex calculations**: < 1000ms

### Caching Strategy
- **Static data**: 1 hour cache
- **User data**: 5 minutes cache
- **Search results**: 15 minutes cache
- **Analytics**: 30 minutes cache

## 🚨 Error Handling

### Standard Error Codes
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "request_id": "req_123456"
}
```

### Common Error Codes
- `VALIDATION_ERROR` (400): Invalid input data
- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `RATE_LIMITED` (429): Too many requests
- `INTERNAL_ERROR` (500): Server error

## 🔒 Security Headers

All API responses include security headers:
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
```

## 📊 Rate Limiting

- **Default**: 60 requests per minute per IP
- **Authenticated users**: 120 requests per minute
- **Enterprise users**: 300 requests per minute
- **Bulk operations**: 10 requests per minute

Rate limit headers:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642248000
```

This API specification provides comprehensive documentation for all CertPathFinder endpoints, including request/response schemas, authentication, error handling, and performance considerations.
