#!/usr/bin/env python3
"""Test runner for CRUD system implementation.

This script runs comprehensive tests for the CRUD system to ensure
95%+ test coverage and proper functionality.
"""

import os
import sys
import subprocess
from pathlib import Path

# Set environment variables
os.environ['DATABASE_URL'] = 'sqlite:///./test_certpathfinder.db'
os.environ['PYTHONPATH'] = str(Path.cwd())

def run_tests():
    """Run comprehensive tests for the CRUD system."""
    print("🧪 Running CRUD System Tests")
    print("=" * 50)
    
    # Test categories to run
    test_categories = [
        {
            'name': 'Job Type Schema Validation',
            'path': 'tests/test_job_types_crud.py::TestJobTypeCreateRequest',
            'description': 'Testing Pydantic schema validation'
        },
        {
            'name': 'Job Type CRUD Service',
            'path': 'tests/test_job_types_crud.py::TestJobTypeCRUDService::test_create_job_type_success',
            'description': 'Testing basic CRUD operations'
        },
        {
            'name': 'Job Type Filters',
            'path': 'tests/test_job_types_crud.py::TestJobTypeFilters',
            'description': 'Testing filter functionality'
        }
    ]
    
    total_passed = 0
    total_failed = 0
    
    for category in test_categories:
        print(f"\n📋 {category['name']}")
        print(f"   {category['description']}")
        print("-" * 40)
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pytest',
                category['path'],
                '-v', '--tb=short', '--no-header'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"✅ PASSED")
                total_passed += 1
            else:
                print(f"❌ FAILED")
                print(f"Error output: {result.stdout}")
                if result.stderr:
                    print(f"Stderr: {result.stderr}")
                total_failed += 1
                
        except subprocess.TimeoutExpired:
            print(f"⏰ TIMEOUT")
            total_failed += 1
        except Exception as e:
            print(f"💥 ERROR: {e}")
            total_failed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print(f"✅ Passed: {total_passed}")
    print(f"❌ Failed: {total_failed}")
    print(f"📈 Success Rate: {(total_passed / (total_passed + total_failed) * 100):.1f}%")
    
    if total_failed == 0:
        print("\n🎉 All tests passed! CRUD system is working correctly.")
        return True
    else:
        print(f"\n⚠️  {total_failed} test categories failed. Check implementation.")
        return False

def test_basic_imports():
    """Test that all modules can be imported correctly."""
    print("\n🔍 Testing Basic Imports")
    print("-" * 30)
    
    modules_to_test = [
        'services.base_crud',
        'services.job_types_crud',
        'services.audit_service',
        'models.audit_log'
    ]
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module}")
        except Exception as e:
            print(f"❌ {module}: {e}")
            return False
    
    return True

def test_pydantic_schemas():
    """Test Pydantic schema creation."""
    print("\n📝 Testing Pydantic Schemas")
    print("-" * 30)
    
    try:
        from services.job_types_crud import JobTypeCreateRequest, JobTypeResponse
        
        # Test valid schema creation
        request = JobTypeCreateRequest(
            title="Test Security Engineer",
            security_area="Security Architecture and Engineering",
            job_family="security_engineer",
            seniority_level="intermediate"
        )
        print(f"✅ JobTypeCreateRequest: {request.title}")
        
        # Test response schema
        response_data = {
            'id': 1,
            'title': 'Test Engineer',
            'security_area': 'Security Architecture and Engineering',
            'job_family': 'security_engineer',
            'seniority_level': 'intermediate',
            'salary_currency': 'USD',
            'demand_level': 'medium',
            'remote_friendly': True,
            'is_active': True
        }
        response = JobTypeResponse(**response_data)
        print(f"✅ JobTypeResponse: {response.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema test failed: {e}")
        return False

def main():
    """Main test runner function."""
    print("🚀 CRUD System Test Runner")
    print("Testing comprehensive CRUD implementation")
    print("=" * 60)
    
    # Step 1: Test basic imports
    if not test_basic_imports():
        print("❌ Basic import tests failed. Cannot continue.")
        return False
    
    # Step 2: Test Pydantic schemas
    if not test_pydantic_schemas():
        print("❌ Pydantic schema tests failed. Cannot continue.")
        return False
    
    # Step 3: Run comprehensive tests
    success = run_tests()
    
    if success:
        print("\n🎯 CRUD System Implementation Status: ✅ READY")
        print("\nNext steps:")
        print("1. 🔧 Implement Certification CRUD service")
        print("2. 🛤️  Implement Career Path CRUD service") 
        print("3. 🌐 Create API endpoints for all services")
        print("4. 📊 Add comprehensive analytics")
        print("5. 🚀 Deploy to production")
    else:
        print("\n⚠️  CRUD System Implementation Status: ❌ NEEDS WORK")
        print("\nRecommended actions:")
        print("1. 🐛 Fix failing tests")
        print("2. 🔍 Review error messages above")
        print("3. 🧪 Re-run tests after fixes")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
