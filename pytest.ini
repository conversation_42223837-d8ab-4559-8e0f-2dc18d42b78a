[tool:pytest]
# Comprehensive pytest configuration for enhanced testing

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Enhanced options for comprehensive testing
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --showlocals
    --durations=10
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=json:coverage.json
    --cov-fail-under=75
    --junitxml=test-results.xml

# Python path
pythonpath = .

# Markers for comprehensive test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    e2e: End-to-end tests for complete workflows
    security: Security and vulnerability tests
    performance: Performance and load tests
    slow: Tests that take a long time to run
    api: API endpoint tests
    database: Database-related tests
    auth: Authentication and authorization tests
    cost_calculator: Cost calculator specific tests
    ai_assistant: AI assistant specific tests
    mobile: Mobile API tests
    enterprise: Enterprise features tests
    admin: Admin functionality tests
    regression: Regression tests for bug fixes
    smoke: Smoke tests for basic functionality
    critical: Critical path tests that must pass
    flaky: Tests that may be unstable
    external: Tests that require external services
    mock: Tests using mocked dependencies

# Test timeout (in seconds)
timeout = 300

# Filtering options
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning
    error::sqlalchemy.exc.SAWarning

# Log settings for better debugging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = tests.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d %(funcName)s(): %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# Asyncio settings
asyncio_mode = auto

# Cache settings
cache_dir = .pytest_cache

# Collection settings
collect_ignore =
    setup.py
    build
    dist
    .git
    .tox
    venv
    env

# Doctest settings
doctest_optionflags = NORMALIZE_WHITESPACE IGNORE_EXCEPTION_DETAIL ELLIPSIS