# CertRats DevContainer

This devcontainer provides a complete, reproducible development environment for the CertRats project.

## 🚀 Quick Start

1. **Open in VS Code**: Open this project in VS Code with the Dev Containers extension installed
2. **Reopen in Container**: When prompted, click "Reopen in Container" or use `Ctrl+Shift+P` → "Dev Containers: Reopen in Container"
3. **Wait for Setup**: The container will build and run the setup script automatically
4. **Start Developing**: Use the provided commands to start services and run tests

## 📦 What's Included

### Development Tools
- **Nix Package Manager** for reproducible dependency management
- **Python 3.11** with all project dependencies via Nix
- **Node.js 20** with npm, yarn, and pnpm via Nix
- **Docker-in-Docker** for container management
- **Git** with GitHub CLI
- **VS Code Extensions** for Python, TypeScript, Docker, and more
- **Direnv** for automatic environment loading

### Services
- **PostgreSQL 15** - Main database
- **Redis 7** - Caching and session storage
- **MinIO** - S3-compatible object storage
- **MailHog** - Email testing

### Testing & Quality Tools
- **Pytest** with coverage and parallel execution
- **Playwright** for E2E testing
- **Black, Flake8, MyPy** for Python code quality
- **ESLint, Prettier** for JavaScript/TypeScript
- **Pre-commit hooks** for automated quality checks

## 🛠️ Development Commands

The devcontainer includes convenient aliases and functions:

### Quick Commands
```bash
# Core development commands (Nix-aware)
start-api          # Start FastAPI backend
start-frontend     # Start Next.js frontend
run-tests          # Run all tests
test-api           # Run API tests only
test-frontend      # Run frontend tests
format-code        # Format all code
lint-code          # Lint all code
db-migrate         # Run database migrations
db-reset           # Reset database

# Nix-specific commands
nix-update         # Update Nix flake dependencies
nix-check          # Check Nix flake validity
nix-shell          # Enter Nix development shell

# Help
dev-help           # Show all commands
```

### Manual Commands
```bash
# Backend Development
cd /workspace
python run_api.py                    # Start API server
python -m pytest tests/ -v          # Run tests
black .                              # Format Python code
flake8 .                             # Lint Python code

# Frontend Development
cd /workspace/frontend-next
npm run dev                          # Start development server
npm test                             # Run tests
npm run lint                         # Lint code
npm run format                       # Format code

# Database Operations
alembic upgrade head                 # Apply migrations
alembic downgrade base               # Reset database
python data/seed.py                  # Seed with test data
```

## 🌐 Service URLs

When the devcontainer is running, these services are available:

- **Frontend**: http://localhost:3000
- **API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **MinIO Console**: http://localhost:9001
- **MailHog**: http://localhost:8025

## 📦 Nix Integration

### Why Nix?
The devcontainer uses Nix for dependency management because it provides:
- **Reproducible builds** - Exact same environment for all developers
- **Declarative configuration** - Dependencies defined in code
- **Isolation** - No conflicts between projects
- **Rollback capability** - Easy to revert changes
- **Cross-platform** - Works on Linux, macOS, and Windows (WSL)

### Nix Files
- **`flake.nix`** - Modern Nix configuration with pinned dependencies
- **`shell.nix`** - Legacy Nix shell for compatibility
- **`.envrc`** - Direnv configuration for automatic environment loading

### Using Nix
```bash
# Enter development environment
nix develop .devcontainer

# Update dependencies
nix flake update

# Check configuration
nix flake check

# Use with direnv (automatic)
cd /workspace  # Environment loads automatically
```

## 🔧 Configuration

### Environment Variables
The devcontainer automatically creates a `.env` file with development settings:
- Database connection to containerized PostgreSQL
- Redis connection to containerized Redis
- Debug mode enabled
- Development CORS settings

### VS Code Settings
Preconfigured settings include:
- Python interpreter and linting
- Code formatting on save
- Test discovery and execution
- File exclusions for build artifacts
- Debugging configurations

## 📁 Project Structure

```
/workspace/
├── .devcontainer/          # DevContainer configuration
├── api/                    # FastAPI backend
├── frontend-next/          # Next.js frontend
├── tests/                  # Test suites
├── data/                   # Database seeds and migrations
├── docs/                   # Documentation
├── scripts/                # Development scripts
└── docker/                 # Docker configurations
```

## 🐛 Debugging

### Python Debugging
1. Set breakpoints in VS Code
2. Use the "FastAPI Debug" launch configuration
3. Or use `ipdb.set_trace()` in your code

### Frontend Debugging
1. Use browser developer tools
2. VS Code debugger for Node.js
3. React Developer Tools extension

### Database Debugging
1. Use the PostgreSQL extension in VS Code
2. Connect to: `postgresql://certrats_dev:certrats_dev_password@localhost:5432/certrats_dev`
3. Or use `psql` in the terminal

## 🧪 Testing

### Running Tests
```bash
# All tests
run-tests

# Specific test suites
test-api                    # Backend tests
test-frontend              # Frontend tests
python -m pytest tests/test_api/test_certifications.py  # Specific file

# With coverage
python -m pytest --cov=api tests/

# Parallel execution
python -m pytest -n auto tests/
```

### E2E Testing with Playwright
```bash
cd frontend-next
npx playwright test
npx playwright test --ui    # Interactive mode
npx playwright show-report # View results
```

## 🔄 Database Management

### Migrations
```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback
alembic downgrade -1
```

### Seeding Data
```bash
# Load test data
python data/seed.py

# Load specific datasets
python data/load_certifications.py
python data/update_validity_periods.py
```

## 🚨 Troubleshooting

### Container Issues
- **Slow startup**: First build takes time to download images and install dependencies
- **Port conflicts**: Ensure ports 3000, 8000, 5432, 6379 are available
- **Permission issues**: The container runs as `vscode` user with proper permissions

### Service Issues
- **Database connection**: Wait for PostgreSQL to be ready (check with `pg_isready`)
- **Redis connection**: Check with `redis-cli ping`
- **API not starting**: Check logs for dependency or configuration issues

### Development Issues
- **Import errors**: Ensure `PYTHONPATH=/workspace` is set
- **Node modules**: Run `npm ci` in frontend-next directory
- **Pre-commit hooks**: Run `pre-commit install` if hooks aren't working

## 📚 Additional Resources

- [VS Code Dev Containers Documentation](https://code.visualstudio.com/docs/remote/containers)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Playwright Documentation](https://playwright.dev/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 🤝 Contributing

1. Make changes in the devcontainer
2. Run tests: `run-tests`
3. Format code: `format-code`
4. Lint code: `lint-code`
5. Commit with pre-commit hooks
6. Push and create PR

The devcontainer ensures all contributors have the same development environment and tools.
