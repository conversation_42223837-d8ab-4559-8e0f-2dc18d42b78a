#!/bin/bash

# CertRats DevContainer Validation Script
# This script validates that the devcontainer setup is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Validation functions
validate_nix() {
    print_header "Nix Environment"

    if command -v nix &> /dev/null; then
        NIX_VERSION=$(nix --version)
        print_success "Nix installed: $NIX_VERSION"

        # Check if flakes are enabled
        if nix eval --expr '1 + 1' &> /dev/null; then
            print_success "Nix flakes enabled"
        else
            print_warning "Nix flakes not enabled"
        fi

        # Check if our flake is valid
        if [ -f ".devcontainer/flake.nix" ]; then
            if nix flake check .devcontainer &> /dev/null; then
                print_success "CertRats flake is valid"
            else
                print_warning "CertRats flake has issues"
            fi
        else
            print_warning "CertRats flake.nix not found"
        fi
    else
        print_warning "Nix not found (falling back to traditional tools)"
        return 1
    fi

    # Check direnv
    if command -v direnv &> /dev/null; then
        print_success "Direnv available for automatic environment loading"
    else
        print_warning "Direnv not found"
    fi
}

validate_python() {
    print_header "Python Environment"

    if command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version)
        print_success "Python installed: $PYTHON_VERSION"
    else
        print_error "Python not found"
        return 1
    fi

    if command -v pip &> /dev/null; then
        PIP_VERSION=$(pip --version)
        print_success "Pip installed: $PIP_VERSION"
    else
        print_error "Pip not found"
        return 1
    fi

    # Check key Python packages (may be provided by Nix)
    local packages=("fastapi" "uvicorn" "sqlalchemy" "pytest" "black" "flake8")
    for package in "${packages[@]}"; do
        if python -c "import $package" &> /dev/null; then
            print_success "$package package available"
        elif command -v nix &> /dev/null; then
            print_info "$package available via Nix environment"
        else
            print_warning "$package package not found"
        fi
    done
}

validate_nodejs() {
    print_header "Node.js Environment"
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js installed: $NODE_VERSION"
    else
        print_error "Node.js not found"
        return 1
    fi
    
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_success "npm installed: $NPM_VERSION"
    else
        print_error "npm not found"
        return 1
    fi
    
    # Check for global packages
    local packages=("yarn" "typescript" "eslint" "prettier")
    for package in "${packages[@]}"; do
        if command -v $package &> /dev/null; then
            print_success "$package available globally"
        else
            print_warning "$package not found globally"
        fi
    done
}

validate_docker() {
    print_header "Docker Environment"
    
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version)
        print_success "Docker installed: $DOCKER_VERSION"
    else
        print_error "Docker not found"
        return 1
    fi
    
    if command -v docker-compose &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version)
        print_success "Docker Compose installed: $COMPOSE_VERSION"
    else
        print_error "Docker Compose not found"
        return 1
    fi
    
    # Test Docker connectivity
    if docker ps &> /dev/null; then
        print_success "Docker daemon accessible"
    else
        print_error "Cannot connect to Docker daemon"
        return 1
    fi
}

validate_database() {
    print_header "Database Connectivity"
    
    # Check PostgreSQL
    if command -v psql &> /dev/null; then
        print_success "PostgreSQL client installed"
        
        if pg_isready -h database -p 5432 -U certrats_dev &> /dev/null; then
            print_success "PostgreSQL server accessible"
        else
            print_warning "PostgreSQL server not accessible (may not be running)"
        fi
    else
        print_error "PostgreSQL client not found"
    fi
    
    # Check Redis
    if command -v redis-cli &> /dev/null; then
        print_success "Redis client installed"
        
        if redis-cli -h redis ping &> /dev/null; then
            print_success "Redis server accessible"
        else
            print_warning "Redis server not accessible (may not be running)"
        fi
    else
        print_error "Redis client not found"
    fi
}

validate_development_tools() {
    print_header "Development Tools"
    
    # Check Git
    if command -v git &> /dev/null; then
        GIT_VERSION=$(git --version)
        print_success "Git installed: $GIT_VERSION"
    else
        print_error "Git not found"
    fi
    
    # Check common development tools
    local tools=("curl" "wget" "jq" "tree" "htop")
    for tool in "${tools[@]}"; do
        if command -v $tool &> /dev/null; then
            print_success "$tool available"
        else
            print_warning "$tool not found"
        fi
    done
    
    # Check VS Code extensions (if running in VS Code)
    if [ -n "$VSCODE_IPC_HOOK_CLI" ]; then
        print_success "Running in VS Code environment"
    else
        print_info "Not running in VS Code (this is normal for command line validation)"
    fi
}

validate_project_structure() {
    print_header "Project Structure"
    
    # Check key directories
    local directories=("api" "frontend-next" "tests" "data" "docs")
    for dir in "${directories[@]}"; do
        if [ -d "/workspace/$dir" ]; then
            print_success "$dir directory exists"
        else
            print_warning "$dir directory not found"
        fi
    done
    
    # Check key files
    local files=("requirements.txt" "frontend-next/package.json" "docker-compose.yml")
    for file in "${files[@]}"; do
        if [ -f "/workspace/$file" ]; then
            print_success "$file exists"
        else
            print_warning "$file not found"
        fi
    done
}

validate_environment_variables() {
    print_header "Environment Variables"
    
    # Check key environment variables
    local env_vars=("PYTHONPATH" "DATABASE_URL" "REDIS_URL" "ENVIRONMENT")
    for var in "${env_vars[@]}"; do
        if [ -n "${!var}" ]; then
            print_success "$var is set"
        else
            print_warning "$var is not set"
        fi
    done
}

validate_ports() {
    print_header "Port Availability"
    
    # Check if key ports are accessible
    local ports=("3000:Frontend" "8000:API" "5432:PostgreSQL" "6379:Redis")
    for port_info in "${ports[@]}"; do
        IFS=':' read -r port service <<< "$port_info"
        if netcat -z localhost $port 2>/dev/null; then
            print_success "$service ($port) is accessible"
        else
            print_info "$service ($port) is not running (this is normal if services aren't started)"
        fi
    done
}

run_quick_tests() {
    print_header "Quick Functionality Tests"
    
    # Test Python import
    if python -c "import sys; print(f'Python path: {sys.path[0]}')" &> /dev/null; then
        print_success "Python imports working"
    else
        print_error "Python import test failed"
    fi
    
    # Test if we can create files
    if touch /tmp/test_file && rm /tmp/test_file; then
        print_success "File system write access working"
    else
        print_error "Cannot write to file system"
    fi
    
    # Test network connectivity
    if curl -s --connect-timeout 5 https://google.com > /dev/null; then
        print_success "External network connectivity working"
    else
        print_warning "External network connectivity issues"
    fi
}

# Main validation function
main() {
    echo -e "${BLUE}"
    echo "🔍 CertRats DevContainer Validation"
    echo "=================================="
    echo -e "${NC}"
    
    local failed=0

    validate_nix
    validate_python || ((failed++))
    validate_nodejs || ((failed++))
    validate_docker || ((failed++))
    validate_database
    validate_development_tools
    validate_project_structure
    validate_environment_variables
    validate_ports
    run_quick_tests
    
    print_header "Validation Summary"
    
    if [ $failed -eq 0 ]; then
        print_success "All critical validations passed! 🎉"
        print_info "Your devcontainer is ready for development."
        print_info "Run 'dev-help' to see available development commands."
    else
        print_warning "$failed critical validation(s) failed."
        print_info "Some issues were found, but you may still be able to develop."
        print_info "Check the errors above and ensure all services are running."
    fi
    
    echo -e "\n${BLUE}Next steps:${NC}"
    echo "1. Start services: docker-compose up -d"
    echo "2. Run API: start-api"
    echo "3. Run frontend: start-frontend"
    echo "4. Run tests: run-tests"
    echo "5. Get help: dev-help"
}

# Run validation
main "$@"
