#!/bin/bash

# CertRats DevContainer Setup Script
# This script runs after the devcontainer is created to set up the development environment

set -e

echo "🚀 Setting up CertRats development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Wait for services to be ready
print_status "Waiting for services to be ready..."

# Wait for PostgreSQL
print_status "Waiting for PostgreSQL..."
until pg_isready -h database -p 5432 -U certrats_dev; do
    sleep 2
done
print_success "PostgreSQL is ready!"

# Wait for Redis
print_status "Waiting for Redis..."
until redis-cli -h redis ping; do
    sleep 2
done
print_success "Redis is ready!"

# Set up Python environment
print_status "Setting up Python environment..."

# Upgrade pip
python -m pip install --upgrade pip

# Install Python dependencies if not already installed
if [ -f "requirements.txt" ]; then
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
fi

if [ -f "tests/requirements-test.txt" ]; then
    print_status "Installing test dependencies..."
    pip install -r tests/requirements-test.txt
fi

# Install development dependencies
print_status "Installing development tools..."
pip install \
    pre-commit \
    black \
    flake8 \
    mypy \
    isort \
    bandit \
    safety \
    pytest-xdist \
    pytest-cov \
    ipython \
    jupyter

# Set up Node.js environment
print_status "Setting up Node.js environment..."

# Install frontend dependencies
if [ -d "frontend-next" ] && [ -f "frontend-next/package.json" ]; then
    print_status "Installing frontend dependencies..."
    cd frontend-next
    npm ci
    cd ..
fi

# Install Playwright browsers if not already installed
print_status "Installing Playwright browsers..."
npx playwright install --with-deps

# Set up database
print_status "Setting up database..."

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    cat > .env << EOF
# Development Environment Variables
DATABASE_URL=*************************************************************/certrats_dev
REDIS_URL=redis://redis:6379/0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
SECRET_KEY=dev-secret-key-change-in-production
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
EOF
fi

# Run database migrations
if [ -f "alembic.ini" ] || [ -f "config/alembic.ini" ]; then
    print_status "Running database migrations..."
    if [ -f "config/alembic.ini" ]; then
        alembic -c config/alembic.ini upgrade head
    else
        alembic upgrade head
    fi
else
    print_warning "No alembic.ini found, skipping migrations"
fi

# Seed database with initial data
if [ -f "data/seed.py" ]; then
    print_status "Seeding database with initial data..."
    python data/seed.py
elif [ -f "load_test_data.py" ]; then
    print_status "Loading test data..."
    python load_test_data.py
fi

# Set up pre-commit hooks
if [ -f ".pre-commit-config.yaml" ]; then
    print_status "Installing pre-commit hooks..."
    pre-commit install
fi

# Set up Git hooks
print_status "Setting up Git configuration..."
git config --global --add safe.directory /workspace

# Create necessary directories
print_status "Creating development directories..."
mkdir -p logs tmp coverage test-results htmlcov .pytest_cache

# Set up VS Code workspace settings
print_status "Setting up VS Code workspace..."
mkdir -p .vscode

# Create VS Code settings if they don't exist
if [ ! -f ".vscode/settings.json" ]; then
    cat > .vscode/settings.json << 'EOF'
{
    "python.defaultInterpreterPath": "/usr/local/bin/python",
    "python.terminal.activateEnvironment": false,
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/node_modules": true,
        "**/.pytest_cache": true,
        "**/htmlcov": true,
        "**/.coverage": true
    }
}
EOF
fi

# Create launch configuration for debugging
if [ ! -f ".vscode/launch.json" ]; then
    cat > .vscode/launch.json << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "FastAPI Debug",
            "type": "python",
            "request": "launch",
            "program": "run_api.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "DEBUG": "true"
            }
        },
        {
            "name": "Pytest Debug",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": ["tests/", "-v"],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        }
    ]
}
EOF
fi

# Set up development scripts
print_status "Setting up development scripts..."

# Make scripts executable
if [ -d "scripts" ]; then
    chmod +x scripts/*.sh 2>/dev/null || true
fi

# Create quick development commands
cat > /workspace/dev-commands.sh << 'EOF'
#!/bin/bash
# Quick development commands for CertRats

# Start API server
start-api() {
    echo "Starting FastAPI server..."
    cd /workspace && python run_api.py
}

# Start frontend
start-frontend() {
    echo "Starting Next.js frontend..."
    cd /workspace/frontend-next && npm run dev
}

# Run tests
run-tests() {
    echo "Running all tests..."
    cd /workspace && python -m pytest tests/ -v
}

# Run API tests only
test-api() {
    echo "Running API tests..."
    cd /workspace && python -m pytest tests/test_api/ -v
}

# Run frontend tests
test-frontend() {
    echo "Running frontend tests..."
    cd /workspace/frontend-next && npm test
}

# Format code
format-code() {
    echo "Formatting Python code..."
    cd /workspace && black .
    echo "Formatting frontend code..."
    cd /workspace/frontend-next && npm run format
}

# Lint code
lint-code() {
    echo "Linting Python code..."
    cd /workspace && flake8 .
    echo "Linting frontend code..."
    cd /workspace/frontend-next && npm run lint
}

# Database operations
db-migrate() {
    echo "Running database migrations..."
    cd /workspace && alembic upgrade head
}

db-reset() {
    echo "Resetting database..."
    cd /workspace && alembic downgrade base && alembic upgrade head
}

# Show available commands
dev-help() {
    echo "Available development commands:"
    echo "  start-api      - Start FastAPI server"
    echo "  start-frontend - Start Next.js frontend"
    echo "  run-tests      - Run all tests"
    echo "  test-api       - Run API tests only"
    echo "  test-frontend  - Run frontend tests"
    echo "  format-code    - Format all code"
    echo "  lint-code      - Lint all code"
    echo "  db-migrate     - Run database migrations"
    echo "  db-reset       - Reset database"
    echo "  dev-help       - Show this help"
}

EOF

chmod +x /workspace/dev-commands.sh
echo "source /workspace/dev-commands.sh" >> /home/<USER>/.zshrc

# Final setup
print_status "Finalizing setup..."

# Set proper permissions
sudo chown -R vscode:vscode /workspace/.vscode 2>/dev/null || true
sudo chown -R vscode:vscode /workspace/logs 2>/dev/null || true
sudo chown -R vscode:vscode /workspace/tmp 2>/dev/null || true

print_success "🎉 CertRats development environment setup complete!"
print_status "Available commands:"
echo "  - start-api: Start the FastAPI backend"
echo "  - start-frontend: Start the Next.js frontend"
echo "  - run-tests: Run all tests"
echo "  - format-code: Format all code"
echo "  - lint-code: Lint all code"
echo "  - dev-help: Show all available commands"
print_status "Happy coding! 🚀"
