#!/bin/bash

# CertRats DevContainer Setup Script
# This script runs after the devcontainer is created to set up the development environment

set -e

echo "🚀 Setting up CertRats development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Setting up basic development environment..."

# Set up Python environment
print_status "Setting up Python environment..."

# Upgrade pip
python -m pip install --upgrade pip

# Install Python dependencies if they exist
if [ -f "requirements.txt" ]; then
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
else
    print_warning "requirements.txt not found, installing basic packages..."
    pip install fastapi uvicorn sqlalchemy pytest black flake8 mypy
fi

if [ -f "tests/requirements-test.txt" ]; then
    print_status "Installing test dependencies..."
    pip install -r tests/requirements-test.txt
fi

# Set up Nix environment if available
if command -v nix >/dev/null 2>&1; then
    print_status "Setting up Nix environment..."

    # Enable flakes if not already enabled
    if [ ! -f ~/.config/nix/nix.conf ] || ! grep -q "experimental-features.*flakes" ~/.config/nix/nix.conf; then
        print_status "Enabling Nix flakes..."
        mkdir -p ~/.config/nix
        echo "experimental-features = nix-command flakes" >> ~/.config/nix/nix.conf
    fi

    # Set up direnv if available
    if command -v direnv >/dev/null 2>&1; then
        print_status "Setting up direnv integration..."
        echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
    fi

    print_success "Nix environment configured"
else
    print_warning "Nix not available yet (will be installed by features)"
fi

# Set up Node.js environment
print_status "Setting up Node.js environment..."

# Install frontend dependencies if they exist
if [ -d "frontend-next" ] && [ -f "frontend-next/package.json" ]; then
    print_status "Installing frontend dependencies..."
    cd frontend-next
    npm ci || npm install
    cd ..
else
    print_warning "frontend-next directory not found"
fi

# Set up basic configuration
print_status "Setting up basic configuration..."

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    cat > .env << EOF
# Development Environment Variables
DATABASE_URL=postgresql://certrats_dev:certrats_dev_password@localhost:5432/certrats_dev
REDIS_URL=redis://localhost:6379/0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
SECRET_KEY=dev-secret-key-change-in-production
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
EOF
    print_success "Created .env file"
fi

# Set up development tools
print_status "Setting up development tools..."

# Set up Git configuration
git config --global --add safe.directory /workspace || true

# Create necessary directories
mkdir -p logs tmp coverage test-results htmlcov .pytest_cache .vscode

# Install pre-commit if available
if command -v pre-commit >/dev/null 2>&1 && [ -f ".pre-commit-config.yaml" ]; then
    print_status "Installing pre-commit hooks..."
    pre-commit install || print_warning "Failed to install pre-commit hooks"
fi

# Create VS Code settings if they don't exist
if [ ! -f ".vscode/settings.json" ]; then
    cat > .vscode/settings.json << 'EOF'
{
    "python.defaultInterpreterPath": "/usr/local/bin/python",
    "python.terminal.activateEnvironment": false,
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/node_modules": true,
        "**/.pytest_cache": true,
        "**/htmlcov": true,
        "**/.coverage": true
    }
}
EOF
fi

# Create launch configuration for debugging
if [ ! -f ".vscode/launch.json" ]; then
    cat > .vscode/launch.json << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "FastAPI Debug",
            "type": "python",
            "request": "launch",
            "program": "run_api.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "DEBUG": "true"
            }
        },
        {
            "name": "Pytest Debug",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": ["tests/", "-v"],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        }
    ]
}
EOF
fi

# Set up development scripts
print_status "Setting up development scripts..."

# Make scripts executable
if [ -d "scripts" ]; then
    chmod +x scripts/*.sh 2>/dev/null || true
fi

# Create simple development commands
print_status "Creating development commands..."

cat > /workspace/dev-commands.sh << 'EOF'
#!/bin/bash
# Simple development commands for CertRats

# Start API server
start-api() {
    echo "Starting FastAPI server..."
    cd /workspace && python run_api.py
}

# Start frontend
start-frontend() {
    echo "Starting Next.js frontend..."
    cd /workspace/frontend-next && npm run dev
}

# Run tests
run-tests() {
    echo "Running all tests..."
    cd /workspace && python -m pytest tests/ -v
}

# Format code
format-code() {
    echo "Formatting code..."
    cd /workspace && black . 2>/dev/null || echo "Black not available"
}

# Show available commands
dev-help() {
    echo "🚀 CertRats Development Commands"
    echo "================================"
    echo "  start-api      - Start FastAPI server"
    echo "  start-frontend - Start Next.js frontend"
    echo "  run-tests      - Run all tests"
    echo "  format-code    - Format code"
    echo "  dev-help       - Show this help"
}

EOF

chmod +x /workspace/dev-commands.sh
echo "source /workspace/dev-commands.sh" >> ~/.zshrc 2>/dev/null || true

print_success "🎉 CertRats development environment setup complete!"
print_status "Type 'dev-help' for available commands"
print_status "Happy coding! 🚀"
