#!/bin/bash

# Minimal CertRats DevContainer Setup
echo "🚀 Setting up CertRats development environment..."

# Create basic development aliases
echo "Creating development shortcuts..."

cat > ~/.bash_aliases << 'EOF'
# CertRats Development Shortcuts
alias start-api='cd /workspace && python run_api.py'
alias start-frontend='cd /workspace/frontend-next && npm run dev'
alias run-tests='cd /workspace && python -m pytest tests/ -v'

dev-help() {
    echo "🚀 CertRats Development Commands"
    echo "================================"
    echo "  start-api      - Start FastAPI server"
    echo "  start-frontend - Start Next.js frontend"
    echo "  run-tests      - Run all tests"
    echo "  dev-help       - Show this help"
}
EOF

# Source aliases
echo "source ~/.bash_aliases" >> ~/.bashrc

echo "✅ Setup complete! Type 'dev-help' for available commands"
