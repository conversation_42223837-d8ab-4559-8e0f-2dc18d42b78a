#!/bin/bash

# CertRats DevContainer Setup Script
# This script runs after the devcontainer is created to set up the development environment

set -e

echo "🚀 Setting up CertRats development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Wait for services to be ready
print_status "Waiting for services to be ready..."

# Wait for PostgreSQL
print_status "Waiting for PostgreSQL..."
until pg_isready -h database -p 5432 -U certrats_dev; do
    sleep 2
done
print_success "PostgreSQL is ready!"

# Wait for Redis
print_status "Waiting for Redis..."
until redis-cli -h redis ping; do
    sleep 2
done
print_success "Redis is ready!"

# Set up Nix environment
print_status "Setting up Nix environment..."

# Check if Nix is available
if command -v nix >/dev/null 2>&1; then
    print_success "Nix is available"

    # Enable flakes if not already enabled
    if [ ! -f ~/.config/nix/nix.conf ] || ! grep -q "experimental-features.*flakes" ~/.config/nix/nix.conf; then
        print_status "Enabling Nix flakes..."
        mkdir -p ~/.config/nix
        echo "experimental-features = nix-command flakes" >> ~/.config/nix/nix.conf
    fi

    # Set up direnv if available
    if command -v direnv >/dev/null 2>&1; then
        print_status "Setting up direnv integration..."
        echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
        # Allow the .envrc file
        if [ -f ".devcontainer/.envrc" ]; then
            cd .devcontainer && direnv allow && cd ..
        fi
    fi

    # Enter Nix development shell
    print_status "Entering Nix development environment..."
    # The Nix shell will provide all dependencies
else
    print_warning "Nix not available, falling back to traditional package management"

    # Fallback: Set up Python environment traditionally
    print_status "Setting up Python environment..."
    python -m pip install --upgrade pip

    if [ -f "requirements.txt" ]; then
        print_status "Installing Python dependencies..."
        pip install -r requirements.txt
    fi

    if [ -f "tests/requirements-test.txt" ]; then
        print_status "Installing test dependencies..."
        pip install -r tests/requirements-test.txt
    fi
fi

# Set up Node.js environment (if not using Nix)
if ! command -v nix >/dev/null 2>&1; then
    print_status "Setting up Node.js environment..."

    # Install frontend dependencies
    if [ -d "frontend-next" ] && [ -f "frontend-next/package.json" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend-next
        npm ci
        cd ..
    fi

    # Install Playwright browsers if not already installed
    print_status "Installing Playwright browsers..."
    npx playwright install --with-deps
else
    print_status "Node.js environment managed by Nix"

    # Still need to install frontend dependencies
    if [ -d "frontend-next" ] && [ -f "frontend-next/package.json" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend-next
        nix-shell -p nodejs_20 --run "npm ci"
        cd ..
    fi

    # Install Playwright browsers
    print_status "Installing Playwright browsers..."
    nix-shell -p nodejs_20 --run "npx playwright install --with-deps"
fi

# Set up database
print_status "Setting up database..."

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    cat > .env << EOF
# Development Environment Variables
DATABASE_URL=*************************************************************/certrats_dev
REDIS_URL=redis://redis:6379/0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
SECRET_KEY=dev-secret-key-change-in-production
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
EOF
fi

# Run database migrations
if [ -f "alembic.ini" ] || [ -f "config/alembic.ini" ]; then
    print_status "Running database migrations..."
    if [ -f "config/alembic.ini" ]; then
        alembic -c config/alembic.ini upgrade head
    else
        alembic upgrade head
    fi
else
    print_warning "No alembic.ini found, skipping migrations"
fi

# Seed database with initial data
if [ -f "data/seed.py" ]; then
    print_status "Seeding database with initial data..."
    python data/seed.py
elif [ -f "load_test_data.py" ]; then
    print_status "Loading test data..."
    python load_test_data.py
fi

# Set up pre-commit hooks
if [ -f ".pre-commit-config.yaml" ]; then
    print_status "Installing pre-commit hooks..."
    pre-commit install
fi

# Set up Git hooks
print_status "Setting up Git configuration..."
git config --global --add safe.directory /workspace

# Create necessary directories
print_status "Creating development directories..."
mkdir -p logs tmp coverage test-results htmlcov .pytest_cache

# Set up VS Code workspace settings
print_status "Setting up VS Code workspace..."
mkdir -p .vscode

# Create VS Code settings if they don't exist
if [ ! -f ".vscode/settings.json" ]; then
    cat > .vscode/settings.json << 'EOF'
{
    "python.defaultInterpreterPath": "/usr/local/bin/python",
    "python.terminal.activateEnvironment": false,
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/node_modules": true,
        "**/.pytest_cache": true,
        "**/htmlcov": true,
        "**/.coverage": true
    }
}
EOF
fi

# Create launch configuration for debugging
if [ ! -f ".vscode/launch.json" ]; then
    cat > .vscode/launch.json << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "FastAPI Debug",
            "type": "python",
            "request": "launch",
            "program": "run_api.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "DEBUG": "true"
            }
        },
        {
            "name": "Pytest Debug",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": ["tests/", "-v"],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        }
    ]
}
EOF
fi

# Set up development scripts
print_status "Setting up development scripts..."

# Make scripts executable
if [ -d "scripts" ]; then
    chmod +x scripts/*.sh 2>/dev/null || true
fi

# Create Nix-aware development commands
cat > /workspace/dev-commands.sh << 'EOF'
#!/bin/bash
# Nix-aware development commands for CertRats

# Check if we should use Nix
use_nix() {
    command -v nix >/dev/null 2>&1 && [ -f ".devcontainer/flake.nix" ]
}

# Run command with Nix if available
nix_run() {
    if use_nix; then
        nix develop .devcontainer --command "$@"
    else
        "$@"
    fi
}

# Start API server
start-api() {
    echo "Starting FastAPI server..."
    cd /workspace
    if use_nix; then
        nix develop .devcontainer --command python run_api.py
    else
        python run_api.py
    fi
}

# Start frontend
start-frontend() {
    echo "Starting Next.js frontend..."
    cd /workspace/frontend-next
    if use_nix; then
        nix develop ../.devcontainer --command npm run dev
    else
        npm run dev
    fi
}

# Run tests
run-tests() {
    echo "Running all tests..."
    cd /workspace
    nix_run python -m pytest tests/ -v
}

# Run API tests only
test-api() {
    echo "Running API tests..."
    cd /workspace
    nix_run python -m pytest tests/test_api/ -v
}

# Run frontend tests
test-frontend() {
    echo "Running frontend tests..."
    cd /workspace/frontend-next
    nix_run npm test
}

# Format code
format-code() {
    echo "Formatting Python code..."
    cd /workspace
    nix_run black .
    echo "Formatting frontend code..."
    cd /workspace/frontend-next
    nix_run npm run format
}

# Lint code
lint-code() {
    echo "Linting Python code..."
    cd /workspace
    nix_run flake8 .
    nix_run mypy .
    echo "Linting frontend code..."
    cd /workspace/frontend-next
    nix_run npm run lint
}

# Database operations
db-migrate() {
    echo "Running database migrations..."
    cd /workspace
    nix_run alembic upgrade head
}

db-reset() {
    echo "Resetting database..."
    cd /workspace
    nix_run alembic downgrade base
    nix_run alembic upgrade head
}

# Nix-specific commands
nix-update() {
    echo "Updating Nix flake..."
    cd /workspace/.devcontainer
    nix flake update
}

nix-check() {
    echo "Checking Nix flake..."
    cd /workspace/.devcontainer
    nix flake check
}

nix-shell() {
    echo "Entering Nix development shell..."
    cd /workspace
    nix develop .devcontainer
}

# Show available commands
dev-help() {
    echo "🚀 CertRats Development Commands"
    echo "================================"
    echo ""
    if use_nix; then
        echo "📦 Using Nix for dependency management"
    else
        echo "⚠️  Nix not available, using traditional tools"
    fi
    echo ""
    echo "🏃 Quick Start:"
    echo "  start-api      - Start FastAPI server"
    echo "  start-frontend - Start Next.js frontend"
    echo "  run-tests      - Run all tests"
    echo ""
    echo "🧪 Testing:"
    echo "  test-api       - Run API tests only"
    echo "  test-frontend  - Run frontend tests"
    echo ""
    echo "🎨 Code Quality:"
    echo "  format-code    - Format all code"
    echo "  lint-code      - Lint all code"
    echo ""
    echo "🗄️  Database:"
    echo "  db-migrate     - Run database migrations"
    echo "  db-reset       - Reset database"
    echo ""
    if use_nix; then
        echo "📦 Nix Commands:"
        echo "  nix-update     - Update Nix flake dependencies"
        echo "  nix-check      - Check Nix flake validity"
        echo "  nix-shell      - Enter Nix development shell"
        echo ""
    fi
    echo "❓ Help:"
    echo "  dev-help       - Show this help"
}

EOF

chmod +x /workspace/dev-commands.sh
echo "source /workspace/dev-commands.sh" >> /home/<USER>/.zshrc

# Final setup
print_status "Finalizing setup..."

# Set proper permissions
sudo chown -R vscode:vscode /workspace/.vscode 2>/dev/null || true
sudo chown -R vscode:vscode /workspace/logs 2>/dev/null || true
sudo chown -R vscode:vscode /workspace/tmp 2>/dev/null || true

print_success "🎉 CertRats development environment setup complete!"
print_status "Available commands:"
echo "  - start-api: Start the FastAPI backend"
echo "  - start-frontend: Start the Next.js frontend"
echo "  - run-tests: Run all tests"
echo "  - format-code: Format all code"
echo "  - lint-code: Lint all code"
echo "  - dev-help: Show all available commands"
print_status "Happy coding! 🚀"
