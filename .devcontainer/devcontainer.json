{
  "name": "CertRats Development Environment",
  "dockerComposeFile": ["../docker-compose.yml", "docker-compose.dev.yml"],
  "service": "devcontainer",
  "workspaceFolder": "/workspace",
  "shutdownAction": "stopCompose",

  // Features to install
  "features": {
    "ghcr.io/devcontainers/features/common-utils:2": {
      "installZsh": true,
      "configureZshAsDefaultShell": true,
      "installOhMyZsh": true,
      "upgradePackages": true,
      "username": "vscode",
      "userUid": "automatic",
      "userGid": "automatic"
    },
    "ghcr.io/devcontainers/features/nix:1": {
      "version": "latest",
      "multiUser": true,
      "enableFlakes": true
    },
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "version": "latest",
      "enableNonRootDocker": true,
      "moby": true
    },
    "ghcr.io/devcontainers/features/git:1": {
      "version": "latest",
      "ppa": true
    },
    "ghcr.io/devcontainers/features/github-cli:1": {
      "version": "latest"
    }
  },

  // Container environment variables
  "containerEnv": {
    "PYTHONPATH": "/workspace",
    "PYTHONDONTWRITEBYTECODE": "1",
    "PYTHONUNBUFFERED": "1",
    "NODE_ENV": "development",
    "DATABASE_URL": "*************************************************************/certrats_dev",
    "REDIS_URL": "redis://redis:6379/0",
    "ENVIRONMENT": "development",
    "DEBUG": "true",
    "LOG_LEVEL": "DEBUG"
  },

  // VS Code settings
  "customizations": {
    "vscode": {
      "settings": {
        "python.defaultInterpreterPath": "/usr/local/bin/python",
        "python.linting.enabled": true,
        "python.linting.pylintEnabled": false,
        "python.linting.flake8Enabled": true,
        "python.linting.mypyEnabled": true,
        "python.linting.banditEnabled": true,
        "python.formatting.provider": "black",
        "python.formatting.blackArgs": ["--line-length=100"],
        "python.sortImports.args": ["--profile=black", "--line-length=100"],
        "python.testing.pytestEnabled": true,
        "python.testing.pytestArgs": ["tests/"],
        "python.testing.unittestEnabled": false,
        "python.testing.autoTestDiscoverOnSaveEnabled": true,
        
        "editor.formatOnSave": true,
        "editor.formatOnPaste": true,
        "editor.codeActionsOnSave": {
          "source.organizeImports": true,
          "source.fixAll": true
        },
        "editor.rulers": [100],
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        
        "files.exclude": {
          "**/__pycache__": true,
          "**/*.pyc": true,
          "**/node_modules": true,
          "**/.pytest_cache": true,
          "**/htmlcov": true,
          "**/.coverage": true,
          "**/venv": true,
          "**/.venv": true
        },
        
        "typescript.preferences.includePackageJsonAutoImports": "auto",
        "typescript.suggest.autoImports": true,
        "javascript.suggest.autoImports": true,
        "eslint.workingDirectories": ["frontend-next"],
        
        "git.autofetch": true,
        "git.enableSmartCommit": true,
        "git.confirmSync": false,
        
        "terminal.integrated.defaultProfile.linux": "zsh",
        "terminal.integrated.profiles.linux": {
          "zsh": {
            "path": "/bin/zsh"
          }
        }
      },
      
      "extensions": [
        // Python Development
        "ms-python.python",
        "ms-python.flake8",
        "ms-python.mypy-type-checker",
        "ms-python.black-formatter",
        "ms-python.isort",
        "ms-toolsai.jupyter",
        
        // JavaScript/TypeScript/React
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-typescript-next",
        "ms-vscode.vscode-json",
        
        // Docker & DevOps
        "ms-azuretools.vscode-docker",
        "ms-vscode-remote.remote-containers",
        "redhat.vscode-yaml",
        
        // Database
        "ms-mssql.mssql",
        "cweijan.vscode-postgresql-client2",
        "cweijan.vscode-redis-client",
        
        // Git & GitHub
        "github.vscode-pull-request-github",
        "github.copilot",
        "github.copilot-chat",
        "eamodio.gitlens",
        
        // Testing
        "ms-playwright.playwright",
        "littlefoxteam.vscode-python-test-adapter",
        
        // Documentation
        "yzhang.markdown-all-in-one",
        "davidanson.vscode-markdownlint",
        "trond-snekvik.simple-rst",
        
        // Code Quality
        "ms-python.pylint",
        "ms-python.bandit",
        "streetsidesoftware.code-spell-checker",
        "editorconfig.editorconfig",
        
        // Productivity
        "ms-vscode.todo-highlight",
        "gruntfuggly.todo-tree",
        "alefragnani.bookmarks",
        "ms-vscode.hexeditor",
        
        // API Development
        "humao.rest-client",
        "42crunch.vscode-openapi",
        
        // Themes & UI
        "pkief.material-icon-theme",
        "github.github-vscode-theme"
      ]
    }
  },

  // Port forwarding for development services
  "forwardPorts": [
    3000,  // Next.js frontend
    3001,  // Alternative frontend port
    3002,  // Alternative frontend port
    8000,  // FastAPI backend
    8001,  // Alternative API port
    8002,  // Minimal API
    5432,  // PostgreSQL
    6379,  // Redis
    9000,  // MinIO API
    9001,  // MinIO Console
    5555,  // Celery Flower
    9090,  // Prometheus
    3001,  // Grafana
    8080,  // Traefik Dashboard
    8081,  // Alternative Traefik
    6006   // Storybook
  ],

  "portsAttributes": {
    "3000": {
      "label": "Next.js Frontend",
      "onAutoForward": "notify"
    },
    "8000": {
      "label": "FastAPI Backend",
      "onAutoForward": "notify"
    },
    "8002": {
      "label": "Minimal API",
      "onAutoForward": "notify"
    },
    "5432": {
      "label": "PostgreSQL Database",
      "onAutoForward": "silent"
    },
    "6379": {
      "label": "Redis Cache",
      "onAutoForward": "silent"
    },
    "8080": {
      "label": "Traefik Dashboard",
      "onAutoForward": "openPreview"
    }
  },

  // Post-creation commands
  "postCreateCommand": ".devcontainer/setup.sh",
  
  // Mount the Docker socket for Docker-in-Docker
  "mounts": [
    "source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"
  ],

  // User settings
  "remoteUser": "vscode",
  "updateRemoteUserUID": true
}
