{
  description = "CertRats DevContainer Development Environment";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
        
        # Python with specific packages
        pythonEnv = pkgs.python311.withPackages (ps: with ps; [
          # Core dependencies
          fastapi
          uvicorn
          sqlalchemy
          alembic
          psycopg2
          redis
          
          # Development tools
          pytest
          pytest-cov
          pytest-xdist
          pytest-asyncio
          black
          flake8
          mypy
          isort
          bandit
          safety
          
          # Additional tools
          ipython
          jupyter
          requests
          httpx
          pydantic
          python-dotenv
          rich
          typer
          
          # Data science (if needed)
          pandas
          numpy
          matplotlib
          seaborn
          
          # Testing utilities
          factory-boy
          faker
          freezegun
        ]);
        
        # Node.js with global packages
        nodeEnv = pkgs.buildEnv {
          name = "node-env";
          paths = with pkgs.nodePackages; [
            nodejs_20
            npm
            yarn
            pnpm
            typescript
            ts-node
            eslint
            prettier
            nodemon
            create-react-app
            next
            webpack
            webpack-cli
            markdownlint-cli
            pyright
            typescript-language-server
            vscode-langservers-extracted
          ];
        };
        
      in
      {
        devShells.default = pkgs.mkShell {
          name = "certrats-devcontainer";
          
          buildInputs = with pkgs; [
            # Python environment
            pythonEnv
            poetry
            
            # Node.js environment
            nodeEnv
            
            # Core development tools
            git
            gitAndTools.gh
            gitAndTools.git-lfs
            gitAndTools.pre-commit
            
            # Shell and terminal enhancements
            zsh
            starship
            fzf
            ripgrep
            fd
            bat
            exa
            tree
            htop
            neofetch
            
            # Network and system tools
            curl
            wget
            httpie
            jq
            netcat
            telnet
            nmap
            
            # Database tools
            postgresql_15
            redis
            sqlite
            
            # Container tools
            docker
            docker-compose
            
            # Build tools
            gnumake
            gcc
            pkg-config
            
            # Text processing
            pandoc
            
            # Archive tools
            unzip
            zip
            gzip
            tar
            
            # Security tools
            openssl
            
            # Development utilities
            direnv
            nix-direnv
            shellcheck
            yamllint
            
            # Monitoring tools
            iotop
            nethogs
            procps
            psmisc
            
            # File system tools
            findutils
            coreutils
            rsync
            
            # Development libraries
            zlib
            libffi
            openssl.dev
            
            # Cloud tools (optional)
            awscli2
            
            # Kubernetes tools (optional)
            kubectl
            helm
            
            # Infrastructure as code (optional)
            terraform
            
            # Container registry tools
            skopeo
            
            # Image processing
            imagemagick
            
            # PDF tools
            poppler_utils
          ];

          shellHook = ''
            echo "🚀 CertRats DevContainer Nix Flake Environment"
            echo "=============================================="
            echo ""
            echo "📦 Environment Information:"
            echo "  • Python: $(python --version)"
            echo "  • Node.js: $(node --version)"
            echo "  • Docker: $(docker --version | cut -d' ' -f1-3)"
            echo "  • PostgreSQL: $(psql --version | cut -d' ' -f1-3)"
            echo "  • Redis: $(redis-cli --version | cut -d' ' -f1-2)"
            echo "  • Git: $(git --version | cut -d' ' -f1-3)"
            echo ""
            echo "🛠️  Quick Commands:"
            echo "  • start-api        - Start FastAPI backend"
            echo "  • start-frontend   - Start Next.js frontend"
            echo "  • run-tests        - Run all tests"
            echo "  • format-code      - Format all code"
            echo "  • lint-code        - Lint all code"
            echo "  • db-migrate       - Run database migrations"
            echo "  • dev-help         - Show all available commands"
            echo ""
            echo "📚 Nix Flake Commands:"
            echo "  • nix develop      - Enter this development shell"
            echo "  • nix flake update - Update flake inputs"
            echo "  • nix flake check  - Check flake validity"
            echo ""
            
            # Set up environment variables
            export PYTHONPATH="$PWD:$PYTHONPATH"
            export PATH="$PWD/scripts:$PATH"
            export NIX_SHELL_PRESERVE_PROMPT=1
            
            # Development environment variables
            export ENVIRONMENT="development"
            export DEBUG="true"
            export LOG_LEVEL="DEBUG"
            export PYTHONDONTWRITEBYTECODE="1"
            export PYTHONUNBUFFERED="1"
            export NODE_ENV="development"
            
            # Set up modern shell aliases
            alias ll='exa -la --git'
            alias la='exa -la --git'
            alias ls='exa --git'
            alias cat='bat --style=auto'
            alias grep='rg'
            alias find='fd'
            alias top='htop'
            
            # Development workflow aliases
            alias start-api='python run_api.py'
            alias start-frontend='cd frontend-next && npm run dev'
            alias run-tests='pytest tests/ -v'
            alias test-api='pytest tests/test_api/ -v'
            alias test-frontend='cd frontend-next && npm test'
            alias format-code='black . && cd frontend-next && npm run format && cd ..'
            alias lint-code='flake8 . && mypy . && cd frontend-next && npm run lint && cd ..'
            alias db-migrate='alembic upgrade head'
            alias db-reset='alembic downgrade base && alembic upgrade head'
            alias db-shell='psql $DATABASE_URL'
            alias redis-shell='redis-cli -u $REDIS_URL'
            
            # Git workflow aliases
            alias gs='git status'
            alias ga='git add'
            alias gc='git commit'
            alias gp='git push'
            alias gl='git log --oneline --graph'
            alias gd='git diff'
            alias gb='git branch'
            alias gco='git checkout'
            alias gm='git merge'
            alias gr='git rebase'
            
            # Docker workflow aliases
            alias dc='docker-compose'
            alias dcu='docker-compose up -d'
            alias dcd='docker-compose down'
            alias dcl='docker-compose logs -f'
            alias dcp='docker-compose ps'
            alias dcr='docker-compose restart'
            
            # Nix workflow aliases
            alias nix-update='nix flake update'
            alias nix-check='nix flake check'
            alias nix-clean='nix-collect-garbage -d'
            
            # Development help function
            dev-help() {
              echo "🚀 CertRats Development Commands"
              echo "================================"
              echo ""
              echo "🏃 Quick Start:"
              echo "  start-api        - Start FastAPI backend server"
              echo "  start-frontend   - Start Next.js frontend server"
              echo "  run-tests        - Run all test suites"
              echo ""
              echo "🧪 Testing:"
              echo "  test-api         - Run backend API tests"
              echo "  test-frontend    - Run frontend tests"
              echo "  pytest tests/    - Run specific test directory"
              echo ""
              echo "🎨 Code Quality:"
              echo "  format-code      - Format Python and TypeScript code"
              echo "  lint-code        - Lint Python and TypeScript code"
              echo "  black .          - Format Python code only"
              echo "  flake8 .         - Lint Python code only"
              echo ""
              echo "🗄️  Database:"
              echo "  db-migrate       - Apply database migrations"
              echo "  db-reset         - Reset database to clean state"
              echo "  db-shell         - Open PostgreSQL shell"
              echo "  redis-shell      - Open Redis shell"
              echo ""
              echo "🐳 Docker:"
              echo "  dcu              - Start all services"
              echo "  dcd              - Stop all services"
              echo "  dcl              - View service logs"
              echo "  dcp              - Show service status"
              echo ""
              echo "📦 Nix:"
              echo "  nix-update       - Update flake dependencies"
              echo "  nix-check        - Validate flake configuration"
              echo "  nix-clean        - Clean up old Nix generations"
              echo ""
              echo "🔧 Git:"
              echo "  gs, ga, gc, gp   - Status, add, commit, push"
              echo "  gl, gd, gb       - Log, diff, branch"
              echo ""
            }
            
            # Set up direnv integration
            if command -v direnv >/dev/null 2>&1; then
              eval "$(direnv hook zsh)"
            fi
            
            # Set up starship prompt
            if command -v starship >/dev/null 2>&1; then
              eval "$(starship init zsh)"
            fi
            
            # Set up fzf integration
            if command -v fzf >/dev/null 2>&1; then
              eval "$(fzf --zsh)"
            fi
            
            # Welcome message
            echo "✨ Environment ready! Type 'dev-help' for available commands."
            echo ""
          '';
        };
        
        # Additional outputs for CI/CD or other uses
        packages.default = pythonEnv;
        
        # Formatter for nix fmt
        formatter = pkgs.nixpkgs-fmt;
      });
}
