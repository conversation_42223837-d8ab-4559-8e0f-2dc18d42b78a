# CertRats Development Container
# Multi-stage Dockerfile optimized for development with all tools and dependencies

FROM mcr.microsoft.com/devcontainers/python:3.11-bullseye

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/workspace \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    NODE_ENV=development \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Build tools
    build-essential \
    gcc \
    g++ \
    make \
    cmake \
    # Database clients and libraries
    postgresql-client \
    libpq-dev \
    redis-tools \
    # Network tools
    curl \
    wget \
    netcat \
    telnet \
    # Development tools
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    # Graphics libraries (for data visualization)
    libcairo2-dev \
    libgirepository1.0-dev \
    # SSL/TLS
    ca-certificates \
    # Cleanup
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20 (latest LTS)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Install global Node.js tools
RUN npm install -g \
    yarn \
    pnpm \
    @playwright/test \
    typescript \
    ts-node \
    eslint \
    prettier \
    nodemon

# Install Docker CLI (for Docker-in-Docker)
RUN curl -fsSL https://get.docker.com | sh

# Install Docker Compose
RUN curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose \
    && chmod +x /usr/local/bin/docker-compose

# Create workspace directory
WORKDIR /workspace

# Copy Python requirements and install dependencies
COPY requirements.txt requirements-minimal.txt pyproject.toml ./
COPY tests/requirements-test.txt ./tests/

# Install Python dependencies
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt && \
    pip install -r tests/requirements-test.txt && \
    # Additional development tools
    pip install \
        ipython \
        ipdb \
        pdbpp \
        jupyter \
        notebook \
        jupyterlab \
        # Code quality tools
        pre-commit \
        safety \
        bandit \
        # Documentation tools
        sphinx \
        sphinx-rtd-theme \
        myst-parser \
        # Performance tools
        py-spy \
        memory-profiler \
        line-profiler

# Install frontend dependencies (if package.json exists)
COPY frontend-next/package*.json ./frontend-next/
RUN cd frontend-next && npm ci

# Install Playwright browsers
RUN npx playwright install --with-deps

# Set up Oh My Zsh for better terminal experience
USER vscode
RUN sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended
USER root

# Configure Git (will be overridden by user's git config)
RUN git config --global init.defaultBranch main && \
    git config --global pull.rebase false

# Create directories for development
RUN mkdir -p /workspace/.vscode \
    /workspace/logs \
    /workspace/tmp \
    /workspace/coverage \
    /workspace/test-results

# Set up pre-commit hooks directory
RUN mkdir -p /workspace/.git/hooks

# Install additional Python packages for AI/ML development
RUN pip install \
    # AI/ML libraries
    scikit-learn \
    numpy \
    pandas \
    matplotlib \
    seaborn \
    plotly \
    # API development
    fastapi-cli \
    uvicorn[standard] \
    # Database tools
    alembic \
    sqlalchemy-utils \
    # Testing enhancements
    pytest-xdist \
    pytest-benchmark \
    pytest-mock \
    pytest-html \
    pytest-json-report \
    # Development utilities
    python-dotenv \
    python-decouple \
    rich \
    typer

# Set up development aliases and shortcuts
RUN echo 'alias ll="ls -la"' >> /home/<USER>/.zshrc && \
    echo 'alias la="ls -la"' >> /home/<USER>/.zshrc && \
    echo 'alias ..="cd .."' >> /home/<USER>/.zshrc && \
    echo 'alias ...="cd ../.."' >> /home/<USER>/.zshrc && \
    echo 'alias grep="grep --color=auto"' >> /home/<USER>/.zshrc && \
    echo 'alias fgrep="fgrep --color=auto"' >> /home/<USER>/.zshrc && \
    echo 'alias egrep="egrep --color=auto"' >> /home/<USER>/.zshrc && \
    echo 'export PATH="/workspace/scripts:$PATH"' >> /home/<USER>/.zshrc && \
    echo 'export PYTHONPATH="/workspace:$PYTHONPATH"' >> /home/<USER>/.zshrc

# Set up development environment shortcuts
RUN echo '# CertRats Development Shortcuts' >> /home/<USER>/.zshrc && \
    echo 'alias api="cd /workspace && python run_api.py"' >> /home/<USER>/.zshrc && \
    echo 'alias frontend="cd /workspace/frontend-next && npm run dev"' >> /home/<USER>/.zshrc && \
    echo 'alias test="cd /workspace && python -m pytest"' >> /home/<USER>/.zshrc && \
    echo 'alias test-api="cd /workspace && python -m pytest tests/test_api/"' >> /home/<USER>/.zshrc && \
    echo 'alias test-frontend="cd /workspace/frontend-next && npm test"' >> /home/<USER>/.zshrc && \
    echo 'alias lint="cd /workspace && flake8 . && cd frontend-next && npm run lint"' >> /home/<USER>/.zshrc && \
    echo 'alias format="cd /workspace && black . && cd frontend-next && npm run format"' >> /home/<USER>/.zshrc && \
    echo 'alias db-migrate="cd /workspace && alembic upgrade head"' >> /home/<USER>/.zshrc && \
    echo 'alias db-reset="cd /workspace && alembic downgrade base && alembic upgrade head"' >> /home/<USER>/.zshrc

# Set proper ownership
RUN chown -R vscode:vscode /home/<USER>

# Switch to vscode user
USER vscode

# Set working directory
WORKDIR /workspace

# Default command
CMD ["sleep", "infinity"]
