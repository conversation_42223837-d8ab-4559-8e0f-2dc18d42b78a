# Docker Compose Override for Dev<PERSON>ontainer
# This file provides additional configuration for development environment
# It extends the main docker-compose.yml and docker-compose.dev.yml

version: '3.8'

services:
  # Override API service for development
  api:
    environment:
      # Development-specific environment variables
      - PYTHONPATH=/workspace
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - RELOAD=true
      - WORKERS=1
    volumes:
      # Mount source code for hot reloading
      - ../api:/app/api:cached
      - ../tests:/app/tests:cached
      - ../data:/app/data:cached
      - ../logs:/app/logs:cached
    # Enable debugging
    command: >
      sh -c "
        echo 'Starting API in development mode...' &&
        uvicorn api.main:app 
          --host 0.0.0.0 
          --port 8000 
          --reload 
          --reload-dir /app/api 
          --log-level debug
      "
    # Add development labels
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-api-dev.rule=Host(`api.certrats.localhost`)"
      - "traefik.http.routers.certrats-api-dev.entrypoints=web"
      - "traefik.http.services.certrats-api-dev.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik"
      - "dev.environment=development"

  # Override frontend service for development
  frontend:
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://api.certrats.localhost/api/v1
      - NEXT_PUBLIC_ENVIRONMENT=development
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      # Mount source code for hot reloading
      - ../frontend-next:/app:cached
      - /app/node_modules
      - /app/.next
    # Development command with hot reloading
    command: >
      sh -c "
        echo 'Installing dependencies...' &&
        npm ci &&
        echo 'Starting frontend in development mode...' &&
        npm run dev
      "
    # Add development labels
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-frontend-dev.rule=Host(`app.certrats.localhost`)"
      - "traefik.http.routers.certrats-frontend-dev.entrypoints=web"
      - "traefik.http.services.certrats-frontend-dev.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik"
      - "dev.environment=development"

  # Development database with enhanced logging
  database:
    environment:
      - POSTGRES_DB=certrats_dev
      - POSTGRES_USER=certrats_dev
      - POSTGRES_PASSWORD=certrats_dev_password
    # Enhanced PostgreSQL configuration for development
    command: >
      postgres
        -c log_statement=all
        -c log_destination=stderr
        -c log_min_messages=info
        -c log_line_prefix='%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
        -c log_min_duration_statement=0
        -c shared_preload_libraries=pg_stat_statements
        -c pg_stat_statements.track=all
        -c pg_stat_statements.max=10000
    volumes:
      # Add development SQL scripts
      - ../docker/database/dev-init:/docker-entrypoint-initdb.d:ro
      - ../data/sql:/sql:ro
    labels:
      - "dev.service=database"
      - "dev.environment=development"

  # Development Redis with enhanced configuration
  redis:
    # Enhanced Redis configuration for development
    command: >
      redis-server
        --appendonly yes
        --loglevel verbose
        --save 60 1000
        --maxmemory 256mb
        --maxmemory-policy allkeys-lru
    labels:
      - "dev.service=cache"
      - "dev.environment=development"

  # Development storage with debug logging
  storage:
    environment:
      - MINIO_ROOT_USER=certrats_dev
      - MINIO_ROOT_PASSWORD=certrats_dev_secret
      - MINIO_BROWSER_REDIRECT_URL=http://localhost:9001
    labels:
      - "dev.service=storage"
      - "dev.environment=development"

  # Add development-specific services
  
  # Adminer for database management
  adminer:
    image: adminer:latest
    container_name: certrats_dev_adminer
    restart: unless-stopped
    environment:
      - ADMINER_DEFAULT_SERVER=database
      - ADMINER_DESIGN=pepa-linha
    networks:
      - certrats_network
    ports:
      - "8080:8080"
    depends_on:
      - database
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-adminer.rule=Host(`db.certrats.localhost`)"
      - "traefik.http.routers.certrats-adminer.entrypoints=web"
      - "traefik.http.services.certrats-adminer.loadbalancer.server.port=8080"
      - "traefik.docker.network=traefik"
      - "dev.service=database-admin"
      - "dev.environment=development"

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: certrats_dev_redis_commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin
    networks:
      - certrats_network
    ports:
      - "8081:8081"
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-redis-commander.rule=Host(`redis.certrats.localhost`)"
      - "traefik.http.routers.certrats-redis-commander.entrypoints=web"
      - "traefik.http.services.certrats-redis-commander.loadbalancer.server.port=8081"
      - "traefik.docker.network=traefik"
      - "dev.service=cache-admin"
      - "dev.environment=development"

  # Swagger UI for API documentation
  swagger-ui:
    image: swaggerapi/swagger-ui:latest
    container_name: certrats_dev_swagger
    restart: unless-stopped
    environment:
      - SWAGGER_JSON_URL=http://api.certrats.localhost/openapi.json
      - BASE_URL=/swagger
    networks:
      - certrats_network
    ports:
      - "8082:8080"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-swagger.rule=Host(`docs.certrats.localhost`)"
      - "traefik.http.routers.certrats-swagger.entrypoints=web"
      - "traefik.http.services.certrats-swagger.loadbalancer.server.port=8080"
      - "traefik.docker.network=traefik"
      - "dev.service=api-docs"
      - "dev.environment=development"

# Development-specific networks
networks:
  certrats_network:
    driver: bridge
    labels:
      - "dev.network=internal"
  traefik:
    external: true
    labels:
      - "dev.network=external"

# Development-specific volumes with labels
volumes:
  certrats_dev_postgres_data:
    labels:
      - "dev.volume=database"
      - "dev.environment=development"
  certrats_dev_redis_data:
    labels:
      - "dev.volume=cache"
      - "dev.environment=development"
  certrats_dev_minio_data:
    labels:
      - "dev.volume=storage"
      - "dev.environment=development"
