# CertRats DevContainer Nix Shell
# This provides all CLI dependencies and development tools via Nix

{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  name = "certrats-devcontainer";
  
  buildInputs = with pkgs; [
    # Core development tools
    git
    curl
    wget
    jq
    tree
    htop
    neofetch
    
    # Shell and terminal tools
    zsh
    oh-my-zsh
    starship
    fzf
    ripgrep
    fd
    bat
    exa
    
    # Python ecosystem
    python311
    python311Packages.pip
    python311Packages.setuptools
    python311Packages.wheel
    python311Packages.virtualenv
    python311Packages.pipenv
    poetry
    
    # Node.js ecosystem
    nodejs_20
    nodePackages.npm
    nodePackages.yarn
    nodePackages.pnpm
    nodePackages.typescript
    nodePackages.ts-node
    nodePackages.eslint
    nodePackages.prettier
    nodePackages.nodemon
    
    # Database tools
    postgresql_15
    redis
    sqlite
    
    # Docker and container tools
    docker
    docker-compose
    
    # Development utilities
    gnumake
    gcc
    pkg-config
    
    # Network tools
    netcat
    telnet
    nmap
    
    # Text editors and IDE tools
    vim
    nano
    
    # Version control
    gitAndTools.gh
    gitAndTools.git-lfs
    gitAndTools.pre-commit
    
    # Testing and quality tools
    shellcheck
    
    # Documentation tools
    pandoc
    
    # Performance and monitoring
    iotop
    nethogs
    
    # Archive and compression
    unzip
    zip
    gzip
    tar
    
    # SSL/TLS tools
    openssl
    
    # Process management
    procps
    psmisc
    
    # File system tools
    findutils
    coreutils
    
    # Development libraries
    zlib
    libffi
    openssl.dev
    
    # Python development tools (via Nix)
    python311Packages.black
    python311Packages.flake8
    python311Packages.mypy
    python311Packages.isort
    python311Packages.pytest
    python311Packages.pytest-cov
    python311Packages.pytest-xdist
    python311Packages.ipython
    python311Packages.jupyter
    
    # Web development tools
    nodePackages.create-react-app
    nodePackages.next
    nodePackages.webpack
    nodePackages.webpack-cli
    
    # API development and testing
    httpie
    postman
    
    # Monitoring and observability
    prometheus
    grafana
    
    # Container registry tools
    skopeo
    
    # Cloud tools (if needed)
    awscli2
    
    # Kubernetes tools (if needed)
    kubectl
    helm
    
    # Terraform (if needed for infrastructure)
    terraform
    
    # Additional development tools
    direnv
    nix-direnv
    
    # Language servers for better IDE support
    nodePackages.pyright
    nodePackages.typescript-language-server
    nodePackages.vscode-langservers-extracted
    
    # Formatters and linters
    nodePackages.markdownlint-cli
    yamllint
    
    # Security tools
    nmap
    
    # Backup and sync tools
    rsync
    
    # Image processing (if needed for documentation)
    imagemagick
    
    # PDF tools (if needed for documentation)
    poppler_utils
  ];

  shellHook = ''
    echo "🚀 CertRats DevContainer Nix Shell Activated!"
    echo "📦 Available tools:"
    echo "  • Python $(python --version | cut -d' ' -f2)"
    echo "  • Node.js $(node --version)"
    echo "  • Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1)"
    echo "  • PostgreSQL $(psql --version | cut -d' ' -f3)"
    echo "  • Redis $(redis-cli --version | cut -d' ' -f2)"
    echo ""
    echo "🛠️  Development commands:"
    echo "  • nix-shell --run 'python run_api.py'     # Start API"
    echo "  • nix-shell --run 'cd frontend-next && npm run dev'  # Start frontend"
    echo "  • nix-shell --run 'pytest tests/'         # Run tests"
    echo "  • nix-shell --run 'black .'               # Format Python code"
    echo "  • nix-shell --run 'flake8 .'              # Lint Python code"
    echo ""
    echo "📚 Documentation: https://nixos.org/manual/nix/stable/"
    echo "🔧 Project tools available via Nix package manager"
    echo ""
    
    # Set up environment variables
    export PYTHONPATH="$PWD:$PYTHONPATH"
    export PATH="$PWD/scripts:$PATH"
    
    # Set up aliases for convenience
    alias ll='exa -la'
    alias la='exa -la'
    alias ls='exa'
    alias cat='bat'
    alias grep='rg'
    alias find='fd'
    alias ps='procs'
    
    # Development aliases
    alias api='nix-shell --run "python run_api.py"'
    alias frontend='nix-shell --run "cd frontend-next && npm run dev"'
    alias test='nix-shell --run "pytest tests/"'
    alias test-api='nix-shell --run "pytest tests/test_api/"'
    alias test-frontend='nix-shell --run "cd frontend-next && npm test"'
    alias lint='nix-shell --run "flake8 . && cd frontend-next && npm run lint"'
    alias format='nix-shell --run "black . && cd frontend-next && npm run format"'
    alias db-migrate='nix-shell --run "alembic upgrade head"'
    alias db-reset='nix-shell --run "alembic downgrade base && alembic upgrade head"'
    
    # Git aliases
    alias gs='git status'
    alias ga='git add'
    alias gc='git commit'
    alias gp='git push'
    alias gl='git log --oneline'
    alias gd='git diff'
    
    # Docker aliases
    alias dc='docker-compose'
    alias dcu='docker-compose up'
    alias dcd='docker-compose down'
    alias dcl='docker-compose logs'
    
    # Set up direnv if available
    if command -v direnv >/dev/null 2>&1; then
      eval "$(direnv hook zsh)"
    fi
    
    # Initialize starship prompt if available
    if command -v starship >/dev/null 2>&1; then
      eval "$(starship init zsh)"
    fi
    
    # Set up fzf if available
    if command -v fzf >/dev/null 2>&1; then
      source <(fzf --zsh)
    fi
  '';

  # Environment variables
  NIX_SHELL_PRESERVE_PROMPT = "1";
  
  # Python environment
  PYTHONPATH = ".";
  PYTHONDONTWRITEBYTECODE = "1";
  PYTHONUNBUFFERED = "1";
  
  # Node.js environment
  NODE_ENV = "development";
  
  # Development environment
  ENVIRONMENT = "development";
  DEBUG = "true";
  LOG_LEVEL = "DEBUG";
}
