version: '3.8'

# DevContainer-specific Docker Compose configuration
# This extends the main docker-compose.yml for development container needs

services:
  # Development Container Service
  devcontainer:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      # Mount the entire workspace
      - ..:/workspace:cached
      # Mount Docker socket for Docker-in-Docker
      - /var/run/docker.sock:/var/run/docker.sock
      # Preserve command history
      - devcontainer-bashhistory:/commandhistory
      # Cache directories for better performance
      - devcontainer-vscode-server:/home/<USER>/.vscode-server
      - devcontainer-vscode-server-insiders:/home/<USER>/.vscode-server-insiders
      # Python cache
      - devcontainer-pip-cache:/home/<USER>/.cache/pip
      # Node.js cache
      - devcontainer-npm-cache:/home/<USER>/.npm
      # Git config
      - devcontainer-git-config:/home/<USER>/.gitconfig
    environment:
      # Development environment variables
      - PYTHONPATH=/workspace
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - NODE_ENV=development
      - DATABASE_URL=*************************************************************/certrats_dev
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - SHELL=/bin/zsh
    networks:
      - certrats_network
      - traefik_network
    depends_on:
      - database
      - redis
      - storage
    # Keep container running
    command: sleep infinity
    # Enable init for proper signal handling
    init: true
    # Security options
    security_opt:
      - seccomp:unconfined
    # Capabilities for development tools
    cap_add:
      - SYS_PTRACE
    user: vscode

  # Development Database (PostgreSQL)
  database:
    image: postgres:15-alpine
    container_name: certrats_dev_database
    environment:
      - POSTGRES_DB=certrats_dev
      - POSTGRES_USER=certrats_dev
      - POSTGRES_PASSWORD=certrats_dev_password
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - certrats_dev_postgres_data:/var/lib/postgresql/data
      - ../docker/database/dev-init:/docker-entrypoint-initdb.d
    networks:
      - certrats_network
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U certrats_dev -d certrats_dev"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Enable logging for development
    command: postgres -c log_statement=all -c log_destination=stderr -c log_min_messages=info

  # Development Redis
  redis:
    image: redis:7-alpine
    container_name: certrats_dev_redis
    command: redis-server --appendonly yes --loglevel verbose
    volumes:
      - certrats_dev_redis_data:/data
    networks:
      - certrats_network
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Development MinIO Storage
  storage:
    image: minio/minio:latest
    container_name: certrats_dev_storage
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=certrats_dev
      - MINIO_ROOT_PASSWORD=certrats_dev_secret
    volumes:
      - certrats_dev_minio_data:/data
    networks:
      - certrats_network
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development Mailhog (for email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: certrats_dev_mailhog
    networks:
      - certrats_network
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    environment:
      - MH_STORAGE=maildir
      - MH_MAILDIR_PATH=/maildir

# Networks
networks:
  certrats_network:
    name: certrats_dev_network
    driver: bridge
  traefik_network:
    name: traefik_dev_network
    external: false

# Volumes for development
volumes:
  # DevContainer specific volumes
  devcontainer-bashhistory:
    name: certrats_devcontainer_bashhistory
  devcontainer-vscode-server:
    name: certrats_devcontainer_vscode_server
  devcontainer-vscode-server-insiders:
    name: certrats_devcontainer_vscode_server_insiders
  devcontainer-pip-cache:
    name: certrats_devcontainer_pip_cache
  devcontainer-npm-cache:
    name: certrats_devcontainer_npm_cache
  devcontainer-git-config:
    name: certrats_devcontainer_git_config
  
  # Development data volumes
  certrats_dev_postgres_data:
    name: certrats_dev_postgres_data
  certrats_dev_redis_data:
    name: certrats_dev_redis_data
  certrats_dev_minio_data:
    name: certrats_dev_minio_data
