# CertRats DevContainer Environment Configuration
# This file is used by direnv to automatically load the Nix environment

# Use Nix flake for development environment
use flake .

# Load environment variables from .env files
dotenv_if_exists .env.devcontainer
dotenv_if_exists .env.local
dotenv_if_exists .env

# Set up development paths
PATH_add scripts
PATH_add .devcontainer/scripts

# Python environment
export PYTHONPATH="$PWD:$PYTHONPATH"
export PYTHONDONTWRITEBYTECODE=1
export PYTHONUNBUFFERED=1

# Node.js environment
export NODE_ENV=development
export NODE_OPTIONS="--max-old-space-size=4096"

# Development environment
export ENVIRONMENT=development
export DEBUG=true
export LOG_LEVEL=DEBUG

# Database URLs (will be overridden by .env files if they exist)
export DATABASE_URL=${DATABASE_URL:-"*************************************************************/certrats_dev"}
export REDIS_URL=${REDIS_URL:-"redis://redis:6379/0"}

# Development flags
export DEV_AUTO_RELOAD=true
export DEV_HOT_RELOAD=true

# Nix-specific settings
export NIX_SHELL_PRESERVE_PROMPT=1

# Development shortcuts
export EDITOR=${EDITOR:-"code"}
export PAGER=${PAGER:-"bat"}

# Timezone
export TZ=${TZ:-"UTC"}

# Locale
export LANG=${LANG:-"en_US.UTF-8"}
export LC_ALL=${LC_ALL:-"en_US.UTF-8"}

# Shell configuration
export SHELL=/bin/zsh

# Development logging
log_status "🚀 CertRats DevContainer environment loaded via direnv"
log_status "📦 Nix flake environment active"
log_status "🛠️  Type 'dev-help' for available commands"
