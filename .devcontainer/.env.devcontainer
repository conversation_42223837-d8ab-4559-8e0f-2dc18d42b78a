# CertRats DevContainer Environment Variables
# These variables are used specifically for the development container environment

# Container Configuration
CONTAINER_NAME=certrats_devcontainer
CONTAINER_USER=vscode
CONTAINER_WORKSPACE=/workspace

# Database Configuration
DATABASE_URL=*************************************************************/certrats_dev
DATABASE_TEST_URL=*************************************************************/certrats_test
POSTGRES_DB=certrats_dev
POSTGRES_USER=certrats_dev
POSTGRES_PASSWORD=certrats_dev_password
POSTGRES_HOST=database
POSTGRES_PORT=5432

# Redis Configuration
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# MinIO Configuration
MINIO_ENDPOINT=storage:9000
MINIO_ACCESS_KEY=certrats_dev
MINIO_SECRET_KEY=certrats_dev_secret
MINIO_BUCKET=certrats-dev
MINIO_SECURE=false

# Email Configuration (MailHog)
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=false
SMTP_SSL=false
EMAIL_FROM=<EMAIL>

# Application Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
SECRET_KEY=dev-secret-key-change-in-production-environments
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:8000
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOW_HEADERS=*

# API Configuration
API_V1_STR=/api/v1
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
API_RELOAD=true

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_APP_NAME=CertRats
NEXT_PUBLIC_APP_VERSION=1.0.0-dev
NEXT_PUBLIC_ENVIRONMENT=development

# Testing Configuration
PYTEST_ADDOPTS=--tb=short --strict-markers --disable-warnings
PYTEST_TIMEOUT=300
COVERAGE_REPORT=html
COVERAGE_MIN_PERCENTAGE=80

# Development Tools
PRE_COMMIT_ENABLED=true
BLACK_LINE_LENGTH=100
FLAKE8_MAX_LINE_LENGTH=100
MYPY_STRICT=true

# Performance Configuration
UVICORN_WORKERS=1
UVICORN_RELOAD=true
UVICORN_LOG_LEVEL=debug

# Security (Development Only)
DISABLE_HTTPS_REDIRECT=true
DISABLE_SECURE_COOKIES=true
ALLOW_HTTP_BASIC_AUTH=true

# Monitoring and Observability
ENABLE_METRICS=true
ENABLE_TRACING=false
METRICS_PORT=9090

# File Upload Configuration
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=pdf,doc,docx,txt,md

# Cache Configuration
CACHE_TTL=300  # 5 minutes for development
CACHE_PREFIX=certrats:dev

# Rate Limiting (Relaxed for Development)
RATE_LIMIT_ENABLED=false
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60

# Background Tasks
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/2
CELERY_TASK_ALWAYS_EAGER=true  # Execute tasks synchronously in development

# AI/ML Configuration
AI_MODEL_PATH=/workspace/models
AI_ENABLE_GPU=false
AI_BATCH_SIZE=32

# Documentation
DOCS_ENABLED=true
DOCS_URL=/docs
REDOC_URL=/redoc

# Development Flags
ENABLE_DEBUG_TOOLBAR=true
ENABLE_PROFILING=true
ENABLE_SQL_LOGGING=true
ENABLE_REQUEST_LOGGING=true

# Playwright Configuration
PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_SLOW_MO=0

# Node.js Configuration
NODE_ENV=development
NODE_OPTIONS=--max-old-space-size=4096

# Python Configuration
PYTHONPATH=/workspace
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1
PYTHONHASHSEED=random

# Development Utilities
SHELL=/bin/zsh
EDITOR=code
PAGER=less

# Timezone
TZ=UTC

# Locale
LANG=en_US.UTF-8
LC_ALL=en_US.UTF-8

# Development Shortcuts
DEV_AUTO_RELOAD=true
DEV_HOT_RELOAD=true
DEV_MOCK_EXTERNAL_APIS=true
DEV_SKIP_AUTH=false  # Set to true to bypass authentication in development

# Backup and Restore
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=7

# Feature Flags for Development
FEATURE_USER_REGISTRATION=true
FEATURE_EMAIL_VERIFICATION=false  # Disabled for easier development
FEATURE_TWO_FACTOR_AUTH=false
FEATURE_SOCIAL_LOGIN=false
FEATURE_ANALYTICS=true
FEATURE_RECOMMENDATIONS=true
FEATURE_NOTIFICATIONS=true

# External Services (Mock in Development)
EXTERNAL_API_TIMEOUT=30
EXTERNAL_API_RETRIES=3
MOCK_EXTERNAL_SERVICES=true

# Development Database Seeding
SEED_DATABASE=true
SEED_SAMPLE_USERS=true
SEED_SAMPLE_CERTIFICATIONS=true
SEED_SAMPLE_LEARNING_PATHS=true

# Logging Configuration
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=/workspace/logs/certrats.log
LOG_ROTATION=midnight
LOG_RETENTION=7

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Development Metrics
COLLECT_METRICS=true
METRICS_INTERVAL=60
METRICS_RETENTION=24h
