#!/bin/bash

# Simple test script to validate devcontainer setup
echo "🧪 Testing devcontainer setup..."

# Test basic commands
echo "Testing Python..."
python --version || echo "❌ Python not found"

echo "Testing Node.js..."
node --version || echo "❌ Node.js not found"

echo "Testing Git..."
git --version || echo "❌ Git not found"

echo "Testing Docker..."
docker --version || echo "❌ Docker not found"

# Test if we can import basic Python packages
echo "Testing Python imports..."
python -c "import sys; print(f'Python path: {sys.executable}')" || echo "❌ Python import failed"

# Test if workspace is mounted correctly
echo "Testing workspace..."
if [ -d "/workspace" ]; then
    echo "✅ Workspace directory exists"
    ls -la /workspace | head -5
else
    echo "❌ Workspace directory not found"
fi

echo "🎉 Devcontainer test complete!"
