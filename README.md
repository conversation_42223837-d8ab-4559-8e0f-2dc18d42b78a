<div align="center">

# 🛡️ CertPathFinder

**The Ultimate Cybersecurity Certification Journey Planner**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![React](https://img.shields.io/badge/React-18.x-61DAFB?logo=react&logoColor=white)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-3178C6?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-00a393.svg)](https://fastapi.tiangolo.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-14.x-4169E1?logo=postgresql&logoColor=white)](https://www.postgresql.org/)
[![D3.js](https://img.shields.io/badge/D3.js-7.x-F9A03C?logo=d3.js&logoColor=white)](https://d3js.org/)

[![🚀 Live Demo](https://img.shields.io/badge/🚀_Live_Demo-Visit_App-blue?style=for-the-badge&logo=vercel)](https://certpathfinder.vercel.app)
[![📖 Documentation](https://img.shields.io/badge/📖_Documentation-Read_Docs-green?style=for-the-badge&logo=gitbook)](https://docs.certpathfinder.com)
[![🐛 Report Bug](https://img.shields.io/badge/🐛_Report_Bug-GitHub_Issues-red?style=for-the-badge&logo=github)](https://github.com/forkrul/replit-CertPathFinder/issues)
[![✨ Request Feature](https://img.shields.io/badge/✨_Request_Feature-GitHub_Issues-purple?style=for-the-badge&logo=github)](https://github.com/forkrul/replit-CertPathFinder/issues)

[![GitHub stars](https://img.shields.io/github/stars/forkrul/replit-CertPathFinder?style=social)](https://github.com/forkrul/replit-CertPathFinder/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/forkrul/replit-CertPathFinder?style=social)](https://github.com/forkrul/replit-CertPathFinder/network/members)
[![GitHub issues](https://img.shields.io/github/issues/forkrul/replit-CertPathFinder?style=flat-square)](https://github.com/forkrul/replit-CertPathFinder/issues)
[![GitHub pull requests](https://img.shields.io/github/issues-pr/forkrul/replit-CertPathFinder?style=flat-square)](https://github.com/forkrul/replit-CertPathFinder/pulls)

</div>

---

## 🎯 **What is CertPathFinder?**

A comprehensive, AI-powered platform that helps cybersecurity professionals navigate their certification journey with confidence. From entry-level to expert, we provide personalized roadmaps, cost analysis, and study planning tools for **465+ security certifications** across **8 domains**.

### 🌟 **Why Choose CertPathFinder?**

<table>
<tr>
<td width="50%">

**🎯 For Professionals**
- Personalized certification roadmaps
- Budget-aware cost planning
- Study time optimization
- Career progression tracking

**🏢 For Organizations**
- Team skill gap analysis
- Training budget planning
- Compliance tracking
- ROI measurement

</td>
<td width="50%">

**📊 Platform Stats**
- **465+** Security Certifications
- **12+** Certification Organizations
- **8** Security Domains
- **7** Supported Languages
- **100+** API Endpoints

</td>
</tr>
</table>

## 📋 Project Overview

This project maps cybersecurity certifications to jobs and career levels, addressing the challenge of navigating the vast array of certifications in the cybersecurity field.

### The Challenge

* Cybersecurity has a vast array of certifications
* It's difficult to determine which certifications are most relevant for specific job roles
* This creates confusion for both job seekers and employers

### The Solution

Our platform creates a comprehensive mapping system that connects:
* Specific job roles (e.g., Security Analyst, Penetration Tester)
* Career levels (Entry-Level, Mid-Level, Senior)
* Required and recommended certifications

### Key Benefits

* **For Job Seekers**: Clear guidance on certification paths aligned with career goals
* **For Employers**: Easier identification of qualified candidates and skill gaps
* **For the Industry**: Standardized framework for discussing cybersecurity competencies

## 🚀 **Quick Start**

<div align="center">

[![🔧 Setup Guide](https://img.shields.io/badge/🔧_Setup_Guide-Follow_Steps-orange?style=for-the-badge&logo=rocket)](docs/setup.md)
[![🐳 Docker Deploy](https://img.shields.io/badge/🐳_Docker_Deploy-One_Click-blue?style=for-the-badge&logo=docker)](docker-compose.yml)
[![⚡ Quick Demo](https://img.shields.io/badge/⚡_Quick_Demo-Try_Now-green?style=for-the-badge&logo=play)](https://certpathfinder.vercel.app)

</div>

### ⚡ **One-Click Setup**

#### 🌟 **Recommended: Nix (Reproducible Environment)**

```bash
# Clone the repository
git clone https://github.com/forkrul/replit-CertPathFinder.git
cd replit-CertPathFinder

# Enter Nix development environment (all dependencies included)
nix develop  # or: make nix-develop

# Start development server
make dev

# 🎉 Open http://localhost:8000
```

#### 📦 **Alternative: Traditional Setup**

```bash
# Backend setup
pip install -r requirements.txt
alembic upgrade head
python run_api.py &

# Frontend setup (React Migration)
cd frontend
npm install
npm start

# 🎉 Open http://localhost:3000
```

### 🐳 **Docker Setup**

```bash
# Start everything with Docker Compose
docker-compose up -d

# 🎉 Open http://localhost:8080
```

---

## ✨ **Features**

### 🎯 **Core Features**

<div align="center">

[![🔍 Explore Certs](https://img.shields.io/badge/🔍_Explore-465+_Certifications-blue?style=for-the-badge&logo=search)](frontend/src/pages/CertificationExplorer.tsx)
[![💰 Cost Calculator](https://img.shields.io/badge/💰_Cost-Calculator-green?style=for-the-badge&logo=calculator)](frontend/src/pages/CostCalculator.tsx)
[![📊 Dashboard](https://img.shields.io/badge/📊_Analytics-Dashboard-purple?style=for-the-badge&logo=chart-bar)](frontend/src/pages/Dashboard.tsx)
[![🎯 Career Paths](https://img.shields.io/badge/🎯_Career-Paths-orange?style=for-the-badge&logo=target)](api/v1/career_transition.py)

</div>

- **🖥️ Interactive Main Page**
  - Engaging hero section with platform value proposition
  - Dashboard preview with key metrics visualization
  - Career path navigator with interactive visualization
  - Certification catalog with filtering capabilities
  - Success stories from platform users
  - Step-by-step getting started guide

- **🔍 Interactive Filtering System**
  - Multi-select domain filter buttons for precise domain selection
  - Experience level filtering (Entry Level to Expert)
  - Cost-based filtering with dynamic slider
  - Real-time results updating

- **📊 Comprehensive Data Display**
  - Detailed certification information
  - Cost and prerequisites details
  - Organization and domain categorization
  - Direct links to certification pages

- **🌐 Multilingual Support**
  - Full internationalization (i18n) support
  - Available languages:
    - 🇬🇧 English (en)
    - 🇩🇪 German (de)
    - 🇪🇸 Spanish (es)
    - 🇫🇷 French (fr)
    - 🇿🇦 Afrikaans (af)
    - 🇿🇦 isiZulu (zu)
    - 🇷🇴 Romanian (ro)
  - Dynamic language switching
  - Localized content and UI elements

- **💾 Database Integration**
  - PostgreSQL database for robust data management
  - Automatic data extraction from Security Certification Roadmap
  - Structured certification data model

- **⚛️ Modern React UI**
  - Component-based architecture
  - TypeScript for type safety
  - Responsive design for all devices
  - Improved user experience

- **🔄 API Versioning System**
  - Centralized API route management with versioning (api/v1/...)
  - Consistent endpoint definitions between frontend and backend
  - React hooks for accessing versioned API endpoints

### 🗺️ **Roadmap**

<div align="center">

#### 🎯 **2024 Q4 - React Migration**
- [x] FastAPI backend with 100+ endpoints
- [x] Database with 465+ certifications
- [x] React frontend foundation
- [ ] Complete UI migration
- [ ] E2E testing with Playwright

#### 🚀 **2025 Q1 - AI & Analytics**
- [ ] AI-powered study recommendations
- [ ] Advanced cost calculator with ROI
- [ ] Interactive D3.js visualizations
- [ ] Mobile app (React Native)

#### 🌟 **2025 Q2 - Community & Enterprise**
- [ ] Community features & reviews
- [ ] Enterprise dashboard
- [ ] Mentorship platform
- [ ] Integration marketplace

</div>

## 🔄 Technical Architecture

### System Overview

<div align="center">

```mermaid
flowchart TB
    subgraph Client ["Client Layer"]
        ReactApp[React Application]
        ApiService[API Service]
        ApiHooks[API Hooks]
    end

    subgraph Server ["Server Layer"]
        FastAPI[FastAPI Application]
        RouteManager[Route Manager]
        Versioning[API Versioning]

        subgraph Endpoints ["API Endpoints"]
            direction TB
            AuthEndpoints[Authentication]
            UserEndpoints[User Management]
            CertEndpoints[Certifications]
            AdminEndpoints[Administration]
        end

        subgraph Database ["Database Layer"]
            PostgreSQL[(PostgreSQL)]
            SQLAlchemy[SQLAlchemy ORM]
        end
    end

    ReactApp --> ApiHooks
    ApiHooks --> ApiService
    ApiService --> FastAPI

    FastAPI --> RouteManager
    RouteManager --> Versioning
    Versioning --> Endpoints

    Endpoints --> SQLAlchemy
    SQLAlchemy --> PostgreSQL
```

</div>

### Data Flow

<div align="center">

```mermaid
sequenceDiagram
    participant User
    participant UI as React Interface
    participant API as FastAPI Backend
    participant Filter as Filter System
    participant DB as PostgreSQL Database
    participant Viz as D3.js Visualization

    User->>UI: Select Filters
    UI->>API: Request Data
    API->>Filter: Apply Criteria
    Filter->>DB: Query Certifications
    DB-->>Filter: Return Matches
    Filter-->>API: Process Results
    API-->>UI: Return Data
    UI->>Viz: Generate Path
    Viz-->>UI: Display Graph
```

</div>

### Project Structure

```
.
├── api/                    # FastAPI backend
│   ├── endpoints/          # API endpoint routes
│   ├── models/             # SQLAlchemy models
│   ├── schemas/            # Pydantic schemas
│   ├── services/           # Business logic
│   └── utils/              # Utilities
├── frontend/               # React frontend
│   ├── src/                # Source code
│   │   ├── components/     # Reusable UI components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
├── shared/                 # Shared code between frontend and backend
│   └── api_endpoints/      # Centralized API endpoint definitions
├── tests/                  # Test suite
│   ├── api/                # Backend tests
│   └── frontend/           # Frontend tests
├── .dockerwrapper/         # Docker configuration
└── docs/                   # Documentation
```

### 🛠️ **Tech Stack**

<div align="center">

[![React](https://img.shields.io/badge/Frontend-React_18-61DAFB?style=for-the-badge&logo=react&logoColor=white)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/Language-TypeScript-3178C6?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![FastAPI](https://img.shields.io/badge/Backend-FastAPI-009688?style=for-the-badge&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com/)
[![PostgreSQL](https://img.shields.io/badge/Database-PostgreSQL-4169E1?style=for-the-badge&logo=postgresql&logoColor=white)](https://www.postgresql.org/)

[![Tailwind CSS](https://img.shields.io/badge/Styling-Tailwind_CSS-06B6D4?style=for-the-badge&logo=tailwindcss&logoColor=white)](https://tailwindcss.com/)
[![Docker](https://img.shields.io/badge/Deploy-Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white)](https://www.docker.com/)
[![Playwright](https://img.shields.io/badge/Testing-Playwright-2EAD33?style=for-the-badge&logo=playwright&logoColor=white)](https://playwright.dev/)
[![D3.js](https://img.shields.io/badge/Visualization-D3.js-F9A03C?style=for-the-badge&logo=d3.js&logoColor=white)](https://d3js.org/)

</div>

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Frontend** | React 18 + TypeScript | Modern, responsive UI |
| **Styling** | Tailwind CSS + Headless UI | Beautiful, accessible design |
| **State** | React Query + Context API | Efficient data management |
| **Backend** | FastAPI + Python 3.8+ | High-performance API |
| **Database** | PostgreSQL + SQLAlchemy | Robust data persistence |
| **Testing** | Jest + Playwright + pytest | Comprehensive test coverage |
| **DevOps** | Docker + GitHub Actions | Automated CI/CD |

### API Versioning

The application implements a versioned API architecture that follows RESTful principles. All API endpoints are versioned using a URL-based strategy, following the pattern `/api/v{n}/resource` where `n` is the API version number (currently v1).

<div align="center">

```mermaid
flowchart LR
    subgraph API ["API Endpoints"]
        direction TB
        Auth[Authentication] --> Login[POST /api/v1/auth/login]
        Auth --> Logout[POST /api/v1/auth/logout]

        User[User Management] --> Profile[GET/PUT /api/v1/users/profile]
        User --> Preferences[GET/PUT /api/v1/users/preferences]

        Cert[Certifications] --> List[GET /api/v1/certifications]
        Cert --> Detail[GET /api/v1/certifications/{id}]
    end
```

</div>

## 🚀 Setup and Installation

<div align="center">

![Docker](https://img.shields.io/badge/Docker-Required-2496ED?logo=docker&logoColor=white)
![Docker Compose](https://img.shields.io/badge/Docker_Compose-Required-2496ED?logo=docker&logoColor=white)

</div>

### Quick Start (Docker)

1. **Prerequisites**
   - Docker and Docker Compose installed on your system
   - Git for cloning the repository

2. **Installation Steps**
   ```bash
   # Clone the repository
   git clone https://github.com/forkrul/CertPathFinder.git
   cd CertPathFinder

   # Start the application
   ./.dockerwrapper/run.sh up -d
   ```

3. **Access the Application**
   - React UI: http://localhost:8080/
   - FastAPI Swagger: http://localhost:8080/api/docs

### Deployment Options

#### Docker Compose (Recommended)

The recommended deployment method using Docker Compose:

```bash
# Production deployment
./.dockerwrapper/run.sh up -d --production

# View logs
./.dockerwrapper/run.sh logs -f
```

#### Single-Container Deployment

For simpler deployments, we offer a single-container option:

```bash
# Build and run the single container
./.dockerwrapper/run-combined.sh
```

#### Manual Deployment

For advanced users who want to deploy services separately:

1. **Database Setup**
   ```bash
   # Set up PostgreSQL
   docker run -d --name certrats-db -e POSTGRES_PASSWORD=your_password -p 5432:5432 postgres:14
   ```

2. **Backend Deployment**
   ```bash
   # Install Python dependencies
   pip install -r requirements.txt

   # Start the FastAPI server
   uvicorn api.app:app --host 0.0.0.0 --port 8000
   ```

3. **Frontend Deployment**
   ```bash
   # Install Node.js dependencies
   cd frontend && npm install

   # Build the production bundle
   npm run build

   # Serve the static files
   npx serve -s build -l 3000
   ```

### Environment Configuration

The application uses environment variables for configuration. Create a `.env` file in the project root:

```
# Database
DATABASE_URL=**************************************/certrats

# API
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Authentication
SECRET_KEY=your_secret_key
AUTH_TOKEN_EXPIRE_MINUTES=60
```

### Cloud Deployment

The application can be deployed to various cloud platforms:

<div align="center">

| Platform | Deployment Guide |
|----------|-----------------|
| **AWS** | [AWS Deployment Guide](docs/deployment/aws.md) |
| **Azure** | [Azure Deployment Guide](docs/deployment/azure.md) |
| **Google Cloud** | [GCP Deployment Guide](docs/deployment/gcp.md) |
| **Heroku** | [Heroku Deployment Guide](docs/deployment/heroku.md) |

</div>

## 👨‍💻 Development

### Current Status

<div align="center">

| Feature | Status |
|---------|--------|
| Main page interface | ✅ Complete |
| Interactive dashboard | ✅ Complete |
| Career path navigator | ✅ Complete |
| Certification catalog | ✅ Complete |
| Filtering system | ✅ Complete |
| Database schema | ✅ Complete |
| Data extraction pipeline | ✅ Complete |
| Internationalization | ✅ Complete |

</div>

### Next Steps

<div align="center">

| Priority | Task |
|----------|------|
| 1️⃣ | Implement certification page based on PRD |
| 2️⃣ | Implement certification data enrichment system |
| 3️⃣ | Enhance graph interaction stability |
| 4️⃣ | Implement certification progression mapping |
| 5️⃣ | Develop comparison features |

</div>

### Development Workflow

#### Local Development

1. **Clone the repository**
   ```bash
   git clone https://github.com/forkrul/CertPathFinder.git
   cd CertPathFinder
   ```

2. **Start the development environment**
   ```bash
   # Using Docker (recommended)
   ./.dockerwrapper/run.sh up -d

   # Or start services individually
   cd frontend && npm run dev  # Start React frontend
   cd api && uvicorn app:app --reload  # Start FastAPI backend
   ```

3. **Access development services**
   - React frontend: http://localhost:3000
   - FastAPI backend: http://localhost:3001
   - API documentation: http://localhost:3001/docs

#### Development Commands

<div align="center">

| Command | Description |
|---------|-------------|
| `./.dockerwrapper/run.sh up -d` | Start all services in Docker |
| `./.dockerwrapper/run.sh down` | Stop all services |
| `./.dockerwrapper/run.sh logs -f` | View logs from all services |
| `npm run dev` | Start React development server |
| `npm run build` | Build React production bundle |
| `uvicorn api.app:app --reload` | Start FastAPI with auto-reload |
| `pytest` | Run backend tests |
| `npm test` | Run frontend tests |

</div>

### Code Standards

#### Python Code Standards

We follow these Python Enhancement Proposals (PEPs) for code quality:

- **PEP-8**: Style Guide (4 spaces indentation, 79 char line length, snake_case for functions)
- **PEP-257**: Docstring Conventions (all modules, functions, classes must have docstrings)
- **PEP-484**: Type Hints (all function arguments and return values must have type hints)

#### JavaScript/TypeScript Code Standards

- **ESLint**: We use ESLint with the Airbnb configuration
- **Prettier**: Code formatting is handled by Prettier
- **TypeScript**: All new code should be written in TypeScript with proper type definitions

#### Commit Guidelines

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Example: `feat(certification): add comparison feature for certifications`

### Known Issues

- Node sizing based on certification difficulty needs improvement
- Graph interaction stability needs enhancement

## 🧪 **Testing Strategy**

<div align="center">

[![🧪 Run Tests](https://img.shields.io/badge/🧪_Run-Tests-green?style=for-the-badge&logo=test-tube)](tests/)
[![📊 Coverage Report](https://img.shields.io/badge/📊_Coverage-Report-blue?style=for-the-badge&logo=codecov)](coverage/)
[![🎭 E2E Tests](https://img.shields.io/badge/🎭_E2E-Playwright-purple?style=for-the-badge&logo=playwright)](tests/e2e/)
[![⚡ Performance](https://img.shields.io/badge/⚡_Performance-Tests-orange?style=for-the-badge&logo=speedometer)](tests/performance/)

![Test Coverage](https://img.shields.io/badge/Test_Coverage-20%25-yellow)
![Pytest](https://img.shields.io/badge/Pytest-Backend-3776AB?logo=python&logoColor=white)
![React Testing Library](https://img.shields.io/badge/React_Testing_Library-Frontend-61DAFB?logo=react&logoColor=white)
![Playwright](https://img.shields.io/badge/Playwright-E2E-2EAD33?logo=playwright&logoColor=white)
![Jest](https://img.shields.io/badge/Jest-Unit_Tests-C21325?logo=jest&logoColor=white)

</div>

### Test Coverage Summary

<div align="center">

| Component | Coverage | Status |
|-----------|----------|--------|
| Backend (Overall) | 20% | 🟡 In Progress |
| Frontend (Overall) | 35% | 🟡 In Progress |
| API Endpoints | 65% | 🟢 Good |
| Core Models | 80% | 🟢 Good |
| UI Components | 40% | 🟡 In Progress |
| E2E Flows | 15% | 🔴 Needs Improvement |

</div>

### Test Categories

Our testing strategy includes multiple layers of tests to ensure comprehensive coverage:

<div align="center">

| Test Type | Purpose | Tools | Examples |
|-----------|---------|-------|----------|
| **Unit Tests** | Test individual functions and classes | Pytest, Jest | Function validation, class methods |
| **Integration Tests** | Test interactions between components | Pytest, React Testing Library | API-database interactions, component interactions |
| **API Tests** | Verify API endpoints behavior | Pytest, TestClient | Endpoint responses, error handling |
| **UI Component Tests** | Test UI components in isolation | React Testing Library | Button clicks, form submissions |
| **E2E Tests** | Test complete user flows | Playwright | User registration, certification filtering |
| **Visual Regression** | Detect UI changes | Playwright | Component appearance, responsive design |
| **Performance Tests** | Measure performance metrics | Locust, Lighthouse | API response times, page load times |
| **Security Tests** | Identify security vulnerabilities | OWASP ZAP, Bandit | SQL injection, XSS vulnerabilities |

</div>

### Backend Testing

We use pytest for backend testing with the following principles:

- **Comprehensive Unit Tests**: Each module has dedicated test files
- **Mock External Dependencies**: Use unittest.mock to isolate components
- **Database Testing**: Test database models and queries with test databases
- **API Testing**: Test API endpoints with TestClient
- **Security Testing**: Verify authentication and authorization

#### Test Organization

Backend tests are organized by test type and module:

```
tests/
├── unit/                  # Unit tests
│   ├── models/            # Database model tests
│   ├── services/          # Business logic tests
│   └── utils/             # Utility function tests
├── integration/           # Integration tests
│   ├── api/               # API integration tests
│   └── database/          # Database integration tests
├── api/                   # API endpoint tests
├── security/              # Security-focused tests
└── performance/           # Performance tests
```

#### Test Markers

We use pytest markers to categorize tests:

```python
@pytest.mark.unit
def test_certification_model_validation():
    # Test certification model validation

@pytest.mark.integration
def test_certification_database_operations():
    # Test certification database operations

@pytest.mark.api
def test_certification_endpoint_get():
    # Test certification GET endpoint

@pytest.mark.security
def test_authentication_required():
    # Test authentication requirements
```

#### Key Modules with High Coverage

- `api/constants.py` - 100% coverage
- `models/base.py` - 100% coverage
- `models/health_check.py` - 100% coverage
- `models/reports.py` - 100% coverage
- `models/user_management.py` - 100% coverage
- `utils/certification_utils.py` - 100% coverage

#### Running Backend Tests

```bash
# Run all backend tests
python -m pytest tests/ api/tests/

# Run with coverage report
python -m pytest tests/ api/tests/ --cov=api --cov=models --cov=utils

# Run specific test category
python -m pytest -m "certification"

# Run tests by module
python -m pytest tests/unit/models/

# Run tests with verbose output
python -m pytest -v tests/api/

# Run tests and generate HTML report
python -m pytest --cov=api --cov-report=html
```

### Frontend Testing

Our frontend testing strategy includes unit tests, component tests, and end-to-end tests.

#### Unit Testing with Jest

We use Jest for unit testing JavaScript/TypeScript utility functions, hooks, and services:

```typescript
// Example utility function test
describe('formatCertificationCost', () => {
  it('formats cost correctly with currency symbol', () => {
    expect(formatCertificationCost(299.99, 'USD')).toBe('$299.99');
    expect(formatCertificationCost(299.99, 'EUR')).toBe('€299.99');
  });

  it('handles zero cost correctly', () => {
    expect(formatCertificationCost(0, 'USD')).toBe('Free');
  });
});

// Example custom hook test
describe('useFilteredCertifications', () => {
  it('filters certifications by domain', () => {
    const { result } = renderHook(() =>
      useFilteredCertifications(mockCertifications, { domains: ['Security'] })
    );
    expect(result.current.length).toBe(3);
  });
});
```

#### Component Testing with React Testing Library

We test React components with React Testing Library, focusing on user interactions:

```typescript
describe('CertificationCard', () => {
  it('displays certification details correctly', () => {
    render(<CertificationCard certification={mockCertification} />);
    expect(screen.getByText('CompTIA Security+')).toBeInTheDocument();
    expect(screen.getByText('$349')).toBeInTheDocument();
  });

  it('expands details when clicked', async () => {
    render(<CertificationCard certification={mockCertification} />);
    const expandButton = screen.getByRole('button', { name: /view details/i });
    await userEvent.click(expandButton);
    expect(screen.getByText('Prerequisites')).toBeInTheDocument();
  });
});
```

#### E2E Testing with Playwright

We use Playwright for end-to-end testing of complete user flows:

```typescript
test('user can filter certifications by domain', async ({ page }) => {
  await page.goto('/certification-explorer');

  // Select Security domain filter
  await page.click('button:has-text("Security")');

  // Verify filtered results
  await expect(page.locator('.certification-card')).toHaveCount(12);
  await expect(page.locator('text=CompTIA Security+')).toBeVisible();
});

test('user can switch language', async ({ page }) => {
  await page.goto('/');

  // Switch to German
  await page.click('[data-testid="language-selector"]');
  await page.click('text=Deutsch');

  // Verify translated content
  await expect(page.locator('h1')).toHaveText('Sicherheitszertifizierungen');
});
```

#### Visual Regression Testing

We use Playwright for visual regression testing:

```typescript
test('certification card visual appearance', async ({ page }) => {
  await page.goto('/certification-explorer');

  // Take screenshot of certification card
  const card = page.locator('.certification-card').first();
  await expect(card).toHaveScreenshot('certification-card.png');
});

test('responsive design on mobile', async ({ page }) => {
  // Set viewport to mobile size
  await page.setViewportSize({ width: 375, height: 667 });
  await page.goto('/certification-explorer');

  // Verify mobile layout
  await expect(page).toHaveScreenshot('mobile-certification-explorer.png');
});
```

#### Running Frontend Tests

```bash
# Run all unit and component tests
cd frontend && npm test

# Run with coverage report
cd frontend && npm run test:coverage

# Run tests in watch mode
cd frontend && npm test -- --watch

# Run specific test file
cd frontend && npm test -- CertificationCard.test.tsx

# Run E2E tests
cd frontend && npm run test:e2e

# Run E2E tests in UI mode
cd frontend && npm run test:e2e:ui

# Run visual regression tests
cd frontend && npm run test:visual
```

### Integration Testing

We test the integration between frontend and backend components:

```typescript
test('certification data flow from API to UI', async ({ page }) => {
  // Start mock API server
  const mockServer = setupMockServer();
  mockServer.get('/api/v1/certifications', (req, res) => {
    res.json(mockCertificationData);
  });

  // Navigate to certification explorer
  await page.goto('/certification-explorer');

  // Verify data is displayed correctly
  await expect(page.locator('.certification-card')).toHaveCount(mockCertificationData.length);
  await expect(page.locator('text=CompTIA Security+')).toBeVisible();
});
```

### Docker-Based Testing

We provide comprehensive Docker-based testing scripts that run the entire test suite in a containerized environment:

```bash
# Run comprehensive test suite
./.dockerwrapper/test-certifications-comprehensive.sh

# Run API tests only
docker exec -it certrats_fastapi sh -c "cd /app && python -m pytest api/tests/test_certification_endpoints.py -v"

# Run UI tests only
docker exec -it certrats_react sh -c "cd /app && npx playwright test e2e/certification-explorer-comprehensive.spec.ts"

# Run performance tests
docker exec -it certrats_fastapi sh -c "cd /app && python -m pytest tests/performance/ -v"

# Run security tests
docker exec -it certrats_fastapi sh -c "cd /app && python -m pytest tests/security/ -v"
```

### Test Reports

Test reports are generated in the following locations:

- Backend coverage: `test-reports/backend-coverage/`
- Frontend coverage: `frontend/coverage/`
- E2E test reports: `.dockerwrapper/playwright-results/`
- Combined report: `test-reports/combined-coverage/`
- Performance reports: `test-reports/performance/`
- Security reports: `test-reports/security/`

### Continuous Integration

Our CI pipeline runs tests automatically on every pull request and push to main branches:

<div align="center">

| CI Stage | Tests Run | Trigger |
|----------|-----------|---------|
| **Quick Check** | Linting, type checking | Every commit |
| **Unit Tests** | Backend and frontend unit tests | Every PR |
| **Integration Tests** | API and component integration tests | Every PR |
| **E2E Tests** | Critical user flows | Every PR to main |
| **Performance Tests** | API and page load performance | Daily on main |
| **Security Tests** | Vulnerability scanning | Weekly on main |

</div>

### Test-Driven Development

We follow test-driven development (TDD) principles:

1. **Write a failing test** that defines the expected behavior
2. **Write the minimal code** to make the test pass
3. **Refactor the code** while keeping tests passing

### Testing Best Practices

- **Test in isolation**: Each test should be independent and not rely on other tests
- **Use descriptive test names**: Test names should describe the expected behavior
- **Follow AAA pattern**: Arrange, Act, Assert
- **Mock external dependencies**: Use mocks for APIs, databases, etc.
- **Test edge cases**: Include tests for boundary conditions and error scenarios
- **Keep tests fast**: Tests should run quickly to encourage frequent testing
- **Maintain test data**: Use fixtures and factories for consistent test data

### Coverage Goals

<div align="center">

| Component | Current | Target | Timeline |
|-----------|---------|--------|----------|
| Backend | 20% | 80% | Q3 2023 |
| Frontend | 35% | 80% | Q3 2023 |
| E2E Tests | 15% | 50% | Q4 2023 |
| Security Tests | 10% | 70% | Q4 2023 |
| Performance Tests | 5% | 40% | Q1 2024 |

</div>

### Contributing Tests

When contributing to the project, please follow these testing guidelines:

- **Add tests for new features**: All new features should include tests
- **Fix tests for bug fixes**: Bug fixes should include tests that would have caught the bug
- **Maintain or improve coverage**: Changes should not decrease test coverage
- **Follow existing patterns**: Use the established testing patterns and utilities

## 🤝 Contributing

We welcome contributions to the Security Certification Explorer project! Here's how you can help:

- **Report bugs** by opening an issue
- **Suggest features** through the issue tracker
- **Submit pull requests** for bug fixes or new features
- **Improve documentation** to help others understand the project

### Contribution Process

1. **Fork the Repository**
   - Create your own fork of the project
   - Clone your fork locally

2. **Create a Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Your Changes**
   - Follow the code standards outlined above
   - Add or update tests as necessary
   - Update documentation to reflect your changes

4. **Run Tests**
   ```bash
   # Backend tests
   pytest

   # Frontend tests
   cd frontend && npm test
   ```

5. **Commit Your Changes**
   - Follow the commit guidelines
   - Use clear, descriptive commit messages

6. **Push to Your Fork**
   ```bash
   git push origin feature/your-feature-name
   ```

7. **Submit a Pull Request**
   - Create a pull request from your fork to the main repository
   - Provide a clear description of the changes
   - Link any related issues

### Pull Request Guidelines

- **One feature per pull request** - Keep PRs focused on a single feature or bug fix
- **Add tests** - Include tests for new features or bug fixes
- **Update documentation** - Update README, docstrings, or other documentation as needed
- **Follow code standards** - Ensure your code follows the project's coding standards
- **Keep it small** - Large PRs are hard to review; try to keep changes focused and minimal

### Issue Guidelines

When creating an issue, please include:

- **Clear title** - Summarize the issue concisely
- **Detailed description** - Explain the problem or feature request
- **Steps to reproduce** - For bugs, provide steps to reproduce the issue
- **Expected behavior** - Describe what you expected to happen
- **Screenshots** - If applicable, add screenshots to help explain the issue
- **Environment** - Include browser, OS, and other relevant details

Please read our [contributing guidelines](CONTRIBUTING.md) for more details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚡ Performance Optimization

The application is optimized for performance in several ways:

### Frontend Optimizations

- **Code Splitting**: Lazy loading of components to reduce initial bundle size
- **Memoization**: React.memo and useMemo to prevent unnecessary re-renders
- **Virtualization**: Virtual scrolling for long lists to improve rendering performance
- **Image Optimization**: Proper sizing, lazy loading, and WebP format for images
- **Bundle Optimization**: Tree shaking and minification for smaller bundle sizes

### Backend Optimizations

- **Database Indexing**: Strategic indexes on frequently queried fields
- **Query Optimization**: Efficient SQL queries with proper joins and filtering
- **Caching**: Redis caching for frequently accessed data
- **Async Processing**: Background tasks for heavy operations
- **Connection Pooling**: Database connection pooling for better resource utilization

### API Performance

- **Pagination**: All list endpoints support pagination to limit response size
- **Field Selection**: Support for selecting only needed fields
- **Compression**: Response compression for reduced network transfer
- **Batching**: Support for batched requests to reduce network overhead

### Monitoring

We use the following tools to monitor performance:

<div align="center">

| Tool | Purpose |
|------|---------|
| **Prometheus** | Metrics collection |
| **Grafana** | Performance dashboards |
| **Sentry** | Error tracking |
| **New Relic** | Application performance monitoring |

</div>

## 📅 Roadmap

Our development roadmap outlines the planned features and improvements:

### Q3 2023

<div align="center">

| Feature | Priority | Status |
|---------|----------|--------|
| Certification comparison tool | High | 🟡 In Progress |
| Enhanced visualization | High | 🟡 In Progress |
| Mobile app (React Native) | Medium | 🔴 Not Started |
| AI-powered study recommendations | Medium | 🟡 Planning |

</div>

### Q4 2023

<div align="center">

| Feature | Priority | Status |
|---------|----------|--------|
| Community features | High | 🔴 Not Started |
| Study progress tracking | High | 🔴 Not Started |
| Integration with learning platforms | Medium | 🔴 Not Started |
| Advanced analytics dashboard | Medium | 🔴 Not Started |

</div>

### 2024

<div align="center">

| Feature | Priority | Status |
|---------|----------|--------|
| Employer portal | High | 🔴 Not Started |
| Job matching | Medium | 🔴 Not Started |
| Mentorship platform | Medium | 🔴 Not Started |
| Enterprise features | Low | 🔴 Not Started |

</div>

## 🔄 Migration from Streamlit to React

<div align="center">

✅ **Migration Complete!**

![Migration Status](https://img.shields.io/badge/Migration-100%25_Complete-success)

</div>

The frontend has been successfully migrated from Streamlit to React while maintaining FastAPI as the backend.

### Migration Highlights

<div align="center">

| Feature | Status |
|---------|--------|
| Dashboard | ✅ Migrated |
| User Management | ✅ Migrated |
| Certification Explorer | ✅ Migrated |
| Reports | ✅ Migrated |
| Admin Interface | ✅ Migrated |

</div>

### Benefits

- **🎨 Improved UX**: Modern UI with better responsiveness
- **🧩 Enhanced Maintainability**: Component-based architecture
- **⚡ Better Performance**: Client-side rendering and optimized data loading
- **🛡️ Type Safety**: TypeScript integration throughout the codebase
- **🔄 Clean Architecture**: Better separation of concerns between frontend and backend

For more details, see the [Migration Guide](MIGRATION.md) and [Migration Milestones](.claude/milestones.md).

## 🔒 Security Considerations

As a platform dealing with cybersecurity certifications, we take security seriously. Here are some security considerations for deployment and development:

### Security Best Practices

<div align="center">

| Category | Recommendations |
|----------|-----------------|
| **Authentication** | Use strong passwords, implement MFA, rotate credentials regularly |
| **API Security** | Use HTTPS, implement rate limiting, validate all inputs |
| **Database** | Use parameterized queries, encrypt sensitive data, restrict access |
| **Frontend** | Implement CSP, use SRI for external resources, sanitize user inputs |
| **Deployment** | Use secure configurations, keep dependencies updated, scan for vulnerabilities |

</div>

### Security Features

- **🔐 Authentication**: JWT-based authentication with secure token handling
- **🛡️ Input Validation**: Comprehensive validation using Pydantic schemas
- **🔍 SQL Injection Protection**: Parameterized queries via SQLAlchemy ORM
- **🚫 CSRF Protection**: Built-in CSRF protection for API endpoints
- **⏱️ Rate Limiting**: API rate limiting to prevent abuse
- **📝 Audit Logging**: Comprehensive logging of security-relevant events

### Security Configuration

For production deployments, ensure these security settings are properly configured:

```bash
# Generate a strong secret key
openssl rand -hex 32 > .secret_key

# Set secure environment variables
export SECRET_KEY=$(cat .secret_key)
export SECURE_COOKIES=true
export CORS_ORIGINS=https://your-domain.com
export RATE_LIMIT_PER_MINUTE=60
```

### Vulnerability Reporting

If you discover a security vulnerability, please do NOT open an issue. Instead, email <EMAIL> with details about the vulnerability.

## 🔧 Troubleshooting

### Common Issues

<div align="center">

| Issue | Solution |
|-------|----------|
| **Docker containers not starting** | Check Docker logs with `docker logs container_name` |
| **Database connection errors** | Verify DATABASE_URL in .env file and database container status |
| **API endpoints returning 500** | Check API logs for detailed error messages |
| **Frontend not loading** | Verify that the React build completed successfully |
| **Authentication failures** | Check that SECRET_KEY is properly set and consistent |

</div>

### Debugging Tools

- **Docker Logs**: `docker logs container_name`
- **API Logs**: Check logs in the FastAPI container
- **Database Inspection**: `docker exec -it certrats-db psql -U postgres`
- **Frontend Debugging**: Use browser developer tools to inspect network requests and console errors

### Getting Help

If you're stuck with an issue:

1. Check the [Troubleshooting Guide](docs/troubleshooting.md) for detailed solutions
2. Search existing [GitHub Issues](https://github.com/forkrul/CertPathFinder/issues) for similar problems
3. Join our [Discord Community](https://discord.gg/example) for real-time help
4. Open a new issue with detailed information about your problem

## 🔄 Recent Updates

<div align="center">

| Category | Updates |
|----------|---------|
| **TypeScript** | Fixed SVG component types, improved D3.js typing |
| **UI Design** | Created PRD for main page, designed modular components |
| **Performance** | Optimized data loading, improved rendering efficiency |
| **Testing** | Added component tests, improved test coverage |
| **Security** | Implemented rate limiting, updated authentication flow |
| **Documentation** | Enhanced README, added deployment guides |

</div>

## 🤝 **Contributing**

<div align="center">

[![🍴 Fork Repo](https://img.shields.io/badge/🍴_Fork-Repository-blue?style=for-the-badge&logo=github)](https://github.com/forkrul/replit-CertPathFinder/fork)
[![📝 Create PR](https://img.shields.io/badge/📝_Create-Pull_Request-green?style=for-the-badge&logo=git)](https://github.com/forkrul/replit-CertPathFinder/compare)
[![🐛 Report Issue](https://img.shields.io/badge/🐛_Report-Issue-red?style=for-the-badge&logo=github)](https://github.com/forkrul/replit-CertPathFinder/issues/new)
[![💬 Join Discord](https://img.shields.io/badge/💬_Join-Discord-7289DA?style=for-the-badge&logo=discord)](https://discord.gg/certpathfinder)

[![Contributors](https://contrib.rocks/image?repo=forkrul/replit-CertPathFinder)](https://github.com/forkrul/replit-CertPathFinder/graphs/contributors)

</div>

We love contributions! Here's how to get started:

### 🔄 **Development Workflow**

1. **🍴 Fork** the repository
2. **🌿 Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **✨ Make** your changes with tests
4. **🧪 Test** your changes: `npm test && pytest`
5. **📝 Commit** with conventional commits: `git commit -m 'feat: add amazing feature'`
6. **🚀 Push** to your branch: `git push origin feature/amazing-feature`
7. **🎯 Open** a Pull Request

### 📋 **Contribution Guidelines**

- Follow our [Code of Conduct](CODE_OF_CONDUCT.md)
- Write tests for new features
- Update documentation
- Use conventional commit messages
- Ensure CI passes

---

## 📄 **License**

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

---

## 🙏 **Acknowledgments**

- **Security Community** for certification data and insights
- **Open Source Contributors** for amazing tools and libraries
- **Beta Testers** for valuable feedback and bug reports
- **Cybersecurity Professionals** for domain expertise

---

<div align="center">

**⭐ Star this repo if you find it helpful!**

[![⭐ Star Repository](https://img.shields.io/badge/⭐_Star-Repository-yellow?style=for-the-badge&logo=github)](https://github.com/forkrul/replit-CertPathFinder/stargazers)
[![👀 Watch Updates](https://img.shields.io/badge/👀_Watch-Updates-blue?style=for-the-badge&logo=github)](https://github.com/forkrul/replit-CertPathFinder/watchers)
[![🔔 Get Notified](https://img.shields.io/badge/🔔_Get-Notified-green?style=for-the-badge&logo=bell)](https://github.com/forkrul/replit-CertPathFinder/subscription)

[![🐛 Report Issues](https://img.shields.io/badge/🐛_Report-Issues-red?style=flat-square&logo=github)](https://github.com/forkrul/replit-CertPathFinder/issues)
[![💬 Discussions](https://img.shields.io/badge/💬_Join-Discussions-blue?style=flat-square&logo=github)](https://github.com/forkrul/replit-CertPathFinder/discussions)
[![📧 Contact](https://img.shields.io/badge/📧_Contact-Support-green?style=flat-square&logo=mail)](mailto:<EMAIL>)
[![🌐 Website](https://img.shields.io/badge/🌐_Visit-Website-purple?style=flat-square&logo=web)](https://certpathfinder.com)

**Made with ❤️ by the CertPathFinder Team**

</div>
