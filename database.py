import os
import logging
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import OperationalError
from sqlalchemy.pool import QueuePool, StaticPool

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get database URL from environment variables (set by Replit)
DATABASE_URL = os.environ.get('DATABASE_URL')

if not DATABASE_URL:
    # Use SQLite for local development
    DATABASE_URL = "sqlite:///./certpathfinder.db"
    logger.info("Using SQLite database for local development")

# Create engine with optimized settings based on database type
if DATABASE_URL.startswith('sqlite'):
    # SQLite configuration with optimizations
    engine = create_engine(
        DATABASE_URL,
        connect_args={
            "check_same_thread": False,  # Allow SQLite to be used with multiple threads
            "timeout": 20,               # Connection timeout
        },
        poolclass=StaticPool,            # Use static pool for SQLite
        pool_pre_ping=True,              # Enable connection health checks
        echo=False,                      # Disable SQL logging in production
        future=True                      # Use SQLAlchemy 2.0 style
    )

    # SQLite-specific optimizations
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        # Enable WAL mode for better concurrency
        cursor.execute("PRAGMA journal_mode=WAL")
        # Increase cache size (negative value = KB)
        cursor.execute("PRAGMA cache_size=-64000")  # 64MB cache
        # Enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys=ON")
        # Optimize synchronous mode
        cursor.execute("PRAGMA synchronous=NORMAL")
        # Set temp store to memory
        cursor.execute("PRAGMA temp_store=MEMORY")
        cursor.close()

else:
    # PostgreSQL configuration with connection pooling
    engine = create_engine(
        DATABASE_URL,
        poolclass=QueuePool,
        pool_size=20,                    # Number of connections to maintain
        max_overflow=30,                 # Additional connections allowed
        pool_timeout=30,                 # Timeout for getting connection
        pool_recycle=3600,              # Recycle connections every hour
        pool_pre_ping=True,             # Enable connection health checks
        echo=False,                     # Disable SQL logging in production
        future=True,                    # Use SQLAlchemy 2.0 style
        connect_args={
            "sslmode": "prefer",        # Prefer SSL but don't require
            "connect_timeout": 10,      # Connection timeout
            "application_name": "CertPathFinder"
        }
    )

# Create base class for declarative models
Base = declarative_base()

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_db():
    """Initialize database tables"""
    try:
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except OperationalError as e:
        logger.error(f"Failed to create database tables: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during database initialization: {e}")
        raise

def get_db():
    """Get database session with automatic closing"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        raise
    finally:
        db.close()