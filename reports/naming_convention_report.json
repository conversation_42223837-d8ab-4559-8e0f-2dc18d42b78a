{"issues": [{"type": "typescript_interface_naming", "file": "frontend/src/services/api.ts", "line": 2, "current": "definitions", "expected": "Definitions", "message": "Interface/Type 'definitions' should use PascalCase"}, {"type": "typescript_interface_naming", "file": "frontend/src/types/ai-progress.ts", "line": 2, "current": "definitions", "expected": "Definitions", "message": "Interface/Type 'definitions' should use PascalCase"}, {"type": "typescript_interface_naming", "file": "frontend/src/types/api.ts", "line": 2, "current": "definitions", "expected": "Definitions", "message": "Interface/Type 'definitions' should use PascalCase"}, {"type": "typescript_interface_naming", "file": "frontend/src/types/certification.ts", "line": 2, "current": "definitions", "expected": "Definitions", "message": "Interface/Type 'definitions' should use PascalCase"}, {"type": "typescript_interface_naming", "file": "frontend/src/types/user.ts", "line": 2, "current": "definitions", "expected": "Definitions", "message": "Interface/Type 'definitions' should use PascalCase"}, {"type": "typescript_interface_naming", "file": "tests/e2e/cost-calculator.e2e.ts", "line": 283, "current": "await", "expected": "Await", "message": "Interface/Type 'await' should use PascalCase"}, {"type": "python_file_naming", "file": "playwright.config.py", "line": 0, "current": "playwright.config", "expected": "playwright.config", "message": "Python file 'playwright.config' should use snake_case"}], "fixes": [], "summary": {"issues_found": 7, "fixes_applied": 0, "summary": {"typescript_interface_naming": 6, "python_file_naming": 1}}}