"""Study session models for tracking user study time and progress.

This module provides models for managing study sessions, including:
- StudySession: Individual study sessions with timing and progress tracking
- StudyGoal: User-defined study goals and targets
- StudyStreak: Tracking consecutive study days and achievements
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from .mixins import TimestampMixin
from database import Base
import logging

logger = logging.getLogger(__name__)


class StudyTimerSession(Base, TimestampMixin):
    """Model for tracking individual study sessions.
    
    Tracks detailed information about each study session including:
    - Duration and timing
    - Certification being studied
    - Progress made during session
    - Session quality metrics
    """
    __tablename__ = 'study_timer_sessions'

    id = Column(Integer, primary_key=True)
    user_id = Column(String, nullable=False, index=True)
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=True)
    learning_path_item_id = Column(Inte<PERSON>, ForeignKey('learning_path_items.id'), nullable=True)
    
    # Session timing
    start_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    end_time = Column(DateTime, nullable=True)
    planned_duration_minutes = Column(Integer, nullable=False, default=60)
    actual_duration_minutes = Column(Integer, nullable=True)
    
    # Session content and progress
    session_type = Column(String(50), nullable=False, default='study')  # 'study', 'practice', 'review'
    topic = Column(String(200), nullable=True)
    notes = Column(Text, nullable=True)
    progress_percentage = Column(Float, nullable=True)  # Progress made during this session
    
    # Session quality metrics
    focus_rating = Column(Integer, nullable=True)  # 1-5 scale
    difficulty_rating = Column(Integer, nullable=True)  # 1-5 scale
    satisfaction_rating = Column(Integer, nullable=True)  # 1-5 scale
    
    # Session status
    status = Column(String(20), nullable=False, default='planned')  # 'planned', 'active', 'paused', 'completed', 'cancelled'
    is_break = Column(Boolean, nullable=False, default=False)
    break_duration_minutes = Column(Integer, nullable=True)
    
    # Relationships
    certification = relationship("Certification")
    # learning_path_item = relationship("LearningPathItem", back_populates="study_sessions")

    def __repr__(self) -> str:
        """String representation of the study session."""
        return f"<StudySession(id={self.id}, user_id='{self.user_id}', duration={self.actual_duration_minutes}min)>"

    @property
    def is_active(self) -> bool:
        """Check if the session is currently active."""
        return self.status == 'active' and self.end_time is None

    @property
    def is_completed(self) -> bool:
        """Check if the session is completed."""
        return self.status == 'completed' and self.end_time is not None

    def start_session(self) -> None:
        """Start the study session."""
        if self.status != 'planned':
            raise ValueError(f"Cannot start session with status: {self.status}")
        
        self.status = 'active'
        self.start_time = datetime.utcnow()
        logger.info(f"Started study session {self.id} for user {self.user_id}")

    def pause_session(self) -> None:
        """Pause the study session."""
        if self.status != 'active':
            raise ValueError(f"Cannot pause session with status: {self.status}")
        
        self.status = 'paused'
        logger.info(f"Paused study session {self.id}")

    def resume_session(self) -> None:
        """Resume a paused study session."""
        if self.status != 'paused':
            raise ValueError(f"Cannot resume session with status: {self.status}")
        
        self.status = 'active'
        logger.info(f"Resumed study session {self.id}")

    def complete_session(self, notes: Optional[str] = None, 
                        focus_rating: Optional[int] = None,
                        difficulty_rating: Optional[int] = None,
                        satisfaction_rating: Optional[int] = None) -> None:
        """Complete the study session with optional feedback."""
        if self.status not in ['active', 'paused']:
            raise ValueError(f"Cannot complete session with status: {self.status}")
        
        self.end_time = datetime.utcnow()
        self.status = 'completed'
        
        # Calculate actual duration
        if self.start_time:
            duration = self.end_time - self.start_time
            self.actual_duration_minutes = int(duration.total_seconds() / 60)
        
        # Update optional feedback
        if notes:
            self.notes = notes
        if focus_rating:
            self.focus_rating = focus_rating
        if difficulty_rating:
            self.difficulty_rating = difficulty_rating
        if satisfaction_rating:
            self.satisfaction_rating = satisfaction_rating
        
        logger.info(f"Completed study session {self.id} with duration {self.actual_duration_minutes} minutes")

    def cancel_session(self, reason: Optional[str] = None) -> None:
        """Cancel the study session."""
        self.status = 'cancelled'
        self.end_time = datetime.utcnow()
        if reason:
            self.notes = f"Cancelled: {reason}"
        
        logger.info(f"Cancelled study session {self.id}")

    def to_dict(self) -> Dict[str, Any]:
        """Convert the study session to a dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'certification_id': self.certification_id,
            'learning_path_item_id': self.learning_path_item_id,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'planned_duration_minutes': self.planned_duration_minutes,
            'actual_duration_minutes': self.actual_duration_minutes,
            'session_type': self.session_type,
            'topic': self.topic,
            'notes': self.notes,
            'progress_percentage': self.progress_percentage,
            'focus_rating': self.focus_rating,
            'difficulty_rating': self.difficulty_rating,
            'satisfaction_rating': self.satisfaction_rating,
            'status': self.status,
            'is_break': self.is_break,
            'break_duration_minutes': self.break_duration_minutes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class StudyGoal(Base, TimestampMixin):
    """Model for tracking user study goals and targets.
    
    Allows users to set and track various types of study goals:
    - Daily/weekly/monthly time targets
    - Certification completion deadlines
    - Progress milestones
    """
    __tablename__ = 'study_goals'

    id = Column(Integer, primary_key=True)
    user_id = Column(String, nullable=False, index=True)
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=True)
    
    # Goal definition
    goal_type = Column(String(50), nullable=False)  # 'daily_time', 'weekly_time', 'monthly_time', 'certification_deadline', 'progress_milestone'
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # Goal targets
    target_value = Column(Float, nullable=False)  # Minutes for time goals, percentage for progress goals
    target_date = Column(DateTime, nullable=True)
    
    # Goal tracking
    current_value = Column(Float, nullable=False, default=0.0)
    is_achieved = Column(Boolean, nullable=False, default=False)
    achievement_date = Column(DateTime, nullable=True)
    
    # Goal settings
    is_active = Column(Boolean, nullable=False, default=True)
    reminder_enabled = Column(Boolean, nullable=False, default=True)
    
    # Relationships
    certification = relationship("Certification")

    def __repr__(self) -> str:
        """String representation of the study goal."""
        return f"<StudyGoal(id={self.id}, user_id='{self.user_id}', type='{self.goal_type}')>"

    @property
    def progress_percentage(self) -> float:
        """Calculate the progress percentage towards the goal."""
        if self.target_value == 0:
            return 0.0
        return min(100.0, (self.current_value / self.target_value) * 100)

    @property
    def is_overdue(self) -> bool:
        """Check if the goal is overdue."""
        if not self.target_date or self.is_achieved:
            return False
        return datetime.utcnow() > self.target_date

    def update_progress(self, value: float) -> None:
        """Update the current progress value."""
        self.current_value = value
        
        # Check if goal is achieved
        if self.current_value >= self.target_value and not self.is_achieved:
            self.is_achieved = True
            self.achievement_date = datetime.utcnow()
            logger.info(f"Goal {self.id} achieved by user {self.user_id}")

    def to_dict(self) -> Dict[str, Any]:
        """Convert the study goal to a dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'certification_id': self.certification_id,
            'goal_type': self.goal_type,
            'title': self.title,
            'description': self.description,
            'target_value': self.target_value,
            'target_date': self.target_date.isoformat() if self.target_date else None,
            'current_value': self.current_value,
            'progress_percentage': self.progress_percentage,
            'is_achieved': self.is_achieved,
            'achievement_date': self.achievement_date.isoformat() if self.achievement_date else None,
            'is_active': self.is_active,
            'reminder_enabled': self.reminder_enabled,
            'is_overdue': self.is_overdue,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class StudyStreak(Base, TimestampMixin):
    """Model for tracking study streaks and achievements.
    
    Tracks consecutive study days and various achievement milestones
    to gamify the study experience and encourage consistency.
    """
    __tablename__ = 'study_streaks'

    id = Column(Integer, primary_key=True)
    user_id = Column(String, nullable=False, index=True, unique=True)
    
    # Current streak
    current_streak_days = Column(Integer, nullable=False, default=0)
    current_streak_start = Column(DateTime, nullable=True)
    last_study_date = Column(DateTime, nullable=True)
    
    # Best streak
    longest_streak_days = Column(Integer, nullable=False, default=0)
    longest_streak_start = Column(DateTime, nullable=True)
    longest_streak_end = Column(DateTime, nullable=True)
    
    # Total statistics
    total_study_days = Column(Integer, nullable=False, default=0)
    total_study_minutes = Column(Integer, nullable=False, default=0)
    total_sessions = Column(Integer, nullable=False, default=0)
    
    # Achievement tracking
    achievements = Column(Text, nullable=True)  # JSON string of achieved milestones

    def __repr__(self) -> str:
        """String representation of the study streak."""
        return f"<StudyStreak(user_id='{self.user_id}', current_streak={self.current_streak_days})>"

    def update_streak(self, study_date: datetime) -> bool:
        """Update the streak based on a new study session.
        
        Args:
            study_date: Date of the study session
            
        Returns:
            True if streak was updated, False if no change
        """
        study_date_only = study_date.date()
        
        # If this is the first study session
        if not self.last_study_date:
            self.current_streak_days = 1
            self.current_streak_start = study_date
            self.last_study_date = study_date
            self.total_study_days = 1
            return True
        
        last_study_date_only = self.last_study_date.date()
        
        # If studying on the same day, no streak change
        if study_date_only == last_study_date_only:
            return False
        
        # If studying the next day, continue streak
        if study_date_only == last_study_date_only + timedelta(days=1):
            self.current_streak_days += 1
            self.last_study_date = study_date
            self.total_study_days += 1
            
            # Update longest streak if current is longer
            if self.current_streak_days > self.longest_streak_days:
                self.longest_streak_days = self.current_streak_days
                self.longest_streak_start = self.current_streak_start
                self.longest_streak_end = None  # Still ongoing
            
            return True
        
        # If there's a gap, reset streak
        if study_date_only > last_study_date_only + timedelta(days=1):
            # End current streak
            if self.current_streak_days > 0:
                if self.current_streak_days > self.longest_streak_days:
                    self.longest_streak_days = self.current_streak_days
                    self.longest_streak_start = self.current_streak_start
                    self.longest_streak_end = self.last_study_date
            
            # Start new streak
            self.current_streak_days = 1
            self.current_streak_start = study_date
            self.last_study_date = study_date
            self.total_study_days += 1
            
            return True
        
        return False

    def add_study_session(self, duration_minutes: int, study_date: datetime) -> None:
        """Add a study session to the streak tracking.
        
        Args:
            duration_minutes: Duration of the study session
            study_date: Date of the study session
        """
        self.update_streak(study_date)
        self.total_study_minutes += duration_minutes
        self.total_sessions += 1

    def to_dict(self) -> Dict[str, Any]:
        """Convert the study streak to a dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'current_streak_days': self.current_streak_days,
            'current_streak_start': self.current_streak_start.isoformat() if self.current_streak_start else None,
            'last_study_date': self.last_study_date.isoformat() if self.last_study_date else None,
            'longest_streak_days': self.longest_streak_days,
            'longest_streak_start': self.longest_streak_start.isoformat() if self.longest_streak_start else None,
            'longest_streak_end': self.longest_streak_end.isoformat() if self.longest_streak_end else None,
            'total_study_days': self.total_study_days,
            'total_study_minutes': self.total_study_minutes,
            'total_sessions': self.total_sessions,
            'achievements': self.achievements,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
