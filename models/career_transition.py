"""Career transition models for pathfinding and budget-aware planning.

This module provides comprehensive models for career transition planning,
including budget constraints, timeline requirements, and difficulty preferences.
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from database import Base
from models.mixins import TimestampMixin


class CareerRole(Base, TimestampMixin):
    """Represents a career role with requirements and progression paths."""
    
    __tablename__ = 'career_roles'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False, unique=True)
    description = Column(Text)
    domain = Column(String(100), nullable=False)
    level = Column(String(50), nullable=False)  # Entry, Mid, Senior, Executive
    
    # Experience requirements
    min_years_experience = Column(Integer, default=0)
    max_years_experience = Column(Integer, nullable=True)
    
    # Salary information
    salary_min = Column(Float, nullable=True)
    salary_max = Column(Float, nullable=True)
    salary_currency = Column(String(3), default='USD')
    
    # Skills and requirements
    required_skills = Column(JSON, default=list)  # List of required skills
    preferred_skills = Column(JSON, default=list)  # List of preferred skills
    required_certifications = Column(JSON, default=list)  # List of certification names
    preferred_certifications = Column(JSON, default=list)  # List of certification names
    
    # Career progression
    common_previous_roles = Column(JSON, default=list)  # List of role IDs
    common_next_roles = Column(JSON, default=list)  # List of role IDs
    
    # Metadata
    is_active = Column(Boolean, default=True)
    market_demand = Column(String(20), default='Medium')  # Low, Medium, High, Very High
    growth_outlook = Column(String(20), default='Stable')  # Declining, Stable, Growing, Booming
    
    # Relationships
    transition_paths_from = relationship(
        "CareerTransitionPath", 
        foreign_keys="CareerTransitionPath.source_role_id",
        back_populates="source_role"
    )
    transition_paths_to = relationship(
        "CareerTransitionPath", 
        foreign_keys="CareerTransitionPath.target_role_id",
        back_populates="target_role"
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert career role to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'domain': self.domain,
            'level': self.level,
            'min_years_experience': self.min_years_experience,
            'max_years_experience': self.max_years_experience,
            'salary_range': {
                'min': self.salary_min,
                'max': self.salary_max,
                'currency': self.salary_currency
            },
            'required_skills': self.required_skills or [],
            'preferred_skills': self.preferred_skills or [],
            'required_certifications': self.required_certifications or [],
            'preferred_certifications': self.preferred_certifications or [],
            'common_previous_roles': self.common_previous_roles or [],
            'common_next_roles': self.common_next_roles or [],
            'market_demand': self.market_demand,
            'growth_outlook': self.growth_outlook,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CareerTransitionPath(Base, TimestampMixin):
    """Represents a specific transition path between two career roles."""
    
    __tablename__ = 'career_transition_paths'
    
    id = Column(Integer, primary_key=True)
    source_role_id = Column(Integer, ForeignKey('career_roles.id'), nullable=False)
    target_role_id = Column(Integer, ForeignKey('career_roles.id'), nullable=False)
    
    # Path characteristics
    name = Column(String(200), nullable=False)
    description = Column(Text)
    difficulty_level = Column(String(20), default='Medium')  # Easy, Medium, Hard, Expert
    
    # Time requirements
    estimated_duration_months = Column(Integer, nullable=False)
    min_duration_months = Column(Integer, nullable=True)
    max_duration_months = Column(Integer, nullable=True)
    
    # Cost estimates
    estimated_cost_min = Column(Float, default=0.0)
    estimated_cost_max = Column(Float, default=0.0)
    cost_currency = Column(String(3), default='USD')
    
    # Requirements
    required_certifications = Column(JSON, default=list)  # List of certification IDs
    recommended_certifications = Column(JSON, default=list)  # List of certification IDs
    prerequisite_experience = Column(JSON, default=dict)  # Experience requirements
    
    # Success metrics
    success_rate = Column(Float, default=0.0)  # 0.0 to 1.0
    average_salary_increase = Column(Float, default=0.0)  # Percentage
    
    # Path metadata
    is_active = Column(Boolean, default=True)
    popularity_score = Column(Float, default=0.0)  # Based on usage
    
    # Relationships
    source_role = relationship(
        "CareerRole", 
        foreign_keys=[source_role_id],
        back_populates="transition_paths_from"
    )
    target_role = relationship(
        "CareerRole", 
        foreign_keys=[target_role_id],
        back_populates="transition_paths_to"
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert transition path to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'source_role_id': self.source_role_id,
            'target_role_id': self.target_role_id,
            'difficulty_level': self.difficulty_level,
            'duration': {
                'estimated_months': self.estimated_duration_months,
                'min_months': self.min_duration_months,
                'max_months': self.max_duration_months
            },
            'cost_estimate': {
                'min': self.estimated_cost_min,
                'max': self.estimated_cost_max,
                'currency': self.cost_currency
            },
            'required_certifications': self.required_certifications or [],
            'recommended_certifications': self.recommended_certifications or [],
            'prerequisite_experience': self.prerequisite_experience or {},
            'success_rate': self.success_rate,
            'average_salary_increase': self.average_salary_increase,
            'popularity_score': self.popularity_score,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CareerTransitionPlan(Base, TimestampMixin):
    """User-specific career transition plan with budget and timeline constraints."""
    
    __tablename__ = 'career_transition_plans'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), nullable=False)  # User identifier
    
    # Plan details
    name = Column(String(200), nullable=False)
    description = Column(Text)
    
    # Current and target roles
    current_role_id = Column(Integer, ForeignKey('career_roles.id'), nullable=True)
    target_role_id = Column(Integer, ForeignKey('career_roles.id'), nullable=False)
    
    # User constraints
    budget_min = Column(Float, default=0.0)
    budget_max = Column(Float, nullable=True)
    budget_currency = Column(String(3), default='USD')
    
    timeline_months = Column(Integer, nullable=True)  # Desired completion time
    max_timeline_months = Column(Integer, nullable=True)  # Maximum acceptable time
    
    # Preferences
    difficulty_preference = Column(String(20), default='Medium')  # Easy, Medium, Hard
    learning_style = Column(String(50), default='Mixed')  # Visual, Reading, Hands-on, Mixed
    study_hours_per_week = Column(Integer, default=10)
    
    # Current status
    status = Column(String(20), default='planning')  # planning, active, completed, paused
    progress_percentage = Column(Float, default=0.0)
    
    # Generated paths
    selected_path_id = Column(Integer, ForeignKey('career_transition_paths.id'), nullable=True)
    alternative_paths = Column(JSON, default=list)  # List of path IDs
    
    # Plan metadata
    is_active = Column(Boolean, default=True)
    
    # Relationships
    current_role = relationship(
        "CareerRole", 
        foreign_keys=[current_role_id]
    )
    target_role = relationship(
        "CareerRole", 
        foreign_keys=[target_role_id]
    )
    selected_path = relationship(
        "CareerTransitionPath",
        foreign_keys=[selected_path_id]
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert transition plan to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'description': self.description,
            'current_role_id': self.current_role_id,
            'target_role_id': self.target_role_id,
            'budget': {
                'min': self.budget_min,
                'max': self.budget_max,
                'currency': self.budget_currency
            },
            'timeline': {
                'desired_months': self.timeline_months,
                'max_months': self.max_timeline_months
            },
            'preferences': {
                'difficulty': self.difficulty_preference,
                'learning_style': self.learning_style,
                'study_hours_per_week': self.study_hours_per_week
            },
            'status': self.status,
            'progress_percentage': self.progress_percentage,
            'selected_path_id': self.selected_path_id,
            'alternative_paths': self.alternative_paths or [],
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CareerTransitionStep(Base, TimestampMixin):
    """Individual step in a career transition plan."""
    
    __tablename__ = 'career_transition_steps'
    
    id = Column(Integer, primary_key=True)
    plan_id = Column(Integer, ForeignKey('career_transition_plans.id'), nullable=False)
    
    # Step details
    name = Column(String(200), nullable=False)
    description = Column(Text)
    step_type = Column(String(50), nullable=False)  # certification, experience, skill, project
    sequence = Column(Integer, nullable=False)  # Order in the plan
    
    # Associated resources
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=True)
    
    # Time and cost
    estimated_duration_weeks = Column(Integer, nullable=True)
    estimated_cost = Column(Float, default=0.0)
    cost_currency = Column(String(3), default='USD')
    
    # Progress tracking
    status = Column(String(20), default='pending')  # pending, in_progress, completed, skipped
    progress_percentage = Column(Float, default=0.0)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Dependencies
    prerequisite_steps = Column(JSON, default=list)  # List of step IDs
    
    # Relationships
    plan = relationship("CareerTransitionPlan")
    certification = relationship("Certification")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert transition step to dictionary."""
        return {
            'id': self.id,
            'plan_id': self.plan_id,
            'name': self.name,
            'description': self.description,
            'step_type': self.step_type,
            'sequence': self.sequence,
            'certification_id': self.certification_id,
            'estimated_duration_weeks': self.estimated_duration_weeks,
            'estimated_cost': self.estimated_cost,
            'cost_currency': self.cost_currency,
            'status': self.status,
            'progress_percentage': self.progress_percentage,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'prerequisite_steps': self.prerequisite_steps or [],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
