"""Progress tracking and learning analytics models.

This module provides comprehensive models for tracking user learning progress,
study sessions, practice test results, and generating learning analytics.
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from database import Base
from models.mixins import TimestampMixin


class ProgressTracking(Base, TimestampMixin):
    """Tracks user progress on certifications and learning paths."""

    __tablename__ = 'progress_tracking'

    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), nullable=False, index=True)

    # What is being tracked
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=True)
    learning_path_id = Column(Integer, ForeignKey('learning_paths.id'), nullable=True)

    # Progress information
    progress_percentage = Column(Float, default=0.0)
    status = Column(String(50), default='not_started')  # not_started, in_progress, completed, paused

    # Time tracking
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    last_activity_at = Column(DateTime, nullable=True)

    # Study metrics
    total_study_hours = Column(Float, default=0.0)
    estimated_completion_date = Column(DateTime, nullable=True)

    # Performance metrics
    confidence_level = Column(Integer, nullable=True)  # 1-5 scale
    mastery_level = Column(String(20), default='beginner')  # beginner, intermediate, advanced, expert

    # Additional metadata
    notes = Column(Text, nullable=True)
    tags = Column(JSON, default=list)

    # Relationships
    certification = relationship("Certification")
    learning_path = relationship("LearningPath")

    def to_dict(self) -> Dict[str, Any]:
        """Convert progress tracking to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'certification_id': self.certification_id,
            'learning_path_id': self.learning_path_id,
            'progress_percentage': self.progress_percentage,
            'status': self.status,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'last_activity_at': self.last_activity_at.isoformat() if self.last_activity_at else None,
            'total_study_hours': self.total_study_hours,
            'estimated_completion_date': self.estimated_completion_date.isoformat() if self.estimated_completion_date else None,
            'confidence_level': self.confidence_level,
            'mastery_level': self.mastery_level,
            'notes': self.notes,
            'tags': self.tags or [],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class StudySession(Base, TimestampMixin):
    """Represents a single study session for tracking learning progress."""
    
    __tablename__ = 'study_sessions'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), nullable=False, index=True)
    
    # Session details
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=True)
    learning_path_id = Column(Integer, ForeignKey('learning_paths.id'), nullable=True)
    transition_plan_id = Column(Integer, ForeignKey('career_transition_plans.id'), nullable=True)
    
    # Study session information
    session_type = Column(String(50), nullable=False)  # reading, practice_test, video, hands_on, review
    topic = Column(String(200), nullable=True)
    subtopic = Column(String(200), nullable=True)
    
    # Time tracking
    started_at = Column(DateTime, nullable=False)
    ended_at = Column(DateTime, nullable=True)
    duration_minutes = Column(Integer, nullable=True)
    planned_duration_minutes = Column(Integer, nullable=True)
    
    # Progress and performance
    progress_before = Column(Float, default=0.0)  # Progress percentage before session
    progress_after = Column(Float, default=0.0)   # Progress percentage after session
    confidence_level = Column(Integer, nullable=True)  # 1-5 scale
    difficulty_rating = Column(Integer, nullable=True)  # 1-5 scale
    
    # Content and notes
    materials_used = Column(JSON, default=list)  # List of study materials/resources
    notes = Column(Text, nullable=True)
    tags = Column(JSON, default=list)  # User-defined tags
    
    # Session quality metrics
    focus_rating = Column(Integer, nullable=True)  # 1-5 scale
    effectiveness_rating = Column(Integer, nullable=True)  # 1-5 scale
    interruptions_count = Column(Integer, default=0)
    
    # Relationships
    certification = relationship("Certification")
    learning_path = relationship("LearningPath")
    transition_plan = relationship("CareerTransitionPlan")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert study session to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'certification_id': self.certification_id,
            'learning_path_id': self.learning_path_id,
            'transition_plan_id': self.transition_plan_id,
            'session_type': self.session_type,
            'topic': self.topic,
            'subtopic': self.subtopic,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'duration_minutes': self.duration_minutes,
            'planned_duration_minutes': self.planned_duration_minutes,
            'progress_before': self.progress_before,
            'progress_after': self.progress_after,
            'confidence_level': self.confidence_level,
            'difficulty_rating': self.difficulty_rating,
            'materials_used': self.materials_used or [],
            'notes': self.notes,
            'tags': self.tags or [],
            'focus_rating': self.focus_rating,
            'effectiveness_rating': self.effectiveness_rating,
            'interruptions_count': self.interruptions_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class PracticeTestResult(Base, TimestampMixin):
    """Represents results from practice tests and assessments."""
    
    __tablename__ = 'practice_test_results'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), nullable=False, index=True)
    
    # Test details
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=False)
    test_name = Column(String(200), nullable=False)
    test_type = Column(String(50), nullable=False)  # practice_exam, quiz, assessment, mock_exam
    test_provider = Column(String(100), nullable=True)
    
    # Test configuration
    total_questions = Column(Integer, nullable=False)
    time_limit_minutes = Column(Integer, nullable=True)
    passing_score = Column(Float, nullable=True)
    
    # Results
    score = Column(Float, nullable=False)  # Raw score
    percentage = Column(Float, nullable=False)  # Percentage score
    passed = Column(Boolean, nullable=False)
    time_taken_minutes = Column(Integer, nullable=True)
    
    # Detailed performance
    correct_answers = Column(Integer, nullable=False)
    incorrect_answers = Column(Integer, nullable=False)
    unanswered_questions = Column(Integer, default=0)
    
    # Domain/topic breakdown
    domain_scores = Column(JSON, default=dict)  # Domain -> score mapping
    topic_scores = Column(JSON, default=dict)   # Topic -> score mapping
    weak_areas = Column(JSON, default=list)     # List of weak topics/domains
    strong_areas = Column(JSON, default=list)   # List of strong topics/domains
    
    # Question analysis
    question_details = Column(JSON, default=list)  # Detailed question results
    flagged_questions = Column(JSON, default=list)  # Questions marked for review
    
    # Test experience
    difficulty_rating = Column(Integer, nullable=True)  # 1-5 scale
    confidence_level = Column(Integer, nullable=True)   # 1-5 scale
    test_environment = Column(String(50), nullable=True)  # online, offline, proctored
    
    # Relationships
    certification = relationship("Certification")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert practice test result to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'certification_id': self.certification_id,
            'test_name': self.test_name,
            'test_type': self.test_type,
            'test_provider': self.test_provider,
            'total_questions': self.total_questions,
            'time_limit_minutes': self.time_limit_minutes,
            'passing_score': self.passing_score,
            'score': self.score,
            'percentage': self.percentage,
            'passed': self.passed,
            'time_taken_minutes': self.time_taken_minutes,
            'correct_answers': self.correct_answers,
            'incorrect_answers': self.incorrect_answers,
            'unanswered_questions': self.unanswered_questions,
            'domain_scores': self.domain_scores or {},
            'topic_scores': self.topic_scores or {},
            'weak_areas': self.weak_areas or [],
            'strong_areas': self.strong_areas or [],
            'question_details': self.question_details or [],
            'flagged_questions': self.flagged_questions or [],
            'difficulty_rating': self.difficulty_rating,
            'confidence_level': self.confidence_level,
            'test_environment': self.test_environment,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class LearningGoal(Base, TimestampMixin):
    """Represents user-defined learning goals and targets."""
    
    __tablename__ = 'learning_goals'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), nullable=False, index=True)
    
    # Goal details
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    goal_type = Column(String(50), nullable=False)  # certification, skill, study_time, practice_score
    
    # Target information
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=True)
    target_value = Column(Float, nullable=True)  # Target score, hours, etc.
    target_unit = Column(String(20), nullable=True)  # hours, percentage, score
    target_date = Column(DateTime, nullable=True)
    
    # Progress tracking
    current_value = Column(Float, default=0.0)
    progress_percentage = Column(Float, default=0.0)
    status = Column(String(20), default='active')  # active, completed, paused, abandoned
    
    # Goal configuration
    priority = Column(String(20), default='medium')  # low, medium, high, critical
    is_public = Column(Boolean, default=False)  # Share with community
    reminder_frequency = Column(String(20), nullable=True)  # daily, weekly, monthly
    
    # Milestones and rewards
    milestones = Column(JSON, default=list)  # List of milestone definitions
    rewards = Column(JSON, default=list)     # List of rewards for completion
    
    # Relationships
    certification = relationship("Certification")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert learning goal to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'description': self.description,
            'goal_type': self.goal_type,
            'certification_id': self.certification_id,
            'target_value': self.target_value,
            'target_unit': self.target_unit,
            'target_date': self.target_date.isoformat() if self.target_date else None,
            'current_value': self.current_value,
            'progress_percentage': self.progress_percentage,
            'status': self.status,
            'priority': self.priority,
            'is_public': self.is_public,
            'reminder_frequency': self.reminder_frequency,
            'milestones': self.milestones or [],
            'rewards': self.rewards or [],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Achievement(Base, TimestampMixin):
    """Represents user achievements and badges."""
    
    __tablename__ = 'achievements'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), nullable=False, index=True)
    
    # Achievement details
    achievement_type = Column(String(50), nullable=False)  # badge, milestone, streak, certification
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    icon = Column(String(100), nullable=True)
    
    # Achievement criteria
    criteria_type = Column(String(50), nullable=False)  # study_hours, test_score, streak_days, certification_earned
    criteria_value = Column(Float, nullable=True)
    criteria_unit = Column(String(20), nullable=True)
    
    # Achievement status
    earned_at = Column(DateTime, nullable=True)
    is_earned = Column(Boolean, default=False)
    progress_percentage = Column(Float, default=0.0)
    
    # Metadata
    rarity = Column(String(20), default='common')  # common, uncommon, rare, epic, legendary
    points = Column(Integer, default=0)  # Achievement points/score
    category = Column(String(50), nullable=True)  # study, testing, consistency, mastery
    
    # Related entities
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=True)
    learning_goal_id = Column(Integer, ForeignKey('learning_goals.id'), nullable=True)
    
    # Relationships
    certification = relationship("Certification")
    learning_goal = relationship("LearningGoal")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert achievement to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'achievement_type': self.achievement_type,
            'title': self.title,
            'description': self.description,
            'icon': self.icon,
            'criteria_type': self.criteria_type,
            'criteria_value': self.criteria_value,
            'criteria_unit': self.criteria_unit,
            'earned_at': self.earned_at.isoformat() if self.earned_at else None,
            'is_earned': self.is_earned,
            'progress_percentage': self.progress_percentage,
            'rarity': self.rarity,
            'points': self.points,
            'category': self.category,
            'certification_id': self.certification_id,
            'learning_goal_id': self.learning_goal_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class LearningAnalytics(Base, TimestampMixin):
    """Aggregated learning analytics and insights for users."""
    
    __tablename__ = 'learning_analytics'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), nullable=False, index=True)
    
    # Time period for analytics
    period_type = Column(String(20), nullable=False)  # daily, weekly, monthly, quarterly, yearly
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # Study time analytics
    total_study_minutes = Column(Integer, default=0)
    average_session_minutes = Column(Float, default=0.0)
    study_sessions_count = Column(Integer, default=0)
    study_streak_days = Column(Integer, default=0)
    
    # Performance analytics
    practice_tests_taken = Column(Integer, default=0)
    average_test_score = Column(Float, default=0.0)
    score_improvement = Column(Float, default=0.0)  # Improvement over period
    weak_areas_count = Column(Integer, default=0)
    
    # Progress analytics
    certifications_completed = Column(Integer, default=0)
    goals_achieved = Column(Integer, default=0)
    milestones_reached = Column(Integer, default=0)
    overall_progress = Column(Float, default=0.0)
    
    # Efficiency metrics
    study_efficiency_score = Column(Float, default=0.0)  # Progress per hour studied
    focus_score = Column(Float, default=0.0)  # Average focus rating
    consistency_score = Column(Float, default=0.0)  # Study consistency
    
    # Detailed breakdowns
    topic_performance = Column(JSON, default=dict)  # Topic -> performance mapping
    study_patterns = Column(JSON, default=dict)     # Study time patterns
    recommendations = Column(JSON, default=list)    # AI-generated recommendations
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert learning analytics to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'period_type': self.period_type,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'total_study_minutes': self.total_study_minutes,
            'average_session_minutes': self.average_session_minutes,
            'study_sessions_count': self.study_sessions_count,
            'study_streak_days': self.study_streak_days,
            'practice_tests_taken': self.practice_tests_taken,
            'average_test_score': self.average_test_score,
            'score_improvement': self.score_improvement,
            'weak_areas_count': self.weak_areas_count,
            'certifications_completed': self.certifications_completed,
            'goals_achieved': self.goals_achieved,
            'milestones_reached': self.milestones_reached,
            'overall_progress': self.overall_progress,
            'study_efficiency_score': self.study_efficiency_score,
            'focus_score': self.focus_score,
            'consistency_score': self.consistency_score,
            'topic_performance': self.topic_performance or {},
            'study_patterns': self.study_patterns or {},
            'recommendations': self.recommendations or [],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
