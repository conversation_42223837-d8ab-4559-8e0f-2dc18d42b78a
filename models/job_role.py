"""Job role and domain relationship models"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String as <PERSON><PERSON>S<PERSON>, Foreign<PERSON>ey, Table
from sqlalchemy.orm import relationship
from database import Base

# Association table for job roles and their responsibilities
job_responsibilities = Table(
    'job_responsibilities',
    Base.metadata,
    Column('job_id', <PERSON><PERSON><PERSON>, Foreign<PERSON>ey('job_roles.id'), primary_key=True),
    Column('responsibility', SQLString(500), primary_key=True)
)

class JobRole(Base):
    """Job role model"""
    __tablename__ = 'job_roles'

    id = Column(Integer, primary_key=True)
    title = Column(SQLString(200), nullable=False, unique=True)
    description = Column(SQLString(500))
    domain = Column(SQLString(100), nullable=False)

    # Changed relationship to use the association table without trying to map String
    responsibilities = Column(SQLString(500))  # Store as comma-separated string

    def to_dict(self):
        """Convert job role to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'domain': self.domain,
            'key_responsibilities': self.responsibilities.split(',') if self.responsibilities else []
        }