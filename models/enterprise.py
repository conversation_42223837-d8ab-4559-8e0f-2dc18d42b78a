"""Enterprise models for organizational management and analytics.

This module provides comprehensive models for enterprise deployment including
organizations, departments, user management, licensing, and analytics.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSON, <PERSON><PERSON>ey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, List, Optional
import enum

from models.base import Base, TimestampMixin, SoftDeleteMixin


class OrganizationType(enum.Enum):
    """Organization type enumeration."""
    EDUCATIONAL = "educational"
    CORPORATE = "corporate"
    GOVERNMENT = "government"
    NON_PROFIT = "non_profit"
    TRAINING_PROVIDER = "training_provider"


class SubscriptionTier(enum.Enum):
    """Subscription tier enumeration."""
    BASIC = "basic"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


class UserRole(enum.Enum):
    """User role enumeration."""
    SUPER_ADMIN = "super_admin"
    ORG_ADMIN = "org_admin"
    DEPARTMENT_ADMIN = "department_admin"
    INSTRUCTOR = "instructor"
    LEARNER = "learner"
    VIEWER = "viewer"


class LicenseStatus(enum.Enum):
    """License status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"
    SUSPENDED = "suspended"
    PENDING = "pending"


class EnterpriseOrganization(Base, TimestampMixin, SoftDeleteMixin):
    """Enhanced organization model for enterprise deployment."""
    
    __tablename__ = 'enterprise_organizations'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, index=True)
    display_name = Column(String(200), nullable=True)
    slug = Column(String(100), unique=True, nullable=False, index=True)
    
    # Organization details
    organization_type = Column(Enum(OrganizationType), nullable=False, default=OrganizationType.EDUCATIONAL)
    industry = Column(String(100), nullable=True)
    size_category = Column(String(50), nullable=True)  # small, medium, large, enterprise
    employee_count = Column(Integer, nullable=True)
    
    # Contact information
    primary_contact_name = Column(String(200), nullable=True)
    primary_contact_email = Column(String(200), nullable=True)
    primary_contact_phone = Column(String(50), nullable=True)
    
    # Address information
    address_line1 = Column(String(200), nullable=True)
    address_line2 = Column(String(200), nullable=True)
    city = Column(String(100), nullable=True)
    state_province = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(100), nullable=True)
    
    # Organization settings
    domain = Column(String(200), nullable=True, index=True)  # email domain for auto-enrollment
    logo_url = Column(String(500), nullable=True)
    website = Column(String(500), nullable=True)
    description = Column(Text, nullable=True)
    
    # Subscription and licensing
    subscription_tier = Column(Enum(SubscriptionTier), nullable=False, default=SubscriptionTier.BASIC)
    license_count = Column(Integer, nullable=False, default=10)
    licenses_used = Column(Integer, nullable=False, default=0)
    subscription_start_date = Column(DateTime, nullable=True)
    subscription_end_date = Column(DateTime, nullable=True)
    
    # Configuration
    settings = Column(JSON, nullable=True, default=dict)
    features_enabled = Column(JSON, nullable=True, default=dict)
    branding_config = Column(JSON, nullable=True, default=dict)
    
    # Status and metadata
    is_active = Column(Boolean, nullable=False, default=True)
    is_trial = Column(Boolean, nullable=False, default=False)
    trial_end_date = Column(DateTime, nullable=True)
    
    # Relationships
    departments = relationship("Department", back_populates="organization", cascade="all, delete-orphan")
    users = relationship("EnterpriseUser", back_populates="organization", cascade="all, delete-orphan")
    licenses = relationship("UserLicense", back_populates="organization", cascade="all, delete-orphan")
    analytics = relationship("OrganizationAnalytics", back_populates="organization", cascade="all, delete-orphan")
    teams = relationship("Team", cascade="all, delete-orphan")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'slug': self.slug,
            'organization_type': self.organization_type.value if self.organization_type else None,
            'industry': self.industry,
            'size_category': self.size_category,
            'employee_count': self.employee_count,
            'primary_contact_name': self.primary_contact_name,
            'primary_contact_email': self.primary_contact_email,
            'domain': self.domain,
            'website': self.website,
            'description': self.description,
            'subscription_tier': self.subscription_tier.value if self.subscription_tier else None,
            'license_count': self.license_count,
            'licenses_used': self.licenses_used,
            'subscription_start_date': self.subscription_start_date.isoformat() if self.subscription_start_date else None,
            'subscription_end_date': self.subscription_end_date.isoformat() if self.subscription_end_date else None,
            'is_active': self.is_active,
            'is_trial': self.is_trial,
            'trial_end_date': self.trial_end_date.isoformat() if self.trial_end_date else None,
            'settings': self.settings or {},
            'features_enabled': self.features_enabled or {},
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Department(Base, TimestampMixin, SoftDeleteMixin):
    """Department model for organizational structure."""
    
    __tablename__ = 'departments'
    
    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    code = Column(String(50), nullable=True)  # Department code/abbreviation
    
    # Hierarchy
    parent_department_id = Column(Integer, ForeignKey('departments.id'), nullable=True)
    
    # Department head
    head_user_id = Column(String(100), nullable=True)
    
    # Budget and settings
    budget_allocated = Column(Float, nullable=True)
    budget_used = Column(Float, nullable=False, default=0.0)
    settings = Column(JSON, nullable=True, default=dict)
    
    # Status
    is_active = Column(Boolean, nullable=False, default=True)
    
    # Relationships
    organization = relationship("EnterpriseOrganization", back_populates="departments")
    parent_department = relationship("Department", remote_side=[id])
    users = relationship("EnterpriseUser", back_populates="department")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'organization_id': self.organization_id,
            'name': self.name,
            'description': self.description,
            'code': self.code,
            'parent_department_id': self.parent_department_id,
            'head_user_id': self.head_user_id,
            'budget_allocated': self.budget_allocated,
            'budget_used': self.budget_used,
            'is_active': self.is_active,
            'settings': self.settings or {},
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class EnterpriseUser(Base, TimestampMixin, SoftDeleteMixin):
    """Enhanced user model for enterprise deployment."""
    
    __tablename__ = 'enterprise_users'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), unique=True, nullable=False, index=True)  # External user ID
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    department_id = Column(Integer, ForeignKey('departments.id'), nullable=True)
    
    # User information
    email = Column(String(200), nullable=False, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    display_name = Column(String(200), nullable=True)
    
    # Role and permissions
    role = Column(Enum(UserRole), nullable=False, default=UserRole.LEARNER)
    permissions = Column(JSON, nullable=True, default=dict)
    
    # Employment information
    employee_id = Column(String(100), nullable=True)
    job_title = Column(String(200), nullable=True)
    manager_user_id = Column(String(100), nullable=True)
    hire_date = Column(DateTime, nullable=True)
    
    # Learning profile
    learning_goals = Column(JSON, nullable=True, default=dict)
    certifications_assigned = Column(JSON, nullable=True, default=list)
    certifications_completed = Column(JSON, nullable=True, default=list)
    
    # Status and settings
    is_active = Column(Boolean, nullable=False, default=True)
    last_login = Column(DateTime, nullable=True)
    preferences = Column(JSON, nullable=True, default=dict)
    
    # Relationships
    organization = relationship("EnterpriseOrganization", back_populates="users")
    department = relationship("Department", back_populates="users")
    license = relationship("UserLicense", back_populates="user", uselist=False)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'organization_id': self.organization_id,
            'department_id': self.department_id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'display_name': self.display_name,
            'role': self.role.value if self.role else None,
            'employee_id': self.employee_id,
            'job_title': self.job_title,
            'manager_user_id': self.manager_user_id,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'learning_goals': self.learning_goals or {},
            'certifications_assigned': self.certifications_assigned or [],
            'certifications_completed': self.certifications_completed or [],
            'permissions': self.permissions or {},
            'preferences': self.preferences or {},
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class UserLicense(Base, TimestampMixin):
    """User license model for tracking license usage."""
    
    __tablename__ = 'user_licenses'
    
    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    user_id = Column(String(100), ForeignKey('enterprise_users.user_id'), nullable=False)
    
    # License details
    license_type = Column(String(50), nullable=False)  # basic, professional, enterprise
    status = Column(Enum(LicenseStatus), nullable=False, default=LicenseStatus.ACTIVE)
    
    # Dates
    assigned_date = Column(DateTime, nullable=False, default=func.now())
    activation_date = Column(DateTime, nullable=True)
    expiration_date = Column(DateTime, nullable=True)
    last_used_date = Column(DateTime, nullable=True)
    
    # Usage tracking
    features_used = Column(JSON, nullable=True, default=dict)
    usage_stats = Column(JSON, nullable=True, default=dict)
    
    # Relationships
    organization = relationship("EnterpriseOrganization", back_populates="licenses")
    user = relationship("EnterpriseUser", back_populates="license")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'organization_id': self.organization_id,
            'user_id': self.user_id,
            'license_type': self.license_type,
            'status': self.status.value if self.status else None,
            'assigned_date': self.assigned_date.isoformat() if self.assigned_date else None,
            'activation_date': self.activation_date.isoformat() if self.activation_date else None,
            'expiration_date': self.expiration_date.isoformat() if self.expiration_date else None,
            'last_used_date': self.last_used_date.isoformat() if self.last_used_date else None,
            'features_used': self.features_used or {},
            'usage_stats': self.usage_stats or {},
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class OrganizationAnalytics(Base, TimestampMixin):
    """Organization analytics model for tracking usage and performance."""
    
    __tablename__ = 'organization_analytics'
    
    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    
    # Time period
    period_type = Column(String(20), nullable=False)  # daily, weekly, monthly, quarterly, yearly
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # User metrics
    total_users = Column(Integer, nullable=False, default=0)
    active_users = Column(Integer, nullable=False, default=0)
    new_users = Column(Integer, nullable=False, default=0)
    user_retention_rate = Column(Float, nullable=True)
    
    # Learning metrics
    total_study_hours = Column(Float, nullable=False, default=0.0)
    average_study_hours_per_user = Column(Float, nullable=True)
    total_certifications_completed = Column(Integer, nullable=False, default=0)
    certification_completion_rate = Column(Float, nullable=True)
    
    # Engagement metrics
    total_sessions = Column(Integer, nullable=False, default=0)
    average_session_duration = Column(Float, nullable=True)
    feature_usage = Column(JSON, nullable=True, default=dict)
    
    # Performance metrics
    average_test_scores = Column(Float, nullable=True)
    goal_completion_rate = Column(Float, nullable=True)
    user_satisfaction_score = Column(Float, nullable=True)
    
    # Cost and ROI metrics
    cost_per_user = Column(Float, nullable=True)
    training_cost_savings = Column(Float, nullable=True)
    productivity_improvement = Column(Float, nullable=True)
    
    # Detailed analytics
    department_breakdown = Column(JSON, nullable=True, default=dict)
    certification_breakdown = Column(JSON, nullable=True, default=dict)
    usage_patterns = Column(JSON, nullable=True, default=dict)
    
    # Relationships
    organization = relationship("EnterpriseOrganization", back_populates="analytics")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'organization_id': self.organization_id,
            'period_type': self.period_type,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'total_users': self.total_users,
            'active_users': self.active_users,
            'new_users': self.new_users,
            'user_retention_rate': self.user_retention_rate,
            'total_study_hours': self.total_study_hours,
            'average_study_hours_per_user': self.average_study_hours_per_user,
            'total_certifications_completed': self.total_certifications_completed,
            'certification_completion_rate': self.certification_completion_rate,
            'total_sessions': self.total_sessions,
            'average_session_duration': self.average_session_duration,
            'average_test_scores': self.average_test_scores,
            'goal_completion_rate': self.goal_completion_rate,
            'user_satisfaction_score': self.user_satisfaction_score,
            'cost_per_user': self.cost_per_user,
            'training_cost_savings': self.training_cost_savings,
            'productivity_improvement': self.productivity_improvement,
            'feature_usage': self.feature_usage or {},
            'department_breakdown': self.department_breakdown or {},
            'certification_breakdown': self.certification_breakdown or {},
            'usage_patterns': self.usage_patterns or {},
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class Team(Base, TimestampMixin, SoftDeleteMixin):
    """Team model for organizing users within organizations."""

    __tablename__ = 'teams'

    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)

    # Team information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    code = Column(String(50), nullable=True)  # Team code/abbreviation

    # Team lead
    team_lead_user_id = Column(String(100), nullable=True)

    # Team settings
    settings = Column(JSON, nullable=True, default=dict)
    goals = Column(JSON, nullable=True, default=list)

    # Status
    is_active = Column(Boolean, nullable=False, default=True)

    # Relationships
    organization = relationship("EnterpriseOrganization")
    memberships = relationship("TeamMembership", back_populates="team", cascade="all, delete-orphan")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'organization_id': self.organization_id,
            'name': self.name,
            'description': self.description,
            'code': self.code,
            'team_lead_user_id': self.team_lead_user_id,
            'is_active': self.is_active,
            'settings': self.settings or {},
            'goals': self.goals or [],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class TeamMembership(Base, TimestampMixin):
    """Team membership model for tracking user-team relationships."""

    __tablename__ = 'team_memberships'

    id = Column(Integer, primary_key=True)
    team_id = Column(Integer, ForeignKey('teams.id'), nullable=False)
    user_id = Column(String(100), ForeignKey('enterprise_users.user_id'), nullable=False)

    # Membership details
    role = Column(String(50), nullable=False, default='member')  # member, lead, admin
    joined_date = Column(DateTime, nullable=False, default=func.now())

    # Status
    is_active = Column(Boolean, nullable=False, default=True)

    # Relationships
    team = relationship("Team", back_populates="memberships")
    user = relationship("EnterpriseUser")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'team_id': self.team_id,
            'user_id': self.user_id,
            'role': self.role,
            'joined_date': self.joined_date.isoformat() if self.joined_date else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
