"""Models package initialization"""
from database import Base

# Import mixins first
from .mixins import TimestampMixin, SoftDeleteMixin

# Import user models first since others depend on them
from .user import User, UserSession
from .user_experience import UserExperience

# Import independent models next
from .certification import Organization, Certification, CertificationVersion
from .security_domain import SecurityDomain

# Import dependent models after their dependencies
from .learning_path import LearningPath, LearningPathItem, StudyResource
from .cost_calculation import CurrencyRate, CostScenario, CostCalculation, CostHistory
from .career_transition import CareerRole, CareerTransitionPath, CareerTransitionPlan, CareerTransitionStep
from .progress_tracking import StudySession, PracticeTestResult, LearningGoal, Achievement, LearningAnalytics
from .enterprise import EnterpriseOrganization, Department, EnterpriseUser, UserLicense, OrganizationAnalytics
from .security_career_framework import SecurityJobType, SecurityCareerPath, SecuritySkillMatrix, SecurityMarketData
from .marketplace import (
    MarketplaceVendor, MarketplaceCourse, PartnershipAgreement, CommissionRecord,
    CourseEnrollment, CourseReview, CurrencyRate as MarketplaceCurrencyRate, InternationalMarket
)
from .audit_log import AuditLog
from .study_session import StudyTimerSession, StudyGoal, StudyStreak  # Note: StudySession already imported from progress_tracking

# This ensures all models are registered with SQLAlchemy's metadata
__all__ = [
    'Base',
    'TimestampMixin',
    'SoftDeleteMixin',
    'User',
    'UserSession',
    'UserExperience',
    'Organization',
    'Certification',
    'CertificationVersion',
    'SecurityDomain',
    'LearningPath',
    'LearningPathItem',
    'StudyResource',
    'CurrencyRate',
    'CostScenario',
    'CostCalculation',
    'CostHistory',
    'CareerRole',
    'CareerTransitionPath',
    'CareerTransitionPlan',
    'CareerTransitionStep',
    'StudySession',
    'StudyTimerSession',
    'PracticeTestResult',
    'LearningGoal',
    'Achievement',
    'LearningAnalytics',
    'EnterpriseOrganization',
    'Department',
    'EnterpriseUser',
    'UserLicense',
    'OrganizationAnalytics',
    'SecurityJobType',
    'SecurityCareerPath',
    'SecuritySkillMatrix',
    'SecurityMarketData',
    'StudyGoal',
    'StudyStreak',
    'MarketplaceVendor',
    'MarketplaceCourse',
    'PartnershipAgreement',
    'CommissionRecord',
    'CourseEnrollment',
    'CourseReview',
    'MarketplaceCurrencyRate',
    'InternationalMarket',
    'AuditLog'
]