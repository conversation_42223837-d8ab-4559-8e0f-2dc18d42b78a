"""User experience model for tracking professional background and skills"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, JSON, CheckConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from .mixins import TimestampMixin
from database import Base
import logging

logger = logging.getLogger(__name__)

class UserExperience(Base, TimestampMixin):
    """User experience and profile model"""
    __tablename__ = 'user_experiences'

    id = Column(Integer, primary_key=True)
    user_id = Column(String, unique=True, nullable=False, index=True)
    years_experience = Column(Integer, nullable=False)
    user_role = Column(String, nullable=False)
    desired_role = Column(String, nullable=False)
    expertise_areas = Column(JSON, nullable=False)  # Store as native JSON array
    preferred_learning_style = Column(String, nullable=False)
    study_time_available = Column(Integer, nullable=False)
    tutorial_completed = Column(Boolean, nullable=False, default=False)

    # Database-level constraints for enterprise validation
    __table_args__ = (
        CheckConstraint('years_experience >= 0', name='check_years_experience_positive'),
        CheckConstraint('study_time_available > 0', name='check_study_time_positive'),
        CheckConstraint('LENGTH(user_role) > 0', name='check_user_role_not_empty'),
        CheckConstraint('LENGTH(desired_role) > 0', name='check_desired_role_not_empty'),
        CheckConstraint('LENGTH(preferred_learning_style) > 0', name='check_learning_style_not_empty'),
    )

    # Define relationship with lazy loading
    learning_paths = relationship(
        "LearningPath",
        back_populates="user_experience",
        lazy="dynamic",
        cascade="all, delete-orphan"
    )

    def __init__(self, **kwargs):
        """Initialize the model with validation"""
        logger.debug(f"Initializing UserExperience with kwargs: {kwargs}")

        # Initialize expertise_areas as empty list if not provided
        if 'expertise_areas' not in kwargs:
            kwargs['expertise_areas'] = []
        elif not isinstance(kwargs['expertise_areas'], list):
            kwargs['expertise_areas'] = [kwargs['expertise_areas']]

        # Verify required fields
        for field in ['years_experience', 'user_role', 'desired_role', 'preferred_learning_style', 'study_time_available']:
            if field not in kwargs:
                raise ValueError(f"{field} is required")

        logger.debug(f"Processed kwargs: {kwargs}")
        super().__init__(**kwargs)
        logger.debug(f"Instance after initialization: {self.__dict__}")

    def to_dict(self):
        """Convert model to dictionary"""
        result = {
            'id': self.id,
            'user_id': self.user_id,
            'years_experience': self.years_experience,
            'user_role': self.user_role,
            'desired_role': self.desired_role,
            'expertise_areas': self.expertise_areas or [],  # Return empty list if None
            'preferred_learning_style': self.preferred_learning_style,
            'study_time_available': self.study_time_available,
            'tutorial_completed': self.tutorial_completed,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        logger.debug(f"Converting to dict: {result}")
        return result

    def __repr__(self):
        return f"<UserExperience(user_id='{self.user_id}', years_experience={self.years_experience})>"