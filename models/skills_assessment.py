"""
SQLAlchemy models for Skills Assessment functionality
Feature 1.1: Skills Vector Representation & Scoring - Integration Tests Phase
"""
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, JSON, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from models.base import Base
from models.mixins import SoftDeleteMixin

class SkillAssessment(Base, SoftDeleteMixin):
    """Skills assessment model for storing user skill evaluations"""
    __tablename__ = "skills_assessments"

    id = Column(Integer, primary_key=True)
    assessment_id = Column(String(100), unique=True, nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Assessment metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    assessment_type = Column(String(50), default="self_assessment", nullable=False)
    
    # Skill data (JSON fields for flexibility)
    skill_scores = Column(JSON, nullable=False)  # Individual skill scores
    domain_scores = Column(JSON, nullable=False)  # Domain-level aggregated scores
    confidence_scores = Column(JSON, nullable=False)  # Confidence levels per skill
    
    # Assessment summary
    total_skills_assessed = Column(Integer, nullable=False)
    average_skill_level = Column(Float, nullable=False)
    strongest_domain = Column(String(100), nullable=True)
    assessment_completeness = Column(Float, nullable=False)  # Percentage of framework covered
    
    # Relationships
    user = relationship("User", back_populates="skills_assessments")
    skill_items = relationship("SkillAssessmentItem", back_populates="assessment", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<SkillAssessment {self.assessment_id} for user {self.user_id}>"

class SkillAssessmentItem(Base):
    """Individual skill assessment items"""
    __tablename__ = "skills_assessment_items"

    id = Column(Integer, primary_key=True)
    assessment_id = Column(Integer, ForeignKey("skills_assessments.id"), nullable=False, index=True)
    
    # Skill information
    skill_name = Column(String(100), nullable=False, index=True)
    skill_domain = Column(String(100), nullable=False, index=True)
    
    # Assessment values
    skill_level = Column(String(20), nullable=False)  # none, basic, intermediate, advanced, expert
    confidence_level = Column(String(30), nullable=False)  # very_confident, confident, somewhat_confident, not_confident
    
    # Calculated scores
    raw_score = Column(Float, nullable=False)
    confidence_weighted_score = Column(Float, nullable=False)
    certification_boost = Column(Float, default=0.0, nullable=False)
    final_score = Column(Float, nullable=False)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    assessment = relationship("SkillAssessment", back_populates="skill_items")
    
    def __repr__(self):
        return f"<SkillAssessmentItem {self.skill_name}: {self.final_score}>"

class UserSkillProfile(Base, SoftDeleteMixin):
    """User's current skill profile (latest assessment summary)"""
    __tablename__ = "user_skill_profiles"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False, index=True)
    
    # Current skill vector (JSON for flexibility)
    skill_vector = Column(JSON, nullable=False)  # All skills with current scores
    domain_scores = Column(JSON, nullable=False)  # Current domain scores
    confidence_scores = Column(JSON, nullable=False)  # Current confidence levels
    
    # Profile metadata
    last_assessment_id = Column(String(100), nullable=False)
    last_updated = Column(DateTime, default=datetime.utcnow, nullable=False)
    total_assessments = Column(Integer, default=1, nullable=False)
    
    # Profile summary
    overall_skill_level = Column(Float, nullable=False)
    strongest_domain = Column(String(100), nullable=True)
    weakest_domain = Column(String(100), nullable=True)
    skill_growth_rate = Column(Float, default=0.0, nullable=False)  # Change since last assessment
    
    # Relationships
    user = relationship("User", back_populates="skill_profile")
    
    def __repr__(self):
        return f"<UserSkillProfile user_id={self.user_id}, level={self.overall_skill_level}>"

class SkillCertification(Base):
    """User certifications that boost skill scores"""
    __tablename__ = "skill_certifications"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Certification information
    name = Column(String(200), nullable=False)
    provider = Column(String(100), nullable=False)
    year_obtained = Column(Integer, nullable=True)
    expiry_year = Column(Integer, nullable=True)
    
    # Skill relevance
    relevant_skills = Column(JSON, nullable=True)  # List of skills this cert boosts
    boost_factor = Column(Float, default=0.1, nullable=False)  # How much boost it provides
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    verified = Column(Boolean, default=False, nullable=False)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="certifications")
    
    def __repr__(self):
        return f"<SkillCertification {self.name} by {self.provider}>"

class SkillDomainWeight(Base):
    """Domain-specific skill weights for different roles"""
    __tablename__ = "skill_domain_weights"

    id = Column(Integer, primary_key=True)
    
    # Role and domain information
    role_name = Column(String(100), nullable=False, index=True)
    domain_name = Column(String(100), nullable=False, index=True)
    
    # Weight information
    weight = Column(Float, nullable=False)  # Importance weight for this domain in this role
    skill_weights = Column(JSON, nullable=True)  # Individual skill weights within domain
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Source information
    source = Column(String(50), default="manual", nullable=False)  # manual, calculated, market_data
    confidence = Column(Float, default=1.0, nullable=False)  # Confidence in these weights
    
    def __repr__(self):
        return f"<SkillDomainWeight {self.role_name}: {self.domain_name} = {self.weight}>"

# Add relationships to User model (this would be added to models/user.py)
"""
Add these relationships to the User class in models/user.py:

# Skills assessment relationships
skills_assessments = relationship("SkillAssessment", back_populates="user")
skill_profile = relationship("UserSkillProfile", back_populates="user", uselist=False)
certifications = relationship("SkillCertification", back_populates="user")
"""
