"""Validation mixins and utilities for model relationship integrity"""
from sqlalchemy import inspect
from sqlalchemy.orm import Session
from typing import Any, Dict, List, Optional, Type, Union
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class RelationshipValidationMixin:
    """Mixin class providing relationship validation methods"""

    @classmethod
    def validate_foreign_key(cls, session: Session, field_name: str, related_model: Type, value: Any) -> bool:
        """Validate that a foreign key value exists in the related table"""
        if value is None:
            return True  # Allow nullable foreign keys

        exists = session.query(related_model).filter_by(id=value).first() is not None
        if not exists:
            logger.error(f"Invalid foreign key: {field_name}={value} does not exist in {related_model.__name__}")
        return exists

    @classmethod
    def validate_domain_reference(cls, session: Session, domain: str) -> bool:
        """Validate that a domain exists in the SecurityDomain table"""
        from models.security_domain import SecurityDomain

        exists = session.query(SecurityDomain).filter_by(
            name=domain,
            is_deleted=False
        ).first() is not None

        if not exists:
            logger.error(f"Invalid domain reference: {domain} does not exist in SecurityDomain")
        return exists

    def validate_relationships(self, session: Session) -> Dict[str, bool]:
        """Validate all relationships for the current model instance"""
        validation_results = {}
        mapper = inspect(self.__class__)

        # Validate foreign key relationships
        for rel in mapper.relationships:
            if hasattr(self, rel.key):
                related_value = getattr(self, rel.key)
                if rel.key == 'organization':
                    # Special handling for organization relationship
                    validation_results[rel.key] = self.validate_foreign_key(
                        session, 
                        'organization_id', 
                        rel.mapper.class_, 
                        self.organization_id if hasattr(self, 'organization_id') else None
                    )
                else:
                    validation_results[rel.key] = True if related_value is not None else False
                    if not validation_results[rel.key]:
                        logger.warning(f"Missing relationship {rel.key}")

        # Validate domain references if applicable
        if hasattr(self, 'domain'):
            validation_results['domain'] = self.validate_domain_reference(session, self.domain)

        return validation_results

    def validate_version_integrity(self, session: Session) -> bool:
        """Validate version integrity for models with versioning"""
        from models.certification import CertificationVersion

        if not hasattr(self, 'current_version'):
            return True

        # Check if versions exist and are sequential
        versions = session.query(CertificationVersion).filter_by(
            certification_id=self.id
        ).order_by(CertificationVersion.version).all()

        if not versions:
            return True

        # Verify version sequence
        expected_version = 1
        for version in versions:
            if version.version != expected_version:
                logger.error(f"Version sequence broken at version {expected_version}")
                return False
            expected_version += 1

        return True

    def validate_soft_delete_integrity(self, session: Session) -> bool:
        """Validate soft delete integrity across related records"""
        if not hasattr(self, 'is_deleted'):
            return True

        mapper = inspect(self.__class__)

        # Check if any parent record is soft deleted
        for rel in mapper.relationships:
            if hasattr(self, rel.key):
                related_obj = getattr(self, rel.key)
                if related_obj and hasattr(related_obj, 'is_deleted'):
                    if related_obj.is_deleted and not self.is_deleted:
                        logger.error(f"Parent record {rel.key} is deleted but child is not")
                        return False

        return True