"""
SQLAlchemy models for learning paths
"""
from sqlalchemy import (
    Column, Integer, String, Text, ForeignKey,
    DateTime, Enum, JSON, Table, Boolean, Float, CheckConstraint
)
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict
from database import Base
from .mixins import TimestampMixin

class LearningPath(Base, TimestampMixin):
    """
    Represents a personalized certification learning path for a user
    """
    __tablename__ = 'learning_paths'

    id = Column(Integer, primary_key=True)
    user_experience_id = Column(Integer, ForeignKey('user_experiences.id', ondelete='CASCADE'), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    estimated_duration_weeks = Column(Integer)
    difficulty_level = Column(String(20))  # "Beginner", "Intermediate", "Advanced"
    status = Column(String(20), default="active")  # "active", "completed", "abandoned"

    # Define relationships with lazy loading
    user_experience = relationship(
        "UserExperience",
        back_populates="learning_paths",
        lazy="joined"
    )
    path_items = relationship(
        "LearningPathItem",
        back_populates="learning_path",
        lazy="joined",
        order_by="LearningPathItem.sequence",
        cascade="all, delete-orphan"
    )

    # Database-level constraints for enterprise validation
    __table_args__ = (
        CheckConstraint('estimated_duration_weeks > 0', name='check_duration_positive'),
        CheckConstraint('LENGTH(name) > 0', name='check_name_not_empty'),
        CheckConstraint("difficulty_level IN ('Beginner', 'Intermediate', 'Advanced')", name='check_difficulty_level'),
        CheckConstraint("status IN ('active', 'completed', 'abandoned')", name='check_status_valid'),
    )

    def to_dict(self) -> Dict:
        """Convert learning path to dictionary with proper datetime serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'estimated_duration_weeks': self.estimated_duration_weeks,
            'difficulty_level': self.difficulty_level,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'items': [item.to_dict() for item in self.path_items]
        }

class LearningPathItem(Base, TimestampMixin):
    """
    Individual item in a learning path (certification or prerequisite study)
    """
    __tablename__ = 'learning_path_items'

    id = Column(Integer, primary_key=True)
    learning_path_id = Column(Integer, ForeignKey('learning_paths.id', ondelete='CASCADE'), nullable=False)
    certification_id = Column(Integer, ForeignKey('certifications.id', ondelete='SET NULL'), nullable=True)
    sequence = Column(Integer, nullable=False)  # Order in the path
    item_type = Column(String(20), nullable=False)  # "certification", "prerequisite", "practice"
    name = Column(String(100), nullable=False)
    description = Column(Text)
    estimated_hours = Column(Integer)
    status = Column(String(20), default="pending")  # "pending", "in_progress", "completed"

    # Define relationships with lazy loading
    learning_path = relationship("LearningPath", back_populates="path_items", lazy="joined")
    certification = relationship("Certification", lazy="joined")
    study_resources = relationship(
        "StudyResource",
        back_populates="learning_path_item",
        lazy="joined",
        cascade="all, delete-orphan"
    )
    # Note: StudySession links to learning_path, not learning_path_item
    # study_sessions = relationship("StudySession", back_populates="learning_path_item")

    def to_dict(self) -> Dict:
        """Convert learning path item to dictionary with proper datetime serialization"""
        return {
            'id': self.id,
            'sequence': self.sequence,
            'item_type': self.item_type,
            'name': self.name,
            'description': self.description,
            'estimated_hours': self.estimated_hours,
            'status': self.status,
            'study_resources': [resource.to_dict() for resource in self.study_resources],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'certification': self.certification.to_dict() if self.certification else None
        }

class StudyResource(Base, TimestampMixin):
    """
    Stores study resources for certifications and learning paths
    """
    __tablename__ = 'study_resources'

    id = Column(Integer, primary_key=True)
    learning_path_item_id = Column(Integer, ForeignKey('learning_path_items.id', ondelete='CASCADE'), nullable=False)
    resource_type = Column(String(50), nullable=False)  # 'documentation', 'practice_exam', 'video', 'tutorial'
    title = Column(String(200), nullable=False)
    url = Column(String(500), nullable=False)
    description = Column(Text)
    is_official = Column(Boolean, default=False)  # Whether it's official certification material
    rating = Column(Float)  # Community rating
    access_type = Column(String(50))  # 'free', 'paid', 'subscription'

    # Define relationship with lazy loading
    learning_path_item = relationship("LearningPathItem", back_populates="study_resources", lazy="joined")

    def to_dict(self) -> Dict:
        """Convert study resource to dictionary with proper datetime serialization"""
        return {
            'id': self.id,
            'resource_type': self.resource_type,
            'title': self.title,
            'url': self.url,
            'description': self.description,
            'is_official': self.is_official,
            'rating': self.rating,
            'access_type': self.access_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }