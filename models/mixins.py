"""SQLAlchemy mixins for model functionality"""
from datetime import datetime
from sqlalchemy import Column, DateTime, Boolean
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy import event

class TimestampMixin:
    """Mixin for adding timestamp fields to models"""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

class SoftDeleteMixin:
    """Mixin class to add soft delete functionality to models"""

    deleted_at = Column(DateTime, nullable=True)
    is_deleted = Column(Boolean, default=False, nullable=False)

    @declared_attr
    def __mapper_args__(cls):
        """Add default query filter to exclude soft-deleted records"""
        return {
            'polymorphic_on': None,
            'with_polymorphic': '*'
        }

    def soft_delete(self):
        """Mark record as deleted"""
        self.deleted_at = datetime.utcnow()
        self.is_deleted = True

    def restore(self):
        """Restore a soft-deleted record"""
        self.deleted_at = None
        self.is_deleted = False

    @classmethod
    def not_deleted(cls):
        """Query only not deleted records"""
        return cls.query.filter_by(is_deleted=False)

@event.listens_for(SoftDeleteMixin, 'load', propagate=True)
def load_listener(target, context):
    """Ensure is_deleted matches deleted_at state"""
    target.is_deleted = target.deleted_at is not None