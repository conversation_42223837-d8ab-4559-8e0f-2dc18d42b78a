"""Cost calculation models for certification cost analysis and planning.

This module provides models for:
- CostCalculation: Individual cost calculations with detailed breakdowns
- CostScenario: Different cost scenarios (self-study, with training, etc.)
- CurrencyRate: Exchange rate tracking for multi-currency support
- CostHistory: Historical cost tracking for trend analysis
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from .mixins import TimestampMixin
from database import Base
import logging

logger = logging.getLogger(__name__)


class CurrencyRate(Base, TimestampMixin):
    """Model for tracking currency exchange rates.
    
    Stores exchange rates for different currencies with automatic updates
    and historical tracking for cost calculations.
    """
    __tablename__ = 'currency_rates'

    id = Column(Integer, primary_key=True)
    base_currency = Column(String(3), nullable=False, default='USD')  # ISO 4217 currency code
    target_currency = Column(String(3), nullable=False)  # ISO 4217 currency code
    rate = Column(Float, nullable=False)
    source = Column(String(50), nullable=False, default='manual')  # 'api', 'manual', 'calculated'
    is_active = Column(Boolean, nullable=False, default=True)
    valid_from = Column(DateTime, nullable=False, default=datetime.utcnow)
    valid_until = Column(DateTime, nullable=True)

    def __repr__(self) -> str:
        """String representation of the currency rate."""
        return f"<CurrencyRate({self.base_currency}/{self.target_currency}={self.rate})>"

    @property
    def is_valid(self) -> bool:
        """Check if the currency rate is currently valid."""
        now = datetime.utcnow()
        return (
            self.is_active and
            self.valid_from <= now and
            (self.valid_until is None or self.valid_until > now)
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert the currency rate to a dictionary."""
        return {
            'id': self.id,
            'base_currency': self.base_currency,
            'target_currency': self.target_currency,
            'rate': self.rate,
            'source': self.source,
            'is_active': self.is_active,
            'valid_from': self.valid_from.isoformat() if self.valid_from else None,
            'valid_until': self.valid_until.isoformat() if self.valid_until else None,
            'is_valid': self.is_valid,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CostScenario(Base, TimestampMixin):
    """Model for different cost calculation scenarios.
    
    Represents different approaches to certification preparation
    with varying cost structures and components.
    """
    __tablename__ = 'cost_scenarios'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    scenario_type = Column(String(50), nullable=False)  # 'self_study', 'bootcamp', 'university', 'corporate'
    
    # Cost multipliers and factors
    materials_multiplier = Column(Float, nullable=False, default=1.0)
    training_multiplier = Column(Float, nullable=False, default=1.0)
    retake_probability = Column(Float, nullable=False, default=0.2)  # 20% chance of retake
    
    # Additional costs
    includes_training = Column(Boolean, nullable=False, default=False)
    includes_mentoring = Column(Boolean, nullable=False, default=False)
    includes_practice_exams = Column(Boolean, nullable=False, default=True)
    
    # Time factors
    study_time_multiplier = Column(Float, nullable=False, default=1.0)
    preparation_weeks = Column(Integer, nullable=True)
    
    is_active = Column(Boolean, nullable=False, default=True)

    def __repr__(self) -> str:
        """String representation of the cost scenario."""
        return f"<CostScenario(name='{self.name}', type='{self.scenario_type}')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert the cost scenario to a dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'scenario_type': self.scenario_type,
            'materials_multiplier': self.materials_multiplier,
            'training_multiplier': self.training_multiplier,
            'retake_probability': self.retake_probability,
            'includes_training': self.includes_training,
            'includes_mentoring': self.includes_mentoring,
            'includes_practice_exams': self.includes_practice_exams,
            'study_time_multiplier': self.study_time_multiplier,
            'preparation_weeks': self.preparation_weeks,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CostCalculation(Base, TimestampMixin):
    """Model for individual cost calculations.
    
    Stores detailed cost breakdowns for certification paths
    with support for multiple currencies and scenarios.
    """
    __tablename__ = 'cost_calculations'

    id = Column(Integer, primary_key=True)
    user_id = Column(String, nullable=False, index=True)
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # Calculation parameters
    base_currency = Column(String(3), nullable=False, default='USD')
    target_currency = Column(String(3), nullable=False, default='USD')
    scenario_id = Column(Integer, ForeignKey('cost_scenarios.id'), nullable=True)
    
    # Certification selection
    certification_ids = Column(JSON, nullable=False)  # List of certification IDs
    
    # Cost breakdown (in base currency)
    exam_fees_total = Column(Float, nullable=False, default=0.0)
    materials_cost = Column(Float, nullable=False, default=0.0)
    training_cost = Column(Float, nullable=False, default=0.0)
    retake_cost = Column(Float, nullable=False, default=0.0)
    additional_costs = Column(Float, nullable=False, default=0.0)
    
    # Totals
    total_cost_base = Column(Float, nullable=False, default=0.0)
    total_cost_target = Column(Float, nullable=False, default=0.0)
    exchange_rate_used = Column(Float, nullable=False, default=1.0)
    
    # Time estimates
    estimated_study_hours = Column(Integer, nullable=True)
    estimated_weeks = Column(Integer, nullable=True)
    
    # Metadata
    calculation_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    is_saved = Column(Boolean, nullable=False, default=False)
    is_shared = Column(Boolean, nullable=False, default=False)
    
    # Relationships
    scenario = relationship("CostScenario", lazy="joined")

    def __repr__(self) -> str:
        """String representation of the cost calculation."""
        return f"<CostCalculation(id={self.id}, user_id='{self.user_id}', total={self.total_cost_target})>"

    @property
    def certification_count(self) -> int:
        """Get the number of certifications in this calculation."""
        return len(self.certification_ids) if self.certification_ids else 0

    @property
    def average_cost_per_certification(self) -> float:
        """Calculate average cost per certification."""
        if self.certification_count == 0:
            return 0.0
        return self.total_cost_target / self.certification_count

    def update_totals(self) -> None:
        """Update total cost calculations."""
        self.total_cost_base = (
            self.exam_fees_total +
            self.materials_cost +
            self.training_cost +
            self.retake_cost +
            self.additional_costs
        )
        self.total_cost_target = self.total_cost_base * self.exchange_rate_used

    def to_dict(self) -> Dict[str, Any]:
        """Convert the cost calculation to a dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'description': self.description,
            'base_currency': self.base_currency,
            'target_currency': self.target_currency,
            'scenario_id': self.scenario_id,
            'certification_ids': self.certification_ids,
            'certification_count': self.certification_count,
            'cost_breakdown': {
                'exam_fees': self.exam_fees_total,
                'materials': self.materials_cost,
                'training': self.training_cost,
                'retakes': self.retake_cost,
                'additional': self.additional_costs
            },
            'totals': {
                'base_currency': self.total_cost_base,
                'target_currency': self.total_cost_target,
                'exchange_rate': self.exchange_rate_used
            },
            'time_estimates': {
                'study_hours': self.estimated_study_hours,
                'weeks': self.estimated_weeks
            },
            'average_cost_per_certification': self.average_cost_per_certification,
            'calculation_date': self.calculation_date.isoformat() if self.calculation_date else None,
            'is_saved': self.is_saved,
            'is_shared': self.is_shared,
            'scenario': self.scenario.to_dict() if self.scenario else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CostHistory(Base, TimestampMixin):
    """Model for tracking historical cost data.
    
    Tracks changes in certification costs over time for
    trend analysis and cost forecasting.
    """
    __tablename__ = 'cost_history'

    id = Column(Integer, primary_key=True)
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=False)
    cost = Column(Float, nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    source = Column(String(100), nullable=True)  # Where the cost data came from
    effective_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Change tracking
    previous_cost = Column(Float, nullable=True)
    change_percentage = Column(Float, nullable=True)
    change_reason = Column(String(200), nullable=True)
    
    # Validation
    is_verified = Column(Boolean, nullable=False, default=False)
    verified_by = Column(String, nullable=True)
    verified_date = Column(DateTime, nullable=True)

    # Relationships
    certification = relationship("Certification", lazy="joined")

    def __repr__(self) -> str:
        """String representation of the cost history entry."""
        return f"<CostHistory(cert_id={self.certification_id}, cost={self.cost}, date={self.effective_date})>"

    @property
    def cost_change_direction(self) -> Optional[str]:
        """Determine if cost increased, decreased, or stayed the same."""
        if self.previous_cost is None:
            return None
        
        if self.cost > self.previous_cost:
            return 'increase'
        elif self.cost < self.previous_cost:
            return 'decrease'
        else:
            return 'unchanged'

    def calculate_change_percentage(self) -> None:
        """Calculate the percentage change from previous cost."""
        if self.previous_cost and self.previous_cost > 0:
            self.change_percentage = ((self.cost - self.previous_cost) / self.previous_cost) * 100

    def to_dict(self) -> Dict[str, Any]:
        """Convert the cost history entry to a dictionary."""
        return {
            'id': self.id,
            'certification_id': self.certification_id,
            'cost': self.cost,
            'currency': self.currency,
            'source': self.source,
            'effective_date': self.effective_date.isoformat() if self.effective_date else None,
            'previous_cost': self.previous_cost,
            'change_percentage': self.change_percentage,
            'change_reason': self.change_reason,
            'cost_change_direction': self.cost_change_direction,
            'is_verified': self.is_verified,
            'verified_by': self.verified_by,
            'verified_date': self.verified_date.isoformat() if self.verified_date else None,
            'certification': self.certification.to_dict() if self.certification else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
