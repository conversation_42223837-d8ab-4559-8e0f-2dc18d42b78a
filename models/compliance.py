"""Compliance models for enterprise regulatory reporting and audit management.

This module provides comprehensive models for managing compliance requirements,
audit trails, and regulatory reporting across multiple frameworks including
GDPR, HIPAA, SOX, CMMC, and custom compliance standards.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, JSON, ForeignKey, Float, Enum as SQLEnum
from sqlalchemy.orm import relationship
from models.base import Base, TimestampMixin, SoftDeleteMixin

logger = logging.getLogger(__name__)


class ComplianceFramework(str, Enum):
    """Supported compliance frameworks."""
    GDPR = "gdpr"
    HIPAA = "hipaa"
    SOX = "sox"
    CMMC = "cmmc"
    ISO27001 = "iso27001"
    NIST = "nist"
    PCI_DSS = "pci_dss"
    CUSTOM = "custom"


class ComplianceStatus(str, Enum):
    """Compliance status levels."""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    UNDER_REVIEW = "under_review"
    REMEDIATION_REQUIRED = "remediation_required"


class AuditEventType(str, Enum):
    """Types of audit events."""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    DATA_EXPORT = "data_export"
    PERMISSION_CHANGE = "permission_change"
    SYSTEM_CONFIGURATION = "system_configuration"
    COMPLIANCE_REPORT = "compliance_report"
    SECURITY_INCIDENT = "security_incident"


class RiskLevel(str, Enum):
    """Risk assessment levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ComplianceRequirement(Base, TimestampMixin, SoftDeleteMixin):
    """Model for compliance requirements and controls."""
    
    __tablename__ = 'compliance_requirements'
    
    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    
    # Requirement details
    framework = Column(SQLEnum(ComplianceFramework), nullable=False)
    requirement_id = Column(String(100), nullable=False)  # e.g., "GDPR-Art-32"
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)
    
    # Implementation details
    implementation_guidance = Column(Text, nullable=True)
    evidence_required = Column(JSON, nullable=True, default=list)
    automated_check = Column(Boolean, nullable=False, default=False)
    check_frequency = Column(String(50), nullable=True)  # daily, weekly, monthly, quarterly
    
    # Status and assessment
    status = Column(SQLEnum(ComplianceStatus), nullable=False, default=ComplianceStatus.UNDER_REVIEW)
    risk_level = Column(SQLEnum(RiskLevel), nullable=False, default=RiskLevel.MEDIUM)
    last_assessed = Column(DateTime, nullable=True)
    next_assessment_due = Column(DateTime, nullable=True)
    
    # Metadata
    assigned_to = Column(String(255), nullable=True)
    tags = Column(JSON, nullable=True, default=list)
    custom_fields = Column(JSON, nullable=True, default=dict)
    
    # Relationships
    organization = relationship("EnterpriseOrganization", back_populates="compliance_requirements")
    assessments = relationship("ComplianceAssessment", back_populates="requirement")
    
    def __repr__(self):
        return f"<ComplianceRequirement {self.framework.value}-{self.requirement_id}>"


class ComplianceAssessment(Base, TimestampMixin):
    """Model for compliance assessments and evaluations."""
    
    __tablename__ = 'compliance_assessments'
    
    id = Column(Integer, primary_key=True)
    requirement_id = Column(Integer, ForeignKey('compliance_requirements.id'), nullable=False)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    
    # Assessment details
    assessment_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    assessor_id = Column(String(255), nullable=False)
    assessment_method = Column(String(100), nullable=True)  # manual, automated, hybrid
    
    # Results
    status = Column(SQLEnum(ComplianceStatus), nullable=False)
    score = Column(Float, nullable=True)  # 0-100 compliance score
    findings = Column(Text, nullable=True)
    evidence_provided = Column(JSON, nullable=True, default=list)
    
    # Remediation
    gaps_identified = Column(JSON, nullable=True, default=list)
    remediation_plan = Column(Text, nullable=True)
    remediation_due_date = Column(DateTime, nullable=True)
    remediation_status = Column(String(50), nullable=True)
    
    # Metadata
    notes = Column(Text, nullable=True)
    attachments = Column(JSON, nullable=True, default=list)
    
    # Relationships
    requirement = relationship("ComplianceRequirement", back_populates="assessments")
    organization = relationship("EnterpriseOrganization")
    
    def __repr__(self):
        return f"<ComplianceAssessment {self.id} - {self.status.value}>"


class ComplianceReport(Base, TimestampMixin):
    """Model for generated compliance reports."""
    
    __tablename__ = 'compliance_reports'
    
    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    
    # Report details
    report_type = Column(SQLEnum(ComplianceFramework), nullable=False)
    report_name = Column(String(200), nullable=False)
    report_period_start = Column(DateTime, nullable=False)
    report_period_end = Column(DateTime, nullable=False)
    
    # Generation details
    generated_by = Column(String(255), nullable=False)
    generated_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    generation_method = Column(String(50), nullable=False, default='automated')
    
    # Report content
    executive_summary = Column(Text, nullable=True)
    overall_status = Column(SQLEnum(ComplianceStatus), nullable=False)
    overall_score = Column(Float, nullable=True)
    
    # Detailed findings
    compliant_requirements = Column(JSON, nullable=True, default=list)
    non_compliant_requirements = Column(JSON, nullable=True, default=list)
    partially_compliant_requirements = Column(JSON, nullable=True, default=list)
    
    # Risk and recommendations
    high_risk_findings = Column(JSON, nullable=True, default=list)
    recommendations = Column(JSON, nullable=True, default=list)
    action_items = Column(JSON, nullable=True, default=list)
    
    # Report metadata
    report_data = Column(JSON, nullable=True, default=dict)
    file_path = Column(String(500), nullable=True)
    file_format = Column(String(20), nullable=True, default='pdf')
    
    # Status
    status = Column(String(20), nullable=False, default='draft')  # draft, final, submitted
    submitted_at = Column(DateTime, nullable=True)
    submitted_to = Column(String(255), nullable=True)
    
    # Relationships
    organization = relationship("EnterpriseOrganization")
    
    def __repr__(self):
        return f"<ComplianceReport {self.report_type.value} - {self.report_name}>"


class AuditLog(Base, TimestampMixin):
    """Model for comprehensive audit logging."""
    
    __tablename__ = 'audit_logs'
    
    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    
    # Event details
    event_type = Column(SQLEnum(AuditEventType), nullable=False)
    event_category = Column(String(100), nullable=True)
    event_description = Column(Text, nullable=False)
    
    # User and session information
    user_id = Column(String(255), nullable=True)
    session_id = Column(String(255), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    
    # Resource information
    resource_type = Column(String(100), nullable=True)
    resource_id = Column(String(255), nullable=True)
    resource_name = Column(String(255), nullable=True)
    
    # Event context
    before_state = Column(JSON, nullable=True)
    after_state = Column(JSON, nullable=True)
    changes_made = Column(JSON, nullable=True, default=list)
    
    # Risk and compliance
    risk_level = Column(SQLEnum(RiskLevel), nullable=False, default=RiskLevel.LOW)
    compliance_relevant = Column(Boolean, nullable=False, default=False)
    retention_period_days = Column(Integer, nullable=False, default=2555)  # 7 years default
    
    # Metadata
    additional_data = Column(JSON, nullable=True, default=dict)
    tags = Column(JSON, nullable=True, default=list)
    
    # Relationships
    organization = relationship("EnterpriseOrganization")
    
    def __repr__(self):
        return f"<AuditLog {self.event_type.value} - {self.user_id}>"


class DataProcessingActivity(Base, TimestampMixin, SoftDeleteMixin):
    """Model for GDPR data processing activities register."""
    
    __tablename__ = 'data_processing_activities'
    
    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey('enterprise_organizations.id'), nullable=False)
    
    # Activity identification
    activity_name = Column(String(200), nullable=False)
    activity_description = Column(Text, nullable=False)
    purpose_of_processing = Column(Text, nullable=False)
    legal_basis = Column(String(100), nullable=False)
    
    # Data details
    data_categories = Column(JSON, nullable=False, default=list)
    data_subjects = Column(JSON, nullable=False, default=list)
    data_sources = Column(JSON, nullable=True, default=list)
    
    # Processing details
    processing_methods = Column(JSON, nullable=True, default=list)
    automated_decision_making = Column(Boolean, nullable=False, default=False)
    profiling_activities = Column(Boolean, nullable=False, default=False)
    
    # Data sharing and transfers
    recipients = Column(JSON, nullable=True, default=list)
    third_country_transfers = Column(JSON, nullable=True, default=list)
    safeguards_applied = Column(JSON, nullable=True, default=list)
    
    # Retention and security
    retention_period = Column(String(100), nullable=True)
    security_measures = Column(JSON, nullable=True, default=list)
    
    # Compliance tracking
    last_reviewed = Column(DateTime, nullable=True)
    next_review_due = Column(DateTime, nullable=True)
    dpia_required = Column(Boolean, nullable=False, default=False)
    dpia_completed = Column(Boolean, nullable=False, default=False)
    
    # Relationships
    organization = relationship("EnterpriseOrganization")
    
    def __repr__(self):
        return f"<DataProcessingActivity {self.activity_name}>"
