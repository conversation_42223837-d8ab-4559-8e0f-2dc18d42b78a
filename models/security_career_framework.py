"""Security Career Framework Models.

This module provides comprehensive models for security career paths, job types,
and seniority levels based on <PERSON>'s 8 security areas and industry
standards for cybersecurity career progression.
"""

from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, <PERSON>olean, Float, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum
from typing import List, Tuple
from models.base import Base, TimestampMixin


class SecurityArea(str, Enum):
    """The 8 core security areas from <PERSON>'s roadmap."""
    SECURITY_ENGINEERING = "Security Architecture and Engineering"
    DEFENSIVE_SECURITY = "Security Operations"
    SECURITY_MANAGEMENT = "Security and Risk Management"
    NETWORK_SECURITY = "Communication and Network Security"
    IDENTITY_ACCESS = "Identity and Access Management"
    ASSET_SECURITY = "Asset Security"
    SECURITY_TESTING = "Security Assessment and Testing"
    SOFTWARE_SECURITY = "Software Security"


class SeniorityLevel(str, Enum):
    """Comprehensive seniority levels for security careers."""
    BEGINNER = "beginner"           # 0-1 years, entry level
    INTERMEDIATE = "intermediate"   # 1-3 years, some experience
    ADVANCED = "advanced"          # 3-5 years, solid experience
    EXPERT = "expert"              # 5+ years, deep expertise
    SENIOR = "senior"              # 7+ years, leadership potential
    PRINCIPAL = "principal"        # 10+ years, technical leadership
    ARCHITECT = "architect"        # 12+ years, strategic design
    EXECUTIVE = "executive"        # 15+ years, organizational leadership


class JobFamily(str, Enum):
    """Job families within security areas."""
    # Security Engineering
    SECURITY_ARCHITECT = "security_architect"
    SECURITY_ENGINEER = "security_engineer"
    SYSTEMS_SECURITY = "systems_security"
    
    # Defensive Security / Security Operations
    SOC_ANALYST = "soc_analyst"
    INCIDENT_RESPONDER = "incident_responder"
    THREAT_HUNTER = "threat_hunter"
    SECURITY_OPERATIONS = "security_operations"
    
    # Security Management
    SECURITY_MANAGER = "security_manager"
    RISK_ANALYST = "risk_analyst"
    COMPLIANCE_OFFICER = "compliance_officer"
    CISO = "ciso"
    
    # Network Security
    NETWORK_SECURITY = "network_security"
    FIREWALL_ADMIN = "firewall_admin"
    NETWORK_ARCHITECT = "network_architect"
    
    # Identity and Access Management
    IAM_ENGINEER = "iam_engineer"
    IAM_ARCHITECT = "iam_architect"
    IDENTITY_ANALYST = "identity_analyst"
    
    # Asset Security
    ASSET_MANAGER = "asset_manager"
    DATA_PROTECTION = "data_protection"
    PRIVACY_OFFICER = "privacy_officer"
    
    # Security Testing
    PENETRATION_TESTER = "penetration_tester"
    VULNERABILITY_ASSESSOR = "vulnerability_assessor"
    SECURITY_RESEARCHER = "security_researcher"
    
    # Software Security
    APPSEC_ENGINEER = "appsec_engineer"
    DEVSECOPS_ENGINEER = "devsecops_engineer"
    SECURE_DEVELOPER = "secure_developer"


class SecurityJobType(Base, TimestampMixin):
    """Comprehensive security job types aligned with the 8 security areas."""
    
    __tablename__ = 'security_job_types'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    security_area = Column(String(100), nullable=False)  # Maps to SecurityArea enum
    job_family = Column(String(100), nullable=False)     # Maps to JobFamily enum
    seniority_level = Column(String(50), nullable=False) # Maps to SeniorityLevel enum
    
    # Job details
    description = Column(Text)
    responsibilities = Column(JSON, default=list)  # List of key responsibilities
    required_skills = Column(JSON, default=list)   # Required technical skills
    preferred_skills = Column(JSON, default=list)  # Preferred additional skills
    
    # Experience requirements
    min_years_experience = Column(Integer, default=0)
    max_years_experience = Column(Integer, nullable=True)
    
    # Education requirements
    education_requirements = Column(JSON, default=list)  # Degree requirements
    required_certifications = Column(JSON, default=list) # Must-have certifications
    preferred_certifications = Column(JSON, default=list) # Nice-to-have certifications
    
    # Compensation
    salary_min = Column(Float, nullable=True)
    salary_max = Column(Float, nullable=True)
    salary_currency = Column(String(3), default='USD')
    
    # Career progression
    career_progression_from = Column(JSON, default=list)  # Previous roles
    career_progression_to = Column(JSON, default=list)    # Next roles
    
    # Metadata
    is_active = Column(Boolean, default=True)
    demand_level = Column(String(20), default='medium')  # low, medium, high, critical
    remote_friendly = Column(Boolean, default=True)
    
    def to_dict(self):
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'title': self.title,
            'security_area': self.security_area,
            'job_family': self.job_family,
            'seniority_level': self.seniority_level,
            'description': self.description,
            'responsibilities': self.responsibilities,
            'required_skills': self.required_skills,
            'preferred_skills': self.preferred_skills,
            'min_years_experience': self.min_years_experience,
            'max_years_experience': self.max_years_experience,
            'education_requirements': self.education_requirements,
            'required_certifications': self.required_certifications,
            'preferred_certifications': self.preferred_certifications,
            'salary_range': {
                'min': self.salary_min,
                'max': self.salary_max,
                'currency': self.salary_currency
            },
            'career_progression': {
                'from': self.career_progression_from,
                'to': self.career_progression_to
            },
            'demand_level': self.demand_level,
            'remote_friendly': self.remote_friendly,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class SecurityCareerPath(Base, TimestampMixin):
    """Career progression paths within security areas."""
    
    __tablename__ = 'security_career_paths'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    security_area = Column(String(100), nullable=False)
    
    # Path definition
    entry_job_types = Column(JSON, default=list)      # Starting positions
    progression_stages = Column(JSON, default=list)   # Career stages
    terminal_job_types = Column(JSON, default=list)   # End positions
    
    # Requirements
    typical_duration_years = Column(Integer, default=10)
    required_certifications = Column(JSON, default=list)
    recommended_education = Column(JSON, default=list)
    
    # Success metrics
    success_rate = Column(Float, default=0.0)
    average_salary_growth = Column(Float, default=0.0)  # Percentage
    
    # Metadata
    is_active = Column(Boolean, default=True)
    popularity_score = Column(Float, default=0.0)


class SecuritySkillMatrix(Base, TimestampMixin):
    """Skills matrix for security roles and areas."""
    
    __tablename__ = 'security_skill_matrix'
    
    id = Column(Integer, primary_key=True)
    security_area = Column(String(100), nullable=False)
    job_family = Column(String(100), nullable=False)
    seniority_level = Column(String(50), nullable=False)
    
    # Technical skills
    core_skills = Column(JSON, default=list)           # Must-have technical skills
    advanced_skills = Column(JSON, default=list)       # Advanced technical skills
    leadership_skills = Column(JSON, default=list)     # Leadership/management skills
    business_skills = Column(JSON, default=list)       # Business acumen skills
    
    # Certifications by level
    entry_certifications = Column(JSON, default=list)      # Beginner certs
    intermediate_certifications = Column(JSON, default=list) # Intermediate certs
    advanced_certifications = Column(JSON, default=list)    # Advanced certs
    expert_certifications = Column(JSON, default=list)      # Expert certs
    
    # Tools and technologies
    required_tools = Column(JSON, default=list)
    preferred_tools = Column(JSON, default=list)
    
    def to_dict(self):
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'security_area': self.security_area,
            'job_family': self.job_family,
            'seniority_level': self.seniority_level,
            'skills': {
                'core': self.core_skills,
                'advanced': self.advanced_skills,
                'leadership': self.leadership_skills,
                'business': self.business_skills
            },
            'certifications': {
                'entry': self.entry_certifications,
                'intermediate': self.intermediate_certifications,
                'advanced': self.advanced_certifications,
                'expert': self.expert_certifications
            },
            'tools': {
                'required': self.required_tools,
                'preferred': self.preferred_tools
            }
        }


class SecurityMarketData(Base, TimestampMixin):
    """Market data for security roles and compensation."""
    
    __tablename__ = 'security_market_data'
    
    id = Column(Integer, primary_key=True)
    security_area = Column(String(100), nullable=False)
    job_family = Column(String(100), nullable=False)
    seniority_level = Column(String(50), nullable=False)
    
    # Geographic data
    country = Column(String(100), default='United States')
    region = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    
    # Market metrics
    average_salary = Column(Float, nullable=True)
    median_salary = Column(Float, nullable=True)
    salary_percentile_25 = Column(Float, nullable=True)
    salary_percentile_75 = Column(Float, nullable=True)
    salary_percentile_90 = Column(Float, nullable=True)
    
    # Job market data
    job_openings = Column(Integer, default=0)
    demand_score = Column(Float, default=0.0)  # 0-100 scale
    competition_score = Column(Float, default=0.0)  # 0-100 scale
    growth_rate = Column(Float, default=0.0)  # Annual growth percentage
    
    # Benefits and compensation
    typical_bonus_percentage = Column(Float, default=0.0)
    stock_options_common = Column(Boolean, default=False)
    remote_work_percentage = Column(Float, default=0.0)
    
    # Data source and freshness
    data_source = Column(String(100), nullable=True)
    data_collection_date = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'security_area': self.security_area,
            'job_family': self.job_family,
            'seniority_level': self.seniority_level,
            'location': {
                'country': self.country,
                'region': self.region,
                'city': self.city
            },
            'salary_data': {
                'average': self.average_salary,
                'median': self.median_salary,
                'percentile_25': self.salary_percentile_25,
                'percentile_75': self.salary_percentile_75,
                'percentile_90': self.salary_percentile_90
            },
            'market_metrics': {
                'job_openings': self.job_openings,
                'demand_score': self.demand_score,
                'competition_score': self.competition_score,
                'growth_rate': self.growth_rate
            },
            'compensation_details': {
                'typical_bonus_percentage': self.typical_bonus_percentage,
                'stock_options_common': self.stock_options_common,
                'remote_work_percentage': self.remote_work_percentage
            },
            'data_source': self.data_source,
            'data_collection_date': self.data_collection_date.isoformat() if self.data_collection_date else None
        }


# Helper functions for career framework

def get_job_types_by_area(security_area: str) -> List[str]:
    """Get all job families for a security area."""
    area_job_mapping = {
        SecurityArea.SECURITY_ENGINEERING: [
            JobFamily.SECURITY_ARCHITECT,
            JobFamily.SECURITY_ENGINEER,
            JobFamily.SYSTEMS_SECURITY
        ],
        SecurityArea.DEFENSIVE_SECURITY: [
            JobFamily.SOC_ANALYST,
            JobFamily.INCIDENT_RESPONDER,
            JobFamily.THREAT_HUNTER,
            JobFamily.SECURITY_OPERATIONS
        ],
        SecurityArea.SECURITY_MANAGEMENT: [
            JobFamily.SECURITY_MANAGER,
            JobFamily.RISK_ANALYST,
            JobFamily.COMPLIANCE_OFFICER,
            JobFamily.CISO
        ],
        SecurityArea.NETWORK_SECURITY: [
            JobFamily.NETWORK_SECURITY,
            JobFamily.FIREWALL_ADMIN,
            JobFamily.NETWORK_ARCHITECT
        ],
        SecurityArea.IDENTITY_ACCESS: [
            JobFamily.IAM_ENGINEER,
            JobFamily.IAM_ARCHITECT,
            JobFamily.IDENTITY_ANALYST
        ],
        SecurityArea.ASSET_SECURITY: [
            JobFamily.ASSET_MANAGER,
            JobFamily.DATA_PROTECTION,
            JobFamily.PRIVACY_OFFICER
        ],
        SecurityArea.SECURITY_TESTING: [
            JobFamily.PENETRATION_TESTER,
            JobFamily.VULNERABILITY_ASSESSOR,
            JobFamily.SECURITY_RESEARCHER
        ],
        SecurityArea.SOFTWARE_SECURITY: [
            JobFamily.APPSEC_ENGINEER,
            JobFamily.DEVSECOPS_ENGINEER,
            JobFamily.SECURE_DEVELOPER
        ]
    }
    
    return [job.value for job in area_job_mapping.get(security_area, [])]


def get_seniority_progression() -> List[str]:
    """Get the typical seniority progression order."""
    return [
        SeniorityLevel.BEGINNER.value,
        SeniorityLevel.INTERMEDIATE.value,
        SeniorityLevel.ADVANCED.value,
        SeniorityLevel.EXPERT.value,
        SeniorityLevel.SENIOR.value,
        SeniorityLevel.PRINCIPAL.value,
        SeniorityLevel.ARCHITECT.value,
        SeniorityLevel.EXECUTIVE.value
    ]


def get_years_experience_for_level(seniority_level: str) -> Tuple[int, int]:
    """Get typical years of experience for a seniority level."""
    experience_mapping = {
        SeniorityLevel.BEGINNER.value: (0, 1),
        SeniorityLevel.INTERMEDIATE.value: (1, 3),
        SeniorityLevel.ADVANCED.value: (3, 5),
        SeniorityLevel.EXPERT.value: (5, 7),
        SeniorityLevel.SENIOR.value: (7, 10),
        SeniorityLevel.PRINCIPAL.value: (10, 12),
        SeniorityLevel.ARCHITECT.value: (12, 15),
        SeniorityLevel.EXECUTIVE.value: (15, 25)
    }
    
    return experience_mapping.get(seniority_level, (0, 1))
