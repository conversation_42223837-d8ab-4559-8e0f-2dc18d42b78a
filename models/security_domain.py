"""Security domain model representing different areas of cybersecurity"""
from sqlalchemy import Column, Integer, String as SQLString, Text, DateTime, ForeignKey, CheckConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from models.mixins import SoftDeleteMixin
from models.certification import Base

class SecurityDomain(Base, SoftDeleteMixin):
    """Security domain model representing major areas like Asset Security, etc"""
    __tablename__ = 'security_domains'

    id = Column(Integer, primary_key=True)
    name = Column(SQLString(100), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Update relationship to use the correct foreign key from certification table
    certifications = relationship(
        "Certification",
        primaryjoin="and_(SecurityDomain.name==Certification.domain, SecurityDomain.is_deleted==False)",
        foreign_keys="Certification.domain",
        viewonly=True
    )

    # Database-level constraints for enterprise validation
    __table_args__ = (
        CheckConstraint('LENGTH(name) > 0', name='check_name_not_empty'),
    )

    def to_dict(self):
        """Convert domain to dictionary representation"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }