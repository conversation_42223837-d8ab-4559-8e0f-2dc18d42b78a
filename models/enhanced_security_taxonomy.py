"""Enhanced Security Taxonomy Models.

This module provides comprehensive security taxonomy models that capture
real-world job market data, skills, and career progression based on
industry research and job board analysis.
"""

from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, <PERSON><PERSON>an, Float, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any
from models.base import Base, TimestampMixin


class SecurityDomain(str, Enum):
    """Enhanced security domains based on industry analysis."""
    DEFENSIVE_SECURITY = "defensive_security"
    OFFENSIVE_SECURITY = "offensive_security"
    SECURITY_ENGINEERING = "security_engineering"
    SECURITY_MANAGEMENT = "security_management"
    NETWORK_SECURITY = "network_security"
    IDENTITY_ACCESS_MANAGEMENT = "identity_access_management"
    ASSET_SECURITY = "asset_security"
    SECURITY_TESTING = "security_testing"
    SOFTWARE_DEVELOPMENT_SECURITY = "software_development_security"
    CLOUD_SECURITY = "cloud_security"
    COMPLIANCE_GOVERNANCE = "compliance_governance"
    INCIDENT_RESPONSE = "incident_response"


class SkillCategory(str, Enum):
    """Categories of security skills."""
    TECHNICAL_CORE = "technical_core"
    TECHNICAL_ADVANCED = "technical_advanced"
    LEADERSHIP = "leadership"
    BUSINESS = "business"
    COMMUNICATION = "communication"
    ANALYTICAL = "analytical"
    COMPLIANCE = "compliance"
    VENDOR_SPECIFIC = "vendor_specific"


class SkillLevel(str, Enum):
    """Skill proficiency levels."""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"
    THOUGHT_LEADER = "thought_leader"


class CertificationLevel(str, Enum):
    """Certification difficulty and recognition levels."""
    ENTRY = "entry"
    ASSOCIATE = "associate"
    PROFESSIONAL = "professional"
    EXPERT = "expert"
    ARCHITECT = "architect"
    SPECIALIST = "specialist"


class SecuritySkillTaxonomy(Base, TimestampMixin):
    """Comprehensive security skills taxonomy."""
    
    __tablename__ = 'security_skill_taxonomy'
    
    id = Column(Integer, primary_key=True)
    skill_name = Column(String(200), nullable=False, unique=True)
    skill_category = Column(String(50), nullable=False)  # Maps to SkillCategory
    security_domains = Column(JSON, default=list)  # List of applicable domains
    
    # Skill details
    description = Column(Text)
    aliases = Column(JSON, default=list)  # Alternative names for the skill
    related_skills = Column(JSON, default=list)  # Related/prerequisite skills
    
    # Market data
    demand_score = Column(Float, default=0.0)  # 0-100 market demand
    salary_impact = Column(Float, default=0.0)  # Percentage salary increase
    job_frequency = Column(Integer, default=0)  # How often it appears in job postings
    
    # Learning resources
    learning_resources = Column(JSON, default=list)  # URLs, courses, books
    practice_platforms = Column(JSON, default=list)  # Labs, simulators
    
    # Metadata
    is_trending = Column(Boolean, default=False)
    is_emerging = Column(Boolean, default=False)
    obsolescence_risk = Column(String(20), default='low')  # low, medium, high
    
    def to_dict(self):
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'skill_name': self.skill_name,
            'skill_category': self.skill_category,
            'security_domains': self.security_domains,
            'description': self.description,
            'aliases': self.aliases,
            'related_skills': self.related_skills,
            'market_data': {
                'demand_score': self.demand_score,
                'salary_impact': self.salary_impact,
                'job_frequency': self.job_frequency
            },
            'learning_resources': self.learning_resources,
            'practice_platforms': self.practice_platforms,
            'metadata': {
                'is_trending': self.is_trending,
                'is_emerging': self.is_emerging,
                'obsolescence_risk': self.obsolescence_risk
            }
        }


class SecurityCertificationTaxonomy(Base, TimestampMixin):
    """Comprehensive security certifications taxonomy."""
    
    __tablename__ = 'security_certification_taxonomy'
    
    id = Column(Integer, primary_key=True)
    certification_name = Column(String(200), nullable=False)
    certification_code = Column(String(50), nullable=False, unique=True)
    issuing_organization = Column(String(200), nullable=False)
    certification_level = Column(String(50), nullable=False)  # Maps to CertificationLevel
    
    # Certification details
    description = Column(Text)
    security_domains = Column(JSON, default=list)  # Applicable domains
    prerequisites = Column(JSON, default=list)  # Required certs or experience
    
    # Exam details
    exam_cost = Column(Float, nullable=True)
    exam_duration_hours = Column(Float, nullable=True)
    passing_score = Column(Integer, nullable=True)
    exam_format = Column(String(100), nullable=True)  # multiple-choice, hands-on, etc.
    
    # Validity and maintenance
    validity_years = Column(Integer, default=3)
    cpe_requirements = Column(Integer, default=0)  # Continuing education hours
    renewal_cost = Column(Float, nullable=True)
    
    # Market data
    holder_count = Column(Integer, default=0)  # Estimated number of holders
    salary_premium = Column(Float, default=0.0)  # Average salary increase
    job_requirement_frequency = Column(Float, default=0.0)  # % of jobs requiring it
    
    # Career impact
    career_paths = Column(JSON, default=list)  # Job roles this cert enables
    next_certifications = Column(JSON, default=list)  # Logical next certs
    
    # Study resources
    official_training = Column(JSON, default=list)  # Official courses
    study_materials = Column(JSON, default=list)  # Books, videos, etc.
    practice_exams = Column(JSON, default=list)  # Practice test resources
    
    def to_dict(self):
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'certification_name': self.certification_name,
            'certification_code': self.certification_code,
            'issuing_organization': self.issuing_organization,
            'certification_level': self.certification_level,
            'description': self.description,
            'security_domains': self.security_domains,
            'prerequisites': self.prerequisites,
            'exam_details': {
                'cost': self.exam_cost,
                'duration_hours': self.exam_duration_hours,
                'passing_score': self.passing_score,
                'format': self.exam_format
            },
            'maintenance': {
                'validity_years': self.validity_years,
                'cpe_requirements': self.cpe_requirements,
                'renewal_cost': self.renewal_cost
            },
            'market_data': {
                'holder_count': self.holder_count,
                'salary_premium': self.salary_premium,
                'job_requirement_frequency': self.job_requirement_frequency
            },
            'career_impact': {
                'career_paths': self.career_paths,
                'next_certifications': self.next_certifications
            },
            'study_resources': {
                'official_training': self.official_training,
                'study_materials': self.study_materials,
                'practice_exams': self.practice_exams
            }
        }


class JobTitleTaxonomy(Base, TimestampMixin):
    """Comprehensive job title taxonomy based on market research."""
    
    __tablename__ = 'job_title_taxonomy'
    
    id = Column(Integer, primary_key=True)
    job_title = Column(String(200), nullable=False)
    normalized_title = Column(String(200), nullable=False)  # Standardized version
    security_domain = Column(String(50), nullable=False)  # Primary domain
    seniority_level = Column(String(50), nullable=False)
    
    # Title variations
    title_variations = Column(JSON, default=list)  # Alternative titles
    company_specific_titles = Column(JSON, default=list)  # Company-specific variations
    
    # Job details
    description = Column(Text)
    key_responsibilities = Column(JSON, default=list)
    required_skills = Column(JSON, default=list)
    preferred_skills = Column(JSON, default=list)
    required_certifications = Column(JSON, default=list)
    preferred_certifications = Column(JSON, default=list)
    
    # Market data
    job_posting_frequency = Column(Integer, default=0)  # Monthly job postings
    average_salary = Column(Float, nullable=True)
    salary_range_min = Column(Float, nullable=True)
    salary_range_max = Column(Float, nullable=True)
    remote_work_percentage = Column(Float, default=0.0)
    
    # Career progression
    entry_level_roles = Column(JSON, default=list)  # Roles that lead to this
    advancement_roles = Column(JSON, default=list)  # Roles this leads to
    lateral_move_roles = Column(JSON, default=list)  # Similar level roles
    
    # Geographic data
    top_locations = Column(JSON, default=list)  # Cities with most opportunities
    industry_distribution = Column(JSON, default=list)  # Industries hiring
    
    def to_dict(self):
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'job_title': self.job_title,
            'normalized_title': self.normalized_title,
            'security_domain': self.security_domain,
            'seniority_level': self.seniority_level,
            'title_variations': self.title_variations,
            'company_specific_titles': self.company_specific_titles,
            'description': self.description,
            'requirements': {
                'key_responsibilities': self.key_responsibilities,
                'required_skills': self.required_skills,
                'preferred_skills': self.preferred_skills,
                'required_certifications': self.required_certifications,
                'preferred_certifications': self.preferred_certifications
            },
            'market_data': {
                'job_posting_frequency': self.job_posting_frequency,
                'average_salary': self.average_salary,
                'salary_range': {
                    'min': self.salary_range_min,
                    'max': self.salary_range_max
                },
                'remote_work_percentage': self.remote_work_percentage
            },
            'career_progression': {
                'entry_level_roles': self.entry_level_roles,
                'advancement_roles': self.advancement_roles,
                'lateral_move_roles': self.lateral_move_roles
            },
            'geographic_data': {
                'top_locations': self.top_locations,
                'industry_distribution': self.industry_distribution
            }
        }


class SecurityTechnologyTaxonomy(Base, TimestampMixin):
    """Taxonomy of security technologies, tools, and platforms."""
    
    __tablename__ = 'security_technology_taxonomy'
    
    id = Column(Integer, primary_key=True)
    technology_name = Column(String(200), nullable=False)
    technology_category = Column(String(100), nullable=False)  # SIEM, Firewall, etc.
    vendor = Column(String(200), nullable=True)
    security_domains = Column(JSON, default=list)
    
    # Technology details
    description = Column(Text)
    use_cases = Column(JSON, default=list)
    deployment_models = Column(JSON, default=list)  # on-prem, cloud, hybrid
    
    # Market data
    market_share = Column(Float, default=0.0)  # Percentage market share
    adoption_rate = Column(Float, default=0.0)  # Growth rate
    job_demand = Column(Integer, default=0)  # Jobs requiring this tech
    
    # Learning resources
    official_training = Column(JSON, default=list)
    certifications = Column(JSON, default=list)  # Related certifications
    hands_on_labs = Column(JSON, default=list)
    
    # Competitive landscape
    competitors = Column(JSON, default=list)
    integration_partners = Column(JSON, default=list)
    
    def to_dict(self):
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'technology_name': self.technology_name,
            'technology_category': self.technology_category,
            'vendor': self.vendor,
            'security_domains': self.security_domains,
            'description': self.description,
            'use_cases': self.use_cases,
            'deployment_models': self.deployment_models,
            'market_data': {
                'market_share': self.market_share,
                'adoption_rate': self.adoption_rate,
                'job_demand': self.job_demand
            },
            'learning_resources': {
                'official_training': self.official_training,
                'certifications': self.certifications,
                'hands_on_labs': self.hands_on_labs
            },
            'competitive_landscape': {
                'competitors': self.competitors,
                'integration_partners': self.integration_partners
            }
        }
