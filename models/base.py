"""Base models and mixins for the application.

This module provides base classes and mixins that are used across
all models in the application for consistent behavior.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, DateTime, Boolean
from sqlalchemy.sql import func
from database import Base


class TimestampMixin:
    """Mixin to add created_at and updated_at timestamps to models."""
    
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())


class SoftDeleteMixin:
    """Mixin to add soft delete functionality to models."""
    
    deleted_at = Column(DateTime, nullable=True)
    is_deleted = Column(Boolean, nullable=False, default=False)
    
    def soft_delete(self):
        """Mark the record as deleted."""
        self.is_deleted = True
        self.deleted_at = func.now()
    
    def restore(self):
        """Restore a soft-deleted record."""
        self.is_deleted = False
        self.deleted_at = None


# Export Base from database module for convenience
__all__ = ['Base', 'TimestampMixin', 'SoftDeleteMixin']
