#!/usr/bin/env python3
"""
Simple API runner for development with SQLite database
"""
import os
import sys
import uvicorn
from pathlib import Path

# Force SQLite database
os.environ['DATABASE_URL'] = 'sqlite:///./certpathfinder.db'

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set other environment variables
os.environ['ENVIRONMENT'] = 'development'
os.environ['SECRET_KEY'] = 'dev-secret-key-change-in-production'
os.environ['CORS_ORIGINS'] = 'http://app.certrats.localhost,http://localhost:3000'

if __name__ == "__main__":
    print("🚀 Starting CertRats API Server")
    print("================================")
    print(f"Database: {os.environ['DATABASE_URL']}")
    print(f"Environment: {os.environ['ENVIRONMENT']}")
    print("API will be available at: http://localhost:8000")
    print("API docs will be available at: http://localhost:8000/docs")
    print("")
    
    # Import and run the app
    try:
        from api.app import create_app
        app = create_app()
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except ImportError as e:
        print(f"Error importing API app: {e}")
        print("Make sure all dependencies are installed")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting API server: {e}")
        sys.exit(1)
