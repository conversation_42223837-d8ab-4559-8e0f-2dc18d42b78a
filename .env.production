# Production Environment Configuration for Traefik Deployment
DATABASE_URL=**************************************/certpathfinder
SECRET_KEY=production-secret-key-change-this-in-real-deployment
ENVIRONMENT=production
DEBUG=False

# API Configuration
API_V1_PREFIX=/api/v1
CORS_ORIGINS=https://app.certrats.localhost,https://admin.certrats.localhost,https://docs.certrats.localhost

# Redis Configuration
REDIS_URL=redis://redis:6379

# External Services (configure with real keys in production)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
OPENAI_API_KEY=your-openai-api-key-here

# Email Configuration
EMAIL_ENABLED=true
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Security
ALLOWED_HOSTS=api.certrats.localhost,app.certrats.localhost
SECURE_COOKIES=true
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Application Settings
VERSION=1.0.0
MAX_UPLOAD_SIZE=10485760  # 10MB
RATE_LIMIT_PER_MINUTE=100

# Database Connection Pool
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Cache Settings
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PASSWORD_RESET=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_ANALYTICS=true
