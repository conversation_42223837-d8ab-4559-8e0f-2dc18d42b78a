"""Create security career framework tables and populate with <PERSON>'s 8 areas.

Revision ID: 008_security_career_framework
Revises: 007_previous_migration
Create Date: 2024-01-15 14:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from datetime import datetime

# revision identifiers
revision = '008_security_career_framework'
down_revision = '005_progress_tracking'
branch_labels = None
depends_on = None


def upgrade():
    """Create security career framework tables."""
    
    # Create security_job_types table
    op.create_table(
        'security_job_types',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(200), nullable=False),
        sa.Column('security_area', sa.String(100), nullable=False),
        sa.Column('job_family', sa.String(100), nullable=False),
        sa.Column('seniority_level', sa.String(50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.<PERSON>('responsibilities', sa.JSO<PERSON>(), nullable=True),
        sa.Column('required_skills', sa.J<PERSON>(), nullable=True),
        sa.Column('preferred_skills', sa.JSON(), nullable=True),
        sa.Column('min_years_experience', sa.Integer(), nullable=True, default=0),
        sa.Column('max_years_experience', sa.Integer(), nullable=True),
        sa.Column('education_requirements', sa.JSON(), nullable=True),
        sa.Column('required_certifications', sa.JSON(), nullable=True),
        sa.Column('preferred_certifications', sa.JSON(), nullable=True),
        sa.Column('salary_min', sa.Float(), nullable=True),
        sa.Column('salary_max', sa.Float(), nullable=True),
        sa.Column('salary_currency', sa.String(3), nullable=False, default='USD'),
        sa.Column('career_progression_from', sa.JSON(), nullable=True),
        sa.Column('career_progression_to', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('demand_level', sa.String(20), nullable=False, default='medium'),
        sa.Column('remote_friendly', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create security_career_paths table
    op.create_table(
        'security_career_paths',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('security_area', sa.String(100), nullable=False),
        sa.Column('entry_job_types', sa.JSON(), nullable=True),
        sa.Column('progression_stages', sa.JSON(), nullable=True),
        sa.Column('terminal_job_types', sa.JSON(), nullable=True),
        sa.Column('typical_duration_years', sa.Integer(), nullable=False, default=10),
        sa.Column('required_certifications', sa.JSON(), nullable=True),
        sa.Column('recommended_education', sa.JSON(), nullable=True),
        sa.Column('success_rate', sa.Float(), nullable=False, default=0.0),
        sa.Column('average_salary_growth', sa.Float(), nullable=False, default=0.0),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('popularity_score', sa.Float(), nullable=False, default=0.0),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create security_skill_matrix table
    op.create_table(
        'security_skill_matrix',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('security_area', sa.String(100), nullable=False),
        sa.Column('job_family', sa.String(100), nullable=False),
        sa.Column('seniority_level', sa.String(50), nullable=False),
        sa.Column('core_skills', sa.JSON(), nullable=True),
        sa.Column('advanced_skills', sa.JSON(), nullable=True),
        sa.Column('leadership_skills', sa.JSON(), nullable=True),
        sa.Column('business_skills', sa.JSON(), nullable=True),
        sa.Column('entry_certifications', sa.JSON(), nullable=True),
        sa.Column('intermediate_certifications', sa.JSON(), nullable=True),
        sa.Column('advanced_certifications', sa.JSON(), nullable=True),
        sa.Column('expert_certifications', sa.JSON(), nullable=True),
        sa.Column('required_tools', sa.JSON(), nullable=True),
        sa.Column('preferred_tools', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('security_area', 'job_family', 'seniority_level', name='uix_skill_matrix')
    )
    
    # Create security_market_data table
    op.create_table(
        'security_market_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('security_area', sa.String(100), nullable=False),
        sa.Column('job_family', sa.String(100), nullable=False),
        sa.Column('seniority_level', sa.String(50), nullable=False),
        sa.Column('country', sa.String(100), nullable=False, default='United States'),
        sa.Column('region', sa.String(100), nullable=True),
        sa.Column('city', sa.String(100), nullable=True),
        sa.Column('average_salary', sa.Float(), nullable=True),
        sa.Column('median_salary', sa.Float(), nullable=True),
        sa.Column('salary_percentile_25', sa.Float(), nullable=True),
        sa.Column('salary_percentile_75', sa.Float(), nullable=True),
        sa.Column('salary_percentile_90', sa.Float(), nullable=True),
        sa.Column('job_openings', sa.Integer(), nullable=False, default=0),
        sa.Column('demand_score', sa.Float(), nullable=False, default=0.0),
        sa.Column('competition_score', sa.Float(), nullable=False, default=0.0),
        sa.Column('growth_rate', sa.Float(), nullable=False, default=0.0),
        sa.Column('typical_bonus_percentage', sa.Float(), nullable=False, default=0.0),
        sa.Column('stock_options_common', sa.Boolean(), nullable=False, default=False),
        sa.Column('remote_work_percentage', sa.Float(), nullable=False, default=0.0),
        sa.Column('data_source', sa.String(100), nullable=True),
        sa.Column('data_collection_date', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for better performance
    op.create_index('ix_job_types_security_area', 'security_job_types', ['security_area'])
    op.create_index('ix_job_types_job_family', 'security_job_types', ['job_family'])
    op.create_index('ix_job_types_seniority', 'security_job_types', ['seniority_level'])
    op.create_index('ix_career_paths_area', 'security_career_paths', ['security_area'])
    op.create_index('ix_market_data_area_family', 'security_market_data', ['security_area', 'job_family'])
    
    # Populate with sample data for the 8 security areas
    populate_security_job_types()
    populate_security_career_paths()
    populate_security_skill_matrix()


def populate_security_job_types():
    """Populate security job types for the 8 areas."""
    
    # Sample job types for each security area and seniority level
    job_types_data = [
        # Security Architecture and Engineering
        {
            'title': 'Junior Security Engineer',
            'security_area': 'Security Architecture and Engineering',
            'job_family': 'security_engineer',
            'seniority_level': 'beginner',
            'description': 'Entry-level security engineer focusing on implementing security controls and basic architecture.',
            'responsibilities': ['Implement security controls', 'Monitor security systems', 'Document security procedures'],
            'required_skills': ['Network security basics', 'Operating systems', 'Security tools'],
            'min_years_experience': 0,
            'max_years_experience': 2,
            'required_certifications': ['Security+', 'Network+'],
            'preferred_certifications': ['GSEC', 'CySA+'],
            'salary_min': 65000,
            'salary_max': 85000,
            'demand_level': 'high'
        },
        {
            'title': 'Security Architect',
            'security_area': 'Security Architecture and Engineering',
            'job_family': 'security_architect',
            'seniority_level': 'expert',
            'description': 'Senior security architect designing enterprise security architecture and frameworks.',
            'responsibilities': ['Design security architecture', 'Lead security initiatives', 'Mentor junior staff'],
            'required_skills': ['Enterprise architecture', 'Security frameworks', 'Risk assessment'],
            'min_years_experience': 7,
            'max_years_experience': 12,
            'required_certifications': ['CISSP', 'SABSA'],
            'preferred_certifications': ['TOGAF', 'CISSP Concentrations'],
            'salary_min': 140000,
            'salary_max': 180000,
            'demand_level': 'critical'
        },
        
        # Security Operations / Defensive Security
        {
            'title': 'SOC Analyst I',
            'security_area': 'Security Operations',
            'job_family': 'soc_analyst',
            'seniority_level': 'beginner',
            'description': 'Entry-level SOC analyst monitoring security events and responding to incidents.',
            'responsibilities': ['Monitor SIEM alerts', 'Perform initial triage', 'Document incidents'],
            'required_skills': ['SIEM tools', 'Log analysis', 'Incident response basics'],
            'min_years_experience': 0,
            'max_years_experience': 2,
            'required_certifications': ['Security+'],
            'preferred_certifications': ['CySA+', 'GCIH'],
            'salary_min': 55000,
            'salary_max': 75000,
            'demand_level': 'high'
        },
        {
            'title': 'Senior Threat Hunter',
            'security_area': 'Security Operations',
            'job_family': 'threat_hunter',
            'seniority_level': 'expert',
            'description': 'Advanced threat hunter proactively searching for advanced persistent threats.',
            'responsibilities': ['Proactive threat hunting', 'Develop hunting hypotheses', 'Advanced malware analysis'],
            'required_skills': ['Threat intelligence', 'Malware analysis', 'Advanced persistent threats'],
            'min_years_experience': 5,
            'max_years_experience': 10,
            'required_certifications': ['GCTI', 'GCFA'],
            'preferred_certifications': ['GREM', 'GNFA'],
            'salary_min': 120000,
            'salary_max': 160000,
            'demand_level': 'critical'
        },
        
        # Security and Risk Management
        {
            'title': 'Risk Analyst',
            'security_area': 'Security and Risk Management',
            'job_family': 'risk_analyst',
            'seniority_level': 'intermediate',
            'description': 'Risk analyst conducting security risk assessments and developing mitigation strategies.',
            'responsibilities': ['Conduct risk assessments', 'Develop risk mitigation plans', 'Risk reporting'],
            'required_skills': ['Risk assessment frameworks', 'Compliance standards', 'Business analysis'],
            'min_years_experience': 2,
            'max_years_experience': 5,
            'required_certifications': ['CRISC', 'CISA'],
            'preferred_certifications': ['CISSP', 'CGRC'],
            'salary_min': 85000,
            'salary_max': 110000,
            'demand_level': 'medium'
        },
        {
            'title': 'Chief Information Security Officer (CISO)',
            'security_area': 'Security and Risk Management',
            'job_family': 'ciso',
            'seniority_level': 'executive',
            'description': 'Executive-level CISO responsible for organizational security strategy and governance.',
            'responsibilities': ['Security strategy', 'Board reporting', 'Security governance', 'Team leadership'],
            'required_skills': ['Executive leadership', 'Security strategy', 'Business acumen', 'Risk management'],
            'min_years_experience': 15,
            'max_years_experience': 25,
            'required_certifications': ['CISSP', 'CISM'],
            'preferred_certifications': ['CCISO', 'MBA'],
            'salary_min': 250000,
            'salary_max': 400000,
            'demand_level': 'critical'
        },
        
        # Security Assessment and Testing
        {
            'title': 'Penetration Tester',
            'security_area': 'Security Assessment and Testing',
            'job_family': 'penetration_tester',
            'seniority_level': 'intermediate',
            'description': 'Penetration tester conducting security assessments and vulnerability testing.',
            'responsibilities': ['Conduct penetration tests', 'Vulnerability assessments', 'Security reporting'],
            'required_skills': ['Penetration testing', 'Vulnerability assessment', 'Security tools'],
            'min_years_experience': 2,
            'max_years_experience': 5,
            'required_certifications': ['OSCP', 'GPEN'],
            'preferred_certifications': ['OSCE', 'GWAPT'],
            'salary_min': 90000,
            'salary_max': 120000,
            'demand_level': 'high'
        },
        
        # Software Security
        {
            'title': 'Application Security Engineer',
            'security_area': 'Software Security',
            'job_family': 'appsec_engineer',
            'seniority_level': 'advanced',
            'description': 'Application security engineer focusing on secure software development and testing.',
            'responsibilities': ['Secure code review', 'Application security testing', 'Developer training'],
            'required_skills': ['Secure coding', 'Application security testing', 'SAST/DAST tools'],
            'min_years_experience': 3,
            'max_years_experience': 7,
            'required_certifications': ['CSSLP', 'GWEB'],
            'preferred_certifications': ['OSWE', 'CASE'],
            'salary_min': 110000,
            'salary_max': 140000,
            'demand_level': 'high'
        }
    ]
    
    # Insert job types data
    for job_data in job_types_data:
        op.execute(f"""
            INSERT INTO security_job_types (
                title, security_area, job_family, seniority_level, description,
                responsibilities, required_skills, min_years_experience, max_years_experience,
                required_certifications, preferred_certifications, salary_min, salary_max,
                demand_level, created_at, updated_at
            ) VALUES (
                '{job_data['title']}',
                '{job_data['security_area']}',
                '{job_data['job_family']}',
                '{job_data['seniority_level']}',
                '{job_data['description']}',
                '{json.dumps(job_data['responsibilities'])}',
                '{json.dumps(job_data['required_skills'])}',
                {job_data['min_years_experience']},
                {job_data['max_years_experience']},
                '{json.dumps(job_data['required_certifications'])}',
                '{json.dumps(job_data['preferred_certifications'])}',
                {job_data['salary_min']},
                {job_data['salary_max']},
                '{job_data['demand_level']}',
                NOW(),
                NOW()
            )
        """)


def populate_security_career_paths():
    """Populate career paths for the 8 security areas."""
    
    career_paths = [
        {
            'name': 'Security Engineering Career Path',
            'description': 'Career progression from junior security engineer to security architect',
            'security_area': 'Security Architecture and Engineering',
            'entry_job_types': ['security_engineer'],
            'progression_stages': [
                {'level': 'beginner', 'duration_years': 2},
                {'level': 'intermediate', 'duration_years': 3},
                {'level': 'advanced', 'duration_years': 3},
                {'level': 'expert', 'duration_years': 4}
            ],
            'terminal_job_types': ['security_architect', 'principal_engineer'],
            'typical_duration_years': 12,
            'required_certifications': ['Security+', 'CISSP', 'SABSA'],
            'success_rate': 0.75,
            'average_salary_growth': 120.0
        },
        {
            'name': 'SOC Analyst Career Path',
            'description': 'Career progression from SOC analyst to security operations manager',
            'security_area': 'Security Operations',
            'entry_job_types': ['soc_analyst'],
            'progression_stages': [
                {'level': 'beginner', 'duration_years': 2},
                {'level': 'intermediate', 'duration_years': 2},
                {'level': 'advanced', 'duration_years': 3},
                {'level': 'expert', 'duration_years': 3}
            ],
            'terminal_job_types': ['security_operations_manager', 'threat_hunter'],
            'typical_duration_years': 10,
            'required_certifications': ['Security+', 'CySA+', 'GCIH'],
            'success_rate': 0.80,
            'average_salary_growth': 100.0
        }
    ]
    
    for path_data in career_paths:
        op.execute(f"""
            INSERT INTO security_career_paths (
                name, description, security_area, entry_job_types, progression_stages,
                terminal_job_types, typical_duration_years, required_certifications,
                success_rate, average_salary_growth, created_at, updated_at
            ) VALUES (
                '{path_data['name']}',
                '{path_data['description']}',
                '{path_data['security_area']}',
                '{json.dumps(path_data['entry_job_types'])}',
                '{json.dumps(path_data['progression_stages'])}',
                '{json.dumps(path_data['terminal_job_types'])}',
                {path_data['typical_duration_years']},
                '{json.dumps(path_data['required_certifications'])}',
                {path_data['success_rate']},
                {path_data['average_salary_growth']},
                NOW(),
                NOW()
            )
        """)


def populate_security_skill_matrix():
    """Populate skill matrix for security roles."""
    
    skill_matrix_data = [
        {
            'security_area': 'Security Architecture and Engineering',
            'job_family': 'security_engineer',
            'seniority_level': 'beginner',
            'core_skills': ['Network security', 'Operating systems', 'Security tools'],
            'entry_certifications': ['Security+', 'Network+'],
            'intermediate_certifications': ['GSEC', 'CySA+'],
            'required_tools': ['Firewalls', 'SIEM', 'Vulnerability scanners']
        },
        {
            'security_area': 'Security Operations',
            'job_family': 'soc_analyst',
            'seniority_level': 'beginner',
            'core_skills': ['SIEM analysis', 'Log analysis', 'Incident response'],
            'entry_certifications': ['Security+'],
            'intermediate_certifications': ['CySA+', 'GCIH'],
            'required_tools': ['Splunk', 'QRadar', 'ArcSight']
        }
    ]
    
    for skill_data in skill_matrix_data:
        op.execute(f"""
            INSERT INTO security_skill_matrix (
                security_area, job_family, seniority_level, core_skills,
                entry_certifications, intermediate_certifications, required_tools,
                created_at, updated_at
            ) VALUES (
                '{skill_data['security_area']}',
                '{skill_data['job_family']}',
                '{skill_data['seniority_level']}',
                '{json.dumps(skill_data['core_skills'])}',
                '{json.dumps(skill_data['entry_certifications'])}',
                '{json.dumps(skill_data['intermediate_certifications'])}',
                '{json.dumps(skill_data['required_tools'])}',
                NOW(),
                NOW()
            )
        """)


def downgrade():
    """Drop security career framework tables."""
    op.drop_index('ix_market_data_area_family', 'security_market_data')
    op.drop_index('ix_career_paths_area', 'security_career_paths')
    op.drop_index('ix_job_types_seniority', 'security_job_types')
    op.drop_index('ix_job_types_job_family', 'security_job_types')
    op.drop_index('ix_job_types_security_area', 'security_job_types')
    
    op.drop_table('security_market_data')
    op.drop_table('security_skill_matrix')
    op.drop_table('security_career_paths')
    op.drop_table('security_job_types')
