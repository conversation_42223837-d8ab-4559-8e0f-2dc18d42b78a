"""Add study timer models

Revision ID: 001_study_timer
Revises: 
Create Date: 2024-01-15 10:00:00.000000

This migration adds the study timer functionality including:
- StudySession model for tracking individual study sessions
- StudyGoal model for user study goals and targets
- StudyStreak model for tracking study streaks and achievements
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_study_timer'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Create study timer tables."""
    
    # Create study_sessions table
    op.create_table(
        'study_sessions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('certification_id', sa.Integer(), nullable=True),
        sa.Column('learning_path_item_id', sa.Integer(), nullable=True),
        sa.Column('start_time', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('end_time', sa.DateTime(), nullable=True),
        sa.Column('planned_duration_minutes', sa.Integer(), nullable=False, default=60),
        sa.Column('actual_duration_minutes', sa.Integer(), nullable=True),
        sa.Column('session_type', sa.String(50), nullable=False, default='study'),
        sa.Column('topic', sa.String(200), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('progress_percentage', sa.Float(), nullable=True),
        sa.Column('focus_rating', sa.Integer(), nullable=True),
        sa.Column('difficulty_rating', sa.Integer(), nullable=True),
        sa.Column('satisfaction_rating', sa.Integer(), nullable=True),
        sa.Column('status', sa.String(20), nullable=False, default='planned'),
        sa.Column('is_break', sa.Boolean(), nullable=False, default=False),
        sa.Column('break_duration_minutes', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['certification_id'], ['certifications.id'], ),
        sa.ForeignKeyConstraint(['learning_path_item_id'], ['learning_path_items.id'], )
    )
    
    # Create indexes for study_sessions
    op.create_index('ix_study_sessions_user_id', 'study_sessions', ['user_id'])
    op.create_index('ix_study_sessions_status', 'study_sessions', ['status'])
    op.create_index('ix_study_sessions_start_time', 'study_sessions', ['start_time'])
    op.create_index('ix_study_sessions_certification_id', 'study_sessions', ['certification_id'])
    
    # Create study_goals table
    op.create_table(
        'study_goals',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('certification_id', sa.Integer(), nullable=True),
        sa.Column('goal_type', sa.String(50), nullable=False),
        sa.Column('title', sa.String(200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('target_value', sa.Float(), nullable=False),
        sa.Column('target_date', sa.DateTime(), nullable=True),
        sa.Column('current_value', sa.Float(), nullable=False, default=0.0),
        sa.Column('is_achieved', sa.Boolean(), nullable=False, default=False),
        sa.Column('achievement_date', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('reminder_enabled', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['certification_id'], ['certifications.id'], )
    )
    
    # Create indexes for study_goals
    op.create_index('ix_study_goals_user_id', 'study_goals', ['user_id'])
    op.create_index('ix_study_goals_goal_type', 'study_goals', ['goal_type'])
    op.create_index('ix_study_goals_is_active', 'study_goals', ['is_active'])
    op.create_index('ix_study_goals_target_date', 'study_goals', ['target_date'])
    
    # Create study_streaks table
    op.create_table(
        'study_streaks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False, unique=True),
        sa.Column('current_streak_days', sa.Integer(), nullable=False, default=0),
        sa.Column('current_streak_start', sa.DateTime(), nullable=True),
        sa.Column('last_study_date', sa.DateTime(), nullable=True),
        sa.Column('longest_streak_days', sa.Integer(), nullable=False, default=0),
        sa.Column('longest_streak_start', sa.DateTime(), nullable=True),
        sa.Column('longest_streak_end', sa.DateTime(), nullable=True),
        sa.Column('total_study_days', sa.Integer(), nullable=False, default=0),
        sa.Column('total_study_minutes', sa.Integer(), nullable=False, default=0),
        sa.Column('total_sessions', sa.Integer(), nullable=False, default=0),
        sa.Column('achievements', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id')
    )
    
    # Create indexes for study_streaks
    op.create_index('ix_study_streaks_user_id', 'study_streaks', ['user_id'])
    op.create_index('ix_study_streaks_current_streak', 'study_streaks', ['current_streak_days'])
    op.create_index('ix_study_streaks_longest_streak', 'study_streaks', ['longest_streak_days'])
    
    # Add check constraints for data validation
    op.create_check_constraint(
        'ck_study_sessions_planned_duration_positive',
        'study_sessions',
        'planned_duration_minutes > 0'
    )
    
    op.create_check_constraint(
        'ck_study_sessions_actual_duration_positive',
        'study_sessions',
        'actual_duration_minutes IS NULL OR actual_duration_minutes >= 0'
    )
    
    op.create_check_constraint(
        'ck_study_sessions_progress_percentage_valid',
        'study_sessions',
        'progress_percentage IS NULL OR (progress_percentage >= 0 AND progress_percentage <= 100)'
    )
    
    op.create_check_constraint(
        'ck_study_sessions_rating_valid',
        'study_sessions',
        '''
        (focus_rating IS NULL OR (focus_rating >= 1 AND focus_rating <= 5)) AND
        (difficulty_rating IS NULL OR (difficulty_rating >= 1 AND difficulty_rating <= 5)) AND
        (satisfaction_rating IS NULL OR (satisfaction_rating >= 1 AND satisfaction_rating <= 5))
        '''
    )
    
    op.create_check_constraint(
        'ck_study_sessions_status_valid',
        'study_sessions',
        "status IN ('planned', 'active', 'paused', 'completed', 'cancelled')"
    )
    
    op.create_check_constraint(
        'ck_study_sessions_type_valid',
        'study_sessions',
        "session_type IN ('study', 'practice', 'review')"
    )
    
    op.create_check_constraint(
        'ck_study_goals_target_value_positive',
        'study_goals',
        'target_value > 0'
    )
    
    op.create_check_constraint(
        'ck_study_goals_current_value_non_negative',
        'study_goals',
        'current_value >= 0'
    )
    
    op.create_check_constraint(
        'ck_study_goals_type_valid',
        'study_goals',
        "goal_type IN ('daily_time', 'weekly_time', 'monthly_time', 'certification_deadline', 'progress_milestone')"
    )
    
    op.create_check_constraint(
        'ck_study_streaks_values_non_negative',
        'study_streaks',
        '''
        current_streak_days >= 0 AND
        longest_streak_days >= 0 AND
        total_study_days >= 0 AND
        total_study_minutes >= 0 AND
        total_sessions >= 0
        '''
    )


def downgrade():
    """Drop study timer tables."""
    
    # Drop check constraints first
    op.drop_constraint('ck_study_streaks_values_non_negative', 'study_streaks')
    op.drop_constraint('ck_study_goals_type_valid', 'study_goals')
    op.drop_constraint('ck_study_goals_current_value_non_negative', 'study_goals')
    op.drop_constraint('ck_study_goals_target_value_positive', 'study_goals')
    op.drop_constraint('ck_study_sessions_type_valid', 'study_sessions')
    op.drop_constraint('ck_study_sessions_status_valid', 'study_sessions')
    op.drop_constraint('ck_study_sessions_rating_valid', 'study_sessions')
    op.drop_constraint('ck_study_sessions_progress_percentage_valid', 'study_sessions')
    op.drop_constraint('ck_study_sessions_actual_duration_positive', 'study_sessions')
    op.drop_constraint('ck_study_sessions_planned_duration_positive', 'study_sessions')
    
    # Drop indexes
    op.drop_index('ix_study_streaks_longest_streak', 'study_streaks')
    op.drop_index('ix_study_streaks_current_streak', 'study_streaks')
    op.drop_index('ix_study_streaks_user_id', 'study_streaks')
    op.drop_index('ix_study_goals_target_date', 'study_goals')
    op.drop_index('ix_study_goals_is_active', 'study_goals')
    op.drop_index('ix_study_goals_goal_type', 'study_goals')
    op.drop_index('ix_study_goals_user_id', 'study_goals')
    op.drop_index('ix_study_sessions_certification_id', 'study_sessions')
    op.drop_index('ix_study_sessions_start_time', 'study_sessions')
    op.drop_index('ix_study_sessions_status', 'study_sessions')
    op.drop_index('ix_study_sessions_user_id', 'study_sessions')
    
    # Drop tables
    op.drop_table('study_streaks')
    op.drop_table('study_goals')
    op.drop_table('study_sessions')
