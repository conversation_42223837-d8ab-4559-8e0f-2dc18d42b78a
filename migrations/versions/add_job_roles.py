"""Add job roles and responsibilities

Revision ID: add_job_roles
Create Date: 2025-02-26 12:04:00.000000
"""
from alembic import op
import sqlalchemy as sa

# Revision identifiers
revision = 'add_job_roles_001'  # unique identifier
down_revision = 'initial_migration_001'  # depends on initial migration
branch_labels = None
depends_on = None

def upgrade():
    # Create job_roles table
    op.create_table(
        'job_roles',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('title', sa.String(200), nullable=False),
        sa.<PERSON>umn('description', sa.String(500), nullable=True),
        sa.Column('domain', sa.String(100), nullable=False),
        sa.Column('responsibilities', sa.String(500), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('title', 'domain', name='uix_title_domain')
    )

    # Create job_responsibilities table as an association table
    op.create_table(
        'job_responsibilities',
        sa.<PERSON>umn('job_id', sa.Integer(), nullable=False),
        sa.Column('responsibility', sa.String(500), nullable=False),
        sa.ForeignKeyConstraint(['job_id'], ['job_roles.id'], ),
        sa.PrimaryKeyConstraint('job_id', 'responsibility')
    )

def downgrade():
    op.drop_table('job_responsibilities')
    op.drop_table('job_roles')