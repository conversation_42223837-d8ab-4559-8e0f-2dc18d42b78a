"""Create career transition tables

Revision ID: 004_career_transition
Revises: 003_cost_calculation_tables
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '004_career_transition'
down_revision = '002_cost_calculator'
branch_labels = None
depends_on = None


def upgrade():
    """Create career transition tables."""
    
    # Create career_roles table
    op.create_table(
        'career_roles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.String(length=100), nullable=False),
        sa.Column('level', sa.String(length=50), nullable=False),
        sa.Column('min_years_experience', sa.Integer(), nullable=True, default=0),
        sa.Column('max_years_experience', sa.Integer(), nullable=True),
        sa.Column('salary_min', sa.Float(), nullable=True),
        sa.Column('salary_max', sa.Float(), nullable=True),
        sa.Column('salary_currency', sa.String(length=3), nullable=True, default='USD'),
        sa.Column('required_skills', sa.JSON(), nullable=True),
        sa.Column('preferred_skills', sa.JSON(), nullable=True),
        sa.Column('required_certifications', sa.JSON(), nullable=True),
        sa.Column('preferred_certifications', sa.JSON(), nullable=True),
        sa.Column('common_previous_roles', sa.JSON(), nullable=True),
        sa.Column('common_next_roles', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('market_demand', sa.String(length=20), nullable=True, default='Medium'),
        sa.Column('growth_outlook', sa.String(length=20), nullable=True, default='Stable'),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('title')
    )
    
    # Create indexes for career_roles
    op.create_index('idx_career_roles_domain', 'career_roles', ['domain'])
    op.create_index('idx_career_roles_level', 'career_roles', ['level'])
    op.create_index('idx_career_roles_active', 'career_roles', ['is_active'])
    op.create_index('idx_career_roles_market_demand', 'career_roles', ['market_demand'])
    op.create_index('idx_career_roles_salary_range', 'career_roles', ['salary_min', 'salary_max'])
    
    # Create career_transition_paths table
    op.create_table(
        'career_transition_paths',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source_role_id', sa.Integer(), nullable=False),
        sa.Column('target_role_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('difficulty_level', sa.String(length=20), nullable=True, default='Medium'),
        sa.Column('estimated_duration_months', sa.Integer(), nullable=False),
        sa.Column('min_duration_months', sa.Integer(), nullable=True),
        sa.Column('max_duration_months', sa.Integer(), nullable=True),
        sa.Column('estimated_cost_min', sa.Float(), nullable=True, default=0.0),
        sa.Column('estimated_cost_max', sa.Float(), nullable=True, default=0.0),
        sa.Column('cost_currency', sa.String(length=3), nullable=True, default='USD'),
        sa.Column('required_certifications', sa.JSON(), nullable=True),
        sa.Column('recommended_certifications', sa.JSON(), nullable=True),
        sa.Column('prerequisite_experience', sa.JSON(), nullable=True),
        sa.Column('success_rate', sa.Float(), nullable=True, default=0.0),
        sa.Column('average_salary_increase', sa.Float(), nullable=True, default=0.0),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('popularity_score', sa.Float(), nullable=True, default=0.0),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['source_role_id'], ['career_roles.id'], ),
        sa.ForeignKeyConstraint(['target_role_id'], ['career_roles.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for career_transition_paths
    op.create_index('idx_transition_paths_source', 'career_transition_paths', ['source_role_id'])
    op.create_index('idx_transition_paths_target', 'career_transition_paths', ['target_role_id'])
    op.create_index('idx_transition_paths_active', 'career_transition_paths', ['is_active'])
    op.create_index('idx_transition_paths_difficulty', 'career_transition_paths', ['difficulty_level'])
    op.create_index('idx_transition_paths_duration', 'career_transition_paths', ['estimated_duration_months'])
    op.create_index('idx_transition_paths_cost', 'career_transition_paths', ['estimated_cost_min', 'estimated_cost_max'])
    op.create_index('idx_transition_paths_success_rate', 'career_transition_paths', ['success_rate'])
    
    # Create career_transition_plans table
    op.create_table(
        'career_transition_plans',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=100), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('current_role_id', sa.Integer(), nullable=True),
        sa.Column('target_role_id', sa.Integer(), nullable=False),
        sa.Column('budget_min', sa.Float(), nullable=True, default=0.0),
        sa.Column('budget_max', sa.Float(), nullable=True),
        sa.Column('budget_currency', sa.String(length=3), nullable=True, default='USD'),
        sa.Column('timeline_months', sa.Integer(), nullable=True),
        sa.Column('max_timeline_months', sa.Integer(), nullable=True),
        sa.Column('difficulty_preference', sa.String(length=20), nullable=True, default='Medium'),
        sa.Column('learning_style', sa.String(length=50), nullable=True, default='Mixed'),
        sa.Column('study_hours_per_week', sa.Integer(), nullable=True, default=10),
        sa.Column('status', sa.String(length=20), nullable=True, default='planning'),
        sa.Column('progress_percentage', sa.Float(), nullable=True, default=0.0),
        sa.Column('selected_path_id', sa.Integer(), nullable=True),
        sa.Column('alternative_paths', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['current_role_id'], ['career_roles.id'], ),
        sa.ForeignKeyConstraint(['target_role_id'], ['career_roles.id'], ),
        sa.ForeignKeyConstraint(['selected_path_id'], ['career_transition_paths.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for career_transition_plans
    op.create_index('idx_transition_plans_user', 'career_transition_plans', ['user_id'])
    op.create_index('idx_transition_plans_status', 'career_transition_plans', ['status'])
    op.create_index('idx_transition_plans_active', 'career_transition_plans', ['is_active'])
    op.create_index('idx_transition_plans_target_role', 'career_transition_plans', ['target_role_id'])
    op.create_index('idx_transition_plans_budget', 'career_transition_plans', ['budget_max'])
    op.create_index('idx_transition_plans_timeline', 'career_transition_plans', ['timeline_months'])
    op.create_index('idx_transition_plans_progress', 'career_transition_plans', ['progress_percentage'])
    
    # Create career_transition_steps table
    op.create_table(
        'career_transition_steps',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('plan_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('step_type', sa.String(length=50), nullable=False),
        sa.Column('sequence', sa.Integer(), nullable=False),
        sa.Column('certification_id', sa.Integer(), nullable=True),
        sa.Column('estimated_duration_weeks', sa.Integer(), nullable=True),
        sa.Column('estimated_cost', sa.Float(), nullable=True, default=0.0),
        sa.Column('cost_currency', sa.String(length=3), nullable=True, default='USD'),
        sa.Column('status', sa.String(length=20), nullable=True, default='pending'),
        sa.Column('progress_percentage', sa.Float(), nullable=True, default=0.0),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('prerequisite_steps', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['plan_id'], ['career_transition_plans.id'], ),
        sa.ForeignKeyConstraint(['certification_id'], ['certifications.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for career_transition_steps
    op.create_index('idx_transition_steps_plan', 'career_transition_steps', ['plan_id'])
    op.create_index('idx_transition_steps_sequence', 'career_transition_steps', ['plan_id', 'sequence'])
    op.create_index('idx_transition_steps_status', 'career_transition_steps', ['status'])
    op.create_index('idx_transition_steps_type', 'career_transition_steps', ['step_type'])
    op.create_index('idx_transition_steps_certification', 'career_transition_steps', ['certification_id'])
    op.create_index('idx_transition_steps_progress', 'career_transition_steps', ['progress_percentage'])


def downgrade():
    """Drop career transition tables."""
    
    # Drop indexes first
    op.drop_index('idx_transition_steps_progress', table_name='career_transition_steps')
    op.drop_index('idx_transition_steps_certification', table_name='career_transition_steps')
    op.drop_index('idx_transition_steps_type', table_name='career_transition_steps')
    op.drop_index('idx_transition_steps_status', table_name='career_transition_steps')
    op.drop_index('idx_transition_steps_sequence', table_name='career_transition_steps')
    op.drop_index('idx_transition_steps_plan', table_name='career_transition_steps')
    
    op.drop_index('idx_transition_plans_progress', table_name='career_transition_plans')
    op.drop_index('idx_transition_plans_timeline', table_name='career_transition_plans')
    op.drop_index('idx_transition_plans_budget', table_name='career_transition_plans')
    op.drop_index('idx_transition_plans_target_role', table_name='career_transition_plans')
    op.drop_index('idx_transition_plans_active', table_name='career_transition_plans')
    op.drop_index('idx_transition_plans_status', table_name='career_transition_plans')
    op.drop_index('idx_transition_plans_user', table_name='career_transition_plans')
    
    op.drop_index('idx_transition_paths_success_rate', table_name='career_transition_paths')
    op.drop_index('idx_transition_paths_cost', table_name='career_transition_paths')
    op.drop_index('idx_transition_paths_duration', table_name='career_transition_paths')
    op.drop_index('idx_transition_paths_difficulty', table_name='career_transition_paths')
    op.drop_index('idx_transition_paths_active', table_name='career_transition_paths')
    op.drop_index('idx_transition_paths_target', table_name='career_transition_paths')
    op.drop_index('idx_transition_paths_source', table_name='career_transition_paths')
    
    op.drop_index('idx_career_roles_salary_range', table_name='career_roles')
    op.drop_index('idx_career_roles_market_demand', table_name='career_roles')
    op.drop_index('idx_career_roles_active', table_name='career_roles')
    op.drop_index('idx_career_roles_level', table_name='career_roles')
    op.drop_index('idx_career_roles_domain', table_name='career_roles')
    
    # Drop tables in reverse order (due to foreign key constraints)
    op.drop_table('career_transition_steps')
    op.drop_table('career_transition_plans')
    op.drop_table('career_transition_paths')
    op.drop_table('career_roles')
