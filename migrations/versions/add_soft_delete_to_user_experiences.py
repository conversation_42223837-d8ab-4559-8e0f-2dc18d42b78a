"""Add soft delete columns to user_experiences

Revision ID: add_soft_delete_001
Create Date: 2025-02-28 18:44:00.000000
"""
from alembic import op
import sqlalchemy as sa

# Revision identifiers
revision = 'add_soft_delete_001'
down_revision = 'initial_migration_001'
branch_labels = None
depends_on = None

def upgrade():
    """Add soft delete columns to user_experiences table"""
    op.add_column('user_experiences',
        sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default='false')
    )
    op.add_column('user_experiences',
        sa.Column('deleted_at', sa.DateTime(), nullable=True)
    )

def downgrade():
    """Remove soft delete columns from user_experiences table"""
    op.drop_column('user_experiences', 'deleted_at')
    op.drop_column('user_experiences', 'is_deleted')
