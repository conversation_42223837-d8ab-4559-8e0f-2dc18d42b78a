"""Merge multiple migration heads

Revision ID: 898682ca3858
Revises: 001, 009_add_audit_log, add_soft_delete_001, add_timestamps_cert_001
Create Date: 2025-06-07 16:12:37.286726

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '898682ca3858'
down_revision = ('001', '009_add_audit_log', 'add_soft_delete_001', 'add_timestamps_cert_001')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
