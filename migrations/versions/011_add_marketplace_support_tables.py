"""Add marketplace support tables for enrollments, reviews, and international support

Revision ID: 011_add_marketplace_support_tables
Revises: 010_add_marketplace_tables
Create Date: 2024-06-15 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '011_add_marketplace_support_tables'
down_revision = '010_add_marketplace_tables'
branch_labels = None
depends_on = None


def upgrade():
    """Create marketplace support tables for enrollments, reviews, and international support."""
    
    # Create course_enrollments table
    op.create_table('course_enrollments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('course_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=100), nullable=False),
        sa.Column('enrollment_date', sa.DateTime(), nullable=False),
        sa.Column('completion_date', sa.DateTime(), nullable=True),
        sa.Column('progress_percentage', sa.Float(), nullable=False),
        sa.Column('purchase_price', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(length=3), nullable=False),
        sa.Column('payment_method', sa.String(length=50), nullable=True),
        sa.Column('transaction_id', sa.String(length=100), nullable=True),
        sa.Column('current_module', sa.String(length=100), nullable=True),
        sa.Column('time_spent_minutes', sa.Integer(), nullable=False),
        sa.Column('last_accessed', sa.DateTime(), nullable=True),
        sa.Column('is_completed', sa.Boolean(), nullable=False),
        sa.Column('completion_certificate_url', sa.String(length=500), nullable=True),
        sa.Column('final_score', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['course_id'], ['marketplace_courses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for course_enrollments
    op.create_index('idx_enrollment_course', 'course_enrollments', ['course_id'])
    op.create_index('idx_enrollment_user', 'course_enrollments', ['user_id'])
    op.create_index('idx_enrollment_status', 'course_enrollments', ['status'])
    op.create_index('idx_enrollment_completion', 'course_enrollments', ['is_completed'])
    
    # Create course_reviews table
    op.create_table('course_reviews',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('course_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=100), nullable=False),
        sa.Column('rating', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=True),
        sa.Column('review_text', sa.Text(), nullable=True),
        sa.Column('is_verified_purchase', sa.Boolean(), nullable=False),
        sa.Column('is_featured', sa.Boolean(), nullable=False),
        sa.Column('helpful_votes', sa.Integer(), nullable=False),
        sa.Column('is_approved', sa.Boolean(), nullable=False),
        sa.Column('moderation_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['course_id'], ['marketplace_courses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for course_reviews
    op.create_index('idx_review_course', 'course_reviews', ['course_id'])
    op.create_index('idx_review_user', 'course_reviews', ['user_id'])
    op.create_index('idx_review_rating', 'course_reviews', ['rating'])
    op.create_index('idx_review_approved', 'course_reviews', ['is_approved'])
    
    # Create currency_rates table
    op.create_table('currency_rates',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('from_currency', sa.String(length=3), nullable=False),
        sa.Column('to_currency', sa.String(length=3), nullable=False),
        sa.Column('rate', sa.Numeric(precision=15, scale=8), nullable=False),
        sa.Column('rate_date', sa.DateTime(), nullable=False),
        sa.Column('source', sa.String(length=100), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for currency_rates
    op.create_index('idx_currency_pair', 'currency_rates', ['from_currency', 'to_currency'])
    op.create_index('idx_currency_date', 'currency_rates', ['rate_date'])
    op.create_index('idx_currency_active', 'currency_rates', ['is_active'])
    
    # Create international_markets table
    op.create_table('international_markets',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('country_code', sa.String(length=2), nullable=False),
        sa.Column('country_name', sa.String(length=100), nullable=False),
        sa.Column('region', sa.String(length=100), nullable=True),
        sa.Column('default_currency', sa.String(length=3), nullable=False),
        sa.Column('supported_languages', sa.JSON(), nullable=True),
        sa.Column('tax_rate', sa.Float(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('launch_date', sa.DateTime(), nullable=True),
        sa.Column('date_format', sa.String(length=50), nullable=False),
        sa.Column('number_format', sa.String(length=50), nullable=False),
        sa.Column('timezone', sa.String(length=50), nullable=True),
        sa.Column('legal_entity', sa.String(length=200), nullable=True),
        sa.Column('tax_id', sa.String(length=100), nullable=True),
        sa.Column('compliance_requirements', sa.JSON(), nullable=True),
        sa.Column('total_users', sa.Integer(), nullable=False),
        sa.Column('total_revenue', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('country_code')
    )
    
    # Create indexes for international_markets
    op.create_index('idx_market_country', 'international_markets', ['country_code'])
    op.create_index('idx_market_active', 'international_markets', ['is_active'])
    op.create_index('idx_market_region', 'international_markets', ['region'])
    
    # Insert some default currency rates
    op.execute("""
        INSERT INTO currency_rates (from_currency, to_currency, rate, rate_date, source, is_active, created_at, updated_at)
        VALUES 
        ('USD', 'EUR', 0.85, NOW(), 'manual', true, NOW(), NOW()),
        ('USD', 'GBP', 0.73, NOW(), 'manual', true, NOW(), NOW()),
        ('USD', 'CAD', 1.25, NOW(), 'manual', true, NOW(), NOW()),
        ('USD', 'AUD', 1.35, NOW(), 'manual', true, NOW(), NOW()),
        ('USD', 'JPY', 110.0, NOW(), 'manual', true, NOW(), NOW()),
        ('EUR', 'USD', 1.18, NOW(), 'manual', true, NOW(), NOW()),
        ('GBP', 'USD', 1.37, NOW(), 'manual', true, NOW(), NOW()),
        ('CAD', 'USD', 0.80, NOW(), 'manual', true, NOW(), NOW()),
        ('AUD', 'USD', 0.74, NOW(), 'manual', true, NOW(), NOW()),
        ('JPY', 'USD', 0.0091, NOW(), 'manual', true, NOW(), NOW())
    """)
    
    # Insert some default international markets
    op.execute("""
        INSERT INTO international_markets (
            country_code, country_name, region, default_currency, supported_languages, 
            tax_rate, is_active, date_format, number_format, total_users, total_revenue, 
            created_at, updated_at
        )
        VALUES 
        ('US', 'United States', 'North America', 'USD', '["en"]', 0.0875, true, 'MM/DD/YYYY', '1,234.56', 0, 0.00, NOW(), NOW()),
        ('CA', 'Canada', 'North America', 'CAD', '["en", "fr"]', 0.13, true, 'DD/MM/YYYY', '1,234.56', 0, 0.00, NOW(), NOW()),
        ('GB', 'United Kingdom', 'Europe', 'GBP', '["en"]', 0.20, true, 'DD/MM/YYYY', '1,234.56', 0, 0.00, NOW(), NOW()),
        ('DE', 'Germany', 'Europe', 'EUR', '["de", "en"]', 0.19, true, 'DD.MM.YYYY', '1.234,56', 0, 0.00, NOW(), NOW()),
        ('FR', 'France', 'Europe', 'EUR', '["fr", "en"]', 0.20, true, 'DD/MM/YYYY', '1 234,56', 0, 0.00, NOW(), NOW()),
        ('AU', 'Australia', 'Oceania', 'AUD', '["en"]', 0.10, true, 'DD/MM/YYYY', '1,234.56', 0, 0.00, NOW(), NOW()),
        ('JP', 'Japan', 'Asia', 'JPY', '["ja", "en"]', 0.10, false, 'YYYY/MM/DD', '1,234', 0, 0.00, NOW(), NOW())
    """)


def downgrade():
    """Drop marketplace support tables."""
    
    # Drop indexes first
    op.drop_index('idx_market_region', table_name='international_markets')
    op.drop_index('idx_market_active', table_name='international_markets')
    op.drop_index('idx_market_country', table_name='international_markets')
    
    op.drop_index('idx_currency_active', table_name='currency_rates')
    op.drop_index('idx_currency_date', table_name='currency_rates')
    op.drop_index('idx_currency_pair', table_name='currency_rates')
    
    op.drop_index('idx_review_approved', table_name='course_reviews')
    op.drop_index('idx_review_rating', table_name='course_reviews')
    op.drop_index('idx_review_user', table_name='course_reviews')
    op.drop_index('idx_review_course', table_name='course_reviews')
    
    op.drop_index('idx_enrollment_completion', table_name='course_enrollments')
    op.drop_index('idx_enrollment_status', table_name='course_enrollments')
    op.drop_index('idx_enrollment_user', table_name='course_enrollments')
    op.drop_index('idx_enrollment_course', table_name='course_enrollments')
    
    # Drop tables
    op.drop_table('international_markets')
    op.drop_table('currency_rates')
    op.drop_table('course_reviews')
    op.drop_table('course_enrollments')
