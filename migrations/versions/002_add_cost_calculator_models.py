"""Add cost calculator models

Revision ID: 002_cost_calculator
Revises: 001_study_timer
Create Date: 2024-01-15 14:00:00.000000

This migration adds the cost calculator functionality including:
- CurrencyRate model for exchange rate tracking
- CostScenario model for different cost calculation scenarios
- CostCalculation model for individual cost calculations
- CostHistory model for historical cost tracking
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002_cost_calculator'
down_revision = '001_study_timer'
branch_labels = None
depends_on = None


def upgrade():
    """Create cost calculator tables."""
    
    # Create currency_rates table
    op.create_table(
        'currency_rates',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('base_currency', sa.String(3), nullable=False, default='USD'),
        sa.Column('target_currency', sa.String(3), nullable=False),
        sa.Column('rate', sa.Float(), nullable=False),
        sa.Column('source', sa.String(50), nullable=False, default='manual'),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('valid_from', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('valid_until', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for currency_rates
    op.create_index('ix_currency_rates_base_target', 'currency_rates', ['base_currency', 'target_currency'])
    op.create_index('ix_currency_rates_is_active', 'currency_rates', ['is_active'])
    op.create_index('ix_currency_rates_valid_from', 'currency_rates', ['valid_from'])
    
    # Create cost_scenarios table
    op.create_table(
        'cost_scenarios',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('scenario_type', sa.String(50), nullable=False),
        sa.Column('materials_multiplier', sa.Float(), nullable=False, default=1.0),
        sa.Column('training_multiplier', sa.Float(), nullable=False, default=1.0),
        sa.Column('retake_probability', sa.Float(), nullable=False, default=0.2),
        sa.Column('includes_training', sa.Boolean(), nullable=False, default=False),
        sa.Column('includes_mentoring', sa.Boolean(), nullable=False, default=False),
        sa.Column('includes_practice_exams', sa.Boolean(), nullable=False, default=True),
        sa.Column('study_time_multiplier', sa.Float(), nullable=False, default=1.0),
        sa.Column('preparation_weeks', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for cost_scenarios
    op.create_index('ix_cost_scenarios_scenario_type', 'cost_scenarios', ['scenario_type'])
    op.create_index('ix_cost_scenarios_is_active', 'cost_scenarios', ['is_active'])
    op.create_index('ix_cost_scenarios_name', 'cost_scenarios', ['name'])
    
    # Create cost_calculations table
    op.create_table(
        'cost_calculations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('name', sa.String(200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('base_currency', sa.String(3), nullable=False, default='USD'),
        sa.Column('target_currency', sa.String(3), nullable=False, default='USD'),
        sa.Column('scenario_id', sa.Integer(), nullable=True),
        sa.Column('certification_ids', sa.JSON(), nullable=False),
        sa.Column('exam_fees_total', sa.Float(), nullable=False, default=0.0),
        sa.Column('materials_cost', sa.Float(), nullable=False, default=0.0),
        sa.Column('training_cost', sa.Float(), nullable=False, default=0.0),
        sa.Column('retake_cost', sa.Float(), nullable=False, default=0.0),
        sa.Column('additional_costs', sa.Float(), nullable=False, default=0.0),
        sa.Column('total_cost_base', sa.Float(), nullable=False, default=0.0),
        sa.Column('total_cost_target', sa.Float(), nullable=False, default=0.0),
        sa.Column('exchange_rate_used', sa.Float(), nullable=False, default=1.0),
        sa.Column('estimated_study_hours', sa.Integer(), nullable=True),
        sa.Column('estimated_weeks', sa.Integer(), nullable=True),
        sa.Column('calculation_date', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('is_saved', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_shared', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['scenario_id'], ['cost_scenarios.id'], )
    )
    
    # Create indexes for cost_calculations
    op.create_index('ix_cost_calculations_user_id', 'cost_calculations', ['user_id'])
    op.create_index('ix_cost_calculations_scenario_id', 'cost_calculations', ['scenario_id'])
    op.create_index('ix_cost_calculations_calculation_date', 'cost_calculations', ['calculation_date'])
    op.create_index('ix_cost_calculations_is_saved', 'cost_calculations', ['is_saved'])
    op.create_index('ix_cost_calculations_is_shared', 'cost_calculations', ['is_shared'])
    
    # Create cost_history table
    op.create_table(
        'cost_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('certification_id', sa.Integer(), nullable=False),
        sa.Column('cost', sa.Float(), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False, default='USD'),
        sa.Column('source', sa.String(100), nullable=True),
        sa.Column('effective_date', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('previous_cost', sa.Float(), nullable=True),
        sa.Column('change_percentage', sa.Float(), nullable=True),
        sa.Column('change_reason', sa.String(200), nullable=True),
        sa.Column('is_verified', sa.Boolean(), nullable=False, default=False),
        sa.Column('verified_by', sa.String(), nullable=True),
        sa.Column('verified_date', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['certification_id'], ['certifications.id'], )
    )
    
    # Create indexes for cost_history
    op.create_index('ix_cost_history_certification_id', 'cost_history', ['certification_id'])
    op.create_index('ix_cost_history_effective_date', 'cost_history', ['effective_date'])
    op.create_index('ix_cost_history_currency', 'cost_history', ['currency'])
    op.create_index('ix_cost_history_is_verified', 'cost_history', ['is_verified'])
    
    # Add check constraints for data validation
    op.create_check_constraint(
        'ck_currency_rates_rate_positive',
        'currency_rates',
        'rate > 0'
    )
    
    op.create_check_constraint(
        'ck_currency_rates_currencies_different',
        'currency_rates',
        'base_currency != target_currency'
    )
    
    op.create_check_constraint(
        'ck_currency_rates_currency_codes_valid',
        'currency_rates',
        '''
        base_currency ~ '^[A-Z]{3}$' AND
        target_currency ~ '^[A-Z]{3}$'
        '''
    )
    
    op.create_check_constraint(
        'ck_cost_scenarios_multipliers_valid',
        'cost_scenarios',
        '''
        materials_multiplier >= 0 AND
        training_multiplier >= 0 AND
        study_time_multiplier > 0 AND
        retake_probability >= 0 AND retake_probability <= 1
        '''
    )
    
    op.create_check_constraint(
        'ck_cost_scenarios_type_valid',
        'cost_scenarios',
        "scenario_type IN ('self_study', 'bootcamp', 'university', 'corporate', 'hybrid')"
    )
    
    op.create_check_constraint(
        'ck_cost_scenarios_preparation_weeks_valid',
        'cost_scenarios',
        'preparation_weeks IS NULL OR (preparation_weeks > 0 AND preparation_weeks <= 104)'
    )
    
    op.create_check_constraint(
        'ck_cost_calculations_costs_non_negative',
        'cost_calculations',
        '''
        exam_fees_total >= 0 AND
        materials_cost >= 0 AND
        training_cost >= 0 AND
        retake_cost >= 0 AND
        additional_costs >= 0 AND
        total_cost_base >= 0 AND
        total_cost_target >= 0
        '''
    )
    
    op.create_check_constraint(
        'ck_cost_calculations_exchange_rate_positive',
        'cost_calculations',
        'exchange_rate_used > 0'
    )
    
    op.create_check_constraint(
        'ck_cost_calculations_time_estimates_valid',
        'cost_calculations',
        '''
        (estimated_study_hours IS NULL OR estimated_study_hours > 0) AND
        (estimated_weeks IS NULL OR estimated_weeks > 0)
        '''
    )
    
    op.create_check_constraint(
        'ck_cost_calculations_currency_codes_valid',
        'cost_calculations',
        '''
        base_currency ~ '^[A-Z]{3}$' AND
        target_currency ~ '^[A-Z]{3}$'
        '''
    )
    
    op.create_check_constraint(
        'ck_cost_history_cost_positive',
        'cost_history',
        'cost >= 0'
    )
    
    op.create_check_constraint(
        'ck_cost_history_previous_cost_valid',
        'cost_history',
        'previous_cost IS NULL OR previous_cost >= 0'
    )
    
    op.create_check_constraint(
        'ck_cost_history_currency_code_valid',
        'cost_history',
        "currency ~ '^[A-Z]{3}$'"
    )
    
    # Insert default cost scenarios
    op.execute("""
        INSERT INTO cost_scenarios (name, description, scenario_type, materials_multiplier, training_multiplier, retake_probability, includes_training, includes_mentoring, includes_practice_exams, study_time_multiplier, preparation_weeks) VALUES
        ('Self-Study', 'Independent study with books and online resources', 'self_study', 1.0, 0.0, 0.25, false, false, true, 1.2, 16),
        ('Bootcamp', 'Intensive training bootcamp with instructor support', 'bootcamp', 1.5, 3.0, 0.10, true, true, true, 0.8, 8),
        ('University Course', 'Formal university or college course', 'university', 2.0, 4.0, 0.05, true, true, true, 1.0, 16),
        ('Corporate Training', 'Company-sponsored training program', 'corporate', 1.2, 2.0, 0.15, true, false, true, 0.9, 12),
        ('Hybrid Approach', 'Combination of self-study and formal training', 'hybrid', 1.3, 1.5, 0.18, true, false, true, 1.1, 14)
    """)
    
    # Insert common currency rates (these would typically be updated via API)
    op.execute("""
        INSERT INTO currency_rates (base_currency, target_currency, rate, source) VALUES
        ('USD', 'EUR', 0.92, 'manual'),
        ('USD', 'GBP', 0.79, 'manual'),
        ('USD', 'CAD', 1.35, 'manual'),
        ('USD', 'AUD', 1.52, 'manual'),
        ('USD', 'JPY', 150.0, 'manual'),
        ('EUR', 'USD', 1.09, 'manual'),
        ('GBP', 'USD', 1.27, 'manual')
    """)


def downgrade():
    """Drop cost calculator tables."""
    
    # Drop check constraints first
    op.drop_constraint('ck_cost_history_currency_code_valid', 'cost_history')
    op.drop_constraint('ck_cost_history_previous_cost_valid', 'cost_history')
    op.drop_constraint('ck_cost_history_cost_positive', 'cost_history')
    op.drop_constraint('ck_cost_calculations_currency_codes_valid', 'cost_calculations')
    op.drop_constraint('ck_cost_calculations_time_estimates_valid', 'cost_calculations')
    op.drop_constraint('ck_cost_calculations_exchange_rate_positive', 'cost_calculations')
    op.drop_constraint('ck_cost_calculations_costs_non_negative', 'cost_calculations')
    op.drop_constraint('ck_cost_scenarios_preparation_weeks_valid', 'cost_scenarios')
    op.drop_constraint('ck_cost_scenarios_type_valid', 'cost_scenarios')
    op.drop_constraint('ck_cost_scenarios_multipliers_valid', 'cost_scenarios')
    op.drop_constraint('ck_currency_rates_currency_codes_valid', 'currency_rates')
    op.drop_constraint('ck_currency_rates_currencies_different', 'currency_rates')
    op.drop_constraint('ck_currency_rates_rate_positive', 'currency_rates')
    
    # Drop indexes
    op.drop_index('ix_cost_history_is_verified', 'cost_history')
    op.drop_index('ix_cost_history_currency', 'cost_history')
    op.drop_index('ix_cost_history_effective_date', 'cost_history')
    op.drop_index('ix_cost_history_certification_id', 'cost_history')
    op.drop_index('ix_cost_calculations_is_shared', 'cost_calculations')
    op.drop_index('ix_cost_calculations_is_saved', 'cost_calculations')
    op.drop_index('ix_cost_calculations_calculation_date', 'cost_calculations')
    op.drop_index('ix_cost_calculations_scenario_id', 'cost_calculations')
    op.drop_index('ix_cost_calculations_user_id', 'cost_calculations')
    op.drop_index('ix_cost_scenarios_name', 'cost_scenarios')
    op.drop_index('ix_cost_scenarios_is_active', 'cost_scenarios')
    op.drop_index('ix_cost_scenarios_scenario_type', 'cost_scenarios')
    op.drop_index('ix_currency_rates_valid_from', 'currency_rates')
    op.drop_index('ix_currency_rates_is_active', 'currency_rates')
    op.drop_index('ix_currency_rates_base_target', 'currency_rates')
    
    # Drop tables
    op.drop_table('cost_history')
    op.drop_table('cost_calculations')
    op.drop_table('cost_scenarios')
    op.drop_table('currency_rates')
