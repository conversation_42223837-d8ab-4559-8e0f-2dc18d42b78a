"""Add audit log table for CRUD operations

Revision ID: 009_add_audit_log
Revises: 008_create_security_career_framework
Create Date: 2024-01-07 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '009_add_audit_log'
down_revision = '008_security_career_framework'
branch_labels = None
depends_on = None


def upgrade():
    """Create audit log table for tracking all database changes."""
    
    # Create audit_logs table
    op.create_table(
        'audit_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('table_name', sa.String(length=100), nullable=False),
        sa.Column('record_id', sa.Integer(), nullable=False),
        sa.Column('operation', sa.String(length=20), nullable=False),
        sa.Column('old_values', sa.JSON(), nullable=True),
        sa.Column('new_values', sa.<PERSON>(), nullable=True),
        sa.Column('changed_fields', sa.JSO<PERSON>(), nullable=True),
        sa.Column('user_id', sa.String(length=100), nullable=True),
        sa.Column('user_role', sa.String(length=50), nullable=True),
        sa.Column('session_id', sa.String(length=100), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        comment='Audit log for tracking all database changes and user actions'
    )
    
    # Create indexes for efficient querying
    op.create_index('idx_audit_logs_id', 'audit_logs', ['id'])
    op.create_index('idx_audit_logs_table_name', 'audit_logs', ['table_name'])
    op.create_index('idx_audit_logs_record_id', 'audit_logs', ['record_id'])
    op.create_index('idx_audit_logs_operation', 'audit_logs', ['operation'])
    op.create_index('idx_audit_logs_user_id', 'audit_logs', ['user_id'])
    op.create_index('idx_audit_logs_session_id', 'audit_logs', ['session_id'])
    op.create_index('idx_audit_logs_timestamp', 'audit_logs', ['timestamp'])
    
    # Composite indexes for common query patterns
    op.create_index('idx_audit_table_record', 'audit_logs', ['table_name', 'record_id'])
    op.create_index('idx_audit_user_timestamp', 'audit_logs', ['user_id', 'timestamp'])
    op.create_index('idx_audit_operation_timestamp', 'audit_logs', ['operation', 'timestamp'])
    op.create_index('idx_audit_table_timestamp', 'audit_logs', ['table_name', 'timestamp'])
    
    # Add check constraints for data integrity
    op.create_check_constraint(
        'ck_audit_logs_operation',
        'audit_logs',
        "operation IN ('INSERT', 'UPDATE', 'DELETE', 'SOFT_DELETE')"
    )
    
    op.create_check_constraint(
        'ck_audit_logs_table_name_not_empty',
        'audit_logs',
        "table_name != ''"
    )
    
    op.create_check_constraint(
        'ck_audit_logs_record_id_positive',
        'audit_logs',
        "record_id > 0"
    )


def downgrade():
    """Drop audit log table and related constraints."""
    
    # Drop check constraints
    op.drop_constraint('ck_audit_logs_record_id_positive', 'audit_logs', type_='check')
    op.drop_constraint('ck_audit_logs_table_name_not_empty', 'audit_logs', type_='check')
    op.drop_constraint('ck_audit_logs_operation', 'audit_logs', type_='check')
    
    # Drop composite indexes
    op.drop_index('idx_audit_table_timestamp', table_name='audit_logs')
    op.drop_index('idx_audit_operation_timestamp', table_name='audit_logs')
    op.drop_index('idx_audit_user_timestamp', table_name='audit_logs')
    op.drop_index('idx_audit_table_record', table_name='audit_logs')
    
    # Drop single column indexes
    op.drop_index('idx_audit_logs_timestamp', table_name='audit_logs')
    op.drop_index('idx_audit_logs_session_id', table_name='audit_logs')
    op.drop_index('idx_audit_logs_user_id', table_name='audit_logs')
    op.drop_index('idx_audit_logs_operation', table_name='audit_logs')
    op.drop_index('idx_audit_logs_record_id', table_name='audit_logs')
    op.drop_index('idx_audit_logs_table_name', table_name='audit_logs')
    op.drop_index('idx_audit_logs_id', table_name='audit_logs')
    
    # Drop the table
    op.drop_table('audit_logs')
