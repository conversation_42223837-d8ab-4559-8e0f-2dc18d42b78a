"""create and populate security domains

Revision ID: 001
Revises: 
Create Date: 2025-02-28 16:20:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Create security_domains table
    op.create_table(
        'security_domains',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )

    # Insert the 8 core security domains
    op.execute("""
        INSERT INTO security_domains (name, description, created_at, updated_at, is_deleted)
        VALUES 
        ('Security and Risk Management', 'Focuses on identifying and managing information security risks', NOW(), NOW(), false),
        ('Asset Security', 'Deals with securing digital and physical assets', NOW(), NOW(), false),
        ('Security Architecture and Engineering', 'Covers the design and implementation of security controls', NOW(), NOW(), false),
        ('Communication and Network Security', 'Focuses on network architecture and secure communication', NOW(), NOW(), false),
        ('Identity and Access Management', 'Manages identification and access control', NOW(), NOW(), false),
        ('Security Assessment and Testing', 'Covers security testing and assessment strategies', NOW(), NOW(), false),
        ('Security Operations', 'Deals with day-to-day security operations and incident response', NOW(), NOW(), false),
        ('Software Development Security', 'Focuses on secure software development practices', NOW(), NOW(), false)
    """)

def downgrade():
    op.drop_table('security_domains')
