"""Add timestamps to certifications

Revision ID: add_timestamps_cert_001
Create Date: 2025-03-01 08:20:00.000000
"""
from alembic import op
import sqlalchemy as sa

# Revision identifiers
revision = 'add_timestamps_cert_001'
down_revision = '930c9296b38e'
branch_labels = None
depends_on = None

def upgrade():
    """Add timestamp columns to certifications table"""
    op.add_column('certifications',
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()'))
    )
    op.add_column('certifications',
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('now()'))
    )

def downgrade():
    """Remove timestamp columns from certifications table"""
    op.drop_column('certifications', 'updated_at')
    op.drop_column('certifications', 'created_at')
