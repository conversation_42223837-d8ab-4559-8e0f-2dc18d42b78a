"""Add marketplace tables for Agent 5

Revision ID: 010_add_marketplace_tables
Revises: 009_add_audit_log_table
Create Date: 2024-06-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '010_add_marketplace_tables'
down_revision = '009_add_audit_log_table'
branch_labels = None
depends_on = None


def upgrade():
    """Create marketplace tables for Agent 5 Marketplace & Integration Hub."""
    
    # Create marketplace_vendors table
    op.create_table('marketplace_vendors',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_name', sa.String(length=200), nullable=False),
        sa.Column('vendor_slug', sa.String(length=100), nullable=False),
        sa.Column('contact_email', sa.String(length=255), nullable=False),
        sa.Column('contact_phone', sa.String(length=50), nullable=True),
        sa.Column('website_url', sa.String(length=500), nullable=True),
        sa.Column('business_name', sa.String(length=200), nullable=True),
        sa.Column('tax_id', sa.String(length=50), nullable=True),
        sa.Column('business_address', sa.JSON(), nullable=True),
        sa.Column('status', sa.Enum('PENDING', 'ACTIVE', 'SUSPENDED', 'TERMINATED', name='vendorstatus'), nullable=False),
        sa.Column('verification_status', sa.String(length=50), nullable=False),
        sa.Column('verification_date', sa.DateTime(), nullable=True),
        sa.Column('quality_score', sa.Float(), nullable=False),
        sa.Column('average_rating', sa.Float(), nullable=True),
        sa.Column('total_reviews', sa.Integer(), nullable=False),
        sa.Column('commission_rate', sa.Float(), nullable=False),
        sa.Column('payment_terms', sa.String(length=100), nullable=False),
        sa.Column('minimum_payout', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('content_categories', sa.JSON(), nullable=True),
        sa.Column('specializations', sa.JSON(), nullable=True),
        sa.Column('total_courses', sa.Integer(), nullable=False),
        sa.Column('total_enrollments', sa.Integer(), nullable=False),
        sa.Column('total_revenue', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('vendor_config', sa.JSON(), nullable=True),
        sa.Column('api_credentials', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('vendor_slug')
    )
    
    # Create indexes for marketplace_vendors
    op.create_index('idx_vendor_status', 'marketplace_vendors', ['status'])
    op.create_index('idx_vendor_quality', 'marketplace_vendors', ['quality_score'])
    op.create_index('idx_vendor_categories', 'marketplace_vendors', ['content_categories'])
    
    # Create marketplace_courses table
    op.create_table('marketplace_courses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('course_title', sa.String(length=300), nullable=False),
        sa.Column('course_slug', sa.String(length=150), nullable=False),
        sa.Column('course_description', sa.Text(), nullable=True),
        sa.Column('short_description', sa.String(length=500), nullable=True),
        sa.Column('course_level', sa.String(length=50), nullable=False),
        sa.Column('duration_hours', sa.Float(), nullable=True),
        sa.Column('language', sa.String(length=10), nullable=False),
        sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(length=3), nullable=False),
        sa.Column('discount_price', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('status', sa.Enum('DRAFT', 'PENDING_REVIEW', 'APPROVED', 'PUBLISHED', 'SUSPENDED', 'ARCHIVED', name='coursestatus'), nullable=False),
        sa.Column('approval_date', sa.DateTime(), nullable=True),
        sa.Column('publication_date', sa.DateTime(), nullable=True),
        sa.Column('course_content', sa.JSON(), nullable=True),
        sa.Column('learning_objectives', sa.JSON(), nullable=True),
        sa.Column('prerequisites', sa.JSON(), nullable=True),
        sa.Column('target_certifications', sa.JSON(), nullable=True),
        sa.Column('certification_domains', sa.JSON(), nullable=True),
        sa.Column('enrollment_count', sa.Integer(), nullable=False),
        sa.Column('completion_rate', sa.Float(), nullable=True),
        sa.Column('average_rating', sa.Float(), nullable=True),
        sa.Column('review_count', sa.Integer(), nullable=False),
        sa.Column('success_rate', sa.Float(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('featured', sa.Boolean(), nullable=False),
        sa.Column('promotional_text', sa.String(length=500), nullable=True),
        sa.Column('thumbnail_url', sa.String(length=500), nullable=True),
        sa.Column('preview_video_url', sa.String(length=500), nullable=True),
        sa.Column('course_materials', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['vendor_id'], ['marketplace_vendors.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for marketplace_courses
    op.create_index('idx_course_vendor', 'marketplace_courses', ['vendor_id'])
    op.create_index('idx_course_status', 'marketplace_courses', ['status'])
    op.create_index('idx_course_level', 'marketplace_courses', ['course_level'])
    op.create_index('idx_course_price', 'marketplace_courses', ['price'])
    op.create_index('idx_course_rating', 'marketplace_courses', ['average_rating'])
    op.create_index('idx_course_featured', 'marketplace_courses', ['featured'])
    op.create_index('idx_course_certifications', 'marketplace_courses', ['target_certifications'])
    
    # Create partnership_agreements table
    op.create_table('partnership_agreements',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('partner_name', sa.String(length=200), nullable=False),
        sa.Column('partnership_tier', sa.Enum('TIER_1', 'TIER_2', 'TIER_3', name='partnershiptier'), nullable=False),
        sa.Column('partnership_type', sa.String(length=100), nullable=False),
        sa.Column('revenue_share', sa.Float(), nullable=False),
        sa.Column('minimum_annual_volume', sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column('contract_duration_months', sa.Integer(), nullable=False),
        sa.Column('exclusive_benefits', sa.JSON(), nullable=True),
        sa.Column('co_branding_rights', sa.Boolean(), nullable=False),
        sa.Column('early_access', sa.Boolean(), nullable=False),
        sa.Column('joint_marketing', sa.Boolean(), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('signed_date', sa.DateTime(), nullable=True),
        sa.Column('effective_date', sa.DateTime(), nullable=True),
        sa.Column('expiration_date', sa.DateTime(), nullable=True),
        sa.Column('current_volume', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('total_commissions_paid', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('agreement_terms', sa.JSON(), nullable=True),
        sa.Column('legal_documents', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['vendor_id'], ['marketplace_vendors.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for partnership_agreements
    op.create_index('idx_partnership_vendor', 'partnership_agreements', ['vendor_id'])
    op.create_index('idx_partnership_tier', 'partnership_agreements', ['partnership_tier'])
    op.create_index('idx_partnership_status', 'partnership_agreements', ['status'])
    op.create_index('idx_partnership_expiration', 'partnership_agreements', ['expiration_date'])
    
    # Create commission_records table
    op.create_table('commission_records',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('course_id', sa.Integer(), nullable=True),
        sa.Column('transaction_id', sa.String(length=100), nullable=False),
        sa.Column('transaction_type', sa.String(length=50), nullable=False),
        sa.Column('gross_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('commission_rate', sa.Float(), nullable=False),
        sa.Column('commission_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(length=3), nullable=False),
        sa.Column('base_commission', sa.Float(), nullable=False),
        sa.Column('quality_bonus', sa.Float(), nullable=False),
        sa.Column('volume_bonus', sa.Float(), nullable=False),
        sa.Column('success_bonus', sa.Float(), nullable=False),
        sa.Column('status', sa.Enum('PENDING', 'CALCULATED', 'APPROVED', 'PAID', 'DISPUTED', name='commissionstatus'), nullable=False),
        sa.Column('calculation_date', sa.DateTime(), nullable=True),
        sa.Column('payment_date', sa.DateTime(), nullable=True),
        sa.Column('payment_reference', sa.String(length=100), nullable=True),
        sa.Column('period_start', sa.DateTime(), nullable=False),
        sa.Column('period_end', sa.DateTime(), nullable=False),
        sa.Column('customer_id', sa.String(length=100), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['course_id'], ['marketplace_courses.id'], ),
        sa.ForeignKeyConstraint(['vendor_id'], ['marketplace_vendors.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('transaction_id')
    )
    
    # Create indexes for commission_records
    op.create_index('idx_commission_vendor', 'commission_records', ['vendor_id'])
    op.create_index('idx_commission_status', 'commission_records', ['status'])
    op.create_index('idx_commission_period', 'commission_records', ['period_start', 'period_end'])
    op.create_index('idx_commission_transaction', 'commission_records', ['transaction_id'])


def downgrade():
    """Drop marketplace tables."""
    
    # Drop indexes first
    op.drop_index('idx_commission_transaction', table_name='commission_records')
    op.drop_index('idx_commission_period', table_name='commission_records')
    op.drop_index('idx_commission_status', table_name='commission_records')
    op.drop_index('idx_commission_vendor', table_name='commission_records')
    
    op.drop_index('idx_partnership_expiration', table_name='partnership_agreements')
    op.drop_index('idx_partnership_status', table_name='partnership_agreements')
    op.drop_index('idx_partnership_tier', table_name='partnership_agreements')
    op.drop_index('idx_partnership_vendor', table_name='partnership_agreements')
    
    op.drop_index('idx_course_certifications', table_name='marketplace_courses')
    op.drop_index('idx_course_featured', table_name='marketplace_courses')
    op.drop_index('idx_course_rating', table_name='marketplace_courses')
    op.drop_index('idx_course_price', table_name='marketplace_courses')
    op.drop_index('idx_course_level', table_name='marketplace_courses')
    op.drop_index('idx_course_status', table_name='marketplace_courses')
    op.drop_index('idx_course_vendor', table_name='marketplace_courses')
    
    op.drop_index('idx_vendor_categories', table_name='marketplace_vendors')
    op.drop_index('idx_vendor_quality', table_name='marketplace_vendors')
    op.drop_index('idx_vendor_status', table_name='marketplace_vendors')
    
    # Drop tables
    op.drop_table('commission_records')
    op.drop_table('partnership_agreements')
    op.drop_table('marketplace_courses')
    op.drop_table('marketplace_vendors')
    
    # Drop enums
    op.execute('DROP TYPE IF EXISTS commissionstatus')
    op.execute('DROP TYPE IF EXISTS partnershiptier')
    op.execute('DROP TYPE IF EXISTS coursestatus')
    op.execute('DROP TYPE IF EXISTS vendorstatus')
