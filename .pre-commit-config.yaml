# Pre-commit configuration for CertPathFinder
# ===========================================

repos:
  # Code formatting
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=100]

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=100]

  # Code linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--max-line-length=100, --extend-ignore=E203,W503]

  # Security linting
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, ., -x, ./venv,./docs,./tests]

  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-merge-conflict
      - id: check-added-large-files
        args: [--maxkb=1000]
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable

  # Python-specific checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: debug-statements
      - id: name-tests-test
        args: [--pytest-test-first]

  # Type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        args: [--ignore-missing-imports, --no-strict-optional]
        additional_dependencies: [types-requests, types-PyYAML]

  # Dependency security check
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check

  # Local custom hooks
  - repo: local
    hooks:
      # Custom security scan
      - id: custom-security-scan
        name: Custom Security Scan
        entry: python scripts/security_scan.py
        language: system
        pass_filenames: false
        stages: [manual]

      # Test runner
      - id: pytest
        name: Run Tests
        entry: python -m pytest tests/ -x --tb=short
        language: system
        pass_filenames: false
        stages: [manual]
      - id: check-merge-conflict
      - id: debug-statements

  - repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
      - id: black
        language_version: python3.11
        args: [--line-length=88]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.0.1
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-setuptools]
        args: [--ignore-missing-imports]

  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: [--convention=pep257, --add-ignore=D100,D104]

  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, ., -f, json, -o, bandit-report.json]
        pass_filenames: false

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile, black, --line-length, "88"]

  - repo: local
    hooks:
      - id: pytest-check
        name: pytest-check
        entry: python -m pytest tests/ -x -v
        language: system
        pass_filenames: false
        always_run: true
        stages: [push]

      - id: ai-code-validation
        name: AI Code Validation
        entry: python scripts/validate_ai_code.py
        language: system
        files: \.py$
        stages: [commit]
