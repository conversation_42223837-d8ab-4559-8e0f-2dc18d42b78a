{"environments": {"development": {"domain": "certrats.localhost", "protocol": "http", "services": {"api": {"subdomain": "api", "port": null, "path": "", "health_endpoint": "/health", "internal_only": false}, "frontend": {"subdomain": "app", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "docs": {"subdomain": "docs", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "status": {"subdomain": "status", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "storage": {"subdomain": "storage", "port": null, "path": "", "health_endpoint": "/minio/health/live", "internal_only": false}, "storage_console": {"subdomain": "storage-console", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "prometheus": {"subdomain": "metrics", "port": null, "path": "", "health_endpoint": "/-/healthy", "internal_only": false}, "grafana": {"subdomain": "grafana", "port": null, "path": "", "health_endpoint": "/api/health", "internal_only": false}, "flower": {"subdomain": "flower", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "traefik": {"subdomain": "traefik", "port": null, "path": "", "health_endpoint": "/ping", "internal_only": false}, "database": {"subdomain": null, "port": 5432, "path": "", "health_endpoint": null, "internal_only": true}, "redis": {"subdomain": null, "port": 6379, "path": "", "health_endpoint": null, "internal_only": true}}}, "staging": {"domain": "staging.certrats.dev", "protocol": "https", "services": {"api": {"subdomain": "api", "port": null, "path": "", "health_endpoint": "/health", "internal_only": false}, "frontend": {"subdomain": "app", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "docs": {"subdomain": "docs", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "status": {"subdomain": "status", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "storage": {"subdomain": "storage", "port": null, "path": "", "health_endpoint": "/minio/health/live", "internal_only": false}, "grafana": {"subdomain": "grafana", "port": null, "path": "", "health_endpoint": "/api/health", "internal_only": false}}}, "production": {"domain": "certrats.com", "protocol": "https", "services": {"api": {"subdomain": "api", "port": null, "path": "", "health_endpoint": "/health", "internal_only": false}, "frontend": {"subdomain": "app", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "docs": {"subdomain": "docs", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}, "status": {"subdomain": "status", "port": null, "path": "", "health_endpoint": "/", "internal_only": false}}}, "local-direct": {"domain": "localhost", "protocol": "http", "services": {"api": {"subdomain": null, "port": 8000, "path": "", "health_endpoint": "/health", "internal_only": false}, "frontend": {"subdomain": null, "port": 3000, "path": "", "health_endpoint": "/", "internal_only": false}, "database": {"subdomain": null, "port": 5432, "path": "", "health_endpoint": null, "internal_only": true}, "redis": {"subdomain": null, "port": 6379, "path": "", "health_endpoint": null, "internal_only": true}, "storage": {"subdomain": null, "port": 9000, "path": "", "health_endpoint": "/minio/health/live", "internal_only": false}, "prometheus": {"subdomain": null, "port": 9090, "path": "", "health_endpoint": "/-/healthy", "internal_only": false}, "grafana": {"subdomain": null, "port": 3001, "path": "", "health_endpoint": "/api/health", "internal_only": false}}}}, "api_endpoints": {"auth": {"login": "/api/v1/auth/login", "logout": "/api/v1/auth/logout", "register": "/api/v1/auth/register", "refresh": "/api/v1/auth/refresh", "profile": "/api/v1/auth/profile"}, "certifications": {"list": "/api/v1/certifications", "detail": "/api/v1/certifications/{cert_id}", "search": "/api/v1/certifications/search", "recommendations": "/api/v1/certifications/recommendations"}, "career_paths": {"list": "/api/v1/career-paths", "detail": "/api/v1/career-paths/{path_id}", "generate": "/api/v1/career-paths/generate", "progress": "/api/v1/career-paths/{path_id}/progress"}, "study": {"sessions": "/api/v1/study/sessions", "timer": "/api/v1/study/timer", "progress": "/api/v1/study/progress", "resources": "/api/v1/study/resources"}, "enterprise": {"dashboard": "/api/v1/enterprise/dashboard", "teams": "/api/v1/enterprise/teams", "analytics": "/api/v1/enterprise/analytics", "reports": "/api/v1/enterprise/reports"}, "health": {"system": "/health", "database": "/api/v1/health/database", "redis": "/api/v1/health/redis", "storage": "/api/v1/health/storage"}}, "storage_buckets": {"uploads": "certrats-uploads", "reports": "certrats-reports", "backups": "certrats-backups", "static": "certrats-static"}}