[{"category": "Entry Level Security Certifications", "certifications": [{"name": "A+", "description": "CompTIA A+\n    $253 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ACE", "description": "AccessData Certified Examiner\n    $100 + software", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "APMG 20000F", "description": "APMG ISO/IEC 20000 Foundation\n    $308 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "APMG 20000P", "description": "APMG ISO/IEC 20000 Practitioner\n    $308 Exam\n    Foundation or ITIL req'd", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "APMG 27001A", "description": "APMG ISO/IEC 27001 Auditor\n    $400 exam\n    Application essay", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "APMG 27001F", "description": "APMG ISO/IEC 27001 Foundation\n    $400 exam\n    Application essay", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "APMG 27001P", "description": "APMG ISO/IEC 27001 Practitioner\n    $400 exam\n    Application essay", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ASIS PCI", "description": "ASIS Professional Certified Investigator\n    $485 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "AWS CP", "description": "Amazon Web Services Certified Cloud Practitioner\n    $100 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "AWS SAA", "description": "Amazon Web Services Certified Solutions Architect - Associate\n    $150 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "AWS SAP", "description": "Amazon Web Services Certified Solutions Architect - Professional\n    $300 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "AZ-104", "description": "Microsoft Azure Administrator Associate\n    $165 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "AZ-220", "description": "Azure IoT Developer Specialty\n    $165 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "AZ-305", "description": "Microsoft Azure Solutions Architect Expert\n    $330 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "AZ-900", "description": "Microsoft Azure Fundamentals\n    $165 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Apple ACSP", "description": "Apple Certified Support Professional\n    $250 exam\n    Limited test locations", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "BCS FISMP", "description": "BCS Foundation Certifiate in Information Security Management Principles\n    $249 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "BCS PCIRM", "description": "BCS Practitioner Certificate in Information Risk Management\n    $287 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "BSCP", "description": "Portswigger Burp Suite Certified Practioner\n    $99 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C CS F", "description": "IBITGQ Certified Cyber Security Foundation\n    $725 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)CSA", "description": "Mile2 Certified Cybersecurity Analyst\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)CSO", "description": "Mile2 Certified Cloud Security Officer\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)DFE", "description": "Mile2 Certified Digital Forensics Examiner\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)DRE", "description": "Mile2 Certified Disaster Recovery Engineer\n                    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)HISSP", "description": "Mile2 Certified Healthcare Information Systems Security Practitioner\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)IHE", "description": "Mile2 Certified Incident Handling Engineer\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)ISCAP", "description": "Mile2 Information Systems Certification and Accredidation Professional\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)ISMS-LA", "description": "Mile2 Certified Information security Management Systems Lead Auditor\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)ISRM", "description": "Mile2 Certified Information Systems Risk Manager\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)ISSA", "description": "Mile2 Certified Information Systems Security Auditor\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)ISSM", "description": "Mile2 Certified Information Systems Security Manager\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)ISSO", "description": "Mile2 Certified Information Systems Security Officer\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)NFE", "description": "Mile2 Certified Network Forensics Examiner\n    $550 exam\n    Groups only", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)PEH", "description": "Mile2 Certified Professional Ethical Hacker\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)PSH", "description": "Mile2 Certified Powershell Hacker\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)PTC", "description": "Mile2 Certified Penetration Testing Consultant\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)PTE", "description": "Mile2 Certified Penetration Testing Engineer\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)SLO", "description": "Mile2 Certified Security Leadership Officer\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)SP", "description": "Mile2 Certified Security Principles\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)TIA", "description": "Mile2 Certified Threat Intelligence Analyst\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "C)VA", "description": "Mile2 Certified Vulnerability Assessor\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CAC", "description": "GAQM Certified Agile Coach\n    $170", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CACE", "description": "Excida IEC 62443 Certified Automation Cybersecurity Expert\n    $700 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CACS", "description": "Excida IEC 62443 Certified Automation Cybersecurity Specialist\n    $700 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CAD", "description": "GAQM Certified Agile Developer\n    $128 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CAMS", "description": "IMI Certfied Access Management Specialist\n    $195 exam", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CAPM", "description": "PMI Certified Associate in Project Management\n    $300 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CASM", "description": "GAQM Certified Agile Scrum Master\n    $128 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CAWFE", "description": "IACIS Certified Advanced Windows Forensic Examiner\n    $750 written exam & lab", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CC", "description": "ISC2 Certified in Cybersecurity\n    Free exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCD", "description": "Certified CyberDefender\n    $800 course\n    2 exam attempt included", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCDE", "description": "Cisco Certified Design Expert\n    ~$1,600 written exam\n    with hands-on lab", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCE", "description": "ISFCE Certified Computer Examiner\n    $485 written exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCFE", "description": "Infosec Institute Certified Computer Forensics Examiner\n    $4,599 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCISO", "description": "EC Council Certified Information Security Officer\n    $3,150 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCNA", "description": "Cisco Certified Network Associate\n    ~$330 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCNP Ent", "description": "Cisco Certified Network Professional - Enterprise\n    ~$600 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCNP Sec", "description": "Cisco Certified Network Professional - Security\n    ~$1,200 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCOA", "description": "ISACA Certified Cybersecurity Operations Analyst\n    $760 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCP", "description": "EC First Certified CCMC Professional\n    $2,995 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCPenX-AWS", "description": "The SecurityOps Group Certified Cloud Pentesting eXpert-AWS\n    $800 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCRMP", "description": "IBITGQ Certified in Managing Cyber Security Risk\n    $2,629 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCSA", "description": "EC First Certified Cyber Security Architect\n    $695 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCSE", "description": "Checkpoint Certified Security Expert\n    $250 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCSM", "description": "Checkpoint Certified Security Master\n    $350 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCSP", "description": "(ISC)2 Certified Cloud Security Professional\n    $599 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CCT", "description": "Cisco Certified Technician\n    $165 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CDRP", "description": "Infosec Institute Certified Data Recovery Professional\n    $4,599 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CFA", "description": "GAQM Certified Forensic Analyst\n    $128 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CFCE", "description": "IACIS Certified Forensic Computer Examiner\n    $750 4 peer reviewed exams", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CFR", "description": "CertNexus CyberSec First Responder\n    $250 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CFSR", "description": "OpenText Certified Forensic Security Responder\n    $250 written exam & lab", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CGEIT", "description": "ISACA Certified in the Governance of Enterprise IT\n    $760 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CGRC", "description": "(ISC)2 Certified in Governance, Risk and Compliance\n    $599 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CHA", "description": "ISECOM Certified Hacker Analyst\n    $100 annual sub\n    Unknown exam cost", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CHAT", "description": "ISECOM Certified Hacker Analyst Trainer\n    $100 annual sub\n    Unknown exam price", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CHFI", "description": "EC Council Computer Hacking Forensics Investigator\n    $650 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIAM", "description": "Identify Management Institute Certified Identify and Access Manager\n    $390 Exam", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIDPRO", "description": "IDPro Certified Identity Professional\n    $700 exam", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIGE", "description": "IMI Certified Identity Governance Expert\n    $395 exam", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIISec ICSF", "description": "CIISec Information and Cybersecurity Fundamentals\n    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIMP", "description": "Identify Management Institute Certified Identity Management Professional\n    $295 + Membership", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIOTSP", "description": "CertNexus Certified Internet of Things Security Practitioner\n    $250 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIPA", "description": "IMI Certified Identity Protection comptia-advanced-security-practitioner\n    $295 Exam", "difficulty": 1, "level": "Entry Level", "domain": "Asset Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIPP", "description": "IAPP Certified Information Privacy Professional\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Asset Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIPT", "description": "IAPP Certified Information Privacy Technologist\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Asset Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIRM Fdn", "description": "IBITGQ Cyber Incident Response Management Foundation\n    $768 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIS F", "description": "IBITGQ Certified ISO 27001 Information Security Management Specialist Foundation\n    $853 course exam\n    Brandeed course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIS IA", "description": "IBITGQ Certified ISO 27001 Information Security Management Specialist Internal Auditor\n    $1543 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIS LA", "description": "IBITGQ Certified ISO 27001 Information Security Management Specialist Lead Auditor\n    $2,008 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIS LI", "description": "IBITGQ Certified ISO 27001 Information Security Management Specialist Lead Implementer\n    $2,008 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIS RM", "description": "IBITGQ Certified ISO 27005 Information Security Management Specialist Risk Management\n    $2,783 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CISM", "description": "ISACA Certified Information Security Manager\n    $760 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CISP", "description": "GAQM Certified Information Security Professional\n    $170 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CISRM", "description": "IBITGQ Certified ISO 27005 Information Security Management Specialist Risk Management\n    $2,783 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CISSM", "description": "GAQM Certified Information Systems Security Manager\n    $170 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CISST", "description": "GAQM Certified Information systems Security Tester\n    $170 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CIST", "description": "IMI Certfied Identity and Security Technologist\n    $295 exam", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CITGP", "description": "IBITGQ Certified in Implementing IT Governance - Foundation & Principles\n    ~$2,499 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CKA", "description": "Cloud Native Computing Foundation Certified Kubernetes Administrator\n    $375 lab\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CKAD", "description": "Cloud Native Computing Foundation Certified Kubernetes Application Developer\n    $375 lab\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CKS", "description": "Cloud Native Computing Foundation Certified Kubernetes Security Specialist\n    $375 lab\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CLCSM", "description": "PECB Lead Cloud Security Manager\n    ~$930 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CM)DFI", "description": "Mile2 Certified Master Digital Forensic Investigator\n    Complete C)SP, C)DFE, C)NFE and C)CSA ($2200)", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CM)IPS", "description": "Mile2 Certified Master Intrusion Prevention Specialist\n    Complete C)VA, C)PEH, C)PTE and C)PTC ($2200)", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CM)ISSO", "description": "Mile2 Certified Master Information Systems Security Officer\n    Complete C)SP, C)ISSO, C)ISSM and IS20 ($2200)", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CMFE", "description": "Infosec Institute Certified Mobile Forensics Examiner\n    $1,699 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CMWAPT", "description": "Infosec Institute Certified Mobile and Web App Penetration Tester\n    $4,599 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CND", "description": "EC Council Certified Network Defender\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CNDA", "description": "EC Council Certified Network Defense Architect\n    $200 application\n    Requires CEH cert", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": ["CEH cert"], "category": "Entry Level Security Certifications"}, {"name": "CPD", "description": "GAQM Certified Project Director\n    $210 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CPENT", "description": "EC Council Certified Penetration Testing Professional\n    $999 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREA", "description": "Infosec Institute Certified Reverse Engineering Analyst\n    $4,599 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CCHIA", "description": "CREST Certified Host intrustion Analyst\n    $2,481 exam & essay\n    Hands on exam in UK", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CCNIA", "description": "CREST Certified Network Intrusion Analyst\n    $2,481 exam & essay\n    Hands on exam in UK", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CCSAS", "description": "CREST Certified Simulated Attack Specialist\n    $2,520 2 exams & lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CCTAPP", "description": "CREST Certified Web Application Tester\n    $2,520 exam & lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CCTIM", "description": "CREST Certified Threat Intelligence Manager\n    $2,480 3 exams", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CCTINF", "description": "CREST Certified Infrastructure Tester\n    $2,520 exam & lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CPIA", "description": "CREST Practitioner Intrusion Analyst\n    $425 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CPSA", "description": "CREST Practitioner Security Analyst\n    $425 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CPTIA", "description": "CREST Practitioner Threat Intelligence Analyst\n    $425 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CRIA", "description": "CREST Registered Intrusion Analyst\n    $612 exam & lab", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CRT", "description": "CREST Registered Penetration Tester\n    $612 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CRTIA", "description": "CREST Registered Threat Intelligence Analyst\n    $615 2 exams", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CRTSA", "description": "CREST Registered Technical Security Architect\n    $2,300 two exams\n    In person in the UK", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CREST CSAS", "description": "CREST Certified Simulated Attack Specialist\n    $2,520 2 exams & lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CRFS", "description": "IMI Certified Red Flag Specialist\n    $295 exam", "difficulty": 1, "level": "Entry Level", "domain": "Asset Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CRTO", "description": "Zero Point Security Certified Red Team Operator\n    $121 lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CRTO II", "description": "Zero Point Security Red Team Operator II\n    $121 lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CRTOP", "description": "Infosec Institute Certified Red Team Operations Professional\n    $4,599 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSA", "description": "EC Council Certified SOC Analyst\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSA CCSK", "description": "Cloud Security Alliance Certificate of Cloud Security Knowledge\n    $395 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSA CGC", "description": "Cloud Security Alliance Cloud Governance & Compliance\n    $315 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSAE", "description": "Cyber Struggle AEGIS\n    $1,700 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSAP", "description": "Infosec Institute Certified Security Awareness Practitioner\n    $2,599 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSBA", "description": "QAI Certified Software Business Analyst\n    $350 exam + written essay", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSCS", "description": "EC First Certified Security Compliance Specialist\n    $695 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSCU", "description": "EC Council Certified Secure Computer User\n                    $125 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSFA", "description": "CSIAC CyberSecurity Forensic Analyst\n    $750 exam & lab", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSM", "description": "GAQM Certified Scrum Master\n    $128 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSP", "description": "GAQM Certified SAFe Practitioner\n    $170 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSTL", "description": "Cyber Scheme Team Leader\n    $1945 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSTM", "description": "Cyber Scheme Team Member\n    $610 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSX-F", "description": "IBITGQ Cyber Incident Response Management Foundation\n    $768 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CSX-P", "description": "ISACA Cybersecurity Practitioner\n    $549 lab", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CTIA", "description": "EC Council Certified Threat intelligence Analyst\n    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CTPRA", "description": "Shared Assessment Certified Third-Party Risk Assessor\n    $1295 course", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CTPRP", "description": "Shared Assessment Certified Third-Party Risk Professional\n    $1295 course", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CWSP", "description": "CWNP Certified Wireless Security Professional\n    $325 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Cisco COA", "description": "Cisco Certified CyberOps Associate Cyber Operations\n    ~$325 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Cisco COP", "description": "Cisco Certified CyberOps Professional\n    $700 two exams", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Cloud Essnt", "description": "CompTIA Cloud Essentials\n    $138 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Cloud+", "description": "CompTIA Cloud+ \n    $369 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "CySA+", "description": "CompTIA Cybersecurity Analyst+\n    $404 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DACRP", "description": "DRI Associate <PERSON>ber Resilience Professional\n    $200 exam\n    Course req", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DCA", "description": "Docker Certified Associate\n    $195 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DCCRP", "description": "DRI Certified Cyber Resilience Professional\n    $400 Exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DCPP", "description": "DSCI Certified Privacy Professional\n    $205 Exam", "difficulty": 1, "level": "Entry Level", "domain": "Asset Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DCRMP", "description": "DRI Certified Risk Management Professional\n    $400 exam\n    Application essay", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DV AOPH", "description": "Dark Vortex Adversary Operations and Proactive Hunting\n    $2500 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DV MILF", "description": "Dark Vortex Malware Incident and Log Foensics\n    $2000 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DV MoS", "description": "Dark Vortex Malware on Steroids\n    $2000 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DV OTD", "description": "Dark Vortex Offensive Tool Development\n    $2000 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "DV RTOS", "description": "Dark Vortex Red Team & Operational Security\n    $2500 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ECES", "description": "EC Council Certified Encryption Specialist\n    $249 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ECIH", "description": "EC Council Certified Incident Handler\n    $300 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ECSS", "description": "EC Council Certified Security Specialist\n    $249 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EDRP", "description": "EC Council Disaster Recovery Professional\n    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EEHF", "description": "EXIN Ethical Hacking Foundation\n    $232 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EEXIN ISM", "description": "EXIN Information Security Management Expert\n    EST $799 oral exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EISM", "description": "EC Council Information Security Manager\n    $3,499\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EITCA/IS", "description": "EITCA/IS Information Security Certificate\n    $120 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EPDPE", "description": "EXIN Privacy and Data Protection Essentials\n    $145 exam", "difficulty": 1, "level": "Entry Level", "domain": "Asset Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EPDPF", "description": "EXIN Privacy and Data Protection Foundation\n    $207 exam", "difficulty": 1, "level": "Entry Level", "domain": "Asset Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EPDPP", "description": "EXIN Privacy and Data Protection Practitioner\n    $243 Exam\n    Course req'd", "difficulty": 1, "level": "Entry Level", "domain": "Asset Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN 27001E", "description": "EXIN ISO/IEC 27001 Expert\n    ~$379 Oral Presentation", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN 27001F", "description": "EXIN ISO/IEC 27001 Foundation\n    $232 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN 27001P", "description": "EXIN ISO/IEC 27001 Professional\n    $279 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN CIT", "description": "EXIN Cyber & IT Security\n    $225 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN PCA", "description": "EXIN Professional Cloud Administrator\n    $315 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN PCD", "description": "EXIN Professional Cloud Developer\n    $315 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN PCSA", "description": "EXIN Professional Cloud Solution Architect\n    $315 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN PCSM", "description": "EXIN Professional Cloud Security Manager\n    $315 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EXIN PCSerM", "description": "EXIN Professional Cloud Service Manager\n    $315 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "EnCE", "description": "OpenText EnCase Certified Examiner\n    $200 two exams", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "F5 CA", "description": "F5 Big-IP Certified Administrator\n    $135 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "F5 CSE Sec", "description": "F5 Big-IP Certified Solution Expert - Security\n    $135 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "F5 CTS APM", "description": "F5 Big-IP Certified Technical Specialist - Access Policy Manager\n    $135 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "F5 CTS DNS", "description": "F5 Big-IP Certified Technical Specialist - Domain Name Services\n    $135 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCA", "description": "Fortinet Certificed Associate\n    Free course and exam required", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCF", "description": "Fortinet Certified Fundamentals Cybersecurity\n    Free 3 courses with exams req", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCP NS", "description": "Fortinet Certified Professional - Network Security\n    $400 for 2 exams", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCP PCS", "description": "Fortinet Certified Professional - Public Cloud Security\n    $400 for 2 exams", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCP SO", "description": "Fortinet Certified Professional - Security Operations\n    $400 for 2 exams", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCSS NS", "description": "Fortinet Certificed Solution Specialist - Network Security\n    $800 two exams", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCSS OT", "description": "Fortinet Certified Solution Specialist - OT Security\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCSS PCS", "description": "Fortinet Certified Solution Specialist - Public Cloud Security\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCSS SASE", "description": "Fortinet Certified Solution Specialist - Secure Access Service Edge\n    $800 two exams", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCSS SO", "description": "Fortinet Certified Solution Specialist - Security Operations\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCSS ZTA", "description": "Fortinet Certified Solution Specialist - Zero Trust Access\n    $800 two exams", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FCX", "description": "Fortinet Certified Expert\n                    $400 written exam\n                    $1600 in-person lab", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "FEXIN", "description": "EXIN Information Security Foundation\n    $232 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Fair Fdn", "description": "Fair Institute Analysis Fundamentals\n    $1499 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GASF", "description": "GIAC Advanced Smartphone Forensics\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GBFA", "description": "GIAC Battlefield Forensics and Acquisition\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCCC", "description": "GIAC Critical Controls Certification\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCDA", "description": "GIAC Certified Detection Analyst\n        $979 exam\n        SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCFR", "description": "GIAC Cloud Forensics Responder\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCIP", "description": "GIAC Critical Infrastructure Protection\n    $979 exam\n    SANS course encouraged", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCLD", "description": "GIAC Cloud Security Essentials\n    $979 exam SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCPEH", "description": "GAQM Certified Professional Ethical Hacker\n    $170 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCPM", "description": "GIAC Certified Project Manager\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCPN", "description": "GIAC Cloud Penetration Tester\n    $2,499 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCPT", "description": "GAQM Certified Penetration Tester\n    $128 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCSA", "description": "GIAC Cloud Security Automation\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCTD", "description": "GIAC Cloud Threat Detection\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCTI", "description": "GIAC Cyber Threat Intelligence\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GCWN", "description": "GIAC Certified Windows Security Administrator\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GDAT", "description": "GIAC Defending Advanced Threats\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GDSA", "description": "GIAC Defensible Security Architecture\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GEIR", "description": "GIAC Enterprise Incident Response\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GICSP", "description": "GIAC Global Industrial Security Professional\n    $979 exam\n    SANS course encouraged", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GISF", "description": "GIAC Information Security Fundamentals\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GISP", "description": "GIAC Information Security Professional\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GLEG", "description": "GIAC Law of Data Security & Investigations\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GMOB", "description": "GIAC Mobile Device Security Analyst\n    $399 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GMON", "description": "GIAC Continuous Monitoring\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GNFA", "description": "GIAC Network Forensic Analyst\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GOSI", "description": "GIAC Open Source Intelligence\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GPCS", "description": "GIAC Public Cloud Security\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GPEN", "description": "GIAC Certified Penetration Tester\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GPYC", "description": "GIAC Python Coder\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GRCA", "description": "OCEG Governance, Risk, and Compliance Auditor\n    $399 12 month license", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GRCP", "description": "OCEG Governance, Risk, and Compliance Professional\n    $399 12 month license", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GRID", "description": "GIAC Response and Industrial Defense\n    $979 exam\n    SANS course encouraged", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GRTP", "description": "GIAC Red Team Professional\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GSLC", "description": "GIAC Security Leadership Certification\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GSNA", "description": "GIAC Systems and Network Auditor\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GSOC", "description": "GIAC Security Operations Certified\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GSTRT", "description": "GIAC Strategic Planning, Policy and Leadership\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GWAPT", "description": "GIAC Web Application Penetration Tester\n    $979 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GX-FA", "description": "GIAC Experienced Forensics Analyst\n    $1299 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "GX-PT", "description": "GIAC Experienced Penetration Tester\n    $1299 exam\n    SANS course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Google ACE", "description": "Google Associate Cloud Engineer\n    $125 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Google PCSA", "description": "Google Professional Cloud Architect\n    $200 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Google PCSE", "description": "Google Professional Cloud Security Engineer\n    $200 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "HTB CBBH", "description": "Hack the Box Certified Bug Bounty Hunter\n    $145 modules + $210 exam\n    $490 Subscription available", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "HTB CDSA", "description": "Hack the Box Certified Defensive Security Analyst\n                    $145 modules + $210 exam\n                    $490 Subscription available", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "HTB CPTS", "description": "Hack the Box Certified Penetration Testing Specialist\n    $200 modules + $210 exam\n    $490 Subscription available", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "HTB CWEE", "description": "Hack the Box Certified Web Exploitation Expert\n    $1260 Subscription available", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "IIA CIA", "description": "The Institute of Internal Auditors Certified Internal Auditor\n    $1315 3 exams", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "IIBA CCA", "description": "IIBA Certification in Cybersecurity Analysis\n    $475 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ISA CAP", "description": "ISA Certified Automation Specialist\n    $467 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ISA CDS", "description": "ISA Certified Design Specialist\n    $2,700 course + exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ISA CE", "description": "ISA Cybersecurity Expert\n    $2,700 course + exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ISA CFS", "description": "ISA Certified Fundamentals Specialist\n    $2,700 course + exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ISA CRAS", "description": "ISA Certified Risk Assesment Specialist\n    $2,700 course + exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ISMI CSM", "description": "ISMI Certified Security Manager\n    $TBD", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ISMI CSMP", "description": "ISMI Certified Security Management Professional\n    $1159", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ITIL SL", "description": "ITIL Strategic Leader\n    $4,800 two course exams\n    2 branded courses required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ITS-C", "description": "Certiport IT Specialist - Cybersecurity\n                    $127 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "ITS-NS", "description": "Certiport IT Specialist - Network Security\n    $127 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "JNCIA Sec", "description": "Juniper Networks Certified Internet Associate, Security\n    $200 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "JNCIE Sec", "description": "Juniper Networks Certified Internet Expert, Security\n    $1,400 Hands-on Lab", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "JNCIP Sec", "description": "Juniper Networks Certified Internet Professional, Security\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "JNCIS Sec", "description": "Juniper Networks Certified Internet Specialist, Security\n    $300 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "KCNA", "description": "Cloud Native Computing Foundation Kubernetes and Cloud Native Associate\n    $250 exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "KLCP", "description": "Kali Linux Certified Professional\n    $299 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "LFCA", "description": "Linux Foundation Certified IT Associate\n    $200 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "LFCS", "description": "Linux Foundation Certified System Administrator\n    $300 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "LPIC-1", "description": "Linux Professional Institute Certified: Linux Administrator\n    $400 2 exams", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "LPIC-2", "description": "Linux Professional Institute Certified: Linux Engineer\n    $400 2 exams", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "LPIC-3", "description": "Linux Professional Institute Certified: 303 Security\n    $200 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "LPT", "description": "EC Council Licensed Penetration Tester\n    $899 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Linux+", "description": "CompTIA Linux+\n    $369 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MAD CTI", "description": "Mitre Att&ck Defender Cyber Threat intelligence\n                    $299 annual subscription", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MAD SOCA", "description": "Mitre Att&ck Defender Security Operations Center Assessment\n                    $299 annual subscription", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MBT", "description": "Mosse Institute Certified Blue Teamer Certification\n  $450 certification programme\n  100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MCD", "description": "Mosse Institute Certified Code Deobfuscation Specialist Certification\n    $450 certification programme\n    100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MCL", "description": "Mosse Institute Cybersecurity Leadership\n                    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MCPE", "description": "Mosse Institute Certified Cyber Protection Expert\n    $800 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MCPT", "description": "Mosse Institute Cloud Penetration Tester\n    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MCSE", "description": "Mosse Institute Cloud Security Engineer\n                    $600 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MCSF", "description": "Mosse Institute Cloud Services Fundamentals\n                    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MDFIR", "description": "Mosse Institute Certified DFIR Specialist\n  $450 certification programme\n  100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MDSO", "description": "Mosse Institute Certified DevSecOps Engineer\n    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MESE", "description": "Mosse Institute Enterprise Security Engineer\n    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MGRC", "description": "Mosse Institute Certified GRC Expert Certification\n  $450 certification programme\n  100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MICS", "description": "Mosse Institute Introductions to Cyber Security\n                    Free exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MNSE", "description": "Mosse Institute Network Security Essentials\n  $450 certification programme\n  100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MOIS", "description": "MOIS Certified OSINT Expert Certification\n  $450 certification programme\n  100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MPT", "description": "Mosse Institute Certified Penetration Tester Certification\n    $450 certification programme\n    100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MRCI", "description": "Mosse Institute Remote Cybersecurity Internship Programme\n  $49 certification programme\n  100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MRE", "description": "Mosse Institute Certified Reverse Engineer Certification\n    $450 certification programme\n    100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MRT", "description": "Mosse Institute Certified Red Teamer Certification\n  $450 certification programme\n  100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MSAF", "description": "Mosse Institute System Administration Fundamentals\n                    $450 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MTH", "description": "Mosse Institute Certified Threat Hunter Certification\n                    $450 certification programme\n                    100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MTIA", "description": "Mosse Institute Certified Threat Intelligence Analyst Certification\n  $450 certification programme\n  100% practical. No expiry.", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "MVRE", "description": "Mosse Institute Vulnerability Researcher and Exploitation Specialist\n                    $450 Exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "M_o_R Fdn", "description": "Axelos M_o_R Framework Foundation\n    $495 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "M_o_R P", "description": "<PERSON><PERSON> M_o_R Practitioner Risk Management\n    $560 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "NCSC CCPLP", "description": "NCSC Certified Cybersecurity Professional - Lead Practitioner\n    $1388 interview", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "NCSC CCPP", "description": "NCSC Certified Cybersecurity Professional - Practitioner\n    $225 interview", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "NCSC CCPSP", "description": "NCSC Certified Cybersecurity Professional - Senior Practitioner\n    $907 interview", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Net+", "description": "CompTIA Network+\n    $369 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OPSA", "description": "ISECOM OSSTMM Professional Security Analyst\n    $100 annual sub\n    Unknown exam fee", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OPSE", "description": "ISECOM OSSTMM Professional Security Expert\n    $100 annual sub\n    Unknown exam cost", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OPST", "description": "ISECOM OSSTMM Professional Security Tester\n    Unknown", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OSCE3", "description": "Offensive Security Certified Expert 3\n    $4,649 3 labs", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OSED", "description": "Offensive Security Exploit Developer\n    $1,499 lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OSEE", "description": "Offensive Security Exploitation Expert\n    $5,000 lab\n    Plus travel", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OSIP", "description": "IntelTechniques Open Source Intelligence Professional\n    $300 practical exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OSMR", "description": "Offensive Security MacOS Researcher\n    $2,499 exam\n    Learning subscription required", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OSWA", "description": "Offensive Security Web Assessor\n    $2,499 Exam\n    Learning subscription required", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OSWE", "description": "Offensive Security Web Expert\n    ~$1649 lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OSWP", "description": "Offensive Security Wireless Professional\n    $450 labs", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "OWSE", "description": "ISECOM OSSTMM Wireless Security Expert\n    $100 annual sub\n    Unknown exam cost", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PACES", "description": "Pentester Academy Certified Enterprise Security Specialist\n    $339-749 Lab access\n    Exam included", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PCCET", "description": "Palo Alto Networks Certified Cybersecurity Entry-level Technician\n    $110 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PCCSE", "description": "Prisma Certified Cloud Security Engineer\n    $350 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PCDRA", "description": "Palo Alto Networks Certified Detection and Remediation Analyst\n    $155 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PCI QSA", "description": "PCI Qualified Security Assessor\n                    $3000 req'd course", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PCNSA", "description": "Palo Alto Networks Certified Network Security Administrator\n  $155 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PCNSE", "description": "Palo Alto Networks Certified Network Security Engineer\n    $175 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PCSAE", "description": "Palo Alto Certified Cloud Security Automation Engineer\n    $350 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PDSO CDE", "description": "PDSO Certified DevSecOps Expert\n    $1199\n    Exam and training bundled", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PDSO CDP", "description": "PDSO Certified DevSecOps Professional\n    $799\n    Exam and training bundled", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PECB 27001F", "description": "PECB ISO/IEC 27001 Foundation\n    $500-749 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PECB 27001LA", "description": "PECB ISO/IEC 27001 Lead Auditor\n    $930 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PECB 27001LI", "description": "PECB ISO/IEC 27001 Lead Implementer\n    $930 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PECB 27005F", "description": "PECB ISO/IEC 27005 Foundation\n    $500-749 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PECB 27005LM", "description": "PECB ISO/IEC 27005 Lead Risk Manager\n    ~$1,595 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PECB 27005RM", "description": "PECB ISO/IEC 27005 Risk Manager\n    ~$995 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PECB 27032CM", "description": "PECB ISO/IEC 27032 Lead Cybersecurity Manager\n    $899-$2,999 course exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PECB 27032F", "description": "PECB ISO/IEC 27032 Foundation\n    $500-749 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PEXIN ISM", "description": "EXIN Information Security Management Professional\n    $268 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PJMR", "description": "Practical Junior Malware Researcher\n    $399 lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PMI ACP", "description": "PMI Agile Certified Practitioner\n    $495 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PMP", "description": "PMI Project Management Professional\n    $555 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PNPT", "description": "TCM Security Practical Network Penetration Tester\n    $299 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PPM", "description": "GAQM Professional in Project Management\n    $210 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PSM I", "description": "Scrum.org Professional Scrum Master I\n    $150 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PSM II", "description": "Scrum.org Professional Scrum Master II\n    $250 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PSM III", "description": "Scrum.org Professional Scrum Master III\n    $500 exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Pentest+", "description": "CompTIA Pentest+\n    $404 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "PgMP", "description": "PMI Program Management Professional\n    $1,000 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Project+", "description": "CompTIA Project+\n    $369 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "RHCA", "description": "Red Hat Certified Architect\n    ~$3,745 exam\n    plus travel", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "RHCE", "description": "Red Hat Certified Engineer\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "RHCSA", "description": "Red Hat Certified System Administrator\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-CEHL", "description": "SECO Certified Ethical Hacker Leader\n    Application", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-CISO", "description": "SECO Certified Information Security Officer\n    Resume review", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-EHE", "description": "SECO Ethical Hacker Expert\n    TBD (still)\n    Being redesigned", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-EHF", "description": "SECO Ethical Hacking Foundation\n    $460 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-EHP", "description": "SECO Ethical Hacking Practitioner\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-ISF", "description": "SECO Information Security Foundation\n                    $460 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-ISME", "description": "SECO Information Security Management Expert\n    $850 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-ISP", "description": "SECO Information Security Practitioner\n    $550", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-SA", "description": "SECO Associate SOC Analyst\n    $480 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "S-TA", "description": "SECO Certified Threat Analyst\n    $550 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SABSA SCF", "description": "SABSA Chartered Security Architect - Foundation Certificate\n    $3,750 exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SABSA SCM", "description": "SABSA Chartered Security Architect - Master Certificate\n    $3,750 exam & thesis\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SABSA SCP", "description": "SABSA Chartered Security Architect - Practitioner Certificate\n    $3,750 written exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SACP", "description": "The H Layer Security Awareness and Culture Professional\n    $369 Exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SC-100", "description": "Microsoft Cybersecurity Architect\n    $165 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SC-200", "description": "Microsoft Certified: Security Operations Analyst Associate\n    ~$165 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SC-300", "description": "Microsoft Certfied: Identity and Access Administrator Associate\n    $165 exam", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SC-400", "description": "Microsoft Certified Information Protection Administrator Associate\n    $165 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SC-900", "description": "Microsoft Certified: Security, Compliance, and Identity Fundamentals\n    $99 exam", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SCA", "description": "SUSE Certified Administrator\n    $149 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SCE", "description": "SUSE Certified Engineer\n    $195 practical exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SF CIAMD", "description": "SalesForce Certified Identity and Access Management Designer\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Identity & Access Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SFCCCC", "description": "SalesForce Certified Community Cloud Consultant\n    $200 exam\n    Must be SalesForce Admin Certified", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SFCTA", "description": "Salesforce Certified Technical Architect\n    $6000\n    Must be SF SA Certified", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SFSA", "description": "SalesForce System Architect\n    $400 hands-on lab", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SOG CAPen", "description": "The SecOps Group Certified AppSec Pentester\n    $500 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SOG CAPenX", "description": "The SecurityOps Group Certified AppSec Pentesting eXpert\n    $800 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SOG CCSP-AWS", "description": "SecOps Group Certified Cloud Security Practitioner - AWS\n    $249 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SOG CMPen And", "description": "The SecOps Group Certified Mobile Pentester - Android\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SOG CMPen iOS", "description": "The SecOps Group Certified Mobile Pentester - iOS\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SOG CNPen", "description": "The SecOps Group Certified Network Pentester\n    $500 exam", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SOG NSP", "description": "SecOps Group Certified Network Security Practitioner\n    $249 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SPLK-3001", "description": "Splunk Enterprise Security Certified Administrator\n    $130 exam\n    Branded course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "SSAP", "description": "SANS Security Awareness Professional\n    $1219 Exam\n    SANS MGT433 course recommended", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Scrum PAL", "description": "Scrum  Professional Agile Leadership\n    $200 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Scrum PSD", "description": "Scrum Professional Scrum Developer\n    $200 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Scrum SPS", "description": "Scrum Scaled Professional Scrum\n    $250 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Server+", "description": "CompTIA Server+\n    $319 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV Auditor", "description": "TUV Rheinland IT Security Auditor (GERMAN)\n    $415 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV COSM", "description": "TUV Certified OT Security Manager\n    $3,070 Course", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV COSP", "description": "TUV Certified OT Security Practitioner\n    $2725 course", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV COSTE", "description": "TUV Certified OT Security Technical Expert\n    $3,070 course", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV COTCP", "description": "TUV Rheinland Certified Operational Technology Cybersecurity Professional (GERMAN)\n                        $415 exam", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV CyAware", "description": "TUV Rheinland Cybersecurity Awareness (GERMAN)\n    $415 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV CySec", "description": "TUV Rheinland Cybersecurity Specialist (GERMAN)\n    $415 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV ITSM", "description": "TUV IT Security Manager (GERMAN)\n    $415 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "TUV MSA", "description": "TUV Rheinland Mobile Security Analyst (GERMAN)\n    $415 exam\n    Course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Testing", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "VCDX DCV", "description": "VMware Certified Design Expert in Datacenter Virtualization\n    $3,995 exams\n    Application also req.", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "VCIX DCV", "description": "VMware Certified Implementation Expert in Datacenter Virtualization\n    $900 two exams", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "VCIX NV", "description": "VMware Certified Implementation Expert in Network Virtualization\n    $900 two exams", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "VCP DCV", "description": "VMware Certified Professional in Datacenter Virtualization\n    $375 exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "VCP NV", "description": "VMware Certified Professional in Network Virtualization\n    $375 exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Engineering", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "WCNA", "description": "Protocol Analysis Institute Wireshark Certified Network Analyst\n    $299 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Zach EAA", "description": "Zachman Enterprise Architect Associate (Level 1)\n    $2,999 course exam\n    Branded course required", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "Zach <PERSON>", "description": "Zachman Enterprise Architect Practitioner (Level 2)\n    $2,999 exam & case study\n    Level 1 cert not req'd", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "<PERSON>", "description": "Zachman Enterprise Architect Professional (Level 3)\n    $2,999 exam & case study\n    Level 1 & 2 cert not req'd", "difficulty": 1, "level": "Entry Level", "domain": "Security Management", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "eCDFP", "description": "eLearnSecurity Certified Digital Forensics Professional\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "eCIR", "description": "eLearnSecurity Certified Incident Responder\n    $400 lab", "difficulty": 1, "level": "Entry Level", "domain": "Defensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "eCPPT", "description": "eLearnSecurity Certified Professional Penetration Tester\n    $400 lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "eJPT", "description": "eLearnSecurity Junior Penetration Tester\n    $249 lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "eMAPT", "description": "eLearnSecurity Mobile Application Penetration Tester\n    $400", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "eNDP", "description": "eLearnSecurity Network Defense Professional\n    $400 exam", "difficulty": 1, "level": "Entry Level", "domain": "Network Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "eWPT", "description": "eLearnSecurity Web Application Penetration Tester\n    $400 lab", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}, {"name": "eWPTX", "description": "eLearnSecurity Web Application Penetration Tester eXtreme\n    $400 exam\n    $2000 training", "difficulty": 1, "level": "Entry Level", "domain": "Offensive Security", "prerequisites": [], "category": "Entry Level Security Certifications"}]}, {"category": "Mid-Level Security Certifications", "certifications": [{"name": "APMG 20000A", "description": "APMG ISO/IEC 20000 Auditor\n    $308 Exam\n    Possible Course Req", "difficulty": 2, "level": "Intermediate", "domain": "Security Testing", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "ASIS APP", "description": "ASIS Associate Protection Professional\n    $350 exam", "difficulty": 2, "level": "Intermediate", "domain": "Asset Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "ASIS CPP", "description": "ASIS Certified Protection Professional\n    $485 exam", "difficulty": 2, "level": "Intermediate", "domain": "Asset Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "AWS CSS", "description": "Amazon Web Services Certified Security - Specialty\n    $150 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Engineering", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "AZ-500", "description": "Microsoft Azure Security Engineer Associate\n    $165 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Engineering", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "BCS PCIAA", "description": "BCS Practitioner Certificate in Information Assurance Architecture\n    $290 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Management", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "BTL1", "description": "Security Blue Team Level 1\n    $660 course\n    1 exam attempt included", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "BTL2", "description": "Security Blue Team Level 2\n    $2,190 course\n    1 exam attempt included", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "CCIE Ent", "description": "Cisco Certified Internetwork Expert - Enterprise Infrastructure\n    ~$2,050 hands-on lab\n    ~$12,000 in travel costs", "difficulty": 2, "level": "Intermediate", "domain": "Network Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "CCIE Sec", "description": "Cisco Certified Implementation Expert - Security\n    $2,050 Hands-on Lab\n    $12,000 est Travel cost", "difficulty": 2, "level": "Intermediate", "domain": "Network Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "CDP", "description": "IMI Certified in Data Protection\n    $395 Exam", "difficulty": 2, "level": "Intermediate", "domain": "Asset Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "CDPSE", "description": "ISACA Certified Data Privacy Solutions Engineer\n    $880 Application", "difficulty": 2, "level": "Intermediate", "domain": "Asset Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "CEH", "description": "EC Council Certified Ethical Hacker\n    $1,199 exam", "difficulty": 2, "level": "Intermediate", "domain": "Offensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "CISA", "description": "ISACA Certified Information Systems Auditor\n    $760 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Testing", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "CREST CSAM", "description": "CREST Certified Simulated Attack Manager\n    $2,499 2 exams", "difficulty": 2, "level": "Intermediate", "domain": "Offensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "CSR", "description": "Cyber Struggle Ranger\n    Location Based Cost\n    Course Req", "difficulty": 2, "level": "Intermediate", "domain": "Offensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "DCBCA", "description": "DRI Certified Business Continuity Auditor\n    $400 exam\n    Application req", "difficulty": 2, "level": "Intermediate", "domain": "Security Testing", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "DCBCLA", "description": "DRI Certified Business Continuity Lead Auditor\n    $400 exam\n    Application req", "difficulty": 2, "level": "Intermediate", "domain": "Security Testing", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GAWN", "description": "GIAC Assessing Wireless Networks\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Offensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GCED", "description": "GIAC Certified Enterprise Defender\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GCFA", "description": "GIAC Certified Forensic Analyst\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GCFE", "description": "GIAC Cerified Forensics Examiner\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GCIA", "description": "GIAC Certified Intrusion Analyst\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Security Testing", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GCIH", "description": "GIAC Certified Forensics Analystr\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GFACT", "description": "GIAC Foundational Cybersecurity Technologies\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GIME", "description": "GIAC iOS and MacOS Examiner\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "GXPN", "description": "GIAC Exploit Researcher and Advanced Penetration Tester\n    $979 exam\n    SANS course recommended", "difficulty": 2, "level": "Intermediate", "domain": "Offensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "IS20", "description": "Mile2 IS20 Controls\n    $550 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Testing", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "ITIL Fdn", "description": "ITIL Foundation\n    $383 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Management", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "ITIL MP", "description": "ITIL Managing Professional\n    $9,600 4 course exams\n    4 branded courses requires", "difficulty": 2, "level": "Intermediate", "domain": "Security Management", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "ITIL Master", "description": "ITIL Master\n    $4,000 Interview", "difficulty": 2, "level": "Intermediate", "domain": "Security Management", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "MS-100", "description": "Microsoft 365 Certified Enterprise Administrator Expert\n    $165 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Engineering", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "OSCP", "description": "Offensive Security Certified Professional\n    $1,499 labs", "difficulty": 2, "level": "Intermediate", "domain": "Offensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "OSDA", "description": "Offensive Security Defense Analyst\n    $2,499 exam\n    Learning subscription required", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "OSEP", "description": "Offensive Security Experienced Penetration Tester\n    $1,499 lab", "difficulty": 2, "level": "Intermediate", "domain": "Offensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "TOGAF", "description": "OpenGroup TOGAF Certified\n    $360 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Management", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "TOGAF Fdn", "description": "OpenGroup TOGAF Certified\n    $360 exam", "difficulty": 2, "level": "Intermediate", "domain": "Security Management", "prerequisites": [], "category": "Mid-Level Security Certifications"}, {"name": "eCTHP", "description": "eLearnSecurity Certified Threat Hunting Professional\n    $400 lab", "difficulty": 2, "level": "Intermediate", "domain": "Defensive Security", "prerequisites": [], "category": "Mid-Level Security Certifications"}]}, {"category": "Advanced Security Certifications", "certifications": [{"name": "CASP+", "description": "CompTIA Advanced Security Practitioner+\n    $509 exam", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Advanced Security Certifications"}, {"name": "CRISC", "description": "ISACA Certified in Risk and Information Systems Control\n    $760 exam", "difficulty": 3, "level": "Advanced", "domain": "Security Management", "prerequisites": [], "category": "Advanced Security Certifications"}, {"name": "GREM", "description": "GIAC Reverse Engineering Malware\n    $979 exam\n    SANS course recommended", "difficulty": 3, "level": "Advanced", "domain": "Defensive Security", "prerequisites": [], "category": "Advanced Security Certifications"}]}, {"category": "Expert Security Certifications", "certifications": [{"name": "CISSP", "description": "(ISC)2 Certified Information Systems Security Professional\n    $749 exam", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Expert Security Certifications"}, {"name": "CISSP Concentrations", "description": "(ISC)2 Certified Information Systems Security Professional Concentrations\n    $599 exam", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Expert Security Certifications"}, {"name": "CSSA", "description": "Infosec Institute Certified SCADA Security Architect\n    $4,599 exam\n    Course required", "difficulty": 4, "level": "Expert", "domain": "Security Engineering", "prerequisites": [], "category": "Expert Security Certifications"}, {"name": "GSE", "description": "GIAC Security Expert\n    ~$7475 for 10 exams", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Expert Security Certifications"}, {"name": "GSEC", "description": "GIAC Security Essentials Certification\n    $979 exam\n    SANS course recommended", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Expert Security Certifications"}, {"name": "GSP", "description": "GIAC Security Professional\n    ~$3735 for 5 exams", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Expert Security Certifications"}, {"name": "Programming Language", "description": "Learning a programming language is valuable to any IT professionals career.\n    Recommendations: Python, Ruby, C++", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Expert Security Certifications"}, {"name": "SSCP", "description": "(ISC)2 Systems Security Certified Practitioner\n    $249 exam", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Expert Security Certifications"}, {"name": "Security+", "description": "CompTIA Security+\n    $404 exam", "difficulty": 4, "level": "Expert", "domain": "Security Management", "prerequisites": [], "category": "Expert Security Certifications"}]}]