/**
 * Offline utilities for CertPathFinder PWA
 * Handles offline data storage, sync, and service worker management
 */

interface OfflineData {
  id: string;
  type: string;
  data: any;
  timestamp: number;
  synced: boolean;
}

interface SyncQueueItem {
  id: string;
  method: string;
  url: string;
  data: any;
  timestamp: number;
  retries: number;
}

class OfflineManager {
  private dbName = 'CertPathFinderOffline';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;
  private isOnline = navigator.onLine;
  private syncQueue: SyncQueueItem[] = [];
  private maxRetries = 3;

  constructor() {
    this.initializeDB();
    this.setupEventListeners();
    this.registerServiceWorker();
  }

  /**
   * Initialize IndexedDB for offline storage
   */
  private async initializeDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores
        if (!db.objectStoreNames.contains('progress')) {
          const progressStore = db.createObjectStore('progress', { keyPath: 'id' });
          progressStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        if (!db.objectStoreNames.contains('studySessions')) {
          const sessionStore = db.createObjectStore('studySessions', { keyPath: 'id' });
          sessionStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        if (!db.objectStoreNames.contains('certifications')) {
          const certStore = db.createObjectStore('certifications', { keyPath: 'id' });
          certStore.createIndex('name', 'name', { unique: false });
        }

        if (!db.objectStoreNames.contains('syncQueue')) {
          const syncStore = db.createObjectStore('syncQueue', { keyPath: 'id' });
          syncStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  /**
   * Setup event listeners for online/offline status
   */
  private setupEventListeners(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processSyncQueue();
      this.notifyOnlineStatus(true);
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.notifyOnlineStatus(false);
    });
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered:', registration);

        // Listen for service worker updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New service worker available
                this.notifyServiceWorkerUpdate();
              }
            });
          }
        });
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  /**
   * Store data offline
   */
  async storeOfflineData(storeName: string, data: OfflineData): Promise<void> {
    if (!this.db) await this.initializeDB();

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Retrieve offline data
   */
  async getOfflineData(storeName: string, id?: string): Promise<any[]> {
    if (!this.db) await this.initializeDB();

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      
      let request: IDBRequest;
      if (id) {
        request = store.get(id);
      } else {
        request = store.getAll();
      }

      request.onsuccess = () => {
        const result = request.result;
        resolve(id ? (result ? [result] : []) : result || []);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Add item to sync queue
   */
  async addToSyncQueue(method: string, url: string, data: any): Promise<void> {
    const queueItem: SyncQueueItem = {
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      method,
      url,
      data,
      timestamp: Date.now(),
      retries: 0
    };

    this.syncQueue.push(queueItem);
    await this.storeOfflineData('syncQueue', queueItem as any);

    // Try to sync immediately if online
    if (this.isOnline) {
      this.processSyncQueue();
    }
  }

  /**
   * Process sync queue when back online
   */
  private async processSyncQueue(): Promise<void> {
    if (!this.isOnline || this.syncQueue.length === 0) return;

    const queueItems = await this.getOfflineData('syncQueue');
    
    for (const item of queueItems) {
      try {
        const response = await fetch(item.url, {
          method: item.method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: item.method !== 'GET' ? JSON.stringify(item.data) : undefined,
        });

        if (response.ok) {
          // Remove from queue on success
          await this.removeFromSyncQueue(item.id);
          console.log(`Synced: ${item.method} ${item.url}`);
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        console.error(`Sync failed for ${item.url}:`, error);
        
        // Increment retry count
        item.retries++;
        if (item.retries >= this.maxRetries) {
          // Remove from queue after max retries
          await this.removeFromSyncQueue(item.id);
          console.error(`Max retries reached for ${item.url}, removing from queue`);
        } else {
          // Update retry count in storage
          await this.storeOfflineData('syncQueue', item as any);
        }
      }
    }
  }

  /**
   * Remove item from sync queue
   */
  private async removeFromSyncQueue(id: string): Promise<void> {
    if (!this.db) return;

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['syncQueue'], 'readwrite');
      const store = transaction.objectStore('syncQueue');
      const request = store.delete(id);

      request.onsuccess = () => {
        this.syncQueue = this.syncQueue.filter(item => item.id !== id);
        resolve();
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Check if app is running offline
   */
  isOffline(): boolean {
    return !this.isOnline;
  }

  /**
   * Notify about online status change
   */
  private notifyOnlineStatus(isOnline: boolean): void {
    const event = new CustomEvent('onlineStatusChange', {
      detail: { isOnline }
    });
    window.dispatchEvent(event);
  }

  /**
   * Notify about service worker update
   */
  private notifyServiceWorkerUpdate(): void {
    const event = new CustomEvent('serviceWorkerUpdate');
    window.dispatchEvent(event);
  }
}

// Create singleton instance
export const offlineManager = new OfflineManager();

// Export utility functions
export const isOffline = () => offlineManager.isOffline();
export const storeOffline = (storeName: string, data: OfflineData) => 
  offlineManager.storeOfflineData(storeName, data);
export const getOffline = (storeName: string, id?: string) => 
  offlineManager.getOfflineData(storeName, id);
export const addToSyncQueue = (method: string, url: string, data: any) => 
  offlineManager.addToSyncQueue(method, url, data);

export default offlineManager;
