/**
 * User-related type definitions
 */

// User Role
export type UserRole = 
  | 'student'
  | 'professional'
  | 'manager'
  | 'admin'
  | 'enterprise_admin';

// User Experience Level
export type ExperienceLevel =
  | 'entry_level'
  | 'junior'
  | 'mid_level'
  | 'senior'
  | 'expert'
  | 'executive';

// Learning Style
export type LearningStyle =
  | 'visual'
  | 'auditory'
  | 'kinesthetic'
  | 'reading'
  | 'mixed';

// User Interface
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  
  // Profile information
  title?: string;
  company?: string;
  location?: string;
  bio?: string;
  website?: string;
  linkedin_url?: string;
  github_url?: string;
  
  // Experience and preferences
  years_experience: number;
  experience_level: ExperienceLevel;
  current_role?: string;
  desired_role?: string;
  expertise_areas: string[];
  learning_style: LearningStyle;
  study_time_available: number; // hours per week
  
  // Settings
  email_notifications: boolean;
  push_notifications: boolean;
  privacy_level: 'public' | 'private' | 'connections_only';
  
  // Onboarding
  tutorial_completed: boolean;
  onboarding_step: number;
  
  // Metadata
  is_active: boolean;
  is_verified: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

// User Profile Update
export interface UserProfileUpdate {
  name?: string;
  title?: string;
  company?: string;
  location?: string;
  bio?: string;
  website?: string;
  linkedin_url?: string;
  github_url?: string;
  years_experience?: number;
  experience_level?: ExperienceLevel;
  current_role?: string;
  desired_role?: string;
  expertise_areas?: string[];
  learning_style?: LearningStyle;
  study_time_available?: number;
  email_notifications?: boolean;
  push_notifications?: boolean;
  privacy_level?: 'public' | 'private' | 'connections_only';
}

// User Preferences
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  date_format: string;
  currency: string;
  
  // Dashboard preferences
  dashboard_layout: 'grid' | 'list';
  default_view: 'certifications' | 'progress' | 'recommendations';
  items_per_page: number;
  
  // Notification preferences
  email_frequency: 'immediate' | 'daily' | 'weekly' | 'never';
  reminder_frequency: 'daily' | 'weekly' | 'monthly';
  achievement_notifications: boolean;
  deadline_reminders: boolean;
  
  // Privacy preferences
  profile_visibility: 'public' | 'private' | 'connections_only';
  show_progress: boolean;
  show_achievements: boolean;
  allow_contact: boolean;
}

// User Statistics
export interface UserStats {
  certifications_completed: number;
  certifications_in_progress: number;
  total_study_hours: number;
  average_score: number;
  streak_days: number;
  achievements_earned: number;
  rank: number;
  percentile: number;
}

// User Achievement
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'study' | 'certification' | 'streak' | 'social' | 'special';
  points: number;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  earned_at?: string;
  progress?: number;
  max_progress?: number;
}

// User Activity
export interface UserActivity {
  id: string;
  type: 'login' | 'study_session' | 'certification_completed' | 'achievement_earned' | 'profile_updated';
  description: string;
  metadata?: Record<string, any>;
  timestamp: string;
}
