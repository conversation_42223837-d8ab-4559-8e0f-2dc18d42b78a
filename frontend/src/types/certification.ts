/**
 * Certification-related type definitions
 */

// Certification Organization
export interface Organization {
  id: number;
  name: string;
  website?: string;
  description?: string;
  logo_url?: string;
}

// Certification Domain
export interface SecurityDomain {
  id: number;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
}

// Certification Level
export type CertificationLevel = 
  | 'Entry Level'
  | 'Associate'
  | 'Professional'
  | 'Expert'
  | 'Specialist'
  | 'Advanced';

// Certification Difficulty
export type CertificationDifficulty = 
  | 'Beginner'
  | 'Intermediate' 
  | 'Advanced'
  | 'Expert';

// Certification Focus Area
export type CertificationFocus =
  | 'General'
  | 'Technical'
  | 'Management'
  | 'Compliance'
  | 'Specialized';

// Main Certification Interface
export interface Certification {
  id: number;
  name: string;
  description?: string;
  level: CertificationLevel;
  difficulty: CertificationDifficulty;
  focus: CertificationFocus;
  domain: string;
  category: string;
  
  // Organization details
  organization: Organization;
  
  // Cost information
  cost: number;
  currency: string;
  
  // Prerequisites
  prerequisites?: string[];
  recommended_experience?: string;
  
  // Exam details
  exam_code?: string;
  exam_duration?: number; // in minutes
  passing_score?: number;
  question_count?: number;
  
  // URLs and resources
  url?: string;
  study_guide_url?: string;
  practice_test_url?: string;
  
  // Validity and renewal
  validity_period?: number; // in months
  renewal_required?: boolean;
  continuing_education_units?: number;
  
  // Metadata
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  
  // Related certifications
  related_certifications?: number[];
  progression_path?: number[];
}

// Certification Filter Options
export interface CertificationFilters {
  domains?: string[];
  levels?: CertificationLevel[];
  difficulties?: CertificationDifficulty[];
  organizations?: number[];
  cost_min?: number;
  cost_max?: number;
  search?: string;
  is_active?: boolean;
}

// Certification Statistics
export interface CertificationStats {
  total_certifications: number;
  total_domains: number;
  total_organizations: number;
  average_cost: number;
  cost_range: {
    min: number;
    max: number;
  };
  level_distribution: Record<CertificationLevel, number>;
  domain_distribution: Record<string, number>;
}

// Certification Comparison
export interface CertificationComparison {
  certifications: Certification[];
  comparison_matrix: {
    [key: string]: {
      [certId: number]: string | number | boolean;
    };
  };
}

// Study Progress
export interface StudyProgress {
  certification_id: number;
  user_id: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'expired';
  progress_percentage: number;
  start_date?: string;
  target_date?: string;
  completion_date?: string;
  study_hours: number;
  practice_test_scores: number[];
  notes?: string;
}
