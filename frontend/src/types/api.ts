/**
 * API-related type definitions
 */

// Base API Response
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

// Error Response
export interface ApiError {
  detail: string;
  status_code: number;
  timestamp: string;
}

// Pagination
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  has_next: boolean;
  has_prev: boolean;
}

// Health Check
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    api: {
      status: 'up' | 'down';
      port: number;
    };
    database: {
      status: 'connected' | 'error';
      last_checked: string;
      error?: string;
    };
    job_search?: {
      status: 'ready' | 'not_configured';
      last_checked: string;
      endpoints: number;
      routes: string[];
    };
  };
}

// Request/Response interceptor types
export interface RequestConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
}

export interface ResponseConfig<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: RequestConfig;
}
