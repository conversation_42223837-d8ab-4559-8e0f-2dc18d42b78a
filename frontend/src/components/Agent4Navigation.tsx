import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { 
  Target, 
  Calculator, 
  TrendingUp, 
  Globe,
  BarChart3,
  DollarSign,
  Award,
  Users,
  Building,
  ArrowRight,
  CheckCircle,
  Clock,
  Star
} from 'lucide-react';

interface NavigationProps {
  currentPage?: string;
  onNavigate?: (page: string) => void;
  className?: string;
}

const Agent4Navigation: React.FC<NavigationProps> = ({ 
  currentPage = 'overview', 
  onNavigate,
  className 
}) => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  const dashboards = [
    {
      id: 'career-planning',
      title: 'Career Planning',
      description: 'AI-powered career pathfinding and transition planning',
      icon: Target,
      color: 'blue',
      features: [
        'A* Pathfinding Algorithm',
        'Cost-Benefit Analysis',
        'Timeline Optimization',
        'Success Probability'
      ],
      status: 'active',
      completionRate: 95
    },
    {
      id: 'roi-analysis',
      title: 'ROI Analysis',
      description: 'Comprehensive return on investment analysis for certifications',
      icon: Calculator,
      color: 'green',
      features: [
        'Multi-year Projections',
        'Risk Assessment',
        'Market Intelligence',
        'Confidence Scoring'
      ],
      status: 'active',
      completionRate: 92
    },
    {
      id: 'budget-optimization',
      title: 'Budget Optimization',
      description: 'Enterprise training budget allocation and optimization',
      icon: BarChart3,
      color: 'purple',
      features: [
        'Allocation Optimization',
        'Efficiency Metrics',
        'Cost Savings Analysis',
        'Implementation Planning'
      ],
      status: 'active',
      completionRate: 88
    },
    {
      id: 'market-intelligence',
      title: 'Market Intelligence',
      description: 'Real-time market trends and competitive intelligence',
      icon: Globe,
      color: 'orange',
      features: [
        'Trend Analysis',
        'Location Insights',
        'Industry Benchmarks',
        'Demand Forecasting'
      ],
      status: 'active',
      completionRate: 85
    }
  ];

  const getColorClasses = (color: string, isHovered: boolean = false) => {
    const baseClasses = {
      blue: {
        icon: 'text-blue-600',
        border: isHovered ? 'border-blue-500' : 'border-blue-200',
        bg: isHovered ? 'bg-blue-50' : 'bg-white',
        badge: 'bg-blue-100 text-blue-800'
      },
      green: {
        icon: 'text-green-600',
        border: isHovered ? 'border-green-500' : 'border-green-200',
        bg: isHovered ? 'bg-green-50' : 'bg-white',
        badge: 'bg-green-100 text-green-800'
      },
      purple: {
        icon: 'text-purple-600',
        border: isHovered ? 'border-purple-500' : 'border-purple-200',
        bg: isHovered ? 'bg-purple-50' : 'bg-white',
        badge: 'bg-purple-100 text-purple-800'
      },
      orange: {
        icon: 'text-orange-600',
        border: isHovered ? 'border-orange-500' : 'border-orange-200',
        bg: isHovered ? 'bg-orange-50' : 'bg-white',
        badge: 'bg-orange-100 text-orange-800'
      }
    };
    return baseClasses[color as keyof typeof baseClasses] || baseClasses.blue;
  };

  const handleCardClick = (dashboardId: string) => {
    if (onNavigate) {
      onNavigate(dashboardId);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full">
            <Award className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-4xl font-bold tracking-tight">Agent 4</h1>
            <p className="text-xl text-muted-foreground">Career & Cost Intelligence</p>
          </div>
        </div>
        
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Comprehensive AI-powered platform for cybersecurity career advancement, 
          cost optimization, and market intelligence
        </p>
      </div>

      {/* System Status */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-6 w-6 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-800">System Status: Operational</h3>
                <p className="text-sm text-green-600">All Agent 4 services are running optimally</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">99.9%</div>
                <div className="text-xs text-green-600">Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">1.2s</div>
                <div className="text-xs text-blue-600">Avg Response</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {dashboards.map((dashboard) => {
          const isHovered = hoveredCard === dashboard.id;
          const isCurrent = currentPage === dashboard.id;
          const colorClasses = getColorClasses(dashboard.color, isHovered || isCurrent);
          const IconComponent = dashboard.icon;

          return (
            <Card
              key={dashboard.id}
              className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${colorClasses.border} ${colorClasses.bg} ${
                isCurrent ? 'ring-2 ring-offset-2 ring-blue-500' : ''
              }`}
              onMouseEnter={() => setHoveredCard(dashboard.id)}
              onMouseLeave={() => setHoveredCard(null)}
              onClick={() => handleCardClick(dashboard.id)}
              data-testid={`dashboard-${dashboard.id}`}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg bg-white shadow-sm`}>
                      <IconComponent className={`h-6 w-6 ${colorClasses.icon}`} />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{dashboard.title}</CardTitle>
                      <Badge className={`text-xs ${colorClasses.badge}`}>
                        {dashboard.status}
                      </Badge>
                    </div>
                  </div>
                  <ArrowRight className={`h-5 w-5 transition-transform ${isHovered ? 'translate-x-1' : ''} ${colorClasses.icon}`} />
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {dashboard.description}
                </p>

                {/* Completion Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs font-medium">Implementation</span>
                    <span className="text-xs text-muted-foreground">
                      {dashboard.completionRate}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-500 ${
                        dashboard.color === 'blue' ? 'bg-blue-600' :
                        dashboard.color === 'green' ? 'bg-green-600' :
                        dashboard.color === 'purple' ? 'bg-purple-600' :
                        'bg-orange-600'
                      }`}
                      style={{ width: `${dashboard.completionRate}%` }}
                    />
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2">
                  <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                    Key Features
                  </h4>
                  <div className="grid grid-cols-2 gap-1">
                    {dashboard.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-1">
                        <Star className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action Button */}
                <Button 
                  className={`w-full mt-4 ${
                    dashboard.color === 'blue' ? 'bg-blue-600 hover:bg-blue-700' :
                    dashboard.color === 'green' ? 'bg-green-600 hover:bg-green-700' :
                    dashboard.color === 'purple' ? 'bg-purple-600 hover:bg-purple-700' :
                    'bg-orange-600 hover:bg-orange-700'
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCardClick(dashboard.id);
                  }}
                >
                  {isCurrent ? 'Current Dashboard' : 'Open Dashboard'}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 mx-auto mb-2 text-blue-600" />
            <div className="text-2xl font-bold">10K+</div>
            <div className="text-sm text-muted-foreground">Active Users</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Target className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <div className="text-2xl font-bold">50K+</div>
            <div className="text-sm text-muted-foreground">Career Paths</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <DollarSign className="h-8 w-8 mx-auto mb-2 text-purple-600" />
            <div className="text-2xl font-bold">$2M+</div>
            <div className="text-sm text-muted-foreground">Budget Optimized</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-orange-600" />
            <div className="text-2xl font-bold">250%</div>
            <div className="text-sm text-muted-foreground">Avg ROI</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { action: 'Career path optimized', user: 'Security Analyst', time: '2 minutes ago', type: 'success' },
              { action: 'Budget allocation updated', user: 'Enterprise Client', time: '5 minutes ago', type: 'info' },
              { action: 'ROI analysis completed', user: 'Senior Engineer', time: '8 minutes ago', type: 'success' },
              { action: 'Market data refreshed', user: 'System', time: '15 minutes ago', type: 'info' }
            ].map((activity, index) => (
              <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50">
                <div className={`h-2 w-2 rounded-full ${
                  activity.type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                }`} />
                <div className="flex-1">
                  <div className="text-sm font-medium">{activity.action}</div>
                  <div className="text-xs text-muted-foreground">{activity.user} • {activity.time}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Agent4Navigation;
