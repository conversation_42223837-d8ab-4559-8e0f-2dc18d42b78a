import React, { useState } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
import { 
  XMarkIcon, 
  PlusIcon, 
  PlayIcon, 
  CheckIcon,
  BookOpenIcon,
  ClockIcon,
  FlagIcon
} from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
// import { Select } from '../ui/select'; // TODO: Create select component

interface QuickActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  actionType: string;
  certification?: any;
  onAction: (action: string, certificationId?: string, additionalData?: any) => Promise<void>;
}

export const QuickActionModal: React.FC<QuickActionModalProps> = ({
  isOpen,
  onClose,
  actionType,
  certification,
  onAction
}) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    learningPathId: '',
    studyHours: '',
    goalType: '',
    goalTarget: '',
    goalDeadline: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      let additionalData = {};

      switch (actionType) {
        case 'add_to_path':
          additionalData = { learning_path_id: formData.learningPathId };
          break;
        case 'start_study':
          additionalData = { study_hours: parseInt(formData.studyHours) };
          break;
        case 'set_goal':
          additionalData = {
            goal_data: {
              type: formData.goalType,
              target: formData.goalTarget,
              deadline: formData.goalDeadline
            }
          };
          break;
      }

      await onAction(actionType, certification?.id, additionalData);
      onClose();
      
      // Reset form
      setFormData({
        learningPathId: '',
        studyHours: '',
        goalType: '',
        goalTarget: '',
        goalDeadline: ''
      });
    } catch (error) {
      console.error('Quick action failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionTitle = () => {
    switch (actionType) {
      case 'add_to_path':
        return 'Add to Learning Path';
      case 'start_study':
        return 'Start Study Session';
      case 'mark_complete':
        return 'Mark as Complete';
      case 'set_goal':
        return 'Set Learning Goal';
      default:
        return 'Quick Action';
    }
  };

  const getActionIcon = () => {
    switch (actionType) {
      case 'add_to_path':
        return <PlusIcon className="w-6 h-6" />;
      case 'start_study':
        return <PlayIcon className="w-6 h-6" />;
      case 'mark_complete':
        return <CheckIcon className="w-6 h-6" />;
      case 'set_goal':
        return <FlagIcon className="w-6 h-6" />;
      default:
        return <BookOpenIcon className="w-6 h-6" />;
    }
  };

  const renderActionForm = () => {
    switch (actionType) {
      case 'add_to_path':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Learning Path
              </label>
              <select
                value={formData.learningPathId}
                onChange={(e) => setFormData(prev => ({ ...prev, learningPathId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                data-testid="learning-path-select"
              >
                <option value="">Choose a learning path</option>
                <option value="path1">AWS Solutions Architect Path</option>
                <option value="path2">Cybersecurity Fundamentals</option>
                <option value="path3">Cloud DevOps Engineer</option>
              </select>
            </div>
            {certification && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-600">Adding certification:</p>
                <p className="font-medium text-gray-900">{certification.name}</p>
                <p className="text-sm text-gray-500">{certification.provider}</p>
              </div>
            )}
          </div>
        );

      case 'start_study':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Planned Study Duration (hours)
              </label>
              <Input
                type="number"
                min="0.5"
                max="8"
                step="0.5"
                value={formData.studyHours}
                onChange={(e) => setFormData(prev => ({ ...prev, studyHours: e.target.value }))}
                placeholder="2.0"
                data-testid="study-hours-input"
              />
            </div>
            {certification && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center space-x-2">
                  <ClockIcon className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-blue-900">{certification.name}</p>
                    <p className="text-sm text-blue-700">
                      Estimated: {certification.estimated_hours} hours total
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'mark_complete':
        return (
          <div className="space-y-4">
            {certification && (
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <CheckIcon className="w-8 h-8 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-green-900">
                      Mark {certification.name} as Complete
                    </h3>
                    <p className="text-sm text-green-700 mt-1">
                      Congratulations on completing this certification! This will update your progress and unlock new recommendations.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'set_goal':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Goal Type
              </label>
              <select
                value={formData.goalType}
                onChange={(e) => setFormData(prev => ({ ...prev, goalType: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                data-testid="goal-type-select"
              >
                <option value="">Select goal type</option>
                <option value="certification">Complete Certification</option>
                <option value="study_hours">Study Hours Target</option>
                <option value="learning_path">Finish Learning Path</option>
                <option value="exam_date">Schedule Exam</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target
              </label>
              <Input
                value={formData.goalTarget}
                onChange={(e) => setFormData(prev => ({ ...prev, goalTarget: e.target.value }))}
                placeholder="e.g., AWS Solutions Architect, 40 hours"
                data-testid="goal-target-input"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target Date
              </label>
              <Input
                type="date"
                value={formData.goalDeadline}
                onChange={(e) => setFormData(prev => ({ ...prev, goalDeadline: e.target.value }))}
                data-testid="goal-deadline-input"
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">Select an action to get started</p>
          </div>
        );
    }
  };

  const isFormValid = () => {
    switch (actionType) {
      case 'add_to_path':
        return formData.learningPathId !== '';
      case 'start_study':
        return formData.studyHours !== '' && parseFloat(formData.studyHours) > 0;
      case 'mark_complete':
        return true;
      case 'set_goal':
        return formData.goalType !== '' && formData.goalTarget !== '' && formData.goalDeadline !== '';
      default:
        return false;
    }
  };

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={onClose}
          />

          {/* Modal */}
          <div className="flex min-h-full items-center justify-center p-4">
            <div
              className="relative w-full max-w-md bg-white rounded-lg shadow-xl"
              data-testid="quick-action-modal"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
                    {getActionIcon()}
                  </div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {getActionTitle()}
                  </h2>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md"
                  data-testid="close-quick-action"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Content */}
              <form onSubmit={handleSubmit} className="p-6">
                {renderActionForm()}

                {/* Actions */}
                <div className="flex space-x-3 mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    className="flex-1"
                    data-testid="cancel-action"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="default"
                    loading={loading}
                    disabled={!isFormValid() || loading}
                    className="flex-1"
                    data-testid="confirm-action"
                  >
                    {loading ? 'Processing...' : 'Confirm'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
