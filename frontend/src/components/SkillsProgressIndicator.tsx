/**
 * Skills Progress Indicator Component
 * Feature 1.1: Skills Vector Representation & Scoring - UI Design Phase
 * Shows visual progress indicators for skill development
 */
import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface SkillProgressData {
  skill_name: string;
  current_score: number;
  previous_score?: number;
  target_score?: number;
  domain: string;
  last_updated: string;
}

interface SkillsProgressIndicatorProps {
  skills: SkillProgressData[];
  showTrends?: boolean;
  compact?: boolean;
}

const SkillsProgressIndicator: React.FC<SkillsProgressIndicatorProps> = ({
  skills,
  showTrends = true,
  compact = false
}) => {
  const getSkillLevelInfo = (score: number) => {
    if (score >= 0.8) return { label: 'Expert', color: 'bg-purple-500', textColor: 'text-purple-700' };
    if (score >= 0.6) return { label: 'Advanced', color: 'bg-orange-500', textColor: 'text-orange-700' };
    if (score >= 0.4) return { label: 'Intermediate', color: 'bg-green-500', textColor: 'text-green-700' };
    if (score >= 0.2) return { label: 'Basic', color: 'bg-blue-500', textColor: 'text-blue-700' };
    return { label: 'Beginner', color: 'bg-gray-500', textColor: 'text-gray-700' };
  };

  const getTrendIcon = (current: number, previous?: number) => {
    if (!previous) return null;
    
    const diff = current - previous;
    if (diff > 0.05) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (diff < -0.05) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const getTrendColor = (current: number, previous?: number) => {
    if (!previous) return 'text-gray-600';
    
    const diff = current - previous;
    if (diff > 0.05) return 'text-green-600';
    if (diff < -0.05) return 'text-red-600';
    return 'text-gray-600';
  };

  if (compact) {
    return (
      <div className="space-y-2">
        {skills.slice(0, 5).map((skill) => {
          const levelInfo = getSkillLevelInfo(skill.current_score);
          const scorePercent = Math.round(skill.current_score * 100);
          
          return (
            <div key={skill.skill_name} className="flex items-center space-x-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-900 truncate capitalize">
                    {skill.skill_name.replace(/_/g, ' ')}
                  </span>
                  <div className="flex items-center space-x-2">
                    {showTrends && getTrendIcon(skill.current_score, skill.previous_score)}
                    <span className="text-sm text-gray-500">{scorePercent}%</span>
                  </div>
                </div>
                <Progress value={scorePercent} className="h-1.5" />
              </div>
            </div>
          );
        })}
        {skills.length > 5 && (
          <div className="text-center pt-2">
            <span className="text-sm text-gray-500">+{skills.length - 5} more skills</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {skills.map((skill) => {
        const levelInfo = getSkillLevelInfo(skill.current_score);
        const scorePercent = Math.round(skill.current_score * 100);
        const targetPercent = skill.target_score ? Math.round(skill.target_score * 100) : null;
        const trendColor = getTrendColor(skill.current_score, skill.previous_score);
        
        return (
          <Card key={skill.skill_name} className="border-l-4" style={{ borderLeftColor: levelInfo.color.replace('bg-', '#') }}>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 capitalize">
                    {skill.skill_name.replace(/_/g, ' ')}
                  </h4>
                  <p className="text-sm text-gray-500 capitalize">{skill.domain.replace(/_/g, ' ')}</p>
                </div>
                <div className="flex items-center space-x-3">
                  {showTrends && skill.previous_score && (
                    <div className="flex items-center space-x-1">
                      {getTrendIcon(skill.current_score, skill.previous_score)}
                      <span className={`text-sm font-medium ${trendColor}`}>
                        {skill.previous_score ? 
                          `${Math.round((skill.current_score - skill.previous_score) * 100) > 0 ? '+' : ''}${Math.round((skill.current_score - skill.previous_score) * 100)}%` 
                          : 'New'
                        }
                      </span>
                    </div>
                  )}
                  <Badge variant="outline" className={levelInfo.textColor}>
                    {levelInfo.label}
                  </Badge>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Current Level</span>
                  <span className="font-medium">{scorePercent}%</span>
                </div>
                
                <div className="relative">
                  <Progress value={scorePercent} className="h-3" />
                  {targetPercent && targetPercent > scorePercent && (
                    <div 
                      className="absolute top-0 h-3 w-1 bg-red-400 rounded-r"
                      style={{ left: `${targetPercent}%` }}
                      title={`Target: ${targetPercent}%`}
                    />
                  )}
                </div>
                
                {targetPercent && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Target</span>
                    <span className="font-medium text-red-600">{targetPercent}%</span>
                  </div>
                )}
              </div>
              
              <div className="mt-3 text-xs text-gray-500">
                Last updated: {new Date(skill.last_updated).toLocaleDateString()}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

// Skill Level Ring Component for circular progress display
export const SkillLevelRing: React.FC<{
  score: number;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}> = ({ score, size = 'md', showLabel = true }) => {
  const levelInfo = {
    sm: { radius: 20, strokeWidth: 3, fontSize: 'text-xs' },
    md: { radius: 30, strokeWidth: 4, fontSize: 'text-sm' },
    lg: { radius: 40, strokeWidth: 5, fontSize: 'text-base' }
  }[size];
  
  const circumference = 2 * Math.PI * levelInfo.radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (score * circumference);
  
  const getColor = (score: number) => {
    if (score >= 0.8) return '#8b5cf6'; // purple
    if (score >= 0.6) return '#f97316'; // orange
    if (score >= 0.4) return '#10b981'; // green
    if (score >= 0.2) return '#3b82f6'; // blue
    return '#6b7280'; // gray
  };

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg
        width={levelInfo.radius * 2 + levelInfo.strokeWidth * 2}
        height={levelInfo.radius * 2 + levelInfo.strokeWidth * 2}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={levelInfo.radius + levelInfo.strokeWidth}
          cy={levelInfo.radius + levelInfo.strokeWidth}
          r={levelInfo.radius}
          stroke="#e5e7eb"
          strokeWidth={levelInfo.strokeWidth}
          fill="none"
        />
        {/* Progress circle */}
        <circle
          cx={levelInfo.radius + levelInfo.strokeWidth}
          cy={levelInfo.radius + levelInfo.strokeWidth}
          r={levelInfo.radius}
          stroke={getColor(score)}
          strokeWidth={levelInfo.strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="transition-all duration-300 ease-in-out"
        />
      </svg>
      {showLabel && (
        <div className={`absolute inset-0 flex items-center justify-center ${levelInfo.fontSize} font-medium text-gray-700`}>
          {Math.round(score * 100)}%
        </div>
      )}
    </div>
  );
};

// Domain Progress Summary Component
export const DomainProgressSummary: React.FC<{
  domains: Array<{
    name: string;
    score: number;
    color: string;
  }>;
}> = ({ domains }) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {domains.map((domain) => (
        <div key={domain.name} className="text-center">
          <SkillLevelRing score={domain.score} size="sm" />
          <div className="mt-2">
            <div className="text-sm font-medium text-gray-900 truncate">
              {domain.name}
            </div>
            <div className="text-xs text-gray-500">
              {Math.round(domain.score * 100)}%
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SkillsProgressIndicator;
