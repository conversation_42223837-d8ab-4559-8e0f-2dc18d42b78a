import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';

interface TermsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
  className?: string;
}

export const TermsModal: React.FC<TermsModalProps> = ({
  isOpen,
  onClose,
  onAccept,
  className = ''
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const acceptButtonRef = useRef<HTMLButtonElement>(null);

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Focus the close button when modal opens
      setTimeout(() => {
        closeButtonRef.current?.focus();
      }, 100);

      // Trap focus within modal
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose();
        }

        if (e.key === 'Tab') {
          const focusableElements = modalRef.current?.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );

          if (focusableElements && focusableElements.length > 0) {
            const firstElement = focusableElements[0] as HTMLElement;
            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

            if (e.shiftKey && document.activeElement === firstElement) {
              e.preventDefault();
              lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
              e.preventDefault();
              firstElement.focus();
            }
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }

    return () => {}; // Return empty cleanup function when modal is closed
  }, [isOpen, onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div
          ref={modalRef}
          className={`relative w-full max-w-4xl bg-white rounded-lg shadow-xl ${className}`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="terms-modal-title"
          data-testid="terms-modal"
        >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 id="terms-modal-title" className="text-2xl font-bold text-gray-900">
                Terms of Service & Privacy Policy
              </h2>
              <button
                ref={closeButtonRef}
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md"
                aria-label="Close modal"
                data-testid="close-terms-modal"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 max-h-96 overflow-y-auto">
              <div className="prose prose-sm max-w-none">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Terms of Service
                </h3>
                
                <div className="space-y-4 text-gray-700">
                  <p>
                    Welcome to CertRats! By using our platform, you agree to these terms of service. 
                    Please read them carefully.
                  </p>

                  <h4 className="font-semibold text-gray-900">1. Acceptance of Terms</h4>
                  <p>
                    By accessing and using CertRats, you accept and agree to be bound by the terms 
                    and provision of this agreement.
                  </p>

                  <h4 className="font-semibold text-gray-900">2. Use License</h4>
                  <p>
                    Permission is granted to temporarily use CertRats for personal, non-commercial 
                    transitory viewing only. This is the grant of a license, not a transfer of title.
                  </p>

                  <h4 className="font-semibold text-gray-900">3. User Account</h4>
                  <p>
                    You are responsible for safeguarding the password and for maintaining the 
                    confidentiality of your account information.
                  </p>

                  <h4 className="font-semibold text-gray-900">4. Privacy Policy</h4>
                  <p>
                    Your privacy is important to us. Our Privacy Policy explains how we collect, 
                    use, and protect your information when you use our service.
                  </p>

                  <h4 className="font-semibold text-gray-900">5. Data Collection</h4>
                  <p>
                    We collect information you provide directly to us, such as when you create an 
                    account, update your profile, or contact us for support.
                  </p>

                  <h4 className="font-semibold text-gray-900">6. Data Usage</h4>
                  <p>
                    We use the information we collect to provide, maintain, and improve our services, 
                    process transactions, and communicate with you.
                  </p>

                  <h4 className="font-semibold text-gray-900">7. Data Protection</h4>
                  <p>
                    We implement appropriate security measures to protect your personal information 
                    against unauthorized access, alteration, disclosure, or destruction.
                  </p>

                  <h4 className="font-semibold text-gray-900">8. Cookies</h4>
                  <p>
                    We use cookies and similar technologies to enhance your experience, analyze usage, 
                    and assist in our marketing efforts.
                  </p>

                  <h4 className="font-semibold text-gray-900">9. Third-Party Services</h4>
                  <p>
                    Our service may contain links to third-party websites or services. We are not 
                    responsible for the privacy practices of these third parties.
                  </p>

                  <h4 className="font-semibold text-gray-900">10. Changes to Terms</h4>
                  <p>
                    We reserve the right to modify these terms at any time. We will notify users of 
                    any material changes via email or through our platform.
                  </p>

                  <h4 className="font-semibold text-gray-900">11. Contact Information</h4>
                  <p>
                    If you have any questions about these Terms of Service or Privacy Policy, 
                    please contact <NAME_EMAIL>.
                  </p>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-end space-x-4 p-6 border-t border-gray-200 bg-gray-50">
              <Button
                variant="outline"
                onClick={onClose}
                data-testid="decline-terms-button"
              >
                Decline
              </Button>
              <Button
                ref={acceptButtonRef}
                variant="default"
                onClick={onAccept}
                data-testid="accept-terms-button"
              >
                Accept Terms
              </Button>
            </div>

            {/* Accessibility: Screen reader announcements */}
            <div className="sr-only" aria-live="polite">
              Terms of Service and Privacy Policy modal opened. 
              Use Tab to navigate through the content and buttons. 
              Press Escape to close.
            </div>
        </div>
      </div>
    </div>
  );
};
