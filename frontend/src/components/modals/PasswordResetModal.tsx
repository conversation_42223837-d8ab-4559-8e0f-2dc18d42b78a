import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
// import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, EnvelopeIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { useNotifications } from '../../hooks/useNotifications';
import { authApi } from '../../services/api';

// Password reset form schema
const passwordResetSchema = z.object({
  email: z.string().email('Please enter a valid email address')
});

type PasswordResetFormData = z.infer<typeof passwordResetSchema>;

interface PasswordResetModalProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const PasswordResetModal: React.FC<PasswordResetModalProps> = ({
  isOpen,
  onClose,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [cooldownTime, setCooldownTime] = useState(0);
  const modalRef = useRef<HTMLDivElement>(null);
  const emailInputRef = useRef<HTMLInputElement>(null);
  const { showNotification, showSuccess, showError } = useNotifications();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch
  } = useForm<PasswordResetFormData>({
    resolver: zodResolver(passwordResetSchema),
    mode: 'onChange'
  });

  const watchedEmail = watch('email');

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Focus the email input when modal opens
      setTimeout(() => {
        emailInputRef.current?.focus();
      }, 100);

      // Trap focus within modal
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose();
        }

        if (e.key === 'Tab') {
          const focusableElements = modalRef.current?.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );

          if (focusableElements && focusableElements.length > 0) {
            const firstElement = focusableElements[0] as HTMLElement;
            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

            if (e.shiftKey && document.activeElement === firstElement) {
              e.preventDefault();
              lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
              e.preventDefault();
              firstElement.focus();
            }
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }

    return () => {}; // Return empty cleanup function when modal is closed
  }, [isOpen, onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setIsSuccess(false);
      setIsLoading(false);
      setCooldownTime(0);
    }
  }, [isOpen, reset]);

  // Cooldown timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (cooldownTime > 0) {
      interval = setInterval(() => {
        setCooldownTime(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [cooldownTime]);

  const onSubmit = async (data: PasswordResetFormData) => {
    if (cooldownTime > 0) return;

    setIsLoading(true);
    
    try {
      const response = await authApi.forgotPassword(data.email);
      
      if (response.success) {
        setIsSuccess(true);
        setCooldownTime(60); // 60 second cooldown
        
        showSuccess(
          'Reset Email Sent',
          'Please check your email for password reset instructions.'
        );
      }
    } catch (error: any) {
      showError(
        'Reset Failed',
        error.response?.data?.detail || 'Failed to send reset email. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = () => {
    if (watchedEmail && cooldownTime === 0) {
      onSubmit({ email: watchedEmail });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div
          ref={modalRef}
          className={`relative w-full max-w-md bg-white rounded-lg shadow-xl ${className}`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="password-reset-title"
          data-testid="password-reset-modal"
        >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 id="password-reset-title" className="text-xl font-semibold text-gray-900">
                Reset Password
              </h2>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md"
                aria-label="Close modal"
                data-testid="close-reset-modal"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {!isSuccess ? (
                <>
                  <div className="mb-6 text-center">
                    <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                      <EnvelopeIcon className="w-6 h-6 text-blue-600" />
                    </div>
                    <p className="text-gray-600">
                      Enter your email address and we'll send you a link to reset your password.
                    </p>
                  </div>

                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    <Input
                      {...register('email')}
                      ref={emailInputRef}
                      type="email"
                      label="Email address"
                      placeholder="Enter your email"
                      error={errors.email?.message}
                      data-testid="reset-email-input"
                      autoComplete="email"
                      required
                    />

                    <div className="flex space-x-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={onClose}
                        className="flex-1"
                        data-testid="cancel-reset-button"
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        variant="default"
                        loading={isLoading}
                        disabled={!isValid || isLoading || cooldownTime > 0}
                        className="flex-1"
                        data-testid="send-reset-button"
                      >
                        {cooldownTime > 0 ? `Wait ${cooldownTime}s` : 'Send Reset Email'}
                      </Button>
                    </div>
                  </form>
                </>
              ) : (
                <>
                  <div className="text-center">
                    <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Reset Email Sent!
                    </h3>
                    <p className="text-gray-600 mb-6">
                      We've sent password reset instructions to <strong>{watchedEmail}</strong>.
                      Please check your email and follow the link to reset your password.
                    </p>
                    
                    <div className="space-y-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleResend}
                        disabled={cooldownTime > 0}
                        className="w-full"
                        data-testid="resend-reset-button"
                      >
                        {cooldownTime > 0 ? `Resend in ${cooldownTime}s` : 'Resend Email'}
                      </Button>
                      
                      <Button
                        type="button"
                        variant="default"
                        onClick={onClose}
                        className="w-full"
                        data-testid="close-success-button"
                      >
                        Close
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Accessibility: Screen reader announcements */}
            <div className="sr-only" aria-live="polite">
              {isSuccess ? 'Password reset email sent successfully' : 'Password reset form'}
            </div>
        </div>
      </div>
    </div>
  );
};
