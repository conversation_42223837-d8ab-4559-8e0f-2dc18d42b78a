/**
 * Skills Assessment Component
 * Feature 1.1: Skills Vector Representation & Scoring - UI Design Phase
 */
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { But<PERSON> } from './ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Progress } from './ui/progress';
import {
    Award,
    BarChart3,
    Brain,
    CheckCircle,
    Target,
    TrendingUp
} from 'lucide-react';
import React, { useState } from 'react';

// Types
interface SkillItem {
  skill_name: string;
  level: 'none' | 'basic' | 'intermediate' | 'advanced' | 'expert';
  confidence: 'very_confident' | 'confident' | 'somewhat_confident' | 'not_confident';
}

interface Certification {
  name: string;
  provider: string;
  year_obtained?: number;
}

interface SkillAssessmentData {
  user_id: number;
  skills: SkillItem[];
  certifications: Certification[];
}

interface DomainInfo {
  name: string;
  skills: string[];
}

// 8-Domain Cybersecurity Framework
const CYBERSECURITY_DOMAINS: Record<string, DomainInfo> = {
  communication_network_security: {
    name: 'Communication and Network Security',
    skills: [
      'network_protocols', 'firewall_configuration', 'vpn_technologies',
      'network_monitoring', 'cloud_network_security', 'wireless_security',
      'network_architecture', 'intrusion_detection', 'packet_analysis'
    ]
  },
  identity_access_management: {
    name: 'Identity and Access Management (IAM)',
    skills: [
      'authentication_mechanisms', 'authorization_rbac', 'privileged_access_management',
      'identity_governance', 'zero_trust_principles', 'single_sign_on',
      'multi_factor_authentication', 'identity_federation', 'access_controls'
    ]
  },
  security_architecture_engineering: {
    name: 'Security Architecture and Engineering',
    skills: [
      'security_frameworks', 'threat_modeling', 'secure_system_design',
      'risk_assessment', 'security_controls', 'security_patterns',
      'defense_in_depth', 'security_by_design', 'architecture_review'
    ]
  },
  asset_security: {
    name: 'Asset Security',
    skills: [
      'data_classification', 'data_loss_prevention', 'endpoint_security',
      'asset_inventory', 'privacy_compliance', 'data_governance',
      'information_lifecycle', 'data_retention', 'asset_management'
    ]
  },
  security_risk_management: {
    name: 'Security and Risk Management',
    skills: [
      'governance_frameworks', 'risk_assessment_methodologies', 'compliance_management',
      'policy_development', 'business_continuity', 'disaster_recovery',
      'risk_mitigation', 'regulatory_compliance', 'security_governance'
    ]
  },
  security_assessment_testing: {
    name: 'Security Assessment and Testing',
    skills: [
      'vulnerability_assessment', 'penetration_testing', 'security_auditing',
      'red_team_operations', 'security_metrics', 'code_review',
      'security_testing', 'compliance_auditing', 'threat_hunting'
    ]
  },
  software_security: {
    name: 'Software Security',
    skills: [
      'secure_coding_practices', 'application_security_testing', 'devsecops',
      'api_security', 'software_supply_chain', 'code_analysis',
      'secure_development_lifecycle', 'application_hardening', 'container_security'
    ]
  },
  security_operations: {
    name: 'Security Operations',
    skills: [
      'incident_response', 'siem_soar', 'threat_hunting_techniques',
      'digital_forensics', 'threat_intelligence', 'security_monitoring',
      'log_analysis', 'malware_analysis', 'security_orchestration'
    ]
  }
};

const SKILL_LEVELS = [
  { value: 'none', label: 'None', description: 'No experience', color: 'bg-gray-200' },
  { value: 'basic', label: 'Basic', description: 'Some exposure', color: 'bg-blue-200' },
  { value: 'intermediate', label: 'Intermediate', description: 'Working knowledge', color: 'bg-green-200' },
  { value: 'advanced', label: 'Advanced', description: 'Strong expertise', color: 'bg-orange-200' },
  { value: 'expert', label: 'Expert', description: 'Deep mastery', color: 'bg-purple-200' }
];

const CONFIDENCE_LEVELS = [
  { value: 'not_confident', label: 'Not Confident', icon: '😟' },
  { value: 'somewhat_confident', label: 'Somewhat Confident', icon: '😐' },
  { value: 'confident', label: 'Confident', icon: '😊' },
  { value: 'very_confident', label: 'Very Confident', icon: '😎' }
];

export const SkillsAssessment: React.FC = () => {
  const [currentDomain, setCurrentDomain] = useState(0);
  const [assessmentData, setAssessmentData] = useState<SkillAssessmentData>({
    user_id: 1, // This would come from auth context
    skills: [],
    certifications: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [assessmentComplete, setAssessmentComplete] = useState(false);

  const domains = Object.entries(CYBERSECURITY_DOMAINS);
  const totalDomains = domains.length;
  const progress = ((currentDomain + 1) / totalDomains) * 100;

  const handleSkillChange = (skillName: string, field: 'level' | 'confidence', value: string) => {
    setAssessmentData(prev => {
      const existingSkillIndex = prev.skills.findIndex(s => s.skill_name === skillName);
      
      if (existingSkillIndex >= 0) {
        // Update existing skill
        const updatedSkills = [...prev.skills];
        const existingSkill = updatedSkills[existingSkillIndex];
        if (existingSkill) {
          updatedSkills[existingSkillIndex] = {
            skill_name: existingSkill.skill_name,
            level: field === 'level' ? value as SkillItem['level'] : existingSkill.level,
            confidence: field === 'confidence' ? value as SkillItem['confidence'] : existingSkill.confidence
          };
        }
        return { ...prev, skills: updatedSkills };
      } else {
        // Add new skill
        const newSkill: SkillItem = {
          skill_name: skillName,
          level: field === 'level' ? value as SkillItem['level'] : 'none',
          confidence: field === 'confidence' ? value as SkillItem['confidence'] : 'not_confident'
        };
        return { ...prev, skills: [...prev.skills, newSkill] };
      }
    });
  };

  const getSkillValue = (skillName: string, field: 'level' | 'confidence') => {
    const skill = assessmentData.skills.find(s => s.skill_name === skillName);
    return skill ? skill[field] : (field === 'level' ? 'none' : 'not_confident');
  };

  const handleNext = () => {
    if (currentDomain < totalDomains - 1) {
      setCurrentDomain(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentDomain > 0) {
      setCurrentDomain(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Here you would call the API
      // const response = await fetch('/api/v1/skills/assess', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(assessmentData)
      // });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      setAssessmentComplete(true);
    } catch (error) {
      console.error('Assessment submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentDomainData = domains[currentDomain];
  if (!currentDomainData) {
    return <div>Error: Invalid domain</div>;
  }
  const [domainId, domainInfo] = currentDomainData;

  if (assessmentComplete) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl text-green-600">Assessment Complete!</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-600 mb-6">
            Your skills have been assessed and your profile has been updated.
          </p>
          <div className="flex justify-center gap-4">
            <Button onClick={() => window.location.href = '/dashboard'}>
              <BarChart3 className="h-4 w-4 mr-2" />
              View Results
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Take Another Assessment
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-6 w-6 text-blue-600" />
                Skills Assessment
              </CardTitle>
              <p className="text-gray-600 mt-1">
                Domain {currentDomain + 1} of {totalDomains}: {domainInfo.name}
              </p>
            </div>
            <Badge variant="outline" className="text-lg px-3 py-1">
              {Math.round(progress)}% Complete
            </Badge>
          </div>
          <Progress value={progress} className="mt-4" />
        </CardHeader>
      </Card>

      {/* Current Domain Assessment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-orange-600" />
            {domainInfo.name}
          </CardTitle>
          <p className="text-gray-600">
            Rate your skill level and confidence for each area below.
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {domainInfo.skills.map((skill) => (
              <div key={skill} className="border rounded-lg p-4 space-y-4">
                <h4 className="font-medium text-lg capitalize">
                  {skill.replace(/_/g, ' ')}
                </h4>
                
                {/* Skill Level Selection */}
                <div>
                  <label className="block text-sm font-medium mb-2">Skill Level</label>
                  <div className="grid grid-cols-5 gap-2">
                    {SKILL_LEVELS.map((level) => (
                      <button
                        key={level.value}
                        type="button"
                        onClick={() => handleSkillChange(skill, 'level', level.value)}
                        className={`p-3 rounded-lg border text-center transition-all ${
                          getSkillValue(skill, 'level') === level.value
                            ? `${level.color} border-blue-500 shadow-md`
                            : 'bg-white border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="font-medium text-sm">{level.label}</div>
                        <div className="text-xs text-gray-600 mt-1">{level.description}</div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Confidence Level Selection */}
                <div>
                  <label className="block text-sm font-medium mb-2">Confidence Level</label>
                  <div className="grid grid-cols-4 gap-2">
                    {CONFIDENCE_LEVELS.map((confidence) => (
                      <button
                        key={confidence.value}
                        type="button"
                        onClick={() => handleSkillChange(skill, 'confidence', confidence.value)}
                        className={`p-3 rounded-lg border text-center transition-all ${
                          getSkillValue(skill, 'confidence') === confidence.value
                            ? 'bg-blue-100 border-blue-500 shadow-md'
                            : 'bg-white border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="text-lg mb-1">{confidence.icon}</div>
                        <div className="text-sm font-medium">{confidence.label}</div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentDomain === 0}
            >
              Previous Domain
            </Button>
            
            <div className="text-center">
              <p className="text-sm text-gray-600">
                {assessmentData.skills.length} skills assessed
              </p>
            </div>

            {currentDomain === totalDomains - 1 ? (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || assessmentData.skills.length === 0}
                className="bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <Award className="h-4 w-4 mr-2" />
                    Complete Assessment
                  </>
                )}
              </Button>
            ) : (
              <Button onClick={handleNext}>
                Next Domain
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Help Text */}
      <Alert>
        <TrendingUp className="h-4 w-4" />
        <AlertDescription>
          <strong>Tip:</strong> Be honest about your skill levels and confidence. 
          This assessment helps create personalized learning recommendations and career pathways.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default SkillsAssessment;

// Export types for use in other components
export type { Certification, SkillAssessmentData, SkillItem };

