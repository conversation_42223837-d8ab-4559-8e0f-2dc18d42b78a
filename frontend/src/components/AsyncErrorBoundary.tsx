/**
 * Async Error Boundary for handling async errors with React Error Boundary
 */
import React, { ReactNode, useState, useEffect } from 'react';
import ErrorBoundary from './ErrorBoundary';

interface AsyncErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error) => void;
}

// Hook to handle async errors
export function useAsyncError() {
  const [, setError] = useState();
  
  return (error: Error) => {
    setError(() => {
      throw error;
    });
  };
}

// Component to wrap async operations
export function AsyncErrorBoundary({ children, fallback, onError }: AsyncErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={fallback}
      onError={(error, errorInfo) => {
        console.error('Async error caught:', error, errorInfo);
        onError?.(error);
      }}
    >
      {children}
    </ErrorBoundary>
  );
}

// HOC for wrapping components with async error boundary
export function withAsyncErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <AsyncErrorBoundary fallback={fallback}>
        <Component {...props} />
      </AsyncErrorBoundary>
    );
  };
}

export default AsyncErrorBoundary;
