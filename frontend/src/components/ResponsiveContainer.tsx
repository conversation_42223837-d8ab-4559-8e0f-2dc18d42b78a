import React, { useState, useEffect } from 'react';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  mobileClassName?: string;
  tabletClassName?: string;
  desktopClassName?: string;
}

interface BreakpointHookReturn {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
}

// Custom hook for responsive breakpoints
export const useBreakpoint = (): BreakpointHookReturn => {
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    isMobile: screenWidth < 768,
    isTablet: screenWidth >= 768 && screenWidth < 1024,
    isDesktop: screenWidth >= 1024,
    screenWidth
  };
};

// Responsive container component
export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className = '',
  mobileClassName = '',
  tabletClassName = '',
  desktopClassName = ''
}) => {
  const { isMobile, isTablet, isDesktop } = useBreakpoint();

  const getResponsiveClassName = () => {
    let classes = className;
    
    if (isMobile && mobileClassName) {
      classes += ` ${mobileClassName}`;
    } else if (isTablet && tabletClassName) {
      classes += ` ${tabletClassName}`;
    } else if (isDesktop && desktopClassName) {
      classes += ` ${desktopClassName}`;
    }
    
    return classes.trim();
  };

  return (
    <div className={getResponsiveClassName()}>
      {children}
    </div>
  );
};

// Mobile-first grid component
interface ResponsiveGridProps {
  children: React.ReactNode;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: string;
  className?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = '1rem',
  className = ''
}) => {
  const { isMobile, isTablet } = useBreakpoint();

  const getGridCols = () => {
    if (isMobile) return cols.mobile || 1;
    if (isTablet) return cols.tablet || 2;
    return cols.desktop || 3;
  };

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${getGridCols()}, 1fr)`,
    gap: gap,
  };

  return (
    <div className={className} style={gridStyle}>
      {children}
    </div>
  );
};

// Touch-friendly button component
interface TouchButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

export const TouchButton: React.FC<TouchButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = '',
  type = 'button'
}) => {
  const { isMobile } = useBreakpoint();

  const getButtonClasses = () => {
    const baseClasses = 'font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    // Touch-friendly sizing
    const sizeClasses = {
      sm: isMobile ? 'px-4 py-3 text-sm min-h-[44px]' : 'px-3 py-2 text-sm',
      md: isMobile ? 'px-6 py-4 text-base min-h-[48px]' : 'px-4 py-2 text-base',
      lg: isMobile ? 'px-8 py-5 text-lg min-h-[52px]' : 'px-6 py-3 text-lg'
    };

    const variantClasses = {
      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300',
      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 disabled:bg-gray-300',
      outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500 disabled:border-blue-300 disabled:text-blue-300'
    };

    const disabledClasses = disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer';

    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${disabledClasses} ${className}`.trim();
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={getButtonClasses()}
      style={{ touchAction: 'manipulation' }} // Prevents double-tap zoom on iOS
    >
      {children}
    </button>
  );
};

// Mobile-optimized input component
interface TouchInputProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  label?: string;
  error?: string;
}

export const TouchInput: React.FC<TouchInputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  disabled = false,
  required = false,
  className = '',
  label,
  error
}) => {
  const { isMobile } = useBreakpoint();

  const inputClasses = `
    w-full border-2 border-gray-300 rounded-lg transition-colors duration-200
    focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200
    disabled:bg-gray-100 disabled:cursor-not-allowed
    ${isMobile ? 'px-4 py-4 text-base min-h-[48px]' : 'px-3 py-2 text-sm'}
    ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : ''}
    ${className}
  `.trim();

  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        className={inputClasses}
        style={{ fontSize: isMobile ? '16px' : undefined }} // Prevents zoom on iOS
      />
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

// Mobile-optimized card component
interface TouchCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
}

export const TouchCard: React.FC<TouchCardProps> = ({
  children,
  className = '',
  onClick,
  hoverable = false
}) => {
  const { isMobile } = useBreakpoint();

  const cardClasses = `
    bg-white rounded-lg shadow-md transition-all duration-200
    ${isMobile ? 'p-6' : 'p-4'}
    ${onClick ? 'cursor-pointer' : ''}
    ${hoverable ? 'hover:shadow-lg hover:scale-105' : ''}
    ${className}
  `.trim();

  return (
    <div
      className={cardClasses}
      onClick={onClick}
      style={{ touchAction: onClick ? 'manipulation' : undefined }}
    >
      {children}
    </div>
  );
};

// Loading skeleton for mobile
interface SkeletonProps {
  width?: string;
  height?: string;
  className?: string;
  lines?: number;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  className = '',
  lines = 1
}) => {
  const skeletonClasses = `
    bg-gray-200 rounded animate-pulse
    ${className}
  `.trim();

  if (lines === 1) {
    return (
      <div
        className={skeletonClasses}
        style={{ width, height }}
      />
    );
  }

  return (
    <div className="space-y-2">
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={skeletonClasses}
          style={{
            width: index === lines - 1 ? '75%' : width,
            height
          }}
        />
      ))}
    </div>
  );
};

// Offline indicator component
interface OfflineIndicatorProps {
  className?: string;
}

export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  className = ''
}) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (isOnline) return null;

  return (
    <div className={`
      fixed top-0 left-0 right-0 z-50 bg-yellow-500 text-white text-center py-2 px-4
      ${className}
    `}>
      <span className="text-sm font-medium">
        📱 You're offline - some features may be limited
      </span>
    </div>
  );
};

export default ResponsiveContainer;
