import React from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
import { ExclamationTriangleIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

interface EmailSuggestionsProps {
  suggestions: string[];
  onSuggestionClick: (suggestion: string) => void;
  className?: string;
}

export const EmailSuggestions: React.FC<EmailSuggestionsProps> = ({
  suggestions,
  onSuggestionClick,
  className = ''
}) => {
  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  return (
    <div
      className={`absolute z-10 w-full mt-1 bg-white border border-orange-200 rounded-md shadow-lg ${className}`}
      data-testid="email-suggestions"
    >
        {/* Header */}
        <div className="px-4 py-3 bg-orange-50 border-b border-orange-200">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="w-5 h-5 text-orange-600 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-orange-800">
                Email already exists
              </p>
              <p className="text-xs text-orange-700">
                Try one of these alternatives:
              </p>
            </div>
          </div>
        </div>

        {/* Suggestions List */}
        <div className="py-2">
          {suggestions.map((suggestion, index) => (
            <button
              key={suggestion}
              onClick={() => onSuggestionClick(suggestion)}
              className="w-full px-4 py-2 text-left hover:bg-blue-50 focus:bg-blue-50 focus:outline-none transition-colors duration-150 group"
              data-testid={`email-suggestion-${index}`}
              type="button"
            >
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-900 font-medium">
                  {suggestion}
                </span>
                <ArrowRightIcon className="w-4 h-4 text-gray-400 group-hover:text-blue-600 group-focus:text-blue-600 transition-colors duration-150" />
              </div>
            </button>
          ))}
        </div>

        {/* Footer */}
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-200">
          <p className="text-xs text-gray-600">
            Click on any suggestion to use it, or continue typing to create a different email.
          </p>
        </div>

        {/* Accessibility: Screen reader announcements */}
        <div className="sr-only" aria-live="polite">
          Email address is already registered. {suggestions.length} alternative suggestions available.
        </div>
    </div>
  );
};
