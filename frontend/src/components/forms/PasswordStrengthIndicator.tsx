import React from 'react';
import { motion } from 'framer-motion';
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

interface PasswordRequirement {
  label: string;
  test: (password: string) => boolean;
  description: string;
}

const passwordRequirements: PasswordRequirement[] = [
  {
    label: 'At least 8 characters',
    test: (password) => password.length >= 8,
    description: 'Password must be at least 8 characters long'
  },
  {
    label: 'One uppercase letter',
    test: (password) => /[A-Z]/.test(password),
    description: 'Include at least one uppercase letter (A-Z)'
  },
  {
    label: 'One lowercase letter',
    test: (password) => /[a-z]/.test(password),
    description: 'Include at least one lowercase letter (a-z)'
  },
  {
    label: 'One number',
    test: (password) => /\d/.test(password),
    description: 'Include at least one number (0-9)'
  },
  {
    label: 'One special character',
    test: (password) => /[!@#$%^&*(),.?":{}|<>]/.test(password),
    description: 'Include at least one special character (!@#$%^&*)'
  }
];

const getPasswordStrength = (password: string): {
  score: number;
  level: 'weak' | 'fair' | 'good' | 'strong';
  color: string;
} => {
  const metRequirements = passwordRequirements.filter(req => req.test(password)).length;
  const score = (metRequirements / passwordRequirements.length) * 100;

  if (score < 40) {
    return { score, level: 'weak', color: 'bg-red-500' };
  } else if (score < 60) {
    return { score, level: 'fair', color: 'bg-orange-500' };
  } else if (score < 80) {
    return { score, level: 'good', color: 'bg-yellow-500' };
  } else {
    return { score, level: 'strong', color: 'bg-green-500' };
  }
};

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  className = ''
}) => {
  const strength = getPasswordStrength(password);
  const metRequirements = passwordRequirements.filter(req => req.test(password));

  if (!password) {
    return null;
  }

  return (
    <div className={`mt-3 ${className}`} data-testid="password-strength-indicator">
      {/* Strength Bar */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-sm font-medium text-gray-700">
            Password strength
          </span>
          <span className={`text-sm font-medium capitalize ${
            strength.level === 'weak' ? 'text-red-600' :
            strength.level === 'fair' ? 'text-orange-600' :
            strength.level === 'good' ? 'text-yellow-600' :
            'text-green-600'
          }`}>
            {strength.level}
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className={`h-2 rounded-full ${strength.color}`}
            initial={{ width: 0 }}
            animate={{ width: `${strength.score}%` }}
            transition={{ duration: 0.3 }}
            data-testid="strength-bar"
          />
        </div>
      </div>

      {/* Requirements Checklist */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-700 mb-2">
          Password requirements:
        </h4>
        
        {passwordRequirements.map((requirement, index) => {
          const isMet = requirement.test(password);
          
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center space-x-2"
              data-testid={`requirement-${index}`}
            >
              <div className={`flex-shrink-0 w-4 h-4 rounded-full flex items-center justify-center ${
                isMet ? 'bg-green-100' : 'bg-gray-100'
              }`}>
                {isMet ? (
                  <CheckIcon className="w-3 h-3 text-green-600" />
                ) : (
                  <XMarkIcon className="w-3 h-3 text-gray-400" />
                )}
              </div>
              
              <span className={`text-sm ${
                isMet ? 'text-green-700' : 'text-gray-600'
              }`}>
                {requirement.label}
              </span>
            </motion.div>
          );
        })}
      </div>

      {/* Accessibility: Screen reader announcements */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        Password strength: {strength.level}. 
        {metRequirements.length} of {passwordRequirements.length} requirements met.
        {metRequirements.length < passwordRequirements.length && (
          ` Missing: ${passwordRequirements
            .filter(req => !req.test(password))
            .map(req => req.description)
            .join(', ')}`
        )}
      </div>

      {/* Additional Security Tips */}
      {strength.level === 'strong' && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md"
        >
          <div className="flex items-start">
            <CheckIcon className="w-5 h-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <p className="text-sm text-green-800 font-medium">
                Great password!
              </p>
              <p className="text-sm text-green-700 mt-1">
                Your password meets all security requirements and will help keep your account safe.
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {strength.level === 'weak' && password.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md"
        >
          <div className="flex items-start">
            <XMarkIcon className="w-5 h-5 text-red-600 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <p className="text-sm text-red-800 font-medium">
                Password too weak
              </p>
              <p className="text-sm text-red-700 mt-1">
                Please include more character types to improve security.
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};
