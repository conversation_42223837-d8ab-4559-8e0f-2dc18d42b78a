import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CircularProgress, Box } from '@mui/material';
import Navigation from './components/Navigation';
import { NotificationSystem } from './components/ui/notification-system';
import { LoginPage } from './pages/LoginPage';
import { DashboardPage } from './pages/DashboardPage';
import { CertificationExplorerPage } from './pages/CertificationExplorerPage';
import './App.css';

// Lazy load components for better performance
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const CertificationExplorer = React.lazy(() => import('./pages/CertificationExplorer'));
const CostCalculator = React.lazy(() => import('./pages/CostCalculator'));
const UserProfile = React.lazy(() => import('./pages/UserProfile'));
const EnterpriseDashboard = React.lazy(() => import('./pages/EnterpriseDashboard'));
const JobSearch = React.lazy(() => import('./pages/JobSearch'));
const StudyTimer = React.lazy(() => import('./pages/StudyTimer'));

// Loading component
const LoadingSpinner = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    height="100vh"
  >
    <CircularProgress />
  </Box>
);

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Mock authentication check
const isAuthenticated = () => {
  // In a real app, this would check for valid tokens
  return localStorage.getItem('access_token') !== null;
};

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return isAuthenticated() ? <>{children}</> : <Navigate to="/login" replace />;
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="App">
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<LoginPage />} />

            {/* Protected Routes with Navigation */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <DashboardPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/certifications"
              element={
                <ProtectedRoute>
                  <CertificationExplorerPage />
                </ProtectedRoute>
              }
            />

            {/* Legacy routes with existing components */}
            <Route
              path="/legacy-dashboard"
              element={
                <ProtectedRoute>
                  <div className="flex h-screen bg-gray-100">
                    <Navigation />
                    <main className="flex-1 overflow-y-auto lg:ml-0 ml-0">
                      <div className="lg:p-0 pt-16 lg:pt-0">
                        <Suspense fallback={<LoadingSpinner />}>
                          <Dashboard />
                        </Suspense>
                      </div>
                    </main>
                  </div>
                </ProtectedRoute>
              }
            />
            <Route
              path="/legacy-certifications"
              element={
                <ProtectedRoute>
                  <div className="flex h-screen bg-gray-100">
                    <Navigation />
                    <main className="flex-1 overflow-y-auto lg:ml-0 ml-0">
                      <div className="lg:p-0 pt-16 lg:pt-0">
                        <Suspense fallback={<LoadingSpinner />}>
                          <CertificationExplorer />
                        </Suspense>
                      </div>
                    </main>
                  </div>
                </ProtectedRoute>
              }
            />
            <Route
              path="/cost-calculator"
              element={
                <ProtectedRoute>
                  <div className="flex h-screen bg-gray-100">
                    <Navigation />
                    <main className="flex-1 overflow-y-auto lg:ml-0 ml-0">
                      <div className="lg:p-0 pt-16 lg:pt-0">
                        <Suspense fallback={<LoadingSpinner />}>
                          <CostCalculator />
                        </Suspense>
                      </div>
                    </main>
                  </div>
                </ProtectedRoute>
              }
            />
            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  <div className="flex h-screen bg-gray-100">
                    <Navigation />
                    <main className="flex-1 overflow-y-auto lg:ml-0 ml-0">
                      <div className="lg:p-0 pt-16 lg:pt-0">
                        <Suspense fallback={<LoadingSpinner />}>
                          <UserProfile />
                        </Suspense>
                      </div>
                    </main>
                  </div>
                </ProtectedRoute>
              }
            />
            <Route
              path="/study-timer"
              element={
                <ProtectedRoute>
                  <div className="flex h-screen bg-gray-100">
                    <Navigation />
                    <main className="flex-1 overflow-y-auto lg:ml-0 ml-0">
                      <div className="lg:p-0 pt-16 lg:pt-0">
                        <Suspense fallback={<LoadingSpinner />}>
                          <StudyTimer />
                        </Suspense>
                      </div>
                    </main>
                  </div>
                </ProtectedRoute>
              }
            />
            <Route
              path="/job-search"
              element={
                <ProtectedRoute>
                  <div className="flex h-screen bg-gray-100">
                    <Navigation />
                    <main className="flex-1 overflow-y-auto lg:ml-0 ml-0">
                      <div className="lg:p-0 pt-16 lg:pt-0">
                        <Suspense fallback={<LoadingSpinner />}>
                          <JobSearch />
                        </Suspense>
                      </div>
                    </main>
                  </div>
                </ProtectedRoute>
              }
            />
            <Route
              path="/enterprise"
              element={
                <ProtectedRoute>
                  <div className="flex h-screen bg-gray-100">
                    <Navigation />
                    <main className="flex-1 overflow-y-auto lg:ml-0 ml-0">
                      <div className="lg:p-0 pt-16 lg:pt-0">
                        <Suspense fallback={<LoadingSpinner />}>
                          <EnterpriseDashboard />
                        </Suspense>
                      </div>
                    </main>
                  </div>
                </ProtectedRoute>
              }
            />

            {/* Default redirect */}
            <Route
              path="/"
              element={
                isAuthenticated() ?
                  <Navigate to="/dashboard" replace /> :
                  <Navigate to="/login" replace />
              }
            />

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>

          {/* Global Notification System */}
          <NotificationSystem />
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
