/**
 * Custom hook for authentication state management
 */
import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { useLocalStorage } from './useLocalStorage';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  avatar?: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; user?: any; error?: string }>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

interface RegisterData {
  email: string;
  password: string;
  name: string;
}

// Create Auth Context
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Auth hook implementation
export function useAuthState(): AuthContextType {
  const [token, setToken, removeToken] = useLocalStorage<string | null>('auth_token', null);
  const [user, setUser, removeUser] = useLocalStorage<User | null>('auth_user', null);
  const [isLoading, setIsLoading] = useState(false);

  const isAuthenticated = Boolean(token && user);

  const login = useCallback(async (email: string, password: string, rememberMe: boolean = false) => {
    setIsLoading(true);
    try {
      // For demo purposes, simulate successful login with demo credentials
      if (email === '<EMAIL>' && password === 'password123') {
        const mockUser = {
          id: '1',
          email: '<EMAIL>',
          name: 'Demo User',
          role: 'user'
        };

        const mockToken = 'mock_access_token_' + Date.now();
        setToken(mockToken);
        setUser(mockUser);

        return { success: true, user: mockUser };
      }

      // TODO: Replace with actual API call
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, remember_me: rememberMe }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Invalid email or password');
      }

      const data = await response.json();
      setToken(data.access_token);
      setUser(data.user);

      return { success: true, user: data.user };
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [setToken, setUser]);

  const logout = useCallback(() => {
    removeToken();
    removeUser();
  }, [removeToken, removeUser]);

  const register = useCallback(async (userData: RegisterData) => {
    setIsLoading(true);
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        throw new Error('Registration failed');
      }

      const data = await response.json();
      setToken(data.access_token);
      setUser(data.user);
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setToken, setUser]);

  const updateUser = useCallback((userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  }, [user, setUser]);

  // Validate token on mount
  useEffect(() => {
    if (token && !user) {
      // TODO: Validate token with API and get user data
      console.log('Validating token...');
    }
  }, [token, user]);

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    login,
    logout,
    register,
    updateUser,
  };
}

export default useAuth;
