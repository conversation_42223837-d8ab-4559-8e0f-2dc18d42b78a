import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import './styles/mobile.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { offlineManager } from './utils/offline';

// Register service worker for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Listen for online/offline status changes
window.addEventListener('onlineStatusChange', (event: any) => {
  const { isOnline } = event.detail;
  console.log(`App is now ${isOnline ? 'online' : 'offline'}`);

  // Show user notification about connectivity status
  if (isOnline) {
    // You can add a toast notification here
    console.log('Connection restored - syncing offline data...');
  } else {
    console.log('App is now offline - data will be cached locally');
  }
});

// Listen for service worker updates
window.addEventListener('serviceWorkerUpdate', () => {
  // You can show a notification to the user about the update
  console.log('New version available - please refresh the page');
});

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
