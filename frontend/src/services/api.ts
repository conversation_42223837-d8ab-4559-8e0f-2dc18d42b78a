import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
// Import types from centralized type definitions
import type {
  Certification,
  CertificationFilters,
  SecurityDomain,
  User,
  UserProfileUpdate,
  PaginatedResponse,
  HealthCheckResponse,
} from '../types';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';
const API_TIMEOUT = 30000; // 30 seconds

// Create axios instance
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth tokens and user identification
apiClient.interceptors.request.use(
  (config: any) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    // Add user ID header for API identification
    const user = localStorage.getItem('auth_user');
    if (user) {
      try {
        const userData = JSON.parse(user);
        config.headers = {
          ...config.headers,
          'X-User-ID': userData.id || 'test_user_1',
        };
      } catch (error) {
        console.warn('Error parsing user data from localStorage:', error);
      }
    } else {
      // Default user ID for testing
      config.headers = {
        ...config.headers,
        'X-User-ID': 'test_user_1',
      };
    }

    // Log request in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data,
        params: config.params,
      });
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for enhanced error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log response in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`, response.data);
    }
    return response;
  },
  (error) => {
    // Handle different error types
    if (error.response) {
      const { status, data } = error.response;

      // Log error in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ API Error: ${status} ${error.config?.url}`, data);
      }

      // Handle specific status codes
      switch (status) {
        case 401:
          localStorage.removeItem('auth_token');
          localStorage.removeItem('auth_user');
          window.location.href = '/login';
          break;
        case 403:
          console.error('Access denied. You do not have permission for this operation.');
          break;
        case 404:
          console.error('Resource not found.');
          break;
        case 422:
          console.error('Validation error:', data.detail);
          break;
        case 500:
          console.error('Server error. Please try again later.');
          break;
        default:
          console.error(`API Error ${status}:`, data.detail || 'Unknown error');
      }
    } else if (error.request) {
      console.error('Network error. Please check your connection.');
    } else {
      console.error('Request error:', error.message);
    }

    return Promise.reject(error);
  }
);

// Legacy types for backward compatibility
export interface CertificationList {
  certifications: Certification[];
}

export interface Domain extends SecurityDomain {}

export interface UserProfile extends User {}

// API Functions with enhanced TypeScript support
export const certificationApi = {
  // Get all certifications with optional filters
  getAll: async (filters?: CertificationFilters): Promise<PaginatedResponse<Certification>> => {
    const response = await apiClient.get<PaginatedResponse<Certification>>('/certifications/', {
      params: filters
    });
    return response.data;
  },

  // Get certification by ID
  getById: async (id: number): Promise<Certification> => {
    const response = await apiClient.get<Certification>(`/certifications/${id}`);
    return response.data;
  },

  // Get study plan for certification
  getStudyPlan: async (certId: number, hoursPerWeek: number) => {
    const response = await apiClient.get(`/certifications/${certId}/study-plan`, {
      params: { hours_per_week: hoursPerWeek }
    });
    return response.data;
  },

  // Search certifications
  search: async (query: string, filters?: CertificationFilters): Promise<PaginatedResponse<Certification>> => {
    const response = await apiClient.get<PaginatedResponse<Certification>>('/certifications/search', {
      params: { q: query, ...filters }
    });
    return response.data;
  },

  // Get certification statistics
  getStats: async () => {
    const response = await apiClient.get('/certifications/stats');
    return response.data;
  },
};

export const domainApi = {
  // Get all domains
  getAll: async (): Promise<Record<string, Domain>> => {
    const response = await apiClient.get('/domains/');
    return response.data;
  },

  // Get domain by ID
  getById: async (id: number): Promise<Domain> => {
    const response = await apiClient.get(`/domains/${id}`);
    return response.data;
  },
};

export const userApi = {
  // Get user profile
  getProfile: async (): Promise<User> => {
    const response = await apiClient.get<User>('/user/profile');
    return response.data;
  },

  // Update user profile
  updateProfile: async (profile: UserProfileUpdate): Promise<User> => {
    const response = await apiClient.put<User>('/user/profile', profile);
    return response.data;
  },

  // Create user profile
  createProfile: async (profile: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<User> => {
    const response = await apiClient.post<User>('/user/profile', profile);
    return response.data;
  },

  // Get user statistics
  getStats: async (): Promise<any> => {
    const response = await apiClient.get('/user/stats');
    return response.data;
  },

  // Get user achievements
  getAchievements: async (): Promise<any> => {
    const response = await apiClient.get('/user/achievements');
    return response.data;
  },
};

export const costCalculatorApi = {
  // Calculate costs
  calculate: async (data: {
    certification_ids: number[];
    include_study_materials: boolean;
    include_training: boolean;
  }) => {
    const response = await apiClient.post('/cost-calculator/calculations', data);
    return response.data;
  },

  // Get calculations
  getCalculations: async () => {
    const response = await apiClient.get('/cost-calculator/calculations');
    return response.data;
  },
};

export const healthApi = {
  // Check API health
  check: async (): Promise<HealthCheckResponse> => {
    const response = await apiClient.get<HealthCheckResponse>('/health');
    return response.data;
  },
};

// Authentication API Types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegistrationRequest {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
  referralCode?: string;
}

export interface EmailCheckResponse {
  available: boolean;
  suggestions?: string[];
}

export interface AuthResponse {
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  user?: User;
  message?: string;
}

// Authentication API
export const authApi = {
  // Enhanced login with remember me
  login: async (loginData: LoginRequest): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/login', loginData);
    return response.data;
  },

  // Enhanced registration
  register: async (registrationData: RegistrationRequest): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/register', registrationData);
    return response.data;
  },

  // Check email availability
  checkEmailAvailability: async (email: string): Promise<EmailCheckResponse> => {
    const response = await apiClient.get('/auth/check-email', {
      params: { email }
    });
    return response.data;
  },

  // Verify email
  verifyEmail: async (token: string, email: string): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/verify-email', { token, email });
    return response.data;
  },

  // Resend verification email
  resendVerification: async (email: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/auth/resend-verification', { email });
    return response.data;
  },

  // Refresh token
  refresh: async (refreshToken: string): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/refresh', { refresh_token: refreshToken });
    return response.data;
  },

  // Logout
  logout: async (logoutAllDevices?: boolean): Promise<{ success: boolean; message: string }> => {
    const refreshToken = localStorage.getItem('refresh_token');
    const response = await apiClient.post('/auth/logout', {
      refreshToken,
      logoutAllDevices
    });
    return response.data;
  },

  // Forgot password
  forgotPassword: async (email: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/auth/forgot-password', { email });
    return response.data;
  },

  // Reset password
  resetPassword: async (token: string, newPassword: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/auth/reset-password', { token, newPassword });
    return response.data;
  },
};

// Export default client
export default apiClient;
