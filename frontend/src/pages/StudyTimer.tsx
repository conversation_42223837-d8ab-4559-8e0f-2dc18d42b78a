import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  IconButton,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Settings as SettingsIcon,
  Timer as TimerIcon,
  Assessment as AssessmentIcon,
  History as HistoryIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useSimpleApi } from '../hooks/useSimpleApi';

interface StudySession {
  id: string;
  subject: string;
  duration: number; // in minutes
  completedAt: string;
  type: 'focus' | 'break';
  notes?: string;
}

interface TimerSettings {
  focusDuration: number; // in minutes
  shortBreakDuration: number;
  longBreakDuration: number;
  sessionsUntilLongBreak: number;
  autoStartBreaks: boolean;
  autoStartFocus: boolean;
  soundEnabled: boolean;
}

interface StudyStats {
  totalSessions: number;
  totalFocusTime: number; // in minutes
  averageSessionLength: number;
  streakDays: number;
  todaysSessions: number;
  todaysFocusTime: number;
  weeklyProgress: number[];
}

const StudyTimer: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes in seconds
  const [isRunning, setIsRunning] = useState(false);
  const [currentSession, setCurrentSession] = useState<'focus' | 'break'>('focus');
  const [sessionCount, setSessionCount] = useState(0);
  const [currentSubject, setCurrentSubject] = useState('');
  const [sessions, setSessions] = useState<StudySession[]>([]);
  const [stats, setStats] = useState<StudyStats | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isSessionCompleteOpen, setIsSessionCompleteOpen] = useState(false);
  const [sessionNotes, setSessionNotes] = useState('');
  const [error, setError] = useState<string | null>(null);

  const [settings, setSettings] = useState<TimerSettings>({
    focusDuration: 25,
    shortBreakDuration: 5,
    longBreakDuration: 15,
    sessionsUntilLongBreak: 4,
    autoStartBreaks: false,
    autoStartFocus: false,
    soundEnabled: true
  });

  const { get, post } = useSimpleApi();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    loadSettings();
    loadSessions();
    loadStats();
    
    // Initialize audio
    audioRef.current = new Audio('/notification.mp3');
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            handleSessionComplete();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning]);

  const loadSettings = async () => {
    try {
      const settingsResponse = await get('/api/v1/study-timer/settings');
      if (settingsResponse.success && settingsResponse.data) {
        const settingsData = settingsResponse.data as TimerSettings;
        setSettings(settingsData);
        setTimeLeft(settingsData.focusDuration * 60);
      }
    } catch (err) {
      console.error('Failed to load settings:', err);
    }
  };

  const loadSessions = async () => {
    try {
      const sessionsResponse = await get('/api/v1/study-timer/sessions');
      if (sessionsResponse.success && sessionsResponse.data) {
        setSessions(sessionsResponse.data as StudySession[]);
      }
    } catch (err) {
      console.error('Failed to load sessions:', err);
    }
  };

  const loadStats = async () => {
    try {
      const statsResponse = await get('/api/v1/study-timer/analytics');
      if (statsResponse.success && statsResponse.data) {
        setStats(statsResponse.data as StudyStats);
      }
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  const handleSessionComplete = async () => {
    setIsRunning(false);
    
    // Play notification sound
    if (settings.soundEnabled && audioRef.current) {
      try {
        await audioRef.current.play();
      } catch (err) {
        console.log('Could not play notification sound');
      }
    }

    // Save session
    try {
      const sessionData = {
        subject: currentSubject || 'General Study',
        duration: currentSession === 'focus' ? settings.focusDuration : 
                 sessionCount % settings.sessionsUntilLongBreak === 0 ? 
                 settings.longBreakDuration : settings.shortBreakDuration,
        type: currentSession,
        completedAt: new Date().toISOString()
      };

      await post('/api/v1/study-timer/sessions', sessionData);
      
      if (currentSession === 'focus') {
        setSessionCount(prev => prev + 1);
        setIsSessionCompleteOpen(true);
      }
      
      // Auto-start next session if enabled
      const nextSession = currentSession === 'focus' ? 'break' : 'focus';
      const autoStart = nextSession === 'break' ? settings.autoStartBreaks : settings.autoStartFocus;
      
      if (autoStart) {
        setTimeout(() => startNextSession(), 2000);
      } else {
        setCurrentSession(nextSession);
        const nextDuration = nextSession === 'focus' ? settings.focusDuration :
                           sessionCount % settings.sessionsUntilLongBreak === 0 ?
                           settings.longBreakDuration : settings.shortBreakDuration;
        setTimeLeft(nextDuration * 60);
      }
      
      // Reload data
      loadSessions();
      loadStats();
      
    } catch (err) {
      setError('Failed to save session');
      console.error('Session save error:', err);
    }
  };

  const startNextSession = () => {
    const nextSession = currentSession === 'focus' ? 'break' : 'focus';
    setCurrentSession(nextSession);
    
    const duration = nextSession === 'focus' ? settings.focusDuration :
                    sessionCount % settings.sessionsUntilLongBreak === 0 ?
                    settings.longBreakDuration : settings.shortBreakDuration;
    
    setTimeLeft(duration * 60);
    setIsRunning(true);
  };

  const startTimer = () => {
    if (!currentSubject && currentSession === 'focus') {
      setError('Please enter a subject before starting a focus session');
      return;
    }
    setIsRunning(true);
    setError(null);
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const stopTimer = () => {
    setIsRunning(false);
    const duration = currentSession === 'focus' ? settings.focusDuration :
                    sessionCount % settings.sessionsUntilLongBreak === 0 ?
                    settings.longBreakDuration : settings.shortBreakDuration;
    setTimeLeft(duration * 60);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setCurrentSession('focus');
    setSessionCount(0);
    setTimeLeft(settings.focusDuration * 60);
    setCurrentSubject('');
  };

  const saveSettings = async () => {
    try {
      await post('/api/v1/study-timer/settings', settings);
      setIsSettingsOpen(false);
      
      // Update timer if not running
      if (!isRunning) {
        const duration = currentSession === 'focus' ? settings.focusDuration :
                        sessionCount % settings.sessionsUntilLongBreak === 0 ?
                        settings.longBreakDuration : settings.shortBreakDuration;
        setTimeLeft(duration * 60);
      }
    } catch (err) {
      setError('Failed to save settings');
      console.error('Settings save error:', err);
    }
  };

  const saveSessionNotes = async () => {
    if (sessions.length > 0 && sessionNotes.trim()) {
      try {
        const lastSession = sessions[0];
        if (lastSession) {
          await post(`/api/v1/study-timer/sessions/${lastSession.id}`, {
            notes: sessionNotes
          });
        }
        setSessionNotes('');
        setIsSessionCompleteOpen(false);
        loadSessions();
      } catch (err) {
        setError('Failed to save session notes');
        console.error('Notes save error:', err);
      }
    } else {
      setIsSessionCompleteOpen(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgress = () => {
    const totalDuration = currentSession === 'focus' ? settings.focusDuration * 60 :
                         sessionCount % settings.sessionsUntilLongBreak === 0 ?
                         settings.longBreakDuration * 60 : settings.shortBreakDuration * 60;
    return ((totalDuration - timeLeft) / totalDuration) * 100;
  };

  const getSessionTypeColor = () => {
    return currentSession === 'focus' ? '#1976d2' : '#4caf50';
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Study Timer
        </Typography>
        <IconButton onClick={() => setIsSettingsOpen(true)}>
          <SettingsIcon />
        </IconButton>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '1.5rem' }}>
        {/* Timer Section */}
        <div>
          <Card sx={{ textAlign: 'center', p: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom color={getSessionTypeColor()}>
                {currentSession === 'focus' ? 'Focus Session' : 'Break Time'}
              </Typography>
              
              <Box position="relative" display="inline-flex" mb={3}>
                <CircularProgress
                  variant="determinate"
                  value={getProgress()}
                  size={200}
                  thickness={4}
                  sx={{ color: getSessionTypeColor() }}
                />
                <Box
                  position="absolute"
                  top={0}
                  left={0}
                  bottom={0}
                  right={0}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  flexDirection="column"
                >
                  <Typography variant="h3" component="div" color="text.primary">
                    {formatTime(timeLeft)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Session {sessionCount + 1}
                  </Typography>
                </Box>
              </Box>

              {currentSession === 'focus' && (
                <TextField
                  fullWidth
                  label="Study Subject"
                  value={currentSubject}
                  onChange={(e) => setCurrentSubject(e.target.value)}
                  sx={{ mb: 3 }}
                  disabled={isRunning}
                />
              )}

              <Box display="flex" justifyContent="center" gap={2}>
                {!isRunning ? (
                  <Button
                    variant="contained"
                    startIcon={<PlayIcon />}
                    onClick={startTimer}
                    size="large"
                    sx={{ bgcolor: getSessionTypeColor() }}
                  >
                    Start
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    startIcon={<PauseIcon />}
                    onClick={pauseTimer}
                    size="large"
                    color="warning"
                  >
                    Pause
                  </Button>
                )}
                <Button
                  variant="outlined"
                  startIcon={<StopIcon />}
                  onClick={stopTimer}
                  size="large"
                >
                  Stop
                </Button>
              </Box>

              <Button
                variant="text"
                onClick={resetTimer}
                sx={{ mt: 2 }}
                disabled={isRunning}
              >
                Reset Session
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Stats Section */}
        <div>
          <Card sx={{ p: 3, mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Today's Progress
              </Typography>
              {stats && (
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {stats.todaysSessions}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Sessions
                    </Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {Math.floor(stats.todaysFocusTime / 60)}h {stats.todaysFocusTime % 60}m
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Focus Time
                    </Typography>
                  </Box>
                </div>
              )}
            </CardContent>
          </Card>

          <Card sx={{ p: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Overall Stats
              </Typography>
              {stats && (
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                  <div>
                    <Typography variant="body2" color="text.secondary">
                      Total Sessions
                    </Typography>
                    <Typography variant="h6">{stats.totalSessions}</Typography>
                  </div>
                  <div>
                    <Typography variant="body2" color="text.secondary">
                      Total Focus Time
                    </Typography>
                    <Typography variant="h6">
                      {Math.floor(stats.totalFocusTime / 60)}h {stats.totalFocusTime % 60}m
                    </Typography>
                  </div>
                  <div>
                    <Typography variant="body2" color="text.secondary">
                      Avg Session
                    </Typography>
                    <Typography variant="h6">{stats.averageSessionLength}m</Typography>
                  </div>
                  <div>
                    <Typography variant="body2" color="text.secondary">
                      Current Streak
                    </Typography>
                    <Typography variant="h6">{stats.streakDays} days</Typography>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Tabs for History and Analytics */}
      <Paper sx={{ mt: 4 }}>
        <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
          <Tab icon={<HistoryIcon />} label="Session History" />
          <Tab icon={<AssessmentIcon />} label="Analytics" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <Box sx={{ mt: 3 }}>
        {currentTab === 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Sessions
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Subject</TableCell>
                      <TableCell>Duration</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Completed</TableCell>
                      <TableCell>Notes</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {sessions.slice(0, 10).map((session) => (
                      <TableRow key={session.id}>
                        <TableCell>{session.subject}</TableCell>
                        <TableCell>{session.duration}m</TableCell>
                        <TableCell>
                          <Chip
                            label={session.type}
                            size="small"
                            color={session.type === 'focus' ? 'primary' : 'success'}
                          />
                        </TableCell>
                        <TableCell>
                          {new Date(session.completedAt).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          {session.notes && (
                            <Typography variant="body2" noWrap>
                              {session.notes.substring(0, 50)}...
                            </Typography>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}

        {currentTab === 1 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Weekly Progress
              </Typography>
              {stats && (
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Focus time per day (minutes)
                  </Typography>
                  <Box display="flex" alignItems="end" gap={1} height={200}>
                    {stats.weeklyProgress.map((minutes, index) => (
                      <Box
                        key={index}
                        sx={{
                          width: 40,
                          height: `${(minutes / Math.max(...stats.weeklyProgress)) * 180}px`,
                          bgcolor: 'primary.main',
                          borderRadius: 1,
                          display: 'flex',
                          alignItems: 'end',
                          justifyContent: 'center',
                          pb: 1
                        }}
                      >
                        <Typography variant="caption" color="white">
                          {minutes}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                  <Box display="flex" gap={1} mt={1}>
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                      <Box key={index} width={40} textAlign="center">
                        <Typography variant="caption">{day}</Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        )}
      </Box>

      {/* Settings Dialog */}
      <Dialog open={isSettingsOpen} onClose={() => setIsSettingsOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Timer Settings</DialogTitle>
        <DialogContent>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginTop: '1rem' }}>
            <TextField
              fullWidth
              label="Focus Duration (minutes)"
              type="number"
              value={settings.focusDuration}
              onChange={(e) => setSettings({
                ...settings,
                focusDuration: parseInt(e.target.value) || 25
              })}
            />
            <TextField
              fullWidth
              label="Short Break (minutes)"
              type="number"
              value={settings.shortBreakDuration}
              onChange={(e) => setSettings({
                ...settings,
                shortBreakDuration: parseInt(e.target.value) || 5
              })}
            />
            <TextField
              fullWidth
              label="Long Break (minutes)"
              type="number"
              value={settings.longBreakDuration}
              onChange={(e) => setSettings({
                ...settings,
                longBreakDuration: parseInt(e.target.value) || 15
              })}
            />
            <TextField
              fullWidth
              label="Sessions Until Long Break"
              type="number"
              value={settings.sessionsUntilLongBreak}
              onChange={(e) => setSettings({
                ...settings,
                sessionsUntilLongBreak: parseInt(e.target.value) || 4
              })}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsSettingsOpen(false)}>Cancel</Button>
          <Button onClick={saveSettings} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Session Complete Dialog */}
      <Dialog open={isSessionCompleteOpen} onClose={() => setIsSessionCompleteOpen(false)}>
        <DialogTitle>Session Complete!</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Great job! You completed a {settings.focusDuration}-minute focus session.
          </Typography>
          <TextField
            fullWidth
            label="Session Notes (optional)"
            multiline
            rows={3}
            value={sessionNotes}
            onChange={(e) => setSessionNotes(e.target.value)}
            placeholder="What did you accomplish? Any insights?"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsSessionCompleteOpen(false)}>Skip</Button>
          <Button onClick={saveSessionNotes} variant="contained">Save Notes</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default StudyTimer;
