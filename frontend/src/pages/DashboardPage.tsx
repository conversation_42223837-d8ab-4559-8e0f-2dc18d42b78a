import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BellIcon,
  PlusIcon,
  PlayIcon,
  CheckIcon,
  ChartBarIcon,
  ClockIcon,
  FireIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '../components/layout/dashboard-layout';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { NotificationPanel } from '../components/dashboard/NotificationPanel';
import { QuickActionModal } from '../components/dashboard/QuickActionModal';
import { useNotifications } from '../hooks/use-notifications';
import { useSimpleApi } from '../hooks/useSimpleApi';

interface DashboardData {
  user: {
    id: number;
    name: string;
    email: string;
  };
  statistics: {
    total_certifications: number;
    in_progress: number;
    total_paths: number;
    completion_percentage: number;
  };
  recent_activity: Array<{
    id: number;
    certification_name: string;
    status: string;
    updated_at: string;
  }>;
  learning_paths: Array<{
    id: number;
    name: string;
    description: string;
    progress: number;
    created_at: string;
  }>;
  recommended_certifications: Array<{
    id: number;
    name: string;
    provider: string;
    difficulty: string;
    estimated_hours: number;
  }>;
}

interface QuickStats {
  completed_this_month: number;
  study_streak_days: number;
  next_exam_in_days: number;
  total_study_hours: number;
}

export const DashboardPage: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [quickStats, setQuickStats] = useState<QuickStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showQuickAction, setShowQuickAction] = useState(false);
  const [quickActionType, setQuickActionType] = useState<string>('');
  const [selectedCertification, setSelectedCertification] = useState<any>(null);
  const { showError, showSuccess } = useNotifications();
  const { get, post } = useSimpleApi();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'in_progress':
        return 'secondary';
      case 'not_started':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getDifficultyBadgeVariant = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return 'default';
      case 'intermediate':
        return 'secondary';
      case 'advanced':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const handleQuickAction = async (action: string, certificationId?: string, additionalData?: any) => {
    try {
      const response = await post('/dashboard/quick-action', {
        action,
        certification_id: certificationId,
        ...additionalData
      });

      if (response.success) {
        const message = response.data && typeof response.data === 'object' && 'message' in response.data
          ? (response.data as any).message
          : 'Action completed successfully';
        showSuccess('Action Completed', message);
        // Reload dashboard data to reflect changes
        loadDashboardData();
      } else {
        showError('Action Failed', response.error);
      }
    } catch (error) {
      showError('Action Failed', 'An error occurred while processing your request');
    }
  };

  const openQuickAction = (type: string, certification?: any) => {
    setQuickActionType(type);
    setSelectedCertification(certification);
    setShowQuickAction(true);
  };

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load dashboard overview and quick stats in parallel
      const [overviewResponse, statsResponse] = await Promise.all([
        get('/dashboard/overview'),
        get('/dashboard/quick-stats')
      ]);

      if (overviewResponse.success) {
        setDashboardData(overviewResponse.data as DashboardData);
      } else {
        showError('Failed to load dashboard', overviewResponse.error);
      }

      if (statsResponse.success) {
        setQuickStats(statsResponse.data as QuickStats);
      } else {
        showError('Failed to load statistics', statsResponse.error);
      }
    } catch (error) {
      showError('Dashboard Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!dashboardData) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Failed to load dashboard</h2>
          <p className="mt-2 text-gray-600">Please try refreshing the page</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Refresh
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white relative overflow-hidden"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Welcome back, {dashboardData.user.name}!</h1>
              <p className="mt-2 opacity-90">
                Continue your certification journey and achieve your career goals.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowNotifications(true)}
                className="text-white border-white hover:bg-white hover:text-blue-600"
                data-testid="notifications-button"
              >
                <BellIcon className="w-4 h-4 mr-2" />
                Notifications
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => openQuickAction('add_to_path')}
                className="text-white border-white hover:bg-white hover:text-blue-600"
                data-testid="quick-action-button"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Quick Action
              </Button>
            </div>
          </div>

          {/* Decorative background elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-white opacity-10 rounded-full"></div>
          <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        </motion.div>

        {/* Quick Stats */}
        {quickStats && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <CheckIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Completed This Month</p>
                      <p className="text-2xl font-bold text-gray-900">{quickStats.completed_this_month}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-orange-100 rounded-lg">
                      <FireIcon className="h-6 w-6 text-orange-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Study Streak</p>
                      <p className="text-2xl font-bold text-gray-900">{quickStats.study_streak_days} days</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <CalendarIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Next Exam</p>
                      <p className="text-2xl font-bold text-gray-900">{quickStats.next_exam_in_days} days</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <ClockIcon className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Study Hours</p>
                      <p className="text-2xl font-bold text-gray-900">{quickStats.total_study_hours}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Learning Paths */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Your Learning Paths</CardTitle>
              </CardHeader>
              <CardContent>
                {dashboardData.learning_paths.length > 0 ? (
                  <div className="space-y-4">
                    {dashboardData.learning_paths.map((path) => (
                      <div key={path.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold text-gray-900">{path.name}</h3>
                          <span className="text-sm text-gray-500">{path.progress}%</span>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{path.description}</p>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${path.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No learning paths yet</p>
                    <Button className="mt-4">Create Your First Path</Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                {dashboardData.recent_activity.length > 0 ? (
                  <div className="space-y-3">
                    {dashboardData.recent_activity.map((activity) => (
                      <div key={activity.id} className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {activity.certification_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(activity.updated_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Badge variant={getStatusBadgeVariant(activity.status)}>
                          {activity.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No recent activity</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Recommended Certifications */}
        <Card>
          <CardHeader>
            <CardTitle>Recommended Certifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dashboardData.recommended_certifications.map((cert) => (
                <div key={cert.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">{cert.name}</h3>
                    <Badge variant={getDifficultyBadgeVariant(cert.difficulty)}>
                      {cert.difficulty}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{cert.provider}</p>
                  <p className="text-xs text-gray-500 mb-3">
                    Est. {cert.estimated_hours} hours
                  </p>
                  <Button
                    size="sm"
                    className="w-full"
                    onClick={() => openQuickAction('add_to_path', cert)}
                    data-testid={`add-to-path-${cert.id}`}
                  >
                    Add to Path
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notification Panel */}
      <NotificationPanel
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
      />

      {/* Quick Action Modal */}
      <QuickActionModal
        isOpen={showQuickAction}
        onClose={() => setShowQuickAction(false)}
        actionType={quickActionType}
        certification={selectedCertification}
        onAction={handleQuickAction}
      />
    </DashboardLayout>
  );
};
