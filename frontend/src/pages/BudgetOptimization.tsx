import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Progress } from '../components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  DollarSign, 
  TrendingUp, 
  Target, 
  PieChart,
  BarChart3,
  Calculator,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Users,
  Building
} from 'lucide-react';

interface BudgetOptimization {
  totalBudget: number;
  optimizedAllocation: Record<string, number>;
  projectedROI: number;
  costSavings: number;
  efficiencyScore: number;
  recommendations: string[];
  riskAssessment: Record<string, any>;
}

interface BudgetOptimizationProps {
  className?: string;
}

const BudgetOptimization: React.FC<BudgetOptimizationProps> = ({ className }) => {
  const [totalBudget, setTotalBudget] = useState('');
  const [strategicPriorities, setStrategicPriorities] = useState<string[]>([]);
  const [timelineMonths, setTimelineMonths] = useState('12');
  const [optimization, setOptimization] = useState<BudgetOptimization | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('allocation');

  const priorityOptions = [
    'Cybersecurity',
    'Cloud Security',
    'Network Security',
    'Application Security',
    'Data Protection',
    'Incident Response',
    'Compliance',
    'Risk Management'
  ];

  const handleOptimizeBudget = async () => {
    if (!totalBudget || strategicPriorities.length === 0) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/budget-optimization/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          enterprise_id: 1, // This would come from user context
          total_budget: parseFloat(totalBudget),
          strategic_priorities: strategicPriorities,
          timeline_months: parseInt(timelineMonths)
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setOptimization(data);
      }
    } catch (error) {
      console.error('Error optimizing budget:', error);
    } finally {
      setLoading(false);
    }
  };

  const togglePriority = (priority: string) => {
    setStrategicPriorities(prev => 
      prev.includes(priority) 
        ? prev.filter(p => p !== priority)
        : [...prev, priority]
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Budget Optimization</h1>
          <p className="text-muted-foreground">
            Optimize your training budget allocation for maximum ROI
          </p>
        </div>
      </div>

      {/* Budget Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Budget Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Total Budget ($)</label>
              <Input
                type="number"
                placeholder="e.g., 100000"
                value={totalBudget}
                onChange={(e) => setTotalBudget(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Timeline (months)</label>
              <Input
                type="number"
                placeholder="12"
                value={timelineMonths}
                onChange={(e) => setTimelineMonths(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Strategic Priorities</label>
              <div className="flex flex-wrap gap-2">
                {priorityOptions.map((priority) => (
                  <Badge
                    key={priority}
                    variant={strategicPriorities.includes(priority) ? 'default' : 'secondary'}
                    className="cursor-pointer"
                    onClick={() => togglePriority(priority)}
                  >
                    {priority}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-6">
            <Button 
              onClick={handleOptimizeBudget} 
              disabled={loading || !totalBudget || strategicPriorities.length === 0}
              className="w-full md:w-auto"
            >
              {loading ? 'Optimizing...' : 'Optimize Budget'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Optimization Results */}
      {optimization && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-8 w-8 text-green-600" />
                  <div>
                    <div className="text-2xl font-bold">{formatCurrency(optimization.totalBudget)}</div>
                    <div className="text-sm text-muted-foreground">Total Budget</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                  <div>
                    <div className="text-2xl font-bold">{formatPercentage(optimization.projectedROI)}</div>
                    <div className="text-sm text-muted-foreground">Projected ROI</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <Target className="h-8 w-8 text-purple-600" />
                  <div>
                    <div className="text-2xl font-bold">{formatCurrency(optimization.costSavings)}</div>
                    <div className="text-sm text-muted-foreground">Cost Savings</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-8 w-8 text-orange-600" />
                  <div>
                    <div className="text-2xl font-bold">{formatPercentage(optimization.efficiencyScore)}</div>
                    <div className="text-sm text-muted-foreground">Efficiency Score</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Budget Optimization Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="allocation">Budget Allocation</TabsTrigger>
                  <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                  <TabsTrigger value="risks">Risk Assessment</TabsTrigger>
                </TabsList>

                <TabsContent value="allocation" className="space-y-4">
                  <div className="space-y-4">
                    {Object.entries(optimization.optimizedAllocation).map(([category, amount]) => {
                      const percentage = (amount / optimization.totalBudget) * 100;
                      return (
                        <div key={category} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{category}</span>
                            <span className="text-sm text-muted-foreground">
                              {formatCurrency(amount)} ({formatPercentage(percentage)})
                            </span>
                          </div>
                          <Progress value={percentage} className="w-full" />
                        </div>
                      );
                    })}
                  </div>
                </TabsContent>

                <TabsContent value="recommendations" className="space-y-4">
                  <div className="space-y-3">
                    {optimization.recommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                        <Lightbulb className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div className="flex-1">
                          <p className="text-sm">{recommendation}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="risks" className="space-y-4">
                  <div className="space-y-3">
                    {Object.entries(optimization.riskAssessment).map(([risk, level]) => (
                      <div key={risk} className="flex items-center gap-3 p-3 border rounded-lg">
                        <AlertTriangle className={`h-5 w-5 ${
                          level === 'high' ? 'text-red-600' :
                          level === 'medium' ? 'text-yellow-600' : 'text-green-600'
                        }`} />
                        <div className="flex-1">
                          <div className="font-medium">{risk}</div>
                          <div className="text-sm text-muted-foreground">
                            Risk Level: {level}
                          </div>
                        </div>
                        <Badge variant={
                          level === 'high' ? 'destructive' :
                          level === 'medium' ? 'secondary' : 'default'
                        }>
                          {level}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Implementation Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Implementation Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { phase: 'Budget Approval', duration: '2-4 weeks', status: 'pending' },
                  { phase: 'Vendor Selection', duration: '4-6 weeks', status: 'pending' },
                  { phase: 'Training Rollout', duration: `${timelineMonths} months`, status: 'pending' },
                  { phase: 'Progress Monitoring', duration: 'Ongoing', status: 'pending' }
                ].map((phase, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                    <div className={`h-3 w-3 rounded-full ${
                      phase.status === 'completed' ? 'bg-green-600' :
                      phase.status === 'current' ? 'bg-blue-600' : 'bg-gray-300'
                    }`} />
                    <div className="flex-1">
                      <div className="font-medium">{phase.phase}</div>
                      <div className="text-sm text-muted-foreground">{phase.duration}</div>
                    </div>
                    {phase.status === 'completed' && (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default BudgetOptimization;
