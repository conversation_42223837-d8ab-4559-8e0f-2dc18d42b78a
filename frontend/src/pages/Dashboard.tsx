import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Shield, Award, Clock, TrendingUp, Users, BookOpen, AlertCircle, CheckCircle } from 'lucide-react';
import { certificationApi, healthApi } from '../services/api';

const Dashboard: React.FC = () => {
  // Fetch certifications for stats
  const { data: certifications, isLoading: certsLoading, error: certsError } = useQuery({
    queryKey: ['certifications'],
    queryFn: () => certificationApi.getAll(),
  });

  // Fetch API health
  const { data: health } = useQuery({
    queryKey: ['health'],
    queryFn: () => healthApi.check(),
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const stats = [
    {
      title: 'Total Certifications',
      value: certifications?.items?.length || 0,
      icon: <Shield className="w-8 h-8 text-blue-500" />,
      change: '+12%',
      changeType: 'positive' as const,
      description: 'Available certifications',
    },
    {
      title: 'Entry Level',
      value: certifications?.items?.filter(c => c.level === 'Associate').length || 0,
      icon: <BookOpen className="w-8 h-8 text-green-500" />,
      change: '+5%',
      changeType: 'positive' as const,
      description: 'Perfect for beginners',
    },
    {
      title: 'Advanced',
      value: certifications?.items?.filter(c => c.level === 'Expert').length || 0,
      icon: <Award className="w-8 h-8 text-purple-500" />,
      change: '+8%',
      changeType: 'positive' as const,
      description: 'Expert-level certifications',
    },
    {
      title: 'Average Cost',
      value: certifications?.items ? Math.round(certifications.items.reduce((acc, cert) => acc + (cert.cost || 0), 0) / certifications.items.length) : 0,
      icon: <TrendingUp className="w-8 h-8 text-orange-500" />,
      change: '-2%',
      changeType: 'negative' as const,
      format: 'currency',
      description: 'Across all certifications',
    },
  ];

  const recentActivity = [
    { action: 'New certification added', item: 'AWS Solutions Architect', time: '2 hours ago', type: 'success' },
    { action: 'Cost updated', item: 'CISSP Certification', time: '4 hours ago', type: 'info' },
    { action: 'Domain created', item: 'Cloud Security', time: '1 day ago', type: 'success' },
    { action: 'User registered', item: '<EMAIL>', time: '2 days ago', type: 'info' },
    { action: 'Database backup', item: 'Automated backup completed', time: '3 days ago', type: 'success' },
  ];

  const formatValue = (value: number, format?: string) => {
    if (format === 'currency') {
      return `$${Math.round(value).toLocaleString()}`;
    }
    return value.toLocaleString();
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'info':
        return <AlertCircle className="w-4 h-4 text-blue-500" />;
      default:
        return <div className="w-2 h-2 bg-gray-400 rounded-full" />;
    }
  };

  if (certsLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-1/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (certsError) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertCircle className="w-6 h-6 text-red-500 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-red-800">Error Loading Dashboard</h3>
              <p className="text-red-600">Unable to load certification data. Please try again later.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's what's happening with your certifications.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8" data-testid="stats-grid">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow" data-testid="stats-card">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {formatValue(stat.value, stat.format)}
                </p>
                <p className={`text-sm ${
                  stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                } mb-1`}>
                  {stat.change} from last month
                </p>
                <p className="text-xs text-gray-500">{stat.description}</p>
              </div>
              <div className="flex-shrink-0 ml-4">
                {stat.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Quick Actions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button 
              onClick={() => window.location.href = '/certifications'}
              className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-blue-50 hover:border-blue-300 transition-colors group"
            >
              <div className="flex items-center">
                <Shield className="w-5 h-5 text-blue-500 mr-3 group-hover:text-blue-600" />
                <div>
                  <span className="font-medium text-gray-900">Explore Certifications</span>
                  <p className="text-sm text-gray-500">Browse 465+ security certifications</p>
                </div>
              </div>
            </button>
            <button 
              onClick={() => window.location.href = '/cost-calculator'}
              className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-green-50 hover:border-green-300 transition-colors group"
            >
              <div className="flex items-center">
                <TrendingUp className="w-5 h-5 text-green-500 mr-3 group-hover:text-green-600" />
                <div>
                  <span className="font-medium text-gray-900">Calculate Costs</span>
                  <p className="text-sm text-gray-500">Plan your certification budget</p>
                </div>
              </div>
            </button>
            <button 
              onClick={() => window.location.href = '/study-time'}
              className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-orange-50 hover:border-orange-300 transition-colors group"
            >
              <div className="flex items-center">
                <Clock className="w-5 h-5 text-orange-500 mr-3 group-hover:text-orange-600" />
                <div>
                  <span className="font-medium text-gray-900">Plan Study Time</span>
                  <p className="text-sm text-gray-500">Optimize your study schedule</p>
                </div>
              </div>
            </button>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3" data-testid="activity-item">
                <div className="flex-shrink-0 mt-1">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                  <p className="text-sm text-gray-600 truncate">{activity.item}</p>
                  <p className="text-xs text-gray-400">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              View all activity →
            </button>
          </div>
        </div>

        {/* System Status */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">API Status</span>
              <span className={`px-2 py-1 text-xs rounded-full ${
                health?.status === 'healthy' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {health?.status || 'Unknown'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Database</span>
              <span className={`px-2 py-1 text-xs rounded-full ${
                health?.services?.database?.status === 'connected'
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {health?.services?.database?.status || 'Unknown'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Certifications</span>
              <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                {certifications?.items?.length || 0} loaded
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Last Updated</span>
              <span className="text-xs text-gray-500">
                {health?.timestamp ? new Date(health.timestamp).toLocaleTimeString() : 'Unknown'}
              </span>
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button 
              onClick={() => window.location.href = '/admin'}
              className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              View system details →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
