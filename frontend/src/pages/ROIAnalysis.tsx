import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Badge } from '../components/ui/badge';
import { Progress } from '../components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  TrendingUp, 
  DollarSign, 
  Clock, 
  Calculator,
  BarChart3,
  PieChart,
  Target,
  AlertCircle,
  CheckCircle,
  Award,
  Building
} from 'lucide-react';

interface ROIAnalysis {
  certificationId: number;
  currentRoleId: number;
  targetRoleId: number;
  investmentCost: number;
  expectedSalaryIncrease: number;
  paybackPeriodMonths: number;
  fiveYearROI: number;
  tenYearROI: number;
  riskFactors: string[];
  confidenceScore: number;
  marketConditions: Record<string, any>;
}

interface ROIAnalysisProps {
  className?: string;
}

const ROIAnalysis: React.FC<ROIAnalysisProps> = ({ className }) => {
  const [certificationId, setCertificationId] = useState('');
  const [currentRoleId, setCurrentRoleId] = useState('');
  const [targetRoleId, setTargetRoleId] = useState('');
  const [investmentCost, setInvestmentCost] = useState('');
  const [location, setLocation] = useState('remote');
  const [experienceYears, setExperienceYears] = useState('5');
  const [roiAnalysis, setRoiAnalysis] = useState<ROIAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('projections');

  const certifications = [
    { id: 1, name: 'CISSP', cost: 3000 },
    { id: 2, name: 'CISM', cost: 2500 },
    { id: 3, name: 'CEH', cost: 1500 },
    { id: 4, name: 'Security+', cost: 800 },
    { id: 5, name: 'CISA', cost: 2200 },
    { id: 6, name: 'GSEC', cost: 4000 }
  ];

  const roles = [
    { id: 1, name: 'Security Analyst' },
    { id: 2, name: 'Security Engineer' },
    { id: 3, name: 'Senior Security Engineer' },
    { id: 4, name: 'Security Architect' },
    { id: 5, name: 'Security Manager' },
    { id: 6, name: 'CISO' }
  ];

  const locations = [
    'remote',
    'San Francisco, CA',
    'New York, NY',
    'Seattle, WA',
    'Austin, TX',
    'Chicago, IL',
    'Boston, MA',
    'Washington, DC'
  ];

  const handleAnalyzeROI = async () => {
    if (!certificationId || !currentRoleId || !investmentCost) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/salary-intelligence/roi-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          certification_id: parseInt(certificationId),
          current_role_id: parseInt(currentRoleId),
          target_role_id: targetRoleId ? parseInt(targetRoleId) : null,
          investment_cost: parseFloat(investmentCost),
          location: location,
          experience_years: parseInt(experienceYears)
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setRoiAnalysis(data);
      }
    } catch (error) {
      console.error('Error analyzing ROI:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`;
  };

  const getROIColor = (roi: number) => {
    if (roi >= 300) return 'text-green-600';
    if (roi >= 200) return 'text-blue-600';
    if (roi >= 100) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'green';
    if (score >= 0.6) return 'yellow';
    return 'red';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">ROI Analysis</h1>
          <p className="text-muted-foreground">
            Analyze the return on investment for certification training
          </p>
        </div>
      </div>

      {/* ROI Calculator */}
      <Card data-testid="roi-calculator">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            ROI Calculator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Certification</label>
              <Select value={certificationId} onValueChange={setCertificationId}>
                <SelectTrigger data-testid="certification-select">
                  <SelectValue placeholder="Select certification" />
                </SelectTrigger>
                <SelectContent>
                  {certifications.map((cert) => (
                    <SelectItem key={cert.id} value={cert.id.toString()}>
                      {cert.name} - {formatCurrency(cert.cost)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Current Role</label>
              <Select value={currentRoleId} onValueChange={setCurrentRoleId}>
                <SelectTrigger data-testid="current-role-select">
                  <SelectValue placeholder="Select current role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Target Role (Optional)</label>
              <Select value={targetRoleId} onValueChange={setTargetRoleId}>
                <SelectTrigger data-testid="target-role-select">
                  <SelectValue placeholder="Select target role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Investment Cost ($)</label>
              <Input
                type="number"
                placeholder="e.g., 3000"
                value={investmentCost}
                onChange={(e) => setInvestmentCost(e.target.value)}
                data-testid="investment-cost-input"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Location</label>
              <Select value={location} onValueChange={setLocation}>
                <SelectTrigger data-testid="location-select">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((loc) => (
                    <SelectItem key={loc} value={loc}>
                      {loc}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Experience (years)</label>
              <Input
                type="number"
                placeholder="5"
                value={experienceYears}
                onChange={(e) => setExperienceYears(e.target.value)}
                data-testid="experience-input"
              />
            </div>
          </div>

          <Button 
            onClick={handleAnalyzeROI} 
            disabled={loading || !certificationId || !currentRoleId || !investmentCost}
            className="w-full md:w-auto"
            data-testid="analyze-roi-button"
          >
            {loading ? 'Analyzing...' : 'Analyze ROI'}
          </Button>
        </CardContent>
      </Card>

      {/* ROI Analysis Results */}
      {roiAnalysis && (
        <div className="space-y-6" data-testid="roi-results">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-8 w-8 text-green-600" />
                  <div>
                    <div className="text-2xl font-bold" data-testid="investment-cost-metric">
                      {formatCurrency(roiAnalysis.investmentCost)}
                    </div>
                    <div className="text-sm text-muted-foreground">Investment Cost</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                  <div>
                    <div className="text-2xl font-bold" data-testid="salary-increase-metric">
                      {formatCurrency(roiAnalysis.expectedSalaryIncrease)}
                    </div>
                    <div className="text-sm text-muted-foreground">Annual Salary Increase</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <Clock className="h-8 w-8 text-purple-600" />
                  <div>
                    <div className="text-2xl font-bold" data-testid="payback-period-metric">
                      {roiAnalysis.paybackPeriodMonths}
                    </div>
                    <div className="text-sm text-muted-foreground">Payback (months)</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-8 w-8 text-orange-600" />
                  <div>
                    <div className={`text-2xl font-bold ${getROIColor(roiAnalysis.fiveYearROI)}`} data-testid="five-year-roi-metric">
                      {formatPercentage(roiAnalysis.fiveYearROI)}
                    </div>
                    <div className="text-sm text-muted-foreground">5-Year ROI</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>ROI Analysis Details</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="projections" data-testid="projections-tab">Projections</TabsTrigger>
                  <TabsTrigger value="risks" data-testid="risks-tab">Risk Factors</TabsTrigger>
                  <TabsTrigger value="market" data-testid="market-tab">Market Conditions</TabsTrigger>
                </TabsList>

                <TabsContent value="projections" className="space-y-4" data-testid="projections-content">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">ROI Timeline</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span>5-Year ROI</span>
                          <span className={`font-bold ${getROIColor(roiAnalysis.fiveYearROI)}`}>
                            {formatPercentage(roiAnalysis.fiveYearROI)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>10-Year ROI</span>
                          <span className={`font-bold ${getROIColor(roiAnalysis.tenYearROI)}`}>
                            {formatPercentage(roiAnalysis.tenYearROI)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>Payback Period</span>
                          <span className="font-bold">{roiAnalysis.paybackPeriodMonths} months</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Confidence Score</h4>
                      <div className="space-y-2">
                        <Progress
                          value={roiAnalysis.confidenceScore * 100}
                          color={getConfidenceColor(roiAnalysis.confidenceScore)}
                          className="w-full"
                          data-testid="confidence-progress"
                        />
                        <p className="text-sm text-muted-foreground">
                          {Math.round(roiAnalysis.confidenceScore * 100)}% confidence in projections
                        </p>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="risks" className="space-y-4" data-testid="risks-content">
                  <div className="space-y-3">
                    {roiAnalysis.riskFactors.map((risk, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 border rounded-lg" data-testid="risk-factor">
                        <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" data-testid="risk-icon" />
                        <div className="flex-1">
                          <p className="text-sm" data-testid="risk-text">{risk}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="market" className="space-y-4" data-testid="market-content">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {Object.entries(roiAnalysis.marketConditions).map(([key, value]) => (
                      <div key={key} className="space-y-2" data-testid="market-condition">
                        <h4 className="font-medium capitalize">{key.replace('_', ' ')}</h4>
                        <p className="text-sm text-muted-foreground">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ROIAnalysis;
