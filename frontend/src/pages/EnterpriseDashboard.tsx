import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  School as SchoolIcon,
  Download as DownloadIcon,
  Email as EmailIcon
} from '@mui/icons-material';
import { useSimpleApi } from '../hooks/useSimpleApi';

interface Team {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  completionRate: number;
  createdAt: string;
  manager: string;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  joinDate: string;
  progress: number;
  certifications: number;
  status: 'active' | 'inactive';
}

interface EnterpriseStats {
  totalUsers: number;
  activeUsers: number;
  totalTeams: number;
  avgCompletionRate: number;
  totalCertifications: number;
  monthlyProgress: number;
}

const EnterpriseDashboard: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [teams, setTeams] = useState<Team[]>([]);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [stats, setStats] = useState<EnterpriseStats | null>(null);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [isTeamDialogOpen, setIsTeamDialogOpen] = useState(false);
  const [isBulkInviteOpen, setIsBulkInviteOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { get, post } = useSimpleApi();

  // Form states
  const [teamForm, setTeamForm] = useState({
    name: '',
    description: '',
    manager: ''
  });
  const [bulkInviteEmails, setBulkInviteEmails] = useState('');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [teamsResponse, statsResponse] = await Promise.all([
        get('/api/v1/enterprise/teams'),
        get('/api/v1/enterprise/analytics')
      ]);

      if (teamsResponse.success && teamsResponse.data) {
        const teamsData = teamsResponse.data as Team[];
        setTeams(teamsData);
        if (teamsData.length > 0 && teamsData[0]) {
          loadTeamMembers(teamsData[0].id);
        }
      }

      if (statsResponse.success) {
        setStats(statsResponse.data as EnterpriseStats);
      }
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard load error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadTeamMembers = async (teamId: string) => {
    try {
      const membersResponse = await get(`/api/v1/enterprise/teams/${teamId}/members`);
      if (membersResponse.success) {
        setMembers(membersResponse.data as TeamMember[]);
      }
    } catch (err) {
      console.error('Failed to load team members:', err);
    }
  };

  const handleCreateTeam = async () => {
    try {
      const response = await post('/api/v1/enterprise/teams', teamForm);
      if (response.success) {
        setTeams([...teams, response.data as Team]);
        setIsTeamDialogOpen(false);
        setTeamForm({ name: '', description: '', manager: '' });
      }
    } catch (err) {
      setError('Failed to create team');
      console.error('Team creation error:', err);
    }
  };

  const handleDeleteTeam = async (teamId: string) => {
    if (window.confirm('Are you sure you want to delete this team?')) {
      try {
        await post(`/api/v1/enterprise/teams/${teamId}/delete`, {});
        setTeams(teams.filter(team => team.id !== teamId));
      } catch (err) {
        setError('Failed to delete team');
        console.error('Team deletion error:', err);
      }
    }
  };

  const handleBulkInvite = async () => {
    try {
      const emails = bulkInviteEmails.split('\n').filter(email => email.trim());
      await post('/api/v1/enterprise/users/bulk-invite', { emails });
      setBulkInviteEmails('');
      setIsBulkInviteOpen(false);
      // Refresh data
      loadDashboardData();
    } catch (err) {
      setError('Failed to send invitations');
      console.error('Bulk invite error:', err);
    }
  };

  const exportReport = async () => {
    try {
      const response = await get('/api/v1/enterprise/reports/export');

      if (response.success && response.data) {
        // Create download link
        const url = window.URL.createObjectURL(new Blob([response.data as any]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `enterprise-report-${new Date().toISOString().split('T')[0]}.pdf`);
        document.body.appendChild(link);
        link.click();
        link.remove();
      }
    } catch (err) {
      setError('Failed to export report');
      console.error('Export error:', err);
    }
  };

  const StatCard: React.FC<{ title: string; value: string | number; icon: React.ReactNode; color: string }> = 
    ({ title, value, icon, color }) => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="h2">
              {value}
            </Typography>
          </Box>
          <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>Loading enterprise dashboard...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Enterprise Dashboard
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<EmailIcon />}
            onClick={() => setIsBulkInviteOpen(true)}
            sx={{ mr: 2 }}
          >
            Bulk Invite
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={exportReport}
            sx={{ mr: 2 }}
          >
            Export Report
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setIsTeamDialogOpen(true)}
          >
            Create Team
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '2rem' }}>
          <StatCard
            title="Total Users"
            value={stats.totalUsers}
            icon={<PeopleIcon />}
            color="#1976d2"
          />
          <StatCard
            title="Active Users"
            value={stats.activeUsers}
            icon={<TrendingUpIcon />}
            color="#2e7d32"
          />
          <StatCard
            title="Total Teams"
            value={stats.totalTeams}
            icon={<AssignmentIcon />}
            color="#ed6c02"
          />
          <StatCard
            title="Avg Completion"
            value={`${stats.avgCompletionRate}%`}
            icon={<SchoolIcon />}
            color="#9c27b0"
          />
        </div>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
          <Tab label="Teams Overview" />
          <Tab label="Team Members" />
          <Tab label="Analytics" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Teams Overview
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Team Name</TableCell>
                    <TableCell>Members</TableCell>
                    <TableCell>Completion Rate</TableCell>
                    <TableCell>Manager</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {teams.map((team) => (
                    <TableRow key={team.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">{team.name}</Typography>
                          <Typography variant="body2" color="textSecondary">
                            {team.description}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip label={team.memberCount} color="primary" size="small" />
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <LinearProgress
                            variant="determinate"
                            value={team.completionRate}
                            sx={{ width: 100, mr: 1 }}
                          />
                          <Typography variant="body2">{team.completionRate}%</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{team.manager}</TableCell>
                      <TableCell>{new Date(team.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Tooltip title="Edit Team">
                          <IconButton size="small" onClick={() => {
                            setSelectedTeam(team);
                            setTeamForm({
                              name: team.name,
                              description: team.description,
                              manager: team.manager
                            });
                            setIsTeamDialogOpen(true);
                          }}>
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Team">
                          <IconButton size="small" onClick={() => handleDeleteTeam(team.id)}>
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Team Creation/Edit Dialog */}
      <Dialog open={isTeamDialogOpen} onClose={() => setIsTeamDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{selectedTeam ? 'Edit Team' : 'Create New Team'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Team Name"
            fullWidth
            variant="outlined"
            value={teamForm.name}
            onChange={(e) => setTeamForm({ ...teamForm, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={teamForm.description}
            onChange={(e) => setTeamForm({ ...teamForm, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Manager"
            fullWidth
            variant="outlined"
            value={teamForm.manager}
            onChange={(e) => setTeamForm({ ...teamForm, manager: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsTeamDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateTeam} variant="contained">
            {selectedTeam ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bulk Invite Dialog */}
      <Dialog open={isBulkInviteOpen} onClose={() => setIsBulkInviteOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Bulk Invite Users</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Enter email addresses, one per line:
          </Typography>
          <TextField
            autoFocus
            margin="dense"
            label="Email Addresses"
            fullWidth
            multiline
            rows={6}
            variant="outlined"
            value={bulkInviteEmails}
            onChange={(e) => setBulkInviteEmails(e.target.value)}
            placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsBulkInviteOpen(false)}>Cancel</Button>
          <Button onClick={handleBulkInvite} variant="contained">
            Send Invitations
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default EnterpriseDashboard;
