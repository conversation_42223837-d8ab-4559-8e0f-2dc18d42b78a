import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Badge } from '../components/ui/badge';
import { Progress } from '../components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  TrendingUp, 
  DollarSign, 
  Clock, 
  Target, 
  BookOpen, 
  Award,
  BarChart3,
  ArrowRight,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface CareerPath {
  id: string;
  name: string;
  currentRole: string;
  targetRole: string;
  estimatedCost: number;
  estimatedMonths: number;
  successProbability: number;
  requiredCertifications: string[];
  salaryIncrease: number;
  roi: number;
}

interface CareerPlanningProps {
  className?: string;
}

const CareerPlanning: React.FC<CareerPlanningProps> = ({ className }) => {
  const [currentRole, setCurrentRole] = useState('');
  const [targetRole, setTargetRole] = useState('');
  const [budget, setBudget] = useState('');
  const [timeline, setTimeline] = useState('');
  const [careerPaths, setCareerPaths] = useState<CareerPath[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPath, setSelectedPath] = useState<CareerPath | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const roles = [
    'Security Analyst',
    'Security Engineer',
    'Senior Security Engineer',
    'Security Architect',
    'CISO',
    'Penetration Tester',
    'Security Consultant',
    'Cloud Security Engineer',
    'DevSecOps Engineer',
    'Incident Response Analyst'
  ];

  const handleFindPaths = async () => {
    if (!currentRole || !targetRole || !budget || !timeline) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/career-transition/pathfinding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          current_role_id: 1, // This would be mapped from role name
          target_role_id: 2,   // This would be mapped from role name
          max_budget: parseFloat(budget),
          max_timeline_months: parseInt(timeline),
          max_difficulty: 'Medium',
          learning_style: 'mixed',
          study_hours_per_week: 15,
          currency: 'USD'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        // Transform API response to CareerPath format
        const paths: CareerPath[] = data.path_options?.map((path: any, index: number) => ({
          id: `path-${index}`,
          name: `Path ${index + 1}`,
          currentRole,
          targetRole,
          estimatedCost: path.total_cost || 0,
          estimatedMonths: path.estimated_duration_months || 0,
          successProbability: path.success_probability || 0.75,
          requiredCertifications: path.certifications || [],
          salaryIncrease: 15000 + (index * 5000), // Mock data
          roi: 250 + (index * 50) // Mock data
        })) || [];
        
        setCareerPaths(paths);
        if (paths.length > 0 && paths[0]) {
          setSelectedPath(paths[0]);
        }
      }
    } catch (error) {
      console.error('Error finding career paths:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Career Planning</h1>
          <p className="text-muted-foreground">
            Plan your cybersecurity career transition with AI-powered pathfinding
          </p>
        </div>
      </div>

      {/* Career Path Finder */}
      <Card data-testid="career-path-finder">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Career Path Finder
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Current Role</label>
              <Select value={currentRole} onValueChange={setCurrentRole}>
                <SelectTrigger data-testid="current-role-select">
                  <SelectValue placeholder="Select current role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role} value={role}>
                      {role}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Target Role</label>
              <Select value={targetRole} onValueChange={setTargetRole}>
                <SelectTrigger data-testid="target-role-select">
                  <SelectValue placeholder="Select target role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role} value={role}>
                      {role}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Budget ($)</label>
              <Input
                type="number"
                placeholder="e.g., 5000"
                value={budget}
                onChange={(e) => setBudget(e.target.value)}
                data-testid="budget-input"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Timeline (months)</label>
              <Input
                type="number"
                placeholder="e.g., 12"
                value={timeline}
                onChange={(e) => setTimeline(e.target.value)}
                data-testid="timeline-input"
              />
            </div>
          </div>

          <Button
            onClick={handleFindPaths}
            disabled={loading || !currentRole || !targetRole || !budget || !timeline}
            className="w-full md:w-auto"
            data-testid="find-paths-button"
          >
            {loading ? 'Finding Paths...' : 'Find Career Paths'}
          </Button>
        </CardContent>
      </Card>

      {/* Career Path Results */}
      {careerPaths.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Path Options */}
          <div className="lg:col-span-1 space-y-4">
            <h3 className="text-lg font-semibold">Path Options</h3>
            {careerPaths.map((path) => (
              <Card 
                key={path.id} 
                className={`cursor-pointer transition-colors ${
                  selectedPath?.id === path.id ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setSelectedPath(path)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{path.name}</h4>
                    <Badge variant="secondary">
                      {Math.round(path.successProbability * 100)}% Success
                    </Badge>
                  </div>
                  
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      {formatCurrency(path.estimatedCost)}
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      {path.estimatedMonths} months
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      {path.roi}% ROI
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Selected Path Details */}
          {selectedPath && (
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ArrowRight className="h-5 w-5" />
                    {selectedPath.currentRole} → {selectedPath.targetRole}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="overview">Overview</TabsTrigger>
                      <TabsTrigger value="certifications">Certifications</TabsTrigger>
                      <TabsTrigger value="timeline">Timeline</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="space-y-4">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-4 bg-muted rounded-lg">
                          <DollarSign className="h-8 w-8 mx-auto mb-2 text-green-600" />
                          <div className="text-2xl font-bold">{formatCurrency(selectedPath.estimatedCost)}</div>
                          <div className="text-sm text-muted-foreground">Total Cost</div>
                        </div>
                        
                        <div className="text-center p-4 bg-muted rounded-lg">
                          <Clock className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                          <div className="text-2xl font-bold">{selectedPath.estimatedMonths}</div>
                          <div className="text-sm text-muted-foreground">Months</div>
                        </div>
                        
                        <div className="text-center p-4 bg-muted rounded-lg">
                          <TrendingUp className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                          <div className="text-2xl font-bold">{formatCurrency(selectedPath.salaryIncrease)}</div>
                          <div className="text-sm text-muted-foreground">Salary Increase</div>
                        </div>
                        
                        <div className="text-center p-4 bg-muted rounded-lg">
                          <BarChart3 className="h-8 w-8 mx-auto mb-2 text-orange-600" />
                          <div className="text-2xl font-bold">{selectedPath.roi}%</div>
                          <div className="text-sm text-muted-foreground">ROI</div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Success Probability</label>
                        <Progress value={selectedPath.successProbability * 100} className="w-full" />
                        <p className="text-sm text-muted-foreground">
                          {Math.round(selectedPath.successProbability * 100)}% chance of successful transition
                        </p>
                      </div>
                    </TabsContent>

                    <TabsContent value="certifications" className="space-y-4">
                      <div className="space-y-3">
                        {selectedPath.requiredCertifications.map((cert, index) => (
                          <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                            <Award className="h-5 w-5 text-yellow-600" />
                            <div className="flex-1">
                              <div className="font-medium">{cert}</div>
                              <div className="text-sm text-muted-foreground">
                                Required certification for career transition
                              </div>
                            </div>
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          </div>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="timeline" className="space-y-4">
                      <div className="space-y-4">
                        {[
                          { phase: 'Preparation', duration: '1-2 months', status: 'pending' },
                          { phase: 'Study & Training', duration: `${selectedPath.estimatedMonths - 3} months`, status: 'pending' },
                          { phase: 'Certification Exams', duration: '1-2 months', status: 'pending' },
                          { phase: 'Job Search & Transition', duration: '1-2 months', status: 'pending' }
                        ].map((phase, index) => (
                          <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                            <div className={`h-3 w-3 rounded-full ${
                              phase.status === 'completed' ? 'bg-green-600' :
                              phase.status === 'current' ? 'bg-blue-600' : 'bg-gray-300'
                            }`} />
                            <div className="flex-1">
                              <div className="font-medium">{phase.phase}</div>
                              <div className="text-sm text-muted-foreground">{phase.duration}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CareerPlanning;
