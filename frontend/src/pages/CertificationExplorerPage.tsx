import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '../components/layout/dashboard-layout';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { useNotifications } from '../hooks/use-notifications';
import { useSimpleApi } from '../hooks/useSimpleApi';

interface Certification {
  id: number;
  name: string;
  provider: string;
  description: string;
  difficulty_level: string;
  estimated_study_hours: number;
  cost: number;
  prerequisites: string[];
  domains: string[];
  exam_code: string;
  validity_years: number;
}

interface FilterOptions {
  provider: string;
  difficulty: string;
  domain: string;
  search: string;
}

export const CertificationExplorerPage: React.FC = () => {
  const [certifications, setCertifications] = useState<Certification[]>([]);
  const [filteredCertifications, setFilteredCertifications] = useState<Certification[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({
    provider: '',
    difficulty: '',
    domain: '',
    search: ''
  });
  
  const { showError, showSuccess } = useNotifications();
  const { get, post } = useSimpleApi();

  // Mock data for demonstration
  const mockCertifications: Certification[] = [
    {
      id: 1,
      name: "AWS Certified Solutions Architect - Associate",
      provider: "Amazon Web Services",
      description: "Validates technical expertise in designing distributed systems on AWS",
      difficulty_level: "Intermediate",
      estimated_study_hours: 60,
      cost: 150,
      prerequisites: ["Basic AWS knowledge", "Cloud computing fundamentals"],
      domains: ["Cloud Computing", "Architecture", "Security"],
      exam_code: "SAA-C03",
      validity_years: 3
    },
    {
      id: 2,
      name: "Microsoft Azure Fundamentals",
      provider: "Microsoft",
      description: "Demonstrates foundational knowledge of cloud services and Microsoft Azure",
      difficulty_level: "Beginner",
      estimated_study_hours: 30,
      cost: 99,
      prerequisites: ["Basic IT knowledge"],
      domains: ["Cloud Computing", "Azure Services"],
      exam_code: "AZ-900",
      validity_years: 2
    },
    {
      id: 3,
      name: "Google Cloud Professional Cloud Architect",
      provider: "Google Cloud",
      description: "Validates ability to design, develop, and manage robust, secure, scalable solutions",
      difficulty_level: "Advanced",
      estimated_study_hours: 80,
      cost: 200,
      prerequisites: ["3+ years cloud experience", "GCP Associate certification"],
      domains: ["Cloud Computing", "Architecture", "DevOps"],
      exam_code: "PCA",
      validity_years: 2
    },
    {
      id: 4,
      name: "CompTIA Security+",
      provider: "CompTIA",
      description: "Establishes core knowledge required of any cybersecurity role",
      difficulty_level: "Intermediate",
      estimated_study_hours: 50,
      cost: 370,
      prerequisites: ["Network+ or equivalent experience"],
      domains: ["Cybersecurity", "Risk Management", "Compliance"],
      exam_code: "SY0-601",
      validity_years: 3
    },
    {
      id: 5,
      name: "Certified Information Systems Security Professional",
      provider: "ISC2",
      description: "Advanced cybersecurity certification for experienced professionals",
      difficulty_level: "Advanced",
      estimated_study_hours: 120,
      cost: 749,
      prerequisites: ["5 years security experience", "Bachelor's degree"],
      domains: ["Cybersecurity", "Risk Management", "Governance"],
      exam_code: "CISSP",
      validity_years: 3
    }
  ];

  useEffect(() => {
    const loadCertifications = async () => {
      try {
        setLoading(true);
        
        // In a real app, this would be an API call
        // const response = await get('/certifications');
        // if (response.success) {
        //   setCertifications(response.data);
        // }
        
        // For now, use mock data
        setCertifications(mockCertifications);
        setFilteredCertifications(mockCertifications);
      } catch (error) {
        showError('Failed to load certifications', 'Please try again later');
      } finally {
        setLoading(false);
      }
    };

    loadCertifications();
  }, [showError]);

  useEffect(() => {
    // Apply filters
    let filtered = certifications;

    if (filters.search) {
      filtered = filtered.filter(cert =>
        cert.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        cert.provider.toLowerCase().includes(filters.search.toLowerCase()) ||
        cert.description.toLowerCase().includes(filters.search.toLowerCase())
      );
    }

    if (filters.provider) {
      filtered = filtered.filter(cert => cert.provider === filters.provider);
    }

    if (filters.difficulty) {
      filtered = filtered.filter(cert => cert.difficulty_level === filters.difficulty);
    }

    if (filters.domain) {
      filtered = filtered.filter(cert => cert.domains.includes(filters.domain));
    }

    setFilteredCertifications(filtered);
  }, [filters, certifications]);

  const handleFilterChange = (key: keyof FilterOptions, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      provider: '',
      difficulty: '',
      domain: '',
      search: ''
    });
  };

  const addToLearningPath = async (certificationId: number) => {
    try {
      // In a real app, this would be an API call
      // const response = await post('/learning-paths/add-certification', { certificationId });
      // if (response.success) {
      //   showSuccess('Added to Learning Path', 'Certification added successfully');
      // }
      
      showSuccess('Added to Learning Path', 'Certification added successfully');
    } catch (error) {
      showError('Failed to add certification', 'Please try again');
    }
  };

  const getDifficultyBadgeVariant = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return 'default';
      case 'intermediate':
        return 'secondary';
      case 'advanced':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getProviders = () => {
    return Array.from(new Set(certifications.map(cert => cert.provider)));
  };

  const getDifficulties = () => {
    return Array.from(new Set(certifications.map(cert => cert.difficulty_level)));
  };

  const getDomains = () => {
    const allDomains = certifications.flatMap(cert => cert.domains);
    return Array.from(new Set(allDomains));
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Certification Explorer</h1>
          <p className="mt-2 text-gray-600">
            Discover and explore certifications to advance your career
          </p>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search
                </label>
                <Input
                  type="text"
                  placeholder="Search certifications..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Provider
                </label>
                <select
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  value={filters.provider}
                  onChange={(e) => handleFilterChange('provider', e.target.value)}
                >
                  <option value="">All Providers</option>
                  {getProviders().map(provider => (
                    <option key={provider} value={provider}>{provider}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Difficulty
                </label>
                <select
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  value={filters.difficulty}
                  onChange={(e) => handleFilterChange('difficulty', e.target.value)}
                >
                  <option value="">All Levels</option>
                  {getDifficulties().map(difficulty => (
                    <option key={difficulty} value={difficulty}>{difficulty}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Domain
                </label>
                <select
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  value={filters.domain}
                  onChange={(e) => handleFilterChange('domain', e.target.value)}
                >
                  <option value="">All Domains</option>
                  {getDomains().map(domain => (
                    <option key={domain} value={domain}>{domain}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mt-4 flex justify-between items-center">
              <p className="text-sm text-gray-600">
                Showing {filteredCertifications.length} of {certifications.length} certifications
              </p>
              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Certifications Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCertifications.map((cert) => (
            <Card key={cert.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg">{cert.name}</CardTitle>
                  <Badge variant={getDifficultyBadgeVariant(cert.difficulty_level)}>
                    {cert.difficulty_level}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">{cert.provider}</p>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700 mb-4">{cert.description}</p>
                
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Study Hours:</span>
                    <span className="font-medium">{cert.estimated_study_hours}h</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Cost:</span>
                    <span className="font-medium">${cert.cost}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Exam Code:</span>
                    <span className="font-medium">{cert.exam_code}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Valid for:</span>
                    <span className="font-medium">{cert.validity_years} years</span>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Domains:</p>
                  <div className="flex flex-wrap gap-1">
                    {cert.domains.map((domain, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {domain}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={() => addToLearningPath(cert.id)}
                  >
                    Add to Learning Path
                  </Button>
                  <Button variant="outline" className="w-full">
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredCertifications.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No certifications found
            </h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your filters to see more results
            </p>
            <Button onClick={clearFilters}>Clear All Filters</Button>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};
