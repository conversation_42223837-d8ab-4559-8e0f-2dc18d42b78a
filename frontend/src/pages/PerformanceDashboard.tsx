import React, { useState, useEffect } from 'react';
import {
  Activity,
  BarChart3,
  Clock,
  Database,
  HardDrive,
  MemoryStick,
  Monitor,
  RefreshCw,
  Server,
  TrendingUp,
  Zap
} from 'lucide-react';
import { Responsive<PERSON>ontainer, TouchCard, TouchButton, useBreakpoint } from '../components/ResponsiveContainer';

interface SystemMetrics {
  cpu: {
    usage_percent: number;
    count: number;
    load_avg?: number[];
  };
  memory: {
    total: number;
    available: number;
    used: number;
    usage_percent: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage_percent: number;
  };
  timestamp: string;
}

interface EndpointStats {
  endpoint: string;
  method: string;
  count: number;
  avg_response_time: number;
  min_response_time: number;
  max_response_time: number;
  error_count: number;
  error_rate: number;
  last_called: string | null;
}

interface PerformanceSummary {
  uptime_seconds: number;
  uptime_human: string;
  total_requests: number;
  requests_last_hour: number;
  avg_response_time: number;
  error_count: number;
  error_rate: number;
  unique_endpoints: number;
  system_metrics: SystemMetrics;
  timestamp: string;
}

interface CacheStats {
  hits: number;
  misses: number;
  errors: number;
  total_keys: number;
  memory_usage: string;
  uptime: string;
}

const PerformanceDashboard: React.FC = () => {
  const [performanceData, setPerformanceData] = useState<PerformanceSummary | null>(null);
  const [endpointStats, setEndpointStats] = useState<EndpointStats[]>([]);
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const { isMobile } = useBreakpoint();

  const fetchPerformanceData = async () => {
    try {
      setError(null);
      
      // Fetch performance summary
      const perfResponse = await fetch('/api/v1/performance/summary');
      if (perfResponse.ok) {
        const perfData = await perfResponse.json();
        setPerformanceData(perfData);
      }

      // Fetch endpoint statistics
      const endpointsResponse = await fetch('/api/v1/performance/endpoints');
      if (endpointsResponse.ok) {
        const endpointsData = await endpointsResponse.json();
        setEndpointStats(endpointsData.slice(0, 10)); // Top 10 endpoints
      }

      // Fetch cache statistics
      const cacheResponse = await fetch('/api/v1/cache/stats');
      if (cacheResponse.ok) {
        const cacheData = await cacheResponse.json();
        setCacheStats(cacheData.stats);
      }

    } catch (err) {
      setError('Failed to fetch performance data');
      console.error('Performance data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchPerformanceData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }

    return () => {}; // Return empty cleanup function when autoRefresh is false
  }, [autoRefresh]);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours}h ${minutes}m ${secs}s`;
  };

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }): string => {
    if (value >= thresholds.critical) return 'text-red-600 bg-red-100';
    if (value >= thresholds.warning) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="animate-spin h-8 w-8 text-blue-500" />
          <span className="ml-2 text-lg">Loading performance data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
          <TouchButton 
            onClick={fetchPerformanceData} 
            className="mt-2"
            variant="outline"
            size="sm"
          >
            Retry
          </TouchButton>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Monitor className="mr-3 h-8 w-8 text-blue-600" />
            Performance Dashboard
          </h1>
          <p className="text-gray-600 mt-1">Real-time system and application performance metrics</p>
        </div>
        
        <div className="flex items-center gap-2">
          <TouchButton
            onClick={() => setAutoRefresh(!autoRefresh)}
            variant={autoRefresh ? 'primary' : 'outline'}
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh
          </TouchButton>
          
          <TouchButton
            onClick={fetchPerformanceData}
            variant="outline"
            size="sm"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Now
          </TouchButton>
        </div>
      </div>

      {/* System Overview Cards */}
      {performanceData && (
        <ResponsiveContainer
          className="grid gap-4"
          mobileClassName="grid-cols-1"
          tabletClassName="grid-cols-2"
          desktopClassName="grid-cols-4"
        >
          {/* Uptime */}
          <TouchCard className="text-center">
            <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {performanceData.uptime_human}
            </div>
            <div className="text-sm text-gray-600">System Uptime</div>
          </TouchCard>

          {/* Total Requests */}
          <TouchCard className="text-center">
            <BarChart3 className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {performanceData.total_requests.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Total Requests</div>
          </TouchCard>

          {/* Average Response Time */}
          <TouchCard className="text-center">
            <Zap className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {performanceData.avg_response_time.toFixed(1)}ms
            </div>
            <div className="text-sm text-gray-600">Avg Response Time</div>
          </TouchCard>

          {/* Error Rate */}
          <TouchCard className="text-center">
            <TrendingUp className="h-8 w-8 text-red-600 mx-auto mb-2" />
            <div className={`text-2xl font-bold ${
              performanceData.error_rate > 5 ? 'text-red-600' : 'text-gray-900'
            }`}>
              {performanceData.error_rate.toFixed(2)}%
            </div>
            <div className="text-sm text-gray-600">Error Rate</div>
          </TouchCard>
        </ResponsiveContainer>
      )}

      {/* System Resources */}
      {performanceData?.system_metrics && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* CPU Usage */}
          <TouchCard>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center">
                <Activity className="h-5 w-5 mr-2 text-blue-600" />
                CPU Usage
              </h3>
              <span className={`px-2 py-1 rounded text-sm font-medium ${
                getStatusColor(performanceData.system_metrics.cpu.usage_percent, { warning: 70, critical: 85 })
              }`}>
                {performanceData.system_metrics.cpu.usage_percent.toFixed(1)}%
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Cores:</span>
                <span>{performanceData.system_metrics.cpu.count}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${performanceData.system_metrics.cpu.usage_percent}%` }}
                />
              </div>
            </div>
          </TouchCard>

          {/* Memory Usage */}
          <TouchCard>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center">
                <MemoryStick className="h-5 w-5 mr-2 text-green-600" />
                Memory Usage
              </h3>
              <span className={`px-2 py-1 rounded text-sm font-medium ${
                getStatusColor(performanceData.system_metrics.memory.usage_percent, { warning: 80, critical: 90 })
              }`}>
                {performanceData.system_metrics.memory.usage_percent.toFixed(1)}%
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Used:</span>
                <span>{formatBytes(performanceData.system_metrics.memory.used)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Total:</span>
                <span>{formatBytes(performanceData.system_metrics.memory.total)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${performanceData.system_metrics.memory.usage_percent}%` }}
                />
              </div>
            </div>
          </TouchCard>

          {/* Disk Usage */}
          <TouchCard>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center">
                <HardDrive className="h-5 w-5 mr-2 text-purple-600" />
                Disk Usage
              </h3>
              <span className={`px-2 py-1 rounded text-sm font-medium ${
                getStatusColor(performanceData.system_metrics.disk.usage_percent, { warning: 80, critical: 90 })
              }`}>
                {performanceData.system_metrics.disk.usage_percent.toFixed(1)}%
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Used:</span>
                <span>{formatBytes(performanceData.system_metrics.disk.used)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Total:</span>
                <span>{formatBytes(performanceData.system_metrics.disk.total)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${performanceData.system_metrics.disk.usage_percent}%` }}
                />
              </div>
            </div>
          </TouchCard>
        </div>
      )}

      {/* Cache Statistics */}
      {cacheStats && (
        <TouchCard>
          <h3 className="text-lg font-semibold flex items-center mb-4">
            <Database className="h-5 w-5 mr-2 text-red-600" />
            Cache Performance
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{cacheStats.total_keys}</div>
              <div className="text-sm text-gray-600">Total Keys</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{cacheStats.memory_usage}</div>
              <div className="text-sm text-gray-600">Memory Usage</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{cacheStats.hits}</div>
              <div className="text-sm text-gray-600">Cache Hits</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{cacheStats.misses}</div>
              <div className="text-sm text-gray-600">Cache Misses</div>
            </div>
          </div>
        </TouchCard>
      )}

      {/* Top Endpoints */}
      <TouchCard>
        <h3 className="text-lg font-semibold flex items-center mb-4">
          <Server className="h-5 w-5 mr-2 text-indigo-600" />
          Top API Endpoints
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">Endpoint</th>
                <th className="text-left py-2">Method</th>
                <th className="text-right py-2">Requests</th>
                <th className="text-right py-2">Avg Time</th>
                <th className="text-right py-2">Error Rate</th>
              </tr>
            </thead>
            <tbody>
              {endpointStats.map((endpoint, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="py-2 font-mono text-xs">{endpoint.endpoint}</td>
                  <td className="py-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                      endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                      endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {endpoint.method}
                    </span>
                  </td>
                  <td className="text-right py-2">{endpoint.count.toLocaleString()}</td>
                  <td className="text-right py-2">{endpoint.avg_response_time.toFixed(1)}ms</td>
                  <td className="text-right py-2">
                    <span className={`${endpoint.error_rate > 5 ? 'text-red-600' : 'text-gray-900'}`}>
                      {endpoint.error_rate.toFixed(1)}%
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </TouchCard>
    </div>
  );
};

export default PerformanceDashboard;
