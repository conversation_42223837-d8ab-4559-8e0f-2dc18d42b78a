import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Search, Filter, Grid, List, ExternalLink, DollarSign, Clock, Award, Building } from 'lucide-react';
import { certificationApi, domainApi } from '../services/api';
import { Certification } from '../types/certification';

const CertificationExplorer: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDomain, setSelectedDomain] = useState<string>('');
  const [selectedLevel, setSelectedLevel] = useState<string>('');
  const [maxCost, setMaxCost] = useState<number>(10000);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'cost' | 'level'>('name');

  // Fetch certifications
  const { data: certifications, isLoading: certsLoading, error: certsError } = useQuery({
    queryKey: ['certifications'],
    queryFn: () => certificationApi.getAll(),
  });

  // Fetch domains
  const { data: domains } = useQuery({
    queryKey: ['domains'],
    queryFn: () => domainApi.getAll(),
  });

  // Filter and sort certifications
  const filteredCertifications = useMemo(() => {
    if (!certifications?.items) return [];

    let filtered = certifications.items.filter((cert: Certification) => {
      const matchesSearch = cert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (cert.description && cert.description.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesDomain = !selectedDomain || cert.domain === selectedDomain;
      const matchesLevel = !selectedLevel || cert.level === selectedLevel;
      const matchesCost = !cert.cost || cert.cost <= maxCost;

      return matchesSearch && matchesDomain && matchesLevel && matchesCost;
    });

    // Sort certifications
    filtered.sort((a: Certification, b: Certification) => {
      switch (sortBy) {
        case 'cost':
          return (a.cost || 0) - (b.cost || 0);
        case 'level':
          const levelOrder = { 'Associate': 1, 'Professional': 2, 'Expert': 3, 'Specialist': 4, 'Advanced': 5 };
          return (levelOrder[a.level as keyof typeof levelOrder] || 0) - (levelOrder[b.level as keyof typeof levelOrder] || 0);
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [certifications, searchTerm, selectedDomain, selectedLevel, maxCost, sortBy]);

  // Get unique levels
  const levels = useMemo(() => {
    if (!certifications?.items) return [];
    return Array.from(new Set(certifications.items.map((cert: Certification) => cert.level)));
  }, [certifications]);

  // Get unique domains
  const domainOptions = useMemo(() => {
    if (!certifications?.items) return [];
    return Array.from(new Set(certifications.items.map((cert: Certification) => cert.domain)));
  }, [certifications]);

  const CertificationCard: React.FC<{ certification: Certification }> = ({ certification }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow" data-testid="certification-card">
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">{certification.name}</h3>
        <span className={`px-2 py-1 text-xs rounded-full ${
          certification.level === 'Associate' ? 'bg-green-100 text-green-800' :
          certification.level === 'Professional' ? 'bg-blue-100 text-blue-800' :
          certification.level === 'Expert' ? 'bg-purple-100 text-purple-800' :
          certification.level === 'Advanced' ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {certification.level}
        </span>
      </div>

      <div className="space-y-2 mb-4">
        <div className="flex items-center text-sm text-gray-600">
          <Building className="w-4 h-4 mr-2" />
          <span>{certification.domain}</span>
        </div>
        {certification.cost && (
          <div className="flex items-center text-sm text-gray-600">
            <DollarSign className="w-4 h-4 mr-2" />
            <span>${certification.cost.toLocaleString()}</span>
          </div>
        )}
        <div className="flex items-center text-sm text-gray-600">
          <Award className="w-4 h-4 mr-2" />
          <span>Difficulty: {certification.difficulty}/5</span>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-4 line-clamp-3">
        {certification.description}
      </p>

      <div className="flex justify-between items-center">
        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
          View Details
        </button>
        {certification.url && (
          <a
            href={certification.url}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center text-gray-500 hover:text-gray-700 text-sm"
          >
            <ExternalLink className="w-4 h-4 mr-1" />
            Official Site
          </a>
        )}
      </div>
    </div>
  );

  const CertificationListItem: React.FC<{ certification: Certification }> = ({ certification }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow" data-testid="certification-list-item">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-semibold text-gray-900">{certification.name}</h3>
            <span className={`px-2 py-1 text-xs rounded-full ${
              certification.level === 'Associate' ? 'bg-green-100 text-green-800' :
              certification.level === 'Professional' ? 'bg-blue-100 text-blue-800' :
              certification.level === 'Expert' ? 'bg-purple-100 text-purple-800' :
              certification.level === 'Advanced' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {certification.level}
            </span>
          </div>
          <div className="flex items-center space-x-6 mt-2 text-sm text-gray-600">
            <span className="flex items-center">
              <Building className="w-4 h-4 mr-1" />
              {certification.domain}
            </span>
            {certification.cost && (
              <span className="flex items-center">
                <DollarSign className="w-4 h-4 mr-1" />
                ${certification.cost.toLocaleString()}
              </span>
            )}
            <span className="flex items-center">
              <Award className="w-4 h-4 mr-1" />
              Difficulty: {certification.difficulty}/5
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium px-3 py-1 rounded border border-blue-200 hover:bg-blue-50">
            View Details
          </button>
          {certification.url && (
            <a
              href={certification.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-gray-500 hover:text-gray-700 text-sm px-3 py-1 rounded border border-gray-200 hover:bg-gray-50"
            >
              <ExternalLink className="w-4 h-4 mr-1" />
              Official Site
            </a>
          )}
        </div>
      </div>
    </div>
  );

  if (certsLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(9)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-6 bg-gray-300 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-2/3 mb-4"></div>
                <div className="h-20 bg-gray-300 rounded mb-4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (certsError) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Certifications</h3>
          <p className="text-red-600">Unable to load certification data. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Certification Explorer</h1>
        <p className="text-gray-600">Discover and explore {certifications?.items?.length || 0} security certifications</p>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search certifications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Domain Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Domain</label>
            <select
              value={selectedDomain}
              onChange={(e) => setSelectedDomain(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Domains</option>
              {domainOptions.map((domain) => (
                <option key={domain} value={domain}>{domain}</option>
              ))}
            </select>
          </div>

          {/* Level Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
            <select
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Levels</option>
              {levels.map((level) => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>

          {/* Cost Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Max Cost: ${maxCost}</label>
            <input
              type="range"
              min="0"
              max="10000"
              step="100"
              value={maxCost}
              onChange={(e) => setMaxCost(Number(e.target.value))}
              className="w-full"
            />
          </div>
        </div>

        {/* View Controls */}
        <div className="flex justify-between items-center mt-6 pt-6 border-t border-gray-200">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              {filteredCertifications.length} of {certifications?.items?.length || 0} certifications
            </span>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'cost' | 'level')}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
              data-testid="sort-select"
            >
              <option value="name">Sort by Name</option>
              <option value="cost">Sort by Cost</option>
              <option value="level">Sort by Level</option>
            </select>
            <div className="flex border border-gray-300 rounded-md">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                data-testid="grid-view"
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                data-testid="list-view"
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      {filteredCertifications.length === 0 ? (
        <div className="text-center py-12">
          <Filter className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No certifications found</h3>
          <p className="text-gray-600">Try adjusting your filters to see more results.</p>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredCertifications.map((certification) => (
            viewMode === 'grid' ? (
              <CertificationCard key={certification.id} certification={certification} />
            ) : (
              <CertificationListItem key={certification.id} certification={certification} />
            )
          ))}
        </div>
      )}
    </div>
  );
};

export default CertificationExplorer;
