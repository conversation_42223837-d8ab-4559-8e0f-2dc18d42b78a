import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Divider,
  Paper
} from '@mui/material';
import {
  Search as SearchIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  AttachMoney as MoneyIcon,
  Business as BusinessIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { useSimpleApi } from '../hooks/useSimpleApi';

interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'remote';
  salaryMin: number;
  salaryMax: number;
  description: string;
  requirements: string[];
  certifications: string[];
  postedDate: string;
  applicationUrl: string;
  isSaved: boolean;
  matchScore: number;
}

interface JobFilters {
  keywords: string;
  location: string;
  jobType: string;
  salaryMin: string;
  salaryMax: string;
  certifications: string[];
}

interface SalaryInsight {
  certification: string;
  averageSalary: number;
  salaryRange: {
    min: number;
    max: number;
  };
  jobCount: number;
  trend: 'up' | 'down' | 'stable';
}

const JobSearch: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [savedJobs, setSavedJobs] = useState<Job[]>([]);
  const [salaryInsights, setSalaryInsights] = useState<SalaryInsight[]>([]);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [isJobDetailOpen, setIsJobDetailOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [filters, setFilters] = useState<JobFilters>({
    keywords: '',
    location: '',
    jobType: '',
    salaryMin: '',
    salaryMax: '',
    certifications: []
  });

  const { get, post } = useSimpleApi();

  useEffect(() => {
    loadSavedJobs();
    loadSalaryInsights();
  }, []);

  const searchJobs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const queryParams = new URLSearchParams();
      if (filters.keywords) queryParams.append('keywords', filters.keywords);
      if (filters.location) queryParams.append('location', filters.location);
      if (filters.jobType) queryParams.append('type', filters.jobType);
      if (filters.salaryMin) queryParams.append('salary_min', filters.salaryMin);
      if (filters.salaryMax) queryParams.append('salary_max', filters.salaryMax);
      if (filters.certifications.length > 0) {
        queryParams.append('certifications', filters.certifications.join(','));
      }

      const jobsResponse = await get(`/api/v1/jobs/search?${queryParams.toString()}`);
      if (jobsResponse.success) {
        setJobs(jobsResponse.data as Job[]);
      }
    } catch (err) {
      setError('Failed to search jobs');
      console.error('Job search error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadSavedJobs = async () => {
    try {
      const savedJobsResponse = await get('/api/v1/jobs/saved');
      if (savedJobsResponse.success) {
        setSavedJobs(savedJobsResponse.data as Job[]);
      }
    } catch (err) {
      console.error('Failed to load saved jobs:', err);
    }
  };

  const loadSalaryInsights = async () => {
    try {
      const insightsResponse = await get('/api/v1/jobs/salary-insights');
      if (insightsResponse.success) {
        setSalaryInsights(insightsResponse.data as SalaryInsight[]);
      }
    } catch (err) {
      console.error('Failed to load salary insights:', err);
    }
  };

  const toggleSaveJob = async (job: Job) => {
    try {
      if (job.isSaved) {
        await post(`/api/v1/jobs/saved/${job.id}/delete`, {});
        setJobs(jobs.map(j => j.id === job.id ? { ...j, isSaved: false } : j));
        setSavedJobs(savedJobs.filter(j => j.id !== job.id));
      } else {
        await post('/api/v1/jobs/save', { jobId: job.id });
        setJobs(jobs.map(j => j.id === job.id ? { ...j, isSaved: true } : j));
        setSavedJobs([...savedJobs, { ...job, isSaved: true }]);
      }
    } catch (err) {
      setError('Failed to save/unsave job');
      console.error('Save job error:', err);
    }
  };

  const openJobDetail = (job: Job) => {
    setSelectedJob(job);
    setIsJobDetailOpen(true);
  };

  const applyToJob = (job: Job) => {
    window.open(job.applicationUrl, '_blank');
  };

  const formatSalary = (min: number, max: number) => {
    return `$${(min / 1000).toFixed(0)}k - $${(max / 1000).toFixed(0)}k`;
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return '#4caf50';
    if (score >= 60) return '#ff9800';
    return '#f44336';
  };

  const JobCard: React.FC<{ job: Job }> = ({ job }) => (
    <Card sx={{ mb: 2, cursor: 'pointer' }} onClick={() => openJobDetail(job)}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box flex={1}>
            <Typography variant="h6" component="h3" gutterBottom>
              {job.title}
            </Typography>
            <Box display="flex" alignItems="center" mb={1}>
              <BusinessIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {job.company}
              </Typography>
            </Box>
            <Box display="flex" alignItems="center" mb={1}>
              <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {job.location}
              </Typography>
              <Chip
                label={job.type}
                size="small"
                sx={{ ml: 2 }}
                color={job.type === 'remote' ? 'primary' : 'default'}
              />
            </Box>
            <Box display="flex" alignItems="center" mb={2}>
              <MoneyIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {formatSalary(job.salaryMin, job.salaryMax)}
              </Typography>
            </Box>
          </Box>
          <Box display="flex" flexDirection="column" alignItems="flex-end">
            <Chip
              label={`${job.matchScore}% match`}
              size="small"
              sx={{
                bgcolor: getMatchScoreColor(job.matchScore),
                color: 'white',
                mb: 1
              }}
            />
            <IconButton
              onClick={(e) => {
                e.stopPropagation();
                toggleSaveJob(job);
              }}
              color={job.isSaved ? 'primary' : 'default'}
            >
              {job.isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}
            </IconButton>
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {job.description.substring(0, 200)}...
        </Typography>
        
        <Box display="flex" flexWrap="wrap" gap={1}>
          {job.certifications.slice(0, 3).map((cert, index) => (
            <Chip key={index} label={cert} size="small" variant="outlined" />
          ))}
          {job.certifications.length > 3 && (
            <Chip label={`+${job.certifications.length - 3} more`} size="small" />
          )}
        </Box>
      </CardContent>
      <CardActions>
        <Button size="small" onClick={(e) => {
          e.stopPropagation();
          applyToJob(job);
        }}>
          Apply Now
        </Button>
        <Button size="small" onClick={() => openJobDetail(job)}>
          View Details
        </Button>
        <Box ml="auto">
          <Typography variant="caption" color="text.secondary">
            Posted {new Date(job.postedDate).toLocaleDateString()}
          </Typography>
        </Box>
      </CardActions>
    </Card>
  );

  const SalaryInsightCard: React.FC<{ insight: SalaryInsight }> = ({ insight }) => (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">{insight.certification}</Typography>
          <Box display="flex" alignItems="center">
            <TrendingUpIcon
              fontSize="small"
              sx={{
                color: insight.trend === 'up' ? '#4caf50' : 
                       insight.trend === 'down' ? '#f44336' : '#757575',
                mr: 1
              }}
            />
            <Typography variant="body2" color="text.secondary">
              {insight.jobCount} jobs
            </Typography>
          </Box>
        </Box>
        
        <Typography variant="h4" color="primary" gutterBottom>
          ${(insight.averageSalary / 1000).toFixed(0)}k
        </Typography>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Average Salary
        </Typography>
        
        <Typography variant="body2" color="text.secondary">
          Range: ${(insight.salaryRange.min / 1000).toFixed(0)}k - ${(insight.salaryRange.max / 1000).toFixed(0)}k
        </Typography>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Job Search & Career Insights
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Search Filters */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Search Filters
        </Typography>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', alignItems: 'center' }}>
          <TextField
            fullWidth
            label="Keywords"
            value={filters.keywords}
            onChange={(e) => setFilters({ ...filters, keywords: e.target.value })}
            placeholder="e.g., Security Analyst"
          />
          <TextField
            fullWidth
            label="Location"
            value={filters.location}
            onChange={(e) => setFilters({ ...filters, location: e.target.value })}
            placeholder="e.g., New York"
          />
          <FormControl fullWidth>
            <InputLabel>Job Type</InputLabel>
            <Select
              value={filters.jobType}
              onChange={(e) => setFilters({ ...filters, jobType: e.target.value })}
            >
              <MenuItem value="">All Types</MenuItem>
              <MenuItem value="full-time">Full Time</MenuItem>
              <MenuItem value="part-time">Part Time</MenuItem>
              <MenuItem value="contract">Contract</MenuItem>
              <MenuItem value="remote">Remote</MenuItem>
            </Select>
          </FormControl>
          <TextField
            fullWidth
            label="Min Salary"
            value={filters.salaryMin}
            onChange={(e) => setFilters({ ...filters, salaryMin: e.target.value })}
            placeholder="50000"
            type="number"
          />
          <TextField
            fullWidth
            label="Max Salary"
            value={filters.salaryMax}
            onChange={(e) => setFilters({ ...filters, salaryMax: e.target.value })}
            placeholder="150000"
            type="number"
          />
          <Button
            fullWidth
            variant="contained"
            onClick={searchJobs}
            disabled={loading}
            startIcon={<SearchIcon />}
          >
            Search
          </Button>
        </div>
      </Paper>

      {loading && <LinearProgress sx={{ mb: 3 }} />}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
          <Tab label={`Search Results (${jobs.length})`} />
          <Tab label={`Saved Jobs (${savedJobs.length})`} />
          <Tab label="Salary Insights" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <div>
        {currentTab === 0 && (
          <Box>
            {jobs.length === 0 && !loading ? (
              <Typography variant="body1" color="text.secondary" textAlign="center" py={4}>
                No jobs found. Try adjusting your search criteria.
              </Typography>
            ) : (
              jobs.map((job) => <JobCard key={job.id} job={job} />)
            )}
          </Box>
        )}

        {currentTab === 1 && (
          <Box>
            {savedJobs.length === 0 ? (
              <Typography variant="body1" color="text.secondary" textAlign="center" py={4}>
                No saved jobs yet. Save jobs from the search results to see them here.
              </Typography>
            ) : (
              savedJobs.map((job) => <JobCard key={job.id} job={job} />)
            )}
          </Box>
        )}

        {currentTab === 2 && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem' }}>
            {salaryInsights.map((insight, index) => (
              <SalaryInsightCard key={index} insight={insight} />
            ))}
          </div>
        )}
      </div>

      {/* Job Detail Dialog */}
      <Dialog
        open={isJobDetailOpen}
        onClose={() => setIsJobDetailOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedJob && (
          <>
            <DialogTitle>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="h5">{selectedJob.title}</Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    {selectedJob.company} • {selectedJob.location}
                  </Typography>
                </Box>
                <IconButton
                  onClick={() => toggleSaveJob(selectedJob)}
                  color={selectedJob.isSaved ? 'primary' : 'default'}
                >
                  {selectedJob.isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Box mb={3}>
                <Typography variant="h6" gutterBottom>
                  Job Details
                </Typography>
                <Typography variant="body1" paragraph>
                  {selectedJob.description}
                </Typography>
              </Box>

              <Box mb={3}>
                <Typography variant="h6" gutterBottom>
                  Requirements
                </Typography>
                <List dense>
                  {selectedJob.requirements.map((req, index) => (
                    <ListItem key={index}>
                      <ListItemText primary={req} />
                    </ListItem>
                  ))}
                </List>
              </Box>

              <Box mb={3}>
                <Typography variant="h6" gutterBottom>
                  Relevant Certifications
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {selectedJob.certifications.map((cert, index) => (
                    <Chip key={index} label={cert} variant="outlined" />
                  ))}
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Salary Range
                  </Typography>
                  <Typography variant="h6">
                    {formatSalary(selectedJob.salaryMin, selectedJob.salaryMax)}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Match Score
                  </Typography>
                  <Chip
                    label={`${selectedJob.matchScore}%`}
                    sx={{
                      bgcolor: getMatchScoreColor(selectedJob.matchScore),
                      color: 'white'
                    }}
                  />
                </Box>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setIsJobDetailOpen(false)}>
                Close
              </Button>
              <Button
                variant="contained"
                onClick={() => applyToJob(selectedJob)}
              >
                Apply Now
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
};

export default JobSearch;
