import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import { Card } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import { Checkbox } from '../components/ui/checkbox';
import { Label } from '../components/ui/label';
import { PasswordStrengthIndicator } from '../components/forms/PasswordStrengthIndicator';
import { EmailSuggestions } from '../components/forms/EmailSuggestions';
import { TermsModal } from '../components/modals/TermsModal';
import { useNotifications } from '../hooks/useNotifications';
import { authApi } from '../services/api';

// Registration form schema with comprehensive validation
const registrationSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
  firstName: z.string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'First name contains invalid characters'),
  lastName: z.string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Last name contains invalid characters'),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
  marketingConsent: z.boolean().optional(),
  referralCode: z.string().optional()
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

type RegistrationFormData = z.infer<typeof registrationSchema>;

interface RegistrationPageProps {
  redirectTo?: string;
  referralCode?: string;
}

export const RegistrationPage: React.FC<RegistrationPageProps> = ({
  redirectTo = '/dashboard',
  referralCode
}) => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotifications();
  const [isLoading, setIsLoading] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [emailSuggestions, setEmailSuggestions] = useState<string[]>([]);
  const [showEmailSuggestions, setShowEmailSuggestions] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
    setError,
    clearErrors
  } = useForm<RegistrationFormData>({
    resolver: zodResolver(registrationSchema),
    mode: 'onChange',
    defaultValues: {
      referralCode: referralCode || '',
      marketingConsent: false,
      acceptTerms: false
    }
  });

  const watchedPassword = watch('password');
  const watchedEmail = watch('email');

  // Check email availability with debouncing
  const checkEmailAvailability = async (email: string) => {
    if (!email || !email.includes('@')) return;

    try {
      const response = await authApi.checkEmailAvailability(email);
      if (!response.available) {
        setError('email', { message: 'This email is already registered' });
        if (response.suggestions) {
          setEmailSuggestions(response.suggestions);
          setShowEmailSuggestions(true);
        }
      } else {
        clearErrors('email');
        setShowEmailSuggestions(false);
      }
    } catch (error) {
      console.error('Email check failed:', error);
    }
  };

  // Debounced email check
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (watchedEmail) {
        checkEmailAvailability(watchedEmail);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [watchedEmail]);

  const onSubmit = async (data: RegistrationFormData) => {
    setIsLoading(true);
    
    try {
      const response = await authApi.register({
        email: data.email,
        password: data.password,
        confirmPassword: data.confirmPassword,
        firstName: data.firstName,
        lastName: data.lastName,
        acceptTerms: data.acceptTerms,
        marketingConsent: data.marketingConsent,
        referralCode: data.referralCode
      });

      if (response.success) {
        showSuccess(
          'Registration Successful!',
          'Please check your email to verify your account.'
        );
        
        // Navigate to email verification page
        navigate('/verify-email', { 
          state: { 
            email: data.email,
            message: 'We\'ve sent a verification email to your inbox.' 
          }
        });
      }
    } catch (error: any) {
      showError(
        'Registration Failed',
        error.response?.data?.detail || 'An error occurred during registration.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailSuggestionClick = (suggestion: string) => {
    setValue('email', suggestion);
    setShowEmailSuggestions(false);
    clearErrors('email');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="p-8 shadow-xl">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Join CertRats
            </h1>
            <p className="text-gray-600">
              Start your cybersecurity certification journey today
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Email Input with Suggestions */}
            <div className="relative">
              <Input
                {...register('email')}
                type="email"
                label="Email address"
                placeholder="Enter your email"
                error={errors.email?.message}
                data-testid="email-input"
                autoComplete="email"
                aria-describedby={errors.email ? 'email-error' : undefined}
              />
              
              {showEmailSuggestions && emailSuggestions.length > 0 && (
                <EmailSuggestions
                  suggestions={emailSuggestions}
                  onSuggestionClick={handleEmailSuggestionClick}
                />
              )}
            </div>

            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-4">
              <Input
                {...register('firstName')}
                type="text"
                label="First name"
                placeholder="John"
                error={errors.firstName?.message}
                data-testid="first-name-input"
                autoComplete="given-name"
              />
              
              <Input
                {...register('lastName')}
                type="text"
                label="Last name"
                placeholder="Doe"
                error={errors.lastName?.message}
                data-testid="last-name-input"
                autoComplete="family-name"
              />
            </div>

            {/* Password Input with Strength Indicator */}
            <div>
              <Input
                {...register('password')}
                type="password"
                label="Password"
                placeholder="Create a strong password"
                error={errors.password?.message}
                data-testid="password-input"
                autoComplete="new-password"
              />
              
              {watchedPassword && (
                <PasswordStrengthIndicator password={watchedPassword} />
              )}
            </div>

            {/* Confirm Password */}
            <Input
              {...register('confirmPassword')}
              type="password"
              label="Confirm password"
              placeholder="Confirm your password"
              error={errors.confirmPassword?.message}
              data-testid="confirm-password-input"
              autoComplete="new-password"
            />

            {/* Referral Code (Optional) */}
            <Input
              {...register('referralCode')}
              type="text"
              label="Referral code (optional)"
              placeholder="Enter referral code"
              data-testid="referral-code-input"
            />

            {/* Terms and Marketing Consent */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  {...register('acceptTerms')}
                  data-testid="terms-checkbox"
                  required
                />
                <Label htmlFor="acceptTerms">
                  <span>
                    I agree to the{' '}
                    <button
                      type="button"
                      onClick={() => setShowTermsModal(true)}
                      className="text-blue-600 hover:text-blue-800 underline"
                    >
                      Terms of Service
                    </button>{' '}
                    and{' '}
                    <button
                      type="button"
                      onClick={() => setShowTermsModal(true)}
                      className="text-blue-600 hover:text-blue-800 underline"
                    >
                      Privacy Policy
                    </button>
                  </span>
                </Label>
              </div>
              {errors.acceptTerms?.message && (
                <p className="text-red-600 text-sm">{errors.acceptTerms.message}</p>
              )}

              <div className="flex items-center space-x-2">
                <Checkbox
                  {...register('marketingConsent')}
                  data-testid="marketing-consent-checkbox"
                />
                <Label htmlFor="marketingConsent">
                  I'd like to receive updates about new features and certification opportunities
                </Label>
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              variant="default"
              size="lg"
              disabled={!isValid || isLoading}
              className="w-full"
              data-testid="register-button"
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </Button>
          </form>

          {/* Login Link */}
          <div className="mt-6 text-center">
            <p className="text-gray-600">
              Already have an account?{' '}
              <Link
                to="/login"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Sign in
              </Link>
            </p>
          </div>
        </Card>

        {/* Terms Modal */}
        <TermsModal
          isOpen={showTermsModal}
          onClose={() => setShowTermsModal(false)}
          onAccept={() => {
            setValue('acceptTerms', true);
            setShowTermsModal(false);
          }}
        />
      </motion.div>
    </div>
  );
};
