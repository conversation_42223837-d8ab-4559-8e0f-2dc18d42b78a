import React, { useState, useEffect } from 'react';
import { Certification, Organization } from '../types/certification';
import api from '../services/api';

interface CostCalculation {
  id?: number;
  name: string;
  certifications: Certification[];
  totalCost: number;
  currency: string;
  estimatedTime: number;
}

const CostCalculator: React.FC = () => {
  const [certifications, setCertifications] = useState<Certification[]>([]);
  const [selectedCerts, setSelectedCerts] = useState<Certification[]>([]);
  const [calculationName, setCalculationName] = useState('');
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [result, setResult] = useState<CostCalculation | null>(null);

  useEffect(() => {
    loadCertifications();
  }, []);

  const loadCertifications = async () => {
    try {
      setLoading(true);
      // For now, use mock data since the API might not be fully connected
      const mockCertifications: Certification[] = [
        {
          id: 1,
          name: 'Security+',
          category: 'Entry Level',
          domain: 'Network Security',
          level: 'Associate',
          difficulty: 'Intermediate',
          focus: 'General',
          cost: 370,
          currency: 'USD',
          description: 'Entry-level cybersecurity certification',
          validity_period: 36,
          is_active: true,
          is_deleted: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization: {
            id: 1,
            name: 'CompTIA',
            website: 'https://www.comptia.org'
          }
        },
        {
          id: 2,
          name: 'CISSP',
          category: 'Advanced',
          domain: 'Risk Management',
          level: 'Expert',
          difficulty: 'Expert',
          focus: 'Management',
          cost: 749,
          currency: 'USD',
          description: 'Advanced security management certification',
          validity_period: 36,
          is_active: true,
          is_deleted: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization: {
            id: 2,
            name: '(ISC)²',
            website: 'https://www.isc2.org'
          }
        },
        {
          id: 3,
          name: 'CEH',
          category: 'Intermediate',
          domain: 'Penetration Testing',
          level: 'Professional',
          difficulty: 'Advanced',
          focus: 'Technical',
          cost: 1199,
          currency: 'USD',
          description: 'Certified Ethical Hacker certification',
          validity_period: 36,
          is_active: true,
          is_deleted: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization: {
            id: 3,
            name: 'EC-Council',
            website: 'https://www.eccouncil.org'
          }
        }
      ];
      setCertifications(mockCertifications);
    } catch (error) {
      console.error('Failed to load certifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCertificationToggle = (cert: Certification) => {
    setSelectedCerts(prev => {
      const isSelected = prev.some(c => c.id === cert.id);
      if (isSelected) {
        return prev.filter(c => c.id !== cert.id);
      } else {
        return [...prev, cert];
      }
    });
  };

  const calculateCost = async () => {
    if (selectedCerts.length === 0) return;

    setCalculating(true);
    try {
      // Simulate calculation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const totalCost = selectedCerts.reduce((sum, cert) => sum + cert.cost, 0);
      const difficultyHours = {
        'Beginner': 40,
        'Intermediate': 60,
        'Advanced': 80,
        'Expert': 120
      };
      const estimatedTime = selectedCerts.reduce((sum, cert) => sum + (difficultyHours[cert.difficulty] || 60), 0);

      const calculation: CostCalculation = {
        name: calculationName || 'Unnamed Calculation',
        certifications: selectedCerts,
        totalCost,
        currency: 'USD',
        estimatedTime
      };

      setResult(calculation);
    } catch (error) {
      console.error('Calculation failed:', error);
    } finally {
      setCalculating(false);
    }
  };

  const resetCalculation = () => {
    setSelectedCerts([]);
    setCalculationName('');
    setResult(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Cost Calculator</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Certification Selection */}
          <div className="bg-white shadow-lg rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Select Certifications</h2>
            
            <div className="mb-4">
              <input
                type="text"
                placeholder="Calculation Name (optional)"
                value={calculationName}
                onChange={(e) => setCalculationName(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {certifications.map(cert => (
                <div
                  key={cert.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedCerts.some(c => c.id === cert.id)
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleCertificationToggle(cert)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-900">{cert.name}</h3>
                      <p className="text-sm text-gray-600">{cert.category} • {cert.domain}</p>
                      <p className="text-sm text-gray-500 mt-1">{cert.description}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-600">${cert.cost}</p>
                      <p className="text-xs text-gray-500">Difficulty: {cert.difficulty}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 flex space-x-4">
              <button
                onClick={calculateCost}
                disabled={selectedCerts.length === 0 || calculating}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {calculating ? 'Calculating...' : 'Calculate Cost'}
              </button>
              <button
                onClick={resetCalculation}
                className="bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
              >
                Reset
              </button>
            </div>
          </div>

          {/* Results */}
          <div className="bg-white shadow-lg rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Calculation Results</h2>
            
            {result ? (
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900">{result.name}</h3>
                  <p className="text-sm text-gray-600">{result.certifications.length} certification(s) selected</p>
                </div>

                <div className="border-t pt-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-600">Total Cost</p>
                      <p className="text-2xl font-bold text-green-600">
                        ${result.totalCost.toLocaleString()} {result.currency}
                      </p>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-600">Estimated Study Time</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {result.estimatedTime} hours
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-semibold mb-2">Selected Certifications:</h4>
                  <div className="space-y-2">
                    {result.certifications.map(cert => (
                      <div key={cert.id} className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-900">{cert.name}</span>
                        <span className="font-semibold text-green-600">${cert.cost}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <p>Select certifications and click "Calculate Cost" to see results</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostCalculator;
