import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication for protected pages
    await page.addInitScript(() => {
      localStorage.setItem('access_token', 'mock_token');
    });
  });

  test('login page should be accessible', async ({ page }) => {
    await page.goto('/login');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('dashboard page should be accessible', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('certification explorer should be accessible', async ({ page }) => {
    await page.goto('/certifications');
    await page.waitForLoadState('networkidle');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('keyboard navigation should work on login page', async ({ page }) => {
    await page.goto('/login');
    
    // Test tab navigation
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('email-input')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('password-input')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('remember-me-checkbox')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('login-button')).toBeFocused();
    
    // Test form submission with Enter
    await page.getByTestId('email-input').focus();
    await page.keyboard.type('<EMAIL>');
    
    await page.keyboard.press('Tab');
    await page.keyboard.type('password123');
    
    await page.keyboard.press('Enter');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
  });

  test('keyboard navigation should work on dashboard', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Test navigation through interactive elements
    await page.keyboard.press('Tab');
    
    // Should be able to navigate through sidebar links
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();
    
    // Test multiple tab presses
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab');
      const currentFocus = page.locator(':focus');
      if (await currentFocus.isVisible()) {
        // Verify focused element is interactive
        const tagName = await currentFocus.evaluate(el => el.tagName.toLowerCase());
        const role = await currentFocus.getAttribute('role');
        const isInteractive = ['a', 'button', 'input', 'select', 'textarea'].includes(tagName) ||
                             ['button', 'link', 'menuitem', 'tab'].includes(role || '');
        
        if (isInteractive) {
          expect(true).toBeTruthy(); // Element is properly focusable
        }
      }
    }
  });

  test('screen reader compatibility on login page', async ({ page }) => {
    await page.goto('/login');
    
    // Check for proper labels
    const emailInput = page.getByTestId('email-input');
    const passwordInput = page.getByTestId('password-input');
    const loginButton = page.getByTestId('login-button');
    
    // Check ARIA labels or associated labels
    const emailLabel = await emailInput.getAttribute('aria-label') || 
                      await page.locator('label[for="email"]').textContent() ||
                      await page.getByText('Email address').textContent();
    expect(emailLabel).toBeTruthy();
    
    const passwordLabel = await passwordInput.getAttribute('aria-label') ||
                         await page.locator('label[for="password"]').textContent() ||
                         await page.getByText('Password').textContent();
    expect(passwordLabel).toBeTruthy();
    
    // Check button has accessible name
    const buttonText = await loginButton.textContent() || await loginButton.getAttribute('aria-label');
    expect(buttonText).toBeTruthy();
    
    // Check form has proper structure
    const form = page.locator('form');
    await expect(form).toBeAttached();
  });

  test('color contrast should meet WCAG standards', async ({ page }) => {
    await page.goto('/login');
    
    // This test would typically use a color contrast analyzer
    // For now, we'll check that text is visible and readable
    
    const headings = page.locator('h1, h2, h3, h4, h5, h6');
    const headingCount = await headings.count();
    
    for (let i = 0; i < headingCount; i++) {
      const heading = headings.nth(i);
      if (await heading.isVisible()) {
        // Check that heading has text content
        const text = await heading.textContent();
        expect(text?.trim()).toBeTruthy();
        
        // Check that heading is not transparent or invisible
        const opacity = await heading.evaluate(el => getComputedStyle(el).opacity);
        expect(parseFloat(opacity)).toBeGreaterThan(0);
      }
    }
    
    // Check button contrast
    const buttons = page.getByRole('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 3); i++) {
      const button = buttons.nth(i);
      if (await button.isVisible()) {
        const opacity = await button.evaluate(el => getComputedStyle(el).opacity);
        expect(parseFloat(opacity)).toBeGreaterThan(0);
      }
    }
  });

  test('focus indicators should be visible', async ({ page }) => {
    await page.goto('/login');
    
    const emailInput = page.getByTestId('email-input');
    await emailInput.focus();
    
    // Check that focus is visible (this is browser-dependent)
    await expect(emailInput).toBeFocused();
    
    // Check that focused element has some visual indication
    const focusStyles = await emailInput.evaluate(el => {
      const styles = getComputedStyle(el);
      return {
        outline: styles.outline,
        outlineWidth: styles.outlineWidth,
        boxShadow: styles.boxShadow,
        borderColor: styles.borderColor
      };
    });
    
    // Should have some form of focus indication
    const hasFocusIndicator = focusStyles.outline !== 'none' ||
                             focusStyles.outlineWidth !== '0px' ||
                             focusStyles.boxShadow !== 'none' ||
                             focusStyles.borderColor !== 'initial';
    
    expect(hasFocusIndicator).toBeTruthy();
  });

  test('error messages should be accessible', async ({ page }) => {
    await page.goto('/login');
    
    // Trigger validation errors
    await page.getByTestId('login-button').click();
    
    // Check that error messages have proper ARIA attributes
    const errorMessages = page.locator('[role="alert"], .error-message, .text-red-600');
    const errorCount = await errorMessages.count();
    
    if (errorCount > 0) {
      const firstError = errorMessages.first();
      await expect(firstError).toBeVisible();
      
      // Error should be announced to screen readers
      const role = await firstError.getAttribute('role');
      const ariaLive = await firstError.getAttribute('aria-live');
      
      expect(role === 'alert' || ariaLive === 'polite' || ariaLive === 'assertive').toBeTruthy();
    }
  });

  test('form validation should be accessible', async ({ page }) => {
    await page.goto('/login');
    
    const emailInput = page.getByTestId('email-input');
    const passwordInput = page.getByTestId('password-input');
    
    // Check required attributes
    const emailRequired = await emailInput.getAttribute('required');
    const passwordRequired = await passwordInput.getAttribute('required');
    
    expect(emailRequired).not.toBeNull();
    expect(passwordRequired).not.toBeNull();
    
    // Check ARIA attributes for validation
    await page.getByTestId('email-input').fill('invalid-email');
    await page.getByTestId('login-button').click();
    
    // Should have aria-invalid or aria-describedby for error association
    const emailAriaInvalid = await emailInput.getAttribute('aria-invalid');
    const emailAriaDescribedBy = await emailInput.getAttribute('aria-describedby');
    
    expect(emailAriaInvalid === 'true' || emailAriaDescribedBy).toBeTruthy();
  });

  test('mobile accessibility should work', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/login');
    
    // Check that touch targets are large enough (minimum 44px)
    const buttons = page.getByRole('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      if (await button.isVisible()) {
        const boundingBox = await button.boundingBox();
        if (boundingBox) {
          expect(boundingBox.height).toBeGreaterThanOrEqual(44);
          expect(boundingBox.width).toBeGreaterThanOrEqual(44);
        }
      }
    }
    
    // Check that form inputs are large enough
    const inputs = page.locator('input[type="email"], input[type="password"]');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      if (await input.isVisible()) {
        const boundingBox = await input.boundingBox();
        if (boundingBox) {
          expect(boundingBox.height).toBeGreaterThanOrEqual(44);
        }
      }
    }
  });

  test('semantic HTML structure should be correct', async ({ page }) => {
    await page.goto('/login');
    
    // Check for proper heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toHaveCount({ min: 1, max: 1 }); // Should have exactly one h1
    
    // Check for proper form structure
    const form = page.locator('form');
    await expect(form).toBeAttached();
    
    // Check for proper button types
    const submitButtons = page.locator('button[type="submit"]');
    const submitButtonCount = await submitButtons.count();
    expect(submitButtonCount).toBeGreaterThanOrEqual(1);
    
    // Check for proper input types
    const emailInput = page.locator('input[type="email"]');
    const passwordInput = page.locator('input[type="password"]');
    
    await expect(emailInput).toBeAttached();
    await expect(passwordInput).toBeAttached();
  });

  test('skip links should work for keyboard users', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Check for skip links (usually hidden until focused)
    const skipLinks = page.locator('a[href^="#"], [data-testid="skip-link"]');
    const skipLinkCount = await skipLinks.count();
    
    if (skipLinkCount > 0) {
      // Focus the first skip link
      await skipLinks.first().focus();
      await expect(skipLinks.first()).toBeFocused();
      
      // Skip link should be visible when focused
      await expect(skipLinks.first()).toBeVisible();
    }
  });
});
