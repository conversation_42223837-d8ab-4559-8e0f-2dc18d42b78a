import { test, expect } from '@playwright/test';

test.describe('Dashboard Overview Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Set up authenticated state
    await page.addInitScript(() => {
      localStorage.setItem('access_token', 'valid_token');
      localStorage.setItem('refresh_token', 'valid_refresh_token');
    });

    // Mock dashboard API responses
    await page.route('**/dashboard/overview', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: '<PERSON>',
            email: '<EMAIL>'
          },
          statistics: {
            total_certifications: 5,
            in_progress: 2,
            total_paths: 3,
            completion_percentage: 65.5
          },
          recent_activity: [
            {
              id: 1,
              certification_name: 'AWS Solutions Architect',
              status: 'in_progress',
              updated_at: '2023-12-01T10:00:00Z'
            },
            {
              id: 2,
              certification_name: 'CISSP',
              status: 'completed',
              updated_at: '2023-11-28T15:30:00Z'
            }
          ],
          learning_paths: [
            {
              id: 1,
              name: 'Cloud Architecture Path',
              description: 'Master cloud architecture with AWS',
              progress: 75,
              created_at: '2023-10-01T00:00:00Z'
            },
            {
              id: 2,
              name: 'Cybersecurity Fundamentals',
              description: 'Build strong security foundations',
              progress: 45,
              created_at: '2023-09-15T00:00:00Z'
            }
          ],
          recommended_certifications: [
            {
              id: 1,
              name: 'AWS DevOps Engineer',
              provider: 'Amazon',
              difficulty: 'intermediate',
              estimated_hours: 120
            },
            {
              id: 2,
              name: 'Azure Security Engineer',
              provider: 'Microsoft',
              difficulty: 'advanced',
              estimated_hours: 150
            }
          ]
        })
      });
    });

    await page.route('**/dashboard/quick-stats', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          completed_this_month: 2,
          study_streak_days: 7,
          next_exam_in_days: 15,
          total_study_hours: 45
        })
      });
    });

    // Navigate to dashboard
    await page.goto('/dashboard');
  });

  test('should display dashboard overview with all sections', async ({ page }) => {
    // Verify welcome section
    await expect(page.getByText('Welcome back, John Doe!')).toBeVisible();
    await expect(page.getByText('Continue your certification journey')).toBeVisible();

    // Verify quick stats cards
    await expect(page.getByText('Completed This Month')).toBeVisible();
    await expect(page.getByText('2', { exact: true })).toBeVisible();
    await expect(page.getByText('Study Streak')).toBeVisible();
    await expect(page.getByText('7 days')).toBeVisible();
    await expect(page.getByText('Next Exam')).toBeVisible();
    await expect(page.getByText('15 days')).toBeVisible();
    await expect(page.getByText('Total Study Hours')).toBeVisible();
    await expect(page.getByText('45', { exact: true })).toBeVisible();

    // Verify learning paths section
    await expect(page.getByText('Your Learning Paths')).toBeVisible();
    await expect(page.getByText('Cloud Architecture Path')).toBeVisible();
    await expect(page.getByText('Cybersecurity Fundamentals')).toBeVisible();

    // Verify recent activity section
    await expect(page.getByText('Recent Activity')).toBeVisible();
    await expect(page.getByText('AWS Solutions Architect')).toBeVisible();
    await expect(page.getByText('CISSP')).toBeVisible();

    // Verify recommended certifications section
    await expect(page.getByText('Recommended Certifications')).toBeVisible();
    await expect(page.getByText('AWS DevOps Engineer')).toBeVisible();
    await expect(page.getByText('Azure Security Engineer')).toBeVisible();
  });

  test('should display progress bars correctly', async ({ page }) => {
    // Check learning path progress bars
    const progressBars = page.locator('.bg-blue-600');
    await expect(progressBars).toHaveCount(2); // Two learning paths

    // Verify progress percentages are displayed
    await expect(page.getByText('75%')).toBeVisible();
    await expect(page.getByText('45%')).toBeVisible();
  });

  test('should display quick actions', async ({ page }) => {
    await expect(page.locator('text=Quick Actions')).toBeVisible();
    await expect(page.locator('text=Explore Certifications')).toBeVisible();
    await expect(page.locator('text=Calculate Costs')).toBeVisible();
    await expect(page.locator('text=Plan Study Time')).toBeVisible();
  });

  test('should navigate to certification explorer when clicking quick action', async ({ page }) => {
    await page.click('text=Explore Certifications');
    await expect(page).toHaveURL('/certifications');
  });

  test('should display recent activity', async ({ page }) => {
    await expect(page.locator('text=Recent Activity')).toBeVisible();
    
    // Check for activity items
    const activityItems = page.locator('[data-testid="activity-item"]');
    await expect(activityItems.first()).toBeVisible();
  });

  test('should display system status', async ({ page }) => {
    await expect(page.locator('text=System Status')).toBeVisible();
    await expect(page.locator('text=API Status')).toBeVisible();
    await expect(page.locator('text=Database')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that the layout adapts to mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="stats-grid"]')).toBeVisible();
  });

  test('should handle loading state', async ({ page }) => {
    // Intercept API calls to simulate slow loading
    await page.route('**/api/v1/certifications/', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });

    await page.goto('/');
    
    // Check for loading skeleton
    await expect(page.locator('.animate-pulse')).toBeVisible();
  });

  test('should handle error state', async ({ page }) => {
    // Intercept API calls to simulate error
    await page.route('**/api/v1/certifications/', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });

    await page.goto('/');
    
    // Check for error message
    await expect(page.locator('text=Error Loading Dashboard')).toBeVisible();
  });

  test('should open notifications panel', async ({ page }) => {
    // Mock notifications API
    await page.route('**/dashboard/notifications*', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          notifications: [
            {
              id: 1,
              type: 'achievement',
              title: 'Certification Completed!',
              message: 'Congratulations on completing AWS Solutions Architect',
              read: false,
              created_at: '2023-12-01T10:00:00Z'
            }
          ],
          total_count: 1,
          unread_count: 1
        })
      });
    });

    // Click notifications button
    await page.click('[data-testid="notifications-button"]');

    // Verify panel opens
    await expect(page.getByTestId('notification-panel')).toBeVisible();
    await expect(page.getByText('Notifications')).toBeVisible();
    await expect(page.getByText('1 new')).toBeVisible();
  });

  test('should handle quick actions', async ({ page }) => {
    // Mock quick action API
    await page.route('**/dashboard/quick-action', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Action completed successfully'
        })
      });
    });

    // Click add to path button on a certification
    await page.click('[data-testid="add-to-path-1"]');

    // Verify modal opens
    await expect(page.getByTestId('quick-action-modal')).toBeVisible();
    await expect(page.getByText('Add to Learning Path')).toBeVisible();
  });
});
