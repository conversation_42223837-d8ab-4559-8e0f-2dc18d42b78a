import { test, expect } from '@playwright/test';

test.describe('Login Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
  });

  test('should display login form', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /sign in to your account/i })).toBeVisible();
    await expect(page.getByTestId('email-input')).toBeVisible();
    await expect(page.getByTestId('password-input')).toBeVisible();
    await expect(page.getByTestId('login-button')).toBeVisible();
  });

  test('should show validation errors for empty fields', async ({ page }) => {
    await page.getByTestId('login-button').click();
    
    await expect(page.getByText('Email is required')).toBeVisible();
    await expect(page.getByText('Password is required')).toBeVisible();
  });

  test('should show validation error for invalid email', async ({ page }) => {
    await page.getByTestId('email-input').fill('invalid-email');
    await page.getByTestId('password-input').fill('password123');
    
    await expect(page.getByText('Please enter a valid email address')).toBeVisible();
  });

  test('should show validation error for short password', async ({ page }) => {
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('short');
    
    await expect(page.getByText('Password must be at least 8 characters')).toBeVisible();
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('password123');
    await page.getByTestId('login-button').click();

    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.getByRole('heading', { name: /dashboard/i })).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('wrongpassword');
    await page.getByTestId('login-button').click();

    await expect(page.getByTestId('error-message')).toBeVisible();
    await expect(page.getByText('Invalid email or password')).toBeVisible();
  });

  test('should toggle password visibility', async ({ page }) => {
    const passwordInput = page.getByTestId('password-input');
    await passwordInput.fill('password123');

    // Password should be hidden by default
    await expect(passwordInput).toHaveAttribute('type', 'password');

    // Click the toggle button
    await page.getByRole('button', { name: /show password/i }).click();
    await expect(passwordInput).toHaveAttribute('type', 'text');

    // Click again to hide
    await page.getByRole('button', { name: /hide password/i }).click();
    await expect(passwordInput).toHaveAttribute('type', 'password');
  });

  test('should remember me checkbox work', async ({ page }) => {
    const checkbox = page.getByTestId('remember-me-checkbox');
    
    // Should be unchecked by default
    await expect(checkbox).not.toBeChecked();
    
    // Click to check
    await checkbox.click();
    await expect(checkbox).toBeChecked();
  });

  test('should navigate to forgot password page', async ({ page }) => {
    await page.getByTestId('forgot-password-link').click();
    await expect(page).toHaveURL('/forgot-password');
  });

  test('should show loading state during login', async ({ page }) => {
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('password123');
    
    const loginButton = page.getByTestId('login-button');
    await loginButton.click();

    // Should show loading state
    await expect(loginButton).toBeDisabled();
    await expect(loginButton).toContainText('Signing in...');
  });

  test('should be accessible', async ({ page }) => {
    // Check for proper ARIA labels and roles
    await expect(page.getByRole('textbox', { name: /email address/i })).toBeVisible();
    await expect(page.getByLabelText(/password/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();
    
    // Check keyboard navigation
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('email-input')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('password-input')).toBeFocused();
  });

  test('should handle form submission with Enter key', async ({ page }) => {
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('password123');
    
    // Press Enter to submit form
    await page.getByTestId('password-input').press('Enter');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
  });

  test('should clear error message when user starts typing', async ({ page }) => {
    // First, trigger an error
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('wrongpassword');
    await page.getByTestId('login-button').click();
    
    await expect(page.getByTestId('error-message')).toBeVisible();
    
    // Start typing in email field
    await page.getByTestId('email-input').fill('<EMAIL>');
    
    // Error should be cleared (this depends on implementation)
    await expect(page.getByTestId('error-message')).not.toBeVisible();
  });

  test('should maintain form state during validation', async ({ page }) => {
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('short');
    await page.getByTestId('remember-me-checkbox').check();
    
    await page.getByTestId('login-button').click();
    
    // Form should show validation error but maintain values
    await expect(page.getByText('Password must be at least 8 characters')).toBeVisible();
    await expect(page.getByTestId('email-input')).toHaveValue('<EMAIL>');
    await expect(page.getByTestId('password-input')).toHaveValue('short');
    await expect(page.getByTestId('remember-me-checkbox')).toBeChecked();
  });

  test('should have proper focus management', async ({ page }) => {
    // When page loads, first focusable element should be email input
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('email-input')).toBeFocused();
    
    // After validation error, focus should remain on the problematic field
    await page.getByTestId('login-button').click();
    await expect(page.getByTestId('email-input')).toBeFocused();
  });

  test('should work on mobile viewport', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await expect(page.getByRole('heading', { name: /sign in to your account/i })).toBeVisible();
    await expect(page.getByTestId('email-input')).toBeVisible();
    await expect(page.getByTestId('password-input')).toBeVisible();
    await expect(page.getByTestId('login-button')).toBeVisible();
    
    // Form should be usable on mobile
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('password123');
    await page.getByTestId('login-button').click();
    
    await expect(page).toHaveURL('/dashboard');
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Simulate network failure
    await page.route('**/api/auth/login', route => route.abort());
    
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('password123');
    await page.getByTestId('login-button').click();
    
    // Should show appropriate error message
    await expect(page.getByText(/network error/i)).toBeVisible();
  });
});
