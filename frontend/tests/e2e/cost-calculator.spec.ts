import { test, expect } from '@playwright/test';

test.describe('Cost Calculator', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/cost-calculator');
  });

  test('should display cost calculator title', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Cost Calculator');
    await expect(page.locator('text=Calculate the total cost of your certification journey')).toBeVisible();
  });

  test('should display empty state initially', async ({ page }) => {
    await expect(page.locator('text=No certifications selected')).toBeVisible();
    await expect(page.locator('text=Add certifications to start calculating costs')).toBeVisible();
    await expect(page.locator('text=Add Your First Certification')).toBeVisible();
  });

  test('should display cost summary with zero values initially', async ({ page }) => {
    await expect(page.locator('text=Cost Summary')).toBeVisible();
    await expect(page.locator('text=Exam Costs')).toBeVisible();
    await expect(page.locator('text=Study Materials')).toBeVisible();
    await expect(page.locator('text=Training Courses')).toBeVisible();
    await expect(page.locator('text=Total Cost')).toBeVisible();
    
    // Check that all costs are initially $0
    const costElements = page.locator('text=/\\$0/');
    await expect(costElements.first()).toBeVisible();
  });

  test('should open add certification modal', async ({ page }) => {
    await page.click('text=Add Certification');
    
    // Check that modal is open
    await expect(page.locator('text=Add Certification')).toBeVisible();
    await expect(page.locator('input[placeholder="Search certifications..."]')).toBeVisible();
    await expect(page.locator('text=Cancel')).toBeVisible();
  });

  test('should search and add certification', async ({ page }) => {
    // Open modal
    await page.click('text=Add Certification');
    
    // Wait for certifications to load in modal
    await page.waitForSelector('[data-testid="certification-option"]', { timeout: 10000 });
    
    // Search for a certification
    await page.fill('input[placeholder="Search certifications..."]', 'CISSP');
    await page.waitForTimeout(500);
    
    // Click on first result
    const firstResult = page.locator('[data-testid="certification-option"]').first();
    await firstResult.click();
    
    // Check that certification was added
    await expect(page.locator('[data-testid="certification-item"]')).toBeVisible();
    await expect(page.locator('text=CISSP')).toBeVisible();
  });

  test('should calculate costs when certification is added', async ({ page }) => {
    // Mock API response for adding certification
    await page.route('**/api/v1/certifications/', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          certifications: [
            {
              id: 1,
              name: 'CISSP',
              cost: 749,
              domain: 'Security Management',
              level: 'Advanced',
              difficulty: 4
            }
          ]
        })
      });
    });

    await page.goto('/cost-calculator');
    
    // Add certification
    await page.click('text=Add Certification');
    await page.waitForSelector('[data-testid="certification-option"]', { timeout: 5000 });
    await page.locator('[data-testid="certification-option"]').first().click();
    
    // Check that costs are updated
    await expect(page.locator('text=/\\$749/')).toBeVisible(); // Exam cost
    await expect(page.locator('text=/Total Cost/')).toBeVisible();
  });

  test('should toggle study materials option', async ({ page }) => {
    // Add a certification first (mock the process)
    await page.evaluate(() => {
      // Simulate adding a certification to the state
      window.localStorage.setItem('test-certification', JSON.stringify({
        id: 1,
        name: 'Test Cert',
        cost: 500
      }));
    });

    // Reload to pick up the test data
    await page.reload();
    
    // If we have a certification item, test the study materials toggle
    const studyMaterialsCheckbox = page.locator('input[type="checkbox"]').first();
    if (await studyMaterialsCheckbox.isVisible()) {
      await studyMaterialsCheckbox.click();
      
      // Check that the cost input appears/disappears
      const costInput = page.locator('input[type="number"]').first();
      await expect(costInput).toBeVisible();
    }
  });

  test('should remove certification', async ({ page }) => {
    // Mock having a certification
    await page.route('**/api/v1/certifications/', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          certifications: [
            {
              id: 1,
              name: 'Test Certification',
              cost: 500,
              domain: 'Security',
              level: 'Intermediate',
              difficulty: 3
            }
          ]
        })
      });
    });

    await page.goto('/cost-calculator');
    
    // Add certification
    await page.click('text=Add Certification');
    await page.waitForSelector('[data-testid="certification-option"]', { timeout: 5000 });
    await page.locator('[data-testid="certification-option"]').first().click();
    
    // Remove certification
    const removeButton = page.locator('[data-testid="remove-certification"]');
    if (await removeButton.isVisible()) {
      await removeButton.click();
      
      // Check that certification is removed
      await expect(page.locator('text=No certifications selected')).toBeVisible();
    }
  });

  test('should save calculation', async ({ page }) => {
    // Mock successful save
    await page.route('**/api/v1/cost-calculator/calculations', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });

    // Mock having certifications
    await page.route('**/api/v1/certifications/', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          certifications: [
            {
              id: 1,
              name: 'Test Certification',
              cost: 500,
              domain: 'Security',
              level: 'Intermediate',
              difficulty: 3
            }
          ]
        })
      });
    });

    await page.goto('/cost-calculator');
    
    // Add certification
    await page.click('text=Add Certification');
    await page.waitForSelector('[data-testid="certification-option"]', { timeout: 5000 });
    await page.locator('[data-testid="certification-option"]').first().click();
    
    // Save calculation
    const saveButton = page.locator('text=Save Calculation');
    if (await saveButton.isVisible()) {
      await saveButton.click();
      
      // Check for success message
      await expect(page.locator('text=Calculation saved successfully!')).toBeVisible();
    }
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that the layout adapts to mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('text=Add Certification')).toBeVisible();
    await expect(page.locator('text=Cost Summary')).toBeVisible();
  });

  test('should handle loading state', async ({ page }) => {
    // Intercept API calls to simulate slow loading
    await page.route('**/api/v1/certifications/', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });

    await page.goto('/cost-calculator');
    
    // Check for loading skeleton
    await expect(page.locator('.animate-pulse')).toBeVisible();
  });

  test('should close modal when clicking cancel', async ({ page }) => {
    // Open modal
    await page.click('text=Add Certification');
    
    // Check that modal is open
    await expect(page.locator('text=Add Certification')).toBeVisible();
    
    // Click cancel
    await page.click('text=Cancel');
    
    // Check that modal is closed
    await expect(page.locator('input[placeholder="Search certifications..."]')).not.toBeVisible();
  });
});
