import { test, expect } from '@playwright/test';

test.describe('User Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
  });

  test('should display login form with all required elements', async ({ page }) => {
    // Verify form elements are present
    await expect(page.getByTestId('email-input')).toBeVisible();
    await expect(page.getByTestId('password-input')).toBeVisible();
    await expect(page.getByTestId('remember-me-checkbox')).toBeVisible();
    await expect(page.getByTestId('login-button')).toBeVisible();
    await expect(page.getByTestId('forgot-password-link')).toBeVisible();

    // Verify form labels and placeholders
    await expect(page.getByText('Email address')).toBeVisible();
    await expect(page.getByText('Password')).toBeVisible();
    await expect(page.getByText('Remember me')).toBeVisible();
    await expect(page.getByPlaceholder('Enter your email')).toBeVisible();
    await expect(page.getByPlaceholder('Enter your password')).toBeVisible();
  });

  test('should validate email format', async ({ page }) => {
    // Enter invalid email
    await page.fill('[data-testid="email-input"]', 'invalid-email');
    await page.blur('[data-testid="email-input"]');

    // Verify error message
    await expect(page.getByText(/please enter a valid email/i)).toBeVisible();

    // Enter valid email
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.blur('[data-testid="email-input"]');

    // Verify error message disappears
    await expect(page.getByText(/please enter a valid email/i)).not.toBeVisible();
  });

  test('should validate password requirement', async ({ page }) => {
    // Try to submit without password
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.click('[data-testid="login-button"]');

    // Verify password required error
    await expect(page.getByText(/password is required/i)).toBeVisible();

    // Enter password
    await page.fill('[data-testid="password-input"]', 'password123');

    // Verify error disappears
    await expect(page.getByText(/password is required/i)).not.toBeVisible();
  });

  test('should toggle password visibility', async ({ page }) => {
    // Enter password
    await page.fill('[data-testid="password-input"]', 'secretpassword');

    // Verify password is hidden by default
    await expect(page.getByTestId('password-input')).toHaveAttribute('type', 'password');

    // Click toggle button
    await page.click('[data-testid="password-toggle-button"]');

    // Verify password is now visible
    await expect(page.getByTestId('password-input')).toHaveAttribute('type', 'text');

    // Click toggle button again
    await page.click('[data-testid="password-toggle-button"]');

    // Verify password is hidden again
    await expect(page.getByTestId('password-input')).toHaveAttribute('type', 'password');
  });

  test('should handle successful login', async ({ page }) => {
    // Mock successful login response
    await page.route('**/auth/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          accessToken: 'mock_access_token',
          refreshToken: 'mock_refresh_token',
          expiresIn: 3600,
          user: {
            id: 'user123',
            email: '<EMAIL>',
            firstName: 'Demo',
            lastName: 'User'
          }
        })
      });
    });

    // Fill login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.check('[data-testid="remember-me-checkbox"]');

    // Submit form
    await page.click('[data-testid="login-button"]');

    // Verify loading state
    await expect(page.getByText(/signing in/i)).toBeVisible();

    // Wait for navigation to dashboard
    await expect(page).toHaveURL('/dashboard');

    // Verify tokens are stored
    const accessToken = await page.evaluate(() => localStorage.getItem('access_token'));
    const refreshToken = await page.evaluate(() => localStorage.getItem('refresh_token'));
    
    expect(accessToken).toBe('mock_access_token');
    expect(refreshToken).toBe('mock_refresh_token');
  });

  test('should handle login errors', async ({ page }) => {
    // Mock login error response
    await page.route('**/auth/login', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          detail: 'Invalid email or password'
        })
      });
    });

    // Fill and submit form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');

    // Verify error notification
    await expect(page.getByText(/login failed/i)).toBeVisible();
    await expect(page.getByText(/invalid email or password/i)).toBeVisible();

    // Verify form remains accessible
    await expect(page.getByTestId('email-input')).toBeFocused();
  });

  test('should open and close forgot password modal', async ({ page }) => {
    // Click forgot password link
    await page.click('[data-testid="forgot-password-link"]');

    // Verify modal opens
    await expect(page.getByTestId('password-reset-modal')).toBeVisible();
    await expect(page.getByText('Reset Password')).toBeVisible();

    // Close modal with X button
    await page.click('[data-testid="close-reset-modal"]');

    // Verify modal closes
    await expect(page.getByTestId('password-reset-modal')).not.toBeVisible();

    // Open modal again
    await page.click('[data-testid="forgot-password-link"]');
    await expect(page.getByTestId('password-reset-modal')).toBeVisible();

    // Close modal with cancel button
    await page.click('[data-testid="cancel-reset-button"]');
    await expect(page.getByTestId('password-reset-modal')).not.toBeVisible();
  });

  test('should handle password reset flow', async ({ page }) => {
    // Mock password reset success
    await page.route('**/auth/forgot-password', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Password reset email sent successfully'
        })
      });
    });

    // Open forgot password modal
    await page.click('[data-testid="forgot-password-link"]');

    // Fill email and submit
    await page.fill('[data-testid="reset-email-input"]', '<EMAIL>');
    await page.click('[data-testid="send-reset-button"]');

    // Verify success state
    await expect(page.getByText(/reset email sent/i)).toBeVisible();
    await expect(page.getByText(/<EMAIL>/)).toBeVisible();

    // Verify resend functionality with cooldown
    await expect(page.getByTestId('resend-reset-button')).toBeDisabled();
    await expect(page.getByText(/resend in \d+s/i)).toBeVisible();
  });

  test('should handle password reset errors', async ({ page }) => {
    // Mock password reset error
    await page.route('**/auth/forgot-password', route => {
      route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          detail: 'Email not found'
        })
      });
    });

    // Open modal and submit
    await page.click('[data-testid="forgot-password-link"]');
    await page.fill('[data-testid="reset-email-input"]', '<EMAIL>');
    await page.click('[data-testid="send-reset-button"]');

    // Verify error notification
    await expect(page.getByText(/reset failed/i)).toBeVisible();
    await expect(page.getByText(/email not found/i)).toBeVisible();
  });

  test('should be accessible via keyboard navigation', async ({ page }) => {
    // Test tab order
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('email-input')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('password-input')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('password-toggle-button')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('remember-me-checkbox')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('forgot-password-link')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('login-button')).toBeFocused();

    // Test form submission with Enter key
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.keyboard.press('Enter');

    // Should trigger form submission
    await expect(page.getByTestId('login-button')).toHaveAttribute('disabled');
  });

  test('should work on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Verify form is still accessible
    await expect(page.getByTestId('email-input')).toBeVisible();
    await expect(page.getByTestId('password-input')).toBeVisible();
    await expect(page.getByTestId('login-button')).toBeVisible();

    // Test form submission on mobile
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'mobilepass123');

    // Verify button is still clickable
    await expect(page.getByTestId('login-button')).toBeEnabled();

    // Test password toggle on mobile
    await page.click('[data-testid="password-toggle-button"]');
    await expect(page.getByTestId('password-input')).toHaveAttribute('type', 'text');
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Mock network error
    await page.route('**/auth/login', route => {
      route.abort();
    });

    // Fill and submit form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');

    // Verify error handling
    await expect(page.getByText(/login failed/i)).toBeVisible();
    await expect(page.getByText(/network error|failed to fetch/i)).toBeVisible();
  });

  test('should remember user preference', async ({ page }) => {
    // Check remember me
    await page.check('[data-testid="remember-me-checkbox"]');

    // Verify checkbox is checked
    await expect(page.getByTestId('remember-me-checkbox')).toBeChecked();

    // Mock successful login with remember me
    await page.route('**/auth/login', route => {
      const requestBody = route.request().postDataJSON();
      expect(requestBody.rememberMe).toBe(true);
      
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          accessToken: 'mock_access_token',
          refreshToken: 'mock_refresh_token',
          expiresIn: 3600
        })
      });
    });

    // Submit form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');

    // Verify remember me was sent in request
    await expect(page).toHaveURL('/dashboard');
  });

  test('should handle token refresh automatically', async ({ page }) => {
    // Set up initial authentication with expired token
    await page.addInitScript(() => {
      localStorage.setItem('access_token', 'expired_token');
      localStorage.setItem('refresh_token', 'valid_refresh_token');
    });

    // Mock token refresh
    await page.route('**/auth/refresh', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          accessToken: 'new_access_token',
          expiresIn: 3600
        })
      });
    });

    // Mock protected API call that triggers refresh
    await page.route('**/dashboard/overview', route => {
      const authHeader = route.request().headers()['authorization'];
      if (authHeader === 'Bearer new_access_token') {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ data: 'success' })
        });
      } else {
        route.fulfill({ status: 401 });
      }
    });

    await page.goto('/dashboard');

    // Verify new token is stored
    const newToken = await page.evaluate(() => localStorage.getItem('access_token'));
    expect(newToken).toBe('new_access_token');
  });

  test('should logout and clear session', async ({ page }) => {
    // Set up authenticated state
    await page.addInitScript(() => {
      localStorage.setItem('access_token', 'valid_token');
      localStorage.setItem('refresh_token', 'valid_refresh_token');
    });

    // Mock logout endpoint
    await page.route('**/auth/logout', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, message: 'Logged out successfully' })
      });
    });

    await page.goto('/dashboard');

    // Click logout button (assuming it exists in dashboard)
    await page.click('[data-testid="logout-button"]');

    // Verify redirect to login
    await expect(page).toHaveURL('/login');

    // Verify tokens are cleared
    const accessToken = await page.evaluate(() => localStorage.getItem('access_token'));
    const refreshToken = await page.evaluate(() => localStorage.getItem('refresh_token'));

    expect(accessToken).toBeNull();
    expect(refreshToken).toBeNull();
  });
});
