import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication for protected pages
    await page.addInitScript(() => {
      localStorage.setItem('access_token', 'mock_token');
    });
  });

  test('login page should load within performance budget', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 2.5 seconds (LCP target)
    expect(loadTime).toBeLessThan(2500);
    
    // Check that main content is visible
    await expect(page.getByRole('heading', { name: /sign in to your account/i })).toBeVisible();
  });

  test('dashboard should load within performance budget', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 3 seconds for dashboard (more complex page)
    expect(loadTime).toBeLessThan(3000);
    
    // Check that main content is visible
    await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();
  });

  test('navigation between pages should be fast', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    const startTime = Date.now();
    
    // Navigate to certifications
    await page.getByRole('link', { name: /certifications/i }).click();
    await page.waitForLoadState('networkidle');
    
    const navigationTime = Date.now() - startTime;
    
    // Navigation should be fast (under 1 second for SPA)
    expect(navigationTime).toBeLessThan(1000);
    
    // Check that new page content is visible
    await expect(page.getByRole('heading', { name: /certification explorer/i })).toBeVisible();
  });

  test('form interactions should be responsive', async ({ page }) => {
    await page.goto('/login');
    
    const emailInput = page.getByTestId('email-input');
    
    // Measure input response time
    const startTime = Date.now();
    
    await emailInput.fill('<EMAIL>');
    
    const inputTime = Date.now() - startTime;
    
    // Input should be responsive (under 100ms)
    expect(inputTime).toBeLessThan(100);
    
    // Check that input value is updated
    await expect(emailInput).toHaveValue('<EMAIL>');
  });

  test('search functionality should be performant', async ({ page }) => {
    await page.goto('/certifications');
    await page.waitForLoadState('networkidle');
    
    const searchInput = page.getByPlaceholder(/search certifications/i);
    
    // Measure search response time
    const startTime = Date.now();
    
    await searchInput.fill('AWS');
    
    // Wait for search results to update
    await page.waitForTimeout(500); // Allow for debouncing
    
    const searchTime = Date.now() - startTime;
    
    // Search should complete within 1 second
    expect(searchTime).toBeLessThan(1000);
    
    // Check that results are filtered
    const certificationCards = page.locator('[data-testid="certification-card"]');
    const cardCount = await certificationCards.count();
    
    if (cardCount > 0) {
      await expect(certificationCards.first()).toContainText('AWS');
    }
  });

  test('image loading should be optimized', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Check for images with proper loading attributes
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      if (await img.isVisible()) {
        // Check for lazy loading or proper optimization
        const loading = await img.getAttribute('loading');
        const src = await img.getAttribute('src');
        
        // Images should either be lazy loaded or be critical images
        if (src && !src.startsWith('data:')) {
          // Non-critical images should be lazy loaded
          const isAboveFold = await img.evaluate(el => {
            const rect = el.getBoundingClientRect();
            return rect.top < window.innerHeight;
          });
          
          if (!isAboveFold) {
            expect(loading).toBe('lazy');
          }
        }
      }
    }
  });

  test('bundle size should be reasonable', async ({ page }) => {
    // Monitor network requests during page load
    const requests: any[] = [];
    
    page.on('request', request => {
      if (request.resourceType() === 'script' || request.resourceType() === 'stylesheet') {
        requests.push({
          url: request.url(),
          type: request.resourceType()
        });
      }
    });
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Check that we're not loading too many resources
    const scriptRequests = requests.filter(r => r.type === 'script');
    const styleRequests = requests.filter(r => r.type === 'stylesheet');
    
    // Should have reasonable number of script files (code splitting)
    expect(scriptRequests.length).toBeLessThan(10);
    
    // Should have reasonable number of CSS files
    expect(styleRequests.length).toBeLessThan(5);
  });

  test('memory usage should be stable', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Get initial memory usage
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });
    
    // Navigate between pages multiple times
    for (let i = 0; i < 3; i++) {
      await page.getByRole('link', { name: /certifications/i }).click();
      await page.waitForLoadState('networkidle');
      
      await page.getByRole('link', { name: /dashboard/i }).click();
      await page.waitForLoadState('networkidle');
    }
    
    // Get final memory usage
    const finalMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });
    
    // Memory should not increase dramatically (memory leaks)
    if (initialMemory > 0 && finalMemory > 0) {
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100;
      
      // Memory increase should be reasonable (less than 50%)
      expect(memoryIncreasePercent).toBeLessThan(50);
    }
  });

  test('animations should be smooth', async ({ page }) => {
    await page.goto('/login');
    
    // Test loading animation performance
    const loginButton = page.getByTestId('login-button');
    
    // Fill form and submit to trigger loading state
    await page.getByTestId('email-input').fill('<EMAIL>');
    await page.getByTestId('password-input').fill('password123');
    
    const startTime = Date.now();
    await loginButton.click();
    
    // Check that loading animation appears quickly
    await expect(loginButton).toBeDisabled({ timeout: 100 });
    
    const animationStartTime = Date.now() - startTime;
    
    // Animation should start within 50ms
    expect(animationStartTime).toBeLessThan(50);
  });

  test('large lists should render efficiently', async ({ page }) => {
    await page.goto('/certifications');
    await page.waitForLoadState('networkidle');
    
    const startTime = Date.now();
    
    // Wait for certification cards to render
    const certificationCards = page.locator('[data-testid="certification-card"]');
    await expect(certificationCards.first()).toBeVisible();
    
    const renderTime = Date.now() - startTime;
    
    // Large lists should render within 1 second
    expect(renderTime).toBeLessThan(1000);
    
    // Check that all visible cards are properly rendered
    const cardCount = await certificationCards.count();
    
    if (cardCount > 0) {
      // Check first few cards for proper content
      for (let i = 0; i < Math.min(cardCount, 3); i++) {
        const card = certificationCards.nth(i);
        await expect(card.locator('h3')).toBeVisible(); // Title should be visible
      }
    }
  });

  test('API calls should be optimized', async ({ page }) => {
    const apiCalls: any[] = [];
    
    // Monitor API calls
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        apiCalls.push({
          url: request.url(),
          method: request.method(),
          timestamp: Date.now()
        });
      }
    });
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Should not make excessive API calls
    expect(apiCalls.length).toBeLessThan(10);
    
    // Should not make duplicate calls
    const uniqueUrls = new Set(apiCalls.map(call => call.url));
    const duplicateCalls = apiCalls.length - uniqueUrls.size;
    
    expect(duplicateCalls).toBeLessThan(3); // Allow some reasonable duplication
  });

  test('error handling should not impact performance', async ({ page }) => {
    // Mock API failure
    await page.route('**/dashboard/overview', route => route.abort());
    
    const startTime = Date.now();
    
    await page.goto('/dashboard');
    
    // Page should still load even with API errors
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(5000);
    
    // Should show error state or fallback content
    const hasErrorHandling = await page.getByText(/failed to load/i).isVisible() ||
                            await page.getByRole('button', { name: /refresh/i }).isVisible() ||
                            await page.getByRole('heading', { name: /dashboard/i }).isVisible();
    
    expect(hasErrorHandling).toBeTruthy();
  });

  test('mobile performance should be acceptable', async ({ page }) => {
    // Set mobile viewport and simulate slower network
    await page.setViewportSize({ width: 375, height: 667 });
    
    const startTime = Date.now();
    
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    const mobileLoadTime = Date.now() - startTime;
    
    // Mobile should load within 3 seconds (accounting for slower devices)
    expect(mobileLoadTime).toBeLessThan(3000);
    
    // Check that content is properly optimized for mobile
    await expect(page.getByRole('heading', { name: /sign in to your account/i })).toBeVisible();
    await expect(page.getByTestId('email-input')).toBeVisible();
    await expect(page.getByTestId('password-input')).toBeVisible();
    await expect(page.getByTestId('login-button')).toBeVisible();
  });
});
