import { test, expect } from '@playwright/test';

test.describe('Certification Explorer', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/certifications');
  });

  test('should display certification explorer title', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Certification Explorer');
    await expect(page.locator('text=Discover and explore')).toBeVisible();
  });

  test('should display search and filter controls', async ({ page }) => {
    // Search input
    await expect(page.locator('input[placeholder="Search certifications..."]')).toBeVisible();
    
    // Filter dropdowns
    await expect(page.locator('select').first()).toBeVisible(); // Domain filter
    await expect(page.locator('select').nth(1)).toBeVisible(); // Level filter
    
    // Cost slider
    await expect(page.locator('input[type="range"]')).toBeVisible();
    
    // View mode toggles
    await expect(page.locator('[data-testid="grid-view"]')).toBeVisible();
    await expect(page.locator('[data-testid="list-view"]')).toBeVisible();
  });

  test('should search certifications', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]', { timeout: 10000 });
    
    // Search for a specific certification
    await page.fill('input[placeholder="Search certifications..."]', 'CISSP');
    
    // Wait for search results
    await page.waitForTimeout(500);
    
    // Check that results are filtered
    const certificationCards = page.locator('[data-testid="certification-card"]');
    const firstCard = certificationCards.first();
    await expect(firstCard).toContainText('CISSP');
  });

  test('should filter by domain', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]', { timeout: 10000 });
    
    // Select a domain filter
    await page.selectOption('select >> nth=0', { index: 1 }); // Select first non-empty option
    
    // Wait for filter to apply
    await page.waitForTimeout(500);
    
    // Check that results are filtered
    const resultCount = page.locator('text=/\\d+ of \\d+ certifications/');
    await expect(resultCount).toBeVisible();
  });

  test('should filter by level', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]', { timeout: 10000 });
    
    // Select a level filter
    await page.selectOption('select >> nth=1', 'Entry Level');
    
    // Wait for filter to apply
    await page.waitForTimeout(500);
    
    // Check that all visible cards show Entry Level
    const certificationCards = page.locator('[data-testid="certification-card"]');
    const firstCard = certificationCards.first();
    await expect(firstCard).toContainText('Entry Level');
  });

  test('should adjust cost filter', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]', { timeout: 10000 });
    
    // Adjust cost slider
    const costSlider = page.locator('input[type="range"]');
    await costSlider.fill('1000');
    
    // Wait for filter to apply
    await page.waitForTimeout(500);
    
    // Check that max cost label is updated
    await expect(page.locator('text=Max Cost: $1,000')).toBeVisible();
  });

  test('should switch between grid and list view', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]', { timeout: 10000 });
    
    // Switch to list view
    await page.click('[data-testid="list-view"]');
    
    // Check that layout changes
    await expect(page.locator('[data-testid="certification-list-item"]')).toBeVisible();
    
    // Switch back to grid view
    await page.click('[data-testid="grid-view"]');
    
    // Check that layout changes back
    await expect(page.locator('[data-testid="certification-card"]')).toBeVisible();
  });

  test('should sort certifications', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]', { timeout: 10000 });
    
    // Change sort order
    await page.selectOption('select[data-testid="sort-select"]', 'cost');
    
    // Wait for sort to apply
    await page.waitForTimeout(500);
    
    // Check that certifications are sorted by cost
    const certificationCards = page.locator('[data-testid="certification-card"]');
    await expect(certificationCards.first()).toBeVisible();
  });

  test('should display certification details', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]', { timeout: 10000 });
    
    const firstCard = page.locator('[data-testid="certification-card"]').first();
    
    // Check that card contains required information
    await expect(firstCard.locator('h3')).toBeVisible(); // Title
    await expect(firstCard.locator('text=/Entry Level|Intermediate|Advanced|Expert/')).toBeVisible(); // Level
    await expect(firstCard.locator('text=/Difficulty: \\d+\\/5/')).toBeVisible(); // Difficulty
  });

  test('should handle empty search results', async ({ page }) => {
    // Wait for certifications to load
    await page.waitForSelector('[data-testid="certification-card"]', { timeout: 10000 });
    
    // Search for something that doesn't exist
    await page.fill('input[placeholder="Search certifications..."]', 'NonExistentCertification123');
    
    // Wait for search results
    await page.waitForTimeout(500);
    
    // Check for empty state
    await expect(page.locator('text=No certifications found')).toBeVisible();
    await expect(page.locator('text=Try adjusting your filters')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that the layout adapts to mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('input[placeholder="Search certifications..."]')).toBeVisible();
    
    // Check that filters are still accessible
    await expect(page.locator('select').first()).toBeVisible();
  });

  test('should handle loading state', async ({ page }) => {
    // Intercept API calls to simulate slow loading
    await page.route('**/api/v1/certifications/', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });

    await page.goto('/certifications');
    
    // Check for loading skeleton
    await expect(page.locator('.animate-pulse')).toBeVisible();
  });

  test('should handle error state', async ({ page }) => {
    // Intercept API calls to simulate error
    await page.route('**/api/v1/certifications/', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });

    await page.goto('/certifications');
    
    // Check for error message
    await expect(page.locator('text=Error Loading Certifications')).toBeVisible();
  });
});
