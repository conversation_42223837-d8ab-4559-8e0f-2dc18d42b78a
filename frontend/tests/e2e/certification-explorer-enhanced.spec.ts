import { test, expect } from '@playwright/test';

test.describe('Certification Explorer Enhanced', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.addInitScript(() => {
      localStorage.setItem('access_token', 'mock_token');
    });
    
    // Navigate to certification explorer
    await page.goto('/certifications');
  });

  test('should display certification explorer layout', async ({ page }) => {
    // Check main heading
    await expect(page.getByRole('heading', { name: /certification explorer/i })).toBeVisible();
    
    // Check description
    await expect(page.getByText(/discover and explore certifications/i)).toBeVisible();
    
    // Check filters section
    await expect(page.getByRole('heading', { name: /filters/i })).toBeVisible();
  });

  test('should display filter controls', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Check search input
    await expect(page.getByPlaceholder(/search certifications/i)).toBeVisible();
    
    // Check filter dropdowns
    await expect(page.getByText('All Providers')).toBeVisible();
    await expect(page.getByText('All Levels')).toBeVisible();
    await expect(page.getByText('All Domains')).toBeVisible();
    
    // Check clear filters button
    await expect(page.getByRole('button', { name: /clear filters/i })).toBeVisible();
  });

  test('should filter certifications by search term', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Get initial certification count
    const initialCount = await page.locator('[data-testid="certification-card"]').count();
    
    // Search for AWS
    await page.getByPlaceholder(/search certifications/i).fill('AWS');
    
    // Wait for filtering to complete
    await page.waitForTimeout(500);
    
    // Should show only AWS certifications
    const awsCertifications = page.locator('[data-testid="certification-card"]');
    const awsCount = await awsCertifications.count();
    
    // Verify AWS certifications are shown
    if (awsCount > 0) {
      await expect(awsCertifications.first()).toContainText('AWS');
    }
    
    // Clear search
    await page.getByPlaceholder(/search certifications/i).clear();
    await page.waitForTimeout(500);
    
    // Should show all certifications again
    const finalCount = await page.locator('[data-testid="certification-card"]').count();
    expect(finalCount).toBeGreaterThanOrEqual(awsCount);
  });

  test('should filter certifications by provider', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Select a provider from dropdown
    const providerSelect = page.locator('select').nth(0); // First select is provider
    await providerSelect.selectOption({ index: 1 }); // Select first non-"All" option
    
    await page.waitForTimeout(500);
    
    // Should show filtered results
    const certificationCards = page.locator('[data-testid="certification-card"]');
    const count = await certificationCards.count();
    
    if (count > 0) {
      // All visible certifications should be from the selected provider
      const selectedProvider = await providerSelect.inputValue();
      if (selectedProvider && selectedProvider !== '') {
        await expect(certificationCards.first()).toContainText(selectedProvider);
      }
    }
  });

  test('should filter certifications by difficulty', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Select difficulty level
    const difficultySelect = page.locator('select').nth(1); // Second select is difficulty
    await difficultySelect.selectOption('Intermediate');
    
    await page.waitForTimeout(500);
    
    // Should show only intermediate certifications
    const certificationCards = page.locator('[data-testid="certification-card"]');
    const count = await certificationCards.count();
    
    if (count > 0) {
      // Check that intermediate badge is visible
      await expect(page.getByText('Intermediate')).toBeVisible();
    }
  });

  test('should display certification details correctly', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    const firstCertification = page.locator('[data-testid="certification-card"]').first();
    await expect(firstCertification).toBeVisible();
    
    // Check certification card elements
    await expect(firstCertification.locator('h3')).toBeVisible(); // Title
    await expect(firstCertification.getByText(/study hours/i)).toBeVisible();
    await expect(firstCertification.getByText(/cost/i)).toBeVisible();
    await expect(firstCertification.getByText(/exam code/i)).toBeVisible();
    await expect(firstCertification.getByText(/valid for/i)).toBeVisible();
    
    // Check action buttons
    await expect(firstCertification.getByRole('button', { name: /add to learning path/i })).toBeVisible();
    await expect(firstCertification.getByRole('button', { name: /view details/i })).toBeVisible();
  });

  test('should add certification to learning path', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    const firstCertification = page.locator('[data-testid="certification-card"]').first();
    const addButton = firstCertification.getByRole('button', { name: /add to learning path/i });
    
    await addButton.click();
    
    // Should show success notification
    await expect(page.getByText(/added to learning path/i)).toBeVisible({ timeout: 5000 });
  });

  test('should clear all filters', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Apply some filters
    await page.getByPlaceholder(/search certifications/i).fill('AWS');
    const providerSelect = page.locator('select').nth(0);
    await providerSelect.selectOption({ index: 1 });
    
    await page.waitForTimeout(500);
    
    // Clear all filters
    await page.getByRole('button', { name: /clear filters/i }).click();
    
    // Should reset all filter controls
    await expect(page.getByPlaceholder(/search certifications/i)).toHaveValue('');
    await expect(providerSelect).toHaveValue('');
  });

  test('should show results count', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Should show results count
    await expect(page.getByText(/showing \d+ of \d+ certifications/i)).toBeVisible();
  });

  test('should handle empty search results', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Search for something that doesn't exist
    await page.getByPlaceholder(/search certifications/i).fill('NonexistentCertification123');
    await page.waitForTimeout(500);
    
    // Should show no results message
    await expect(page.getByText(/no certifications found/i)).toBeVisible();
    await expect(page.getByText(/try adjusting your filters/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /clear all filters/i })).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    
    // Check that main elements are still visible
    await expect(page.getByRole('heading', { name: /certification explorer/i })).toBeVisible();
    await expect(page.getByPlaceholder(/search certifications/i)).toBeVisible();
    
    // Check that certification cards adapt to mobile layout
    const certificationCards = page.locator('[data-testid="certification-card"]');
    if (await certificationCards.count() > 0) {
      await expect(certificationCards.first()).toBeVisible();
    }
  });

  test('should handle keyboard navigation', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Test tab navigation through filters
    await page.keyboard.press('Tab');
    await expect(page.getByPlaceholder(/search certifications/i)).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('select').first()).toBeFocused();
    
    // Test search with keyboard
    await page.getByPlaceholder(/search certifications/i).focus();
    await page.keyboard.type('AWS');
    await page.waitForTimeout(500);
    
    // Should filter results
    const awsCertifications = page.locator('[data-testid="certification-card"]');
    if (await awsCertifications.count() > 0) {
      await expect(awsCertifications.first()).toContainText('AWS');
    }
  });

  test('should display domain badges correctly', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    const firstCertification = page.locator('[data-testid="certification-card"]').first();
    
    // Should show domain badges
    const domainBadges = firstCertification.locator('.text-xs'); // Domain badges are typically small
    const badgeCount = await domainBadges.count();
    
    if (badgeCount > 0) {
      await expect(domainBadges.first()).toBeVisible();
    }
  });

  test('should handle loading states', async ({ page }) => {
    // Intercept API calls to simulate slow loading
    await page.route('**/certifications', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.continue();
    });
    
    await page.goto('/certifications');
    
    // Should show loading spinner
    await expect(page.locator('.animate-spin')).toBeVisible();
    
    // Should eventually show content
    await expect(page.getByRole('heading', { name: /certification explorer/i })).toBeVisible({ timeout: 15000 });
  });

  test('should maintain filter state during navigation', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    // Apply a filter
    await page.getByPlaceholder(/search certifications/i).fill('AWS');
    await page.waitForTimeout(500);
    
    // Navigate away and back
    await page.getByRole('link', { name: /dashboard/i }).click();
    await expect(page).toHaveURL(/.*dashboard.*/);
    
    await page.getByRole('link', { name: /certifications/i }).click();
    await expect(page).toHaveURL(/.*certifications.*/);
    
    // Filter should be cleared (this is typical behavior)
    await expect(page.getByPlaceholder(/search certifications/i)).toHaveValue('');
  });

  test('should show difficulty badges with correct colors', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    const certificationCards = page.locator('[data-testid="certification-card"]');
    const cardCount = await certificationCards.count();
    
    if (cardCount > 0) {
      // Check for difficulty badges
      const difficultyBadges = page.locator('[data-testid="difficulty-badge"]');
      
      if (await difficultyBadges.count() > 0) {
        // Should have appropriate styling for different difficulty levels
        await expect(difficultyBadges.first()).toBeVisible();
      }
    }
  });

  test('should handle view details action', async ({ page }) => {
    await page.waitForLoadState('networkidle');
    
    const firstCertification = page.locator('[data-testid="certification-card"]').first();
    const viewDetailsButton = firstCertification.getByRole('button', { name: /view details/i });
    
    if (await viewDetailsButton.isVisible()) {
      await viewDetailsButton.click();
      
      // Should navigate to details page or show modal
      // This depends on the implementation
      // For now, we just verify the button is clickable
      expect(true).toBeTruthy();
    }
  });
});
