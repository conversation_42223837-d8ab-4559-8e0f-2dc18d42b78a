import { test, expect } from '@playwright/test';

test.describe('User Registration Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to registration page
    await page.goto('/register');
  });

  test('should display registration form with all required fields', async ({ page }) => {
    // Verify form elements are present
    await expect(page.getByTestId('email-input')).toBeVisible();
    await expect(page.getByTestId('first-name-input')).toBeVisible();
    await expect(page.getByTestId('last-name-input')).toBeVisible();
    await expect(page.getByTestId('password-input')).toBeVisible();
    await expect(page.getByTestId('confirm-password-input')).toBeVisible();
    await expect(page.getByTestId('terms-checkbox')).toBeVisible();
    await expect(page.getByTestId('register-button')).toBeVisible();

    // Verify form labels
    await expect(page.getByText('Email address')).toBeVisible();
    await expect(page.getByText('First name')).toBeVisible();
    await expect(page.getByText('Last name')).toBeVisible();
    await expect(page.getByText('Password')).toBeVisible();
    await expect(page.getByText('Confirm password')).toBeVisible();
  });

  test('should validate email format', async ({ page }) => {
    // Enter invalid email
    await page.fill('[data-testid="email-input"]', 'invalid-email');
    await page.blur('[data-testid="email-input"]');

    // Verify error message
    await expect(page.getByText(/please enter a valid email/i)).toBeVisible();

    // Enter valid email
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.blur('[data-testid="email-input"]');

    // Verify error message disappears
    await expect(page.getByText(/please enter a valid email/i)).not.toBeVisible();
  });

  test('should validate password strength', async ({ page }) => {
    // Enter weak password
    await page.fill('[data-testid="password-input"]', 'weak');

    // Verify password strength indicator appears
    await expect(page.getByTestId('password-strength-indicator')).toBeVisible();
    await expect(page.getByText(/password too weak/i)).toBeVisible();

    // Enter strong password
    await page.fill('[data-testid="password-input"]', 'StrongPass123!');

    // Verify strength indicator shows strong
    await expect(page.getByText(/strong/i)).toBeVisible();
    await expect(page.getByText(/great password/i)).toBeVisible();
  });

  test('should validate password confirmation', async ({ page }) => {
    // Enter password
    await page.fill('[data-testid="password-input"]', 'StrongPass123!');
    
    // Enter different confirmation
    await page.fill('[data-testid="confirm-password-input"]', 'DifferentPass123!');
    await page.blur('[data-testid="confirm-password-input"]');

    // Verify error message
    await expect(page.getByText(/passwords don't match/i)).toBeVisible();

    // Enter matching confirmation
    await page.fill('[data-testid="confirm-password-input"]', 'StrongPass123!');
    await page.blur('[data-testid="confirm-password-input"]');

    // Verify error message disappears
    await expect(page.getByText(/passwords don't match/i)).not.toBeVisible();
  });

  test('should validate name fields', async ({ page }) => {
    // Test first name validation
    await page.fill('[data-testid="first-name-input"]', 'J');
    await page.blur('[data-testid="first-name-input"]');
    await expect(page.getByText(/first name must be at least 2 characters/i)).toBeVisible();

    // Test invalid characters
    await page.fill('[data-testid="first-name-input"]', 'John123');
    await page.blur('[data-testid="first-name-input"]');
    await expect(page.getByText(/first name contains invalid characters/i)).toBeVisible();

    // Test valid name
    await page.fill('[data-testid="first-name-input"]', 'John');
    await page.blur('[data-testid="first-name-input"]');
    await expect(page.getByText(/first name must be at least 2 characters/i)).not.toBeVisible();
  });

  test('should check email availability', async ({ page }) => {
    // Mock email availability check
    await page.route('**/auth/check-email*', route => {
      const url = new URL(route.request().url());
      const email = url.searchParams.get('email');
      
      if (email === '<EMAIL>') {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            available: false,
            suggestions: ['<EMAIL>', '<EMAIL>']
          })
        });
      } else {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true })
        });
      }
    });

    // Enter existing email
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.blur('[data-testid="email-input"]');

    // Wait for email check
    await page.waitForTimeout(600); // Wait for debounce

    // Verify error message and suggestions
    await expect(page.getByText(/this email is already registered/i)).toBeVisible();
    await expect(page.getByTestId('email-suggestions')).toBeVisible();
    await expect(page.getByText('<EMAIL>')).toBeVisible();

    // Click on suggestion
    await page.click('[data-testid="email-suggestion-0"]');

    // Verify email field is updated
    await expect(page.getByTestId('email-input')).toHaveValue('<EMAIL>');
    await expect(page.getByTestId('email-suggestions')).not.toBeVisible();
  });

  test('should require terms acceptance', async ({ page }) => {
    // Fill all fields except terms
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="first-name-input"]', 'John');
    await page.fill('[data-testid="last-name-input"]', 'Doe');
    await page.fill('[data-testid="password-input"]', 'StrongPass123!');
    await page.fill('[data-testid="confirm-password-input"]', 'StrongPass123!');

    // Try to submit without accepting terms
    await page.click('[data-testid="register-button"]');

    // Verify error message
    await expect(page.getByText(/you must accept the terms/i)).toBeVisible();

    // Accept terms
    await page.check('[data-testid="terms-checkbox"]');

    // Verify error disappears
    await expect(page.getByText(/you must accept the terms/i)).not.toBeVisible();
  });

  test('should open and close terms modal', async ({ page }) => {
    // Click on terms link
    await page.click('text=Terms of Service');

    // Verify modal opens
    await expect(page.getByTestId('terms-modal')).toBeVisible();
    await expect(page.getByText('Terms of Service & Privacy Policy')).toBeVisible();

    // Close modal with X button
    await page.click('[data-testid="close-terms-modal"]');

    // Verify modal closes
    await expect(page.getByTestId('terms-modal')).not.toBeVisible();

    // Open modal again
    await page.click('text=Privacy Policy');
    await expect(page.getByTestId('terms-modal')).toBeVisible();

    // Accept terms from modal
    await page.click('[data-testid="accept-terms-button"]');

    // Verify modal closes and checkbox is checked
    await expect(page.getByTestId('terms-modal')).not.toBeVisible();
    await expect(page.getByTestId('terms-checkbox')).toBeChecked();
  });

  test('should complete successful registration flow', async ({ page }) => {
    // Mock successful registration
    await page.route('**/auth/register', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Registration successful. Please check your email for verification.',
          userId: 'user123'
        })
      });
    });

    // Fill registration form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="first-name-input"]', 'John');
    await page.fill('[data-testid="last-name-input"]', 'Doe');
    await page.fill('[data-testid="password-input"]', 'StrongPass123!');
    await page.fill('[data-testid="confirm-password-input"]', 'StrongPass123!');
    await page.check('[data-testid="terms-checkbox"]');

    // Submit form
    await page.click('[data-testid="register-button"]');

    // Verify loading state
    await expect(page.getByText('Creating Account...')).toBeVisible();

    // Wait for navigation to verification page
    await expect(page).toHaveURL(/.*verify-email.*/);
    
    // Verify success message
    await expect(page.getByText(/verification email sent/i)).toBeVisible();
  });

  test('should handle registration errors', async ({ page }) => {
    // Mock registration error
    await page.route('**/auth/register', route => {
      route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          detail: 'Email already exists'
        })
      });
    });

    // Fill and submit form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="first-name-input"]', 'John');
    await page.fill('[data-testid="last-name-input"]', 'Doe');
    await page.fill('[data-testid="password-input"]', 'StrongPass123!');
    await page.fill('[data-testid="confirm-password-input"]', 'StrongPass123!');
    await page.check('[data-testid="terms-checkbox"]');
    await page.click('[data-testid="register-button"]');

    // Verify error notification
    await expect(page.getByText(/registration failed/i)).toBeVisible();
    await expect(page.getByText(/email already exists/i)).toBeVisible();

    // Verify form remains accessible
    await expect(page.getByTestId('email-input')).toBeFocused();
  });

  test('should be accessible via keyboard navigation', async ({ page }) => {
    // Test tab order
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('email-input')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('first-name-input')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('last-name-input')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('password-input')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('confirm-password-input')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('referral-code-input')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('terms-checkbox')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('marketing-consent-checkbox')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.getByTestId('register-button')).toBeFocused();
  });

  test('should work on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Verify form is still accessible
    await expect(page.getByTestId('email-input')).toBeVisible();
    await expect(page.getByTestId('register-button')).toBeVisible();

    // Test form submission on mobile
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="first-name-input"]', 'Mobile');
    await page.fill('[data-testid="last-name-input"]', 'User');
    await page.fill('[data-testid="password-input"]', 'MobilePass123!');
    await page.fill('[data-testid="confirm-password-input"]', 'MobilePass123!');
    await page.check('[data-testid="terms-checkbox"]');

    // Verify button is still clickable
    await expect(page.getByTestId('register-button')).toBeEnabled();
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Mock network error
    await page.route('**/auth/register', route => {
      route.abort();
    });

    // Fill and submit form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="first-name-input"]', 'John');
    await page.fill('[data-testid="last-name-input"]', 'Doe');
    await page.fill('[data-testid="password-input"]', 'StrongPass123!');
    await page.fill('[data-testid="confirm-password-input"]', 'StrongPass123!');
    await page.check('[data-testid="terms-checkbox"]');
    await page.click('[data-testid="register-button"]');

    // Verify error handling
    await expect(page.getByText(/registration failed/i)).toBeVisible();
    await expect(page.getByText(/an error occurred/i)).toBeVisible();
  });
});
