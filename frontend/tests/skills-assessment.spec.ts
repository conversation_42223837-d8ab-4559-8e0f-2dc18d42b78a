/**
 * Playwright UI Tests for Skills Assessment
 * Feature 1.1: Skills Vector Representation & Scoring - Playwright UI Tests Phase
 */
import { test, expect, Page } from '@playwright/test';

// Test data
const mockSkillsData = {
  domains: [
    'Communication and Network Security',
    'Identity and Access Management (IAM)',
    'Security Architecture and Engineering',
    'Asset Security',
    'Security and Risk Management',
    'Security Assessment and Testing',
    'Software Security',
    'Security Operations'
  ],
  skillLevels: ['None', 'Basic', 'Intermediate', 'Advanced', 'Expert'],
  confidenceLevels: ['Not Confident', 'Somewhat Confident', 'Confident', 'Very Confident']
};

// Helper functions
async function navigateToSkillsAssessment(page: Page) {
  await page.goto('/skills/assessment');
  await expect(page.locator('h1, [data-testid="page-title"]')).toContainText('Skills Assessment');
}

async function selectSkillLevel(page: Page, skillName: string, level: string) {
  const skillSection = page.locator(`[data-testid="skill-${skillName}"], .skill-item`).filter({ hasText: skillName });
  await skillSection.locator(`button:has-text("${level}")`).click();
}

async function selectConfidenceLevel(page: Page, skillName: string, confidence: string) {
  const skillSection = page.locator(`[data-testid="skill-${skillName}"], .skill-item`).filter({ hasText: skillName });
  await skillSection.locator(`button:has-text("${confidence}")`).click();
}

test.describe('Skills Assessment UI Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/v1/skills/domains', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          domains: {
            communication_network_security: {
              name: 'Communication and Network Security',
              skills: ['network_protocols', 'firewall_configuration', 'vpn_technologies']
            },
            identity_access_management: {
              name: 'Identity and Access Management (IAM)',
              skills: ['authentication_mechanisms', 'authorization_rbac', 'privileged_access_management']
            }
          },
          total_domains: 8,
          total_skills: 72
        })
      });
    });

    await page.route('**/api/v1/skills/assess', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          assessment_id: 'assess_1_test',
          user_id: 1,
          skill_scores: [],
          domain_scores: {},
          overall_profile: {
            total_skills_assessed: 3,
            average_skill_level: 0.5,
            strongest_domain: 'communication_network_security',
            certifications_count: 0,
            assessment_completeness: 0.04
          },
          timestamp: new Date().toISOString()
        })
      });
    });
  });

  test('should display skills assessment form with correct structure', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Check main components are present
    await expect(page.locator('[data-testid="skills-assessment"], .skills-assessment')).toBeVisible();
    await expect(page.locator('[data-testid="progress-header"], .progress-header')).toBeVisible();
    await expect(page.locator('[data-testid="domain-assessment"], .domain-assessment')).toBeVisible();
    await expect(page.locator('[data-testid="navigation-controls"], .navigation-controls')).toBeVisible();

    // Check progress indicator
    await expect(page.locator('text=Domain 1 of')).toBeVisible();
    await expect(page.locator('[data-testid="progress-bar"], .progress-bar, [role="progressbar"]')).toBeVisible();

    // Check domain title is displayed
    await expect(page.locator('h2, h3, [data-testid="domain-title"]')).toContainText('Communication and Network Security');
  });

  test('should allow skill level selection', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Find first skill and select intermediate level
    const firstSkill = page.locator('.skill-item, [data-testid^="skill-"]').first();
    await expect(firstSkill).toBeVisible();

    // Click on intermediate level
    await firstSkill.locator('button:has-text("Intermediate")').click();

    // Verify selection is highlighted
    await expect(firstSkill.locator('button:has-text("Intermediate")')).toHaveClass(/selected|active|bg-/);
  });

  test('should allow confidence level selection', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Find first skill and select confidence level
    const firstSkill = page.locator('.skill-item, [data-testid^="skill-"]').first();
    await expect(firstSkill).toBeVisible();

    // Click on confident level
    await firstSkill.locator('button:has-text("Confident")').click();

    // Verify selection is highlighted
    await expect(firstSkill.locator('button:has-text("Confident")')).toHaveClass(/selected|active|bg-/);
  });

  test('should navigate between domains', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Check initial domain
    await expect(page.locator('text=Domain 1 of')).toBeVisible();

    // Click next domain button
    await page.locator('button:has-text("Next Domain"), [data-testid="next-domain"]').click();

    // Check domain changed
    await expect(page.locator('text=Domain 2 of')).toBeVisible();
    await expect(page.locator('h2, h3, [data-testid="domain-title"]')).toContainText('Identity and Access Management');

    // Click previous domain button
    await page.locator('button:has-text("Previous Domain"), [data-testid="previous-domain"]').click();

    // Check back to first domain
    await expect(page.locator('text=Domain 1 of')).toBeVisible();
    await expect(page.locator('h2, h3, [data-testid="domain-title"]')).toContainText('Communication and Network Security');
  });

  test('should update progress as skills are assessed', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Initial progress should be low
    const progressText = page.locator('[data-testid="skills-count"], text=/skills assessed/');
    await expect(progressText).toContainText('0 skills assessed');

    // Assess a skill
    const firstSkill = page.locator('.skill-item, [data-testid^="skill-"]').first();
    await firstSkill.locator('button:has-text("Intermediate")').click();
    await firstSkill.locator('button:has-text("Confident")').click();

    // Progress should update
    await expect(progressText).toContainText('1 skills assessed');
  });

  test('should validate form before submission', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Navigate to last domain without assessing any skills
    for (let i = 0; i < 7; i++) {
      await page.locator('button:has-text("Next Domain")').click();
      await page.waitForTimeout(100); // Small delay for navigation
    }

    // Try to submit without any assessments
    const submitButton = page.locator('button:has-text("Complete Assessment"), [data-testid="submit-assessment"]');
    await expect(submitButton).toBeDisabled();
  });

  test('should complete assessment workflow', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Assess at least one skill in first domain
    const firstSkill = page.locator('.skill-item, [data-testid^="skill-"]').first();
    await firstSkill.locator('button:has-text("Advanced")').click();
    await firstSkill.locator('button:has-text("Very Confident")').click();

    // Navigate to last domain
    for (let i = 0; i < 7; i++) {
      await page.locator('button:has-text("Next Domain")').click();
      await page.waitForTimeout(100);
    }

    // Submit assessment
    const submitButton = page.locator('button:has-text("Complete Assessment"), [data-testid="submit-assessment"]');
    await expect(submitButton).toBeEnabled();
    await submitButton.click();

    // Check for completion message or redirect
    await expect(page.locator('text=Assessment Complete, text=Successfully, [data-testid="completion-message"]')).toBeVisible({ timeout: 10000 });
  });

  test('should handle loading states during submission', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Assess a skill
    const firstSkill = page.locator('.skill-item, [data-testid^="skill-"]').first();
    await firstSkill.locator('button:has-text("Intermediate")').click();
    await firstSkill.locator('button:has-text("Confident")').click();

    // Navigate to last domain
    for (let i = 0; i < 7; i++) {
      await page.locator('button:has-text("Next Domain")').click();
      await page.waitForTimeout(100);
    }

    // Mock slow API response
    await page.route('**/api/v1/skills/assess', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ assessment_id: 'test_assessment' })
      });
    });

    // Submit and check loading state
    await page.locator('button:has-text("Complete Assessment")').click();
    await expect(page.locator('text=Submitting, .loading, [data-testid="loading"]')).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await navigateToSkillsAssessment(page);

    // Check that components are still visible and usable
    await expect(page.locator('[data-testid="skills-assessment"], .skills-assessment')).toBeVisible();
    await expect(page.locator('[data-testid="progress-header"], .progress-header')).toBeVisible();

    // Check that skill selection buttons are still clickable
    const firstSkill = page.locator('.skill-item, [data-testid^="skill-"]').first();
    await firstSkill.locator('button:has-text("Basic")').click();
    await expect(firstSkill.locator('button:has-text("Basic")')).toHaveClass(/selected|active|bg-/);
  });

  test('should handle keyboard navigation', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Test tab navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Test that focused element is visible
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();

    // Test Enter key on skill level button
    const firstSkillButton = page.locator('.skill-item button').first();
    await firstSkillButton.focus();
    await page.keyboard.press('Enter');
    
    // Verify selection
    await expect(firstSkillButton).toHaveClass(/selected|active|bg-/);
  });

  test('should display help text and tooltips', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Check for help text
    await expect(page.locator('text=Be honest about your skill levels, text=Tip:, [data-testid="help-text"]')).toBeVisible();

    // Check for skill level descriptions
    await expect(page.locator('text=No experience, text=Some exposure, text=Working knowledge')).toBeVisible();
  });

  test('should preserve selections when navigating between domains', async ({ page }) => {
    await navigateToSkillsAssessment(page);

    // Make selections in first domain
    const firstSkill = page.locator('.skill-item, [data-testid^="skill-"]').first();
    await firstSkill.locator('button:has-text("Advanced")').click();
    await firstSkill.locator('button:has-text("Confident")').click();

    // Navigate to next domain and back
    await page.locator('button:has-text("Next Domain")').click();
    await page.locator('button:has-text("Previous Domain")').click();

    // Check selections are preserved
    await expect(firstSkill.locator('button:has-text("Advanced")')).toHaveClass(/selected|active|bg-/);
    await expect(firstSkill.locator('button:has-text("Confident")')).toHaveClass(/selected|active|bg-/);
  });
});

test.describe('Skills Dashboard UI Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock dashboard API responses
    await page.route('**/api/v1/skills/profile/*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user_id: 1,
          skill_vector: {
            network_protocols: 0.7,
            incident_response: 0.6,
            threat_modeling: 0.4
          },
          domain_scores: {
            communication_network_security: 0.7,
            security_operations: 0.6,
            security_architecture_engineering: 0.4
          },
          last_updated: new Date().toISOString(),
          confidence_scores: {
            network_protocols: 0.8,
            incident_response: 0.7,
            threat_modeling: 0.5
          }
        })
      });
    });
  });

  test('should display skills dashboard with results', async ({ page }) => {
    await page.goto('/skills/dashboard');

    // Check main dashboard components
    await expect(page.locator('[data-testid="skills-dashboard"], .skills-dashboard')).toBeVisible();
    await expect(page.locator('text=Skills Assessment Results')).toBeVisible();

    // Check overview cards
    await expect(page.locator('[data-testid="overall-level"], text=Overall Level')).toBeVisible();
    await expect(page.locator('[data-testid="skills-assessed"], text=Skills Assessed')).toBeVisible();
    await expect(page.locator('[data-testid="certifications"], text=Certifications')).toBeVisible();

    // Check tabs are present
    await expect(page.locator('[data-testid="domain-overview-tab"], text=Domain Overview')).toBeVisible();
    await expect(page.locator('[data-testid="individual-skills-tab"], text=Individual Skills')).toBeVisible();
    await expect(page.locator('[data-testid="recommendations-tab"], text=Recommendations')).toBeVisible();
  });

  test('should switch between dashboard tabs', async ({ page }) => {
    await page.goto('/skills/dashboard');

    // Click on Individual Skills tab
    await page.locator('[data-testid="individual-skills-tab"], text=Individual Skills').click();
    await expect(page.locator('[data-testid="skills-breakdown"], text=Individual Skill Scores')).toBeVisible();

    // Click on Recommendations tab
    await page.locator('[data-testid="recommendations-tab"], text=Recommendations').click();
    await expect(page.locator('[data-testid="recommendations"], text=Personalized Recommendations')).toBeVisible();

    // Click back to Domain Overview
    await page.locator('[data-testid="domain-overview-tab"], text=Domain Overview').click();
    await expect(page.locator('[data-testid="domain-scores"], text=Domain Scores')).toBeVisible();
  });

  test('should display progress bars for domain scores', async ({ page }) => {
    await page.goto('/skills/dashboard');

    // Check that progress bars are visible
    await expect(page.locator('[data-testid="progress-bar"], .progress-bar, [role="progressbar"]')).toHaveCount({ min: 3 });

    // Check that domain names are displayed
    await expect(page.locator('text=Communication & Network Security')).toBeVisible();
    await expect(page.locator('text=Security Operations')).toBeVisible();
  });

  test('should handle export and share functionality', async ({ page }) => {
    await page.goto('/skills/dashboard');

    // Check export button
    const exportButton = page.locator('button:has-text("Export"), [data-testid="export-button"]');
    await expect(exportButton).toBeVisible();

    // Check share button
    const shareButton = page.locator('button:has-text("Share"), [data-testid="share-button"]');
    await expect(shareButton).toBeVisible();

    // Test export click (would normally trigger download)
    await exportButton.click();
    // Note: Actual file download testing would require additional setup
  });
});

test.describe('Skills Progress Indicator Tests', () => {
  test('should display progress indicators correctly', async ({ page }) => {
    await page.goto('/skills/progress');

    // Check progress indicators are visible
    await expect(page.locator('[data-testid="progress-indicator"], .progress-indicator')).toBeVisible();

    // Check circular progress rings
    await expect(page.locator('[data-testid="skill-ring"], .skill-ring, svg circle')).toHaveCount({ min: 1 });

    // Check progress percentages
    await expect(page.locator('text=%, [data-testid="progress-percentage"]')).toBeVisible();
  });

  test('should show trend indicators', async ({ page }) => {
    await page.goto('/skills/progress');

    // Check for trend icons (up, down, or stable)
    await expect(page.locator('[data-testid="trend-icon"], .trend-icon, svg')).toHaveCount({ min: 1 });

    // Check for trend percentages
    await expect(page.locator('text=+, text=-, [data-testid="trend-percentage"]')).toBeVisible();
  });
});
