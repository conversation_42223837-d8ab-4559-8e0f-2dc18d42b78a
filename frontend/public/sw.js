// Service Worker for CertPathFinder PWA
// Provides offline functionality, caching, and background sync

const CACHE_NAME = 'certpathfinder-v1.0.0';
const API_CACHE_NAME = 'certpathfinder-api-v1.0.0';
const STATIC_CACHE_NAME = 'certpathfinder-static-v1.0.0';

// Files to cache for offline functionality
const STATIC_ASSETS = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
  '/favicon.ico',
  // Add other critical static assets
];

// API endpoints to cache
const CACHEABLE_API_ROUTES = [
  '/api/v1/certifications/',
  '/api/v1/domains/',
  '/api/v1/jobs/',
  '/api/v1/cost-calculator/',
];

// Network-first routes (always try network first)
const NETWORK_FIRST_ROUTES = [
  '/api/v1/user/',
  '/api/v1/progress/',
  '/api/v1/ai-assistant/',
];

// Cache-first routes (serve from cache if available)
const CACHE_FIRST_ROUTES = [
  '/api/v1/certifications/',
  '/api/v1/domains/',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== API_CACHE_NAME && 
                cacheName !== STATIC_CACHE_NAME) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
  } else if (url.pathname.startsWith('/static/')) {
    event.respondWith(handleStaticRequest(request));
  } else {
    event.respondWith(handleNavigationRequest(request));
  }
});

// Handle API requests with different caching strategies
async function handleApiRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  try {
    // Network-first strategy for dynamic content
    if (NETWORK_FIRST_ROUTES.some(route => pathname.startsWith(route))) {
      return await networkFirstStrategy(request, API_CACHE_NAME);
    }
    
    // Cache-first strategy for static content
    if (CACHE_FIRST_ROUTES.some(route => pathname.startsWith(route))) {
      return await cacheFirstStrategy(request, API_CACHE_NAME);
    }
    
    // Default: stale-while-revalidate
    return await staleWhileRevalidateStrategy(request, API_CACHE_NAME);
    
  } catch (error) {
    console.error('Service Worker: API request failed:', error);
    
    // Return cached response if available
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline fallback
    return new Response(
      JSON.stringify({ 
        error: 'Offline', 
        message: 'This feature is not available offline' 
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle static asset requests
async function handleStaticRequest(request) {
  try {
    return await cacheFirstStrategy(request, STATIC_CACHE_NAME);
  } catch (error) {
    console.error('Service Worker: Static request failed:', error);
    return fetch(request);
  }
}

// Handle navigation requests (HTML pages)
async function handleNavigationRequest(request) {
  try {
    // Try network first for navigation
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('Service Worker: Network failed, serving from cache');
    
    // Serve from cache if network fails
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Serve index.html for SPA routing
    const indexResponse = await caches.match('/');
    if (indexResponse) {
      return indexResponse;
    }
    
    // Final fallback
    return new Response('Offline', { status: 503 });
  }
}

// Caching strategies
async function networkFirstStrategy(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

async function cacheFirstStrategy(request, cacheName) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  const networkResponse = await fetch(request);
  
  if (networkResponse.ok) {
    const cache = await caches.open(cacheName);
    cache.put(request, networkResponse.clone());
  }
  
  return networkResponse;
}

async function staleWhileRevalidateStrategy(request, cacheName) {
  const cachedResponse = await caches.match(request);
  
  const networkResponsePromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      const cache = caches.open(cacheName);
      cache.then(c => c.put(request, networkResponse.clone()));
    }
    return networkResponse;
  });
  
  return cachedResponse || networkResponsePromise;
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync-progress') {
    event.waitUntil(syncProgressData());
  }
  
  if (event.tag === 'background-sync-study-sessions') {
    event.waitUntil(syncStudySessionData());
  }
});

// Sync progress data when back online
async function syncProgressData() {
  try {
    // Get stored offline progress data
    const progressData = await getStoredData('offline-progress');
    
    if (progressData && progressData.length > 0) {
      // Send to server
      for (const data of progressData) {
        await fetch('/api/v1/progress/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
      }
      
      // Clear stored data after successful sync
      await clearStoredData('offline-progress');
      console.log('Service Worker: Progress data synced successfully');
    }
    
  } catch (error) {
    console.error('Service Worker: Failed to sync progress data:', error);
  }
}

// Sync study session data when back online
async function syncStudySessionData() {
  try {
    const sessionData = await getStoredData('offline-study-sessions');
    
    if (sessionData && sessionData.length > 0) {
      for (const data of sessionData) {
        await fetch('/api/v1/study-timer/', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
      }
      
      await clearStoredData('offline-study-sessions');
      console.log('Service Worker: Study session data synced successfully');
    }
    
  } catch (error) {
    console.error('Service Worker: Failed to sync study session data:', error);
  }
}

// Helper functions for IndexedDB operations
async function getStoredData(storeName) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('CertPathFinderOffline', 1);
    
    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = () => resolve(getAllRequest.result);
      getAllRequest.onerror = () => reject(getAllRequest.error);
    };
    
    request.onerror = () => reject(request.error);
  });
}

async function clearStoredData(storeName) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('CertPathFinderOffline', 1);
    
    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const clearRequest = store.clear();
      
      clearRequest.onsuccess = () => resolve();
      clearRequest.onerror = () => reject(clearRequest.error);
    };
    
    request.onerror = () => reject(request.error);
  });
}

// Push notification handling
self.addEventListener('push', (event) => {
  if (!event.data) return;
  
  const data = event.data.json();
  const options = {
    body: data.body,
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    data: data.data || {},
    actions: data.actions || []
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  const data = event.notification.data;
  const action = event.action;
  
  if (action === 'open-app' || !action) {
    event.waitUntil(
      clients.openWindow(data.url || '/')
    );
  }
});

console.log('Service Worker: Loaded successfully');
