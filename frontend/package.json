{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@axe-core/playwright": "^4.10.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.80.10", "@tanstack/react-query-devtools": "^5.80.10", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "framer-motion": "^11.18.2", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.49.2", "react-router-dom": "^7.6.2", "react-scripts": "^5.0.1", "tailwind-merge": "^2.2.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zod": "^3.25.67", "zustand": "^4.4.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@playwright/test": "^1.52.0", "autoprefixer": "^10.4.21", "postcss": "^8.4.47", "tailwindcss": "^4.1.8"}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.47", "svgo": "^3.3.2", "webpack-dev-server": "^5.2.1", "@svgr/webpack": "^8.1.0", "@svgr/plugin-svgo": "^8.1.0", "css-select": "^5.1.0", "resolve-url-loader": "^5.0.0"}}