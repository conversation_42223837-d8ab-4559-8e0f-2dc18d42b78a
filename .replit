modules = ["python-3.11", "python3"]

[nix]
channel = "stable-24_05"

[deployment]
deploymentTarget = "autoscale"
run = ["sh", "-c", "python run_api.py"]

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Streamlit App"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "FastAPI Server"

[[workflows.workflow]]
name = "Streamlit App"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "streamlit run main.py"
waitForPort = 5000

[[workflows.workflow]]
name = "FastAPI Server"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python run_api.py"
waitForPort = 8000

[[ports]]
localPort = 3000
externalPort = 3000

[[ports]]
localPort = 3001
externalPort = 3001

[[ports]]
localPort = 3002
externalPort = 3002

[[ports]]
localPort = 3003
externalPort = 3003

[[ports]]
localPort = 5000
externalPort = 80

[[ports]]
localPort = 5001
externalPort = 4200

[[ports]]
localPort = 8000
externalPort = 8000

[[ports]]
localPort = 8001
externalPort = 5000

[[ports]]
localPort = 8002
externalPort = 5173
