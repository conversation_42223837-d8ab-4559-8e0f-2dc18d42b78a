version: '3.8'

# CertRats Platform - Development Environment Overrides
#
# This file contains development-specific configurations that override
# the base docker-compose.yml settings for local development.
#
# Features:
# - Hot reload for development
# - Debug mode enabled
# - Exposed ports for direct access
# - Development-friendly logging
# - Relaxed security settings
# - Additional development tools
#
# Usage:
#   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

services:
  # =============================================================================
  # CORE SERVICES - DEVELOPMENT OVERRIDES
  # =============================================================================

  # API Development Configuration
  api:
    build:
      target: development
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - RELOAD=true
      - ENVIRONMENT=development
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
    ports:
      - "8000:8000"  # Direct access for debugging
    volumes:
      - ./backend:/app
      - /app/__pycache__  # Exclude cache directory
    labels:
      # Development-specific Traefik labels
      - "traefik.http.routers.certrats-api.middlewares=cors"
      - "traefik.http.middlewares.cors.headers.accesscontrolalloworiginlist=*"

  # Frontend Development Configuration
  frontend:
    build:
      target: development
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://api.certrats.localhost
      - REACT_APP_DEBUG=true
      - FAST_REFRESH=true
      - CHOKIDAR_USEPOLLING=true  # For file watching in Docker
    command: npm start
    ports:
      - "3000:3000"  # Direct access for debugging
    volumes:
      - ./frontend:/app
      - /app/node_modules  # Exclude node_modules
    stdin_open: true
    tty: true

  # =============================================================================
  # DATA LAYER - DEVELOPMENT OVERRIDES
  # =============================================================================

  # Database Development Configuration
  database:
    environment:
      - POSTGRES_DB=certrats_dev
      - POSTGRES_USER=certrats_dev
      - POSTGRES_PASSWORD=certrats_dev_password
    ports:
      - "5432:5432"  # Exposed for development tools
    volumes:
      - ./docker/database/dev-init:/docker-entrypoint-initdb.d
    command: postgres -c log_statement=all -c log_destination=stderr

  # Redis Development Configuration
  redis:
    command: redis-server --appendonly yes --loglevel verbose
    ports:
      - "6379:6379"  # Exposed for development tools

  # MinIO Development Configuration
  storage:
    environment:
      - MINIO_ROOT_USER=certrats_dev
      - MINIO_ROOT_PASSWORD=certrats_dev_secret
    ports:
      - "9000:9000"  # Direct API access
      - "9001:9001"  # Direct console access

  # =============================================================================
  # BACKGROUND PROCESSING - DEVELOPMENT OVERRIDES
  # =============================================================================

  # Celery Worker Development Configuration
  celery_worker:
    command: celery -A app.celery worker --loglevel=debug --concurrency=2
    environment:
      - CELERY_LOG_LEVEL=DEBUG
    volumes:
      - ./backend:/app

  # Celery Beat Development Configuration
  celery_beat:
    command: celery -A app.celery beat --loglevel=debug
    environment:
      - CELERY_LOG_LEVEL=DEBUG
    volumes:
      - ./backend:/app

  # Flower Development Configuration
  flower:
    command: celery -A app.celery flower --port=5555 --debug
    ports:
      - "5555:5555"  # Direct access for monitoring

  # =============================================================================
  # MONITORING - DEVELOPMENT OVERRIDES
  # =============================================================================

  # Prometheus Development Configuration
  prometheus:
    ports:
      - "9090:9090"  # Direct access for debugging
    volumes:
      - ./docker/monitoring/prometheus-dev.yml:/etc/prometheus/prometheus.yml

  # Grafana Development Configuration
  grafana:
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=true
      - GF_LOG_LEVEL=debug
      - GF_FEATURE_TOGGLES_ENABLE=ngalert
    ports:
      - "3001:3000"  # Direct access (avoiding conflict with frontend)
    volumes:
      - ./docker/monitoring/grafana/dev-dashboards:/var/lib/grafana/dashboards

  # =============================================================================
  # DEVELOPMENT TOOLS
  # =============================================================================

  # pgAdmin for Database Management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: certrats_pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=certrats_pgadmin
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - certrats_network
      - traefik_network
    depends_on:
      - database
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-pgadmin.rule=Host(`pgadmin.certrats.localhost`)"
      - "traefik.http.routers.certrats-pgadmin.entrypoints=web"
      - "traefik.http.services.certrats-pgadmin.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik_network"

  # Redis Commander for Redis Management
  redis_commander:
    image: rediscommander/redis-commander:latest
    container_name: certrats_redis_commander
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=certrats_redis
    networks:
      - certrats_network
      - traefik_network
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-redis-commander.rule=Host(`redis.certrats.localhost`)"
      - "traefik.http.routers.certrats-redis-commander.entrypoints=web"
      - "traefik.http.services.certrats-redis-commander.loadbalancer.server.port=8081"
      - "traefik.docker.network=traefik_network"

  # MailHog for Email Testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: certrats_mailhog
    networks:
      - certrats_network
      - traefik_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-mailhog.rule=Host(`mail.certrats.localhost`)"
      - "traefik.http.routers.certrats-mailhog.entrypoints=web"
      - "traefik.http.services.certrats-mailhog.loadbalancer.server.port=8025"
      - "traefik.docker.network=traefik_network"

  # Jupyter Notebook for Data Analysis
  jupyter:
    image: jupyter/scipy-notebook:latest
    container_name: certrats_jupyter
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=certrats_jupyter
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./backend:/home/<USER>/work/backend:ro
    networks:
      - certrats_network
      - traefik_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-jupyter.rule=Host(`jupyter.certrats.localhost`)"
      - "traefik.http.routers.certrats-jupyter.entrypoints=web"
      - "traefik.http.services.certrats-jupyter.loadbalancer.server.port=8888"
      - "traefik.docker.network=traefik_network"

# =============================================================================
# DEVELOPMENT VOLUMES
# =============================================================================

volumes:
  pgadmin_data:
    name: certrats_pgadmin_data

# =============================================================================
# DEVELOPMENT NETWORKS
# =============================================================================

networks:
  certrats_network:
    name: certrats_dev_network
  traefik_network:
    external: true
