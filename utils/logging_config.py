"""
Comprehensive logging configuration for CertPathFinder.

This module provides structured logging with proper formatting,
rotation, and different log levels for different environments.
"""

import logging
import logging.handlers
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from pythonjsonlogger import jsonlogger

class StructuredFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter for structured logging."""
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]):
        """Add custom fields to log record."""
        super().add_fields(log_record, record, message_dict)
        
        # Add timestamp
        log_record['timestamp'] = datetime.utcnow().isoformat()
        
        # Add service information
        log_record['service'] = 'certpathfinder'
        log_record['version'] = '1.0.0'
        
        # Add request context if available
        if hasattr(record, 'request_id'):
            log_record['request_id'] = record.request_id
        
        if hasattr(record, 'user_id'):
            log_record['user_id'] = record.user_id
        
        # Add module and function information
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno

class RequestContextFilter(logging.Filter):
    """Filter to add request context to log records."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add request context to log record."""
        # This would be populated by middleware in a real request
        if not hasattr(record, 'request_id'):
            record.request_id = None
        if not hasattr(record, 'user_id'):
            record.user_id = None
        return True

def setup_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_console: bool = True
) -> logging.Logger:
    """
    Set up comprehensive logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Format type ('json' or 'text')
        log_file: Path to log file (if None, only console logging)
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
        enable_console: Whether to enable console logging
    
    Returns:
        Configured logger instance
    """
    # Create root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Add request context filter
    context_filter = RequestContextFilter()
    
    # Set up formatters
    if log_format.lower() == "json":
        formatter = StructuredFormatter(
            '%(timestamp)s %(level)s %(name)s %(message)s'
        )
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
    
    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.addFilter(context_filter)
        logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        file_handler.setFormatter(formatter)
        file_handler.addFilter(context_filter)
        logger.addHandler(file_handler)
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name."""
    return logging.getLogger(name)

class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")

def log_function_call(func):
    """Decorator to log function calls with parameters and execution time."""
    import functools
    import time
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()
        
        # Log function entry
        logger.debug(
            f"Calling {func.__name__}",
            extra={
                'function': func.__name__,
                'args': str(args)[:200],  # Truncate long args
                'kwargs': str(kwargs)[:200]
            }
        )
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Log successful completion
            logger.debug(
                f"Completed {func.__name__}",
                extra={
                    'function': func.__name__,
                    'execution_time': execution_time,
                    'success': True
                }
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # Log error
            logger.error(
                f"Error in {func.__name__}: {str(e)}",
                extra={
                    'function': func.__name__,
                    'execution_time': execution_time,
                    'success': False,
                    'error': str(e)
                },
                exc_info=True
            )
            raise
    
    return wrapper

def log_api_request(request_id: str, user_id: Optional[str] = None):
    """Context manager for logging API requests."""
    import contextvars
    import time

    class APIRequestLogger:
        def __init__(self, request_id: str, user_id: Optional[str] = None):
            self.request_id = request_id
            self.user_id = user_id
            self.start_time = None
        
        def __enter__(self):
            self.start_time = time.time()
            # Set context variables that can be picked up by the filter
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            execution_time = time.time() - self.start_time
            logger = logging.getLogger("api")
            
            if exc_type is None:
                logger.info(
                    "API request completed",
                    extra={
                        'request_id': self.request_id,
                        'user_id': self.user_id,
                        'execution_time': execution_time,
                        'success': True
                    }
                )
            else:
                logger.error(
                    f"API request failed: {str(exc_val)}",
                    extra={
                        'request_id': self.request_id,
                        'user_id': self.user_id,
                        'execution_time': execution_time,
                        'success': False,
                        'error': str(exc_val)
                    },
                    exc_info=True
                )
    
    return APIRequestLogger(request_id, user_id)

# Security logging functions
def log_security_event(event_type: str, details: Dict[str, Any], severity: str = "WARNING"):
    """Log security-related events."""
    security_logger = logging.getLogger("security")
    
    log_data = {
        'event_type': event_type,
        'severity': severity,
        'timestamp': datetime.utcnow().isoformat(),
        **details
    }
    
    getattr(security_logger, severity.lower())(
        f"Security event: {event_type}",
        extra=log_data
    )

def log_audit_event(action: str, resource: str, user_id: str, details: Dict[str, Any] = None):
    """Log audit events for compliance."""
    audit_logger = logging.getLogger("audit")
    
    log_data = {
        'action': action,
        'resource': resource,
        'user_id': user_id,
        'timestamp': datetime.utcnow().isoformat(),
        'details': details or {}
    }
    
    audit_logger.info(
        f"Audit: {action} on {resource} by {user_id}",
        extra=log_data
    )

# Performance logging
def log_performance_metric(metric_name: str, value: float, tags: Dict[str, str] = None):
    """Log performance metrics."""
    perf_logger = logging.getLogger("performance")
    
    log_data = {
        'metric_name': metric_name,
        'value': value,
        'tags': tags or {},
        'timestamp': datetime.utcnow().isoformat()
    }
    
    perf_logger.info(
        f"Performance metric: {metric_name} = {value}",
        extra=log_data
    )
