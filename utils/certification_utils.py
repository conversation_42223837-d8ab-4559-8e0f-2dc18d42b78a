"""Utility functions for certification management"""
from typing import List, Dict, Any, Sequence
from sqlalchemy.orm import Session
from database import get_db
from models.certification import Certification

def get_certifications() -> Sequence[Dict[str, Any]]:
    """
    Get all active certifications from the database
    Returns them in a format suitable for visualization

    Returns:
        A sequence of certification dictionaries containing certification details
    """
    with next(get_db()) as db:
        certifications = (db.query(Certification)
                        .filter(Certification.is_deleted == False)
                        .all())
        return [cert.to_dict() for cert in certifications]

def get_certification_by_id(cert_id: int) -> Dict[str, Any]:
    """
    Get a specific certification by ID

    Args:
        cert_id: The ID of the certification to retrieve

    Returns:
        A dictionary containing the certification details
    """
    with next(get_db()) as db:
        cert = db.query(Certification).filter_by(id=cert_id).first()
        return cert.to_dict() if cert else None

def get_certifications_by_domain(domain: str) -> List[Dict[str, Any]]:
    """
    Get all certifications for a specific domain

    Args:
        domain: The domain to filter certifications by

    Returns:
        A list of certification dictionaries
    """
    with next(get_db()) as db:
        certifications = (db.query(Certification)
                        .filter(Certification.domain == domain)
                        .filter(Certification.is_deleted == False)
                        .all())
        return [cert.to_dict() for cert in certifications]

def get_certification_stats() -> Dict[str, Any]:
    """
    Get certification statistics

    Returns:
        A dictionary containing certification statistics
    """
    with next(get_db()) as db:
        total_certs = db.query(Certification).filter(Certification.is_deleted == False).count()
        domains = (db.query(Certification.domain)
                  .filter(Certification.is_deleted == False)
                  .distinct()
                  .count())
        return {
            'total_certifications': total_certs,
            'total_domains': domains
        }