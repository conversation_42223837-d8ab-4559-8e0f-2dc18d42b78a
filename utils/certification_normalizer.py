"""
Utility for normalizing certification data.
"""
import json
import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from models.certification_schema import CertificationBase
from pydantic import ValidationError

logger = logging.getLogger(__name__)

def normalize_url(url: str) -> str:
    """Normalize URL to ensure proper format."""
    url = url.strip()

    # Fix common URL typos
    if url.startswith('hhttps://'):
        url = url.replace('hhttps://', 'https://')
    elif url.startswith('hhttp://'):
        url = url.replace('hhttp://', 'http://')
    elif not url.startswith(('http://', 'https://')):
        # Remove any leading or trailing slashes
        url = url.strip('/')
        # Add https:// prefix
        url = f'https://{url}'

    return url

def normalize_focus(focus: str) -> str:
    """Normalize focus field to match valid values."""
    focus_mapping = {
        'mgmtx': 'Mgmt',
        'management': 'Mgmt',
        'blue': 'Blueops',
        'defensive': 'Blueops',
        'red': 'Redops',
        'offensive': 'Redops',
        'engineering': 'Engineer',
        'identity': 'Iam',
        'testing': 'Test',
        'networking': 'Network',
        'assets': 'Asset'
    }

    # Convert to lowercase for matching
    focus_lower = focus.lower().strip()
    # Remove common suffixes
    focus_lower = re.sub(r'[^a-z]', '', focus_lower)

    # Try to match with known mappings
    if focus_lower in focus_mapping:
        return focus_mapping[focus_lower]

    # If no mapping found, return original if valid, otherwise default to Engineer
    valid_focuses = {'Mgmt', 'Blueops', 'Redops', 'Engineer', 'Iam', 'Test', 'Network', 'Asset'}
    return focus if focus in valid_focuses else 'Engineer'

def normalize_certification_data(raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Normalize raw certification data to match the standardized schema.

    Args:
        raw_data: List of raw certification dictionaries

    Returns:
        List of normalized certification dictionaries
    """
    normalized_data = []

    for cert_data in raw_data:
        try:
            # Extract category from the structure
            category = cert_data.get('category', 'Uncategorized')

            for cert in cert_data.get('certifications', [cert_data]):
                try:
                    # Pre-process and normalize data
                    url = normalize_url(cert.get('url', ''))
                    focus = normalize_focus(cert.get('focus', 'Engineer'))

                    processed_data = {
                        'name': cert.get('name', '').strip(),
                        'category': category.strip(),
                        'description': cert.get('description', '').strip(),
                        'url': url,
                        'cost': cert.get('cost'),
                        'difficulty': cert.get('difficulty', 1),
                        'focus': focus,
                        'domain': cert.get('domain', 'Security Engineering'),
                        'prerequisites': cert.get('prerequisites', []),
                        'validity_period': None,  # Will be extracted from description
                        'last_updated': datetime.utcnow()
                    }

                    # Extract validity period from description if present
                    desc = cert.get('description', '').lower()
                    if 'valid for' in desc or 'validity' in desc:
                        period_match = re.search(r'valid(?:ity)?\s+(?:period\s+)?(?:is\s+)?(?:for\s+)?(\d+)\s+(year|month)', desc.lower())
                        if period_match:
                            number = int(period_match.group(1))
                            unit = period_match.group(2)
                            processed_data['validity_period'] = number * (12 if unit == 'year' else 1)

                    # Validate and normalize through schema
                    cert_model = CertificationBase(**processed_data)
                    normalized = cert_model.normalize()
                    normalized_data.append(normalized)
                    logger.info(f"Successfully normalized certification: {cert.get('name', 'Unknown')}")

                except ValidationError as e:
                    logger.error(f"Validation error for certification {cert.get('name', 'Unknown')}: {str(e)}")
                    continue
                except Exception as e:
                    logger.error(f"Error processing certification {cert.get('name', 'Unknown')}: {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"Error processing category: {str(e)}")
            continue

    return normalized_data

def update_certification_data(input_file: str, output_file: str) -> None:
    """
    Read, normalize and update certification data.

    Args:
        input_file: Path to input JSON file
        output_file: Path to output JSON file
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)

        normalized_data = normalize_certification_data(raw_data)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(normalized_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Successfully normalized {len(normalized_data)} certifications")

    except Exception as e:
        logger.error(f"Error updating certification data: {str(e)}")
        raise