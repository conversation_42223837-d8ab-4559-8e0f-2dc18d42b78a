"""
Claude AI integration module for career path recommendations.
"""
import os
from anthropic import <PERSON><PERSON><PERSON>, APIConnectionError, APIError, APITimeoutError
import json
from typing import List, Dict, Optional, TypedDict, Union, Any
import logging
from functools import lru_cache
import time
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.DEBUG)  # Changed to DEBUG for more detailed logs
logger = logging.getLogger(__name__)

def get_api_key() -> Optional[str]:
    """Get API key from environment variables."""
    try:
        # Try environment variable
        api_key = os.environ.get("ANTHROPIC_API_KEY")
        if api_key:
            logger.info("Found API key in environment variables")
            return api_key

        logger.error("ANTHROPIC_API_KEY not found in environment variables")
        return None
    except Exception as e:
        logger.error(f"Error accessing API key: {str(e)}")
        return None

# Initialize client at module level
anthropic_client = None

def init_anthropic_client():
    """Initialize the Anthropic client if not already initialized."""
    global anthropic_client
    if anthropic_client is None:
        api_key = get_api_key()
        if api_key:
            try:
                anthropic_client = Anthropic(api_key=api_key)
                logger.info("Successfully initialized Anthropic client")
            except Exception as e:
                logger.error(f"Failed to initialize Anthropic client: {str(e)}")
                return None
    return anthropic_client

def parse_claude_response(content: str) -> Dict[str, Any]:
    """Parse <PERSON>'s response and handle any JSON formatting issues."""
    try:
        logger.debug(f"Parsing response content type: {type(content)}")
        logger.debug(f"Raw response content: {content}")

        # Handle list of TextBlock objects
        if isinstance(content, list):
            # Extract text from first message
            text_content = content[0].text if content and hasattr(content[0], 'text') else str(content)
            logger.debug(f"Extracted text content: {text_content}")

            # Look for JSON content
            start_idx = text_content.find('{')
            end_idx = text_content.rfind('}') + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_str = text_content[start_idx:end_idx]
                logger.debug(f"Found JSON content: {json_str}")
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON: {str(e)}")
                    logger.error(f"Invalid JSON string: {json_str}")
                    raise
            else:
                logger.error("No JSON content found in response")
                raise ValueError("No JSON content found in response")

        # Handle string content directly
        elif isinstance(content, str):
            try:
                return json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse string content: {str(e)}")
                logger.error(f"Invalid JSON string: {content}")
                raise
        else:
            logger.error(f"Unexpected content type: {type(content)}")
            raise ValueError(f"Unexpected content type: {type(content)}")

    except json.JSONDecodeError as e:
        logger.error(f"JSON parsing error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Failed to parse response content: {str(e)}")
        raise

def serialize_cert_data(cert_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Serialize certification data, ensuring all objects are JSON-serializable."""
    def serialize_datetime(obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return obj

    try:
        serialized_data = []
        for cert in cert_data:
            serialized_cert = {}
            for key, value in cert.items():
                serialized_cert[key] = serialize_datetime(value)
            serialized_data.append(serialized_cert)
        return serialized_data
    except Exception as e:
        logger.error(f"Error serializing certification data: {str(e)}")
        return []

def analyze_certification_alignment(
    current_certs: List[str],
    target_role: str,
    cert_data: List[Dict[str, Any]],
    context_id: str
) -> Union[Dict[str, Any], Dict[str, str]]:
    """Analyze how well current certifications align with target role."""
    try:
        if not init_anthropic_client():
            return {"error": "Service configuration error"}

        # Create prompt for analysis
        prompt = f"""Given:
- Current certifications: {', '.join(current_certs)}
- Target role: {target_role}
- Available certifications: {json.dumps(cert_data, indent=2)}

Analyze the alignment between current certifications and target role.
Return a JSON response with this exact structure:
{{
    "alignment_score": number between 0 and 1,
    "strengths": ["strength1", "strength2"],
    "gaps": ["gap1", "gap2"],
    "recommended_next_certs": ["cert1", "cert2"]
}}"""

        try:
            response = anthropic_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=2000,
                messages=[{"role": "user", "content": prompt}]
            )

            logger.info(f"Received response from Claude API: {response.content}")
            result = parse_claude_response(response.content)

            # Validate required fields
            required_fields = ['alignment_score', 'strengths', 'gaps', 'recommended_next_certs']
            if not all(field in result for field in required_fields):
                logger.error(f"Missing required fields in API response: {result}")
                return {"error": "Invalid API response format"}

            return result

        except APIConnectionError as e:
            logger.error(f"Connection error: {str(e)}")
            return {"error": "Failed to connect to service"}
        except APITimeoutError as e:
            logger.error(f"Request timeout: {str(e)}")
            return {"error": "Service request timed out"}
        except Exception as e:
            logger.error(f"Error processing API response: {str(e)}")
            return {"error": "Service temporarily unavailable"}

    except Exception as e:
        logger.error(f"Unexpected error in certification analysis: {str(e)}")
        return {"error": "An unexpected error occurred"}

def generate_career_path(
    interests: List[str],
    experience_level: str,
    certifications: List[Dict[str, Any]],
    target_role: str,
    context_id: str
) -> Union[Dict[str, Any], Dict[str, str]]:
    """Generate a personalized career path recommendation."""
    try:
        logger.info(f"Generating career path for context: {context_id}")
        logger.debug(f"Input parameters: interests={interests}, experience_level={experience_level}, target_role={target_role}")
        logger.debug(f"Number of available certifications: {len(certifications)}")

        # Initialize client if needed
        client = init_anthropic_client()
        if not client:
            logger.error("Anthropic client initialization failed")
            return {"error": "Service configuration error"}

        # Serialize certification data safely
        try:
            serialized_certs = [
                {
                    "name": cert["name"],
                    "category": cert["category"],
                    "domain": cert["domain"],
                    "level": cert["level"],
                    "focus": cert["focus"],
                    "difficulty": cert["difficulty"]
                }
                for cert in certifications
            ]
            logger.debug(f"Serialized {len(serialized_certs)} certifications")
            logger.debug(f"Sample certification: {serialized_certs[0] if serialized_certs else 'None'}")
        except Exception as e:
            logger.error(f"Error serializing certification data: {str(e)}")
            return {"error": "Failed to process certification data"}

        # Construct the prompt
        prompt = f"""Based on:
- Experience Level: {experience_level}
- Target Role: {target_role}
- Interests: {', '.join(interests)}
- Available Certifications: {json.dumps(serialized_certs[:5], indent=2)}  # Limiting to 5 certs for brevity

Create a career path with certification stages.
Return a JSON object with exactly this structure:
{{
    "career_path": [
        {{
            "stage": "Foundation",
            "timeline": "3-6 months",
            "certifications": ["Network+", "Security+"]
        }}
    ],
    "total_stages": 1
}}"""

        try:
            logger.info("Sending request to Claude API")
            response = client.messages.create(
                model="claude-3-5-sonnet-20241022",  # Using latest model
                max_tokens=2000,
                messages=[{
                    "role": "user",
                    "content": prompt
                }]
            )

            if not response or not response.content:
                logger.error("Empty response from Claude API")
                return {"error": "Empty response from service"}

            logger.debug(f"Raw API response: {response.content}")

            # Parse the response carefully
            try:
                # Handle both string and list response formats
                content = response.content
                if isinstance(content, list):
                    content = content[0].text

                # Try to find JSON in the content
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1

                if start_idx >= 0 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    result = json.loads(json_str)
                else:
                    logger.error("No JSON content found in response")
                    return {"error": "Invalid response format"}

                logger.debug(f"Parsed result: {result}")

                # Validate response structure
                if not isinstance(result.get('career_path'), list):
                    logger.error("Invalid response structure: career_path is not a list")
                    return {"error": "Invalid career path structure"}

                for stage in result.get('career_path', []):
                    if not all(k in stage for k in ['stage', 'timeline', 'certifications']):
                        logger.error(f"Invalid stage structure: {stage}")
                        return {"error": "Invalid stage structure in career path"}

                return result

            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing error: {str(e)}")
                return {"error": "Failed to parse service response"}

        except APIConnectionError as e:
            logger.error(f"Connection error: {str(e)}")
            return {"error": "Failed to connect to service"}
        except APITimeoutError as e:
            logger.error(f"Request timeout: {str(e)}")
            return {"error": "Service request timed out"}
        except Exception as e:
            logger.error(f"Error processing API response: {str(e)}", exc_info=True)
            return {"error": "Service temporarily unavailable"}

    except Exception as e:
        logger.exception("Unexpected error in career path generation")
        return {"error": "An unexpected error occurred"}

# Type definitions for career path data structures
class CertificationCost(TypedDict):
    """Type definition for certification costs."""
    exam: float
    materials: float
    training: float

class CareerPathStage(TypedDict):
    """Type definition for a career path stage."""
    stage: str
    timeline: str
    certifications: List[str]
    certification_costs: Optional[Dict[str, CertificationCost]]

class CareerPathResponse(TypedDict):
    """Type definition for the complete career path response."""
    career_path: List[CareerPathStage]
    total_stages: int

# Initialize conversation context cache
CONTEXT_CACHE: Dict[str, Dict[str, Union[List[Dict[str, str]], datetime]]] = {}

# Initialize anthropic client when module is loaded
init_anthropic_client()