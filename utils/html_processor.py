"""
Process HTML data from SecCertRoadmapHTML submodule
"""
import os
from bs4 import BeautifulSoup
import json
import logging
import re
from typing import List, Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_prerequisites(tooltip_text: str) -> list:
    """Extract prerequisite certifications from tooltip text"""
    prerequisites = []
    if not tooltip_text:
        return prerequisites

    # Common prerequisite patterns
    patterns = [
        r"requires? (the )?([A-Za-z0-9\+\-\(\)]+(?:\s*[A-Za-z0-9\+\-\(\)]+)*)",
        r"prerequisite:?\s*([A-Za-z0-9\+\-\(\)]+(?:\s*[A-Za-z0-9\+\-\(\)]+)*)",
        r"after completing ([A-Za-z0-9\+\-\(\)]+(?:\s*[A-Za-z0-9\+\-\(\)]+)*)",
        r"must have ([A-Za-z0-9\+\-\(\)]+(?:\s*[A-Za-z0-9\+\-\(\)]+)*) certification"
    ]

    for pattern in patterns:
        matches = re.finditer(pattern, tooltip_text, re.IGNORECASE)
        for match in matches:
            cert_name = match.group(1) if len(match.groups()) == 1 else match.group(2)
            cert_name = cert_name.strip()
            if cert_name and len(cert_name) > 2:  # Avoid short acronyms or false matches
                prerequisites.append(cert_name)

    return list(set(prerequisites))  # Remove duplicates

def process_certification_html() -> List[Dict[str, Any]]:
    """Process the Security Certification Roadmap HTML file and extract certification data"""
    html_file = os.path.join('submodules', 'SecCertRoadmapHTML', 'Security-Certification-Roadmap9.html')

    try:
        if not os.path.exists(html_file):
            logger.error(f"HTML file not found at {html_file}")
            return []

        logger.info(f"Processing HTML file: {html_file}")
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():
                logger.error("HTML file is empty")
                return []
            logger.info(f"Successfully read HTML file, size: {len(content)} bytes")
            soup = BeautifulSoup(content, 'html.parser')

        # Initialize categories
        categories = {
            'Entry Level Security Certifications': [],
            'Mid-Level Security Certifications': [],
            'Advanced Security Certifications': [],
            'Expert Security Certifications': [],
            'Specialized Security Certifications': [],
            'Management Security Certifications': []
        }

        # Find the certification elements
        cert_patterns = [
            'networkitem', 'redopsitem', 'mgmtitem', 'engineeritem',
            'blueopsitem', 'testitem', 'iamitem', 'assetitem'
        ]

        processed_certs = set()
        total_elements = 0

        # Create a combined pattern for finding certification elements
        pattern = f"({'|'.join(cert_patterns)})[\\w-]*"
        logger.debug(f"Using pattern: {pattern}")

        # Find all elements with certification class patterns
        cert_elements = soup.find_all(class_=re.compile(pattern))
        total_elements = len(cert_elements)
        logger.info(f"Found {total_elements} certification elements")

        # For debugging
        if total_elements == 0:
            sample_elements = soup.find_all('div', class_=True)
            logger.debug("Sample of div elements with classes:")
            for i, elem in enumerate(sample_elements[:5]):
                logger.debug(f"Element {i}: classes={elem.get('class', [])}")

        # Map domains
        domain_mapping = {
            'redops': 'Offensive Security',
            'network': 'Network Security',
            'mgmt': 'Security Management',
            'engineer': 'Security Engineering',
            'blueops': 'Defensive Security',
            'test': 'Security Testing',
            'iam': 'Identity & Access Management',
            'asset': 'Asset Security'
        }

        for element in cert_elements:
            # Get name from title or text
            name = element.get('title', '').strip() or element.get_text().strip()
            if not name or name in processed_certs:
                continue

            logger.debug(f"Processing element with classes: {element.get('class', [])}")
            processed_certs.add(name)
            logger.debug(f"Processing certification: {name}")

            # Extract description
            description = (element.get('tooltiptext', '') or 
                         element.get('tooltipleft', '') or 
                         element.get('tooltipright', '') or 
                         element.get('data-tooltip', ''))

            # Get element classes
            classes = element.get('class', [])
            logger.debug(f"Classes for {name}: {classes}")

            # Extract difficulty and domain
            difficulty = None
            domain = None

            for cls in classes:
                if not isinstance(cls, str):
                    continue

                # Get domain from class name
                for base in domain_mapping:
                    if base in cls.lower():
                        domain = domain_mapping[base]
                        break

                # Extract difficulty from class name
                if difficulty is None:
                    diff_match = re.search(r'(\d+)', cls)
                    if diff_match:
                        try:
                            difficulty = int(diff_match.group(1))
                        except ValueError:
                            continue

            domain = domain or 'General Security'
            difficulty = difficulty or 1  # Default to entry level if no difficulty found

            # Determine category based on difficulty and description
            if difficulty == 1 or any(term in description.lower() for term in ['entry', 'basic', 'fundamental']):
                category = 'Entry Level Security Certifications'
            elif difficulty == 2 or 'intermediate' in description.lower():
                category = 'Mid-Level Security Certifications'
            elif difficulty == 3 or 'advanced' in description.lower():
                category = 'Advanced Security Certifications'
            elif difficulty == 4 or any(term in description.lower() for term in ['expert', 'master']):
                category = 'Expert Security Certifications'
            elif 'management' in description.lower():
                category = 'Management Security Certifications'
            else:
                category = 'Specialized Security Certifications'

            # Extract prerequisites
            prerequisites = extract_prerequisites(description)

            # Create certification entry
            cert_data = {
                "name": name,
                "description": description,
                "difficulty": difficulty,
                "level": "Entry Level" if difficulty == 1 else "Intermediate" if difficulty == 2 else "Advanced" if difficulty == 3 else "Expert",
                "domain": domain,
                "prerequisites": prerequisites,
                "category": category
            }

            # Add to appropriate category
            categories[category].append(cert_data)
            logger.debug(f"Added certification {name} to category {category}")

        # Convert categories dict to list format
        formatted_categories = [
            {
                "category": category,
                "certifications": sorted(certs, key=lambda x: x['name'])
            }
            for category, certs in categories.items()
            if certs  # Only include categories with certifications
        ]

        # Save to JSON file
        output_file = os.path.join('attached_assets', 'certifications.json')
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(formatted_categories, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved certifications data to {output_file}")
        except Exception as e:
            logger.error(f"Error saving certifications JSON: {str(e)}")

        # Log results
        logger.info(f"Found {total_elements} total elements")
        logger.info(f"Processed {len(processed_certs)} unique certifications across {len(formatted_categories)} categories")

        # Print detailed category breakdown
        for category in formatted_categories:
            logger.info(f"{category['category']}: {len(category['certifications'])} certifications")

        return formatted_categories

    except Exception as e:
        logger.error(f"Error processing certification HTML: {str(e)}")
        return []

if __name__ == "__main__":
    process_certification_html()