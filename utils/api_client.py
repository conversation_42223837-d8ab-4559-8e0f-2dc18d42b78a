"""API client utility for making HTTP requests to the FastAPI backend.

This utility provides a simple interface for making API calls from frontend
components to the FastAPI backend services.
"""

import requests
import logging
from typing import Dict, Any, Optional
import os

logger = logging.getLogger(__name__)


class APIClient:
    """Simple API client for backend communication."""
    
    def __init__(self, base_url: Optional[str] = None):
        """Initialize API client with base URL."""
        self.base_url = base_url or self._get_api_base_url()
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _get_api_base_url(self) -> str:
        """Get API base URL from environment or default."""
        # Try to get from environment first
        api_url = os.getenv('API_BASE_URL')
        
        if api_url:
            return api_url.rstrip('/')
        
        # Default to localhost for development
        return "http://localhost:8000/api/v1"
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers if user is logged in."""
        headers = {}

        # For now, use a default user ID
        # TODO: Implement proper authentication with React frontend
        headers['X-User-ID'] = 'test_user_1'

        return headers
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> requests.Response:
        """Make GET request to API endpoint."""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_auth_headers()
        
        try:
            logger.debug(f"GET {url} with params: {params}")
            response = self.session.get(url, params=params, headers=headers)
            logger.debug(f"Response: {response.status_code}")
            return response
        
        except requests.exceptions.RequestException as e:
            logger.error(f"GET request failed: {e}")
            raise
    
    def post(self, endpoint: str, json: Optional[Dict[str, Any]] = None, data: Optional[Dict[str, Any]] = None) -> requests.Response:
        """Make POST request to API endpoint."""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_auth_headers()
        
        try:
            logger.debug(f"POST {url} with data: {json or data}")
            response = self.session.post(url, json=json, data=data, headers=headers)
            logger.debug(f"Response: {response.status_code}")
            return response
        
        except requests.exceptions.RequestException as e:
            logger.error(f"POST request failed: {e}")
            raise
    
    def put(self, endpoint: str, json: Optional[Dict[str, Any]] = None, data: Optional[Dict[str, Any]] = None) -> requests.Response:
        """Make PUT request to API endpoint."""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_auth_headers()
        
        try:
            logger.debug(f"PUT {url} with data: {json or data}")
            response = self.session.put(url, json=json, data=data, headers=headers)
            logger.debug(f"Response: {response.status_code}")
            return response
        
        except requests.exceptions.RequestException as e:
            logger.error(f"PUT request failed: {e}")
            raise
    
    def delete(self, endpoint: str) -> requests.Response:
        """Make DELETE request to API endpoint."""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_auth_headers()
        
        try:
            logger.debug(f"DELETE {url}")
            response = self.session.delete(url, headers=headers)
            logger.debug(f"Response: {response.status_code}")
            return response
        
        except requests.exceptions.RequestException as e:
            logger.error(f"DELETE request failed: {e}")
            raise
    
    def patch(self, endpoint: str, json: Optional[Dict[str, Any]] = None, data: Optional[Dict[str, Any]] = None) -> requests.Response:
        """Make PATCH request to API endpoint."""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_auth_headers()
        
        try:
            logger.debug(f"PATCH {url} with data: {json or data}")
            response = self.session.patch(url, json=json, data=data, headers=headers)
            logger.debug(f"Response: {response.status_code}")
            return response
        
        except requests.exceptions.RequestException as e:
            logger.error(f"PATCH request failed: {e}")
            raise
    
    def upload_file(self, endpoint: str, file_data: bytes, filename: str, field_name: str = 'file') -> requests.Response:
        """Upload file to API endpoint."""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_auth_headers()
        
        # Don't set Content-Type for file uploads, let requests handle it
        if 'Content-Type' in self.session.headers:
            del self.session.headers['Content-Type']
        
        files = {field_name: (filename, file_data)}
        
        try:
            logger.debug(f"POST {url} with file: {filename}")
            response = self.session.post(url, files=files, headers=headers)
            logger.debug(f"Response: {response.status_code}")
            return response
        
        except requests.exceptions.RequestException as e:
            logger.error(f"File upload failed: {e}")
            raise
        
        finally:
            # Restore Content-Type header
            self.session.headers.update({'Content-Type': 'application/json'})
    
    def download_file(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> requests.Response:
        """Download file from API endpoint."""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_auth_headers()
        
        try:
            logger.debug(f"GET {url} for file download")
            response = self.session.get(url, params=params, headers=headers, stream=True)
            logger.debug(f"Response: {response.status_code}")
            return response
        
        except requests.exceptions.RequestException as e:
            logger.error(f"File download failed: {e}")
            raise
    
    def health_check(self) -> bool:
        """Check if API is healthy and reachable."""
        try:
            response = self.get("/health")
            return response.status_code == 200
        except:
            return False
    
    def close(self):
        """Close the session."""
        self.session.close()


# Global API client instance
_api_client = None


def get_api_client() -> APIClient:
    """Get global API client instance."""
    global _api_client
    
    if _api_client is None:
        _api_client = APIClient()
    
    return _api_client


def set_api_base_url(base_url: str):
    """Set API base URL and reset global client."""
    global _api_client
    _api_client = APIClient(base_url)


# Convenience functions for common operations

def api_get(endpoint: str, params: Optional[Dict[str, Any]] = None) -> requests.Response:
    """Convenience function for GET requests."""
    return get_api_client().get(endpoint, params)


def api_post(endpoint: str, json: Optional[Dict[str, Any]] = None) -> requests.Response:
    """Convenience function for POST requests."""
    return get_api_client().post(endpoint, json=json)


def api_put(endpoint: str, json: Optional[Dict[str, Any]] = None) -> requests.Response:
    """Convenience function for PUT requests."""
    return get_api_client().put(endpoint, json=json)


def api_delete(endpoint: str) -> requests.Response:
    """Convenience function for DELETE requests."""
    return get_api_client().delete(endpoint)


def api_download(endpoint: str, params: Optional[Dict[str, Any]] = None) -> requests.Response:
    """Convenience function for file downloads."""
    return get_api_client().download_file(endpoint, params)


def api_upload(endpoint: str, file_data: bytes, filename: str) -> requests.Response:
    """Convenience function for file uploads."""
    return get_api_client().upload_file(endpoint, file_data, filename)


def is_api_available() -> bool:
    """Check if API is available."""
    return get_api_client().health_check()


# Context manager for API client
class APIClientContext:
    """Context manager for API client operations."""
    
    def __init__(self, base_url: Optional[str] = None):
        self.base_url = base_url
        self.client = None
    
    def __enter__(self) -> APIClient:
        self.client = APIClient(self.base_url)
        return self.client
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            self.client.close()


# Error handling utilities

def handle_api_error(response: requests.Response, operation: str = "API operation") -> bool:
    """Handle API response errors with user-friendly messages."""
    if response.status_code == 200:
        return True

    error_messages = {
        400: "Invalid request. Please check your input.",
        401: "Authentication required. Please log in.",
        403: "Access denied. You don't have permission for this operation.",
        404: "Resource not found.",
        422: "Validation error. Please check your input.",
        500: "Server error. Please try again later.",
        503: "Service unavailable. Please try again later."
    }

    message = error_messages.get(response.status_code, f"Unexpected error ({response.status_code})")

    try:
        error_detail = response.json().get('detail', message)
        logger.error(f"{operation} failed: {error_detail}")
    except:
        logger.error(f"{operation} failed: {message}")

    return False


def with_api_error_handling(func):
    """Decorator for API operations with error handling."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except requests.exceptions.ConnectionError:
            logger.error("Cannot connect to API server. Please check if the server is running.")
            return None
        except requests.exceptions.Timeout:
            logger.error("Request timed out. Please try again.")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in API operation: {e}")
            return None

    return wrapper
