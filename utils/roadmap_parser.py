"""
Parser for the Security Certification Roadmap HTML data
"""
import os
from bs4 import BeautifulSoup, Tag
import re
import logging
from typing import Dict, Any, Optional, List, Union

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_roadmap_html() -> Dict[str, Any]:
    """
    Parse the Security Certification Roadmap HTML to extract certification relationships
    and additional metadata
    """
    roadmap_path = os.path.join('submodules', 'SecCertRoadmapHTML', 'index.html')
    logger.info(f"Attempting to parse roadmap from {roadmap_path}")

    try:
        if not os.path.exists(roadmap_path):
            # Try alternate filename
            roadmap_path = os.path.join('submodules', 'SecCertRoadmapHTML', 'Security-Certification-Roadmap9.html')
            logger.info(f"Original path not found, trying alternate path: {roadmap_path}")
            if not os.path.exists(roadmap_path):
                logger.error("Could not find roadmap HTML file")
                return {}

        with open(roadmap_path, 'r', encoding='utf-8') as f:
            content = f.read()
            logger.info(f"Successfully read roadmap file, size: {len(content)} bytes")
            soup = BeautifulSoup(content, 'html.parser')

        certifications: Dict[str, Any] = {}

        # Find all certification elements
        cert_patterns = [
            'redopsitem', 'networkitem', 'mgmtitem', 'engineeritem',
            'blueopsitem', 'testitem', 'iamitem', 'assetitem'
        ]

        logger.info("Searching for certification elements...")
        total_elements = 0

        for pattern in cert_patterns:
            elements = soup.find_all('div', class_=lambda x: x and isinstance(x, str) and pattern in x.lower())
            logger.debug(f"Found {len(elements)} elements for pattern: {pattern}")
            total_elements += len(elements)

            for element in elements:
                # Extract basic information
                title = element.get('title', '')
                if not title:
                    continue

                # Clean up the certification name
                name = re.sub(r'\s+', ' ', title).strip()

                # Extract difficulty level from class
                classes = element.get('class', [])
                difficulty: Optional[int] = None
                domain: Optional[str] = None
                level: Optional[str] = None

                for cls in classes:
                    if isinstance(cls, str):
                        if cls.endswith(('1', '2', '3', '4')):
                            difficulty = int(cls[-1])
                        elif any(pattern in cls.lower() for pattern in cert_patterns):
                            domain = cls.replace('item', '').replace('1', '').replace('2', '').replace('3', '').replace('4', '')

                # Map difficulty to level
                level_map = {
                    1: 'Entry Level',
                    2: 'Intermediate', 
                    3: 'Advanced',
                    4: 'Expert'
                }
                level = level_map.get(difficulty) if difficulty else 'Entry Level'

                # Extract relationships from positioning and connections
                relationships: Dict[str, List[str]] = {
                    'prerequisites': [],
                    'leads_to': [],
                    'related': []
                }

                # Look for vertical connections (progression paths)
                y_pos = float(element.get('data-y', '0'))
                connected_elements = soup.find_all('div', 
                    class_=lambda x: x and isinstance(x, str) and any(p in x.lower() for p in cert_patterns))

                for conn in connected_elements:
                    conn_y = float(conn.get('data-y', '0'))
                    if conn_y > y_pos:  # Element is below current cert
                        related = conn.get('title', '').strip()
                        if related:
                            relationships['leads_to'].append(related)
                    elif conn_y < y_pos:  # Element is above current cert
                        related = conn.get('title', '').strip()
                        if related:
                            relationships['prerequisites'].append(related)

                # Look for horizontal connections (related certs)
                x_pos = float(element.get('data-x', '0'))
                for conn in connected_elements:
                    conn_x = float(conn.get('data-x', '0'))
                    if abs(conn_x - x_pos) < 100 and abs(float(conn.get('data-y', '0')) - y_pos) < 50:
                        related = conn.get('title', '').strip()
                        if related and related != name:
                            relationships['related'].append(related)

                # Remove empty relationships and deduplicate
                relationships = {k: list(set(v)) for k, v in relationships.items() if v}

                certifications[name] = {
                    'difficulty': difficulty,
                    'domain': domain,
                    'level': level,
                    'relationships': relationships,
                    'position': {'x': x_pos, 'y': y_pos}
                }

        logger.info(f"Found {total_elements} total elements")
        logger.info(f"Successfully parsed {len(certifications)} certifications with relationships")
        return certifications

    except Exception as e:
        logger.error(f"Error parsing roadmap HTML: {str(e)}")
        return {}

def merge_roadmap_data(base_certs: Dict[str, Any], roadmap_certs: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge the roadmap data with our existing certification data

    Args:
        base_certs: Original certification data
        roadmap_certs: Additional data from roadmap parsing

    Returns:
        Dictionary containing merged certification data
    """
    try:
        logger.info(f"Merging {len(base_certs)} base certifications with {len(roadmap_certs)} roadmap certifications")
        merged = {}

        for name, cert_data in base_certs.items():
            merged[name] = cert_data.copy()

            # If we have additional data from the roadmap, merge it
            if name in roadmap_certs:
                roadmap_data = roadmap_certs[name]

                # Update relationships
                merged[name]['related_certs'] = roadmap_data['relationships']

                # Add positioning data for visualization
                merged[name]['position'] = roadmap_data['position']

                # Update level and domain if not already set
                if 'level' not in merged[name] and roadmap_data['level']:
                    merged[name]['level'] = roadmap_data['level']

                if 'domain' not in merged[name] and roadmap_data['domain']:
                    merged[name]['domain'] = roadmap_data['domain']

        logger.info(f"Successfully merged data for {len(merged)} certifications")
        return merged

    except Exception as e:
        logger.error(f"Error during roadmap data merge: {str(e)}")
        return base_certs