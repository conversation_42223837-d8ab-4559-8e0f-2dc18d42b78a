#!/usr/bin/env python3
"""
Load certifications from normalized_certifications.json into the database
"""
import json
import sys
import os
from pathlib import Path

# Force SQLite database before importing any database modules
os.environ['DATABASE_URL'] = 'sqlite:///./certpathfinder.db'

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import init_db, get_db
from models.certification import Certification, Organization
from sqlalchemy.exc import IntegrityError
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_organization(db, org_name):
    """Create or get organization"""
    org = db.query(Organization).filter_by(name=org_name).first()
    if not org:
        # Map common organization names
        org_mapping = {
            'A+': 'CompTIA',
            'AWS': 'Amazon Web Services',
            'AZ-': 'Microsoft',
            'COMPTIA': 'CompTIA',
            'CISSP': 'ISC2',
            'CISM': 'ISACA',
            'CISA': 'ISACA',
            'CEH': 'EC-Council',
            'OSCP': 'Offensive Security',
            'GSEC': 'SANS/GIAC',
            'CCNA': 'Cisco',
            'Security+': 'CompTIA',
        }
        
        # Try to map the organization name
        mapped_name = org_name
        for key, value in org_mapping.items():
            if key.upper() in org_name.upper():
                mapped_name = value
                break
        
        org = Organization(
            name=mapped_name,
            description=f"Certification provider: {mapped_name}",
            website="",
            country="Unknown"
        )
        db.add(org)
        db.commit()  # Commit organization immediately
        logger.info(f"Created organization: {mapped_name}")
    return org

def load_certifications_from_json():
    """Load certifications from normalized JSON file"""
    json_path = project_root / "data" / "normalized_certifications.json"
    
    if not json_path.exists():
        logger.error(f"File not found: {json_path}")
        return []
    
    logger.info(f"Loading certifications from: {json_path}")
    
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"Loaded {len(data)} certifications from JSON")
    return data

def map_difficulty(difficulty_value):
    """Map difficulty from various formats to standard text"""
    if isinstance(difficulty_value, (int, float)):
        if difficulty_value <= 1:
            return "Beginner"
        elif difficulty_value <= 2:
            return "Intermediate"
        else:
            return "Advanced"
    elif isinstance(difficulty_value, str):
        difficulty_lower = difficulty_value.lower()
        if any(word in difficulty_lower for word in ['beginner', 'entry', 'basic', 'foundation']):
            return "Beginner"
        elif any(word in difficulty_lower for word in ['intermediate', 'associate', 'professional']):
            return "Intermediate"
        elif any(word in difficulty_lower for word in ['advanced', 'expert', 'senior', 'specialist']):
            return "Advanced"
    return "Intermediate"  # Default

def map_level(category_value):
    """Map category to certification level"""
    if not category_value:
        return "Associate"
    
    category_lower = category_value.lower()
    if any(word in category_lower for word in ['entry', 'foundation', 'basic']):
        return "Entry Level"
    elif any(word in category_lower for word in ['professional', 'associate']):
        return "Professional"
    elif any(word in category_lower for word in ['expert', 'advanced', 'specialist']):
        return "Expert"
    return "Associate"  # Default

def map_focus_to_domain(focus):
    """Map focus field to domain"""
    if not focus:
        return "Cybersecurity"
    
    focus_mapping = {
        'Engineer': 'Security Engineering',
        'Blueops': 'Defensive Security', 
        'Redops': 'Offensive Security',
        'Test': 'Security Testing',
        'Mgmt': 'Security Management',
        'Management': 'Security Management'
    }
    
    return focus_mapping.get(focus, focus)

def load_certifications_to_db():
    """Load certifications into database"""
    logger.info("Initializing database...")
    init_db()
    
    db = next(get_db())
    
    try:
        # Check if certifications already exist
        existing_count = db.query(Certification).count()
        if existing_count > 0:
            logger.info(f"Database already has {existing_count} certifications. Skipping load.")
            return existing_count
        
        # Load JSON data
        cert_data = load_certifications_from_json()
        if not cert_data:
            logger.error("No certification data to load")
            return 0
        
        success_count = 0
        error_count = 0
        organizations = {}
        
        logger.info(f"Processing {len(cert_data)} certifications...")
        
        for cert in cert_data:
            try:
                name = cert.get('name', '').strip()
                if not name:
                    logger.warning("Skipping certification with no name")
                    error_count += 1
                    continue
                
                # Extract organization name from certification name or use a default
                org_name = name.split()[0] if name else "Unknown"
                
                # Create or get organization
                if org_name not in organizations:
                    organizations[org_name] = create_organization(db, org_name)
                
                org = organizations[org_name]
                
                # Create certification
                certification = Certification(
                    name=name,
                    description=cert.get('description', ''),
                    level=map_level(cert.get('category')),
                    difficulty=map_difficulty(cert.get('difficulty')),
                    focus='Technical',  # Default focus
                    domain=map_focus_to_domain(cert.get('domain', cert.get('focus'))),
                    category=cert.get('category', 'General'),
                    organization_id=org.id,
                    cost=cert.get('cost'),
                    currency='USD',
                    exam_code=name.split()[0] if name else '',  # Use first part as exam code
                    validity_period=cert.get('validity_period', 36),
                    url=cert.get('url', ''),
                    prerequisites=','.join(cert.get('prerequisites', [])) if cert.get('prerequisites') else None,
                    is_active=True,
                    is_deleted=False
                )
                
                db.add(certification)
                db.commit()  # Commit immediately after each successful add
                success_count += 1

                if success_count % 100 == 0:
                    logger.info(f"Processed {success_count} certifications...")

            except IntegrityError as e:
                logger.warning(f"Skipping duplicate certification: {name}")
                db.rollback()
                error_count += 1
            except Exception as e:
                logger.error(f"Error processing certification {cert.get('name', 'Unknown')}: {str(e)}")
                db.rollback()
                error_count += 1
        
        logger.info(f"Successfully loaded {success_count} certifications")
        logger.info(f"Encountered {error_count} errors")
        
        return success_count
        
    except Exception as e:
        logger.error(f"Error loading certifications: {str(e)}")
        db.rollback()
        return 0
    finally:
        db.close()

if __name__ == "__main__":
    count = load_certifications_to_db()
    logger.info(f"Certification loading complete. Total loaded: {count}")
