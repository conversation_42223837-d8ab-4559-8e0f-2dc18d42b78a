# CertPathFinder - Database Schema & Data Model

## 🗄️ Database Overview

**Database Engine**: PostgreSQL 14+  
**ORM**: SQLAlchemy with Alembic migrations  
**Connection Pool**: 20 connections, 30 max overflow  
**Indexing Strategy**: Optimized for read-heavy workloads  

## 📊 Core Entity Relationships

```mermaid
erDiagram
    Organization ||--o{ Certification : "provides"
    Certification ||--o{ CostCalculation : "has_costs"
    Certification ||--o{ LearningPathItem : "included_in"
    UserExperience ||--o{ LearningPath : "creates"
    UserExperience ||--o{ StudySession : "logs"
    UserExperience ||--o{ LearningGoal : "sets"
    CareerRole ||--o{ CareerTransitionPath : "source_role"
    CareerRole ||--o{ CareerTransitionPath : "target_role"
    CareerTransitionPlan ||--o{ CareerTransitionStep : "contains"
    EnterpriseOrganization ||--o{ Department : "has"
    Department ||--o{ EnterpriseUser : "employs"
```

## 🏢 Core Tables

### 1. Organizations Table
```sql
CREATE TABLE organizations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    website VARCHAR(255),
    description TEXT,
    logo_url VARCHAR(255),
    industry VARCHAR(100),
    founded_year INTEGER,
    headquarters VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_organizations_name ON organizations(name);
CREATE INDEX idx_organizations_industry ON organizations(industry);
```

### 2. Certifications Table
```sql
CREATE TABLE certifications (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    category VARCHAR(100) NOT NULL,
    domain VARCHAR(50) NOT NULL,
    level VARCHAR(50) NOT NULL,
    focus VARCHAR(50),
    difficulty INTEGER NOT NULL CHECK (difficulty BETWEEN 1 AND 4),
    cost DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    description TEXT,
    prerequisites TEXT,
    validity_period INTEGER, -- months
    exam_code VARCHAR(50),
    organization_id INTEGER REFERENCES organizations(id) ON DELETE SET NULL,
    url VARCHAR(255),
    custom_hours INTEGER,
    study_notes TEXT,
    course_content TEXT,
    current_version INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_certifications_domain ON certifications(domain);
CREATE INDEX idx_certifications_level ON certifications(level);
CREATE INDEX idx_certifications_difficulty ON certifications(difficulty);
CREATE INDEX idx_certifications_cost ON certifications(cost);
CREATE INDEX idx_certifications_organization ON certifications(organization_id);
CREATE INDEX idx_certifications_active ON certifications(is_active);
CREATE INDEX idx_certifications_search ON certifications USING gin(to_tsvector('english', name || ' ' || description));
```

### 3. User Experience Table
```sql
CREATE TABLE user_experiences (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    years_experience INTEGER NOT NULL CHECK (years_experience >= 0),
    user_role VARCHAR(255) NOT NULL,
    desired_role VARCHAR(255) NOT NULL,
    expertise_areas JSONB NOT NULL DEFAULT '[]',
    preferred_learning_style VARCHAR(50) NOT NULL,
    study_time_available INTEGER NOT NULL CHECK (study_time_available > 0),
    tutorial_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_user_experiences_user_id ON user_experiences(user_id);
CREATE INDEX idx_user_experiences_role ON user_experiences(user_role);
CREATE INDEX idx_user_experiences_desired_role ON user_experiences(desired_role);
CREATE INDEX idx_user_experiences_expertise ON user_experiences USING gin(expertise_areas);
```

## 💰 Cost Management Tables

### 4. Cost Calculations Table
```sql
CREATE TABLE cost_calculations (
    id SERIAL PRIMARY KEY,
    certification_id INTEGER NOT NULL REFERENCES certifications(id) ON DELETE CASCADE,
    scenario_id INTEGER REFERENCES cost_scenarios(id) ON DELETE SET NULL,
    user_id VARCHAR(255),
    base_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    training_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    materials_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    retake_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_cost DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

CREATE TABLE cost_scenarios (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    cost_multipliers JSONB NOT NULL DEFAULT '{}',
    includes_training BOOLEAN DEFAULT FALSE,
    includes_materials BOOLEAN DEFAULT FALSE,
    includes_retakes BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE currency_rates (
    id SERIAL PRIMARY KEY,
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(10,6) NOT NULL,
    rate_date DATE NOT NULL,
    source VARCHAR(50) DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(from_currency, to_currency, rate_date)
);
```

## 🚀 Career Management Tables

### 5. Career Roles & Transitions
```sql
CREATE TABLE career_roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    security_domain VARCHAR(100),
    seniority_level VARCHAR(20) NOT NULL,
    required_skills JSONB DEFAULT '[]',
    preferred_certifications JSONB DEFAULT '[]',
    salary_range_min INTEGER,
    salary_range_max INTEGER,
    currency VARCHAR(3) DEFAULT 'USD',
    job_growth_rate DECIMAL(5,2),
    market_demand VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE career_transition_paths (
    id SERIAL PRIMARY KEY,
    source_role_id INTEGER REFERENCES career_roles(id) ON DELETE CASCADE,
    target_role_id INTEGER NOT NULL REFERENCES career_roles(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    difficulty_level VARCHAR(20) DEFAULT 'Medium',
    estimated_duration_months INTEGER NOT NULL,
    min_duration_months INTEGER,
    max_duration_months INTEGER,
    required_certifications JSONB DEFAULT '[]',
    recommended_certifications JSONB DEFAULT '[]',
    success_rate DECIMAL(5,4) DEFAULT 0.5000,
    total_cost_estimate DECIMAL(10,2),
    prerequisites JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE career_transition_plans (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    current_role_id INTEGER REFERENCES career_roles(id),
    target_role_id INTEGER NOT NULL REFERENCES career_roles(id),
    budget_min DECIMAL(10,2) DEFAULT 0.00,
    budget_max DECIMAL(10,2),
    budget_currency VARCHAR(3) DEFAULT 'USD',
    timeline_months INTEGER,
    max_timeline_months INTEGER,
    difficulty_preference VARCHAR(20) DEFAULT 'Medium',
    learning_style VARCHAR(50) DEFAULT 'Mixed',
    study_hours_per_week INTEGER DEFAULT 10,
    status VARCHAR(20) DEFAULT 'planning',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    selected_path_id INTEGER REFERENCES career_transition_paths(id),
    alternative_paths JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📚 Learning & Progress Tables

### 6. Learning Paths & Progress
```sql
CREATE TABLE learning_paths (
    id SERIAL PRIMARY KEY,
    user_experience_id INTEGER NOT NULL REFERENCES user_experiences(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    target_certification_id INTEGER REFERENCES certifications(id),
    estimated_duration_weeks INTEGER,
    difficulty_level VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE study_sessions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    certification_id INTEGER REFERENCES certifications(id),
    session_date TIMESTAMP NOT NULL,
    duration_minutes INTEGER NOT NULL CHECK (duration_minutes > 0),
    topics_covered JSONB DEFAULT '[]',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    notes TEXT,
    session_type VARCHAR(20) DEFAULT 'Study',
    effectiveness_rating INTEGER CHECK (effectiveness_rating BETWEEN 1 AND 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE learning_goals (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    certification_id INTEGER REFERENCES certifications(id),
    goal_type VARCHAR(50) NOT NULL,
    target_date DATE,
    current_progress DECIMAL(5,2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'Active',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE achievements (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    achievement_type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    points INTEGER DEFAULT 0,
    badge_url VARCHAR(255),
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    certification_id INTEGER REFERENCES certifications(id)
);
```

## 🏢 Enterprise Tables

### 7. Enterprise Management
```sql
CREATE TABLE enterprise_organizations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    domain VARCHAR(100),
    industry VARCHAR(100),
    size_category VARCHAR(50),
    subscription_tier VARCHAR(50),
    settings JSONB DEFAULT '{}',
    billing_contact_email VARCHAR(255),
    technical_contact_email VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    organization_id INTEGER NOT NULL REFERENCES enterprise_organizations(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    manager_user_id VARCHAR(255),
    budget_allocated DECIMAL(12,2) DEFAULT 0.00,
    budget_currency VARCHAR(3) DEFAULT 'USD',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE enterprise_users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    organization_id INTEGER NOT NULL REFERENCES enterprise_organizations(id) ON DELETE CASCADE,
    department_id INTEGER REFERENCES departments(id) ON DELETE SET NULL,
    employee_id VARCHAR(100),
    role VARCHAR(100),
    manager_user_id VARCHAR(255),
    hire_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔍 Indexing Strategy

### Performance Indexes
```sql
-- Certification search and filtering
CREATE INDEX idx_cert_domain_level ON certifications(domain, level);
CREATE INDEX idx_cert_cost_range ON certifications(cost) WHERE cost IS NOT NULL;
CREATE INDEX idx_cert_fulltext ON certifications USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- User activity tracking
CREATE INDEX idx_study_sessions_user_date ON study_sessions(user_id, session_date DESC);
CREATE INDEX idx_learning_goals_user_status ON learning_goals(user_id, status);

-- Career planning
CREATE INDEX idx_career_paths_roles ON career_transition_paths(source_role_id, target_role_id);
CREATE INDEX idx_career_plans_user ON career_transition_plans(user_id, status);

-- Enterprise queries
CREATE INDEX idx_enterprise_users_org ON enterprise_users(organization_id, is_active);
CREATE INDEX idx_departments_org ON departments(organization_id);
```

### Composite Indexes for Complex Queries
```sql
-- Multi-column indexes for common filter combinations
CREATE INDEX idx_cert_domain_level_difficulty ON certifications(domain, level, difficulty);
CREATE INDEX idx_cert_active_cost ON certifications(is_active, cost) WHERE is_active = TRUE;

-- Time-series indexes for analytics
CREATE INDEX idx_study_sessions_date_user ON study_sessions(session_date, user_id);
CREATE INDEX idx_cost_calc_date_cert ON cost_calculations(calculation_date, certification_id);
```

## 🔒 Security & Constraints

### Data Integrity Constraints
```sql
-- Ensure valid difficulty ratings
ALTER TABLE certifications ADD CONSTRAINT chk_difficulty 
    CHECK (difficulty BETWEEN 1 AND 4);

-- Ensure positive costs
ALTER TABLE certifications ADD CONSTRAINT chk_cost_positive 
    CHECK (cost IS NULL OR cost >= 0);

-- Ensure valid progress percentages
ALTER TABLE learning_goals ADD CONSTRAINT chk_progress_range 
    CHECK (current_progress BETWEEN 0.00 AND 100.00);

-- Ensure valid study time
ALTER TABLE user_experiences ADD CONSTRAINT chk_study_time_positive 
    CHECK (study_time_available > 0);
```

### Row Level Security (RLS)
```sql
-- Enable RLS for user data
ALTER TABLE user_experiences ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_goals ENABLE ROW LEVEL SECURITY;

-- Policies for user data access
CREATE POLICY user_data_policy ON user_experiences
    FOR ALL TO authenticated_users
    USING (user_id = current_setting('app.current_user_id'));
```

## 📊 Data Archival & Retention

### Archival Strategy
```sql
-- Archive old study sessions (keep 2 years)
CREATE TABLE study_sessions_archive (LIKE study_sessions INCLUDING ALL);

-- Partition tables by date for better performance
CREATE TABLE study_sessions_2024 PARTITION OF study_sessions
    FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### Soft Delete Pattern
```sql
-- Add soft delete columns to key tables
ALTER TABLE certifications ADD COLUMN deleted_at TIMESTAMP;
ALTER TABLE user_experiences ADD COLUMN deleted_at TIMESTAMP;

-- Indexes for soft delete queries
CREATE INDEX idx_cert_not_deleted ON certifications(id) WHERE deleted_at IS NULL;
CREATE INDEX idx_user_not_deleted ON user_experiences(user_id) WHERE deleted_at IS NULL;
```

This database schema provides a robust foundation for the CertPathFinder platform, optimized for performance, scalability, and data integrity while supporting all core business functions.
