# Technical Specifications: CRUD & Data Management System

## 1. API Endpoint Specifications

### 1.1 Job Types CRUD API

#### **GET /api/v1/job-types**
**Purpose**: List job types with advanced filtering and pagination

**Query Parameters:**
```typescript
interface JobTypeListParams {
  // Pagination
  page?: number = 1;
  page_size?: number = 20;

  // Filtering
  security_area?: SecurityAreaEnum[];
  seniority_level?: SeniorityLevelEnum[];
  job_family?: JobFamilyEnum[];
  salary_min?: number;
  salary_max?: number;
  location?: string[];
  remote_friendly?: boolean;

  // Search
  search?: string;
  search_fields?: ('title' | 'description' | 'skills')[];

  // Sorting
  sort_by?: 'title' | 'salary_min' | 'demand_level' | 'created_at';
  sort_order?: 'asc' | 'desc';

  // Data enrichment
  include_related?: boolean;
  include_market_data?: boolean;
  include_career_paths?: boolean;
}
```

**Response Schema:**
```typescript
interface JobTypeListResponse {
  data: JobTypeResponse[];
  pagination: {
    page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
    has_next: boolean;
    has_previous: boolean;
  };
  filters_applied: JobTypeListParams;
  aggregations: {
    security_areas: { [key: string]: number };
    seniority_levels: { [key: string]: number };
    salary_ranges: { [key: string]: number };
  };
}
```

#### **POST /api/v1/job-types**
**Purpose**: Create new job type with validation

**Request Schema:**
```typescript
interface JobTypeCreateRequest {
  // Basic information
  title: string;
  description: string;
  security_area: SecurityAreaEnum;
  job_family: JobFamilyEnum;
  seniority_level: SeniorityLevelEnum;

  // Requirements
  required_skills: string[];
  preferred_skills: string[];
  required_certifications: string[];
  preferred_certifications: string[];
  education_requirements: string[];
  experience_years_min: number;
  experience_years_max?: number;

  // Compensation
  salary_min?: number;
  salary_max?: number;
  salary_currency: string = 'USD';

  // Work details
  remote_friendly: boolean = false;
  travel_requirements?: string;
  security_clearance_required?: string;

  // Market data
  demand_level: DemandLevelEnum;
  growth_outlook: GrowthOutlookEnum;

  // Career progression
  career_progression_from: number[];
  career_progression_to: number[];

  // Metadata
  tags: string[];
  is_active: boolean = true;
}
```

#### **PUT /api/v1/job-types/{id}**
**Purpose**: Update existing job type with change tracking

**Request Schema:** Same as create with optional fields
**Additional Features:**
- Change tracking with audit trail
- Validation against existing data
- Automatic versioning
- Conflict resolution for concurrent edits

#### **DELETE /api/v1/job-types/{id}**
**Purpose**: Soft delete with dependency management

**Query Parameters:**
```typescript
interface JobTypeDeleteParams {
  force?: boolean = false;  // Hard delete (admin only)
  cascade?: boolean = false;  // Delete related data
  replacement_id?: number;  // Migrate references
}
```

### 1.2 Certifications CRUD API

#### **GET /api/v1/certifications**
**Purpose**: Advanced certification search and filtering

**Query Parameters:**
```typescript
interface CertificationListParams {
  // Pagination
  page?: number = 1;
  page_size?: number = 20;

  // Filtering
  domain?: string[];
  level?: ('Entry Level' | 'Associate' | 'Professional' | 'Expert')[];
  focus?: string[];
  organization_id?: number[];
  cost_min?: number;
  cost_max?: number;
  difficulty?: (1 | 2 | 3 | 4)[];
  validity_period?: number[];

  // Search
  search?: string;
  search_fields?: ('name' | 'description' | 'prerequisites')[];

  // Sorting
  sort_by?: 'name' | 'cost' | 'difficulty' | 'popularity' | 'created_at';
  sort_order?: 'asc' | 'desc';

  // Data enrichment
  include_organization?: boolean;
  include_prerequisites?: boolean;
  include_career_paths?: boolean;
  include_market_data?: boolean;
  include_study_materials?: boolean;
}
```

#### **POST /api/v1/certifications**
**Purpose**: Create certification with comprehensive validation

**Request Schema:**
```typescript
interface CertificationCreateRequest {
  // Basic information
  name: string;
  category: string;
  domain: string;
  level: string;
  focus: string;
  description: string;

  // Organization
  organization_id: number;
  exam_code?: string;
  url?: string;

  // Requirements
  prerequisites: string[];
  education_requirements: string[];
  experience_requirements: string[];

  // Exam details
  exam_format: string;
  exam_duration_minutes?: number;
  passing_score?: number;
  question_count?: number;

  // Costs
  cost?: number;
  cost_currency: string = 'USD';
  retake_cost?: number;

  // Study information
  difficulty: 1 | 2 | 3 | 4;
  estimated_study_hours?: number;
  custom_hours?: number;

  // Validity
  validity_period?: number;  // months
  renewal_requirements?: string[];

  // Relationships
  related_certs: number[];
  prerequisite_certs: number[];

  // Metadata
  tags: string[];
  study_notes?: string;
  is_active: boolean = true;
}
```

### 1.3 Career Paths CRUD API

#### **GET /api/v1/career-paths**
**Purpose**: Retrieve career paths with relationship data

**Query Parameters:**
```typescript
interface CareerPathListParams {
  // Pagination
  page?: number = 1;
  page_size?: number = 20;

  // Filtering
  security_area?: SecurityAreaEnum[];
  entry_level?: SeniorityLevelEnum[];
  target_level?: SeniorityLevelEnum[];
  duration_min?: number;  // years
  duration_max?: number;  // years
  success_rate_min?: number;  // 0-1

  // Search
  search?: string;

  // Sorting
  sort_by?: 'name' | 'duration' | 'success_rate' | 'popularity';
  sort_order?: 'asc' | 'desc';

  // Data enrichment
  include_steps?: boolean;
  include_certifications?: boolean;
  include_analytics?: boolean;
}
```

#### **POST /api/v1/career-paths**
**Purpose**: Create career path with validation

**Request Schema:**
```typescript
interface CareerPathCreateRequest {
  // Basic information
  name: string;
  description: string;
  security_area: SecurityAreaEnum;

  // Path definition
  entry_job_types: number[];
  progression_stages: CareerStage[];
  terminal_job_types: number[];

  // Requirements
  typical_duration_years: number;
  required_certifications: string[];
  recommended_education: string[];

  // Success metrics
  success_rate?: number;
  average_salary_growth?: number;

  // Metadata
  tags: string[];
  is_active: boolean = true;
}

interface CareerStage {
  stage_name: string;
  description: string;
  job_types: number[];
  required_skills: string[];
  certifications: string[];
  duration_months: number;
  success_criteria: string[];
}
```

## 2. Database Schema Enhancements

### 2.1 Indexing Strategy

```sql
-- Job Types Indexes
CREATE INDEX idx_job_types_security_area ON security_job_types(security_area);
CREATE INDEX idx_job_types_seniority ON security_job_types(seniority_level);
CREATE INDEX idx_job_types_salary ON security_job_types(salary_min, salary_max);
CREATE INDEX idx_job_types_search ON security_job_types USING gin(to_tsvector('english', title || ' ' || description));
CREATE INDEX idx_job_types_active ON security_job_types(is_active) WHERE is_active = true;

-- Certifications Indexes
CREATE INDEX idx_certifications_domain ON certifications(domain);
CREATE INDEX idx_certifications_level ON certifications(level);
CREATE INDEX idx_certifications_cost ON certifications(cost);
CREATE INDEX idx_certifications_search ON certifications USING gin(to_tsvector('english', name || ' ' || description));
CREATE INDEX idx_certifications_org ON certifications(organization_id);

-- Career Paths Indexes
CREATE INDEX idx_career_paths_area ON security_career_paths(security_area);
CREATE INDEX idx_career_paths_duration ON security_career_paths(typical_duration_years);
CREATE INDEX idx_career_paths_success ON security_career_paths(success_rate);
```

### 2.2 Audit Trail Schema

```sql
CREATE TABLE audit_log (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id INTEGER NOT NULL,
    operation VARCHAR(10) NOT NULL,  -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    user_id VARCHAR(100),
    user_role VARCHAR(50),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100)
);

CREATE INDEX idx_audit_log_table_record ON audit_log(table_name, record_id);
CREATE INDEX idx_audit_log_timestamp ON audit_log(timestamp);
CREATE INDEX idx_audit_log_user ON audit_log(user_id);
```

### 2.3 Search Enhancement Schema

```sql
-- Full-text search configuration
CREATE TEXT SEARCH CONFIGURATION certpathfinder (COPY = english);

-- Search analytics
CREATE TABLE search_analytics (
    id SERIAL PRIMARY KEY,
    query TEXT NOT NULL,
    entity_type VARCHAR(50),
    filters_applied JSONB,
    results_count INTEGER,
    user_id VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    response_time_ms INTEGER,
    clicked_results INTEGER[]
);
```

## 3. Service Layer Architecture

### 3.1 Base CRUD Service

```python
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, List, Optional, Dict, Any
from sqlalchemy.orm import Session
from pydantic import BaseModel

T = TypeVar('T')  # Database model
CreateSchema = TypeVar('CreateSchema', bound=BaseModel)
UpdateSchema = TypeVar('UpdateSchema', bound=BaseModel)
ResponseSchema = TypeVar('ResponseSchema', bound=BaseModel)

class BaseCRUDService(Generic[T, CreateSchema, UpdateSchema, ResponseSchema], ABC):
    """Base CRUD service with common operations."""

    def __init__(self, db: Session, model: type[T]):
        self.db = db
        self.model = model

    @abstractmethod
    def create(self, obj_in: CreateSchema, user_id: str) -> ResponseSchema:
        """Create new entity with audit trail."""
        pass

    @abstractmethod
    def get(self, id: int, include_related: bool = False) -> Optional[ResponseSchema]:
        """Get entity by ID with optional related data."""
        pass

    @abstractmethod
    def list(self, filters: Dict[str, Any], pagination: Dict[str, int]) -> Dict[str, Any]:
        """List entities with filtering and pagination."""
        pass

    @abstractmethod
    def update(self, id: int, obj_in: UpdateSchema, user_id: str) -> ResponseSchema:
        """Update entity with change tracking."""
        pass

    @abstractmethod
    def delete(self, id: int, user_id: str, soft: bool = True) -> bool:
        """Delete entity with audit trail."""
        pass

    def bulk_create(self, objects: List[CreateSchema], user_id: str) -> List[ResponseSchema]:
        """Bulk create with transaction management."""
        pass

    def bulk_update(self, updates: List[Dict[str, Any]], user_id: str) -> List[ResponseSchema]:
        """Bulk update with validation."""
        pass

    def search(self, query: str, filters: Dict[str, Any]) -> List[ResponseSchema]:
        """Full-text search with filtering."""
        pass
```

### 3.2 Job Types Service Implementation

```python
class JobTypeService(BaseCRUDService[SecurityJobType, JobTypeCreateRequest, JobTypeUpdateRequest, JobTypeResponse]):
    """Service for job type CRUD operations."""

    def __init__(self, db: Session):
        super().__init__(db, SecurityJobType)
        self.audit_service = AuditService(db)
        self.search_service = SearchService(db)

    def create(self, obj_in: JobTypeCreateRequest, user_id: str) -> JobTypeResponse:
        """Create job type with validation and audit trail."""
        # Validate business rules
        self._validate_job_type(obj_in)

        # Create database object
        db_obj = SecurityJobType(**obj_in.dict())
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)

        # Log audit trail
        self.audit_service.log_create(
            table_name='security_job_types',
            record_id=db_obj.id,
            new_values=obj_in.dict(),
            user_id=user_id
        )

        return JobTypeResponse.from_orm(db_obj)

    def list(self, filters: JobTypeListParams, pagination: PaginationParams) -> JobTypeListResponse:
        """List job types with advanced filtering."""
        query = self.db.query(SecurityJobType)

        # Apply filters
        query = self._apply_filters(query, filters)

        # Get total count for pagination
        total_count = query.count()

        # Apply sorting
        query = self._apply_sorting(query, filters.sort_by, filters.sort_order)

        # Apply pagination
        offset = (pagination.page - 1) * pagination.page_size
        query = query.offset(offset).limit(pagination.page_size)

        # Execute query
        job_types = query.all()

        # Generate aggregations
        aggregations = self._generate_aggregations(filters)

        return JobTypeListResponse(
            data=[JobTypeResponse.from_orm(jt) for jt in job_types],
            pagination=self._build_pagination_info(pagination, total_count),
            filters_applied=filters,
            aggregations=aggregations
        )

    def _validate_job_type(self, obj_in: JobTypeCreateRequest) -> None:
        """Validate job type business rules."""
        # Check for duplicate titles in same security area
        existing = self.db.query(SecurityJobType).filter(
            SecurityJobType.title == obj_in.title,
            SecurityJobType.security_area == obj_in.security_area,
            SecurityJobType.is_active == True
        ).first()

        if existing:
            raise ValueError(f"Job type '{obj_in.title}' already exists in {obj_in.security_area}")

        # Validate salary range
        if obj_in.salary_min and obj_in.salary_max:
            if obj_in.salary_min > obj_in.salary_max:
                raise ValueError("Minimum salary cannot be greater than maximum salary")

        # Validate career progression references
        if obj_in.career_progression_from:
            self._validate_job_type_references(obj_in.career_progression_from)

        if obj_in.career_progression_to:
            self._validate_job_type_references(obj_in.career_progression_to)
```

## 4. Frontend Interface Specifications

### 4.1 Job Types Management Interface

#### **4.1.1 List View Component**
```typescript
interface JobTypesListProps {
  filters: JobTypeFilters;
  onFilterChange: (filters: JobTypeFilters) => void;
  onJobTypeSelect: (jobType: JobTypeResponse) => void;
  onBulkAction: (action: string, selectedIds: number[]) => void;
}

const JobTypesList: React.FC<JobTypesListProps> = ({
  filters,
  onFilterChange,
  onJobTypeSelect,
  onBulkAction
}) => {
  const [selectedJobTypes, setSelectedJobTypes] = useState<number[]>([]);
  const [sortConfig, setSortConfig] = useState<SortConfig>();

  // Advanced filtering panel
  const FilterPanel = () => (
    <div className="filter-panel">
      <SearchInput
        value={filters.search}
        onChange={(value) => onFilterChange({...filters, search: value})}
        placeholder="Search job types..."
      />

      <MultiSelect
        label="Security Areas"
        options={SECURITY_AREAS}
        value={filters.security_area}
        onChange={(value) => onFilterChange({...filters, security_area: value})}
      />

      <RangeSlider
        label="Salary Range"
        min={0}
        max={300000}
        value={[filters.salary_min, filters.salary_max]}
        onChange={([min, max]) => onFilterChange({...filters, salary_min: min, salary_max: max})}
      />

      <Toggle
        label="Remote Friendly"
        checked={filters.remote_friendly}
        onChange={(checked) => onFilterChange({...filters, remote_friendly: checked})}
      />
    </div>
  );

  return (
    <div className="job-types-list">
      <FilterPanel />

      <DataTable
        data={jobTypes}
        columns={JOB_TYPE_COLUMNS}
        sortConfig={sortConfig}
        onSort={setSortConfig}
        selectable
        selectedRows={selectedJobTypes}
        onSelectionChange={setSelectedJobTypes}
        onRowClick={onJobTypeSelect}
      />

      <BulkActionBar
        selectedCount={selectedJobTypes.length}
        actions={BULK_ACTIONS}
        onAction={onBulkAction}
      />

      <Pagination
        currentPage={pagination.page}
        totalPages={pagination.total_pages}
        onPageChange={handlePageChange}
      />
    </div>
  );
};
```

#### **4.1.2 Create/Edit Form Component**
```typescript
interface JobTypeFormProps {
  jobType?: JobTypeResponse;
  onSave: (data: JobTypeCreateRequest) => Promise<void>;
  onCancel: () => void;
}

const JobTypeForm: React.FC<JobTypeFormProps> = ({
  jobType,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState<JobTypeCreateRequest>(
    jobType ? mapToCreateRequest(jobType) : getDefaultFormData()
  );
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Client-side validation
    const errors = validateJobTypeForm(formData);
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      // Handle server validation errors
      setValidationErrors(error.validationErrors);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="job-type-form">
      <FormSection title="Basic Information">
        <TextInput
          label="Job Title"
          value={formData.title}
          onChange={(value) => setFormData({...formData, title: value})}
          error={validationErrors.title}
          required
        />

        <TextArea
          label="Description"
          value={formData.description}
          onChange={(value) => setFormData({...formData, description: value})}
          error={validationErrors.description}
          rows={4}
          required
        />

        <Select
          label="Security Area"
          options={SECURITY_AREA_OPTIONS}
          value={formData.security_area}
          onChange={(value) => setFormData({...formData, security_area: value})}
          error={validationErrors.security_area}
          required
        />
      </FormSection>

      <FormSection title="Requirements">
        <SkillsSelector
          label="Required Skills"
          value={formData.required_skills}
          onChange={(skills) => setFormData({...formData, required_skills: skills})}
          suggestions={SKILLS_SUGGESTIONS}
        />

        <CertificationSelector
          label="Required Certifications"
          value={formData.required_certifications}
          onChange={(certs) => setFormData({...formData, required_certifications: certs})}
        />
      </FormSection>

      <FormSection title="Compensation">
        <SalaryRangeInput
          minValue={formData.salary_min}
          maxValue={formData.salary_max}
          currency={formData.salary_currency}
          onMinChange={(value) => setFormData({...formData, salary_min: value})}
          onMaxChange={(value) => setFormData({...formData, salary_max: value})}
          onCurrencyChange={(currency) => setFormData({...formData, salary_currency: currency})}
        />
      </FormSection>

      <FormActions>
        <Button type="button" variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" variant="primary">
          {jobType ? 'Update' : 'Create'} Job Type
        </Button>
      </FormActions>
    </form>
  );
};
```

### 4.2 Search & Discovery Interface

#### **4.2.1 Global Search Component**
```typescript
interface GlobalSearchProps {
  onResultSelect: (result: SearchResult) => void;
  placeholder?: string;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({
  onResultSelect,
  placeholder = "Search certifications, job types, career paths..."
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const debouncedSearch = useDebounce(async (searchQuery: string) => {
    if (searchQuery.length < 2) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await searchAPI.globalSearch({
        query: searchQuery,
        include_suggestions: true,
        max_results: 10
      });
      setResults(response.results);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsLoading(false);
    }
  }, 300);

  useEffect(() => {
    debouncedSearch(query);
  }, [query]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && results[selectedIndex]) {
          onResultSelect(results[selectedIndex]);
          setQuery('');
          setResults([]);
        }
        break;
      case 'Escape':
        setQuery('');
        setResults([]);
        setSelectedIndex(-1);
        break;
    }
  };

  return (
    <div className="global-search">
      <SearchInput
        value={query}
        onChange={setQuery}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        icon={<SearchIcon />}
        loading={isLoading}
      />

      {results.length > 0 && (
        <SearchResults
          results={results}
          selectedIndex={selectedIndex}
          onResultClick={onResultSelect}
          onMouseEnter={(index) => setSelectedIndex(index)}
        />
      )}
    </div>
  );
};

interface SearchResult {
  id: string;
  type: 'job_type' | 'certification' | 'career_path' | 'skill';
  title: string;
  description: string;
  metadata: Record<string, any>;
  relevance_score: number;
  highlighted_fields: Record<string, string>;
}
```

#### **4.2.2 Advanced Filter Panel**
```typescript
interface AdvancedFiltersProps {
  entityType: 'job_types' | 'certifications' | 'career_paths';
  filters: Record<string, any>;
  onFiltersChange: (filters: Record<string, any>) => void;
  onReset: () => void;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  entityType,
  filters,
  onFiltersChange,
  onReset
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const filterConfig = getFilterConfig(entityType);

  const updateFilter = (key: string, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const renderFilterControl = (config: FilterConfig) => {
    switch (config.type) {
      case 'multiselect':
        return (
          <MultiSelect
            label={config.label}
            options={config.options}
            value={filters[config.key] || []}
            onChange={(value) => updateFilter(config.key, value)}
            placeholder={config.placeholder}
          />
        );

      case 'range':
        return (
          <RangeSlider
            label={config.label}
            min={config.min}
            max={config.max}
            value={filters[config.key] || [config.min, config.max]}
            onChange={(value) => updateFilter(config.key, value)}
            formatValue={config.formatValue}
          />
        );

      case 'toggle':
        return (
          <Toggle
            label={config.label}
            checked={filters[config.key] || false}
            onChange={(checked) => updateFilter(config.key, checked)}
          />
        );

      case 'date_range':
        return (
          <DateRangePicker
            label={config.label}
            value={filters[config.key]}
            onChange={(value) => updateFilter(config.key, value)}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="advanced-filters">
      <div className="filter-header">
        <h3>Filters</h3>
        <div className="filter-actions">
          <Button
            variant="text"
            size="small"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Less' : 'More'} Filters
          </Button>
          <Button
            variant="text"
            size="small"
            onClick={onReset}
          >
            Reset
          </Button>
        </div>
      </div>

      <div className="filter-controls">
        {filterConfig.primary.map((config) => (
          <div key={config.key} className="filter-control">
            {renderFilterControl(config)}
          </div>
        ))}

        {isExpanded && filterConfig.secondary.map((config) => (
          <div key={config.key} className="filter-control">
            {renderFilterControl(config)}
          </div>
        ))}
      </div>

      <FilterSummary
        filters={filters}
        onRemoveFilter={(key) => updateFilter(key, undefined)}
      />
    </div>
  );
};
```

## 5. Data Validation & Business Rules

### 5.1 Validation Schemas

```python
from pydantic import BaseModel, validator, Field
from typing import List, Optional
import re

class JobTypeValidation:
    """Business rules and validation for job types."""

    @staticmethod
    def validate_title(title: str) -> str:
        """Validate job title format and content."""
        if not title or len(title.strip()) < 3:
            raise ValueError("Job title must be at least 3 characters long")

        if len(title) > 200:
            raise ValueError("Job title cannot exceed 200 characters")

        # Check for inappropriate content
        inappropriate_words = ['test', 'dummy', 'fake']
        if any(word in title.lower() for word in inappropriate_words):
            raise ValueError("Job title contains inappropriate content")

        return title.strip()

    @staticmethod
    def validate_salary_range(salary_min: Optional[float], salary_max: Optional[float]) -> tuple:
        """Validate salary range consistency."""
        if salary_min is not None and salary_min < 0:
            raise ValueError("Minimum salary cannot be negative")

        if salary_max is not None and salary_max < 0:
            raise ValueError("Maximum salary cannot be negative")

        if salary_min is not None and salary_max is not None:
            if salary_min > salary_max:
                raise ValueError("Minimum salary cannot exceed maximum salary")

            if salary_max > 1000000:  # $1M cap
                raise ValueError("Maximum salary exceeds reasonable limit")

        return salary_min, salary_max

    @staticmethod
    def validate_skills(skills: List[str]) -> List[str]:
        """Validate and normalize skills list."""
        if not skills:
            return []

        # Remove duplicates and normalize
        normalized_skills = []
        seen = set()

        for skill in skills:
            skill = skill.strip()
            if skill and skill.lower() not in seen:
                normalized_skills.append(skill)
                seen.add(skill.lower())

        if len(normalized_skills) > 50:
            raise ValueError("Cannot specify more than 50 skills")

        return normalized_skills

class CertificationValidation:
    """Business rules and validation for certifications."""

    @staticmethod
    def validate_exam_code(exam_code: Optional[str]) -> Optional[str]:
        """Validate exam code format."""
        if not exam_code:
            return None

        # Common exam code patterns
        patterns = [
            r'^[A-Z]{2,5}-\d{3,4}$',  # e.g., CISSP-001, SEC+-501
            r'^[A-Z]{3,10}$',         # e.g., CISSP, CISM
            r'^[A-Z]{2,5}\d{3,4}$'    # e.g., CS0001, SY0601
        ]

        if not any(re.match(pattern, exam_code.upper()) for pattern in patterns):
            raise ValueError("Invalid exam code format")

        return exam_code.upper()

    @staticmethod
    def validate_cost(cost: Optional[float], currency: str = 'USD') -> Optional[float]:
        """Validate certification cost."""
        if cost is None:
            return None

        if cost < 0:
            raise ValueError("Cost cannot be negative")

        # Currency-specific validation
        max_costs = {
            'USD': 5000,
            'EUR': 4500,
            'GBP': 4000,
            'JPY': 500000
        }

        max_cost = max_costs.get(currency, 5000)
        if cost > max_cost:
            raise ValueError(f"Cost exceeds maximum for {currency}: {max_cost}")

        return round(cost, 2)

    @staticmethod
    def validate_difficulty(difficulty: int) -> int:
        """Validate difficulty level."""
        if difficulty not in [1, 2, 3, 4]:
            raise ValueError("Difficulty must be between 1 and 4")

        return difficulty

class CareerPathValidation:
    """Business rules and validation for career paths."""

    @staticmethod
    def validate_progression_stages(stages: List[dict]) -> List[dict]:
        """Validate career progression stages."""
        if not stages:
            raise ValueError("Career path must have at least one stage")

        if len(stages) > 10:
            raise ValueError("Career path cannot have more than 10 stages")

        total_duration = 0
        for i, stage in enumerate(stages):
            # Validate stage structure
            required_fields = ['stage_name', 'duration_months', 'job_types']
            for field in required_fields:
                if field not in stage:
                    raise ValueError(f"Stage {i+1} missing required field: {field}")

            # Validate duration
            duration = stage['duration_months']
            if not isinstance(duration, int) or duration < 1 or duration > 60:
                raise ValueError(f"Stage {i+1} duration must be between 1 and 60 months")

            total_duration += duration

        if total_duration > 240:  # 20 years max
            raise ValueError("Total career path duration cannot exceed 20 years")

        return stages

    @staticmethod
    def validate_success_rate(success_rate: Optional[float]) -> Optional[float]:
        """Validate success rate."""
        if success_rate is None:
            return None

        if not 0 <= success_rate <= 1:
            raise ValueError("Success rate must be between 0 and 1")

        return round(success_rate, 3)
```

### 5.2 Business Rules Engine

```python
class BusinessRulesEngine:
    """Centralized business rules validation."""

    def __init__(self, db: Session):
        self.db = db

    def validate_job_type_creation(self, job_type: JobTypeCreateRequest) -> List[str]:
        """Validate job type creation business rules."""
        errors = []

        # Check for duplicate titles in same security area
        existing = self.db.query(SecurityJobType).filter(
            SecurityJobType.title.ilike(f"%{job_type.title}%"),
            SecurityJobType.security_area == job_type.security_area,
            SecurityJobType.is_active == True
        ).first()

        if existing:
            errors.append(f"Similar job type already exists: {existing.title}")

        # Validate career progression references
        if job_type.career_progression_from:
            invalid_refs = self._validate_job_type_references(job_type.career_progression_from)
            if invalid_refs:
                errors.append(f"Invalid career progression references: {invalid_refs}")

        # Check skill requirements consistency
        if job_type.required_skills and job_type.preferred_skills:
            overlap = set(job_type.required_skills) & set(job_type.preferred_skills)
            if overlap:
                errors.append(f"Skills cannot be both required and preferred: {overlap}")

        return errors

    def validate_certification_creation(self, cert: CertificationCreateRequest) -> List[str]:
        """Validate certification creation business rules."""
        errors = []

        # Check for duplicate certifications
        existing = self.db.query(Certification).filter(
            Certification.name.ilike(f"%{cert.name}%"),
            Certification.organization_id == cert.organization_id,
            Certification.is_active == True
        ).first()

        if existing:
            errors.append(f"Similar certification already exists: {existing.name}")

        # Validate exam code uniqueness
        if cert.exam_code:
            existing_code = self.db.query(Certification).filter(
                Certification.exam_code == cert.exam_code,
                Certification.is_active == True
            ).first()

            if existing_code:
                errors.append(f"Exam code already in use: {cert.exam_code}")

        # Validate prerequisite certifications exist
        if cert.prerequisite_certs:
            invalid_prereqs = self._validate_certification_references(cert.prerequisite_certs)
            if invalid_prereqs:
                errors.append(f"Invalid prerequisite certifications: {invalid_prereqs}")

        return errors

    def validate_career_path_creation(self, path: CareerPathCreateRequest) -> List[str]:
        """Validate career path creation business rules."""
        errors = []

        # Validate job type references
        all_job_types = (
            path.entry_job_types +
            path.terminal_job_types +
            [jt for stage in path.progression_stages for jt in stage.job_types]
        )

        invalid_job_types = self._validate_job_type_references(all_job_types)
        if invalid_job_types:
            errors.append(f"Invalid job type references: {invalid_job_types}")

        # Validate logical progression
        if not self._validate_career_progression_logic(path):
            errors.append("Career progression logic is invalid")

        # Check for circular dependencies
        if self._has_circular_dependencies(path):
            errors.append("Career path contains circular dependencies")

        return errors

    def _validate_job_type_references(self, job_type_ids: List[int]) -> List[int]:
        """Validate job type references exist."""
        if not job_type_ids:
            return []

        existing_ids = self.db.query(SecurityJobType.id).filter(
            SecurityJobType.id.in_(job_type_ids),
            SecurityJobType.is_active == True
        ).all()

        existing_ids = [id[0] for id in existing_ids]
        return [id for id in job_type_ids if id not in existing_ids]

    def _validate_certification_references(self, cert_ids: List[int]) -> List[int]:
        """Validate certification references exist."""
        if not cert_ids:
            return []

        existing_ids = self.db.query(Certification.id).filter(
            Certification.id.in_(cert_ids),
            Certification.is_active == True
        ).all()

        existing_ids = [id[0] for id in existing_ids]
        return [id for id in cert_ids if id not in existing_ids]
```

## 6. Performance Optimization

### 6.1 Caching Strategy

```python
from functools import wraps
from typing import Any, Callable
import redis
import json
import hashlib

class CacheManager:
    """Centralized cache management for CRUD operations."""

    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.default_ttl = 3600  # 1 hour

    def cache_key(self, prefix: str, **kwargs) -> str:
        """Generate cache key from parameters."""
        key_data = json.dumps(kwargs, sort_keys=True)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{prefix}:{key_hash}"

    def cached_query(self, ttl: int = None):
        """Decorator for caching database queries."""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                cache_key = self.cache_key(
                    f"query:{func.__name__}",
                    args=str(args),
                    kwargs=kwargs
                )

                # Try to get from cache
                cached_result = self.redis.get(cache_key)
                if cached_result:
                    return json.loads(cached_result)

                # Execute query and cache result
                result = func(*args, **kwargs)
                self.redis.setex(
                    cache_key,
                    ttl or self.default_ttl,
                    json.dumps(result, default=str)
                )

                return result
            return wrapper
        return decorator

    def invalidate_pattern(self, pattern: str):
        """Invalidate cache keys matching pattern."""
        keys = self.redis.keys(pattern)
        if keys:
            self.redis.delete(*keys)

# Usage in service classes
class JobTypeService:
    def __init__(self, db: Session, cache: CacheManager):
        self.db = db
        self.cache = cache

    @cache.cached_query(ttl=1800)  # 30 minutes
    def get_popular_job_types(self, security_area: str, limit: int = 10):
        """Get popular job types with caching."""
        return self.db.query(SecurityJobType)\
            .filter(SecurityJobType.security_area == security_area)\
            .order_by(SecurityJobType.view_count.desc())\
            .limit(limit)\
            .all()

    def create(self, job_type: JobTypeCreateRequest, user_id: str):
        """Create job type and invalidate related caches."""
        result = super().create(job_type, user_id)

        # Invalidate related caches
        self.cache.invalidate_pattern(f"query:get_popular_job_types:*")
        self.cache.invalidate_pattern(f"query:list_job_types:*")

        return result
```

### 6.2 Database Query Optimization

```python
class OptimizedQueryBuilder:
    """Build optimized database queries with eager loading."""

    def __init__(self, db: Session):
        self.db = db

    def build_job_type_query(self, filters: JobTypeListParams, include_related: bool = False):
        """Build optimized job type query with selective eager loading."""
        query = self.db.query(SecurityJobType)

        # Selective eager loading based on requirements
        if include_related:
            query = query.options(
                joinedload(SecurityJobType.organization),
                selectinload(SecurityJobType.required_certifications),
                selectinload(SecurityJobType.career_progressions)
            )

        # Apply filters with optimized joins
        if filters.security_area:
            query = query.filter(SecurityJobType.security_area.in_(filters.security_area))

        if filters.salary_min or filters.salary_max:
            if filters.salary_min:
                query = query.filter(SecurityJobType.salary_min >= filters.salary_min)
            if filters.salary_max:
                query = query.filter(SecurityJobType.salary_max <= filters.salary_max)

        # Full-text search optimization
        if filters.search:
            search_vector = func.to_tsvector('english',
                SecurityJobType.title + ' ' +
                func.coalesce(SecurityJobType.description, '')
            )
            search_query = func.plainto_tsquery('english', filters.search)
            query = query.filter(search_vector.match(search_query))

            # Add relevance ranking
            query = query.add_columns(
                func.ts_rank(search_vector, search_query).label('relevance')
            ).order_by(desc('relevance'))

        return query

    def build_aggregation_query(self, base_query, aggregation_fields: List[str]):
        """Build aggregation query for faceted search."""
        aggregations = {}

        for field in aggregation_fields:
            if field == 'security_area':
                agg_query = self.db.query(
                    SecurityJobType.security_area,
                    func.count(SecurityJobType.id).label('count')
                ).filter(
                    SecurityJobType.id.in_(base_query.subquery())
                ).group_by(SecurityJobType.security_area)

                aggregations[field] = {
                    row.security_area: row.count
                    for row in agg_query.all()
                }

        return aggregations
```

---

**Document Version**: 1.0
**Last Updated**: 2024-01-07
**Next Review**: 2024-02-07