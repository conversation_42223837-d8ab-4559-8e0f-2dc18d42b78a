 .claude/CONTRIBUTING.md                            |   128 +
 .claude/api_versioning.md                          |   127 +
 .claude/cheatsheets/README.md                      |    17 +
 .claude/cheatsheets/database_operations.md         |    57 +
 .claude/cheatsheets/fastapi_backend.md             |    53 +
 .claude/cheatsheets/flask_ui.md                    |    53 +
 .claude/code_index/README.md                       |    17 +
 .claude/code_index/call_graphs.json                |    46 +
 .claude/code_index/intent_classifications.json     |    42 +
 .claude/code_index/type_relationships.json         |    30 +
 .claude/debug_history/README.md                    |    17 +
 .claude/debug_history/context_versions.json        |    25 +
 .claude/debug_history/debug_sessions.json          |    24 +
 .claude/debug_history/error_categories.json        |    37 +
 .claude/guidelines.md                              |   124 +
 .claude/metadata/README.md                         |    17 +
 .claude/metadata/component_dependencies.json       |    24 +
 .claude/metadata/error_patterns.json               |    21 +
 .claude/metadata/file_classifications.json         |    24 +
 .claude/milestones.md                              |   215 +
 .claude/patterns/README.md                         |    17 +
 .claude/patterns/composition_patterns.json         |    30 +
 .claude/patterns/error_handling_patterns.json      |    30 +
 .claude/patterns/implementation_patterns.json      |    30 +
 .claude/qa/README.md                               |    17 +
 .claude/qa/main_page_components.md                 |   124 +
 .claude/qa/solution_patterns.json                  |    51 +
 .claude/qa/solved_problems.json                    |    25 +
 .claude/qa/troubleshooting_guides.md               |    33 +
 .claude/setup.md                                   |   120 +
 .coverage                                          |   Bin 53248 -> 0 bytes
 .cursor/001_fastAPI.mdc                            |    68 +
 .cursor/002_core_server.mdc                        |    39 +
 .cursor/002_core_server.mdc.bak                    |    39 +
 .cursor/003_frontend.mdc                           |     0
 .cursor/005-PEP8-Style.mdc                         |    55 +
 .cursor/006-PEP257-Docstrings.mdc                  |    47 +
 .cursor/007-PEP484-Type-Hints.mdc                  |    52 +
 .cursor/400-Best-Practices.mdc                     |    38 +
 .dockerwrapper/Dockerfile.api                      |    35 +
 .dockerwrapper/Dockerfile.combined                 |    58 +
 .dockerwrapper/Dockerfile.react                    |    22 +
 .dockerwrapper/Dockerfile.streamlit.bak            |    23 +
 .dockerwrapper/PRD_CACHET_STATUS_PAGE.md           |   419 +
 .dockerwrapper/README.md                           |   119 +
 .dockerwrapper/README_TESTING.md                   |   132 +
 .dockerwrapper/cachet/Dockerfile                   |    20 +
 .dockerwrapper/cachet/Dockerfile.cachet            |   148 +
 .dockerwrapper/cachet/config/.env.example          |   259 +
 .../data/cachet/bootstrap_cache/packages.php       |   122 +
 .../data/cachet/bootstrap_cache/services.php       |   250 +
 .dockerwrapper/cachet/data/components.json         |   319 +
 .dockerwrapper/cachet/docker-compose.basic.yml     |   122 +
 .dockerwrapper/cachet/docker-compose.simple.yml    |   120 +
 .dockerwrapper/cachet/docker-compose.yml           |   251 +
 .dockerwrapper/cachet/entrypoint.sh                |    22 +
 .dockerwrapper/cachet/init-scripts/setup-cachet.sh |   144 +
 .dockerwrapper/cachet/monitor/certrats_monitor.py  |   220 +
 .dockerwrapper/cachet/monitor/requirements.txt     |     3 +
 .dockerwrapper/cachet/scripts/Dockerfile.monitor   |    70 +
 .dockerwrapper/cachet/scripts/monitor.py           |   337 +
 .dockerwrapper/cachet/scripts/requirements.txt     |    38 +
 .dockerwrapper/cachet/scripts/setup.sh             |   327 +
 .dockerwrapper/cachet/setup.sh                     |   162 +
 .dockerwrapper/check_certifications.py             |    45 +
 .dockerwrapper/dark-mode-cert-card.png             |   Bin 0 -> 76620 bytes
 .dockerwrapper/docker-compose.yml                  |   148 +
 .dockerwrapper/fixed_database.py                   |    53 +
 .dockerwrapper/fixed_seed.py                       |   115 +
 .dockerwrapper/init.sh                             |    21 +
 .dockerwrapper/install-mui.sh                      |    25 +
 .dockerwrapper/migrate-feature.sh                  |   508 +
 .dockerwrapper/nginx.conf                          |    75 +
 .dockerwrapper/nginx.conf.bak                      |    75 +
 .dockerwrapper/nginx/default.conf                  |    75 +
 .dockerwrapper/playwright-results/index.html       |    71 +
 .../01d38a073f28c31c1d333178be4fa3b0e028b053.zip   |   Bin 0 -> 89580 bytes
 .../048ac9420c125e477f9f0fdc921c907a9c2d8bf7.zip   |   Bin 0 -> 97041 bytes
 .../15d3394db18bfb6744d00c4ee4bf5f2632b41116.zip   |   Bin 0 -> 89590 bytes
 .../21c4911d4c30082a0d42451ec0d58577b25e9fbd.zip   |   Bin 0 -> 89182 bytes
 .../3e8d546eb8d6e40bad6cd05f1727bc4f0c7ee76c.zip   |   Bin 0 -> 89534 bytes
 .../4b81f11e063eefc4f445e40e41d39eaeedd0f6ff.zip   |   Bin 0 -> 89596 bytes
 .../5e37fa719b221e2940ef0eaf08844c66fe726df4.zip   |   Bin 0 -> 89539 bytes
 .../ab7ef6450a4f6c3305e29a9777f78048896b77d3.png   |   Bin 0 -> 2184 bytes
 .../c64f3d21dd70e798c2fde03cbf7bdfdd36facbdf.zip   |   Bin 0 -> 86554 bytes
 .../d606c7545cd71c2b0c27d9f99edbad4fa39f1174.png   |   Bin 0 -> 4331 bytes
 .../ea1fabe2f4eab8c8fd0b134da570992abac96ab2.zip   |   Bin 0 -> 89621 bytes
 .../f2cac5672f8df6f492f6c60ffa67efcfecfadcb5.zip   |   Bin 0 -> 89582 bytes
 .../f9e830ee3ffa991607d0e019e54703637c82e3f2.zip   |   Bin 0 -> 97032 bytes
 .../playwright-report/index.html                   |    71 +
 .../trace/assets/codeMirrorModule-DpJ-EmBQ.js      |    24 +
 .../trace/assets/defaultSettingsView-DTenqiGw.js   |   259 +
 .../trace/codeMirrorModule.C3UTv-Ge.css            |     1 +
 .../playwright-report/trace/codicon.DCmgc-ay.ttf   |   Bin 0 -> 80340 bytes
 .../trace/defaultSettingsView.5fN5lw10.css         |     1 +
 .../playwright-report/trace/index.CFOW-Ezb.css     |     1 +
 .../playwright-report/trace/index.CUq7VgrV.js      |     2 +
 .../playwright-report/trace/index.html             |    43 +
 .../playwright-report/trace/playwright-logo.svg    |     9 +
 .../playwright-report/trace/snapshot.html          |    21 +
 .../playwright-report/trace/sw.bundle.js           |     3 +
 .../playwright-report/trace/uiMode.BatfzHMG.css    |     1 +
 .../playwright-report/trace/uiMode.CHJSAD7F.js     |     5 +
 .../playwright-report/trace/uiMode.html            |    17 +
 .../trace/xtermModule.Beg8tuEN.css                 |    32 +
 .../screenshots/nginx-homepage.png                 |   Bin 0 -> 52523 bytes
 .../screenshots/react-homepage.png                 |   Bin 0 -> 52523 bytes
 .../screenshots/responsive-desktop.png             |   Bin 0 -> 56633 bytes
 .../screenshots/responsive-mobile.png              |   Bin 0 -> 32921 bytes
 .../screenshots/responsive-tablet.png              |   Bin 0 -> 51116 bytes
 .dockerwrapper/remove-streamlit.sh                 |   121 +
 .dockerwrapper/requirements.txt                    |    24 +
 .dockerwrapper/run-combined.sh                     |    18 +
 .dockerwrapper/run.sh                              |    19 +
 .dockerwrapper/seed_database.py                    |    21 +
 .dockerwrapper/supervisord.conf                    |    46 +
 .dockerwrapper/test-certificates.sh                |    10 +
 .../test-certifications-comprehensive.sh           |   203 +
 .dockerwrapper/test-certifications.sh              |   201 +
 .dockerwrapper/update-routes.sh                    |   114 +
 .env.example                                       |   164 +
 .focus_forge_config                                |    10 +
 .github/workflows/ci-cd.yml                        |   344 +
 .github/workflows/frontend-tests.yml               |    85 +
 .github/workflows/production-deploy.yml            |   385 +
 .gitignore                                         |    87 +
 .gitmodules                                        |     3 +
 .next/app-build-manifest.json                      |    15 +
 .next/build-manifest.json                          |    19 +
 .next/cache/config.json                            |     7 +
 .../webpack/client-development-fallback/0.pack.gz  |   Bin 0 -> 11582860 bytes
 .../client-development-fallback/index.pack.gz      |   Bin 0 -> 78052 bytes
 .next/cache/webpack/client-development/0.pack.gz   |   Bin 0 -> 44265 bytes
 .next/cache/webpack/client-development/1.pack.gz   |   Bin 0 -> 9555408 bytes
 .next/cache/webpack/client-development/10.pack.gz  |   Bin 0 -> 1687 bytes
 .next/cache/webpack/client-development/11.pack.gz  |   Bin 0 -> 4592 bytes
 .next/cache/webpack/client-development/12.pack.gz  |   Bin 0 -> 7966 bytes
 .next/cache/webpack/client-development/13.pack.gz  |   Bin 0 -> 9572652 bytes
 .next/cache/webpack/client-development/14.pack.gz  |   Bin 0 -> 25413 bytes
 .next/cache/webpack/client-development/2.pack.gz   |   Bin 0 -> 677650 bytes
 .next/cache/webpack/client-development/3.pack.gz   |   Bin 0 -> 7695123 bytes
 .next/cache/webpack/client-development/4.pack.gz   |   Bin 0 -> 9573176 bytes
 .next/cache/webpack/client-development/5.pack.gz   |   Bin 0 -> 588995 bytes
 .next/cache/webpack/client-development/6.pack.gz   |   Bin 0 -> 32778 bytes
 .next/cache/webpack/client-development/7.pack.gz   |   Bin 0 -> 9161 bytes
 .next/cache/webpack/client-development/8.pack.gz   |   Bin 0 -> 4266 bytes
 .next/cache/webpack/client-development/9.pack.gz   |   Bin 0 -> 213800 bytes
 .../cache/webpack/client-development/index.pack.gz |   Bin 0 -> 117057 bytes
 .../webpack/client-development/index.pack.gz.old   |   Bin 0 -> 2312 bytes
 .next/cache/webpack/server-development/0.pack.gz   |   Bin 0 -> 2742942 bytes
 .next/cache/webpack/server-development/1.pack.gz   |   Bin 0 -> 233570 bytes
 .next/cache/webpack/server-development/10.pack.gz  |   Bin 0 -> 57842 bytes
 .next/cache/webpack/server-development/11.pack.gz  |   Bin 0 -> 8758 bytes
 .next/cache/webpack/server-development/12.pack.gz  |   Bin 0 -> 981840 bytes
 .next/cache/webpack/server-development/13.pack.gz  |   Bin 0 -> 116762 bytes
 .next/cache/webpack/server-development/2.pack.gz   |   Bin 0 -> 2209541 bytes
 .next/cache/webpack/server-development/3.pack.gz   |   Bin 0 -> 1463349 bytes
 .next/cache/webpack/server-development/4.pack.gz   |   Bin 0 -> 12969 bytes
 .next/cache/webpack/server-development/5.pack.gz   |   Bin 0 -> 173491 bytes
 .next/cache/webpack/server-development/6.pack.gz   |   Bin 0 -> 1164 bytes
 .next/cache/webpack/server-development/7.pack.gz   |   Bin 0 -> 2096242 bytes
 .next/cache/webpack/server-development/8.pack.gz   |   Bin 0 -> 1540182 bytes
 .next/cache/webpack/server-development/9.pack.gz   |   Bin 0 -> 610961 bytes
 .../cache/webpack/server-development/index.pack.gz |   Bin 0 -> 122711 bytes
 .../webpack/server-development/index.pack.gz.old   |   Bin 0 -> 143054 bytes
 .next/package.json                                 |     1 +
 .next/react-loadable-manifest.json                 |     1 +
 .next/server/app-paths-manifest.json               |     3 +
 .next/server/app/_not-found/page.js                |   193 +
 .../_not-found/page_client-reference-manifest.js   |     1 +
 .../server/interception-route-rewrite-manifest.js  |     1 +
 .next/server/middleware-build-manifest.js          |    21 +
 .next/server/middleware-manifest.json              |     6 +
 .next/server/middleware-react-loadable-manifest.js |     1 +
 .next/server/next-font-manifest.js                 |     1 +
 .next/server/next-font-manifest.json               |     1 +
 .next/server/pages-manifest.json                   |     1 +
 .next/server/server-reference-manifest.js          |     1 +
 .next/server/server-reference-manifest.json        |     5 +
 .next/server/vendor-chunks/@swc.js                 |    75 +
 .next/server/vendor-chunks/next-themes.js          |    25 +
 .next/server/vendor-chunks/next.js                 |  2166 +++
 .next/server/webpack-runtime.js                    |   215 +
 .next/static/chunks/app-pages-internals.js         |   138 +
 .next/static/chunks/app/_not-found/page.js         |    39 +
 .next/static/chunks/app/layout.js                  |   125 +
 .next/static/chunks/main-app.js                    |  2000 +++
 .next/static/chunks/polyfills.js                   |     1 +
 .next/static/chunks/webpack.js                     |  1398 ++
 .next/static/css/app/layout.css                    |  2565 +++
 .next/static/development/_buildManifest.js         |     1 +
 .next/static/development/_ssgManifest.js           |     1 +
 .next/static/media/26a46d62cd723877-s.woff2        |   Bin 0 -> 18820 bytes
 .next/static/media/55c55f0601d81cf3-s.woff2        |   Bin 0 -> 25908 bytes
 .next/static/media/581909926a08bbc8-s.woff2        |   Bin 0 -> 19072 bytes
 .next/static/media/8e9860b6e62d6359-s.woff2        |   Bin 0 -> 85272 bytes
 .next/static/media/97e0cb1ae144a2a9-s.woff2        |   Bin 0 -> 11220 bytes
 .next/static/media/df0a9ae256c0569c-s.woff2        |   Bin 0 -> 10280 bytes
 .next/static/media/e4af272ccee01ff0-s.p.woff2      |   Bin 0 -> 48432 bytes
 .../webpack/633457081244afec._.hot-update.json     |     1 +
 .../92150ffab21eb272.webpack.hot-update.json       |     1 +
 .../app/layout.db66a5dab999bf14.hot-update.js      |    22 +
 .../db66a5dab999bf14.webpack.hot-update.json       |     1 +
 .../webpack/webpack.92150ffab21eb272.hot-update.js |    48 +
 .../webpack/webpack.db66a5dab999bf14.hot-update.js |    18 +
 .next/trace                                        |    16 +
 .next/types/app/layout.ts                          |    79 +
 .next/types/package.json                           |     1 +
 .nojekyll                                          |     0
 .streamlit/config.toml                             |    25 +-
 .vscode/launch.json                                |    39 +
 .vscode/settings.json                              |    50 +
 .vscode/tasks.json                                 |    77 +
 ADVANCED_AI_FEATURES.md                            |   639 +
 BUSINESS_STRATEGY.md                               |   405 +
 CHANGELOG.md                                       |    77 +
 COMPREHENSIVE_TEST_COVERAGE_REPORT.md              |   283 +
 DEPLOYMENT_OPERATIONS.md                           |   525 +
 DEPLOYMENT_VERIFICATION.md                         |   138 +
 Dockerfile.api                                     |    35 +
 Dockerfile.frontend                                |    34 +
 EXTENDED_TESTING_GUIDE.md                          |   260 +
 IMPLEMENTATION_GUIDE.md                            |   650 +
 MIGRATION.md                                       |   145 +
 MIGRATION_NOTICE.md                                |    60 +
 Makefile                                           |   261 +
 Makefile.new                                       |   263 +
 NEXT_STEPS_FEATURE_1_1.md                          |   232 +
 NEXT_STEPS_ROADMAP.md                              |   304 +
 PRD_ALGORITHMIC_SKILLS_GAP_ANALYSIS.md             |  2389 +++
 PRD_PRODUCTION_READINESS.md                        |   538 +
 PRODUCTION_READINESS_STATUS.md                     |   323 +
 PROJECT_EXPORT.md                                  |   467 +
 README.md                                          |   923 +-
 README.md.bak                                      |   276 +
 SECURITY.md                                        |   242 +
 SYSTEM_OVERVIEW_FOR_AI_PLANNING.md                 |   328 +
 T11_vulnerability_remediation.json                 |    42 +
 T12_1_subtasks.json                                |    35 +
 T12_2_subtasks.json                                |    35 +
 T12_certification_page.json                        |    30 +
 TECHNICAL_ARCHITECTURE.md                          |   432 +
 TECHNOLOGY_INNOVATION_ROADMAP.md                   |   406 +
 TESTING_STRATEGY.md                                |   651 +
 TEST_INFRASTRUCTURE_SUMMARY.md                     |   228 +
 add_subtasks.py                                    |    70 +
 add_task.py                                        |    23 +
 api/__pycache__/__init__.cpython-311.pyc           |   Bin 0 -> 954 bytes
 api/__pycache__/app.cpython-311.pyc                |   Bin 0 -> 10000 bytes
 api/__pycache__/config.cpython-311.pyc             |   Bin 0 -> 1830 bytes
 api/__pycache__/constants.cpython-311.pyc          |   Bin 0 -> 3100 bytes
 api/__pycache__/route_manager.cpython-311.pyc      |   Bin 0 -> 3044 bytes
 api/__pycache__/routes.cpython-311.pyc             |   Bin 0 -> 13022 bytes
 api/app.py                                         |    84 +-
 api/config.py                                      |   134 +-
 api/constants.py                                   |   111 +
 api/deps.py                                        |    56 +
 .../__pycache__/dashboard.cpython-311.pyc          |   Bin 0 -> 12443 bytes
 .../__pycache__/user_management.cpython-311.pyc    |   Bin 0 -> 38315 bytes
 api/endpoints/admin_interface.py                   |   230 +
 api/endpoints/auth.py                              |   342 +
 api/endpoints/certification_comparison.py          |   490 +
 api/endpoints/certification_explorer.py            |   204 +
 api/endpoints/dashboard.py                         |   260 +
 api/endpoints/peer_progress.py                     |     0
 api/endpoints/reports.py                           |   526 +
 api/endpoints/skills_assessment.py                 |   415 +
 api/endpoints/study.py                             |   733 +
 api/endpoints/study_plan_generator.py              |   488 +
 api/endpoints/user_management.py                   |   907 +
 api/error_reporting.py                             |   153 +
 api/route_manager.py                               |    63 +
 api/routes.py                                      |   107 +-
 api/tests/mock_auth_test.py                        |    77 +
 api/tests/run_tests.py                             |    53 +
 api/tests/test_api_endpoints_sync.py               |    40 +
 api/tests/test_auth_endpoints.py                   |   307 +
 api/tests/test_certification_endpoints.py          |   646 +
 api/tests/test_direct.py                           |    40 +
 api/tests/test_study_endpoints.py                  |   393 +
 api/tests/test_user_endpoints.py                   |   518 +
 api/tests/test_versioning.py                       |    71 +
 api/update_api_endpoints.py                        |   120 +
 api/v1/enhanced_security_taxonomy.py               |   359 +
 api/v1/security_career_framework.py                |   534 +
 api/v1/study_timer.py                              |   519 +
 attached_assets/security taxonomy and skills.md    |  1116 ++
 certrats.code-workspace                            |     7 +
 certrats/shell.nix                                 |    40 +
 check_certifications.py                            |    45 +
 check_streamlit_deps.py                            |    48 +
 data/seed.py                                       |   121 +-
 database.py                                        |    18 +-
 docker-compose.yml                                 |   151 +
 docker/Dockerfile.backend                          |    66 +
 docker/Dockerfile.frontend                         |    71 +
 docker/docker-compose.dev.yml                      |   120 +
 docker/docker-compose.yml                          |   135 +
 docker/nginx/nginx.conf                            |   164 +
 docker/postgres/pg_hba.conf                        |    15 +
 docker/postgres/postgresql.conf                    |    60 +
 docker/redis/redis.conf                            |   112 +
 docs/DEPLOYMENT_GUIDE.md                           |   426 +
 docs/LAUNCH_PLAN.md                                |   486 +
 docs/LAUNCH_SUCCESS.md                             |   278 +
 docs/Makefile                                      |    56 +
 docs/OPERATIONS_RUNBOOK.md                         |   612 +
 docs/SECURITY_HARDENING.md                         |   400 +
 docs/_build/html/.buildinfo                        |     4 +
 .../_build/html/.doctrees/DEPLOYMENT_GUIDE.doctree |   Bin 0 -> 67786 bytes
 docs/_build/html/.doctrees/LAUNCH_PLAN.doctree     |   Bin 0 -> 117013 bytes
 docs/_build/html/.doctrees/LAUNCH_SUCCESS.doctree  |   Bin 0 -> 85517 bytes
 .../html/.doctrees/OPERATIONS_RUNBOOK.doctree      |   Bin 0 -> 66956 bytes
 .../html/.doctrees/SECURITY_HARDENING.doctree      |   Bin 0 -> 95223 bytes
 .../_build/html/.doctrees/api-architecture.doctree |   Bin 0 -> 34360 bytes
 docs/_build/html/.doctrees/api-versioning.doctree  |   Bin 0 -> 23733 bytes
 .../certification-page-implementation.doctree      |   Bin 0 -> 34127 bytes
 docs/_build/html/.doctrees/database.doctree        |   Bin 0 -> 38290 bytes
 .../.doctrees/deployment/deployment-guide.doctree  |   Bin 0 -> 50062 bytes
 .../html/.doctrees/deployment/docker-guide.doctree |   Bin 0 -> 34073 bytes
 docs/_build/html/.doctrees/environment.pickle      |   Bin 0 -> 4287678 bytes
 docs/_build/html/.doctrees/feature_roadmap.doctree |   Bin 0 -> 32698 bytes
 docs/_build/html/.doctrees/index.doctree           |   Bin 0 -> 44051 bytes
 .../main_page_implementation_report.doctree        |   Bin 0 -> 32517 bytes
 docs/_build/html/.doctrees/main_page_prd.doctree   |   Bin 0 -> 32388 bytes
 .../.doctrees/user-guide/getting-started.doctree   |   Bin 0 -> 40127 bytes
 .../userjourneys/lifelong_learner.doctree          |   Bin 0 -> 51291 bytes
 .../html/.doctrees/userjourneys/site_admin.doctree |   Bin 0 -> 32381 bytes
 docs/_build/html/.nojekyll                         |     0
 docs/_build/html/DEPLOYMENT_GUIDE.html             |   584 +
 docs/_build/html/LAUNCH_PLAN.html                  |   794 +
 docs/_build/html/LAUNCH_SUCCESS.html               |   459 +
 docs/_build/html/OPERATIONS_RUNBOOK.html           |   822 +
 docs/_build/html/SECURITY_HARDENING.html           |   599 +
 docs/_build/html/_sources/DEPLOYMENT_GUIDE.md.txt  |   426 +
 docs/_build/html/_sources/LAUNCH_PLAN.md.txt       |   486 +
 docs/_build/html/_sources/LAUNCH_SUCCESS.md.txt    |   278 +
 .../_build/html/_sources/OPERATIONS_RUNBOOK.md.txt |   612 +
 .../_build/html/_sources/SECURITY_HARDENING.md.txt |   400 +
 docs/_build/html/_sources/api-architecture.md.txt  |   249 +
 docs/_build/html/_sources/api-versioning.md.txt    |   182 +
 .../certification-page-implementation.md.txt       |   135 +
 docs/_build/html/_sources/database.md.txt          |   367 +
 .../_sources/deployment/deployment-guide.md.txt    |   336 +
 .../html/_sources/deployment/docker-guide.md.txt   |   221 +
 docs/_build/html/_sources/feature_roadmap.md.txt   |   162 +
 docs/_build/html/_sources/index.rst.txt            |   246 +
 .../main_page_implementation_report.md.txt         |   120 +
 docs/_build/html/_sources/main_page_prd.md.txt     |   133 +
 .../_sources/user-guide/getting-started.md.txt     |   169 +
 .../_sources/userjourneys/lifelong_learner.md.txt  |   198 +
 .../html/_sources/userjourneys/site_admin.md.txt   |   132 +
 .../_sphinx_javascript_frameworks_compat.js        |   123 +
 docs/_build/html/_static/basic.css                 |   906 +
 docs/_build/html/_static/css/badge_only.css        |     1 +
 .../html/_static/css/fonts/Roboto-Slab-Bold.woff   |   Bin 0 -> 87624 bytes
 .../html/_static/css/fonts/Roboto-Slab-Bold.woff2  |   Bin 0 -> 67312 bytes
 .../_static/css/fonts/Roboto-Slab-Regular.woff     |   Bin 0 -> 86288 bytes
 .../_static/css/fonts/Roboto-Slab-Regular.woff2    |   Bin 0 -> 66444 bytes
 .../html/_static/css/fonts/fontawesome-webfont.eot |   Bin 0 -> 165742 bytes
 .../html/_static/css/fonts/fontawesome-webfont.svg |  2671 +++
 .../html/_static/css/fonts/fontawesome-webfont.ttf |   Bin 0 -> 165548 bytes
 .../_static/css/fonts/fontawesome-webfont.woff     |   Bin 0 -> 98024 bytes
 .../_static/css/fonts/fontawesome-webfont.woff2    |   Bin 0 -> 77160 bytes
 .../html/_static/css/fonts/lato-bold-italic.woff   |   Bin 0 -> 323344 bytes
 .../html/_static/css/fonts/lato-bold-italic.woff2  |   Bin 0 -> 193308 bytes
 docs/_build/html/_static/css/fonts/lato-bold.woff  |   Bin 0 -> 309728 bytes
 docs/_build/html/_static/css/fonts/lato-bold.woff2 |   Bin 0 -> 184912 bytes
 .../html/_static/css/fonts/lato-normal-italic.woff |   Bin 0 -> 328412 bytes
 .../_static/css/fonts/lato-normal-italic.woff2     |   Bin 0 -> 195704 bytes
 .../_build/html/_static/css/fonts/lato-normal.woff |   Bin 0 -> 309192 bytes
 .../html/_static/css/fonts/lato-normal.woff2       |   Bin 0 -> 182708 bytes
 docs/_build/html/_static/css/theme.css             |     4 +
 docs/_build/html/_static/custom.css                |   265 +
 docs/_build/html/_static/doctools.js               |   149 +
 docs/_build/html/_static/documentation_options.js  |    13 +
 docs/_build/html/_static/fonts/Lato/lato-bold.eot  |   Bin 0 -> 256056 bytes
 docs/_build/html/_static/fonts/Lato/lato-bold.ttf  |   Bin 0 -> 600856 bytes
 docs/_build/html/_static/fonts/Lato/lato-bold.woff |   Bin 0 -> 309728 bytes
 .../_build/html/_static/fonts/Lato/lato-bold.woff2 |   Bin 0 -> 184912 bytes
 .../html/_static/fonts/Lato/lato-bolditalic.eot    |   Bin 0 -> 266158 bytes
 .../html/_static/fonts/Lato/lato-bolditalic.ttf    |   Bin 0 -> 622572 bytes
 .../html/_static/fonts/Lato/lato-bolditalic.woff   |   Bin 0 -> 323344 bytes
 .../html/_static/fonts/Lato/lato-bolditalic.woff2  |   Bin 0 -> 193308 bytes
 .../_build/html/_static/fonts/Lato/lato-italic.eot |   Bin 0 -> 268604 bytes
 .../_build/html/_static/fonts/Lato/lato-italic.ttf |   Bin 0 -> 639388 bytes
 .../html/_static/fonts/Lato/lato-italic.woff       |   Bin 0 -> 328412 bytes
 .../html/_static/fonts/Lato/lato-italic.woff2      |   Bin 0 -> 195704 bytes
 .../html/_static/fonts/Lato/lato-regular.eot       |   Bin 0 -> 253461 bytes
 .../html/_static/fonts/Lato/lato-regular.ttf       |   Bin 0 -> 607720 bytes
 .../html/_static/fonts/Lato/lato-regular.woff      |   Bin 0 -> 309192 bytes
 .../html/_static/fonts/Lato/lato-regular.woff2     |   Bin 0 -> 182708 bytes
 .../fonts/RobotoSlab/roboto-slab-v7-bold.eot       |   Bin 0 -> 79520 bytes
 .../fonts/RobotoSlab/roboto-slab-v7-bold.ttf       |   Bin 0 -> 170616 bytes
 .../fonts/RobotoSlab/roboto-slab-v7-bold.woff      |   Bin 0 -> 87624 bytes
 .../fonts/RobotoSlab/roboto-slab-v7-bold.woff2     |   Bin 0 -> 67312 bytes
 .../fonts/RobotoSlab/roboto-slab-v7-regular.eot    |   Bin 0 -> 78331 bytes
 .../fonts/RobotoSlab/roboto-slab-v7-regular.ttf    |   Bin 0 -> 169064 bytes
 .../fonts/RobotoSlab/roboto-slab-v7-regular.woff   |   Bin 0 -> 86288 bytes
 .../fonts/RobotoSlab/roboto-slab-v7-regular.woff2  |   Bin 0 -> 66444 bytes
 docs/_build/html/_static/jquery.js                 |     2 +
 docs/_build/html/_static/js/badge_only.js          |     1 +
 docs/_build/html/_static/js/theme.js               |     1 +
 docs/_build/html/_static/js/versions.js            |   228 +
 docs/_build/html/_static/language_data.js          |   192 +
 docs/_build/html/_static/pygments.css              |    75 +
 docs/_build/html/_static/searchtools.js            |   635 +
 docs/_build/html/_static/sphinx_highlight.js       |   154 +
 docs/_build/html/api-architecture.html             |   363 +
 docs/_build/html/api-versioning.html               |   294 +
 .../html/certification-page-implementation.html    |   287 +
 docs/_build/html/database.html                     |   510 +
 docs/_build/html/deployment/deployment-guide.html  |   553 +
 docs/_build/html/deployment/docker-guide.html      |   416 +
 docs/_build/html/feature_roadmap.html              |   343 +
 docs/_build/html/genindex.html                     |   114 +
 docs/_build/html/index.html                        |   353 +
 .../html/main_page_implementation_report.html      |   271 +
 docs/_build/html/main_page_prd.html                |   300 +
 docs/_build/html/objects.inv                       |   Bin 0 -> 721 bytes
 docs/_build/html/search.html                       |   128 +
 docs/_build/html/searchindex.js                    |     1 +
 docs/_build/html/user-guide/getting-started.html   |   355 +
 .../_build/html/userjourneys/lifelong_learner.html |   359 +
 docs/_build/html/userjourneys/site_admin.html      |   304 +
 docs/_static/custom.css                            |   265 +
 docs/api-architecture.md                           |   249 +
 docs/api-versioning.md                             |   182 +
 docs/certification-page-implementation.md          |   135 +
 docs/conf.py                                       |   172 +
 docs/deployment/deployment-guide.md                |   336 +
 docs/deployment/docker-guide.md                    |   221 +
 docs/feature_roadmap.md                            |    23 +
 docs/index.rst                                     |   201 +
 docs/main_page_implementation_report.md            |   120 +
 docs/main_page_prd.md                              |   133 +
 docs/requirements.txt                              |     9 +
 docs/study-timer-api.md                            |   469 +
 docs/user-guide/getting-started.md                 |   169 +
 drizzle/0000_initial.sql                           |    16 +
 drizzle/seed.sql                                   |    78 +
 fixed_database.py                                  |    53 +
 fixed_seed.py                                      |   115 +
 focus_forge                                        |     1 +
 frontend/.npmrc                                    |     7 +
 frontend/DEPENDENCY_STRATEGY.md                    |    99 +
 frontend/Dockerfile.react                          |    19 +
 frontend/ERROR_HANDLING.md                         |   145 +
 frontend/TESTING.md                                |   149 +
 frontend/card-interaction-test.png                 |   Bin 0 -> 53610 bytes
 .../certification-page-initial-1743235421004.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235481309.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235541603.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235601918.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235662209.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235722462.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235784332.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235865417.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235925749.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743235986057.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236031183.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236046354.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236091490.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236106650.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236151807.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236166906.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236212104.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236228854.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236272419.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236332670.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743236394526.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743238959908.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743239020215.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743239080519.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743239140829.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743239201133.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743239261393.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1743239323258.png   |   Bin 0 -> 4331 bytes
 .../certification-page-initial-1744091517982.png   |   Bin 0 -> 55150 bytes
 .../certification-page-initial-1744091518423.png   |   Bin 0 -> 55150 bytes
 .../certification-page-initial-1744091518809.png   |   Bin 0 -> 55150 bytes
 .../certification-page-initial-1744091519248.png   |   Bin 0 -> 55158 bytes
 .../certification-page-initial-1744091519630.png   |   Bin 0 -> 55162 bytes
 .../certification-page-initial-1744091523104.png   |   Bin 0 -> 55158 bytes
 frontend/certifications-updated.spec.ts            |   125 +
 frontend/coverage/clover.xml                       |  3408 ++++
 frontend/coverage/coverage-final.json              |    89 +
 frontend/coverage/lcov-report/base.css             |   224 +
 frontend/coverage/lcov-report/block-navigation.js  |    87 +
 frontend/coverage/lcov-report/favicon.png          |   Bin 0 -> 445 bytes
 frontend/coverage/lcov-report/index.html           |   626 +
 frontend/coverage/lcov-report/prettify.css         |     1 +
 frontend/coverage/lcov-report/prettify.js          |     2 +
 .../coverage/lcov-report/sort-arrow-sprite.png     |   Bin 0 -> 138 bytes
 frontend/coverage/lcov-report/sorter.js            |   196 +
 frontend/coverage/lcov-report/src/App.tsx.html     |   745 +
 .../lcov-report/src/components/ApiExample.tsx.html |   346 +
 .../src/components/LanguageSelector.tsx.html       |   370 +
 .../coverage/lcov-report/src/components/index.html |   131 +
 .../src/components/shared/FeedbackButton.tsx.html  |   175 +
 .../src/components/shared/FeedbackPopup.tsx.html   |   535 +
 .../src/components/shared/Icons.tsx.html           |   778 +
 .../components/shared/OnboardingTutorial.tsx.html  |   775 +
 .../lcov-report/src/components/shared/index.html   |   206 +
 .../src/components/shared/useFeedback.tsx.html     |   205 +
 .../src/components/shared/useOnboarding.tsx.html   |   514 +
 .../lcov-report/src/constants/apiEndpoints.ts.html |   472 +
 .../coverage/lcov-report/src/constants/index.html  |   116 +
 .../lcov-report/src/contexts/AuthContext.tsx.html  |   499 +
 .../src/contexts/LanguageContext.tsx.html          |   709 +
 .../lcov-report/src/contexts/ThemeContext.tsx.html |   295 +
 .../coverage/lcov-report/src/contexts/index.html   |   161 +
 frontend/coverage/lcov-report/src/hooks/index.html |   131 +
 .../coverage/lcov-report/src/hooks/useApi.ts.html  |   262 +
 frontend/coverage/lcov-report/src/index.html       |   131 +
 .../coverage/lcov-report/src/jest.config.js.html   |   274 +
 .../lcov-report/src/mocks/chartMock.ts.html        |   394 +
 .../lcov-report/src/mocks/fileMock.js.html         |    88 +
 .../lcov-report/src/mocks/handlers.ts.html         |   835 +
 frontend/coverage/lcov-report/src/mocks/index.html |   176 +
 .../coverage/lcov-report/src/mocks/server.ts.html  |   109 +
 .../lcov-report/src/mocks/styleMock.js.html        |    88 +
 .../pages/AdminInterface/AdminInterface.tsx.html   |   463 +
 .../components/CertificationManagement.tsx.html    |   655 +
 .../components/FeedbackManagement.tsx.html         |  1558 ++
 .../components/OrganizationManagement.tsx.html     |  1255 ++
 .../components/TranslationManagement.tsx.html      |  2416 +++
 .../src/pages/AdminInterface/components/index.html |   161 +
 .../src/pages/AdminInterface/index.html            |   131 +
 .../src/pages/AdminInterface/index.tsx.html        |  2536 +++
 .../src/pages/CareerPath/CareerPath.tsx.html       |   622 +
 .../lcov-report/src/pages/CareerPath/index.html    |   131 +
 .../src/pages/CareerPath/index.tsx.html            |  2140 +++
 .../CertificationExplorer.tsx.html                 |   760 +
 .../components/CertificationCard.tsx.html          |   400 +
 .../components/CertificationComparison.tsx.html    |   700 +
 .../components/CertificationDetail.tsx.html        |   673 +
 .../components/CertificationFilter.tsx.html        |   607 +
 .../components/CertificationList.tsx.html          |   478 +
 .../CertificationExplorer/components/index.html    |   176 +
 .../pages/CertificationExplorer/constants.ts.html  |   454 +
 .../pages/CertificationExplorer/hooks/index.html   |   131 +
 .../hooks/useCertificationComparison.ts.html       |   724 +
 .../hooks/useCertifications.ts.html                |   649 +
 .../src/pages/CertificationExplorer/index.html     |   146 +
 .../src/pages/CertificationExplorer/index.tsx.html |  1453 ++
 .../types/certification.types.ts.html              |   541 +
 .../pages/CertificationExplorer/types/index.html   |   116 +
 .../pages/CostCalculator/CostCalculator.tsx.html   |   769 +
 .../src/pages/CostCalculator/index.html            |   116 +
 .../lcov-report/src/pages/Dashboard.tsx.html       |   325 +
 .../src/pages/Dashboard/Dashboard.tsx.html         |   466 +
 .../lcov-report/src/pages/Dashboard/index.html     |   131 +
 .../lcov-report/src/pages/Dashboard/index.tsx.html |  1117 ++
 .../lcov-report/src/pages/FAQ/FAQ.tsx.html         |   619 +
 .../coverage/lcov-report/src/pages/FAQ/index.html  |   146 +
 .../lcov-report/src/pages/FAQ/index.ts.html        |    88 +
 .../lcov-report/src/pages/FAQ/index.tsx.html       |   799 +
 .../src/pages/JobSearch/JobSearch.tsx.html         |  1006 ++
 .../lcov-report/src/pages/JobSearch/index.html     |   131 +
 .../lcov-report/src/pages/JobSearch/index.tsx.html |  2467 +++
 .../src/pages/MainPage/MainPage.tsx.html           |   892 +
 .../lcov-report/src/pages/MainPage/index.html      |   131 +
 .../lcov-report/src/pages/MainPage/index.tsx.html  |    91 +
 .../lcov-report/src/pages/NotFound.tsx.html        |   175 +
 .../lcov-report/src/pages/Reports/index.html       |   116 +
 .../lcov-report/src/pages/Reports/index.tsx.html   |  2356 +++
 .../StudyTimeTracker/StudyTimeTracker.tsx.html     |  1975 +++
 .../src/pages/StudyTimeTracker/index.html          |   116 +
 .../TestErrorBoundary/TestErrorBoundary.tsx.html   |   517 +
 .../src/pages/TestErrorBoundary/index.html         |   116 +
 .../src/pages/UserJourneys/UserJourneys.tsx.html   |   763 +
 .../lcov-report/src/pages/UserJourneys/index.html  |   131 +
 .../src/pages/UserJourneys/index.tsx.html          |  1879 ++
 .../src/pages/UserManagement/index.html            |   116 +
 .../src/pages/UserManagement/index.tsx.html        |  1831 ++
 .../src/pages/UserProfile/UserProfile.tsx.html     |  1051 ++
 .../lcov-report/src/pages/UserProfile/index.html   |   116 +
 frontend/coverage/lcov-report/src/pages/index.html |   131 +
 .../coverage/lcov-report/src/services/api.ts.html  |  1042 ++
 .../lcov-report/src/services/apiService.ts.html    |   436 +
 .../src/services/feedbackService.ts.html           |   136 +
 .../coverage/lcov-report/src/services/index.html   |   161 +
 .../lcov-report/src/services/mockApiClient.ts.html |   199 +
 frontend/coverage/lcov-report/src/types/index.html |   116 +
 .../coverage/lcov-report/src/types/user.ts.html    |   199 +
 .../src/utils/error/ErrorBoundary.tsx.html         |   307 +
 .../src/utils/error/ErrorContext.tsx.html          |   427 +
 .../src/utils/error/ErrorFallback.tsx.html         |   265 +
 .../src/utils/error/ErrorToast.tsx.html            |   280 +
 .../src/utils/error/FormErrorMessage.tsx.html      |   175 +
 .../src/utils/error/LazyLoadErrorBoundary.tsx.html |   349 +
 .../src/utils/error/RouteErrorBoundary.tsx.html    |   334 +
 .../src/utils/error/buildErrorLogger.js.html       |   421 +
 .../src/utils/error/errorLogger.ts.html            |   622 +
 .../lcov-report/src/utils/error/index.html         |   266 +
 .../lcov-report/src/utils/error/index.ts.html      |   244 +
 .../src/utils/error/useApiErrorHandler.ts.html     |   364 +
 frontend/coverage/lcov-report/src/utils/index.html |   116 +
 .../lcov-report/src/utils/test-utils.tsx.html      |   217 +
 frontend/coverage/lcov.info                        |  7337 ++++++++
 frontend/docker-playwright.config.ts               |    31 +
 frontend/e2e/admin-interface.spec.ts               |    80 +
 frontend/e2e/basic-connection.spec.ts              |    29 +
 frontend/e2e/career-path.spec.ts                   |    87 +
 frontend/e2e/certificate-count-test.spec.ts        |   113 +
 frontend/e2e/certificate-count-test.spec.ts.bak    |   113 +
 .../certification-explorer-comprehensive.spec.ts   |   297 +
 frontend/e2e/certification-explorer.spec.ts        |    55 +
 frontend/e2e/certifications-updated.spec.ts        |   127 +
 frontend/e2e/certifications.spec.ts                |   300 +
 frontend/e2e/cost-calculator.spec.ts               |   243 +
 frontend/e2e/error-handling.spec.ts                |   119 +
 frontend/e2e/faq-page.spec.ts                      |    89 +
 frontend/e2e/feedback-popup.spec.ts                |   106 +
 frontend/e2e/job-search.spec.ts                    |   113 +
 frontend/e2e/mainPage.careerPath.spec.ts           |    80 +
 frontend/e2e/mainPage.certificationCatalog.spec.ts |   106 +
 frontend/e2e/mainPage.dashboardPreview.spec.ts     |    70 +
 frontend/e2e/mainPage.gettingStarted.spec.ts       |   114 +
 frontend/e2e/mainPage.hero.spec.ts                 |    45 +
 frontend/e2e/mainPage.responsive.spec.ts           |   183 +
 frontend/e2e/mainPage.successStories.spec.ts       |    91 +
 frontend/e2e/minimal-cert-tests.spec.ts            |    17 +
 frontend/e2e/onboarding-tutorial.spec.ts           |   129 +
 frontend/e2e/study-time-tracker.spec.ts            |   288 +
 frontend/e2e/theme.spec.ts                         |   115 +
 frontend/e2e/ui-check.spec.ts                      |    60 +
 frontend/e2e/ui-compatibility.spec.ts              |   140 +
 frontend/e2e/user-journeys.spec.ts                 |   101 +
 frontend/e2e/user-profile.spec.ts                  |   162 +
 frontend/e2e/visual-check.ts                       |    72 +
 frontend/filter-test-initial.png                   |   Bin 0 -> 53631 bytes
 frontend/list-of-UI-errors.md                      |    61 +
 frontend/migration-assessment.md                   |   303 +
 frontend/migration-progress.md                     |    83 +
 frontend/migration-progress_2.md                   |    64 +
 frontend/nginx-homepage.png                        |   Bin 0 -> 52523 bytes
 frontend/npm-audit-report.json                     |   266 +
 frontend/package-lock.json                         | 17131 +++++++++++++++++++
 frontend/package.json                              |    94 +
 frontend/page-structure-test.png                   |   Bin 0 -> 53645 bytes
 .../CertificationExplorer.tsx                      |   226 +
 frontend/patches/README.md                         |    52 +
 frontend/patches/msw+1.3.0.patch                   |     7 +
 frontend/patches/resolve-url-loader+4.0.0.patch    |     8 +
 frontend/patches/svgo+1.3.2.patch                  |    27 +
 frontend/playwright-report/index.html              |    71 +
 frontend/playwright.config.js                      |    42 +
 frontend/playwright.config.ts                      |    73 +
 frontend/public/data/certifications.json           |  5139 ++++++
 frontend/public/debug.html                         |   231 +
 frontend/public/index.html                         |    20 +
 frontend/react-homepage.png                        |   Bin 0 -> 52523 bytes
 frontend/responsive-desktop.png                    |   Bin 0 -> 56633 bytes
 frontend/responsive-mobile.png                     |   Bin 0 -> 32921 bytes
 frontend/responsive-tablet.png                     |   Bin 0 -> 51116 bytes
 frontend/run-certification-tests.sh                |    16 +
 frontend/run-skills-tests.sh                       |   215 +
 frontend/screenshot.png                            |   Bin 0 -> 90768 bytes
 frontend/scripts/build-with-error-logging.js       |   114 +
 frontend/scripts/security-monitor.js               |   149 +
 frontend/scripts/type-check.js                     |   101 +
 frontend/scripts/update_api_endpoints.js           |   169 +
 frontend/search-test-initial.png                   |   Bin 0 -> 53631 bytes
 frontend/shell.nix                                 |    36 +
 frontend/src/App.css                               |   211 +
 frontend/src/App.tsx                               |   221 +
 frontend/src/__tests__/App.test.tsx                |    36 +
 frontend/src/adapters/certificationAdapter.ts      |    93 +
 frontend/src/components/ApiExample.css             |    76 +
 frontend/src/components/ApiExample.tsx             |    88 +
 .../CareerPathNavigator/CareerPathNavigator.css    |   217 +
 .../CareerPathNavigator/CareerPathNavigator.tsx    |   242 +
 .../src/components/CareerPathNavigator/index.tsx   |     3 +
 .../CertificationCatalogPreview.css                |   239 +
 .../CertificationCatalogPreview.tsx                |   172 +
 .../CertificationCatalogPreview/index.tsx          |     3 +
 .../DashboardPreview/DashboardPreview.css          |   190 +
 .../DashboardPreview/DashboardPreview.tsx          |   164 +
 frontend/src/components/DashboardPreview/index.tsx |     3 +
 .../GettingStartedGuide/GettingStartedGuide.css    |   388 +
 .../GettingStartedGuide/GettingStartedGuide.tsx    |   180 +
 .../src/components/GettingStartedGuide/index.tsx   |     3 +
 frontend/src/components/LanguageSelector.css       |    86 +
 frontend/src/components/LanguageSelector.tsx       |    96 +
 frontend/src/components/SkillsAssessment.tsx       |   384 +
 frontend/src/components/SkillsDashboard.tsx        |   456 +
 .../src/components/SkillsProgressIndicator.tsx     |   253 +
 .../components/SuccessStories/SuccessStories.css   |   345 +
 .../components/SuccessStories/SuccessStories.tsx   |   225 +
 frontend/src/components/SuccessStories/index.tsx   |     3 +
 .../src/components/__tests__/ApiExample.test.tsx   |    83 +
 .../__tests__/CertificationExplorer.test.tsx       |   238 +
 frontend/src/components/shared/FeedbackButton.css  |    62 +
 frontend/src/components/shared/FeedbackButton.tsx  |    31 +
 frontend/src/components/shared/FeedbackPopup.css   |   161 +
 frontend/src/components/shared/FeedbackPopup.tsx   |   151 +
 frontend/src/components/shared/Icons.tsx           |   232 +
 frontend/src/components/shared/LazyLoad.tsx        |    46 +
 .../src/components/shared/OnboardingTutorial.css   |   221 +
 .../src/components/shared/OnboardingTutorial.tsx   |   231 +
 .../shared/__tests__/FeedbackButton.test.tsx       |    40 +
 .../shared/__tests__/FeedbackPopup.test.tsx        |   179 +
 .../shared/__tests__/OnboardingTutorial.test.tsx   |   216 +
 .../shared/__tests__/useFeedback.test.tsx          |    94 +
 frontend/src/components/shared/useFeedback.tsx     |    41 +
 frontend/src/components/shared/useOnboarding.tsx   |   144 +
 frontend/src/constants/apiEndpoints.ts             |   130 +
 frontend/src/contexts/AnalyticsContext.tsx         |   204 +
 frontend/src/contexts/AuthContext.tsx              |   139 +
 frontend/src/contexts/LanguageContext.tsx          |   209 +
 frontend/src/contexts/ThemeContext.tsx             |    71 +
 frontend/src/hooks/useApi.ts                       |    60 +
 frontend/src/hooks/useLazyLoad.tsx                 |    61 +
 frontend/src/index.css                             |   137 +
 frontend/src/index.tsx                             |    31 +
 frontend/src/jest.config.js                        |    64 +
 frontend/src/mocks/chartMock.ts                    |   104 +
 frontend/src/mocks/fileMock.js                     |     2 +
 frontend/src/mocks/handlers.ts                     |   251 +
 frontend/src/mocks/server.ts                       |     9 +
 frontend/src/mocks/styleMock.js                    |     2 +
 .../src/pages/AdminInterface/AdminInterface.css    |    90 +
 .../src/pages/AdminInterface/AdminInterface.tsx    |   127 +
 .../components/CertificationManagement.test.tsx    |   173 +
 .../components/CertificationManagement.tsx         |   191 +
 .../components/FeedbackManagement.test.tsx         |   230 +
 .../components/FeedbackManagement.tsx              |   492 +
 .../components/OrganizationManagement.test.tsx     |   183 +
 .../components/OrganizationManagement.tsx          |   391 +
 .../components/TranslationManagement.test.tsx      |   309 +
 .../components/TranslationManagement.tsx           |   778 +
 frontend/src/pages/AdminInterface/index.test.tsx   |   289 +
 frontend/src/pages/AdminInterface/index.tsx        |   817 +
 frontend/src/pages/AdminInterface/styles.css       |   554 +
 frontend/src/pages/CareerPath/CareerPath.css       |   234 +
 frontend/src/pages/CareerPath/CareerPath.tsx       |   180 +
 frontend/src/pages/CareerPath/index.test.tsx       |   221 +
 frontend/src/pages/CareerPath/index.tsx            |   686 +
 frontend/src/pages/CareerPath/styles.css           |   380 +
 .../CertificationExplorer.css                      |   381 +
 .../CertificationExplorer.tsx                      |   226 +
 .../components/CertificationCard.tsx               |   106 +
 .../components/CertificationComparison.css         |   230 +
 .../components/CertificationComparison.tsx         |   206 +
 .../components/CertificationDetail.tsx             |   197 +
 .../components/CertificationFilter.tsx             |   175 +
 .../components/CertificationList.tsx               |   132 +
 .../src/pages/CertificationExplorer/constants.ts   |   124 +
 .../hooks/useCertificationComparison.ts            |   214 +
 .../hooks/useCertifications.ts                     |   189 +
 frontend/src/pages/CertificationExplorer/index.tsx |   456 +
 .../src/pages/CertificationExplorer/styles.css     |   283 +
 .../types/certification.types.ts                   |   153 +
 .../src/pages/CostCalculator/CostCalculator.css    |   187 +
 .../pages/CostCalculator/CostCalculator.test.tsx   |   331 +
 .../src/pages/CostCalculator/CostCalculator.tsx    |   229 +
 frontend/src/pages/Dashboard.tsx                   |    81 +
 frontend/src/pages/Dashboard/Dashboard.css         |    79 +
 frontend/src/pages/Dashboard/Dashboard.tsx         |   128 +
 frontend/src/pages/Dashboard/index.tsx             |   344 +
 frontend/src/pages/Dashboard/styles.css            |   263 +
 frontend/src/pages/FAQ/FAQ.css                     |   270 +
 frontend/src/pages/FAQ/FAQ.test.tsx                |    57 +
 frontend/src/pages/FAQ/FAQ.tsx                     |   179 +
 frontend/src/pages/FAQ/index.test.tsx              |   220 +
 frontend/src/pages/FAQ/index.ts                    |     1 +
 frontend/src/pages/FAQ/index.tsx                   |   239 +
 frontend/src/pages/FAQ/styles.css                  |   310 +
 frontend/src/pages/JobSearch/JobSearch.css         |   251 +
 frontend/src/pages/JobSearch/JobSearch.tsx         |   308 +
 frontend/src/pages/JobSearch/index.test.tsx        |   327 +
 frontend/src/pages/JobSearch/index.tsx             |   795 +
 frontend/src/pages/JobSearch/styles.css            |   570 +
 frontend/src/pages/MainPage/MainPage.css           |   592 +
 frontend/src/pages/MainPage/MainPage.tsx           |   270 +
 frontend/src/pages/MainPage/index.tsx              |     3 +
 frontend/src/pages/NotFound.tsx                    |    31 +
 frontend/src/pages/Reports/index.tsx               |   757 +
 frontend/src/pages/Reports/styles.css              |   523 +
 .../pages/StudyTimeTracker/StudyTimeTracker.css    |   354 +
 .../StudyTimeTracker/StudyTimeTracker.test.tsx     |   349 +
 .../pages/StudyTimeTracker/StudyTimeTracker.tsx    |   631 +
 .../pages/TestErrorBoundary/TestErrorBoundary.tsx  |   145 +
 frontend/src/pages/UserJourneys/UserJourneys.css   |   203 +
 frontend/src/pages/UserJourneys/UserJourneys.tsx   |   227 +
 frontend/src/pages/UserJourneys/index.test.tsx     |   243 +
 frontend/src/pages/UserJourneys/index.tsx          |   599 +
 frontend/src/pages/UserJourneys/styles.css         |   687 +
 frontend/src/pages/UserManagement/index.tsx        |   582 +
 frontend/src/pages/UserManagement/styles.css       |   377 +
 frontend/src/pages/UserProfile/UserProfile.css     |   265 +
 .../src/pages/UserProfile/UserProfile.test.tsx     |   200 +
 frontend/src/pages/UserProfile/UserProfile.tsx     |   323 +
 frontend/src/reportWebVitals.ts                    |    15 +
 frontend/src/services/api.ts                       |   319 +
 frontend/src/services/apiService.ts                |   118 +
 frontend/src/services/feedbackService.ts           |    18 +
 frontend/src/services/mockApiClient.ts             |    39 +
 frontend/src/setupTests.ts                         |    57 +
 frontend/src/tests/ApiVersioning.test.ts           |    63 +
 frontend/src/types/user.ts                         |    39 +
 frontend/src/utils/error/ErrorBoundary.tsx         |    75 +
 frontend/src/utils/error/ErrorContext.tsx          |   115 +
 frontend/src/utils/error/ErrorFallback.tsx         |    61 +
 frontend/src/utils/error/ErrorStyles.css           |   442 +
 frontend/src/utils/error/ErrorToast.tsx            |    66 +
 frontend/src/utils/error/FormErrorMessage.tsx      |    31 +
 frontend/src/utils/error/LazyLoadErrorBoundary.tsx |    89 +
 frontend/src/utils/error/README.md                 |   121 +
 frontend/src/utils/error/RouteErrorBoundary.tsx    |    84 +
 .../utils/error/__tests__/ErrorBoundary.test.tsx   |    75 +
 .../utils/error/__tests__/ErrorContext.test.tsx    |   181 +
 .../error/__tests__/useApiErrorHandler.test.tsx    |   134 +
 frontend/src/utils/error/buildErrorLogger.js       |   113 +
 frontend/src/utils/error/errorLogger.ts            |   180 +
 frontend/src/utils/error/index.ts                  |    54 +
 frontend/src/utils/error/useApiErrorHandler.ts     |    94 +
 frontend/src/utils/test-utils.tsx                  |    44 +
 frontend/test-results/.last-run.json               |     4 +
 frontend/tests/certifications-updated.spec.ts      |   138 +
 frontend/tests/certifications.spec.ts              |   300 +
 frontend/tests/direct-api-file-to-vm.spec.ts       |   178 +
 frontend/tests/file-upload-to-vm.spec.ts           |   139 +
 frontend/tests/minimal-cert-tests.ts               |    18 +
 frontend/tests/skills-assessment.spec.ts           |   411 +
 frontend/tests/skills-global-setup.ts              |   126 +
 frontend/tests/skills-global-teardown.ts           |   119 +
 frontend/tests/skills-test-config.ts               |   106 +
 frontend/theme-after-toggle.png                    |   Bin 0 -> 51488 bytes
 frontend/tsconfig.json                             |    28 +
 frontend/validate-skills-tests.py                  |   265 +
 frontend/webpack.error.config.js                   |    84 +
 helm/certrats/Chart.yaml                           |    33 +
 helm/certrats/values.yaml                          |   312 +
 job_market_task.json                               |    68 +
 k8s/advanced-monitoring.yaml                       |   486 +
 k8s/backend.yaml                                   |   311 +
 k8s/configmap.yaml                                 |   238 +
 k8s/disaster-recovery.yaml                         |   480 +
 k8s/frontend.yaml                                  |   353 +
 k8s/monitoring.yaml                                |   413 +
 k8s/namespace.yaml                                 |    26 +
 k8s/performance-optimization.yaml                  |   503 +
 k8s/postgres.yaml                                  |   311 +
 k8s/secrets.yaml                                   |   139 +
 launch-report-20250604_143603.txt                  |    22 +
 main.py                                            |   160 +-
 migrations/versions/001_add_study_timer_models.py  |   218 +
 migrations/versions/001_create_security_domains.py |    47 +
 .../008_create_security_career_framework.py        |   431 +
 models/__pycache__/__init__.cpython-311.pyc        |   Bin 0 -> 157 bytes
 models/__pycache__/certification.cpython-311.pyc   |   Bin 0 -> 13244 bytes
 models/__pycache__/dashboard.cpython-311.pyc       |   Bin 0 -> 5255 bytes
 models/__pycache__/job.cpython-311.pyc             |   Bin 0 -> 3672 bytes
 models/__pycache__/mixins.cpython-311.pyc          |   Bin 0 -> 2422 bytes
 models/__pycache__/user.cpython-311.pyc            |   Bin 0 -> 5343 bytes
 models/__pycache__/user_management.cpython-311.pyc |   Bin 0 -> 6231 bytes
 models/admin_interface.py                          |    67 +
 models/base.py                                     |     3 +
 models/certification.py                            |    85 +-
 models/certification_comparison.py                 |   124 +
 models/certification_explorer.py                   |   173 +
 models/dashboard.py                                |    77 +
 models/enhanced_security_taxonomy.py               |   330 +
 models/peer_progress.py                            |   143 +
 models/reports.py                                  |   148 +
 models/security_career_framework.py                |   390 +
 models/security_domain.py                          |    34 +
 models/skills_assessment.py                        |   166 +
 models/study_plan_generator.py                     |   115 +
 models/study_session.py                            |   369 +
 models/user.py                                     |    98 +
 models/user_management.py                          |    93 +
 next-env.d.ts                                      |     5 +
 next.config.js                                     |    94 +
 package-lock.json                                  |  6793 ++++++++
 package.json                                       |    43 +
 playwright.config.ts                               |    26 +
 postcss.config.js                                  |     6 +
 prisma/schema.prisma                               |    28 +
 pytest.ini                                         |    50 +-
 readme_enhancement.patch                           |  1136 ++
 reports/coverage/coverage_by_category.json         |     1 +
 reports/coverage/test_output.txt                   |    11 +
 requirements.txt                                   |   259 +
 schemas/enhanced_security_taxonomy.py              |   402 +
 schemas/security_career_framework.py               |   421 +
 schemas/study_session.py                           |   302 +
 scripts/build-docs.sh                              |    93 +
 scripts/complete_translations.py                   |   292 +
 scripts/cost-optimization.sh                       |   494 +
 scripts/deploy-docs.sh                             |    79 +
 scripts/deploy.sh                                  |   417 +
 scripts/go-live-checklist.sh                       |   433 +
 scripts/load-testing.js                            |   371 +
 scripts/migrate-to-postgresql.py                   |   329 +
 scripts/performance-optimization.py                |   456 +
 scripts/populate_security_jobs.py                  |   213 +-
 scripts/production-launch.sh                       |   325 +
 scripts/run-tests.sh                               |   303 +
 scripts/run_comprehensive_extended_tests.py        |   376 +
 scripts/run_comprehensive_tests.py                 |   386 +
 scripts/run_coverage_analysis.py                   |   458 +
 scripts/run_security_tests.py                      |   260 +
 scripts/security_scan.py                           |   374 +
 scripts/setup-dev.sh                               |   180 +
 scripts/translation_manager.py                     |   389 +
 secrets/jwt_secret.txt                             |     1 +
 secrets/postgres_password.txt                      |     1 +
 secrets/redis_password.txt                         |     1 +
 services/study_timer.py                            |   491 +
 shared/README.md                                   |    92 +
 shared/__init__.py                                 |    19 +
 shared/api_endpoints.js                            |   104 +
 shared/api_endpoints.py                            |   154 +
 shared/api_endpoints.ts                            |   112 +
 shared/sync_endpoints.py                           |   279 +
 shell.nix                                          |    44 +
 src/app/__tests__/page.test.ts                     |    52 +
 .../admin/certifications/certification-form.tsx    |   221 +
 src/app/admin/certifications/page.tsx              |   165 +
 src/app/api/certifications/[id]/related/route.ts   |    55 +
 src/app/api/certifications/[id]/route.ts           |    92 +
 src/app/api/certifications/providers/route.ts      |    26 +
 src/app/api/certifications/route.ts                |   105 +
 src/app/api/v1/career-path/route.ts                |    33 +
 src/app/career-path/page.tsx                       |   279 +
 src/app/certifications/page.tsx                    |     9 +
 src/app/globals.css                                |   122 +
 src/app/layout.tsx                                 |    47 +
 src/app/page.css                                   |   608 +
 src/app/page.tsx                                   |   266 +
 .../CertificationExplorer.tsx                      |   129 +
 .../components/CertificationComparison.tsx         |    93 +
 .../components/CertificationDetail.tsx             |   177 +
 .../components/CertificationFilter.tsx             |   124 +
 .../components/CertificationList.tsx               |   127 +
 .../hooks/useCertificationComparison.ts            |   109 +
 .../hooks/useCertifications.ts                     |   147 +
 .../types/certification.types.ts                   |    74 +
 src/components/mode-toggle.tsx                     |    20 +
 src/components/theme-provider.tsx                  |     8 +
 src/components/ui/button.tsx                       |    56 +
 src/components/ui/dialog.tsx                       |   110 +
 src/components/ui/dropdown-menu.tsx                |   189 +
 src/components/ui/form.tsx                         |   171 +
 src/components/ui/input.tsx                        |    24 +
 src/components/ui/label.tsx                        |    24 +
 src/components/ui/table.tsx                        |   110 +
 .../components}/certification_path.py              |     0
 streamlit_backup/components/comparison.py          |    66 +
 .../components}/cost_analysis.py                   |     0
 .../components}/estimates.py                       |     0
 .../components}/feedback.py                        |     0
 .../components}/filters.py                         |     0
 .../components}/icons.py                           |     0
 .../components}/mermaid_diagram.py                 |     0
 .../components}/navigation.py                      |     0
 .../components}/onboarding_tutorial.py             |     0
 .../components}/skill_radar.py                     |     0
 .../components/study_progress_sunburst.py          |   151 +
 .../components}/visualization.py                   |     0
 streamlit_backup/main.py                           |   128 +
 .../pages}/1_visualization.py                      |     0
 {pages => streamlit_backup/pages}/2_listings.py    |     0
 {pages => streamlit_backup/pages}/3_admin.py       |     0
 {pages => streamlit_backup/pages}/4_faq.py         |     0
 {pages => streamlit_backup/pages}/5_study_time.py  |     0
 .../pages}/6_cost_calculator.py                    |     0
 .../pages}/7_certrat_career_path.py                |     0
 .../pages}/8_user_journeys.py                      |     0
 {pages => streamlit_backup/pages}/9_job_search.py  |     0
 {pages => streamlit_backup/pages}/__init__.py      |     0
 {pages => streamlit_backup/pages}/callback.py      |     0
 {pages => streamlit_backup/pages}/listings.py      |     0
 {pages => streamlit_backup/pages}/user_profile.py  |     0
 {pages => streamlit_backup/pages}/visualization.py |     0
 tailwind.config.js                                 |    76 +
 tailwind.config.ts                                 |    56 +
 tasks.json                                         |   451 +
 tasks/task_001.txt                                 |    17 +
 tasks/task_023.txt                                 |    22 +
 tasks/task_024.txt                                 |    27 +
 tasks/task_025.txt                                 |    28 +
 tasks/task_0T1.txt                                 |    22 +
 tasks/task_0T2.txt                                 |    21 +
 tasks/task_0T3.txt                                 |    21 +
 tasks/task_0T4.txt                                 |    22 +
 tasks/task_0T5.txt                                 |    22 +
 tasks/task_0T6.txt                                 |    22 +
 tasks/task_0T7.txt                                 |    21 +
 tasks/task_0T8.txt                                 |    22 +
 tasks/task_0T9.txt                                 |    21 +
 tasks/task_T10.txt                                 |    28 +
 temp_certificationexplorer.tsx                     |   226 +
 terraform/main.tf                                  |   380 +
 terraform/variables.tf                             |   322 +
 test-certificates.sh                               |    10 +
 testing_task.json                                  |    39 +
 tests/behave.ini                                   |   104 +
 tests/conftest.py                                  |   363 +-
 tests/conftest_extended.py                         |   355 +
 tests/docs/Makefile                                |    90 +
 tests/docs/README.md                               |   253 +
 tests/docs/best-practices.rst                      |   755 +
 tests/docs/conf.py                                 |   207 +
 tests/docs/implementation/index.rst                |   612 +
 tests/docs/index.rst                               |   140 +
 tests/docs/overview.rst                            |   187 +
 tests/docs/requirements.txt                        |    32 +
 tests/docs/test-types/ai-quality-tests.rst         |   547 +
 tests/docs/test-types/chaos-tests.rst              |   502 +
 tests/docs/test-types/contract-tests.rst           |   385 +
 tests/docs/test-types/index.rst                    |   330 +
 tests/docs/testing-strategy.rst                    |   346 +
 tests/docs/tools-and-frameworks.rst                |   711 +
 tests/features/skills_assessment.feature           |   160 +
 tests/run-behave-tests.sh                          |   323 +
 tests/steps/skills_assessment_steps.py             |   884 +
 tests/test_accessibility/test_a11y.py              |   311 +
 tests/test_admin_interface.py                      |   367 +
 tests/test_ai_quality/test_ai_models.py            |   308 +
 tests/test_api/test_auth_endpoints.py              |   416 +
 tests/test_api/test_career_path_comprehensive.py   |   317 +
 .../test_certification_endpoints_comprehensive.py  |   635 +
 tests/test_api/test_health_comprehensive.py        |   465 +
 .../test_api_integration/test_auth_integration.py  |   343 +
 .../test_certification_integration.py              |   415 +
 tests/test_basic_coverage.py                       |   272 +
 .../test_certification_processing.py               |   469 +
 tests/test_certification_comparison.py             |   314 +
 tests/test_certification_explorer.py               |   399 +
 tests/test_chaos/test_resilience.py                |   206 +
 tests/test_contracts/test_api_contracts.py         |   141 +
 tests/test_dashboard.py                            |    99 +
 tests/test_data_quality/test_data_integrity.py     |   276 +
 tests/test_database_setup.py                       |   280 +
 tests/test_e2e/test_career_path_flow.py            |   436 +
 .../test_error_handling_comprehensive.py           |   396 +
 tests/test_error_handling/test_edge_cases.py       |   441 +
 tests/test_i18n/test_internationalization.py       |   328 +
 .../test_certification_comprehensive.py            |   413 +
 tests/test_models/test_certification_explorer.py   |   300 +
 tests/test_models/test_health_check.py             |   348 +
 tests/test_models/test_user_comprehensive.py       |   496 +
 tests/test_performance/test_api_performance.py     |   292 +
 tests/test_performance/test_load_performance.py    |   523 +
 tests/test_reports.py                              |   535 +
 tests/test_security/test_authentication.py         |   292 +
 .../test_authentication_comprehensive.py           |   598 +
 tests/test_security/test_input_validation.py       |   292 +-
 tests/test_security/test_middleware.py             |   364 +
 tests/test_security/test_security_audit.py         |   392 +
 tests/test_skills_assessment.py                    |   340 +
 tests/test_skills_core.py                          |   304 +
 tests/test_skills_integration.py                   |   427 +
 tests/test_study_plan_generator.py                 |   197 +
 tests/test_user_management.py                      |   400 +
 tests/test_utils/test_certification_utils.py       |   381 +
 .../test_claude_assistant_comprehensive.py         |   620 +
 tests/test_utils/test_database_comprehensive.py    |   599 +
 tests/validate-behave-tests.py                     |   353 +
 translations/README.md                             |   292 +
 translations/ar/LC_MESSAGES/messages.po            |   236 +
 translations/da/LC_MESSAGES/messages.po            |   236 +
 translations/de/LC_MESSAGES/messages.mo            |   Bin 457 -> 4698 bytes
 translations/de/LC_MESSAGES/messages.po            |   198 +-
 translations/es/LC_MESSAGES/messages.mo            |   Bin 458 -> 4683 bytes
 translations/es/LC_MESSAGES/messages.po            |   198 +-
 translations/fr/LC_MESSAGES/messages.mo            |   Bin 456 -> 4774 bytes
 translations/fr/LC_MESSAGES/messages.po            |   196 +-
 translations/hi/LC_MESSAGES/messages.po            |   236 +
 translations/it/LC_MESSAGES/messages.po            |   236 +
 translations/ja/LC_MESSAGES/messages.po            |   236 +
 translations/ko/LC_MESSAGES/messages.po            |   236 +
 translations/nl/LC_MESSAGES/messages.po            |   236 +
 translations/no/LC_MESSAGES/messages.po            |   236 +
 translations/pt/LC_MESSAGES/messages.po            |   236 +
 translations/ru/LC_MESSAGES/messages.po            |   236 +
 translations/sv/LC_MESSAGES/messages.po            |   236 +
 translations/zh/LC_MESSAGES/messages.po            |   236 +
 tsconfig.json                                      |    40 +
 utils/__pycache__/__init__.cpython-311.pyc         |   Bin 0 -> 199 bytes
 utils/__pycache__/claude_assistant.cpython-311.pyc |   Bin 0 -> 15586 bytes
 utils/__pycache__/job_management.cpython-311.pyc   |   Bin 0 -> 2689 bytes
 utils/claude_assistant.py                          |    11 +-
 utils/roadmap_parser.py                            |    91 +-
 1089 files changed, 253992 insertions(+), 971 deletions(-)
