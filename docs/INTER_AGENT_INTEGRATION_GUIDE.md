# Inter-Agent Integration Implementation Guide

This guide provides step-by-step instructions for implementing the inter-agent integration and cleanup work outlined in the PRD.

## 🚀 Quick Start - Critical Fixes

### 1. Fix Missing API Routes (IMMEDIATE - 30 minutes)

**File: `api/routes.py`**

Add these imports at the top:
```python
from api.v1.certratsagent4 import router as certratsagent4_router
from api.v1.salary_intelligence import router as salary_intelligence_router
```

Add these router inclusions after line 70:
```python
router.include_router(certratsagent4_router, prefix="/api/v1/certratsagent4", tags=["CertRatsAgent4 - Unified Intelligence"])
router.include_router(salary_intelligence_router, prefix="/api/v1/salary", tags=["Salary Intelligence"])
```

### 2. Fix Authentication in CertRatsAgent4 (IMMEDIATE - 1 hour)

**File: `api/v1/certratsagent4.py`**

Replace the placeholder authentication function:
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from services.auth_service import AuthService
import jwt

security = HTTPBearer()
auth_service = AuthService()

def get_current_user_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Extract user ID from JWT token."""
    try:
        token = credentials.credentials
        payload = jwt.decode(token, auth_service.secret_key, algorithms=["HS256"])
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
        return user_id
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
```

### 3. Create Unified Authentication Service (1 day)

**File: `services/unified_auth_service.py`**

```python
"""Unified authentication service for all agents."""

import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from models.user import User

class UnifiedAuthService:
    """Centralized authentication for all 5 agents."""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 30
    
    def create_access_token(self, user_id: str, additional_claims: Dict[str, Any] = None) -> str:
        """Create JWT access token for user."""
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "sub": user_id,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token and return payload."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    def get_user_permissions(self, user_id: str, db: Session) -> Dict[str, bool]:
        """Get user permissions across all agents."""
        user = db.query(User).filter(User.user_id == user_id).first()
        if not user:
            return {}
        
        # Define permissions based on subscription tier
        permissions = {
            "agent1_core": True,  # Always available
            "agent2_ai_study": user.subscription_tier in ["professional", "premium"],
            "agent3_enterprise": user.subscription_tier == "premium",
            "agent4_career_intelligence": user.subscription_tier in ["professional", "premium"],
            "agent5_marketplace": True,  # Always available
        }
        
        return permissions
```

## 🔗 Cross-Agent Integration Patterns

### 1. Unified User Profile Service

**File: `services/unified_user_profile_service.py`**

```python
"""Unified user profile management across all agents."""

from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from dataclasses import dataclass
from datetime import datetime

from services.ai_study_assistant import OnDeviceAIStudyAssistant
from services.enterprise_ai_service import EnterpriseAIService
from services.salary_intelligence import SalaryIntelligenceService
from services.marketplace_service import MarketplaceService
from models.user import User

@dataclass
class CompleteUserProfile:
    """Complete user profile aggregated from all agents."""
    user_id: str
    core_profile: Dict[str, Any]
    study_preferences: Dict[str, Any]
    enterprise_profile: Optional[Dict[str, Any]]
    career_profile: Dict[str, Any]
    marketplace_profile: Dict[str, Any]
    last_updated: datetime

class UnifiedUserProfileService:
    """Centralized user profile management across all agents."""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_assistant = OnDeviceAIStudyAssistant(db)
        self.enterprise_service = EnterpriseAIService(db)
        self.salary_service = SalaryIntelligenceService(db)
        self.marketplace_service = MarketplaceService(db)
    
    def get_complete_user_profile(self, user_id: str) -> CompleteUserProfile:
        """Aggregate user data from all agents."""
        
        # Get core profile from Agent 1
        user = self.db.query(User).filter(User.user_id == user_id).first()
        if not user:
            raise ValueError(f"User {user_id} not found")
        
        core_profile = {
            "user_id": user.user_id,
            "email": user.email,
            "name": user.name,
            "subscription_tier": user.subscription_tier,
            "is_active": user.is_active,
            "last_login": user.last_login.isoformat() if user.last_login else None
        }
        
        # Get study preferences from Agent 2
        try:
            study_data = self.ai_assistant._gather_user_data(user_id)
            study_preferences = {
                "learning_style": study_data.get("learning_style", "mixed"),
                "study_hours_per_week": study_data.get("study_hours_per_week", 10),
                "preferred_study_times": study_data.get("preferred_study_times", []),
                "current_certifications": study_data.get("current_certifications", [])
            }
        except Exception:
            study_preferences = {}
        
        # Get enterprise profile from Agent 3 (if applicable)
        enterprise_profile = None
        if user.subscription_tier == "premium":
            try:
                enterprise_profile = self.enterprise_service.get_user_enterprise_profile(user_id)
            except Exception:
                pass
        
        # Get career profile from Agent 4
        try:
            career_profile = {
                "current_role": "Security Analyst",  # TODO: Get from career service
                "target_role": "Security Engineer",   # TODO: Get from career service
                "experience_years": 5,               # TODO: Get from career service
                "salary_range": self.salary_service.get_user_salary_range(user_id)
            }
        except Exception:
            career_profile = {}
        
        # Get marketplace profile from Agent 5
        try:
            marketplace_profile = self.marketplace_service.get_user_marketplace_profile(user_id)
        except Exception:
            marketplace_profile = {}
        
        return CompleteUserProfile(
            user_id=user_id,
            core_profile=core_profile,
            study_preferences=study_preferences,
            enterprise_profile=enterprise_profile,
            career_profile=career_profile,
            marketplace_profile=marketplace_profile,
            last_updated=datetime.utcnow()
        )
    
    def sync_profile_changes(self, user_id: str, changes: Dict[str, Any]):
        """Propagate profile changes across all relevant agents."""
        
        # Update core profile if needed
        if "core" in changes:
            user = self.db.query(User).filter(User.user_id == user_id).first()
            if user:
                for key, value in changes["core"].items():
                    if hasattr(user, key):
                        setattr(user, key, value)
                self.db.commit()
        
        # Notify other agents of changes
        if "study_preferences" in changes:
            # TODO: Update Agent 2 study preferences
            pass
        
        if "career_profile" in changes:
            # TODO: Update Agent 4 career profile
            pass
        
        if "marketplace_profile" in changes:
            # TODO: Update Agent 5 marketplace profile
            pass
```

### 2. Cross-Agent Recommendation Engine

**File: `services/cross_agent_recommendation_engine.py`**

```python
"""Cross-agent recommendation engine for holistic insights."""

from typing import List, Dict, Any
from sqlalchemy.orm import Session
from dataclasses import dataclass
from datetime import datetime

@dataclass
class HolisticRecommendation:
    """Recommendation that considers all agent capabilities."""
    id: str
    title: str
    description: str
    type: str  # study, career, enterprise, marketplace
    priority: int  # 1-5
    confidence_score: float  # 0.0-1.0
    estimated_impact: str  # low, medium, high
    time_investment: str  # hours, days, weeks
    cost_estimate: float
    roi_estimate: float
    source_agents: List[str]  # Which agents contributed to this recommendation
    actionable_steps: List[str]
    metadata: Dict[str, Any]

class CrossAgentRecommendationEngine:
    """Generate recommendations considering all agent capabilities."""
    
    def __init__(self, db: Session):
        self.db = db
        # Initialize all agent services
        from services.ai_study_assistant import OnDeviceAIStudyAssistant
        from services.salary_intelligence import SalaryIntelligenceService
        from services.enterprise_ai_service import EnterpriseAIService
        from services.marketplace_service import MarketplaceService
        
        self.ai_assistant = OnDeviceAIStudyAssistant(db)
        self.salary_service = SalaryIntelligenceService(db)
        self.enterprise_service = EnterpriseAIService(db)
        self.marketplace_service = MarketplaceService(db)
    
    def generate_holistic_recommendations(self, user_id: str) -> List[HolisticRecommendation]:
        """Generate recommendations considering all agent capabilities."""
        
        recommendations = []
        
        # Get recommendations from each agent
        study_recs = self._get_study_recommendations(user_id)
        career_recs = self._get_career_recommendations(user_id)
        enterprise_recs = self._get_enterprise_recommendations(user_id)
        marketplace_recs = self._get_marketplace_recommendations(user_id)
        
        # Combine and prioritize recommendations
        all_recs = study_recs + career_recs + enterprise_recs + marketplace_recs
        
        # Apply cross-agent intelligence to enhance recommendations
        enhanced_recs = self._enhance_with_cross_agent_intelligence(user_id, all_recs)
        
        # Sort by priority and confidence
        enhanced_recs.sort(key=lambda x: (x.priority, x.confidence_score), reverse=True)
        
        return enhanced_recs[:10]  # Return top 10 recommendations
    
    def _get_study_recommendations(self, user_id: str) -> List[HolisticRecommendation]:
        """Get study recommendations from Agent 2."""
        try:
            agent2_recs = self.ai_assistant.generate_personalized_recommendations(user_id)
            
            holistic_recs = []
            for rec in agent2_recs:
                holistic_recs.append(HolisticRecommendation(
                    id=f"study_{rec.id if hasattr(rec, 'id') else 'unknown'}",
                    title=rec.title,
                    description=rec.description if hasattr(rec, 'description') else rec.title,
                    type="study",
                    priority=rec.priority,
                    confidence_score=rec.confidence_score,
                    estimated_impact="medium",
                    time_investment=f"{rec.estimated_time_minutes} minutes",
                    cost_estimate=0.0,
                    roi_estimate=0.0,
                    source_agents=["agent2"],
                    actionable_steps=rec.actionable_steps if hasattr(rec, 'actionable_steps') else [],
                    metadata=rec.metadata if hasattr(rec, 'metadata') else {}
                ))
            
            return holistic_recs
        except Exception:
            return []
    
    def _get_career_recommendations(self, user_id: str) -> List[HolisticRecommendation]:
        """Get career recommendations from Agent 4."""
        # TODO: Implement career recommendations
        return []
    
    def _get_enterprise_recommendations(self, user_id: str) -> List[HolisticRecommendation]:
        """Get enterprise recommendations from Agent 3."""
        # TODO: Implement enterprise recommendations
        return []
    
    def _get_marketplace_recommendations(self, user_id: str) -> List[HolisticRecommendation]:
        """Get marketplace recommendations from Agent 5."""
        # TODO: Implement marketplace recommendations
        return []
    
    def _enhance_with_cross_agent_intelligence(
        self, 
        user_id: str, 
        recommendations: List[HolisticRecommendation]
    ) -> List[HolisticRecommendation]:
        """Enhance recommendations with cross-agent intelligence."""
        
        enhanced = []
        
        for rec in recommendations:
            # Add salary intelligence to study recommendations
            if rec.type == "study" and "certification" in rec.metadata:
                try:
                    cert_name = rec.metadata["certification"]
                    salary_impact = self.salary_service.get_certification_salary_impact(cert_name)
                    rec.roi_estimate = salary_impact.get("annual_increase", 0)
                    rec.estimated_impact = "high" if rec.roi_estimate > 10000 else "medium"
                except Exception:
                    pass
            
            # Add marketplace opportunities to career recommendations
            if rec.type == "career":
                try:
                    # TODO: Add marketplace integration
                    pass
                except Exception:
                    pass
            
            enhanced.append(rec)
        
        return enhanced
```

## 🧪 Testing Integration

### Integration Test Example

**File: `tests/test_inter_agent_integration.py`**

```python
"""Integration tests for cross-agent functionality."""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.app import app
from services.unified_auth_service import UnifiedAuthService
from services.unified_user_profile_service import UnifiedUserProfileService

class TestInterAgentIntegration:
    """Test cross-agent integration functionality."""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def auth_service(self):
        return UnifiedAuthService("test-secret-key")
    
    def test_unified_authentication(self, client, auth_service):
        """Test that authentication works across all agents."""
        # Create test token
        token = auth_service.create_access_token("test_user_123")
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test Agent 1 (Core Platform)
        response = client.get("/api/v1/certifications", headers=headers)
        assert response.status_code in [200, 401]  # Should not be 500
        
        # Test Agent 2 (AI Study Assistant)
        response = client.get("/api/v1/ai-assistant/recommendations", headers=headers)
        assert response.status_code in [200, 401]
        
        # Test Agent 4 (CertRatsAgent4)
        response = client.get("/api/v1/certratsagent4/dashboard", headers=headers)
        assert response.status_code in [200, 401]
    
    def test_cross_agent_data_consistency(self, db_session):
        """Test that user data is consistent across agents."""
        profile_service = UnifiedUserProfileService(db_session)
        
        # This should not raise an exception
        try:
            profile = profile_service.get_complete_user_profile("test_user_123")
            assert profile.user_id == "test_user_123"
        except ValueError:
            # User not found is acceptable for test
            pass
    
    def test_unified_api_routes(self, client):
        """Test that all agent routes are accessible."""
        # Test that CertRatsAgent4 routes are included
        response = client.get("/docs")
        assert response.status_code == 200
        
        # Check that the OpenAPI spec includes our routes
        response = client.get("/openapi.json")
        assert response.status_code == 200
        openapi_spec = response.json()
        
        # Should include CertRatsAgent4 routes
        paths = openapi_spec.get("paths", {})
        certratsagent4_paths = [path for path in paths.keys() if "certratsagent4" in path]
        assert len(certratsagent4_paths) > 0
```

## 📋 Implementation Checklist

### Immediate Actions (Today)
- [ ] Add missing routes to `api/routes.py`
- [ ] Fix authentication in `api/v1/certratsagent4.py`
- [ ] Test that all routes are accessible
- [ ] Verify OpenAPI documentation includes all agents

### Week 1
- [ ] Implement `UnifiedAuthService`
- [ ] Implement `UnifiedUserProfileService`
- [ ] Add integration tests
- [ ] Update documentation

### Week 2
- [ ] Implement `CrossAgentRecommendationEngine`
- [ ] Add cross-agent analytics
- [ ] Implement unified dashboard service
- [ ] Add performance monitoring

This guide provides the foundation for implementing seamless inter-agent integration. Start with the immediate actions and work through each phase systematically.
