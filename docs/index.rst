CertPathFinder Documentation
=============================

.. image:: _static/logo.png
   :alt: CertPathFinder Logo
   :align: center
   :width: 300px

**The Ultimate Cybersecurity Certification Journey Planner**

CertPathFinder is a comprehensive platform designed to help cybersecurity professionals plan, track, and optimize their certification journey. With over 465+ certifications, intelligent cost calculations, and personalized career path recommendations, CertPathFinder is your trusted companion in advancing your cybersecurity career.

.. note::
   This documentation covers version |version| of CertPathFinder. For the latest updates and features, please visit our `GitHub repository <https://github.com/forkrul/replit-CertPathFinder>`_.

Quick Start
-----------

.. tabs::

   .. tab:: Users

      Get started with CertPathFinder as a user:

      1. **Explore Certifications**: Browse our comprehensive database of 465+ cybersecurity certifications
      2. **Calculate Costs**: Use our intelligent cost calculator to plan your certification budget
      3. **Plan Your Path**: Get personalized recommendations based on your experience and goals
      4. **Track Progress**: Monitor your certification journey and achievements

      :doc:`Get Started as a User <user-guide/getting-started>`

   .. tab:: Developers

      Set up CertPathFinder for development:

      1. **Clone the Repository**: ``git clone https://github.com/forkrul/replit-CertPathFinder.git``
      2. **Install Dependencies**: ``pip install -r requirements.txt && cd frontend && npm install``
      3. **Set Up Database**: ``alembic upgrade head``
      4. **Run the Application**: ``uvicorn api.app:app --reload`` and ``npm start``

      :doc:`Developer Setup Guide <developer-guide/setup>`

   .. tab:: API

      Integrate with CertPathFinder API:

      1. **Authentication**: Get your API key from the dashboard
      2. **Make Requests**: Use our RESTful API endpoints
      3. **Handle Responses**: Process JSON responses with proper error handling
      4. **Rate Limits**: Respect our rate limiting policies

      :doc:`API Documentation <api/overview>`

Features
--------

🎯 **Comprehensive Certification Database**
   Access detailed information about 465+ cybersecurity certifications from leading organizations.

💰 **Intelligent Cost Calculator**
   Calculate total certification costs including exams, study materials, training, and renewal fees.

🗺️ **Personalized Career Paths**
   Get customized certification recommendations based on your experience, goals, and industry trends.

🤖 **AI-Powered Study Assistant**
   Leverage AI to create personalized study plans and get intelligent recommendations.

📊 **Progress Tracking**
   Monitor your certification journey with detailed analytics and achievement tracking.

🏢 **Enterprise Solutions**
   Manage team certifications, track compliance, and optimize training budgets.

Table of Contents
-----------------

.. toctree::
   :maxdepth: 2
   :caption: User Guide

   user-guide/getting-started
   user-guide/certification-explorer
   user-guide/cost-calculator
   user-guide/career-paths
   user-guide/study-assistant
   user-guide/progress-tracking
   user-guide/enterprise-features

.. toctree::
   :maxdepth: 2
   :caption: Developer Guide

   developer-guide/setup
   developer-guide/architecture
   developer-guide/contributing
   developer-guide/testing
   developer-guide/deployment
   developer-guide/troubleshooting

.. toctree::
   :maxdepth: 2
   :caption: Product Requirements

   PRDs/README

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/overview
   api/authentication
   api/certifications
   api/cost-calculator
   api/user-management
   api/career-paths
   api/ai-assistant
   api/enterprise
   api/webhooks

.. toctree::
   :maxdepth: 2
   :caption: Administration

   admin/installation
   admin/configuration
   admin/database
   admin/monitoring
   admin/backup
   admin/security

.. toctree::
   :maxdepth: 1
   :caption: Additional Resources

   changelog
   roadmap
   faq
   support
   license

Key Statistics
--------------

.. list-table::
   :widths: 30 70
   :header-rows: 1

   * - Metric
     - Value
   * - Total Certifications
     - 465+
   * - Supported Organizations
     - 50+
   * - API Endpoints
     - 100+
   * - Supported Currencies
     - 25+
   * - Languages
     - English (more coming)
   * - Test Coverage
     - 90%+

Architecture Overview
--------------------

CertPathFinder is built with modern technologies and follows best practices:

**Backend**
   - **FastAPI**: High-performance Python web framework
   - **PostgreSQL**: Robust relational database
   - **SQLAlchemy**: Powerful ORM with type safety
   - **Pydantic**: Data validation and serialization
   - **Alembic**: Database migration management

**Frontend**
   - **React**: Modern JavaScript library
   - **TypeScript**: Type-safe JavaScript
   - **Material-UI**: Professional component library
   - **React Query**: Efficient data fetching
   - **React Router**: Client-side routing

**Infrastructure**
   - **Docker**: Containerized deployment
   - **Redis**: Caching and session management
   - **Nginx**: Reverse proxy and load balancing
   - **GitHub Actions**: CI/CD pipeline

Community & Support
-------------------

Join our growing community of cybersecurity professionals:

- **GitHub**: `forkrul/replit-CertPathFinder <https://github.com/forkrul/replit-CertPathFinder>`_
- **Issues**: Report bugs and request features
- **Discussions**: Ask questions and share ideas
- **Contributing**: Help improve CertPathFinder

License
-------

CertPathFinder is open source software licensed under the MIT License. See the :doc:`license` page for full details.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
