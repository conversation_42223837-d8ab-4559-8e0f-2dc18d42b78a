# Cost Calculator API Documentation

## Overview

The Cost Calculator API provides comprehensive functionality for calculating and analyzing certification costs across different scenarios, currencies, and learning paths. This API helps users make informed decisions about their certification investments by providing detailed cost breakdowns, comparisons, and recommendations.

## Base URL

All API endpoints are prefixed with `/api/v1/cost-calculator`

## Authentication

All endpoints require user authentication. The current implementation uses a placeholder authentication system that returns `test_user_1` as the user ID.

## Currency Management

### Create Currency Rate

**POST** `/currency-rates`

Create a new currency exchange rate.

#### Request Body

```json
{
  "base_currency": "USD",
  "target_currency": "EUR",
  "rate": 0.92,
  "source": "api",
  "valid_from": "2024-01-15T00:00:00Z",
  "valid_until": "2024-02-15T00:00:00Z"
}
```

#### Response

```json
{
  "id": 1,
  "base_currency": "USD",
  "target_currency": "EUR",
  "rate": 0.92,
  "source": "api",
  "is_active": true,
  "valid_from": "2024-01-15T00:00:00Z",
  "valid_until": "2024-02-15T00:00:00Z",
  "is_valid": true,
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T10:00:00Z"
}
```

### Get Exchange Rate

**GET** `/currency-rates/{base_currency}/{target_currency}`

Get current exchange rate between two currencies.

#### Response

```json
{
  "base_currency": "USD",
  "target_currency": "EUR",
  "rate": 0.92,
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### Update Exchange Rates from API

**POST** `/currency-rates/update-from-api?base_currency=USD`

Update exchange rates from external API service.

#### Response

```json
{
  "success": true,
  "updated_count": 15,
  "errors": [],
  "timestamp": "2024-01-15T10:00:00Z"
}
```

## Cost Scenarios

### Create Cost Scenario

**POST** `/scenarios`

Create a new cost calculation scenario.

#### Request Body

```json
{
  "name": "Self-Study Path",
  "description": "Independent study with minimal external resources",
  "scenario_type": "self_study",
  "materials_multiplier": 1.0,
  "training_multiplier": 0.0,
  "retake_probability": 0.25,
  "includes_training": false,
  "includes_mentoring": false,
  "includes_practice_exams": true,
  "study_time_multiplier": 1.2,
  "preparation_weeks": 12
}
```

#### Scenario Types

- `self_study`: Independent study approach
- `bootcamp`: Intensive training bootcamp
- `university`: Formal university course
- `corporate`: Company-sponsored training
- `hybrid`: Combination approach

### Get Cost Scenarios

**GET** `/scenarios`

Retrieve paginated list of cost scenarios.

#### Query Parameters

- `page` (integer, default: 1): Page number
- `page_size` (integer, default: 20, max: 100): Number of scenarios per page
- `scenario_type` (string, optional): Filter by scenario type
- `is_active` (boolean, optional): Filter by active status

#### Response

```json
{
  "scenarios": [
    {
      "id": 1,
      "name": "Self-Study Path",
      "description": "Independent study with minimal external resources",
      "scenario_type": "self_study",
      "materials_multiplier": 1.0,
      "training_multiplier": 0.0,
      "retake_probability": 0.25,
      "includes_training": false,
      "includes_mentoring": false,
      "includes_practice_exams": true,
      "study_time_multiplier": 1.2,
      "preparation_weeks": 12,
      "is_active": true,
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T10:00:00Z"
    }
  ],
  "total_count": 1,
  "page": 1,
  "page_size": 20,
  "total_pages": 1
}
```

### Update Cost Scenario

**PUT** `/scenarios/{scenario_id}`

Update an existing cost scenario.

#### Request Body

```json
{
  "name": "Updated Scenario Name",
  "retake_probability": 0.15,
  "preparation_weeks": 16
}
```

## Cost Calculations

### Create Cost Calculation

**POST** `/calculations`

Create a new cost calculation for certification paths.

#### Request Body

```json
{
  "name": "Security+ and CISSP Path",
  "description": "Cost calculation for entry to advanced security path",
  "certification_ids": [1, 2],
  "base_currency": "USD",
  "target_currency": "EUR",
  "scenario_id": 1,
  "materials_cost": 500.0,
  "additional_costs": 200.0,
  "is_saved": true
}
```

#### Response

```json
{
  "id": 1,
  "user_id": "test_user_1",
  "name": "Security+ and CISSP Path",
  "description": "Cost calculation for entry to advanced security path",
  "base_currency": "USD",
  "target_currency": "EUR",
  "scenario_id": 1,
  "certification_ids": [1, 2],
  "certification_count": 2,
  "cost_breakdown": {
    "exam_fees": 1098.0,
    "materials": 500.0,
    "training": 0.0,
    "retakes": 274.5,
    "additional": 200.0
  },
  "totals": {
    "base_currency": 2072.5,
    "target_currency": 1906.7,
    "exchange_rate": 0.92
  },
  "time_estimates": {
    "study_hours": 240,
    "weeks": 16
  },
  "average_cost_per_certification": 953.35,
  "calculation_date": "2024-01-15T10:00:00Z",
  "is_saved": true,
  "is_shared": false,
  "scenario": {
    "id": 1,
    "name": "Self-Study Path",
    "scenario_type": "self_study"
  },
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T10:00:00Z"
}
```

### Get Cost Calculations

**GET** `/calculations`

Retrieve paginated list of cost calculations for the current user.

#### Query Parameters

- `page` (integer, default: 1): Page number
- `page_size` (integer, default: 20, max: 100): Number of calculations per page
- `is_saved` (boolean, optional): Filter by saved status

### Get Cost Calculation by ID

**GET** `/calculations/{calculation_id}`

Retrieve a specific cost calculation by ID.

### Update Cost Calculation

**PUT** `/calculations/{calculation_id}`

Update an existing cost calculation.

#### Request Body

```json
{
  "name": "Updated Calculation Name",
  "target_currency": "GBP",
  "materials_cost": 600.0
}
```

### Delete Cost Calculation

**DELETE** `/calculations/{calculation_id}`

Delete a cost calculation.

#### Response

Returns `204 No Content` on success.

## Advanced Features

### Compare Cost Calculations

**POST** `/calculations/compare`

Compare multiple cost calculations to identify the most cost-effective options.

#### Request Body

```json
{
  "calculation_ids": [1, 2, 3],
  "comparison_currency": "USD",
  "include_time_analysis": true
}
```

#### Response

```json
{
  "calculations": [
    {
      "id": 1,
      "name": "Self-Study Path",
      "totals": {
        "target_currency": 1500.0
      }
    },
    {
      "id": 2,
      "name": "Bootcamp Path",
      "totals": {
        "target_currency": 3500.0
      }
    }
  ],
  "comparison_currency": "USD",
  "summary": {
    "lowest_cost": 1500.0,
    "highest_cost": 3500.0,
    "average_cost": 2500.0,
    "cost_range": 2000.0,
    "total_certifications": 4
  },
  "recommendations": [
    "Consider the self-study path for cost savings",
    "Factor in retake probability for budget planning",
    "Compare training vs self-study paths based on your learning style"
  ]
}
```

### Bulk Cost Calculations

**POST** `/calculations/bulk`

Create multiple cost calculations in a single request with optional bulk discounts.

#### Request Body

```json
{
  "calculations": [
    {
      "name": "Security Path 1",
      "certification_ids": [1, 2],
      "materials_cost": 500.0
    },
    {
      "name": "Security Path 2",
      "certification_ids": [3, 4],
      "materials_cost": 600.0
    }
  ],
  "apply_bulk_discount": true,
  "bulk_discount_percentage": 10.0
}
```

#### Response

Returns an array of created cost calculations with applied discounts.

## Supported Currencies

The API supports the following currency codes (ISO 4217):

- `USD` - US Dollar
- `EUR` - Euro
- `GBP` - British Pound
- `CAD` - Canadian Dollar
- `AUD` - Australian Dollar
- `JPY` - Japanese Yen
- `CHF` - Swiss Franc
- `CNY` - Chinese Yuan
- `INR` - Indian Rupee
- `NZD` - New Zealand Dollar

## Cost Calculation Components

### Exam Fees
Base certification exam costs retrieved from the certification database.

### Materials Cost
Additional study materials including:
- Books and study guides
- Practice exams
- Online courses
- Lab access

### Training Cost
Formal training costs including:
- Instructor-led training
- Bootcamps
- University courses
- Corporate training programs

### Retake Cost
Estimated cost for exam retakes based on:
- Historical pass rates
- Scenario-specific retake probability
- Individual preparation level

### Additional Costs
Other certification-related expenses:
- Travel and accommodation
- Membership fees
- Continuing education
- Equipment and software

## Error Responses

### Validation Error (422)

```json
{
  "detail": [
    {
      "loc": ["body", "certification_ids"],
      "msg": "ensure this value has at least 1 items",
      "type": "value_error.list.min_items",
      "ctx": {"limit_value": 1}
    }
  ]
}
```

### Not Found (404)

```json
{
  "detail": "Cost calculation not found"
}
```

### Bad Request (400)

```json
{
  "detail": "Certifications not found: [999]"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Cost Calculations**: 50 requests per hour per user
- **Currency Rates**: 20 requests per hour per user
- **Bulk Operations**: 10 requests per hour per user

## Best Practices

1. **Currency Management**: Update exchange rates regularly for accurate calculations
2. **Scenario Selection**: Choose appropriate scenarios based on learning preferences
3. **Cost Estimation**: Include all potential costs for realistic budgeting
4. **Comparison Analysis**: Use comparison features to evaluate multiple paths
5. **Bulk Operations**: Use bulk endpoints for efficiency when creating multiple calculations

## SDK Examples

### Python

```python
import requests

# Create a cost calculation
calculation_data = {
    "name": "Security Professional Path",
    "certification_ids": [1, 2, 3],
    "base_currency": "USD",
    "target_currency": "EUR",
    "scenario_id": 1,
    "materials_cost": 800.0,
    "is_saved": True
}

response = requests.post(
    '/api/v1/cost-calculator/calculations',
    json=calculation_data
)
calculation = response.json()

# Compare multiple calculations
comparison_data = {
    "calculation_ids": [1, 2, 3],
    "comparison_currency": "USD"
}

comparison = requests.post(
    '/api/v1/cost-calculator/calculations/compare',
    json=comparison_data
).json()

print(f"Lowest cost option: ${comparison['summary']['lowest_cost']}")
```

### JavaScript

```javascript
// Create cost calculation
const calculationData = {
  name: "Cloud Security Path",
  certification_ids: [4, 5],
  base_currency: "USD",
  target_currency: "GBP",
  scenario_id: 2,
  materials_cost: 600.0
};

const response = await fetch('/api/v1/cost-calculator/calculations', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(calculationData)
});

const calculation = await response.json();
console.log(`Total cost: ${calculation.totals.target_currency} ${calculation.target_currency}`);
```
