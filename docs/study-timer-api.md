# Study Timer API Documentation

## Overview

The Study Timer API provides comprehensive functionality for tracking study sessions, managing study goals, and monitoring progress streaks. This API is designed to help users maintain consistent study habits and track their certification preparation progress.

## Base URL

All API endpoints are prefixed with `/api/v1/study-timer`

## Authentication

All endpoints require user authentication. The current implementation uses a placeholder authentication system that returns `test_user_1` as the user ID.

## Study Sessions

### Create Study Session

**POST** `/sessions`

Create a new study session for the authenticated user.

#### Request Body

```json
{
  "certification_id": 1,
  "learning_path_item_id": null,
  "planned_duration_minutes": 90,
  "session_type": "study",
  "topic": "Network Security Fundamentals"
}
```

#### Response

```json
{
  "id": 1,
  "user_id": "test_user_1",
  "certification_id": 1,
  "learning_path_item_id": null,
  "start_time": null,
  "end_time": null,
  "planned_duration_minutes": 90,
  "actual_duration_minutes": null,
  "session_type": "study",
  "topic": "Network Security Fundamentals",
  "notes": null,
  "progress_percentage": null,
  "focus_rating": null,
  "difficulty_rating": null,
  "satisfaction_rating": null,
  "status": "planned",
  "is_break": false,
  "break_duration_minutes": null,
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T10:00:00Z"
}
```

### Get Study Sessions

**GET** `/sessions`

Retrieve paginated list of study sessions with optional filters.

#### Query Parameters

- `page` (integer, default: 1): Page number
- `page_size` (integer, default: 20, max: 100): Number of sessions per page
- `status` (string, optional): Filter by session status
- `certification_id` (integer, optional): Filter by certification ID
- `start_date` (date, optional): Filter by start date (YYYY-MM-DD)
- `end_date` (date, optional): Filter by end date (YYYY-MM-DD)

#### Response

```json
{
  "sessions": [
    {
      "id": 1,
      "user_id": "test_user_1",
      "certification_id": 1,
      "planned_duration_minutes": 90,
      "session_type": "study",
      "topic": "Network Security Fundamentals",
      "status": "completed",
      "created_at": "2024-01-15T10:00:00Z"
    }
  ],
  "total_count": 1,
  "page": 1,
  "page_size": 20,
  "total_pages": 1
}
```

### Get Study Session by ID

**GET** `/sessions/{session_id}`

Retrieve a specific study session by ID.

#### Response

```json
{
  "id": 1,
  "user_id": "test_user_1",
  "certification_id": 1,
  "planned_duration_minutes": 90,
  "actual_duration_minutes": 85,
  "session_type": "study",
  "topic": "Network Security Fundamentals",
  "status": "completed",
  "focus_rating": 4,
  "difficulty_rating": 3,
  "satisfaction_rating": 5,
  "created_at": "2024-01-15T10:00:00Z",
  "updated_at": "2024-01-15T11:30:00Z"
}
```

### Update Study Session

**PUT** `/sessions/{session_id}`

Update a study session.

#### Request Body

```json
{
  "topic": "Updated Network Security Topic",
  "notes": "Made good progress on encryption concepts",
  "progress_percentage": 75.0,
  "focus_rating": 4,
  "difficulty_rating": 3,
  "satisfaction_rating": 5
}
```

### Control Study Session

**POST** `/sessions/{session_id}/control`

Control a study session (start, pause, resume, complete, cancel).

#### Request Body

```json
{
  "action": "complete",
  "notes": "Completed chapter on cryptography",
  "focus_rating": 4,
  "difficulty_rating": 3,
  "satisfaction_rating": 5
}
```

#### Valid Actions

- `start`: Start a planned session
- `pause`: Pause an active session
- `resume`: Resume a paused session
- `complete`: Complete an active or paused session
- `cancel`: Cancel any session

### Delete Study Session

**DELETE** `/sessions/{session_id}`

Delete a study session.

#### Response

Returns `204 No Content` on success.

## Study Goals

### Create Study Goal

**POST** `/goals`

Create a new study goal.

#### Request Body

```json
{
  "certification_id": 1,
  "goal_type": "weekly_time",
  "title": "Study 10 hours per week",
  "description": "Maintain consistent study schedule",
  "target_value": 600.0,
  "target_date": "2024-02-15T00:00:00Z",
  "reminder_enabled": true
}
```

#### Goal Types

- `daily_time`: Daily study time target (in minutes)
- `weekly_time`: Weekly study time target (in minutes)
- `monthly_time`: Monthly study time target (in minutes)
- `certification_deadline`: Certification completion deadline (percentage)
- `progress_milestone`: Progress milestone (percentage)

### Get Study Goals

**GET** `/goals`

Retrieve paginated list of study goals with optional filters.

#### Query Parameters

- `page` (integer, default: 1): Page number
- `page_size` (integer, default: 20, max: 100): Number of goals per page
- `is_active` (boolean, optional): Filter by active status
- `goal_type` (string, optional): Filter by goal type

#### Response

```json
{
  "goals": [
    {
      "id": 1,
      "user_id": "test_user_1",
      "certification_id": 1,
      "goal_type": "weekly_time",
      "title": "Study 10 hours per week",
      "target_value": 600.0,
      "current_value": 300.0,
      "progress_percentage": 50.0,
      "is_achieved": false,
      "is_active": true,
      "is_overdue": false,
      "created_at": "2024-01-15T10:00:00Z"
    }
  ],
  "total_count": 1,
  "page": 1,
  "page_size": 20,
  "total_pages": 1
}
```

### Update Study Goal

**PUT** `/goals/{goal_id}`

Update a study goal.

#### Request Body

```json
{
  "title": "Updated goal title",
  "target_value": 720.0,
  "is_active": true
}
```

### Delete Study Goal

**DELETE** `/goals/{goal_id}`

Delete a study goal.

#### Response

Returns `204 No Content` on success.

## Study Streak

### Get Study Streak

**GET** `/streak`

Get study streak information for the current user.

#### Response

```json
{
  "id": 1,
  "user_id": "test_user_1",
  "current_streak_days": 7,
  "current_streak_start": "2024-01-08T10:00:00Z",
  "last_study_date": "2024-01-15T10:00:00Z",
  "longest_streak_days": 14,
  "longest_streak_start": "2023-12-01T10:00:00Z",
  "longest_streak_end": "2023-12-15T10:00:00Z",
  "total_study_days": 45,
  "total_study_minutes": 2700,
  "total_sessions": 60,
  "achievements": null,
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-15T10:00:00Z"
}
```

## Study Statistics

### Get Study Statistics

**GET** `/statistics`

Get comprehensive study statistics for the current user.

#### Response

```json
{
  "total_sessions": 45,
  "total_study_minutes": 2700,
  "average_session_duration": 60.0,
  "total_study_days": 30,
  "current_streak": 7,
  "longest_streak": 14,
  "sessions_this_week": 5,
  "minutes_this_week": 300,
  "sessions_this_month": 20,
  "minutes_this_month": 1200,
  "focus_rating_average": 4.2,
  "difficulty_rating_average": 3.5,
  "satisfaction_rating_average": 4.5
}
```

## Error Responses

### Validation Error (422)

```json
{
  "detail": [
    {
      "loc": ["body", "planned_duration_minutes"],
      "msg": "ensure this value is greater than or equal to 5",
      "type": "value_error.number.not_ge",
      "ctx": {"limit_value": 5}
    }
  ]
}
```

### Not Found (404)

```json
{
  "detail": "Study session not found"
}
```

### Bad Request (400)

```json
{
  "detail": "Certification with ID 999 not found"
}
```

### Internal Server Error (500)

```json
{
  "detail": "Internal server error"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Study Sessions**: 100 requests per hour per user
- **Study Goals**: 50 requests per hour per user
- **Statistics**: 20 requests per hour per user

## Data Validation

### Study Sessions

- `planned_duration_minutes`: Must be between 5 and 480 minutes (8 hours)
- `session_type`: Must be one of "study", "practice", "review"
- `status`: Must be one of "planned", "active", "paused", "completed", "cancelled"
- `focus_rating`, `difficulty_rating`, `satisfaction_rating`: Must be between 1 and 5
- `progress_percentage`: Must be between 0 and 100

### Study Goals

- `target_value`: Must be greater than 0
- `goal_type`: Must be one of the valid goal types
- `target_date`: Must be in the future (if provided)
- `title`: Must be between 1 and 200 characters

## Best Practices

1. **Session Management**: Always start sessions before marking them as active
2. **Goal Setting**: Set realistic and achievable goals
3. **Progress Tracking**: Update session progress regularly for accurate statistics
4. **Error Handling**: Implement proper error handling for all API calls
5. **Pagination**: Use appropriate page sizes to avoid performance issues

## SDK Examples

### JavaScript/TypeScript

```typescript
// Create a study session
const session = await fetch('/api/v1/study-timer/sessions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    certification_id: 1,
    planned_duration_minutes: 90,
    session_type: 'study',
    topic: 'Network Security'
  })
});

// Start the session
await fetch(`/api/v1/study-timer/sessions/${session.id}/control`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'start' })
});

// Complete the session
await fetch(`/api/v1/study-timer/sessions/${session.id}/control`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'complete',
    focus_rating: 4,
    satisfaction_rating: 5
  })
});
```

### Python

```python
import requests

# Create a study session
response = requests.post('/api/v1/study-timer/sessions', json={
    'certification_id': 1,
    'planned_duration_minutes': 90,
    'session_type': 'study',
    'topic': 'Network Security'
})
session = response.json()

# Start the session
requests.post(f'/api/v1/study-timer/sessions/{session["id"]}/control', 
              json={'action': 'start'})

# Complete the session
requests.post(f'/api/v1/study-timer/sessions/{session["id"]}/control', 
              json={
                  'action': 'complete',
                  'focus_rating': 4,
                  'satisfaction_rating': 5
              })
```
