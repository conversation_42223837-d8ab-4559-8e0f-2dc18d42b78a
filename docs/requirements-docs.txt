# CertRats Documentation Requirements
# 
# This file contains all the Python packages required to build
# the Sphinx documentation for the CertRats platform.

# Core Sphinx packages
sphinx>=7.2.6
sphinx-rtd-theme>=2.0.0
sphinx-copybutton>=0.5.2
sphinx-tabs>=3.4.5
sphinx-design>=0.5.0
sphinx-togglebutton>=0.3.2
sphinx-inline-tabs>=2023.4.21

# Markdown support
myst-parser>=2.0.0

# API documentation
sphinx-autodoc-typehints>=1.25.2
sphinx-autoapi>=3.0.0

# Social media integration
sphinxext-opengraph>=0.9.1

# Additional extensions
sphinx-sitemap>=2.6.0
sphinx-notfound-page>=1.0.0
sphinx-external-toc>=1.0.1

# Development and testing
sphinx-autobuild>=2024.2.4
doc8>=1.1.1
rstcheck>=6.2.0

# Theme customization
sphinx-book-theme>=1.1.2

# Diagram support (for Mermaid integration)
sphinxcontrib-mermaid>=0.9.2

# PDF generation support
rst2pdf>=0.101

# Internationalization
sphinx-intl>=2.1.0

# Performance and optimization
sphinx-minify>=0.1.0

# Code highlighting
pygments>=2.17.2

# Additional utilities
requests>=2.31.0
beautifulsoup4>=4.12.2
lxml>=5.1.0

# Quality assurance
linkchecker>=10.4.0
