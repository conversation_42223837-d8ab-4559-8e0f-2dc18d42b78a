/**
 * Mermaid diagram styling for Sphinx documentation
 * Provides beautiful, responsive styling for Mermaid diagrams
 */

/* Base mermaid diagram container */
.mermaid-diagram {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #ffffff;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: auto;
    position: relative;
    text-align: center;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .mermaid-diagram {
        background: #1e1e1e;
        border-color: #404040;
        color: #ffffff;
    }
}

/* Rendered mermaid diagrams */
.mermaid-diagram.mermaid-rendered {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mermaid-diagram.mermaid-rendered svg {
    max-width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

/* RST specific styling */
.mermaid-diagram.rst-mermaid {
    border-left: 4px solid #2980b9;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* Error handling */
.mermaid-error-container {
    background: #fff5f5;
    border-color: #fed7d7;
    color: #c53030;
}

.mermaid-error {
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    background: #fed7d7;
    border-radius: 4px;
    margin: 0.5rem 0;
}

/* Zoom controls */
.mermaid-zoom-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.mermaid-diagram:hover .mermaid-zoom-controls {
    opacity: 1;
}

.zoom-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.zoom-btn:hover {
    background: #ffffff;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.zoom-btn:active {
    transform: scale(0.95);
}

/* Fullscreen button */
.fullscreen-btn {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
}

.mermaid-diagram:hover .fullscreen-btn {
    opacity: 1;
}

.fullscreen-btn:hover {
    background: #ffffff;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Fullscreen mode */
.mermaid-diagram:fullscreen {
    background: #ffffff;
    padding: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mermaid-diagram:fullscreen svg {
    max-width: 90vw;
    max-height: 90vh;
}

/* Responsive design */
@media (max-width: 768px) {
    .mermaid-diagram {
        margin: 1rem -1rem;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
    
    .mermaid-zoom-controls {
        position: static;
        opacity: 1;
        justify-content: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e1e4e8;
    }
    
    .fullscreen-btn {
        position: static;
        opacity: 1;
        margin: 1rem auto 0;
    }
}

/* Specific diagram type styling */

/* Flowchart styling */
.mermaid-diagram svg .node rect,
.mermaid-diagram svg .node circle,
.mermaid-diagram svg .node ellipse,
.mermaid-diagram svg .node polygon {
    stroke-width: 2px;
    transition: all 0.3s ease;
}

.mermaid-diagram svg .node:hover rect,
.mermaid-diagram svg .node:hover circle,
.mermaid-diagram svg .node:hover ellipse,
.mermaid-diagram svg .node:hover polygon {
    stroke-width: 3px;
    filter: brightness(1.1);
}

/* Sequence diagram styling */
.mermaid-diagram svg .actor {
    transition: all 0.3s ease;
}

.mermaid-diagram svg .actor:hover {
    filter: brightness(1.1);
}

/* Gantt chart styling */
.mermaid-diagram svg .task {
    transition: all 0.3s ease;
}

.mermaid-diagram svg .task:hover {
    filter: brightness(1.1);
}

/* Graph styling */
.mermaid-diagram svg .edgePath path {
    stroke-width: 2px;
    transition: stroke-width 0.3s ease;
}

.mermaid-diagram svg .edgePath:hover path {
    stroke-width: 3px;
}

/* Loading animation */
.mermaid-diagram.loading {
    position: relative;
}

.mermaid-diagram.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2980b9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print styles */
@media print {
    .mermaid-diagram {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .mermaid-zoom-controls,
    .fullscreen-btn {
        display: none;
    }
    
    .mermaid-diagram svg {
        max-width: 100% !important;
        height: auto !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .mermaid-diagram {
        border: 2px solid #000;
        background: #fff;
    }
    
    .zoom-btn,
    .fullscreen-btn {
        border: 1px solid #000;
        background: #fff;
        color: #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .mermaid-diagram svg,
    .zoom-btn,
    .fullscreen-btn,
    .mermaid-zoom-controls {
        transition: none;
    }
    
    .mermaid-diagram.loading::before {
        animation: none;
    }
}

/* Focus styles for accessibility */
.zoom-btn:focus,
.fullscreen-btn:focus {
    outline: 2px solid #2980b9;
    outline-offset: 2px;
}

/* Custom scrollbar for diagram containers */
.mermaid-diagram::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.mermaid-diagram::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.mermaid-diagram::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.mermaid-diagram::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Tooltip styling for diagram elements */
.mermaid-diagram .tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mermaid-diagram .tooltip.visible {
    opacity: 1;
}
