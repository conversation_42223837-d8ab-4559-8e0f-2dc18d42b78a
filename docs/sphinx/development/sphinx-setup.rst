🛠️ Sphinx Documentation Setup Guide
=====================================

**Complete Guide to CertRats Sphinx Documentation System**

This guide provides comprehensive instructions for setting up, configuring, and maintaining the Sphinx documentation system for the CertRats platform.

📋 Overview
-----------

The CertRats documentation system uses Sphinx, a powerful documentation generator that creates beautiful, searchable, and cross-referenced documentation from reStructuredText files.

**Key Features:**

* **Modern Theme** - Responsive design with dark mode support
* **Interactive Elements** - Mermaid diagrams, tabs, and code blocks
* **API Documentation** - Automatic API reference generation
* **Multi-format Output** - HTML, PDF, and ePub generation
* **Search Integration** - Full-text search with advanced filtering
* **Cross-references** - Automatic linking between documents
* **Internationalization** - Multi-language support ready

🚀 Quick Setup
--------------

**Prerequisites:**

.. code-block:: bash

   # Python 3.8+ required
   python --version

   # Install Sphinx and extensions
   pip install -r docs/requirements-docs.txt

**Build Documentation:**

.. code-block:: bash

   # Navigate to Sphinx directory
   cd docs/sphinx

   # Build HTML documentation
   make html

   # Open in browser
   open _build/html/index.html

🔧 Configuration Details
------------------------

**Sphinx Configuration (conf.py):**

.. code-block:: python

   # Project information
   project = 'CertRats'
   copyright = '2025, CertRats Team'
   author = 'CertRats Team'
   release = '1.0.0'
   version = '1.0'

   # Extensions for enhanced functionality
   extensions = [
       'sphinx.ext.autodoc',          # Automatic documentation from docstrings
       'sphinx.ext.viewcode',         # Source code links
       'sphinx.ext.napoleon',         # Google/NumPy docstring support
       'sphinx.ext.intersphinx',      # Cross-project references
       'sphinx.ext.todo',             # TODO items support
       'sphinx.ext.coverage',         # Documentation coverage
       'sphinx.ext.ifconfig',         # Conditional content
       'sphinx_tabs.tabs',            # Tabbed content
       'sphinx_copybutton',           # Copy button for code blocks
       'myst_parser',                 # Markdown support
       'sphinxext.opengraph',         # Social media meta tags
       'sphinx_design',               # Modern design elements
       'sphinx_togglebutton',         # Toggle buttons
       'sphinx_inline_tabs',          # Inline tabs
   ]

   # Source file configuration
   source_suffix = {
       '.rst': None,
       '.md': 'myst_parser',
   }

   # Master document
   master_doc = 'index'

   # Language and localization
   language = 'en_GB'
   locale_dirs = ['locale/']
   gettext_compact = False

**Theme Configuration:**

.. code-block:: python

   # HTML theme settings
   html_theme = 'sphinx_rtd_theme'
   html_theme_options = {
       'analytics_id': 'G-XXXXXXXXXX',
       'analytics_anonymize_ip': False,
       'logo_only': False,
       'display_version': True,
       'prev_next_buttons_location': 'bottom',
       'style_external_links': False,
       'vcs_pageview_mode': '',
       'style_nav_header_background': '#2980B9',
       'collapse_navigation': True,
       'sticky_navigation': True,
       'navigation_depth': 4,
       'includehidden': True,
       'titles_only': False
   }

   # Custom CSS and JavaScript
   html_static_path = ['_static']
   html_css_files = [
       'custom.css',
       'mermaid.css',
   ]
   html_js_files = [
       'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js',
       'mermaid-init.js',
       'custom.js',
   ]

📁 Directory Structure
----------------------

**Documentation Organization:**

.. code-block:: text

   docs/sphinx/
   ├── conf.py                     # Sphinx configuration
   ├── index.rst                   # Main documentation index
   ├── Makefile                    # Build automation
   ├── make.bat                    # Windows build script
   ├── requirements-docs.txt       # Python dependencies
   ├── _static/                    # Static assets
   │   ├── custom.css             # Custom styling
   │   ├── custom.js              # Custom JavaScript
   │   ├── mermaid.css            # Mermaid diagram styling
   │   ├── mermaid-init.js        # Mermaid initialization
   │   └── images/                # Documentation images
   ├── _templates/                 # Custom templates
   │   ├── layout.html            # Base layout template
   │   ├── breadcrumbs.html       # Navigation breadcrumbs
   │   └── searchbox.html         # Custom search box
   ├── _build/                     # Generated output
   │   ├── html/                  # HTML documentation
   │   ├── latex/                 # LaTeX output
   │   └── doctrees/              # Sphinx doctrees
   ├── api/                        # API reference documentation
   ├── guides/                     # User and developer guides
   ├── faq/                        # Frequently asked questions
   ├── prds/                       # Product requirement documents
   ├── development/                # Development documentation
   ├── enterprise/                 # Enterprise features
   └── ai/                         # AI and ML documentation

🎨 Custom Styling
-----------------

**Custom CSS (_static/custom.css):**

.. code-block:: css

   /* CertRats Documentation Custom Styles */
   
   /* Color scheme */
   :root {
       --certrats-primary: #2980B9;
       --certrats-secondary: #3498DB;
       --certrats-accent: #E74C3C;
       --certrats-success: #27AE60;
       --certrats-warning: #F39C12;
       --certrats-dark: #2C3E50;
   }

   /* Enhanced navigation */
   .wy-nav-side {
       background: linear-gradient(180deg, var(--certrats-primary) 0%, var(--certrats-dark) 100%);
   }

   /* Improved code blocks */
   .highlight {
       border-radius: 8px;
       box-shadow: 0 2px 4px rgba(0,0,0,0.1);
   }

   /* Responsive design improvements */
   @media screen and (max-width: 768px) {
       .wy-nav-content-wrap {
           margin-left: 0;
       }
   }

   /* Mermaid diagram styling */
   .mermaid {
       text-align: center;
       margin: 20px 0;
   }

   /* Custom admonitions */
   .admonition.tip {
       border-left: 4px solid var(--certrats-success);
   }

   .admonition.warning {
       border-left: 4px solid var(--certrats-warning);
   }

**Mermaid Integration (_static/mermaid-init.js):**

.. code-block:: javascript

   // Initialize Mermaid diagrams
   document.addEventListener('DOMContentLoaded', function() {
       mermaid.initialize({
           startOnLoad: true,
           theme: 'default',
           themeVariables: {
               primaryColor: '#2980B9',
               primaryTextColor: '#2C3E50',
               primaryBorderColor: '#3498DB',
               lineColor: '#34495E',
               secondaryColor: '#ECF0F1',
               tertiaryColor: '#F8F9FA'
           },
           flowchart: {
               useMaxWidth: true,
               htmlLabels: true,
               curve: 'basis'
           },
           sequence: {
               diagramMarginX: 50,
               diagramMarginY: 10,
               actorMargin: 50,
               width: 150,
               height: 65,
               boxMargin: 10,
               boxTextMargin: 5,
               noteMargin: 10,
               messageMargin: 35
           }
       });
   });

📝 Writing Documentation
------------------------

**reStructuredText Basics:**

.. code-block:: rst

   Main Title
   ==========

   Section Title
   -------------

   Subsection
   ~~~~~~~~~~

   **Bold text** and *italic text*

   - Bullet point
   - Another point

   1. Numbered list
   2. Second item

   `Inline code` and::

       Code block
       with syntax highlighting

   .. note::
      This is a note admonition

   .. warning::
      This is a warning admonition

**Cross-References:**

.. code-block:: rst

   # Link to other documents
   :doc:`user_guide`
   :doc:`api/authentication`

   # Link to sections
   :ref:`installation-guide`

   # External links
   `Sphinx Documentation <https://www.sphinx-doc.org/>`_

**API Documentation:**

.. code-block:: rst

   .. automodule:: certrats.api.authentication
      :members:
      :undoc-members:
      :show-inheritance:

   .. autoclass:: certrats.models.User
      :members:
      :inherited-members:

**HTTP API Documentation:**

.. code-block:: rst

   .. http:get:: /api/v1/users

      Get user information

      **Example request**:

      .. sourcecode:: http

         GET /api/v1/users HTTP/1.1
         Host: api.certrats.com
         Authorization: Bearer <token>

      **Example response**:

      .. sourcecode:: http

         HTTP/1.1 200 OK
         Content-Type: application/json

         {
           "id": 123,
           "username": "john_doe",
           "email": "<EMAIL>"
         }

      :statuscode 200: Success
      :statuscode 401: Unauthorized
      :statuscode 404: User not found

🔧 Advanced Features
-------------------

**Mermaid Diagrams:**

.. code-block:: rst

   .. mermaid::

      flowchart TD
          A[User Login] --> B[Dashboard]
          B --> C[Certification Explorer]
          C --> D[Learning Path]
          D --> E[Progress Tracking]

**Tabbed Content:**

.. code-block:: rst

   .. tabs::

      .. tab:: Python

         .. code-block:: python

            import requests
            response = requests.get('/api/users')

      .. tab:: JavaScript

         .. code-block:: javascript

            fetch('/api/users')
              .then(response => response.json())

      .. tab:: cURL

         .. code-block:: bash

            curl -X GET /api/users

**Design Elements:**

.. code-block:: rst

   .. grid:: 2

      .. grid-item-card:: Feature 1
         :img-top: _static/images/feature1.png

         Description of feature 1

      .. grid-item-card:: Feature 2
         :img-top: _static/images/feature2.png

         Description of feature 2

🚀 Build and Deployment
-----------------------

**Local Development:**

.. code-block:: bash

   # Install dependencies
   pip install -r requirements-docs.txt

   # Build documentation
   cd docs/sphinx
   make html

   # Live reload during development
   sphinx-autobuild . _build/html

   # Clean build
   make clean html

**Production Build:**

.. code-block:: bash

   # Build all formats
   make html latexpdf epub

   # Check for broken links
   make linkcheck

   # Check spelling
   make spelling

**GitHub Actions Deployment:**

.. code-block:: yaml

   name: Build and Deploy Documentation

   on:
     push:
       branches: [main]
       paths: ['docs/sphinx/**']

   jobs:
     build-and-deploy:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         
         - name: Setup Python
           uses: actions/setup-python@v4
           with:
             python-version: '3.9'
             
         - name: Install dependencies
           run: |
             pip install -r docs/requirements-docs.txt
             
         - name: Build documentation
           run: |
             cd docs/sphinx
             make html
             
         - name: Deploy to GitHub Pages
           uses: peaceiris/actions-gh-pages@v3
           with:
             github_token: ${{ secrets.GITHUB_TOKEN }}
             publish_dir: docs/sphinx/_build/html

🔍 Quality Assurance
--------------------

**Documentation Testing:**

.. code-block:: bash

   # Test documentation build
   sphinx-build -b dummy . _build/dummy

   # Check for warnings
   sphinx-build -W -b html . _build/html

   # Validate links
   sphinx-build -b linkcheck . _build/linkcheck

   # Check spelling
   sphinx-build -b spelling . _build/spelling

**Content Validation:**

.. code-block:: bash

   # Validate reStructuredText syntax
   rst-lint *.rst

   # Check for orphaned files
   sphinx-build -b dummy . _build/dummy 2>&1 | grep "WARNING: document isn't included"

   # Generate coverage report
   sphinx-build -b coverage . _build/coverage

📊 Analytics and Monitoring
---------------------------

**Google Analytics Integration:**

.. code-block:: html

   <!-- _templates/layout.html -->
   {% if theme_analytics_id %}
   <script async src="https://www.googletagmanager.com/gtag/js?id={{ theme_analytics_id }}"></script>
   <script>
     window.dataLayer = window.dataLayer || [];
     function gtag(){dataLayer.push(arguments);}
     gtag('js', new Date());
     gtag('config', '{{ theme_analytics_id }}');
   </script>
   {% endif %}

**Documentation Metrics:**

* Page views and popular content
* Search queries and results
* User engagement and time on page
* Download statistics for PDFs
* Cross-reference usage patterns

🔄 Maintenance
--------------

**Regular Tasks:**

.. code-block:: bash

   # Update dependencies
   pip install --upgrade sphinx sphinx-rtd-theme

   # Rebuild search index
   make clean html

   # Update translations
   sphinx-build -b gettext . _build/gettext
   sphinx-intl update -p _build/gettext -l en_GB

**Content Review:**

1. **Quarterly Reviews** - Update content for accuracy
2. **Version Updates** - Sync with platform releases
3. **User Feedback** - Incorporate user suggestions
4. **SEO Optimization** - Improve search visibility
5. **Accessibility Audit** - Ensure WCAG compliance

---

This comprehensive Sphinx setup guide ensures that the CertRats documentation system is maintainable, scalable, and provides an excellent user experience for all documentation consumers.
