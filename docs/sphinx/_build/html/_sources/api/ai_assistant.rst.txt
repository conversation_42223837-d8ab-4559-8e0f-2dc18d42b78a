AI Study Assistant API
=======================

The AI Study Assistant provides intelligent, personalised learning recommendations using on-device machine learning models. All processing occurs locally to ensure complete privacy.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The AI Study Assistant API offers:

- **Performance Prediction** - Estimate exam success probability
- **Difficulty Assessment** - Evaluate certification difficulty for individual users
- **Topic Recommendations** - Suggest optimal study topics and sequences
- **Learning Style Analysis** - Identify and adapt to user learning preferences
- **Study Plan Generation** - Create personalised study schedules

All AI processing occurs on-device with zero external dependencies, ensuring complete data privacy.

Core Endpoints
--------------

Get Comprehensive Recommendations
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Retrieve personalised certification and study recommendations.

.. code-block:: http

   POST /api/v1/ai-assistant/comprehensive-recommendations
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "user_id": 123,
     "current_skills": ["Network Security", "Incident Response"],
     "target_domain": "cloud_security",
     "learning_style": "hands_on",
     "available_hours_per_week": 10,
     "target_exam_date": "2025-06-01",
     "budget_range": "moderate"
   }

**Response**

.. code-block:: json

   {
     "user_id": 123,
     "recommendations": {
       "certifications": [
         {
           "id": 45,
           "name": "AWS Certified Security - Specialty",
           "provider": "Amazon Web Services",
           "success_probability": 78.5,
           "confidence_score": 0.85,
           "estimated_study_weeks": 12,
           "difficulty_level": "intermediate",
           "roi_score": 8.7,
           "prerequisites_met": true,
           "reasons": [
             "Strong alignment with cloud security goals",
             "Builds on existing network security knowledge",
             "High market demand and salary impact"
           ]
         }
       ],
       "study_plan": {
         "name": "AWS Security Specialty - Personalised Path",
         "total_weeks": 12,
         "hours_per_week": 10,
         "weekly_schedule": [
           {
             "week": 1,
             "topics": ["AWS IAM Fundamentals", "Security Groups"],
             "study_hours": 10,
             "practice_hours": 2
           }
         ]
       },
       "priority_topics": [
         {
           "name": "AWS Identity and Access Management",
           "importance_score": 9.2,
           "current_knowledge": 0.3,
           "estimated_hours": 15
         }
       ]
     },
     "ai_insights": {
       "key_recommendation": "Focus on hands-on AWS labs to match your learning style",
       "success_factors": ["Consistent study schedule", "Practical experience"],
       "potential_challenges": ["Time management", "Complex IAM policies"]
     },
     "generated_at": "2025-01-15T10:30:00Z"
   }

Skills Assessment
~~~~~~~~~~~~~~~~~

Evaluate user skills across security domains.

.. code-block:: http

   POST /api/v1/ai-assistant/skills-assessment
   Content-Type: application/json

   {
     "user_id": 123,
     "assessment_type": "comprehensive",
     "skill_assessments": [
       {
         "skill_id": 1,
         "skill_name": "Network Security",
         "skill_level": "intermediate",
         "confidence_level": 7,
         "years_experience": 2
       }
     ],
     "career_goals": ["Security Architect", "CISO"],
     "preferred_learning_style": "hands_on"
   }

**Response**

.. code-block:: json

   {
     "assessment_id": "assess_789",
     "user_id": 123,
     "overall_skill_level": "intermediate",
     "domain_scores": {
       "network_security": 7.2,
       "cloud_security": 4.1,
       "incident_response": 6.8,
       "governance_risk_compliance": 3.5
     },
     "recommendations": [
       {
         "type": "skill_development",
         "priority": "high",
         "description": "Strengthen cloud security fundamentals",
         "suggested_certifications": ["AWS Security Specialty", "Azure Security Engineer"]
       }
     ],
     "learning_path_suggestions": [
       {
         "path_name": "Cloud Security Mastery",
         "estimated_duration_months": 8,
         "certifications": ["AWS Security", "Azure Security", "CCSP"]
       }
     ],
     "assessment_date": "2025-01-15T10:30:00Z"
   }

Performance Prediction
~~~~~~~~~~~~~~~~~~~~~~

Predict exam success probability for specific certifications.

.. code-block:: http

   GET /api/v1/ai-assistant/performance-prediction
   Authorization: Bearer <token>

   ?certification_id=45&user_id=123&study_hours_planned=120

**Response**

.. code-block:: json

   {
     "certification_id": 45,
     "certification_name": "AWS Certified Security - Specialty",
     "user_id": 123,
     "prediction": {
       "success_probability": 78.5,
       "confidence_interval": [72.1, 84.9],
       "confidence_score": 0.85,
       "prediction_factors": {
         "experience_level": 0.7,
         "relevant_skills": 0.8,
         "study_time_planned": 0.75,
         "learning_style_match": 0.9
       }
     },
     "recommendations": {
       "optimal_study_hours": 140,
       "recommended_timeline_weeks": 14,
       "focus_areas": [
         "AWS IAM Advanced Concepts",
         "Security Monitoring and Logging"
       ]
     },
     "generated_at": "2025-01-15T10:30:00Z"
   }

Difficulty Assessment
~~~~~~~~~~~~~~~~~~~~~

Assess certification difficulty for individual users.

.. code-block:: http

   POST /api/v1/ai-assistant/difficulty-assessment
   Content-Type: application/json

   {
     "certification_id": 45,
     "user_profile": {
       "experience_years": 3,
       "current_skills": ["Network Security", "Linux Administration"],
       "completed_certifications": ["Security+", "Network+"],
       "learning_style": "visual"
     }
   }

**Response**

.. code-block:: json

   {
     "certification_id": 45,
     "certification_name": "AWS Certified Security - Specialty",
     "difficulty_assessment": {
       "overall_difficulty": "moderate",
       "difficulty_score": 6.2,
       "personalised_factors": {
         "knowledge_gaps": [
           {
             "topic": "AWS IAM",
             "gap_severity": "high",
             "estimated_study_hours": 25
           }
         ],
         "strengths": [
           {
             "topic": "Network Security Concepts",
             "advantage_level": "high"
           }
         ]
       },
       "time_estimates": {
         "minimum_study_hours": 100,
         "recommended_study_hours": 140,
         "intensive_study_hours": 80
       }
     },
     "preparation_recommendations": [
       "Complete AWS fundamentals course first",
       "Focus heavily on IAM and access management",
       "Practice with AWS hands-on labs"
     ]
   }

Study Techniques
~~~~~~~~~~~~~~~~

Get AI-recommended study techniques based on learning style.

.. code-block:: http

   GET /api/v1/ai-assistant/study-techniques
   Authorization: Bearer <token>

   ?learning_style=hands_on&effectiveness_threshold=0.7

**Response**

.. code-block:: json

   {
     "learning_style": "hands_on",
     "recommended_techniques": [
       {
         "technique": "Hands-on Labs",
         "effectiveness_score": 0.92,
         "description": "Practice with real AWS environments",
         "time_allocation": 0.4,
         "resources": [
           "AWS Free Tier Account",
           "Cloud Academy Labs",
           "A Cloud Guru Hands-on Labs"
         ]
       },
       {
         "technique": "Scenario-based Learning",
         "effectiveness_score": 0.85,
         "description": "Work through real-world security scenarios",
         "time_allocation": 0.3
       }
     ],
     "study_schedule_template": {
       "weekly_pattern": {
         "hands_on_practice": "40%",
         "theory_review": "30%",
         "practice_tests": "20%",
         "review_and_notes": "10%"
       }
     }
   }

Learning Analytics
------------------

Learning Style Analysis
~~~~~~~~~~~~~~~~~~~~~~~

Analyse user learning patterns to optimise study approaches.

.. code-block:: http

   GET /api/v1/ai-assistant/learning-style-analysis
   Authorization: Bearer <token>

**Response**

.. code-block:: json

   {
     "user_id": 123,
     "learning_style_profile": {
       "primary_style": "kinesthetic",
       "secondary_style": "visual",
       "confidence_score": 0.78,
       "learning_preferences": {
         "hands_on_practice": 0.85,
         "visual_diagrams": 0.72,
         "reading_text": 0.45,
         "video_content": 0.68
       }
     },
     "optimal_study_patterns": {
       "session_length_minutes": 45,
       "break_frequency_minutes": 15,
       "best_study_times": ["09:00-11:00", "14:00-16:00"],
       "weekly_study_days": 5
     },
     "personalised_recommendations": [
       "Use interactive labs and simulations",
       "Create visual mind maps for complex topics",
       "Take regular breaks to maintain focus"
     ]
   }

Progress Tracking Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Track learning progress and adjust AI recommendations.

.. code-block:: http

   POST /api/v1/ai-assistant/update-progress
   Content-Type: application/json

   {
     "user_id": 123,
     "study_session": {
       "certification_id": 45,
       "topics_studied": ["AWS IAM", "Security Groups"],
       "duration_minutes": 60,
       "confidence_rating": 7,
       "difficulty_experienced": "moderate"
     }
   }

**Response**

.. code-block:: json

   {
     "progress_updated": true,
     "updated_predictions": {
       "success_probability": 79.2,
       "confidence_score": 0.87
     },
     "adjusted_recommendations": [
       "Continue focus on IAM advanced topics",
       "Add more hands-on practice with Security Groups"
     ],
     "next_study_suggestions": [
       {
         "topic": "AWS CloudTrail",
         "priority": "high",
         "estimated_hours": 8
       }
     ]
   }

Model Training & Customisation
-------------------------------

Train AI Models
~~~~~~~~~~~~~~~

Update AI models with latest user data for improved predictions.

.. code-block:: http

   POST /api/v1/ai-assistant/train-models
   Authorization: Bearer <token>

   ?user_id=123

**Response**

.. code-block:: json

   {
     "message": "AI models trained successfully",
     "user_id": 123,
     "training_date": "2025-01-15T10:30:00Z",
     "models_updated": [
       "performance_predictor",
       "difficulty_estimator",
       "topic_recommender"
     ],
     "training_metrics": {
       "data_points_processed": 1250,
       "model_accuracy_improvement": 0.03,
       "training_duration_seconds": 45
     }
   }

Real-time Recommendations
~~~~~~~~~~~~~~~~~~~~~~~~~

Get instant recommendations based on current context.

.. code-block:: http

   POST /api/v1/ai-assistant/real-time-recommendations
   Content-Type: application/json

   {
     "user_id": 123,
     "context": "study_session",
     "current_topic": "AWS IAM",
     "session_duration_minutes": 30,
     "confidence_level": 6,
     "energy_level": "medium"
   }

**Response**

.. code-block:: json

   {
     "recommendations": [
       {
         "type": "topic_suggestion",
         "suggestion": "Move to IAM Policies hands-on practice",
         "reason": "Good understanding of concepts, ready for application",
         "confidence": 0.82
       },
       {
         "type": "break_suggestion",
         "suggestion": "Take a 10-minute break in 15 minutes",
         "reason": "Optimal learning retention timing"
       }
     ],
     "study_adjustments": {
       "increase_difficulty": false,
       "add_practice_questions": true,
       "extend_session": false
     }
   }

Error Handling
--------------

Common error responses for AI Assistant endpoints:

.. code-block:: json

   {
     "error": "insufficient_data",
     "message": "Not enough user data for accurate predictions",
     "code": 422,
     "suggestions": [
       "Complete skills assessment first",
       "Add more study session data"
     ]
   }

**Error Codes**

- ``422`` - Insufficient data for AI processing
- ``429`` - AI processing rate limit exceeded
- ``503`` - AI models temporarily unavailable

Privacy & Security
------------------

**Data Processing**

- All AI processing occurs on-device
- No user data sent to external services
- Models trained locally with user's own data
- Complete data ownership and control

**Model Security**

- Models encrypted at rest
- Secure model loading and execution
- No telemetry or usage tracking
- Full compliance with privacy regulations

See Also
--------

- :doc:`../ai/study_assistant` - Detailed AI features guide
- :doc:`../guides/ai_features_guide` - User guide for AI features
- :doc:`authentication` - API authentication
- :doc:`../development/ai_models` - AI model implementation details
