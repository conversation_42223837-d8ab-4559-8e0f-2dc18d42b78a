Enterprise API
==============

The Enterprise API provides comprehensive organisational management capabilities for multi-tenant deployments. It supports unlimited organisations with complete data isolation and advanced analytics.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Enterprise features include:

- **Multi-tenant Architecture** - Complete data isolation between organisations
- **Role-based Access Control** - Six hierarchical permission levels
- **Organisation Management** - Department structure and user administration
- **Advanced Analytics** - Real-time insights and executive reporting
- **Licence Management** - Usage tracking and optimisation
- **Compliance Reporting** - Automated regulatory compliance

All enterprise endpoints require appropriate administrative permissions.

Organisation Management
-----------------------

Create Organisation
~~~~~~~~~~~~~~~~~~~

Create a new organisation with initial configuration.

.. code-block:: http

   POST /api/v1/enterprise/organisations
   Content-Type: application/json
   Authorization: Bearer <admin_token>

   {
     "name": "Acme Corporation",
     "domain": "acme.com",
     "industry": "Financial Services",
     "size": "large",
     "country": "United Kingdom",
     "settings": {
       "enable_sso": true,
       "require_2fa": true,
       "data_retention_days": 2555,
       "allowed_domains": ["acme.com", "acme.co.uk"]
     },
     "billing": {
       "plan": "enterprise",
       "max_users": 1000,
       "billing_contact": "<EMAIL>"
     }
   }

**Response**

.. code-block:: json

   {
     "id": "org_12345",
     "name": "Acme Corporation",
     "domain": "acme.com",
     "status": "active",
     "created_at": "2025-01-15T10:30:00Z",
     "settings": {
       "enable_sso": true,
       "require_2fa": true,
       "data_retention_days": 2555,
       "allowed_domains": ["acme.com", "acme.co.uk"]
     },
     "limits": {
       "max_users": 1000,
       "current_users": 0,
       "max_departments": 50,
       "current_departments": 0
     },
     "api_keys": {
       "organisation_key": "org_key_abcd1234",
       "webhook_secret": "whsec_5678efgh"
     }
   }

Get Organisation Details
~~~~~~~~~~~~~~~~~~~~~~~~

Retrieve comprehensive organisation information.

.. code-block:: http

   GET /api/v1/enterprise/organisations/{org_id}
   Authorization: Bearer <admin_token>

**Response**

.. code-block:: json

   {
     "id": "org_12345",
     "name": "Acme Corporation",
     "domain": "acme.com",
     "status": "active",
     "created_at": "2025-01-15T10:30:00Z",
     "updated_at": "2025-01-15T10:30:00Z",
     "statistics": {
       "total_users": 245,
       "active_users_30d": 198,
       "total_departments": 12,
       "certifications_in_progress": 89,
       "certifications_completed": 156
     },
     "settings": {
       "enable_sso": true,
       "sso_provider": "Azure AD",
       "require_2fa": true,
       "password_policy": "strong",
       "session_timeout_minutes": 480
     },
     "compliance": {
       "gdpr_compliant": true,
       "soc2_compliant": true,
       "last_audit_date": "2024-12-15T00:00:00Z",
       "next_audit_due": "2025-06-15T00:00:00Z"
     }
   }

Update Organisation
~~~~~~~~~~~~~~~~~~~

Modify organisation settings and configuration.

.. code-block:: http

   PATCH /api/v1/enterprise/organisations/{org_id}
   Content-Type: application/json

   {
     "settings": {
       "require_2fa": false,
       "session_timeout_minutes": 720
     },
     "billing": {
       "max_users": 1500
     }
   }

Department Management
---------------------

Create Department
~~~~~~~~~~~~~~~~~

Create a new department within an organisation.

.. code-block:: http

   POST /api/v1/enterprise/organisations/{org_id}/departments
   Content-Type: application/json

   {
     "name": "Information Security",
     "description": "Cybersecurity and risk management team",
     "parent_department_id": null,
     "manager_user_id": 456,
     "budget_annual": 500000,
     "cost_centre": "IT-SEC-001",
     "settings": {
       "auto_enroll_certifications": ["Security+", "CISSP"],
       "mandatory_training": true,
       "budget_approval_required": true
     }
   }

**Response**

.. code-block:: json

   {
     "id": "dept_789",
     "organisation_id": "org_12345",
     "name": "Information Security",
     "description": "Cybersecurity and risk management team",
     "parent_department_id": null,
     "manager": {
       "user_id": 456,
       "name": "Sarah Johnson",
       "email": "<EMAIL>"
     },
     "statistics": {
       "total_users": 0,
       "budget_utilised": 0,
       "certifications_in_progress": 0
     },
     "created_at": "2025-01-15T10:30:00Z"
   }

List Departments
~~~~~~~~~~~~~~~~

Get all departments within an organisation.

.. code-block:: http

   GET /api/v1/enterprise/organisations/{org_id}/departments
   Authorization: Bearer <admin_token>

   ?include_stats=true&include_users=false

**Response**

.. code-block:: json

   {
     "departments": [
       {
         "id": "dept_789",
         "name": "Information Security",
         "manager_name": "Sarah Johnson",
         "user_count": 15,
         "budget_utilised_percentage": 23.5,
         "active_certifications": 8,
         "created_at": "2025-01-15T10:30:00Z"
       }
     ],
     "total_departments": 12,
     "organisation_id": "org_12345"
   }

User Management
---------------

Create Enterprise User
~~~~~~~~~~~~~~~~~~~~~~

Add a new user to the organisation.

.. code-block:: http

   POST /api/v1/enterprise/organisations/{org_id}/users
   Content-Type: application/json

   {
     "email": "<EMAIL>",
     "full_name": "John Smith",
     "role": "user",
     "department_id": "dept_789",
     "job_title": "Security Analyst",
     "manager_user_id": 456,
     "start_date": "2025-01-20",
     "permissions": {
       "can_access_ai_features": true,
       "can_export_data": false,
       "can_manage_team": false
     },
     "licence_type": "premium"
   }

**Response**

.. code-block:: json

   {
     "id": 789,
     "organisation_id": "org_12345",
     "email": "<EMAIL>",
     "full_name": "John Smith",
     "role": "user",
     "status": "active",
     "department": {
       "id": "dept_789",
       "name": "Information Security"
     },
     "licence": {
       "type": "premium",
       "expires_at": "2026-01-15T00:00:00Z",
       "features_enabled": ["ai_assistant", "advanced_analytics", "export_data"]
     },
     "created_at": "2025-01-15T10:30:00Z",
     "invitation_sent": true
   }

Bulk User Import
~~~~~~~~~~~~~~~~

Import multiple users from CSV or JSON data.

.. code-block:: http

   POST /api/v1/enterprise/organisations/{org_id}/users/bulk-import
   Content-Type: application/json

   {
     "import_format": "json",
     "users": [
       {
         "email": "<EMAIL>",
         "full_name": "Alice Brown",
         "department_name": "Information Security",
         "job_title": "Senior Security Engineer",
         "licence_type": "premium"
       }
     ],
     "options": {
       "send_invitations": true,
       "skip_duplicates": true,
       "default_role": "user"
     }
   }

**Response**

.. code-block:: json

   {
     "import_id": "import_abc123",
     "status": "processing",
     "total_users": 50,
     "processed_users": 0,
     "successful_imports": 0,
     "failed_imports": 0,
     "errors": [],
     "estimated_completion": "2025-01-15T10:35:00Z"
   }

Analytics & Reporting
---------------------

Organisation Analytics
~~~~~~~~~~~~~~~~~~~~~~

Get comprehensive organisation-wide analytics.

.. code-block:: http

   GET /api/v1/enterprise/organisations/{org_id}/analytics
   Authorization: Bearer <admin_token>

   ?period=30d&include_predictions=true

**Response**

.. code-block:: json

   {
     "organisation_id": "org_12345",
     "period": "30d",
     "generated_at": "2025-01-15T10:30:00Z",
     "user_engagement": {
       "total_active_users": 198,
       "daily_average_users": 156,
       "user_growth_rate": 0.12,
       "engagement_score": 8.7
     },
     "learning_progress": {
       "certifications_started": 45,
       "certifications_completed": 23,
       "average_completion_time_days": 89,
       "success_rate": 0.78
     },
     "cost_analysis": {
       "total_training_investment": 125000,
       "cost_per_certification": 2500,
       "roi_percentage": 245,
       "budget_utilisation": 0.67
     },
     "predictions": {
       "projected_completions_next_30d": 28,
       "estimated_budget_needed": 75000,
       "risk_factors": ["Q1 budget constraints", "Holiday season impact"]
     },
     "top_certifications": [
       {
         "name": "CISSP",
         "enrollments": 15,
         "completions": 8,
         "success_rate": 0.73
       }
     ]
   }

Department Analytics
~~~~~~~~~~~~~~~~~~~~

Get detailed analytics for specific departments.

.. code-block:: http

   GET /api/v1/enterprise/departments/{dept_id}/analytics
   Authorization: Bearer <admin_token>

**Response**

.. code-block:: json

   {
     "department_id": "dept_789",
     "department_name": "Information Security",
     "period": "30d",
     "team_performance": {
       "total_team_members": 15,
       "active_learners": 12,
       "team_engagement_score": 9.2,
       "average_study_hours_per_week": 8.5
     },
     "certification_progress": {
       "in_progress": 8,
       "completed_this_period": 3,
       "success_rate": 0.85,
       "average_completion_time": 76
     },
     "budget_tracking": {
       "allocated_budget": 50000,
       "spent_to_date": 23500,
       "projected_spend": 45000,
       "cost_per_completion": 2800
     },
     "skill_development": {
       "top_skills_developed": ["Cloud Security", "Incident Response"],
       "skill_gaps_identified": ["Zero Trust Architecture", "DevSecOps"],
       "recommended_training": ["CISSP", "AWS Security Specialty"]
     }
   }

Licence Management
------------------

Licence Usage Overview
~~~~~~~~~~~~~~~~~~~~~~

Monitor licence utilisation across the organisation.

.. code-block:: http

   GET /api/v1/enterprise/organisations/{org_id}/licences
   Authorization: Bearer <admin_token>

**Response**

.. code-block:: json

   {
     "organisation_id": "org_12345",
     "licence_summary": {
       "total_licences": 1000,
       "active_licences": 245,
       "available_licences": 755,
       "utilisation_rate": 0.245
     },
     "licence_types": {
       "basic": {
         "allocated": 500,
         "used": 123,
         "available": 377
       },
       "premium": {
         "allocated": 300,
         "used": 89,
         "available": 211
       },
       "enterprise": {
         "allocated": 200,
         "used": 33,
         "available": 167
       }
     },
     "usage_trends": {
       "monthly_growth": 0.08,
       "projected_usage_6m": 312,
       "recommended_licence_adjustment": "increase_premium_by_50"
     }
   }

Assign Licences
~~~~~~~~~~~~~~~

Assign or modify user licences.

.. code-block:: http

   POST /api/v1/enterprise/organisations/{org_id}/licences/assign
   Content-Type: application/json

   {
     "assignments": [
       {
         "user_id": 789,
         "licence_type": "premium",
         "expires_at": "2026-01-15T00:00:00Z"
       }
     ],
     "bulk_assignment": {
       "department_id": "dept_789",
       "licence_type": "premium",
       "duration_months": 12
     }
   }

Compliance & Audit
------------------

Compliance Report
~~~~~~~~~~~~~~~~~

Generate comprehensive compliance reports.

.. code-block:: http

   GET /api/v1/enterprise/organisations/{org_id}/compliance/report
   Authorization: Bearer <admin_token>

   ?standards=gdpr,soc2&format=json

**Response**

.. code-block:: json

   {
     "organisation_id": "org_12345",
     "report_date": "2025-01-15T10:30:00Z",
     "compliance_standards": {
       "gdpr": {
         "status": "compliant",
         "last_assessment": "2024-12-01T00:00:00Z",
         "requirements_met": 47,
         "total_requirements": 47,
         "compliance_score": 100
       },
       "soc2": {
         "status": "compliant",
         "last_assessment": "2024-11-15T00:00:00Z",
         "requirements_met": 64,
         "total_requirements": 67,
         "compliance_score": 95.5
       }
     },
     "data_governance": {
       "data_retention_policy": "active",
       "data_deletion_requests": 3,
       "data_export_requests": 12,
       "privacy_policy_version": "2.1"
     },
     "audit_trail": {
       "total_events": 15420,
       "security_events": 234,
       "access_events": 12890,
       "data_events": 2296
     }
   }

Audit Log
~~~~~~~~~

Access detailed audit logs for compliance and security monitoring.

.. code-block:: http

   GET /api/v1/enterprise/organisations/{org_id}/audit-log
   Authorization: Bearer <admin_token>

   ?start_date=2025-01-01&end_date=2025-01-15&event_type=security

**Response**

.. code-block:: json

   {
     "organisation_id": "org_12345",
     "period": {
       "start_date": "2025-01-01T00:00:00Z",
       "end_date": "2025-01-15T23:59:59Z"
     },
     "total_events": 1250,
     "events": [
       {
         "id": "audit_12345",
         "timestamp": "2025-01-15T09:30:00Z",
         "event_type": "security",
         "action": "login_failed",
         "user_id": 456,
         "user_email": "<EMAIL>",
         "ip_address": "*************",
         "user_agent": "Mozilla/5.0...",
         "details": {
           "reason": "invalid_password",
           "attempt_count": 3
         },
         "risk_level": "medium"
       }
     ],
     "summary": {
       "security_events": 45,
       "access_events": 890,
       "data_events": 315,
       "high_risk_events": 2
     }
   }

Error Handling
--------------

Enterprise API error responses:

.. code-block:: json

   {
     "error": "organisation_limit_exceeded",
     "message": "Maximum number of organisations reached for this account",
     "code": 403,
     "details": {
       "current_organisations": 10,
       "maximum_allowed": 10,
       "upgrade_required": true
     }
   }

**Common Error Codes**

- ``403`` - Insufficient permissions or limits exceeded
- ``404`` - Organisation or resource not found
- ``409`` - Conflict (duplicate organisation domain)
- ``422`` - Invalid organisation configuration
- ``429`` - Rate limit exceeded for enterprise operations

See Also
--------

- :doc:`../enterprise/dashboard` - Enterprise dashboard guide
- :doc:`../enterprise/multi_tenant` - Multi-tenant setup
- :doc:`../guides/enterprise_guide` - Enterprise user guide
- :doc:`authentication` - API authentication
- :doc:`../enterprise/compliance` - Compliance documentation
