Integration Hub API
===================

The Integration Hub API provides comprehensive integration capabilities for connecting CertPathFinder with external systems, learning management systems, and enterprise tools.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Integration Hub features include:

- **LMS Integration** - Connect with learning management systems
- **SSO Authentication** - Single sign-on with enterprise identity providers
- **Webhook Management** - Real-time event notifications
- **Data Synchronisation** - Bi-directional data sync with external systems
- **API Gateway** - Secure external API access management
- **Third-party Connectors** - Pre-built integrations with popular tools

All integrations maintain enterprise-grade security and compliance standards.

Learning Management System Integration
--------------------------------------

Configure LMS Integration
~~~~~~~~~~~~~~~~~~~~~~~~~

Set up integration with learning management systems.

.. code-block:: http

   POST /api/v1/integration-hub/lms/configure
   Content-Type: application/json
   Authorization: Bearer <admin_token>

   {
     "organisation_id": "org_12345",
     "lms_provider": "moodle",
     "configuration": {
       "base_url": "https://lms.company.com",
       "api_version": "3.9",
       "authentication": {
         "method": "token",
         "api_token": "moodle_api_token_here",
         "username": "certpathfinder_integration"
       },
       "sync_settings": {
         "sync_users": true,
         "sync_courses": true,
         "sync_progress": true,
         "sync_frequency": "daily",
         "sync_direction": "bidirectional"
       }
     },
     "field_mappings": {
       "user_fields": {
         "email": "email",
         "full_name": "fullname",
         "department": "department",
         "job_title": "institution"
       },
       "course_fields": {
         "certification_id": "idnumber",
         "course_name": "fullname",
         "description": "summary"
       }
     }
   }

**Response**

.. code-block:: json

   {
     "integration_id": "lms_integration_789",
     "organisation_id": "org_12345",
     "lms_provider": "moodle",
     "status": "configured",
     "created_at": "2025-01-15T10:30:00Z",
     "configuration_status": {
       "connection_test": "successful",
       "authentication": "verified",
       "permissions": "sufficient",
       "field_mapping": "validated"
     },
     "sync_capabilities": {
       "users": {
         "import": true,
         "export": true,
         "update": true
       },
       "courses": {
         "import": true,
         "export": false,
         "update": true
       },
       "progress": {
         "import": true,
         "export": true,
         "update": true
       }
     },
     "next_sync_scheduled": "2025-01-16T02:00:00Z",
     "webhook_endpoints": [
       {
         "event": "user_progress_updated",
         "url": "https://lms.company.com/webservice/rest/server.php",
         "method": "POST"
       }
     ]
   }

Synchronise LMS Data
~~~~~~~~~~~~~~~~~~~

Trigger data synchronisation with connected LMS.

.. code-block:: http

   POST /api/v1/integration-hub/lms/{integration_id}/sync
   Content-Type: application/json

   {
     "sync_type": "incremental",
     "sync_entities": ["users", "progress"],
     "date_range": {
       "start_date": "2025-01-01T00:00:00Z",
       "end_date": "2025-01-15T23:59:59Z"
     },
     "options": {
       "force_update": false,
       "skip_validation": false,
       "batch_size": 100
     }
   }

**Response**

.. code-block:: json

   {
     "sync_job_id": "sync_job_456",
     "integration_id": "lms_integration_789",
     "status": "running",
     "started_at": "2025-01-15T10:30:00Z",
     "estimated_completion": "2025-01-15T10:45:00Z",
     "progress": {
       "total_records": 500,
       "processed_records": 0,
       "successful_syncs": 0,
       "failed_syncs": 0,
       "current_entity": "users"
     },
     "sync_summary": {
       "entities_to_sync": ["users", "progress"],
       "sync_direction": "bidirectional",
       "batch_size": 100
     }
   }

Single Sign-On (SSO) Integration
---------------------------------

Configure SSO Provider
~~~~~~~~~~~~~~~~~~~~~~

Set up SSO integration with enterprise identity providers.

.. code-block:: http

   POST /api/v1/integration-hub/sso/configure
   Content-Type: application/json

   {
     "organisation_id": "org_12345",
     "provider": "azure_ad",
     "configuration": {
       "tenant_id": "azure_tenant_id_here",
       "client_id": "azure_client_id_here",
       "client_secret": "azure_client_secret_here",
       "redirect_uri": "https://certpathfinder.company.com/auth/callback",
       "scopes": ["openid", "profile", "email", "User.Read"]
     },
     "attribute_mapping": {
       "email": "mail",
       "full_name": "displayName",
       "first_name": "givenName",
       "last_name": "surname",
       "department": "department",
       "job_title": "jobTitle",
       "manager": "manager"
     },
     "provisioning_settings": {
       "auto_create_users": true,
       "auto_update_users": true,
       "default_role": "user",
       "require_group_membership": true,
       "allowed_groups": ["CertPathFinder-Users", "Security-Team"]
     }
   }

**Response**

.. code-block:: json

   {
     "sso_integration_id": "sso_azure_123",
     "organisation_id": "org_12345",
     "provider": "azure_ad",
     "status": "configured",
     "created_at": "2025-01-15T10:30:00Z",
     "configuration_status": {
       "connection_test": "successful",
       "authentication": "verified",
       "attribute_mapping": "validated",
       "group_access": "configured"
     },
     "sso_endpoints": {
       "login_url": "https://certpathfinder.company.com/auth/sso/azure",
       "logout_url": "https://certpathfinder.company.com/auth/logout",
       "metadata_url": "https://certpathfinder.company.com/auth/sso/azure/metadata"
     },
     "security_settings": {
       "encryption_enabled": true,
       "signature_validation": true,
       "session_timeout_minutes": 480,
       "force_authentication": false
     }
   }

Test SSO Configuration
~~~~~~~~~~~~~~~~~~~~~

Validate SSO setup and user authentication flow.

.. code-block:: http

   POST /api/v1/integration-hub/sso/{sso_integration_id}/test
   Content-Type: application/json

   {
     "test_type": "full_flow",
     "test_user": {
       "email": "<EMAIL>",
       "expected_attributes": {
         "department": "Information Security",
         "job_title": "Security Analyst"
       }
     }
   }

**Response**

.. code-block:: json

   {
     "test_id": "sso_test_789",
     "sso_integration_id": "sso_azure_123",
     "test_results": {
       "overall_status": "successful",
       "authentication_flow": "passed",
       "attribute_mapping": "passed",
       "user_provisioning": "passed",
       "group_membership": "passed"
     },
     "test_details": {
       "authentication_time_ms": 1250,
       "attributes_received": {
         "email": "<EMAIL>",
         "full_name": "Test User",
         "department": "Information Security",
         "job_title": "Security Analyst"
       },
       "groups_matched": ["CertPathFinder-Users", "Security-Team"],
       "user_created": true,
       "role_assigned": "user"
     },
     "recommendations": [
       "SSO configuration is working correctly",
       "Consider enabling multi-factor authentication"
     ]
   }

Webhook Management
------------------

Register Webhook
~~~~~~~~~~~~~~~~

Set up webhooks for real-time event notifications.

.. code-block:: http

   POST /api/v1/integration-hub/webhooks
   Content-Type: application/json

   {
     "organisation_id": "org_12345",
     "webhook_url": "https://external-system.company.com/webhooks/certpathfinder",
     "events": [
       "user.certification.completed",
       "user.goal.achieved",
       "user.study_session.completed",
       "organisation.user.created"
     ],
     "authentication": {
       "method": "signature",
       "secret": "webhook_secret_key_here"
     },
     "configuration": {
       "retry_attempts": 3,
       "retry_delay_seconds": 30,
       "timeout_seconds": 10,
       "include_metadata": true
     },
     "filters": {
       "departments": ["Information Security", "IT Operations"],
       "certification_types": ["technical", "management"],
       "minimum_score": 80
     }
   }

**Response**

.. code-block:: json

   {
     "webhook_id": "webhook_456",
     "organisation_id": "org_12345",
     "webhook_url": "https://external-system.company.com/webhooks/certpathfinder",
     "status": "active",
     "created_at": "2025-01-15T10:30:00Z",
     "events_subscribed": [
       "user.certification.completed",
       "user.goal.achieved",
       "user.study_session.completed",
       "organisation.user.created"
     ],
     "security": {
       "signature_method": "HMAC-SHA256",
       "signature_header": "X-CertPathFinder-Signature",
       "timestamp_header": "X-CertPathFinder-Timestamp"
     },
     "delivery_settings": {
       "retry_attempts": 3,
       "retry_delay_seconds": 30,
       "timeout_seconds": 10,
       "next_retry_backoff": "exponential"
     },
     "test_endpoint": "/api/v1/integration-hub/webhooks/webhook_456/test"
   }

Webhook Event Examples
~~~~~~~~~~~~~~~~~~~~~

Example webhook payloads for different events:

**User Certification Completed**

.. code-block:: json

   {
     "event": "user.certification.completed",
     "timestamp": "2025-01-15T10:30:00Z",
     "organisation_id": "org_12345",
     "data": {
       "user": {
         "id": 123,
         "email": "<EMAIL>",
         "full_name": "John Smith",
         "department": "Information Security"
       },
       "certification": {
         "id": 45,
         "name": "CISSP",
         "provider": "ISC2",
         "completion_date": "2025-01-15T10:30:00Z",
         "score": 85,
         "pass_threshold": 70
       },
       "study_statistics": {
         "total_study_hours": 145,
         "study_duration_days": 89,
         "sessions_completed": 23
       }
     },
     "metadata": {
       "webhook_id": "webhook_456",
       "delivery_attempt": 1,
       "signature": "sha256=abc123..."
     }
   }

**User Goal Achieved**

.. code-block:: json

   {
     "event": "user.goal.achieved",
     "timestamp": "2025-01-15T10:30:00Z",
     "organisation_id": "org_12345",
     "data": {
       "user": {
         "id": 123,
         "email": "<EMAIL>",
         "full_name": "John Smith"
       },
       "goal": {
         "id": "goal_789",
         "title": "Achieve CISSP Certification",
         "type": "certification",
         "target_date": "2025-07-15",
         "completed_date": "2025-01-15T10:30:00Z",
         "completion_percentage": 100
       },
       "achievement_details": {
         "days_ahead_of_schedule": 151,
         "final_score": 85,
         "study_efficiency": 0.92
       }
     }
   }

Data Synchronisation
--------------------

Configure Data Sync
~~~~~~~~~~~~~~~~~~~

Set up automated data synchronisation with external systems.

.. code-block:: http

   POST /api/v1/integration-hub/data-sync/configure
   Content-Type: application/json

   {
     "organisation_id": "org_12345",
     "sync_name": "HR System Integration",
     "external_system": {
       "type": "rest_api",
       "base_url": "https://hr-system.company.com/api/v1",
       "authentication": {
         "method": "bearer_token",
         "token": "hr_system_api_token"
       }
     },
     "sync_configuration": {
       "entities": [
         {
           "entity_type": "users",
           "sync_direction": "import",
           "source_endpoint": "/employees",
           "field_mapping": {
             "employee_id": "external_id",
             "email": "work_email",
             "full_name": "display_name",
             "department": "department_name",
             "job_title": "position_title",
             "manager_email": "manager_email",
             "start_date": "hire_date"
           },
           "filters": {
             "active_only": true,
             "departments": ["IT", "Security", "Engineering"]
           }
         }
       ],
       "schedule": {
         "frequency": "daily",
         "time": "02:00",
         "timezone": "Europe/London"
       },
       "error_handling": {
         "on_error": "continue",
         "notification_email": "<EMAIL>",
         "max_failures": 5
       }
     }
   }

**Response**

.. code-block:: json

   {
     "sync_configuration_id": "sync_config_123",
     "organisation_id": "org_12345",
     "sync_name": "HR System Integration",
     "status": "configured",
     "created_at": "2025-01-15T10:30:00Z",
     "validation_results": {
       "connection_test": "successful",
       "authentication": "verified",
       "endpoint_access": "confirmed",
       "field_mapping": "validated"
     },
     "sync_schedule": {
       "next_sync": "2025-01-16T02:00:00Z",
       "frequency": "daily",
       "timezone": "Europe/London"
     },
     "monitoring": {
       "health_check_url": "/api/v1/integration-hub/data-sync/sync_config_123/health",
       "logs_url": "/api/v1/integration-hub/data-sync/sync_config_123/logs",
       "metrics_url": "/api/v1/integration-hub/data-sync/sync_config_123/metrics"
     }
   }

Third-party Connectors
----------------------

Available Connectors
~~~~~~~~~~~~~~~~~~~

Get list of available pre-built integrations.

.. code-block:: http

   GET /api/v1/integration-hub/connectors
   Authorization: Bearer <admin_token>

   ?category=lms&status=available

**Response**

.. code-block:: json

   {
     "connectors": [
       {
         "connector_id": "moodle_connector",
         "name": "Moodle LMS",
         "category": "lms",
         "description": "Full integration with Moodle learning management system",
         "version": "2.1.0",
         "status": "available",
         "capabilities": [
           "user_sync",
           "course_sync",
           "progress_sync",
           "grade_passback"
         ],
         "requirements": {
           "moodle_version": "3.9+",
           "required_permissions": ["webservice/rest:use", "moodle/course:view"],
           "api_access": true
         },
         "configuration_complexity": "medium",
         "setup_time_estimate": "30-60 minutes"
       },
       {
         "connector_id": "azure_ad_connector",
         "name": "Azure Active Directory",
         "category": "identity",
         "description": "Single sign-on and user provisioning with Azure AD",
         "version": "1.5.0",
         "status": "available",
         "capabilities": [
           "sso_authentication",
           "user_provisioning",
           "group_sync",
           "attribute_mapping"
         ]
       }
     ],
     "categories": [
       {
         "category": "lms",
         "name": "Learning Management Systems",
         "connector_count": 8
       },
       {
         "category": "identity",
         "name": "Identity Providers",
         "connector_count": 6
       },
       {
         "category": "hr",
         "name": "HR Systems",
         "connector_count": 4
       }
     ]
   }

Install Connector
~~~~~~~~~~~~~~~~

Install and configure a pre-built connector.

.. code-block:: http

   POST /api/v1/integration-hub/connectors/{connector_id}/install
   Content-Type: application/json

   {
     "organisation_id": "org_12345",
     "configuration": {
       "connection_name": "Company Moodle LMS",
       "base_url": "https://lms.company.com",
       "api_token": "moodle_token_here",
       "sync_settings": {
         "auto_sync": true,
         "sync_frequency": "daily"
       }
     }
   }

Integration Monitoring
----------------------

Get Integration Status
~~~~~~~~~~~~~~~~~~~~~

Monitor the health and status of all integrations.

.. code-block:: http

   GET /api/v1/integration-hub/status
   Authorization: Bearer <admin_token>

   ?organisation_id=org_12345

**Response**

.. code-block:: json

   {
     "organisation_id": "org_12345",
     "generated_at": "2025-01-15T10:30:00Z",
     "overall_status": "healthy",
     "integrations": [
       {
         "integration_id": "lms_integration_789",
         "type": "lms",
         "name": "Moodle LMS",
         "status": "healthy",
         "last_sync": "2025-01-15T02:00:00Z",
         "next_sync": "2025-01-16T02:00:00Z",
         "sync_success_rate": 0.98,
         "last_error": null
       },
       {
         "integration_id": "sso_azure_123",
         "type": "sso",
         "name": "Azure AD SSO",
         "status": "healthy",
         "last_authentication": "2025-01-15T09:45:00Z",
         "authentication_success_rate": 0.995,
         "active_sessions": 45
       }
     ],
     "metrics": {
       "total_integrations": 5,
       "healthy_integrations": 4,
       "warning_integrations": 1,
       "failed_integrations": 0,
       "total_sync_operations_24h": 12,
       "successful_sync_operations_24h": 11
     }
   }

Error Handling
--------------

Integration Hub API error responses:

.. code-block:: json

   {
     "error": "integration_configuration_invalid",
     "message": "LMS integration configuration failed validation",
     "code": 422,
     "details": {
       "validation_errors": [
         "Invalid API token format",
         "Base URL not accessible"
       ],
       "integration_type": "lms",
       "provider": "moodle"
     },
     "suggestions": [
       "Verify API token has correct permissions",
       "Check network connectivity to LMS"
     ]
   }

**Common Error Codes**

- ``400`` - Invalid integration configuration
- ``401`` - Authentication failed with external system
- ``403`` - Insufficient permissions for integration
- ``422`` - Integration validation failed
- ``503`` - External system unavailable

See Also
--------

- :doc:`../enterprise/dashboard` - Enterprise integration management
- :doc:`authentication` - API authentication
- :doc:`../guides/admin_guide` - Integration setup guide
- :doc:`../enterprise/compliance` - Integration compliance requirements
