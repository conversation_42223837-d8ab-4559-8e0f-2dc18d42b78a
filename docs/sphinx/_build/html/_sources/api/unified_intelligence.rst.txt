🤖 Unified Intelligence API
============================

**Complete API Reference for Unified Platform**

The Unified Intelligence API provides seamless access to all platform components through standardized endpoints. This API combines insights from AI Study Assistant, Career Intelligence, Enterprise Analytics, and Marketplace Hub into a cohesive, intelligent system.

🔗 **Base URL**
---------------

All unified intelligence endpoints are available under:

.. code-block:: text

   https://your-domain/api/v1/

🔐 **Authentication**
--------------------

The API uses Traefik-based authentication with header forwarding:

.. code-block:: http

   X-Forwarded-User: your-user-id
   X-Remote-User: your-user-id
   X-User: your-user-id

Example request:

.. code-block:: bash

   curl -H "X-Forwarded-User: user123" \
        https://your-domain/api/v1/dashboard/

📊 **Unified Dashboard Endpoints**
----------------------------------

Complete Unified Dashboard
~~~~~~~~~~~~~~~~~~~~~~~~~~

Get comprehensive dashboard combining all platform components.

.. http:get:: /api/v1/dashboard/

   **Query Parameters:**
   
   * ``include_market_intelligence`` (boolean, optional) - Include market intelligence data (default: true)
   * ``include_enterprise_data`` (boolean, optional) - Include enterprise analytics if available (default: true)

   **Response:**

   .. code-block:: json

      {
        "user_id": "user123",
        "dashboard_date": "2024-01-16T10:30:00Z",
        "user_summary": {
          "name": "John Doe",
          "subscription_tier": "premium",
          "current_role": "Security Analyst",
          "target_certifications": ["CISSP", "Security+"]
        },
        "key_metrics": {
          "overall_progress": 0.75,
          "study_efficiency": 0.82,
          "career_advancement": 0.68,
          "platform_engagement": 0.85,
          "goal_completion_rate": 0.72,
          "study_streak_days": 14,
          "total_study_hours": 45.5,
          "certifications_completed": 2,
          "certifications_in_progress": 1
        },
        "insights": [
          {
            "id": "study_efficiency_high",
            "title": "Excellent Study Efficiency",
            "description": "Your study sessions are highly effective...",
            "category": "study",
            "priority": 4,
            "confidence": 0.9,
            "actionable_steps": ["Continue current approach"],
            "impact": "high",
            "time_to_complete": "ongoing",
            "source": "ai_study_assistant"
          }
        ],
        "recommendations": [
          {
            "id": "focus_target_cert",
            "title": "Focus on CISSP Certification",
            "description": "Prioritize your CISSP certification...",
            "type": "certification",
            "priority": 5,
            "confidence": 0.85,
            "estimated_roi": 15000.0,
            "time_investment": "3-6 months",
            "cost_estimate": 2000.0,
            "actionable_steps": ["Create study plan", "Schedule practice tests"],
            "sources": ["ai_study_assistant", "career_intelligence"]
          }
        ],
        "quick_actions": [
          "Continue your study session",
          "Take a practice test",
          "Review your progress"
        ],
        "upcoming_milestones": [
          {
            "title": "Complete CISSP certification",
            "target_date": "2024-06-30",
            "progress": 0.3,
            "category": "certification"
          }
        ],
        "recent_achievements": [
          {
            "title": "High Study Efficiency Achieved",
            "description": "Maintained >70% study efficiency",
            "date": "2024-01-15",
            "category": "study"
          }
        ],
        "motivational_message": "Outstanding progress! You're on track to exceed your goals."
      }

Complete User Profile
~~~~~~~~~~~~~~~~~~~~~

Get complete user profile aggregated from all platform components.

.. http:get:: /api/v1/dashboard/profile

   **Response:**

   .. code-block:: json

      {
        "user_id": "user123",
        "email": "<EMAIL>",
        "name": "John Doe",
        "subscription_tier": "premium",
        "is_active": true,
        "created_at": "2023-01-01T00:00:00Z",
        "last_updated": "2024-01-16T10:30:00Z",
        "study_profile": {
          "learning_style": "mixed",
          "study_hours_per_week": 10,
          "preferred_study_times": ["evening"],
          "current_certifications": ["Security+"],
          "target_certifications": ["CISSP"],
          "study_efficiency_score": 0.82,
          "consistency_score": 0.75,
          "last_study_session": "2024-01-15T20:00:00Z"
        },
        "career_profile": {
          "current_role": "Security Analyst",
          "target_role": "Security Engineer",
          "experience_years": 5,
          "location": "remote",
          "industry": "cybersecurity",
          "skills": ["network_security", "incident_response"],
          "career_goals": ["cissp_certification", "senior_role"]
        },
        "marketplace_profile": {
          "preferred_vendors": ["CompTIA", "ISC2"],
          "voucher_balance": 0.0,
          "purchase_history": [],
          "wishlist": ["CISSP", "Security+"]
        },
        "platform_metrics": {
          "engagement_score": 0.85,
          "progress_score": 0.75,
          "recommendation_preferences": {
            "include_market_trends": true,
            "focus_areas": ["cybersecurity", "career_growth"]
          }
        }
      }

Unified Performance Metrics
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Get key performance metrics and indicators.

.. http:get:: /api/v1/dashboard/metrics

   **Response:**

   .. code-block:: json

      {
        "user_id": "user123",
        "metrics_date": "2024-01-16T10:30:00Z",
        "overall_progress_score": 0.75,
        "study_efficiency_score": 0.82,
        "career_advancement_score": 0.68,
        "platform_engagement_score": 0.85,
        "goal_completion_rate": 0.72,
        "study_streak_days": 14,
        "total_study_hours": 45.5,
        "certifications_completed": 2,
        "certifications_in_progress": 1,
        "performance_indicators": {
          "study_consistency": "high",
          "learning_efficiency": "high",
          "goal_progress": "on_track",
          "platform_usage": "active"
        }
      }

🤖 **Unified Intelligence Endpoints**
-------------------------------------

Comprehensive Planning
~~~~~~~~~~~~~~~~~~~~~~

Create comprehensive certification and career plan using all platform components.

.. http:post:: /api/v1/unified-intelligence/comprehensive-plan

   **Request Body:**

   .. code-block:: json

      {
        "target_certification_id": 1,
        "current_role_id": 1,
        "target_role_id": 2,
        "max_budget": 5000,
        "max_timeline_months": 12,
        "learning_style": "mixed",
        "study_hours_per_week": 10,
        "experience_years": 5,
        "location": "remote"
      }

   **Response:**

   .. code-block:: json

      {
        "user_id": "user123",
        "plan_id": "plan_abc123",
        "created_at": "2024-01-16T10:30:00Z",
        "study_recommendations": [
          {
            "type": "study_focus",
            "priority": 5,
            "title": "Focus on Network Security",
            "confidence_score": 0.85,
            "estimated_time_minutes": 120,
            "actionable_steps": ["Review TCP/IP fundamentals"]
          }
        ],
        "career_transition_paths": [
          {
            "path_id": "path_123",
            "source_role": "Security Analyst",
            "target_role": "Security Engineer",
            "estimated_duration_months": 8,
            "success_probability": 0.82,
            "required_certifications": ["CISSP"],
            "steps": [
              {
                "step_number": 1,
                "action": "Obtain CISSP certification",
                "estimated_duration_months": 6,
                "cost_estimate": 2000
              }
            ]
          }
        ],
        "roi_analysis": {
          "total_investment": 5000,
          "expected_salary_increase": 18000,
          "payback_period_months": 4,
          "five_year_roi": 300.0,
          "confidence_score": 0.85
        },
        "success_probability": 0.82,
        "estimated_timeline_months": 8,
        "total_cost_estimate": 5000
      }

Enterprise Training Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Analyze enterprise training needs and budget optimization.

.. http:post:: /api/v1/unified-intelligence/enterprise-analysis

   **Request Body:**

   .. code-block:: json

      {
        "enterprise_id": "enterprise_123",
        "total_budget": 100000,
        "budget_period_months": 12,
        "strategic_priorities": ["cybersecurity", "cloud_security"],
        "team_constraints": {
          "max_teams": 5,
          "priority_teams": ["security_team", "cloud_team"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "enterprise_id": "enterprise_123",
        "analysis_id": "analysis_456",
        "total_budget": 100000,
        "optimized_allocation": {
          "Security Team": 40000,
          "Cloud Team": 35000,
          "Compliance Team": 25000
        },
        "cost_savings": 15000,
        "roi_projections": {
          "one_year_roi": 22.0,
          "three_year_roi": 55.0,
          "five_year_roi": 110.0
        },
        "recommended_certifications": [
          {
            "certification_name": "CISSP",
            "total_recommended_members": 10,
            "average_roi": 18.0,
            "total_investment": 20000
          }
        ],
        "implementation_timeline": {
          "immediate_actions": [
            {
              "team": "Security Team",
              "action": "Begin CISSP training program",
              "budget": 15000
            }
          ]
        }
      }

💰 **Salary Intelligence Endpoints**
------------------------------------

Role Salary Analysis
~~~~~~~~~~~~~~~~~~~~

Comprehensive salary analysis for specific roles.

.. http:get:: /api/v1/salary/roles/(int:role_id)/analysis

   **Query Parameters:**
   
   * ``location`` (string, optional) - Location for salary analysis (default: "remote")
   * ``experience_years`` (integer, optional) - Years of experience (default: 5)

   **Response:**

   .. code-block:: json

      {
        "role_id": 1,
        "role_name": "Security Engineer",
        "current_salary_range": {
          "min": 120000,
          "max": 180000,
          "median": 150000
        },
        "market_salary_range": {
          "min": 125000,
          "max": 185000,
          "median": 155000
        },
        "certification_impact": {
          "CISSP": {
            "salary_premium_percentage": 15.0,
            "annual_increase": 22500
          }
        },
        "location_adjustments": {
          "San Francisco": {"multiplier": 1.8},
          "New York": {"multiplier": 1.6}
        },
        "growth_projections": {
          "five_year": 195000,
          "ten_year": 245000
        },
        "analysis_date": "2024-01-16T10:30:00Z"
      }

ROI Analysis
~~~~~~~~~~~~

Calculate ROI for certification investments.

.. http:post:: /api/v1/salary/roi-analysis

   **Request Body:**

   .. code-block:: json

      {
        "certification_id": 1,
        "current_role_id": 1,
        "target_role_id": 2,
        "investment_cost": 3000.0,
        "location": "remote",
        "experience_years": 5
      }

   **Response:**

   .. code-block:: json

      {
        "certification_id": 1,
        "investment_cost": 3000.0,
        "expected_salary_increase": 18000.0,
        "payback_period_months": 2,
        "five_year_roi": 300.0,
        "ten_year_roi": 600.0,
        "confidence_score": 0.85,
        "risk_factors": ["Market volatility"],
        "analysis_date": "2024-01-16T10:30:00Z"
      }

🏥 **Health Check Endpoints**
-----------------------------

Service Health Checks
~~~~~~~~~~~~~~~~~~~~~

Monitor the health of all unified platform services.

.. http:get:: /api/v1/dashboard/health

   **Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "service": "Unified Dashboard",
        "version": "1.0.0",
        "components": {
          "user_profile_service": "Active",
          "dashboard_service": "Active",
          "cross_component_integration": "Active"
        },
        "features": [
          "unified_dashboard",
          "complete_user_profile",
          "cross_component_metrics"
        ],
        "timestamp": "2024-01-16T10:30:00Z"
      }

.. http:get:: /api/v1/unified-intelligence/health

   **Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "service": "Unified Intelligence Platform",
        "version": "1.0.0",
        "components": {
          "ai_study_assistant": "AI Study Assistant - Active",
          "career_intelligence": "Career & Cost Intelligence - Active",
          "salary_intelligence": "Salary Intelligence - Active",
          "budget_optimizer": "Budget Optimizer - Active"
        },
        "timestamp": "2024-01-16T10:30:00Z"
      }

⚠️ **Error Handling**
---------------------

All endpoints use standardized error responses:

.. code-block:: json

   {
     "error_id": "550e8400-e29b-41d4-a716-446655440000",
     "error_code": "UNIFIED_DASHBOARD_SERVICE_ERROR",
     "message": "Error generating dashboard",
     "timestamp": "2024-01-16T10:30:00Z",
     "type": "application_error"
   }

Common HTTP status codes:

* ``200`` - Success
* ``400`` - Bad Request (invalid parameters)
* ``401`` - Unauthorized (missing or invalid authentication)
* ``404`` - Not Found (resource doesn't exist)
* ``422`` - Unprocessable Entity (validation error)
* ``500`` - Internal Server Error (server-side error)

📚 **Additional Resources**
--------------------------

* :doc:`../unified_platform` - Unified platform overview
* :doc:`../guides/unified_dashboard_guide` - Dashboard integration guide
* :doc:`../development/integration_patterns` - Development patterns
* :doc:`authentication` - Authentication details
