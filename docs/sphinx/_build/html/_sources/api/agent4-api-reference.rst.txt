Agent 4 API Reference
=====================

.. meta::
   :description: Complete API reference for Agent 4 Career & Cost Intelligence
   :keywords: API, Agent 4, career pathfinding, ROI analysis, budget optimization

Overview
--------

Agent 4 Career & Cost Intelligence provides a comprehensive REST API for career pathfinding, cost analysis, ROI calculations, budget optimization, and market intelligence. All endpoints are production-ready with enterprise-grade performance and security.

**Base URL**: ``https://api.certpathfinder.com/api/v1``

**Authentication**: Bearer token required for all endpoints

**Rate Limiting**: 10,000 requests per minute per API key

Career Pathfinding API
----------------------

Find optimal career transition paths using advanced A* algorithms.

POST /career-transition/pathfinding
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Generate optimal career transition paths with multi-constraint optimization.

**Request Body:**

.. code-block:: json

   {
     "current_role_id": 1,
     "target_role_id": 3,
     "max_budget": 5000,
     "max_timeline_months": 18,
     "max_difficulty": "Medium",
     "learning_style": "mixed",
     "study_hours_per_week": 15,
     "currency": "USD"
   }

**Response:**

.. code-block:: json

   {
     "path_options": [
       {
         "path_id": "path_001",
         "total_cost": 4500,
         "estimated_duration_months": 16,
         "success_probability": 0.85,
         "certifications": ["CISSP", "CISM"],
         "cost_breakdown": {
           "exam_fees": 1400,
           "study_materials": 800,
           "training": 2300
         },
         "timeline": {
           "preparation": 2,
           "study_phase": 12,
           "certification": 2
         }
       }
     ],
     "constraints_analysis": {
       "budget_utilization": 0.90,
       "timeline_efficiency": 0.89
     },
     "recommendations": [
       "Consider CISSP first for maximum ROI",
       "Schedule exams during low-demand periods"
     ]
   }

**Performance**: < 3 seconds response time

GET /career-transition/roles
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Retrieve available career roles and transition relationships.

**Response:**

.. code-block:: json

   {
     "roles": [
       {
         "id": 1,
         "name": "Security Analyst",
         "level": "Entry",
         "category": "Analysis",
         "average_salary": 75000,
         "growth_rate": 0.15
       }
     ],
     "transitions": [
       {
         "from_role_id": 1,
         "to_role_id": 3,
         "difficulty_score": 0.7,
         "typical_duration_months": 18,
         "success_rate": 0.82
       }
     ]
   }

Cost Calculator API
-------------------

Comprehensive cost calculation with hidden cost analysis.

POST /cost-calculator/calculate
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Calculate total cost of ownership for certification paths.

**Request Body:**

.. code-block:: json

   {
     "certification_ids": [1, 2],
     "location": "San Francisco, CA",
     "currency": "USD",
     "include_hidden_costs": true,
     "study_materials": true,
     "practice_exams": true,
     "bootcamp_training": false
   }

**Response:**

.. code-block:: json

   {
     "total_cost": 4750,
     "currency": "USD",
     "cost_breakdown": {
       "exam_fees": 1400,
       "study_materials": 800,
       "practice_exams": 300,
       "travel_costs": 250,
       "time_opportunity_cost": 2000
     },
     "location_adjustments": {
       "cost_of_living_multiplier": 1.25,
       "regional_pricing": 150
     },
     "hidden_costs": {
       "renewal_fees": 200,
       "continuing_education": 300,
       "membership_fees": 150
     }
   }

**Performance**: < 2 seconds response time

Salary Intelligence API
-----------------------

Advanced salary analysis and ROI calculations.

POST /salary-intelligence/roi-analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Comprehensive ROI analysis with risk assessment.

**Request Body:**

.. code-block:: json

   {
     "certification_id": 1,
     "current_role_id": 1,
     "target_role_id": 3,
     "investment_cost": 3000,
     "location": "remote",
     "experience_years": 5,
     "current_salary": 75000
   }

**Response:**

.. code-block:: json

   {
     "investment_cost": 3000,
     "expected_salary_increase": 15000,
     "payback_period_months": 14,
     "five_year_roi": 285,
     "ten_year_roi": 450,
     "confidence_score": 0.88,
     "risk_factors": [
       "Market saturation in target role",
       "Technology evolution impact"
     ],
     "market_conditions": {
       "demand_level": "high",
       "competition_level": "medium",
       "growth_projection": 0.12
     },
     "sensitivity_analysis": {
       "best_case_roi": 320,
       "worst_case_roi": 180,
       "probability_distribution": {
         "p90": 310,
         "p50": 285,
         "p10": 195
       }
     }
   }

**Performance**: < 2 seconds response time

Budget Optimization API
------------------------

Enterprise budget allocation and optimization.

POST /budget-optimization/optimize
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Optimize enterprise training budget allocation.

**Request Body:**

.. code-block:: json

   {
     "enterprise_id": 1,
     "total_budget": 100000,
     "strategic_priorities": ["cybersecurity", "cloud_security"],
     "timeline_months": 12,
     "team_constraints": {
       "security_team_size": 25,
       "experience_levels": {
         "junior": 10,
         "mid": 10,
         "senior": 5
       }
     }
   }

**Response:**

.. code-block:: json

   {
     "enterprise_id": 1,
     "total_budget": 100000,
     "optimized_allocation": {
       "foundational_training": 35000,
       "advanced_certifications": 40000,
       "specialized_training": 20000,
       "contingency": 5000
     },
     "projected_roi": 245,
     "cost_savings": 15000,
     "efficiency_score": 92,
     "recommendations": [
       "Prioritize CISSP training for senior staff",
       "Implement group training for cost efficiency"
     ],
     "risk_assessment": {
       "budget_overrun_risk": "low",
       "timeline_risk": "medium",
       "skill_gap_risk": "low"
     },
     "implementation_timeline": {
       "phase_1": "Months 1-3: Foundational training",
       "phase_2": "Months 4-8: Advanced certifications",
       "phase_3": "Months 9-12: Specialized training"
     }
   }

**Performance**: < 5 seconds response time

Market Intelligence API
------------------------

Real-time market trends and competitive intelligence.

POST /market-intelligence/analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Comprehensive market trend analysis.

**Request Body:**

.. code-block:: json

   {
     "region": "north_america",
     "industry": "technology",
     "timeframe": "12months",
     "include_projections": true
   }

**Response:**

.. code-block:: json

   {
     "trends": [
       {
         "certification": "CISSP",
         "demand_change": 15,
         "salary_trend": 8,
         "job_openings": 2500,
         "competition_level": "medium",
         "growth_projection": 12
       }
     ],
     "locations": [
       {
         "location": "San Francisco, CA",
         "average_salary": 125000,
         "job_count": 450,
         "cost_of_living": 185,
         "demand_level": "high"
       }
     ],
     "industries": [
       {
         "industry": "technology",
         "top_certifications": ["CISSP", "CISM", "CEH"],
         "average_budget": 8500,
         "growth_rate": 18,
         "key_trends": [
           "Increased focus on cloud security",
           "Growing demand for DevSecOps skills"
         ]
       }
     ]
   }

**Performance**: < 1 second response time

Error Handling
--------------

All APIs use standard HTTP status codes and return detailed error information.

**Error Response Format:**

.. code-block:: json

   {
     "error": {
       "code": "INVALID_REQUEST",
       "message": "Budget amount must be greater than 1000",
       "details": {
         "field": "total_budget",
         "provided_value": 500,
         "minimum_value": 1000
       },
       "request_id": "req_123456789"
     }
   }

**Common Status Codes:**

- ``200 OK``: Request successful
- ``400 Bad Request``: Invalid request parameters
- ``401 Unauthorized``: Invalid or missing authentication
- ``404 Not Found``: Resource not found
- ``429 Too Many Requests``: Rate limit exceeded
- ``500 Internal Server Error``: Server error

Rate Limiting
-------------

**Limits:**
- 10,000 requests per minute per API key
- 100,000 requests per day per API key
- Burst limit: 100 requests per second

**Headers:**
- ``X-RateLimit-Limit``: Request limit per minute
- ``X-RateLimit-Remaining``: Remaining requests in current window
- ``X-RateLimit-Reset``: Time when rate limit resets

Authentication
--------------

All API endpoints require authentication using Bearer tokens.

**Header Format:**

.. code-block:: http

   Authorization: Bearer your_api_token_here

**Token Management:**
- Tokens expire after 24 hours
- Refresh tokens available for long-term access
- Enterprise customers receive dedicated API keys

SDKs and Libraries
------------------

**Official SDKs:**
- Python SDK: ``pip install certpathfinder-sdk``
- JavaScript SDK: ``npm install @certpathfinder/sdk``
- Java SDK: Available on Maven Central

**Community Libraries:**
- Go client library
- Ruby gem
- PHP composer package

Support
-------

**Technical Support:**
- Email: <EMAIL>
- Documentation: https://docs.certpathfinder.com
- Status Page: https://status.certpathfinder.com

**SLA:**
- 99.9% uptime guarantee
- < 2 second average response time
- 24/7 monitoring and support
