Progress Tracking API
=====================

The Progress Tracking API provides comprehensive learning analytics, study session management, and achievement tracking. It enables detailed monitoring of learning progress with gamification elements and predictive insights.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Progress Tracking features include:

- **Study Session Management** - Detailed tracking of all study activities
- **Learning Analytics** - Comprehensive progress analysis and insights
- **Achievement System** - Gamified milestones and badges
- **Performance Metrics** - Learning velocity and effectiveness tracking
- **Predictive Analytics** - AI-powered exam readiness predictions
- **Goal Management** - Personal learning objectives and targets

All tracking occurs locally with complete privacy protection.

Study Session Management
------------------------

Start Study Session
~~~~~~~~~~~~~~~~~~~

Begin a new study session with intelligent recommendations.

.. code-block:: http

   POST /api/v1/progress-tracking/sessions/start
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "user_id": 123,
     "certification_id": 45,
     "planned_duration_minutes": 60,
     "study_type": "practice_questions",
     "topics": ["Network Security", "Cryptography"],
     "difficulty_level": "intermediate",
     "study_method": "active_recall",
     "environment": "home_office",
     "energy_level": "high"
   }

**Response**

.. code-block:: json

   {
     "session_id": "session_12345",
     "user_id": 123,
     "certification_id": 45,
     "certification_name": "CISSP",
     "started_at": "2025-01-15T10:30:00Z",
     "planned_duration_minutes": 60,
     "study_type": "practice_questions",
     "ai_recommendations": {
       "focus_areas": [
         "Access Control Models",
         "Cryptographic Protocols"
       ],
       "suggested_techniques": [
         "Spaced repetition for weak areas",
         "Practice questions with explanations"
       ],
       "optimal_break_schedule": [
         {
           "break_after_minutes": 25,
           "break_duration_minutes": 5,
           "activity": "brief_walk"
         }
       ],
       "difficulty_adjustment": "maintain_current_level"
     },
     "session_context": {
       "previous_session_performance": 0.78,
       "current_knowledge_level": 0.65,
       "recommended_focus_time": 45,
       "predicted_effectiveness": 0.82
     },
     "study_materials": [
       {
         "type": "practice_questions",
         "source": "Official CISSP Practice Tests",
         "difficulty": "intermediate",
         "estimated_questions": 30
       }
     ]
   }

Complete Study Session
~~~~~~~~~~~~~~~~~~~~~~

End a study session and record progress.

.. code-block:: http

   POST /api/v1/progress-tracking/sessions/complete
   Content-Type: application/json

   {
     "session_id": "session_12345",
     "actual_duration_minutes": 55,
     "topics_covered": [
       {
         "topic": "Access Control Models",
         "time_spent_minutes": 25,
         "confidence_level": 7,
         "difficulty_experienced": "moderate"
       },
       {
         "topic": "Cryptographic Protocols",
         "time_spent_minutes": 30,
         "confidence_level": 6,
         "difficulty_experienced": "challenging"
       }
     ],
     "overall_session_rating": 8,
     "notes": "Good progress on access control concepts. Need more practice with cryptographic implementations.",
     "interruptions": 2,
     "energy_level_end": "medium",
     "practice_results": {
       "questions_attempted": 28,
       "questions_correct": 21,
       "accuracy_percentage": 75,
       "time_per_question_seconds": 90
     }
   }

**Response**

.. code-block:: json

   {
     "session_id": "session_12345",
     "completed_at": "2025-01-15T11:25:00Z",
     "session_summary": {
       "total_duration_minutes": 55,
       "effective_study_time_minutes": 50,
       "topics_covered": 2,
       "overall_effectiveness": 0.78,
       "knowledge_gain_estimated": 0.12
     },
     "progress_update": {
       "certification_progress_percentage": 67.5,
       "topic_progress": {
         "Access Control Models": 0.75,
         "Cryptographic Protocols": 0.58
       },
       "overall_knowledge_level": 0.68,
       "progress_since_last_session": 0.03
     },
     "achievements_unlocked": [
       {
         "achievement_id": "consistent_learner_7_days",
         "name": "Week Warrior",
         "description": "Completed study sessions for 7 consecutive days",
         "points_awarded": 100,
         "badge_level": "bronze"
       }
     ],
     "ai_insights": {
       "performance_analysis": "Strong improvement in access control understanding",
       "areas_for_focus": [
         "Cryptographic implementations need more practice",
         "Consider reviewing symmetric vs asymmetric encryption"
       ],
       "next_session_recommendations": [
         "Focus on cryptography hands-on exercises",
         "Review previous session notes before starting"
       ],
       "study_pattern_insights": "Most effective during morning sessions",
       "predicted_exam_readiness": 0.72
     },
     "statistics_updated": {
       "total_study_hours": 145.5,
       "sessions_completed": 23,
       "average_session_duration": 63,
       "current_study_streak": 7
     }
   }

Learning Analytics
------------------

Get Progress Overview
~~~~~~~~~~~~~~~~~~~~

Retrieve comprehensive progress analytics for a user.

.. code-block:: http

   GET /api/v1/progress-tracking/analytics/overview
   Authorization: Bearer <token>

   ?period=30d&certification_id=45&include_predictions=true

**Response**

.. code-block:: json

   {
     "user_id": 123,
     "period": "30d",
     "certification_id": 45,
     "certification_name": "CISSP",
     "generated_at": "2025-01-15T10:30:00Z",
     "progress_summary": {
       "overall_progress_percentage": 67.5,
       "knowledge_level": 0.68,
       "study_hours_completed": 145.5,
       "study_hours_planned": 200,
       "sessions_completed": 23,
       "days_studied": 18,
       "current_streak": 7,
       "longest_streak": 12
     },
     "learning_velocity": {
       "average_hours_per_week": 12.1,
       "progress_rate_per_hour": 0.0047,
       "estimated_completion_date": "2025-04-15",
       "pace_compared_to_plan": "ahead",
       "velocity_trend": "increasing"
     },
     "topic_breakdown": [
       {
         "topic": "Security and Risk Management",
         "progress_percentage": 85,
         "confidence_level": 8.2,
         "time_invested_hours": 25,
         "last_studied": "2025-01-14T15:30:00Z",
         "mastery_level": "proficient"
       },
       {
         "topic": "Asset Security",
         "progress_percentage": 72,
         "confidence_level": 7.1,
         "time_invested_hours": 18,
         "last_studied": "2025-01-15T10:30:00Z",
         "mastery_level": "developing"
       }
     ],
     "performance_metrics": {
       "average_session_effectiveness": 0.78,
       "knowledge_retention_rate": 0.85,
       "practice_test_average": 76.5,
       "improvement_rate": 0.12,
       "consistency_score": 8.7
     },
     "study_patterns": {
       "most_productive_time": "09:00-11:00",
       "optimal_session_length": 55,
       "preferred_study_methods": ["practice_questions", "flashcards"],
       "best_performing_topics": ["Risk Management", "Governance"],
       "challenging_topics": ["Cryptography", "Software Development Security"]
     },
     "predictions": {
       "exam_readiness_score": 0.72,
       "predicted_exam_score": 78,
       "confidence_interval": [72, 84],
       "recommended_exam_date": "2025-05-01",
       "success_probability": 0.82,
       "areas_needing_focus": [
         "Cryptography implementation",
         "Software security testing"
       ]
     }
   }

Detailed Session History
~~~~~~~~~~~~~~~~~~~~~~~~

Get comprehensive study session history with analytics.

.. code-block:: http

   GET /api/v1/progress-tracking/sessions/history
   Authorization: Bearer <token>

   ?start_date=2025-01-01&end_date=2025-01-15&include_analytics=true

**Response**

.. code-block:: json

   {
     "user_id": 123,
     "period": {
       "start_date": "2025-01-01T00:00:00Z",
       "end_date": "2025-01-15T23:59:59Z"
     },
     "total_sessions": 15,
     "total_study_time_minutes": 825,
     "sessions": [
       {
         "session_id": "session_12345",
         "date": "2025-01-15T10:30:00Z",
         "duration_minutes": 55,
         "certification": "CISSP",
         "topics": ["Access Control", "Cryptography"],
         "study_type": "practice_questions",
         "effectiveness_score": 0.78,
         "knowledge_gain": 0.12,
         "session_rating": 8,
         "achievements": ["Week Warrior"]
       }
     ],
     "analytics": {
       "average_session_duration": 55,
       "most_studied_topics": [
         {
           "topic": "Security Management",
           "sessions": 8,
           "total_time_minutes": 420
         }
       ],
       "study_consistency": {
         "days_with_study": 12,
         "longest_streak": 7,
         "average_daily_study_minutes": 68.75
       },
       "performance_trends": {
         "effectiveness_trend": "improving",
         "knowledge_retention": 0.85,
         "session_rating_average": 7.8
       }
     }
   }

Achievement System
------------------

Get User Achievements
~~~~~~~~~~~~~~~~~~~~

Retrieve all achievements and progress towards goals.

.. code-block:: http

   GET /api/v1/progress-tracking/achievements
   Authorization: Bearer <token>

   ?include_progress=true&category=all

**Response**

.. code-block:: json

   {
     "user_id": 123,
     "generated_at": "2025-01-15T10:30:00Z",
     "achievement_summary": {
       "total_achievements": 15,
       "total_points": 2350,
       "current_level": "Advanced Learner",
       "next_level": "Expert Scholar",
       "points_to_next_level": 650,
       "badges_earned": 8
     },
     "achievements": [
       {
         "achievement_id": "first_session",
         "name": "Getting Started",
         "description": "Complete your first study session",
         "category": "milestone",
         "points": 50,
         "badge_level": "bronze",
         "earned_at": "2024-12-01T10:00:00Z",
         "rarity": "common"
       },
       {
         "achievement_id": "consistent_learner_30_days",
         "name": "Monthly Master",
         "description": "Study consistently for 30 days",
         "category": "consistency",
         "points": 500,
         "badge_level": "gold",
         "earned_at": "2025-01-10T15:30:00Z",
         "rarity": "rare"
       }
     ],
     "in_progress": [
       {
         "achievement_id": "knowledge_expert",
         "name": "Knowledge Expert",
         "description": "Achieve 90% knowledge level in any certification",
         "category": "mastery",
         "points": 1000,
         "badge_level": "platinum",
         "current_progress": 0.75,
         "requirements": {
           "target_knowledge_level": 0.9,
           "current_knowledge_level": 0.68
         },
         "estimated_completion": "2025-03-15"
       }
     ],
     "available_achievements": [
       {
         "achievement_id": "speed_learner",
         "name": "Speed Learner",
         "description": "Complete a certification in under 3 months",
         "category": "efficiency",
         "points": 750,
         "requirements": "Complete any certification within 90 days",
         "difficulty": "challenging"
       }
     ],
     "leaderboard_position": {
       "overall_rank": 23,
       "monthly_rank": 8,
       "category_ranks": {
         "study_hours": 15,
         "consistency": 12,
         "achievements": 23
       }
     }
   }

Goal Management
---------------

Create Learning Goal
~~~~~~~~~~~~~~~~~~~

Set up a new learning objective with tracking.

.. code-block:: http

   POST /api/v1/progress-tracking/goals
   Content-Type: application/json

   {
     "goal_type": "certification",
     "title": "Achieve CISSP Certification",
     "description": "Complete CISSP certification within 6 months with 85% exam score target",
     "certification_id": 45,
     "target_date": "2025-07-15",
     "target_metrics": {
       "exam_score_target": 85,
       "study_hours_target": 200,
       "knowledge_level_target": 0.9
     },
     "study_schedule": {
       "hours_per_week": 12,
       "preferred_days": ["monday", "wednesday", "friday", "sunday"],
       "preferred_times": ["09:00-11:00", "19:00-21:00"]
     },
     "milestones": [
       {
         "title": "Complete Domain 1-4",
         "target_date": "2025-04-01",
         "description": "Master first four CISSP domains"
       },
       {
         "title": "Practice Test Readiness",
         "target_date": "2025-06-01",
         "description": "Consistently score 80%+ on practice tests"
       }
     ]
   }

**Response**

.. code-block:: json

   {
     "goal_id": "goal_789",
     "user_id": 123,
     "created_at": "2025-01-15T10:30:00Z",
     "goal_details": {
       "title": "Achieve CISSP Certification",
       "status": "active",
       "progress_percentage": 0,
       "target_date": "2025-07-15",
       "days_remaining": 181,
       "estimated_completion": "2025-07-10"
     },
     "tracking_setup": {
       "automatic_progress_tracking": true,
       "milestone_notifications": true,
       "weekly_progress_reports": true,
       "ai_recommendations": true
     },
     "initial_plan": {
       "total_study_hours_required": 200,
       "weekly_study_hours": 12,
       "estimated_weeks_to_completion": 17,
       "success_probability": 0.78
     },
     "milestones": [
       {
         "milestone_id": "milestone_1",
         "title": "Complete Domain 1-4",
         "target_date": "2025-04-01",
         "progress": 0,
         "status": "pending"
       }
     ],
     "ai_insights": {
       "goal_feasibility": "realistic",
       "recommended_adjustments": [
         "Consider adding buffer time for difficult topics",
         "Schedule regular practice test sessions"
       ],
       "success_factors": [
         "Consistent study schedule",
         "Regular progress reviews",
         "Active practice testing"
       ]
     }
   }

Update Goal Progress
~~~~~~~~~~~~~~~~~~~

Manually update progress towards learning goals.

.. code-block:: http

   PATCH /api/v1/progress-tracking/goals/{goal_id}
   Content-Type: application/json

   {
     "progress_update": {
       "current_progress_percentage": 45,
       "study_hours_completed": 90,
       "knowledge_level_current": 0.65
     },
     "milestone_updates": [
       {
         "milestone_id": "milestone_1",
         "status": "completed",
         "completed_at": "2025-03-28T14:00:00Z",
         "notes": "Completed all four domains ahead of schedule"
       }
     ],
     "notes": "Making excellent progress. Cryptography domain was challenging but now confident.",
     "schedule_adjustments": {
       "increase_weekly_hours": 2,
       "add_practice_test_sessions": true
     }
   }

Performance Analytics
---------------------

Learning Effectiveness Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Analyse learning patterns and effectiveness.

.. code-block:: http

   GET /api/v1/progress-tracking/analytics/effectiveness
   Authorization: Bearer <token>

   ?period=90d&include_recommendations=true

**Response**

.. code-block:: json

   {
     "user_id": 123,
     "analysis_period": "90d",
     "generated_at": "2025-01-15T10:30:00Z",
     "effectiveness_metrics": {
       "overall_effectiveness_score": 0.78,
       "knowledge_retention_rate": 0.85,
       "study_efficiency": 0.72,
       "consistency_score": 8.7,
       "improvement_rate": 0.12
     },
     "learning_patterns": {
       "optimal_study_times": [
         {
           "time_range": "09:00-11:00",
           "effectiveness_score": 0.89,
           "frequency": 0.65
         }
       ],
       "best_session_duration": {
         "optimal_minutes": 55,
         "effectiveness_at_optimal": 0.82,
         "diminishing_returns_after": 75
       },
       "most_effective_methods": [
         {
           "method": "practice_questions",
           "effectiveness": 0.85,
           "knowledge_retention": 0.88
         },
         {
           "method": "active_recall",
           "effectiveness": 0.82,
           "knowledge_retention": 0.91
         }
       ]
     },
     "performance_trends": {
       "effectiveness_trend": "improving",
       "monthly_improvement": 0.08,
       "consistency_trend": "stable",
       "knowledge_growth_rate": 0.15
     },
     "recommendations": {
       "schedule_optimisation": [
         "Focus study sessions between 9-11 AM for maximum effectiveness",
         "Maintain 55-minute session length for optimal retention"
       ],
       "method_improvements": [
         "Increase practice question sessions to 60% of study time",
         "Add spaced repetition for challenging topics"
       ],
       "consistency_tips": [
         "Set up study session reminders",
         "Prepare study materials in advance"
       ]
     }
   }

Error Handling
--------------

Progress Tracking API error responses:

.. code-block:: json

   {
     "error": "session_not_found",
     "message": "Study session not found or access denied",
     "code": 404,
     "details": {
       "session_id": "session_12345",
       "user_id": 123
     }
   }

**Common Error Codes**

- ``400`` - Invalid session or goal parameters
- ``404`` - Session, goal, or achievement not found
- ``409`` - Conflicting session state (already completed)
- ``422`` - Invalid progress data or metrics

See Also
--------

- :doc:`../guides/user_guide` - Progress tracking user guide
- :doc:`ai_assistant` - AI-powered learning insights
- :doc:`authentication` - API authentication
- :doc:`../enterprise/analytics` - Enterprise progress analytics
