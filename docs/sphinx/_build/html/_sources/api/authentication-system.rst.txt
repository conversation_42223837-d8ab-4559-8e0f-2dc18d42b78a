Core Authentication System
==========================

.. meta::
   :description: Complete authentication system with JWT tokens and session management
   :keywords: authentication, JWT, security, session management

Overview
--------

The Core Authentication System provides enterprise-grade security with JWT token management, user authentication, and comprehensive session handling. This production-ready system ensures secure access to all platform features.

**🔐 Key Features:**

- **JWT Token Management**: Secure token generation, validation, and refresh
- **Session Management**: Comprehensive user session tracking and control
- **Multi-Factor Authentication**: Enhanced security with MFA support
- **Role-Based Access Control**: Granular permissions and authorization
- **Enterprise Integration**: SSO and LDAP support for organizations

Authentication Endpoints
------------------------

User Registration
~~~~~~~~~~~~~~~~~

Register a new user account with comprehensive validation.

**POST** ``/api/v1/auth/register``

.. code-block:: json

   {
     "email": "<EMAIL>",
     "password": "SecurePassword123!",
     "first_name": "<PERSON>",
     "last_name": "Doe",
     "organization_id": 1,
     "role": "user"
   }

**Response:**

.. code-block:: json

   {
     "user_id": 12345,
     "email": "<EMAIL>",
     "first_name": "John",
     "last_name": "Doe",
     "organization_id": 1,
     "role": "user",
     "created_at": "2025-06-16T10:30:00Z",
     "email_verified": false,
     "status": "active"
   }

User Login
~~~~~~~~~~

Authenticate user and receive JWT tokens.

**POST** ``/api/v1/auth/login``

.. code-block:: json

   {
     "email": "<EMAIL>",
     "password": "SecurePassword123!"
   }

**Response:**

.. code-block:: json

   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "expires_in": 3600,
     "user": {
       "user_id": 12345,
       "email": "<EMAIL>",
       "first_name": "John",
       "last_name": "Doe",
       "role": "user",
       "organization_id": 1,
       "permissions": ["read:profile", "write:progress", "read:certifications"]
     }
   }

Token Refresh
~~~~~~~~~~~~~

Refresh expired access tokens using refresh token.

**POST** ``/api/v1/auth/refresh``

.. code-block:: json

   {
     "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   }

**Response:**

.. code-block:: json

   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "expires_in": 3600
   }

Session Management
------------------

Active Sessions
~~~~~~~~~~~~~~~

Get all active user sessions.

**GET** ``/api/v1/auth/sessions``

**Headers:**
``Authorization: Bearer {access_token}``

**Response:**

.. code-block:: json

   {
     "sessions": [
       {
         "session_id": "sess_abc123",
         "device_type": "desktop",
         "browser": "Chrome 91.0",
         "ip_address": "*************",
         "location": "San Francisco, CA",
         "created_at": "2025-06-16T10:30:00Z",
         "last_activity": "2025-06-16T14:45:00Z",
         "is_current": true
       }
     ],
     "total_sessions": 1
   }

Revoke Session
~~~~~~~~~~~~~~

Revoke a specific session or all sessions.

**DELETE** ``/api/v1/auth/sessions/{session_id}``

**Headers:**
``Authorization: Bearer {access_token}``

**Response:**

.. code-block:: json

   {
     "message": "Session revoked successfully",
     "session_id": "sess_abc123",
     "revoked_at": "2025-06-16T15:00:00Z"
   }

Multi-Factor Authentication
---------------------------

Enable MFA
~~~~~~~~~~~

Enable multi-factor authentication for enhanced security.

**POST** ``/api/v1/auth/mfa/enable``

**Headers:**
``Authorization: Bearer {access_token}``

**Response:**

.. code-block:: json

   {
     "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
     "secret_key": "JBSWY3DPEHPK3PXP",
     "backup_codes": [
       "12345678",
       "87654321",
       "11223344"
     ]
   }

Verify MFA
~~~~~~~~~~

Verify MFA token during login.

**POST** ``/api/v1/auth/mfa/verify``

.. code-block:: json

   {
     "email": "<EMAIL>",
     "password": "SecurePassword123!",
     "mfa_token": "123456"
   }

**Response:**

.. code-block:: json

   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "expires_in": 3600,
     "mfa_verified": true
   }

Role-Based Access Control
-------------------------

User Permissions
~~~~~~~~~~~~~~~~

Get user permissions and role information.

**GET** ``/api/v1/auth/permissions``

**Headers:**
``Authorization: Bearer {access_token}``

**Response:**

.. code-block:: json

   {
     "user_id": 12345,
     "role": "admin",
     "permissions": [
       "read:all",
       "write:all",
       "delete:users",
       "manage:organization"
     ],
     "organization_permissions": {
       "organization_id": 1,
       "role": "admin",
       "permissions": [
         "manage:users",
         "view:analytics",
         "manage:billing"
       ]
     }
   }

Update User Role
~~~~~~~~~~~~~~~~

Update user role and permissions (admin only).

**PUT** ``/api/v1/auth/users/{user_id}/role``

**Headers:**
``Authorization: Bearer {access_token}``

.. code-block:: json

   {
     "role": "manager",
     "permissions": [
       "read:team",
       "write:team",
       "view:analytics"
     ]
   }

**Response:**

.. code-block:: json

   {
     "user_id": 12345,
     "role": "manager",
     "permissions": [
       "read:team",
       "write:team",
       "view:analytics"
     ],
     "updated_at": "2025-06-16T15:30:00Z"
   }

Security Features
-----------------

Password Reset
~~~~~~~~~~~~~~

Initiate password reset process.

**POST** ``/api/v1/auth/password/reset``

.. code-block:: json

   {
     "email": "<EMAIL>"
   }

**Response:**

.. code-block:: json

   {
     "message": "Password reset email sent",
     "reset_token_expires": "2025-06-16T16:30:00Z"
   }

Confirm Password Reset
~~~~~~~~~~~~~~~~~~~~~~

Complete password reset with token.

**POST** ``/api/v1/auth/password/confirm``

.. code-block:: json

   {
     "reset_token": "reset_abc123xyz",
     "new_password": "NewSecurePassword123!"
   }

**Response:**

.. code-block:: json

   {
     "message": "Password reset successfully",
     "user_id": 12345,
     "reset_at": "2025-06-16T16:45:00Z"
   }

Account Security
~~~~~~~~~~~~~~~~

Get account security status and recommendations.

**GET** ``/api/v1/auth/security/status``

**Headers:**
``Authorization: Bearer {access_token}``

**Response:**

.. code-block:: json

   {
     "security_score": 85,
     "mfa_enabled": true,
     "password_strength": "strong",
     "last_password_change": "2025-05-15T10:00:00Z",
     "suspicious_activity": false,
     "recommendations": [
       "Consider updating password every 90 days",
       "Review active sessions regularly"
     ],
     "recent_activity": [
       {
         "action": "login",
         "timestamp": "2025-06-16T14:30:00Z",
         "ip_address": "*************",
         "location": "San Francisco, CA"
       }
     ]
   }

Enterprise Integration
----------------------

SSO Configuration
~~~~~~~~~~~~~~~~~

Configure Single Sign-On for enterprise organizations.

**POST** ``/api/v1/auth/sso/configure``

**Headers:**
``Authorization: Bearer {admin_token}``

.. code-block:: json

   {
     "organization_id": 1,
     "provider": "okta",
     "domain": "company.okta.com",
     "client_id": "client_abc123",
     "client_secret": "secret_xyz789",
     "redirect_uri": "https://app.certpathfinder.com/auth/callback"
   }

**Response:**

.. code-block:: json

   {
     "sso_config_id": "sso_config_123",
     "organization_id": 1,
     "provider": "okta",
     "status": "active",
     "configured_at": "2025-06-16T16:00:00Z"
   }

Error Handling
--------------

**Common Error Responses:**

.. code-block:: json

   {
     "error": {
       "code": "INVALID_CREDENTIALS",
       "message": "Invalid email or password",
       "details": {
         "field": "password",
         "attempts_remaining": 2
       }
     }
   }

**Status Codes:**
- ``200 OK``: Request successful
- ``400 Bad Request``: Invalid request data
- ``401 Unauthorized``: Invalid credentials or token
- ``403 Forbidden``: Insufficient permissions
- ``429 Too Many Requests``: Rate limit exceeded
- ``500 Internal Server Error``: Server error

Security Best Practices
------------------------

**Token Management:**
- Access tokens expire in 1 hour
- Refresh tokens expire in 30 days
- Tokens are automatically rotated on refresh
- Secure storage required for refresh tokens

**Session Security:**
- Sessions automatically expire after 24 hours of inactivity
- Maximum 5 concurrent sessions per user
- Suspicious activity detection and alerting
- Geographic location tracking for security

**Password Requirements:**
- Minimum 8 characters
- Must include uppercase, lowercase, number, and special character
- Cannot reuse last 5 passwords
- Automatic password strength validation
