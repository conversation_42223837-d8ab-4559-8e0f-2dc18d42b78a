System Architecture
===================

CertPathFinder is built using modern, scalable architecture patterns with a focus on maintainability, security, and performance.

.. contents:: Table of Contents
   :local:
   :depth: 2

Architecture Overview
---------------------

High-Level Architecture
~~~~~~~~~~~~~~~~~~~~~~~

CertPathFinder follows a modular architecture with clear separation of concerns:

**Technology Stack:**

- **Python 3.10+** - Core programming language
- **FastAPI** - Modern, high-performance web framework
- **SQLAlchemy** - Object-relational mapping (ORM)
- **PostgreSQL 13+** - Primary relational database
- **Redis 6+** - Caching and session storage
- **React 18+** - User interface framework
- **Docker** - Containerisation platform

Core Components
---------------

API Layer
~~~~~~~~~

The API layer is built with FastAPI and organised into versioned modules:

**API Structure:**

.. code-block:: text

   api/
   ├── __init__.py
   ├── app.py              # FastAPI application factory
   ├── config.py           # Configuration management
   └── v1/                 # API version 1
       ├── ai_study_assistant.py
       ├── career_transition.py
       ├── certifications.py
       ├── cost_calculator.py
       ├── enterprise.py
       └── user_profile.py

**Key API Features:**

- **Automatic OpenAPI Documentation** - Self-documenting API with Swagger UI
- **Request/Response Validation** - Pydantic models for data validation
- **Authentication & Authorisation** - JWT-based security with role-based access
- **Rate Limiting** - Configurable rate limits per endpoint and user type
- **CORS Support** - Cross-origin resource sharing for web applications

Service Layer
~~~~~~~~~~~~~

Business logic is organised into service modules with clear responsibilities:

**Service Architecture:**

.. code-block:: text

   services/
   ├── __init__.py
   ├── base_crud.py           # Base CRUD operations
   ├── ai_study_assistant.py  # AI-powered learning assistance
   ├── career_transition.py   # Career planning and analysis
   ├── certification.py       # Certification management
   ├── cost_calculator.py     # Cost analysis and ROI calculations
   └── progress_tracking.py   # Learning progress and analytics

Data Layer
~~~~~~~~~~

The data layer uses SQLAlchemy ORM with a well-structured model hierarchy:

**Model Organisation:**

.. code-block:: text

   models/
   ├── __init__.py
   ├── base.py                    # Base model with common fields
   ├── certification.py          # Certification and organisation models
   ├── cost_calculation.py       # Cost analysis models
   ├── progress_tracking.py      # Learning progress models
   ├── security_career_framework.py  # Career framework models
   └── user_experience.py        # User profiles and preferences

AI/ML Architecture
------------------

On-Device AI Processing
~~~~~~~~~~~~~~~~~~~~~~~

CertPathFinder implements privacy-first AI with complete on-device processing:

**AI Features:**

- **Performance Prediction** - Estimate exam success probability
- **Difficulty Assessment** - Personalised difficulty evaluation
- **Topic Recommendations** - Intelligent study topic sequencing
- **Learning Style Analysis** - Adaptive learning approach identification

**Privacy Architecture:**

- **Zero External Dependencies** - No data sent to external AI services
- **Local Model Training** - Models trained on user's own data
- **Encrypted Model Storage** - AI models encrypted at rest

Security Architecture
---------------------

Authentication & Authorisation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Multi-layered security approach with enterprise-grade features:

**Authentication Methods:**

- **JWT Tokens** - Stateless authentication with configurable expiration
- **Single Sign-On (SSO)** - Integration with enterprise identity providers
- **Multi-Factor Authentication** - Optional 2FA/MFA support

**Authorisation Model:**

.. code-block:: python

   # Role-based access control hierarchy
   ROLES = {
       'guest': 1,        # Read-only public access
       'user': 2,         # Personal features access
       'premium': 3,      # Advanced AI features
       'admin': 4,        # Organisation management
       'super_admin': 5,  # System administration
       'enterprise': 6    # Multi-tenant control
   }

Data Security
~~~~~~~~~~~~~

Comprehensive data protection at multiple levels:

**Encryption:**

- **Data at Rest** - AES-256 encryption for sensitive data
- **Data in Transit** - TLS 1.3 for all communications
- **Database Encryption** - Transparent data encryption support

**Privacy Controls:**

- **Data Minimisation** - Collect only necessary information
- **Retention Policies** - Configurable data retention periods
- **Right to Deletion** - GDPR-compliant data deletion
- **Audit Logging** - Comprehensive access and modification logs

Deployment Architecture
-----------------------

Containerised Deployment
~~~~~~~~~~~~~~~~~~~~~~~~

Docker-based deployment with multiple environment support:

**Container Structure:**

.. code-block:: yaml

   # docker-compose.yml structure
   services:
     api:
       build: ./Dockerfile.api
       environment:
         - DATABASE_URL
         - REDIS_URL
         - SECRET_KEY
     
     database:
       image: postgres:15
       volumes:
         - postgres_data:/var/lib/postgresql/data
     
     redis:
       image: redis:7-alpine

**Environment Configurations:**

- **Development** - Local development with hot reloading
- **Testing** - Automated testing environment
- **Staging** - Production-like environment for testing
- **Production** - Optimised production deployment

Performance Optimisation
-------------------------

Database Optimisation
~~~~~~~~~~~~~~~~~~~~~

Strategic optimisations for database performance:

**Query Optimisation:**

- **Selective Loading** - Load only required fields and relationships
- **Batch Operations** - Bulk inserts and updates where possible
- **Index Maintenance** - Regular index analysis and optimisation

**Caching Strategy:**

- **Application-Level Caching** - Frequently accessed data cached in Redis
- **Query Result Caching** - Database query results cached with TTL
- **Session Caching** - User sessions stored in Redis

API Performance
~~~~~~~~~~~~~~~

Optimisations for API response times:

**Response Optimisation:**

- **Pagination** - Large result sets paginated by default
- **Field Selection** - Allow clients to specify required fields
- **Compression** - Gzip compression for API responses
- **HTTP/2 Support** - Modern HTTP protocol support

Development Workflow
--------------------

Code Organisation
~~~~~~~~~~~~~~~~~

Structured codebase following Python best practices:

**Project Structure:**

.. code-block:: text

   certpathfinder/
   ├── api/                 # API layer
   ├── services/            # Business logic
   ├── models/              # Data models
   ├── schemas/             # Pydantic schemas
   ├── utils/               # Utility functions
   ├── tests/               # Test suite
   ├── migrations/          # Database migrations
   └── docs/                # Documentation

**Code Quality Standards:**

- **Type Hints** - Full type annotation coverage
- **Docstrings** - Comprehensive function and class documentation
- **Linting** - Black, isort, and flake8 for code formatting
- **Testing** - Comprehensive test coverage with pytest

See Also
--------

- :doc:`ai_models` - AI model implementation details
- :doc:`contributing` - Development contribution guidelines
- :doc:`../api/index` - API documentation and reference
