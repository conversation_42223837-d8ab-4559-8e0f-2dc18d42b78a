Contributing Guide
=================

Welcome to the CertPathFinder project! We appreciate your interest in contributing to this open-source cybersecurity education platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Getting Started
---------------

Prerequisites
~~~~~~~~~~~~~

Before contributing, ensure you have the following installed:

**Required Software:**

- **Python 3.10+** - Core development language
- **Node.js 18+** - Frontend development
- **Docker & Docker Compose** - Containerised development environment
- **Git** - Version control system
- **PostgreSQL 13+** - Database (or use Docker)
- **Redis 6+** - Caching (or use Docker)

Setting Up Development Environment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Fork and Clone the Repository**

.. code-block:: bash

   # Fork the repository on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/replit-CertPathFinder.git
   cd replit-CertPathFinder
   
   # Add upstream remote
   git remote add upstream https://github.com/forkrul/replit-CertPathFinder.git

2. **Set Up Python Environment**

.. code-block:: bash

   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install dependencies
   pip install -r requirements.txt

3. **Configure Environment Variables**

.. code-block:: bash

   # Copy example environment file
   cp .env.example .env
   
   # Edit .env with your local configuration

4. **Set Up Database**

.. code-block:: bash

   # Using Docker (recommended)
   docker-compose up -d database redis
   
   # Apply database migrations
   alembic upgrade head

5. **Verify Installation**

.. code-block:: bash

   # Run tests to verify setup
   pytest tests/
   
   # Start development server
   python run_api.py

Development Workflow
--------------------

Branch Strategy
~~~~~~~~~~~~~~~

We use a Git flow-inspired branching strategy:

**Branch Types:**

- **main** - Production-ready code
- **develop** - Integration branch for features
- **feature/*** - New features and enhancements
- **bugfix/*** - Bug fixes
- **docs/*** - Documentation updates

**Creating a Feature Branch:**

.. code-block:: bash

   # Update your local repository
   git checkout develop
   git pull upstream develop
   
   # Create feature branch
   git checkout -b feature/your-feature-name
   
   # Make your changes and commit
   git add .
   git commit -m "feat: add new feature description"
   
   # Push to your fork
   git push origin feature/your-feature-name

Code Standards
~~~~~~~~~~~~~~

**Python Code Standards:**

- **PEP 8** - Follow Python style guide
- **Type Hints** - Use type annotations for all functions
- **Docstrings** - Document all public functions and classes
- **Black** - Code formatting (run `black .`)
- **isort** - Import sorting (run `isort .`)
- **flake8** - Linting (run `flake8`)

**Example Python Code:**

.. code-block:: python

   from typing import List, Optional
   from datetime import datetime
   
   def calculate_certification_cost(
       certification_id: int,
       location: str,
       include_materials: bool = True
   ) -> dict:
       """
       Calculate the total cost for a certification.
       
       Args:
           certification_id: Unique identifier for the certification
           location: User's location for localised pricing
           include_materials: Whether to include study materials cost
           
       Returns:
           Dictionary containing cost breakdown and total
           
       Raises:
           ValueError: If certification_id is invalid
       """
       if certification_id <= 0:
           raise ValueError("Certification ID must be positive")
       
       # Implementation here
       return {
           "total_cost": 1500.00,
           "currency": "GBP",
           "breakdown": {
               "exam_fee": 749.00,
               "materials": 751.00 if include_materials else 0.00
           }
       }

Testing Requirements
~~~~~~~~~~~~~~~~~~~~

All contributions must include appropriate tests:

**Test Types Required:**

- **Unit Tests** - Test individual functions and methods
- **Integration Tests** - Test API endpoints and service interactions
- **End-to-End Tests** - Test complete user workflows (for major features)

**Running Tests:**

.. code-block:: bash

   # Run all tests
   pytest tests/
   
   # Run tests with coverage
   pytest tests/ --cov=. --cov-report=html
   
   # Run specific test file
   pytest tests/test_cost_calculator.py

**Writing Tests:**

.. code-block:: python

   import pytest
   from unittest.mock import Mock, patch
   from services.cost_calculator import CostCalculatorService
   
   class TestCostCalculatorService:
       def setup_method(self):
           """Set up test fixtures"""
           self.service = CostCalculatorService()
           
       def test_calculate_basic_cost(self):
           """Test basic cost calculation"""
           result = self.service.calculate_cost(
               certification_id=1,
               location="United Kingdom"
           )
           
           assert result["total_cost"] > 0
           assert result["currency"] == "GBP"
           assert "breakdown" in result

Documentation Standards
~~~~~~~~~~~~~~~~~~~~~~~

**Documentation Requirements:**

- **API Documentation** - All endpoints must be documented
- **Code Comments** - Complex logic should be commented
- **README Updates** - Update relevant README files
- **Changelog** - Add entries for user-facing changes

**Documentation Style:**

- Use British English spelling and grammar
- Write clear, concise explanations
- Include code examples where helpful
- Use proper reStructuredText formatting for Sphinx docs

Contribution Types
------------------

Bug Reports
~~~~~~~~~~~

When reporting bugs, please include:

**Bug Report Template:**

.. code-block:: text

   **Bug Description**
   A clear description of what the bug is.
   
   **Steps to Reproduce**
   1. Go to '...'
   2. Click on '....'
   3. See error
   
   **Expected Behaviour**
   What you expected to happen.
   
   **Actual Behaviour**
   What actually happened.
   
   **Environment**
   - OS: [e.g. Ubuntu 22.04]
   - Python Version: [e.g. 3.10.8]
   - CertPathFinder Version: [e.g. 1.0.0]

Feature Requests
~~~~~~~~~~~~~~~~

For new features, please provide:

**Feature Request Template:**

.. code-block:: text

   **Feature Summary**
   A brief summary of the feature you'd like to see.
   
   **Problem Statement**
   What problem does this feature solve?
   
   **Proposed Solution**
   Describe your proposed solution in detail.
   
   **User Stories**
   - As a [user type], I want [goal] so that [benefit]
   
   **Acceptance Criteria**
   - [ ] Criterion 1
   - [ ] Criterion 2

Pull Request Process
~~~~~~~~~~~~~~~~~~~~

1. **Create Feature Branch**

.. code-block:: bash

   git checkout -b feature/descriptive-name

2. **Make Changes**
   - Write clean, well-documented code
   - Add appropriate tests
   - Update documentation

3. **Test Your Changes**

.. code-block:: bash

   # Run full test suite
   pytest tests/
   
   # Run linting
   black .
   isort .
   flake8

4. **Commit Changes**

.. code-block:: bash

   # Use conventional commit format
   git commit -m "feat: add certification cost calculator API"
   git commit -m "fix: resolve authentication token expiry issue"
   git commit -m "docs: update API documentation for new endpoints"

5. **Push and Create Pull Request**

.. code-block:: bash

   git push origin feature/descriptive-name
   # Create pull request on GitHub

Review Process
--------------

Code Review Guidelines
~~~~~~~~~~~~~~~~~~~~~~

**For Contributors:**

- Respond to feedback promptly and professionally
- Make requested changes in separate commits
- Ask questions if feedback is unclear
- Test your changes after addressing feedback

**For Reviewers:**

- Be constructive and specific in feedback
- Focus on code quality, security, and maintainability
- Suggest improvements rather than just pointing out problems
- Approve when code meets project standards

**Review Criteria:**

- Code quality and readability
- Test coverage and quality
- Documentation completeness
- Security considerations
- Performance implications
- Backwards compatibility

Community Guidelines
--------------------

Code of Conduct
~~~~~~~~~~~~~~~

We are committed to providing a welcoming and inclusive environment:

- **Be respectful** - Treat all community members with respect
- **Be inclusive** - Welcome newcomers and diverse perspectives
- **Be collaborative** - Work together towards common goals
- **Be patient** - Help others learn and grow
- **Be constructive** - Provide helpful feedback and suggestions

Getting Help
~~~~~~~~~~~~

If you need help with contributing:

1. Check this documentation first
2. Search existing GitHub issues
3. Create a new issue with the "question" label
4. Join community discussions on GitHub

Thank you for contributing to CertPathFinder! Your efforts help make cybersecurity education more accessible and effective for everyone.

See Also
--------

- :doc:`architecture` - System architecture overview
- :doc:`../installation` - Installation and setup guide
- :doc:`../api/index` - API documentation and reference
