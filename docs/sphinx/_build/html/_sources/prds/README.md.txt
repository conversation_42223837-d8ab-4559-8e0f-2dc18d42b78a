# PRD Documentation System

This directory contains the Sphinx documentation for CertPathFinder's 5-agent distributed architecture. The documentation is automatically updated based on git commit activity.

## 📋 Agent Documentation

### Core Agents

1. **[Agent 1: Core Platform Engine](agent-1-core-platform-engine.rst)**
   - Foundation APIs and certification database
   - User management and authentication
   - Revenue Target: $15M ARR
   - Status: 🟡 In Development

2. **[Agent 2: AI Study Assistant](agent-2-ai-study-assistant.rst)**
   - On-device AI models for personalized learning
   - Adaptive learning paths and performance prediction
   - Revenue Target: $12M ARR
   - Status: 🟡 In Development

3. **[Agent 3: Enterprise & Analytics Engine](agent-3-enterprise-analytics-engine.rst)**
   - Multi-tenant team management and compliance
   - Data monetization through industry intelligence
   - Revenue Target: $18M ARR
   - Status: 🟡 In Development

4. **[Agent 4: Career & Cost Intelligence](agent-4-career-cost-intelligence.rst)**
   - A* pathfinding for career transitions
   - Comprehensive ROI analysis and cost modeling
   - Revenue Target: $10M ARR
   - Status: 🔴 Planning Phase

5. **[Agent 5: Marketplace & Integration Hub](agent-5-marketplace-integration-hub.rst)**
   - Partnership ecosystem with certification bodies
   - Training marketplace with commission revenue
   - Revenue Target: $12M ARR
   - Status: 🔴 Planning Phase

## 🔄 Automatic Documentation Updates

The documentation is automatically updated using a sophisticated monitoring system that:

### Monitoring Features

- **Git Commit Monitoring**: Tracks all commits for agent-related changes
- **Smart Agent Detection**: Identifies which agents are affected by each commit
- **Automatic Status Updates**: Updates documentation status based on commit activity
- **Real-time Tracking**: Maintains current implementation progress

### How It Works

1. **Commit Analysis**: The system analyzes commit messages and changed files
2. **Agent Mapping**: Maps changes to specific agents based on keywords and file paths
3. **Documentation Updates**: Automatically updates relevant agent documentation
4. **Status Tracking**: Updates implementation status and progress indicators

### Monitoring Script Usage

```bash
# Check for updates once
python3 scripts/prd_docs_monitor.py --check

# Force update all documentation
python3 scripts/prd_docs_monitor.py --update

# Run as daemon (continuous monitoring)
python3 scripts/prd_docs_monitor.py --daemon

# Quick wrapper script
./update-prd-docs.sh
```

### Agent Detection Keywords

The system uses these keywords to identify agent-related commits:

- **Agent 1**: core, platform, foundation, api, user, auth, certification
- **Agent 2**: ai, ml, study, recommendation, prediction, assistant
- **Agent 3**: enterprise, analytics, compliance, team, organization
- **Agent 4**: career, cost, salary, transition, pathfinding
- **Agent 5**: marketplace, integration, partnership, api, hub

## 📊 Documentation Structure

Each agent documentation includes:

### Standard Sections

1. **Executive Summary**: Mission, value propositions, and key features
2. **Market Opportunity**: Target market analysis and revenue projections
3. **Technical Requirements**: APIs, architecture, and performance targets
4. **Features & Capabilities**: Detailed feature descriptions and code examples
5. **User Experience**: UI/UX requirements and user flows
6. **Success Metrics**: KPIs, performance targets, and business metrics
7. **Integration Strategy**: Inter-agent communication and data flow
8. **Implementation Roadmap**: Development phases and milestones
9. **Risk Mitigation**: Technical and business risk management
10. **Development Workflow**: Testing strategy and quality assurance

### Dynamic Status Tracking

Each document includes automatically updated status information:

- **Current Status**: Real-time development status based on commits
- **Last Updated**: Automatic timestamp with commit reference
- **Next Milestone**: Upcoming development targets and deadlines

## 🚀 Building Documentation

### Prerequisites

```bash
pip install sphinx sphinx-rtd-theme myst-parser
```

### Build Commands

```bash
# Navigate to docs directory
cd docs/sphinx

# Build HTML documentation
sphinx-build -b html . _build/html

# Build with clean rebuild
sphinx-build -b html . _build/html -E

# Serve locally (if sphinx-autobuild is installed)
sphinx-autobuild . _build/html
```

### Output Location

Built documentation is available at: `docs/sphinx/_build/html/index.html`

## 🔧 Configuration

### Monitoring Configuration

The monitoring system can be configured by editing `scripts/prd_docs_monitor.py`:

- **Check Interval**: Modify daemon check frequency
- **Agent Keywords**: Update keyword detection for agents
- **File Patterns**: Adjust file path patterns for agent detection
- **Status Updates**: Customize status update logic

### Sphinx Configuration

Sphinx settings are in `docs/sphinx/conf.py`:

- **Theme Settings**: Customize appearance and navigation
- **Extensions**: Add or remove Sphinx extensions
- **Cross-References**: Configure inter-document linking

## 📈 Metrics and Analytics

The system tracks:

### Documentation Metrics

- **Update Frequency**: How often each agent's documentation is updated
- **Commit Relevance**: Accuracy of agent detection from commits
- **Status Accuracy**: Alignment between actual development and documented status

### Development Metrics

- **Implementation Progress**: Track development progress across all agents
- **Commit Activity**: Monitor development velocity and focus areas
- **Milestone Tracking**: Automatic milestone progress updates

## 🛠️ Maintenance

### Regular Tasks

1. **Review Agent Keywords**: Ensure keyword detection remains accurate
2. **Update Status Logic**: Refine status determination based on commit patterns
3. **Documentation Quality**: Review and improve documentation content
4. **Build Verification**: Ensure Sphinx builds complete successfully

### Troubleshooting

#### Common Issues

1. **Missing Updates**: Check git repository status and commit access
2. **Build Failures**: Verify Sphinx installation and configuration
3. **Incorrect Agent Detection**: Review and update keyword patterns
4. **Status Inconsistencies**: Manually update status if automatic detection fails

#### Debug Commands

```bash
# Test monitoring script
python3 scripts/prd_docs_monitor.py --check --verbose

# Force update specific agent
python3 scripts/prd_docs_monitor.py --update --agent agent-1

# Check git status
git status && git log --oneline -5
```

## 📞 Support

For issues with the PRD documentation system:

1. Check the monitoring logs: `/tmp/prd-monitor.log`
2. Verify git repository status and permissions
3. Test Sphinx build manually: `cd docs/sphinx && sphinx-build -b html . _build/html`
4. Review agent keyword detection in `scripts/prd_docs_monitor.py`

---

**Last Updated**: Automatically maintained by the PRD monitoring system  
**System Status**: ✅ Active monitoring enabled  
**Documentation Coverage**: 5/5 agents documented with automatic updates
