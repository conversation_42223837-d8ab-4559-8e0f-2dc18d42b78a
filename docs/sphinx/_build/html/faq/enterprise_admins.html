<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>🏢 Enterprise Administrators FAQ &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/faq/enterprise_admins.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="🔄 Career Changers FAQ" href="career_changers.html" />
    <link rel="prev" title="🔧 System Administrators FAQ" href="administrators.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">❓ Frequently Asked Questions</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="students.html">👨‍🎓 Students &amp; Learners FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="professionals.html">👩‍💼 Working Professionals FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="managers.html">👨‍💼 Managers &amp; Team Leads FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html">🔧 System Administrators FAQ</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">🏢 Enterprise Administrators FAQ</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#enterprise-deployment">🏗️ <strong>Enterprise Deployment</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#user-management">👥 <strong>User Management</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#analytics-reporting">📊 <strong>Analytics &amp; Reporting</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#integration-customization">🔧 <strong>Integration &amp; Customization</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#security-governance">🔒 <strong>Security &amp; Governance</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#enterprise-resources">🎯 <strong>Enterprise Resources</strong></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="career_changers.html">🔄 Career Changers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="academics_researchers.html">🎓 Academics &amp; Researchers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="training_managers.html">📚 Corporate Training Managers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#choose-your-user-type">🎯 <strong>Choose Your User Type</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-topics-across-all-user-types">🔍 <strong>Common Topics Across All User Types</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#still-need-help">📞 <strong>Still Need Help?</strong></a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">❓ Frequently Asked Questions</a></li>
      <li class="breadcrumb-item active">🏢 Enterprise Administrators FAQ</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/faq/enterprise_admins.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="enterprise-administrators-faq">
<h1>🏢 Enterprise Administrators FAQ<a class="headerlink" href="#enterprise-administrators-faq" title="Link to this heading"></a></h1>
<p><strong>25 Essential Questions for Enterprise Administrators</strong></p>
<p>This FAQ section addresses the unique needs of enterprise administrators managing CertPathFinder deployments across large organizations with multiple departments, locations, and complex requirements.</p>
<section id="enterprise-deployment">
<h2>🏗️ <strong>Enterprise Deployment</strong><a class="headerlink" href="#enterprise-deployment" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>1. How do I deploy CertPathFinder across multiple organisations?</strong></dt><dd><p>Use our multi-tenant architecture with organisation isolation, centralised management console, and automated provisioning. Each organisation gets isolated data, custom branding, and independent configuration. Our enterprise deployment guide provides detailed implementation steps.</p>
</dd>
<dt><strong>2. What’s the recommended architecture for enterprise scale?</strong></dt><dd><p><strong>Kubernetes cluster</strong> with auto-scaling, <strong>separate database clusters</strong> per region, <strong>Redis cluster</strong> for session management, <strong>CDN</strong> for global content delivery, and <strong>load balancers</strong> with health checks. Our enterprise architecture guide provides reference implementations.</p>
</dd>
<dt><strong>3. How do I configure single sign-on (SSO) for the enterprise?</strong></dt><dd><p>Integrate with SAML 2.0, OAuth 2.0, or OpenID Connect providers. Configure attribute mapping, group synchronization, and automatic user provisioning. Our SSO integration guide covers Active Directory, Okta, Azure AD, and other providers.</p>
</dd>
<dt><strong>4. What’s the licensing model for enterprise deployments?</strong></dt><dd><p><strong>Per-user licensing</strong> with volume discounts, <strong>site licenses</strong> for unlimited users, and <strong>custom enterprise agreements</strong>. Includes support, training, and customization services. Contact our enterprise team for pricing and terms.</p>
</dd>
<dt><strong>5. How do I handle data residency and compliance requirements?</strong></dt><dd><p>Deploy in specific geographic regions, configure data encryption at rest and in transit, implement audit logging, and ensure compliance with local regulations (GDPR, SOC 2, HIPAA). Our compliance guide covers regional requirements.</p>
</dd>
</dl>
</section>
<section id="user-management">
<h2>👥 <strong>User Management</strong><a class="headerlink" href="#user-management" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>6. How do I manage thousands of users efficiently?</strong></dt><dd><p>Use bulk import/export tools, automated user provisioning via LDAP/AD sync, role-based access control with groups, and self-service user management. Our user management tools handle large-scale operations efficiently.</p>
</dd>
<dt><strong>7. What’s the best way to organise users across departments?</strong></dt><dd><p>Create organisational units matching your company structure, assign department-specific roles and permissions, and use custom fields for additional categorisation. Our organisational structure guide provides best practises.</p>
</dd>
<dt><strong>8. How do I handle user onboarding and offboarding at scale?</strong></dt><dd><p>Automate user lifecycle management through HR system integration, configure automatic account provisioning/deprovisioning, and maintain audit trails. Our automation tools reduce manual overhead.</p>
</dd>
<dt><strong>9. What reporting is available for user activity and progress?</strong></dt><dd><p><strong>Executive dashboards</strong> with high-level metrics, <strong>department reports</strong> showing team progress, <strong>individual progress tracking</strong>, and <strong>compliance reporting</strong>. Export data for external analysis or integrate with BI tools.</p>
</dd>
<dt><strong>10. How do I manage user permissions across different business units?</strong></dt><dd><p>Use hierarchical role structures, delegate administration to department managers, and implement approval workflows for sensitive operations. Our permission management system provides granular control.</p>
</dd>
</dl>
</section>
<section id="analytics-reporting">
<h2>📊 <strong>Analytics &amp; Reporting</strong><a class="headerlink" href="#analytics-reporting" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>11. What enterprise analytics are available?</strong></dt><dd><p><strong>Organisational performance metrics</strong>, <strong>certification ROI analysis</strong>, <strong>skills gap identification</strong>, <strong>training effectiveness measurement</strong>, and <strong>predictive analytics</strong> for workforce planning. Our analytics suite provides comprehensive insights.</p>
</dd>
<dt><strong>12. How do I create custom reports for executives?</strong></dt><dd><p>Use our report builder with drag-and-drop interface, schedule automated report delivery, and customize branding and formatting. Export to PDF, Excel, or integrate with BI tools like Tableau or Power BI.</p>
</dd>
<dt><strong>13. What benchmarking data is available?</strong></dt><dd><p><strong>Industry comparisons</strong> for certification rates, <strong>peer organisation benchmarks</strong>, <strong>regional performance data</strong>, and <strong>best practise recommendations</strong>. Our benchmarking service provides competitive intelligence.</p>
</dd>
<dt><strong>14. How do I track training ROI across the organization?</strong></dt><dd><p>Measure certification completion rates, time-to-competency improvements, employee retention correlation, and business impact metrics. Our ROI calculator provides comprehensive analysis frameworks.</p>
</dd>
<dt><strong>15. What compliance reporting features are included?</strong></dt><dd><p><strong>Automated compliance reports</strong> for various standards, <strong>audit trail documentation</strong>, <strong>certification status tracking</strong>, and <strong>regulatory requirement mapping</strong>. Generate reports for internal and external audits.</p>
</dd>
</dl>
</section>
<section id="integration-customization">
<h2>🔧 <strong>Integration &amp; Customization</strong><a class="headerlink" href="#integration-customization" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>16. How do I integrate with existing HR and LMS systems?</strong></dt><dd><p>Use our REST APIs, pre-built connectors for popular systems (Workday, SuccessFactors, Cornerstone), and webhook integrations. Our integration guide covers common scenarios and provides sample code.</p>
</dd>
<dt><strong>17. What customisation options are available for enterprise clients?</strong></dt><dd><p><strong>Custom branding</strong> and themes, <strong>workflow customisation</strong>, <strong>additional data fields</strong>, <strong>custom reporting</strong>, and <strong>specialised integrations</strong>. Our professional services team handles complex customisations.</p>
</dd>
<dt><strong>18. How do I configure automated workflows for certification management?</strong></dt><dd><p>Set up approval workflows, automatic notifications, escalation procedures, and integration with external systems. Our workflow engine provides visual configuration tools.</p>
</dd>
<dt><strong>19. Can I integrate with procurement and budgeting systems?</strong></dt><dd><p>Yes, through API integration with financial systems, automated purchase requisitions, budget tracking, and approval workflows. Our financial integration guide covers common ERP systems.</p>
</dd>
<dt><strong>20. What APIs are available for custom development?</strong></dt><dd><p><strong>Complete REST API</strong> covering all platform functionality, <strong>GraphQL endpoints</strong> for complex queries, <strong>webhook support</strong> for real-time notifications, and <strong>SDK libraries</strong> for popular languages.</p>
</dd>
</dl>
</section>
<section id="security-governance">
<h2>🔒 <strong>Security &amp; Governance</strong><a class="headerlink" href="#security-governance" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>21. What enterprise security features are included?</strong></dt><dd><p><strong>Multi-factor authentication</strong>, <strong>IP whitelisting</strong>, <strong>session management</strong>, <strong>audit logging</strong>, <strong>data encryption</strong>, and <strong>security monitoring</strong>. Our security framework meets enterprise requirements.</p>
</dd>
<dt><strong>22. How do I implement data governance policies?</strong></dt><dd><p>Configure data retention policies, access controls, data classification, and automated compliance monitoring. Our governance tools help maintain regulatory compliance.</p>
</dd>
<dt><strong>23. What backup and disaster recovery options are available?</strong></dt><dd><p><strong>Automated backups</strong> with configurable retention, <strong>cross-region replication</strong>, <strong>point-in-time recovery</strong>, and <strong>disaster recovery testing</strong>. Our DR solutions ensure business continuity.</p>
</dd>
<dt><strong>24. How do I manage security across multiple tenants?</strong></dt><dd><p>Each tenant has isolated security configurations, independent audit logs, and separate encryption keys. Our multi-tenant security model ensures complete isolation.</p>
</dd>
<dt><strong>25. What monitoring and alerting capabilities exist?</strong></dt><dd><p><strong>Real-time monitoring</strong> of system health, <strong>automated alerting</strong> for issues, <strong>performance analytics</strong>, and <strong>security event monitoring</strong>. Integration with enterprise monitoring tools.</p>
</dd>
</dl>
</section>
<section id="enterprise-resources">
<h2>🎯 <strong>Enterprise Resources</strong><a class="headerlink" href="#enterprise-resources" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>🏢 Deployment Services</strong></dt><dd><ul class="simple">
<li><p>Architecture consulting</p></li>
<li><p>Implementation planning</p></li>
<li><p>Migration assistance</p></li>
<li><p>Performance optimization</p></li>
</ul>
</dd>
<dt><strong>📚 Training &amp; Support</strong></dt><dd><ul class="simple">
<li><p>Administrator training programs</p></li>
<li><p>User adoption workshops</p></li>
<li><p>24/7 enterprise support</p></li>
<li><p>Dedicated customer success manager</p></li>
</ul>
</dd>
<dt><strong>🔧 Professional Services</strong></dt><dd><ul class="simple">
<li><p>Custom development</p></li>
<li><p>System integration</p></li>
<li><p>Data migration</p></li>
<li><p>Compliance consulting</p></li>
</ul>
</dd>
<dt><strong>📊 Business Intelligence</strong></dt><dd><ul class="simple">
<li><p>Executive dashboards</p></li>
<li><p>Predictive analytics</p></li>
<li><p>Benchmarking services</p></li>
<li><p>ROI analysis tools</p></li>
</ul>
</dd>
</dl>
<p>—</p>
<p><em>Need more help? Check out our :doc:`../enterprise/index` or contact enterprise support at enterprise&#64;certpathfinder.com</em></p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="administrators.html" class="btn btn-neutral float-left" title="🔧 System Administrators FAQ" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="career_changers.html" class="btn btn-neutral float-right" title="🔄 Career Changers FAQ" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>