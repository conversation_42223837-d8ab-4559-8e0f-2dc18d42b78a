<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>🔄 Career Changers FAQ &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/faq/career_changers.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="🎓 Academics &amp; Researchers FAQ" href="academics_researchers.html" />
    <link rel="prev" title="🏢 Enterprise Administrators FAQ" href="enterprise_admins.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">❓ Frequently Asked Questions</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="students.html">👨‍🎓 Students &amp; Learners FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="professionals.html">👩‍💼 Working Professionals FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="managers.html">👨‍💼 Managers &amp; Team Leads FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html">🔧 System Administrators FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise_admins.html">🏢 Enterprise Administrators FAQ</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">🔄 Career Changers FAQ</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#getting-started">🚀 <strong>Getting Started</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#leveraging-existing-skills">💼 <strong>Leveraging Existing Skills</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#learning-strategy">📚 <strong>Learning Strategy</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#job-search-strategy">🎯 <strong>Job Search Strategy</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#technical-development">🔧 <strong>Technical Development</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#career-changer-resources">🎯 <strong>Career Changer Resources</strong></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="academics_researchers.html">🎓 Academics &amp; Researchers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="training_managers.html">📚 Corporate Training Managers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#choose-your-user-type">🎯 <strong>Choose Your User Type</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-topics-across-all-user-types">🔍 <strong>Common Topics Across All User Types</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#still-need-help">📞 <strong>Still Need Help?</strong></a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">❓ Frequently Asked Questions</a></li>
      <li class="breadcrumb-item active">🔄 Career Changers FAQ</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/faq/career_changers.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="career-changers-faq">
<h1>🔄 Career Changers FAQ<a class="headerlink" href="#career-changers-faq" title="Link to this heading"></a></h1>
<p><strong>25 Essential Questions for Career Transition Professionals</strong></p>
<p>This FAQ section is designed for professionals transitioning into cybersecurity from other fields, helping you leverage existing skills while building new cybersecurity competencies.</p>
<section id="getting-started">
<h2>🚀 <strong>Getting Started</strong><a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>1. I have no cybersecurity experience. Is it too late to start?</strong></dt><dd><p>It’s never too late! Many successful cybersecurity professionals started later in their careers. Your diverse background brings valuable perspectives. Use our career transition assessment to identify transferable skills and create a personalized roadmap. Many employers value diverse experience.</p>
</dd>
<dt><strong>2. How do I identify which cybersecurity skills I already have?</strong></dt><dd><p>Use our skills mapping tool to analyse your background. Project management, risk assessment, compliance, technical troubleshooting, and analytical thinking are all valuable in cybersecurity. Our transferable skills analyser shows how your experience applies to security roles.</p>
</dd>
<dt><strong>3. What’s the typical timeline for transitioning to cybersecurity?</strong></dt><dd><p><strong>6-18 months</strong> for entry-level positions with intensive study. <strong>12-24 months</strong> for mid-level roles leveraging existing experience. Timeline depends on your background, study time, and target role. Our transition planner provides personalized timelines.</p>
</dd>
<dt><strong>4. Should I quit my current job to focus on the transition?</strong></dt><dd><p>Generally no. Study part-time whilst working, gain relevant experience through volunteer work or side projects, and transition gradually. Our part-time study guides help balance work and learning. Consider sabbaticals or reduced hours if financially feasible.</p>
</dd>
<dt><strong>5. What’s the best first certification for career changers?</strong></dt><dd><p><strong>CompTIA Security+</strong> is ideal for most career changers - it covers fundamentals without requiring deep technical background. <strong>CISSP Associate</strong> works for those with management experience. Our career changer assessment recommends the best starting point.</p>
</dd>
</dl>
</section>
<section id="leveraging-existing-skills">
<h2>💼 <strong>Leveraging Existing Skills</strong><a class="headerlink" href="#leveraging-existing-skills" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>6. How do I highlight transferable skills on my resume?</strong></dt><dd><p>Map your experience to cybersecurity contexts: project management → security program management, audit experience → compliance roles, IT support → security operations. Our resume transformation guide provides templates and examples.</p>
</dd>
<dt><strong>7. What if my background is completely non-technical?</strong></dt><dd><p>Focus on governance, risk, compliance (GRC) roles that value business acumen. Consider security awareness training, policy development, or vendor management roles. Our non-technical pathway shows progression options.</p>
</dd>
<dt><strong>8. How do I leverage finance/accounting experience in cybersecurity?</strong></dt><dd><p><strong>Risk management</strong> translates directly to cybersecurity risk. <strong>Audit experience</strong> applies to security audits and compliance. <strong>Financial controls</strong> knowledge helps with security controls. Consider roles in GRC, audit, or security finance.</p>
</dd>
<dt><strong>9. Can project management experience help in cybersecurity?</strong></dt><dd><p>Absolutely! Security programmes need strong project managers. Your skills apply to security implementations, incident response coordination, and compliance projects. Consider security programme management or security consulting roles.</p>
</dd>
<dt><strong>10. How does military experience translate to cybersecurity?</strong></dt><dd><p>Military experience is highly valued for discipline, security clearance, and understanding of threats. Many concepts translate directly. Use our military transition guide to map your experience to civilian cybersecurity roles.</p>
</dd>
</dl>
</section>
<section id="learning-strategy">
<h2>📚 <strong>Learning Strategy</strong><a class="headerlink" href="#learning-strategy" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>11. What’s the most efficient learning path for career changers?</strong></dt><dd><p>Start with fundamentals (Security+), gain hands-on experience through labs, focus on one domain initially, and gradually expand. Our accelerated learning program is designed specifically for career changers.</p>
</dd>
<dt><strong>12. Should I get a cybersecurity degree or focus on certifications?</strong></dt><dd><p><strong>Certifications first</strong> for faster entry and immediate credibility. Consider degrees for long-term career advancement or if you have time and resources. Many successful professionals have certifications without degrees.</p>
</dd>
<dt><strong>13. How do I build hands-on experience without a security job?</strong></dt><dd><p>Set up home labs, participate in Capture The Flag (CTF) competitions, volunteer for nonprofits, contribute to open source security projects, and practise with virtual environments. Our hands-on experience guide provides specific opportunities.</p>
</dd>
<dt><strong>14. What’s the best way to network in the cybersecurity community?</strong></dt><dd><p>Join local cybersecurity meetups, attend virtual conferences, participate in online communities (Reddit, Discord), and connect with professionals on LinkedIn. Our networking guide provides specific strategies for career changers.</p>
</dd>
<dt><strong>15. How do I stay motivated during the transition period?</strong></dt><dd><p>Set small, achievable milestones, visually progress, join study groups for accountability, and connect with other career changers. Our motivation toolkit includes specific strategies for maintaining momentum during career transitions.</p>
</dd>
</dl>
</section>
<section id="job-search-strategy">
<h2>🎯 <strong>Job Search Strategy</strong><a class="headerlink" href="#job-search-strategy" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>16. What entry-level cybersecurity roles should I target?</strong></dt><dd><p><strong>Security Analyst</strong>, <strong>Compliance Analyst</strong>, <strong>IT Auditor</strong>, <strong>Security Awareness Coordinator</strong>, or <strong>Junior Consultant</strong> roles. Focus on positions that value your background. Our job targeting guide matches roles to your experience.</p>
</dd>
<dt><strong>17. How do I explain my career change to potential employers?</strong></dt><dd><p>Emphasise transferable skills, show genuine passion for cybersecurity, demonstrate commitment through certifications and learning, and explain how your unique background adds value. Our interview preparation guide provides specific talking points.</p>
</dd>
<dt><strong>18. Should I consider contract or consulting work initially?</strong></dt><dd><p>Yes! Contract work provides experience, networking opportunities, and skill development. It’s often easier to get contract positions and can lead to permanent roles. Our contracting guide covers getting started.</p>
</dd>
<dt><strong>19. What salary expectations should I have?</strong></dt><dd><p>Entry-level cybersecurity roles typically pay $50,000-$80,000, varying by location and background. Your previous experience may command higher starting salaries. Our salary guide provides regional and role-specific data.</p>
</dd>
<dt><strong>20. How do I address the experience gap in job applications?</strong></dt><dd><p>Emphasise relevant projects, certifications, lab work, and transferable skills. Consider entry-level positions or roles that specifically welcome career changers. Our application strategy guide provides specific approaches.</p>
</dd>
</dl>
</section>
<section id="technical-development">
<h2>🔧 <strong>Technical Development</strong><a class="headerlink" href="#technical-development" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>21. How technical do I need to become?</strong></dt><dd><p>Depends on your target role. <strong>GRC roles</strong> need less technical depth, <strong>analyst roles</strong> need moderate technical skills, <strong>engineering roles</strong> require deep technical knowledge. Our technical requirements guide maps skills to roles.</p>
</dd>
<dt><strong>22. What programming languages should I learn?</strong></dt><dd><p><strong>Python</strong> is most valuable for automation and analysis. <strong>PowerShell</strong> for Windows environments. <strong>SQL</strong> for database queries. <strong>Bash</strong> for Linux systems. Start with Python and add others based on your target role.</p>
</dd>
<dt><strong>23. How do I build a portfolio to demonstrate my skills?</strong></dt><dd><p>Document lab projects, create security assessments, write blog posts about your learning, contribute to open source projects, and showcase certifications. Our portfolio guide provides templates and examples.</p>
</dd>
<dt><strong>24. What tools should I become familiar with?</strong></dt><dd><p>Start with <strong>Wireshark</strong> (network analysis), <strong>Nmap</strong> (network scanning), <strong>Metasploit</strong> (penetration testing), and <strong>Splunk</strong> (log analysis). Our tool learning path prioritizes based on your target role.</p>
</dd>
<dt><strong>25. How do I keep up with the rapidly changing cybersecurity landscape?</strong></dt><dd><p>Follow security blogs and podcasts, join professional associations, attend webinars and conferences, and participate in online communities. Our continuous learning guide provides curated resources for staying current.</p>
</dd>
</dl>
</section>
<section id="career-changer-resources">
<h2>🎯 <strong>Career Changer Resources</strong><a class="headerlink" href="#career-changer-resources" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>🔄 Transition Planning</strong></dt><dd><ul class="simple">
<li><p>Skills assessment tools</p></li>
<li><p>Career pathway mapping</p></li>
<li><p>Timeline planning guides</p></li>
<li><p>Financial transition planning</p></li>
</ul>
</dd>
<dt><strong>📚 Accelerated Learning</strong></dt><dd><ul class="simple">
<li><p>Career changer study programs</p></li>
<li><p>Intensive bootcamp options</p></li>
<li><p>Mentorship matching</p></li>
<li><p>Peer support groups</p></li>
</ul>
</dd>
<dt><strong>💼 Job Search Support</strong></dt><dd><ul class="simple">
<li><p>Resume transformation services</p></li>
<li><p>Interview preparation</p></li>
<li><p>Networking event calendar</p></li>
<li><p>Job placement assistance</p></li>
</ul>
</dd>
<dt><strong>🤝 Community Support</strong></dt><dd><ul class="simple">
<li><p>Career changer forums</p></li>
<li><p>Success story sharing</p></li>
<li><p>Mentor connections</p></li>
<li><p>Peer accountability groups</p></li>
</ul>
</dd>
</dl>
<p>—</p>
<p><em>Need more help? Check out our :doc:`../guides/user_guide` or contact career transition support at careers&#64;certpathfinder.com</em></p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="enterprise_admins.html" class="btn btn-neutral float-left" title="🏢 Enterprise Administrators FAQ" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="academics_researchers.html" class="btn btn-neutral float-right" title="🎓 Academics &amp; Researchers FAQ" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>