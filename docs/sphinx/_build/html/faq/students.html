<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>👨‍🎓 Students &amp; Learners FAQ &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/faq/students.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="👩‍💼 Working Professionals FAQ" href="professionals.html" />
    <link rel="prev" title="❓ Frequently Asked Questions" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">❓ Frequently Asked Questions</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">👨‍🎓 Students &amp; Learners FAQ</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#getting-started">🚀 <strong>Getting Started</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#study-strategies">📚 <strong>Study Strategies</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#career-planning">🎯 <strong>Career Planning</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#financial-planning">💰 <strong>Financial Planning</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#technical-questions">🔧 <strong>Technical Questions</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#additional-resources-for-students">📞 <strong>Additional Resources for Students</strong></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="professionals.html">👩‍💼 Working Professionals FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="managers.html">👨‍💼 Managers &amp; Team Leads FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html">🔧 System Administrators FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise_admins.html">🏢 Enterprise Administrators FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_changers.html">🔄 Career Changers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="academics_researchers.html">🎓 Academics &amp; Researchers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="training_managers.html">📚 Corporate Training Managers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#choose-your-user-type">🎯 <strong>Choose Your User Type</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-topics-across-all-user-types">🔍 <strong>Common Topics Across All User Types</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#still-need-help">📞 <strong>Still Need Help?</strong></a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">❓ Frequently Asked Questions</a></li>
      <li class="breadcrumb-item active">👨‍🎓 Students &amp; Learners FAQ</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/faq/students.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="students-learners-faq">
<h1>👨‍🎓 Students &amp; Learners FAQ<a class="headerlink" href="#students-learners-faq" title="Link to this heading"></a></h1>
<p><strong>25 Essential Questions for Students and Entry-Level Learners</strong></p>
<p>This FAQ section is designed for students, recent graduates, and entry-level professionals who are new to cybersecurity and looking to start their certification journey.</p>
<section id="getting-started">
<h2>🚀 <strong>Getting Started</strong><a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>1. I’m completely new to cybersecurity. Where should I start?</strong></dt><dd><p>Start with our AI-powered career assessment. Input your current skills and interests, and our system will recommend entry-level certifications like CompTIA Security+ or (ISC)² Systems Security Certified Practitioner (SSCP). Use the “Entry Level” filter in the certification explorer to see beginner-friendly options.</p>
</dd>
<dt><strong>2. What’s the difference between vendor-neutral and vendor-specific certifications?</strong></dt><dd><p>Vendor-neutral certifications (like CompTIA Security+, CISSP) cover general cybersecurity principles applicable across all technologies. Vendor-specific certifications (like Cisco CCNA Security, Microsoft Azure Security) focus on specific products or platforms. As a beginner, start with vendor-neutral certifications for broader knowledge.</p>
</dd>
<dt><strong>3. How much does it typically cost to get my first certification?</strong></dt><dd><p>Entry-level certifications typically cost $300-$500 for the exam, plus $200-$800 for study materials. Use our cost calculator to get precise estimates including retake scenarios. Many students spend $500-$1,200 total for their first certification including materials and potential retakes.</p>
</dd>
<dt><strong>4. How long should I study for my first certification?</strong></dt><dd><p>For entry-level certifications, plan 2-4 months of study with 10-15 hours per week. Our study time estimator considers your background and learning style to provide personalized timelines. Students with no IT background typically need 3-4 months, while those with some technical experience may need 2-3 months.</p>
</dd>
<dt><strong>5. Can I get certified while still in school?</strong></dt><dd><p>Absolutely! Many certifications have no formal prerequisites. CompTIA Security+ is popular among students and can be earned alongside your degree. Some certifications like CISSP require work experience, but you can take the exam and become an Associate until you gain the required experience.</p>
</dd>
</dl>
</section>
<section id="study-strategies">
<h2>📚 <strong>Study Strategies</strong><a class="headerlink" href="#study-strategies" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>6. What study materials should I use?</strong></dt><dd><p>Our platform recommends materials based on your learning style. Visual learners benefit from video courses (CBT Nuggets, Cybrary), whilst reading-focused learners prefer books (Official Cert Guide series). Hands-on learners should prioritise lab environments and practise tests. Always combine multiple resource types.</p>
</dd>
<dt><strong>7. How important are practise tests?</strong></dt><dd><p>Extremely important! Practise tests help you understand the exam format and identify knowledge gaps. Aim for consistently scoring 85%+ on practise tests before taking the real exam. Our AI assistant adjusts study recommendations accordingly.</p>
</dd>
<dt><strong>8. Should I join study groups?</strong></dt><dd><p>Yes! Study groups provide motivation, different perspectives, and help clarify difficult concepts. Our platform can connect you with other students preparing for the same certification. Online communities like Reddit’s r/CompTIA and Discord study groups are also valuable.</p>
</dd>
<dt><strong>9. How do I create an effective study schedule?</strong></dt><dd><p>Use our study planner to create a realistic schedule. Break study sessions into 45-60 minute blocks with breaks. Focus on one domain at a time, review regularly, and increase practise test frequency as your exam date approaches. Consistency is more important than long study sessions.</p>
</dd>
<dt><strong>10. What if I fail my first attempt?</strong></dt><dd><p>Don’t be discouraged! Many successful professionals failed their first attempt. Analyze your score report to identify weak areas, adjust your study plan, and retake when ready. Our cost calculator includes retake scenarios to help you budget appropriately.</p>
</dd>
</dl>
</section>
<section id="career-planning">
<h2>🎯 <strong>Career Planning</strong><a class="headerlink" href="#career-planning" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>11. Which certification should I get first?</strong></dt><dd><p>For most students, CompTIA Security+ is the best starting point. It’s widely recognized, covers fundamental concepts, and meets DoD 8570 requirements for many government positions. Our AI career advisor can provide personalized recommendations based on your interests and career goals.</p>
</dd>
<dt><strong>12. How do certifications help with job searching?</strong></dt><dd><p>Certifications demonstrate commitment and validate your knowledge to employers. Many entry-level positions require or prefer specific certifications. They help you stand out in a competitive job market and often lead to higher starting salaries. Use our job market insights to see which certifications are most valued in your area.</p>
</dd>
<dt><strong>13. Should I specialise early or stay general?</strong></dt><dd><p>Start with foundational, general certifications to build a broad knowledge base. Specialisation comes later as you gain experience and discover your interests. Our career path visualiser shows how general certifications lead to specialised tracks in areas like cloud security, penetration testing, or governance.</p>
</dd>
<dt><strong>14. How do I choose between different certification tracks?</strong></dt><dd><p>Consider your interests, local job market, and long-term goals. Use our domain explorer to understand different cybersecurity areas. Talk to professionals in your target field and review job postings to see which certifications are most requested.</p>
</dd>
<dt><strong>15. What’s the typical career progression for someone starting out?</strong></dt><dd><p>Common progression: Entry-level cert (Security+) → Junior role → Intermediate cert (CySA+, GSEC) → Mid-level role → Advanced cert (CISSP, CISM) → Senior role. Our career transition planner shows detailed pathways with timeline estimates and skill requirements.</p>
</dd>
</dl>
</section>
<section id="financial-planning">
<h2>💰 <strong>Financial Planning</strong><a class="headerlink" href="#financial-planning" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>16. Are there student discounts available?</strong></dt><dd><p>Many certification providers offer student discounts. CompTIA provides academic pricing, (ISC)² has student memberships, and some vendors offer free vouchers through academic programs. Check with your school’s career services and our cost calculator for current discount information.</p>
</dd>
<dt><strong>17. How can I minimize certification costs?</strong></dt><dd><p>Use free study materials (Professor Messer, Cybrary), join study groups to share resources, look for employer sponsorship opportunities, and consider certification bootcamps that include exam vouchers. Our cost optimizer suggests the most economical study approaches.</p>
</dd>
<dt><strong>18. Is it worth paying for expensive training courses?</strong></dt><dd><p>It depends on your learning style and budget. Self-study with books and free resources can be effective for motivated learners. Paid courses provide structure and support but aren’t always necessary. Our learning style assessment helps determine the most cost-effective approach for you.</p>
</dd>
<dt><strong>19. Should I get multiple certifications quickly or focus on one?</strong></dt><dd><p>Focus on one certification at a time, especially as a beginner. Master the material thoroughly rather than rushing through multiple certifications. Quality over quantity leads to better job prospects and deeper understanding.</p>
</dd>
<dt><strong>20. How do I budget for ongoing certification maintenance?</strong></dt><dd><p>Plan for continuing education requirements and renewal fees. Most certifications require 20-40 hours of continuing education annually and renewal fees of $50-$200. Our maintenance tracker helps you plan and budget for these ongoing costs.</p>
</dd>
</dl>
</section>
<section id="technical-questions">
<h2>🔧 <strong>Technical Questions</strong><a class="headerlink" href="#technical-questions" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>21. Do I need hands-on experience before getting certified?</strong></dt><dd><p>While not required for most entry-level certifications, hands-on experience is valuable. Set up home labs, use virtual machines, and practice with free tools. Many concepts become clearer when you can apply them practically.</p>
</dd>
<dt><strong>22. How do I set up a home lab for studying?</strong></dt><dd><p>Start with virtualisation software (VMware, VirtualBox) and download free operating systems and security tools. Our lab setup guides provide step-by-step instructions for creating practise environments relevant to your target certification.</p>
</dd>
<dt><strong>23. What programming skills do I need?</strong></dt><dd><p>Basic scripting knowledge (Python, PowerShell) is increasingly valuable but not required for entry-level certifications. Focus on understanding concepts first, then add technical skills as you progress. Our skill gap analysis identifies which technical skills to prioritize.</p>
</dd>
<dt><strong>24. How important is networking knowledge for cybersecurity?</strong></dt><dd><p>Very important! Understanding how networks function is fundamental to cybersecurity. If you’re weak in networking, consider CompTIA Network+ before Security+, or spend extra time on networking topics during your security studies.</p>
</dd>
<dt><strong>25. Should I learn specific tools or focus on concepts?</strong></dt><dd><p>Focus on concepts first, then learn tools. Understanding the “why” behind security practises is more valuable than memorising specific tool interfaces. Tools change frequently, but fundamental concepts remain consistent. Our curriculum balances conceptual knowledge with practical tool experience.</p>
</dd>
</dl>
</section>
<section id="additional-resources-for-students">
<h2>📞 <strong>Additional Resources for Students</strong><a class="headerlink" href="#additional-resources-for-students" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>🎓 Academic Support</strong></dt><dd><ul class="simple">
<li><p>University career services integration</p></li>
<li><p>Student organization partnerships</p></li>
<li><p>Academic advisor consultation guides</p></li>
</ul>
</dd>
<dt><strong>💼 Internship Preparation</strong></dt><dd><ul class="simple">
<li><p>Resume building with certifications</p></li>
<li><p>Interview preparation resources</p></li>
<li><p>Networking event recommendations</p></li>
</ul>
</dd>
<dt><strong>🔗 Professional Development</strong></dt><dd><ul class="simple">
<li><p>LinkedIn profile optimization</p></li>
<li><p>Professional association memberships</p></li>
<li><p>Mentorship program connections</p></li>
</ul>
</dd>
<dt><strong>📱 Mobile Learning</strong></dt><dd><ul class="simple">
<li><p>Study apps and flashcards</p></li>
<li><p>Mobile-friendly practise tests</p></li>
<li><p>Offline study capabilities</p></li>
</ul>
</dd>
</dl>
<p>—</p>
<p><em>Need more help? Check out our :doc:`../guides/user_guide` or contact student support at students&#64;certpathfinder.com</em></p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="❓ Frequently Asked Questions" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="professionals.html" class="btn btn-neutral float-right" title="👩‍💼 Working Professionals FAQ" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>