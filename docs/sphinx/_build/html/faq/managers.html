<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>👨‍💼 Managers &amp; Team Leads FAQ &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/faq/managers.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="🔧 System Administrators FAQ" href="administrators.html" />
    <link rel="prev" title="👩‍💼 Working Professionals FAQ" href="professionals.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">❓ Frequently Asked Questions</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="students.html">👨‍🎓 Students &amp; Learners FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="professionals.html">👩‍💼 Working Professionals FAQ</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">👨‍💼 Managers &amp; Team Leads FAQ</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#team-development">🎯 <strong>Team Development</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-management">📊 <strong>Performance Management</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#budget-planning">💰 <strong>Budget Planning</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#technical-leadership">🔧 <strong>Technical Leadership</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#team-dynamics">👥 <strong>Team Dynamics</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#strategic-planning">📈 <strong>Strategic Planning</strong></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html">🔧 System Administrators FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise_admins.html">🏢 Enterprise Administrators FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_changers.html">🔄 Career Changers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="academics_researchers.html">🎓 Academics &amp; Researchers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="training_managers.html">📚 Corporate Training Managers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#choose-your-user-type">🎯 <strong>Choose Your User Type</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-topics-across-all-user-types">🔍 <strong>Common Topics Across All User Types</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#still-need-help">📞 <strong>Still Need Help?</strong></a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">❓ Frequently Asked Questions</a></li>
      <li class="breadcrumb-item active">👨‍💼 Managers &amp; Team Leads FAQ</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/faq/managers.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="managers-team-leads-faq">
<h1>👨‍💼 Managers &amp; Team Leads FAQ<a class="headerlink" href="#managers-team-leads-faq" title="Link to this heading"></a></h1>
<p><strong>25 Essential Questions for Managers and Team Leaders</strong></p>
<p>This FAQ section addresses the unique challenges faced by cybersecurity managers, team leads, and department heads who need to balance their own professional development with team management responsibilities.</p>
<section id="team-development">
<h2>🎯 <strong>Team Development</strong><a class="headerlink" href="#team-development" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>1. How do I create a certification roadmap for my team?</strong></dt><dd><p>Use our team planning tools to assess current skills, identify gaps, and create individualized certification paths. Consider business needs, budget constraints, and career aspirations. Our team roadmap generator creates visual plans showing certification timelines and dependencies across team members.</p>
</dd>
<dt><strong>2. What’s the best way to prioritise team certifications with limited budget?</strong></dt><dd><p>Focus on certifications that provide maximum business value: compliance requirements, client demands, and critical skill gaps. Use our ROI calculator to compare certification investments. Consider group training discounts and stagger certifications to spread costs across budget cycles.</p>
</dd>
<dt><strong>3. How do I motivate team members to pursue certifications?</strong></dt><dd><p>Connect certifications to career advancement, provide study time during work hours, offer financial incentives or bonuses, and celebrate achievements publicly. Create a team certification leaderboard and recognition program. Our motivation toolkit provides proven strategies for encouraging professional development.</p>
</dd>
<dt><strong>4. Should I require specific certifications for my team?</strong></dt><dd><p>Consider making certifications requirements for new hires rather than existing staff. For current team members, tie certifications to role progression and provide support to achieve them. Mandatory certifications can create resentment if not handled carefully.</p>
</dd>
<dt><strong>5. How do I handle team members who resist getting certified?</strong></dt><dd><p>Understand their concerns (time, difficulty, relevance), address barriers, and show clear benefits. Some may prefer alternative professional development. Focus on business needs while respecting individual preferences. Document expectations clearly for performance management.</p>
</dd>
</dl>
</section>
<section id="performance-management">
<h2>📊 <strong>Performance Management</strong><a class="headerlink" href="#performance-management" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>6. How do I measure the ROI of team certification investments?</strong></dt><dd><p>Metrics like improved incident response times, reduced security incidents, client satisfaction scores, and team retention rates. Use our analytics dashboard to correlate certification achievements with performance improvements. Include both quantitative and qualitative benefits in your analysis.</p>
</dd>
<dt><strong>7. What’s the best way to evaluate certification progress during reviews?</strong></dt><dd><p>Include certification goals in individual development plans, progress quarterly, and tie achievements to performance ratings. Use our progress tools to monitor study time and milestone completion. Recognise effort as well as achievement.</p>
</dd>
<dt><strong>8. How do I balance individual career goals with team needs?</strong></dt><dd><p>Align individual aspirations with business objectives where possible. If someone wants to specialize in an area the team needs, support it fully. For misaligned goals, discuss alternatives or transition planning. Our career alignment tool helps identify win-win scenarios.</p>
</dd>
<dt><strong>9. Should certification achievements affect salary or promotion decisions?</strong></dt><dd><p>Yes, but establish clear criteria upfront. Certifications should be one factor among many, including performance, leadership, and business impact. Use our compensation benchmarking data to ensure fair and competitive adjustments.</p>
</dd>
<dt><strong>10. How do I handle team members who achieve certifications but don’t apply the knowledge?</strong></dt><dd><p>Set clear expectations about applying new skills, provide opportunities to use new knowledge, and include practical application in performance goals. Consider whether the certification was the right choice or if additional support is needed.</p>
</dd>
</dl>
</section>
<section id="budget-planning">
<h2>💰 <strong>Budget Planning</strong><a class="headerlink" href="#budget-planning" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>11. How much should I budget annually for team certifications?</strong></dt><dd><p>Plan $2,000-$5,000 per team member annually, including exams, study materials, training, and time costs. Use our budget calculator to get precise estimates based on your team’s certification goals. Consider multi-year planning for expensive certifications.</p>
</dd>
<dt><strong>12. What’s the most cost-effective way to train multiple team members?</strong></dt><dd><p>Group training sessions, shared study materials, internal knowledge sharing, and bulk exam voucher purchases can reduce costs. Our group training optimiser suggests the most economical approaches for your team size and certification mix.</p>
</dd>
<dt><strong>13. How do I justify certification budgets to upper management?</strong></dt><dd><p>Present business cases showing risk reduction, compliance benefits, competitive advantages, and retention value. Use our executive briefing templates and ROI calculators to build compelling presentations. Include industry benchmarks and competitor analysis.</p>
</dd>
<dt><strong>14. Should I pay for certification maintenance and renewals?</strong></dt><dd><p>Yes, maintaining certifications protects your investment and ensures continued value. Budget for renewal fees and continuing education requirements. Our maintenance tracker helps plan these ongoing costs across your team.</p>
</dd>
<dt><strong>15. How do I handle budget constraints whilst maintaining team development?</strong></dt><dd><p>Prioritise high-impact certifications, use free study resources, leverage vendor training credits, and consider spreading certifications across multiple budget cycles. Our cost optimisation tools help maximise value within budget constraints.</p>
</dd>
</dl>
</section>
<section id="technical-leadership">
<h2>🔧 <strong>Technical Leadership</strong><a class="headerlink" href="#technical-leadership" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>16. How do I stay technically current whilst managing a team?</strong></dt><dd><p>Allocate time for your own learning, attend management-focused technical conferences, participate in vendor briefings, and learn from your team’s certification studies. Our executive learning paths provide efficient ways to maintain technical awareness.</p>
</dd>
<dt><strong>17. What certifications should I pursue as a manager?</strong></dt><dd><p>Focus on management-oriented certifications like CISSP, CISM, CRISC, or CGEIT. These demonstrate strategic thinking and are valued for leadership roles. Our management track shows progression paths for different leadership levels.</p>
</dd>
<dt><strong>18. How do I evaluate the technical content of certifications for my team?</strong></dt><dd><p>Review certification blueprints, consult with technical leads, and use our certification comparison tools. Consider bringing in subject matter experts to assess relevance. Our technical evaluation framework helps non-technical managers make informed decisions.</p>
</dd>
<dt><strong>19. Should I require hands-on vs. theoretical certifications?</strong></dt><dd><p>Balance both based on team roles. Technical team members need hands-on certifications, while those moving toward management benefit from theoretical ones. Our role-based certification matrix provides guidance for different positions.</p>
</dd>
<dt><strong>20. How do I ensure certifications align with our technology stack?</strong></dt><dd><p>Map certifications to your current and planned technologies. Consider vendor-specific certifications for critical systems and vendor-neutral ones for foundational knowledge. Our technology alignment tool helps match certifications to your environment.</p>
</dd>
</dl>
</section>
<section id="team-dynamics">
<h2>👥 <strong>Team Dynamics</strong><a class="headerlink" href="#team-dynamics" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>21. How do I handle competition between team members for certifications?</strong></dt><dd><p>Channel competition positively through team challenges and recognition programmes. Ensure fair access to training opportunities and avoid favouritism. Our team collaboration tools help create supportive rather than destructive competition.</p>
</dd>
<dt><strong>22. What if a certified team member wants to leave for a better opportunity?</strong></dt><dd><p>View this as a success - you’ve developed talent that’s in demand. Maintain good relationships for potential future collaboration. Use our retention strategies to keep valuable team members whilst accepting that some movement is natural.</p>
</dd>
<dt><strong>23. How do I integrate new certified team members effectively?</strong></dt><dd><p>Create onboarding plans that leverage their new knowledge, assign mentorship roles, and provide opportunities to share learning with the team. Our integration toolkit helps maximize the value of newly certified team members.</p>
</dd>
<dt><strong>24. Should I create internal certification study groups?</strong></dt><dd><p>Yes! Study groups improve success rates, build team cohesion, and reduce individual study time. Provide meeting space and time, and consider bringing in external instructors. Our study group management guide provides best practises.</p>
</dd>
<dt><strong>25. How do I handle team members who fail certification exams?</strong></dt><dd><p>Provide emotional support, analyze what went wrong, adjust study approaches, and offer additional resources. Failure is often a learning opportunity. Our failure analysis tools help identify improvement strategies and prevent future issues.</p>
</dd>
</dl>
</section>
<section id="strategic-planning">
<h2>📈 <strong>Strategic Planning</strong><a class="headerlink" href="#strategic-planning" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>🎯 Leadership Development</strong></dt><dd><ul class="simple">
<li><p>Management certification tracks</p></li>
<li><p>Executive briefing materials</p></li>
<li><p>Strategic planning templates</p></li>
<li><p>Leadership assessment tools</p></li>
</ul>
</dd>
<dt><strong>📊 Team Analytics</strong></dt><dd><ul class="simple">
<li><p>Performance correlation analysis</p></li>
<li><p>Skill gap identification</p></li>
<li><p>Certification impact measurement</p></li>
<li><p>Team development metrics</p></li>
</ul>
</dd>
<dt><strong>💼 Business Alignment</strong></dt><dd><ul class="simple">
<li><p>ROI calculation tools</p></li>
<li><p>Budget planning templates</p></li>
<li><p>Executive presentation materials</p></li>
<li><p>Competitive analysis reports</p></li>
</ul>
</dd>
<dt><strong>🤝 Stakeholder Management</strong></dt><dd><ul class="simple">
<li><p>Upper management reporting</p></li>
<li><p>Client communication templates</p></li>
<li><p>Vendor relationship management</p></li>
<li><p>Cross-team collaboration tools</p></li>
</ul>
</dd>
</dl>
<p>—</p>
<p><em>Need more help? Check out our :doc:`../guides/admin_guide` or contact management support at managers&#64;certpathfinder.com</em></p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="professionals.html" class="btn btn-neutral float-left" title="👩‍💼 Working Professionals FAQ" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="administrators.html" class="btn btn-neutral float-right" title="🔧 System Administrators FAQ" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>