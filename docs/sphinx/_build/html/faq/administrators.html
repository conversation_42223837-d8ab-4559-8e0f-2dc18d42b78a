<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>🔧 System Administrators FAQ &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/faq/administrators.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="🏢 Enterprise Administrators FAQ" href="enterprise_admins.html" />
    <link rel="prev" title="👨‍💼 Managers &amp; Team Leads FAQ" href="managers.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">❓ Frequently Asked Questions</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="students.html">👨‍🎓 Students &amp; Learners FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="professionals.html">👩‍💼 Working Professionals FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="managers.html">👨‍💼 Managers &amp; Team Leads FAQ</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">🔧 System Administrators FAQ</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#installation-setup">🚀 <strong>Installation &amp; Setup</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#configuration-management">🔧 <strong>Configuration Management</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#database-management">🗄️ <strong>Database Management</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#security-compliance">🔒 <strong>Security &amp; Compliance</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#monitoring-performance">📊 <strong>Monitoring &amp; Performance</strong></a></li>
<li class="toctree-l3"><a class="reference internal" href="#administrative-tools">🛠️ <strong>Administrative Tools</strong></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="enterprise_admins.html">🏢 Enterprise Administrators FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_changers.html">🔄 Career Changers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="academics_researchers.html">🎓 Academics &amp; Researchers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="training_managers.html">📚 Corporate Training Managers FAQ</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#choose-your-user-type">🎯 <strong>Choose Your User Type</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-topics-across-all-user-types">🔍 <strong>Common Topics Across All User Types</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#still-need-help">📞 <strong>Still Need Help?</strong></a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">❓ Frequently Asked Questions</a></li>
      <li class="breadcrumb-item active">🔧 System Administrators FAQ</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/faq/administrators.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="system-administrators-faq">
<h1>🔧 System Administrators FAQ<a class="headerlink" href="#system-administrators-faq" title="Link to this heading"></a></h1>
<p><strong>25 Essential Questions for Platform Administrators</strong></p>
<p>This FAQ section provides technical guidance for system administrators responsible for deploying, configuring, and maintaining the CertPathFinder platform.</p>
<section id="installation-setup">
<h2>🚀 <strong>Installation &amp; Setup</strong><a class="headerlink" href="#installation-setup" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>1. What are the minimum system requirements for CertPathFinder?</strong></dt><dd><p><strong>Production Environment:</strong> 4 CPU cores, 8GB RAM, 100GB storage, PostgreSQL 12+, Redis 6+, Python 3.9+, Node.js 16+. <strong>Development:</strong> 2 CPU cores, 4GB RAM, 50GB storage. Use our system requirements calculator for specific deployment scenarios.</p>
</dd>
<dt><strong>2. How do I perform a fresh installation of CertPathFinder?</strong></dt><dd><p>Follow our installation guide: 1) Install dependencies, 2) Clone repository, 3) Configure environment variables, 4) Initialize database, 5) Build frontend, 6) Start services. Our automated installer script handles most steps. See <a class="reference internal" href="../installation.html"><span class="doc">Installation Guide</span></a> for detailed instructions.</p>
</dd>
<dt><strong>3. What’s the recommended deployment architecture?</strong></dt><dd><p><strong>Small deployment:</strong> Single server with Docker Compose. <strong>Medium:</strong> Separate database server, load balancer, and application servers. <strong>Large:</strong> Kubernetes cluster with auto-scaling, separate Redis cluster, and CDN. Our architecture guide provides detailed diagrams.</p>
</dd>
<dt><strong>4. How do I configure SSL/TLS certificates?</strong></dt><dd><p>Use Let’s Encrypt for automatic certificate management, or configure custom certificates in the web server. Update the <cite>SSL_CERT_PATH</cite> and <cite>SSL_KEY_PATH</cite> environment variables. Our SSL configuration guide covers common scenarios including wildcard certificates.</p>
</dd>
<dt><strong>5. What environment variables need to be configured?</strong></dt><dd><p>Essential variables: <cite>DATABASE_URL</cite>, <cite>REDIS_URL</cite>, <cite>SECRET_KEY</cite>, <cite>AI_MODEL_PATH</cite>, <cite>FRONTEND_URL</cite>. Optional: <cite>SMTP_*</cite> for email, <cite>OAUTH_*</cite> for authentication, <cite>MONITORING_*</cite> for observability. See our configuration reference for complete list.</p>
</dd>
</dl>
</section>
<section id="configuration-management">
<h2>🔧 <strong>Configuration Management</strong><a class="headerlink" href="#configuration-management" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>6. How do I configure the AI models for on-device processing?</strong></dt><dd><p>Download pre-trained models to the <cite>AI_MODEL_PATH</cite> directory, ensure sufficient disk space (2-5GB per model), and configure model parameters in <cite>ai_config.yaml</cite>. Our AI setup guide provides step-by-step instructions for model deployment.</p>
</dd>
<dt><strong>7. What’s the best way to manage configuration across environments?</strong></dt><dd><p>Use environment-specific configuration files, Docker secrets for sensitive data, and configuration management tools like Ansible or Terraform. Our configuration templates provide starting points for different environments.</p>
</dd>
<dt><strong>8. How do I configure email notifications?</strong></dt><dd><p>Set SMTP configuration in environment variables: <cite>SMTP_HOST</cite>, <cite>SMTP_PORT</cite>, <cite>SMTP_USER</cite>, <cite>SMTP_PASSWORD</cite>, <cite>SMTP_TLS</cite>. Test with the built-in email test utility. Our email configuration guide covers popular providers.</p>
</dd>
<dt><strong>9. How do I set up OAuth authentication providers?</strong></dt><dd><p>Register applications with providers (GitHub, Google, Microsoft), configure OAuth settings in the admin panel, and set environment variables for client IDs and secrets. Our OAuth setup guide covers each provider.</p>
</dd>
<dt><strong>10. What logging configuration is recommended?</strong></dt><dd><p>Configure log levels, rotation policies, and centralised logging. Use structured logging (JSON) for production. Set <cite>LOG_LEVEL=INFO</cite> for production, <cite>DEBUG</cite> for development. Our logging guide covers ELK stack integration.</p>
</dd>
</dl>
</section>
<section id="database-management">
<h2>🗄️ <strong>Database Management</strong><a class="headerlink" href="#database-management" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>11. How do I perform database backups?</strong></dt><dd><p>Use <cite>pg_dump</cite> for PostgreSQL backups, schedule automated backups with cron, and test restore procedures regularly. Our backup script automates the process and includes verification. Store backups securely with encryption.</p>
</dd>
<dt><strong>12. What’s the recommended database maintenance schedule?</strong></dt><dd><p><strong>Daily:</strong> Automated backups, log rotation. <strong>Weekly:</strong> VACUUM ANALYZE, index maintenance. <strong>Monthly:</strong> Full backup verification, performance review. <strong>Quarterly:</strong> Capacity planning, upgrade evaluation. Our maintenance checklist provides detailed procedures.</p>
</dd>
<dt><strong>13. How do I migrate data between environments?</strong></dt><dd><p>Use our migration scripts to export/import data safely. Sanitise production data for development environments. Our data migration guide covers schema updates, data transformation, and validation procedures.</p>
</dd>
<dt><strong>14. How do I monitor database performance?</strong></dt><dd><p>Monitor query performance, connection counts, disk usage, and slow queries. Use tools like pgAdmin, DataDog, or our built-in monitoring dashboard. Set up alerts for critical metrics.</p>
</dd>
<dt><strong>15. What’s the database scaling strategy?</strong></dt><dd><p>Start with vertical scaling (more CPU/RAM), implement read replicas for read-heavy workloads, consider partitioning for large datasets, and evaluate sharding for extreme scale. Our scaling guide provides implementation details.</p>
</dd>
</dl>
</section>
<section id="security-compliance">
<h2>🔒 <strong>Security &amp; Compliance</strong><a class="headerlink" href="#security-compliance" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>16. How do I secure the CertPathFinder installation?</strong></dt><dd><p>Enable HTTPS, configure firewall rules, use strong passwords, enable audit logging, keep software updated, and follow security hardening guides. Our security checklist covers all essential measures.</p>
</dd>
<dt><strong>17. What audit logging is available?</strong></dt><dd><p>All user actions, admin operations, data changes, and system events are logged. Configure audit log retention, secure storage, and automated analysis. Our audit guide covers compliance requirements.</p>
</dd>
<dt><strong>18. How do I configure role-based access control?</strong></dt><dd><p>Define roles in the admin panel, assign permissions, and map users to roles. Use groups for easier management. Our RBAC guide provides best practises for different organisational structures.</p>
</dd>
<dt><strong>19. What compliance features are available?</strong></dt><dd><p>GDPR data export/deletion, SOC 2 audit trails, FERPA student privacy controls, and HIPAA-compatible configurations. Our compliance guide covers each standard’s requirements.</p>
</dd>
<dt><strong>20. How do I handle security updates?</strong></dt><dd><p>Subscribe to security notifications, test updates in staging, schedule maintenance windows, and maintain rollback procedures. Our update process ensures minimal downtime and maximum security.</p>
</dd>
</dl>
</section>
<section id="monitoring-performance">
<h2>📊 <strong>Monitoring &amp; Performance</strong><a class="headerlink" href="#monitoring-performance" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>21. What monitoring tools are integrated?</strong></dt><dd><p>Built-in health checks, Prometheus metrics, Grafana dashboards, and alerting rules. Integration with external tools like DataDog, New Relic, and Splunk. Our monitoring guide covers setup and configuration.</p>
</dd>
<dt><strong>22. How do I troubleshoot performance issues?</strong></dt><dd><p>Use our performance diagnostic tools, check database query performance, monitor resource usage, and analyse application logs. Our troubleshooting guide provides systematic approaches to common issues.</p>
</dd>
<dt><strong>23. What are the key metrics to monitor?</strong></dt><dd><p>Response times, error rates, database performance, AI model inference times, user session metrics, and resource utilization. Our metrics guide defines thresholds and alerting rules.</p>
</dd>
<dt><strong>24. How do I set up automated alerting?</strong></dt><dd><p>Configure alert rules for critical metrics, set up notification channels (email, Slack, PagerDuty), and define escalation procedures. Our alerting guide provides templates for common scenarios.</p>
</dd>
<dt><strong>25. What’s the disaster recovery procedure?</strong></dt><dd><p>Maintain current backups, document recovery procedures, test recovery regularly, and maintain standby systems for critical deployments. Our DR guide provides detailed runbooks and automation scripts.</p>
</dd>
</dl>
</section>
<section id="administrative-tools">
<h2>🛠️ <strong>Administrative Tools</strong><a class="headerlink" href="#administrative-tools" title="Link to this heading"></a></h2>
<dl class="simple">
<dt><strong>🔧 System Management</strong></dt><dd><ul class="simple">
<li><p>Automated installation scripts</p></li>
<li><p>Configuration management templates</p></li>
<li><p>Health check utilities</p></li>
<li><p>Performance diagnostic tools</p></li>
</ul>
</dd>
<dt><strong>📊 Monitoring Dashboards</strong></dt><dd><ul class="simple">
<li><p>System performance metrics</p></li>
<li><p>User activity analytics</p></li>
<li><p>Error tracking and alerting</p></li>
<li><p>Capacity planning reports</p></li>
</ul>
</dd>
<dt><strong>🔒 Security Tools</strong></dt><dd><ul class="simple">
<li><p>Security scanning utilities</p></li>
<li><p>Audit log analyzers</p></li>
<li><p>Compliance reporting tools</p></li>
<li><p>Vulnerability assessment scripts</p></li>
</ul>
</dd>
<dt><strong>💾 Data Management</strong></dt><dd><ul class="simple">
<li><p>Backup and restore utilities</p></li>
<li><p>Data migration scripts</p></li>
<li><p>Database maintenance tools</p></li>
<li><p>Archive management systems</p></li>
</ul>
</dd>
</dl>
<p>—</p>
<p><em>Need more help? Check out our :doc:`../guides/admin_guide` or contact technical support at admin&#64;certpathfinder.com</em></p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="managers.html" class="btn btn-neutral float-left" title="👨‍💼 Managers &amp; Team Leads FAQ" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="enterprise_admins.html" class="btn btn-neutral float-right" title="🏢 Enterprise Administrators FAQ" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>