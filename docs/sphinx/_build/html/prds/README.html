<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>PRD Documentation System &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/prds/README.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">PRD Documentation System</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/prds/README.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="prd-documentation-system">
<h1>PRD Documentation System<a class="headerlink" href="#prd-documentation-system" title="Link to this heading"></a></h1>
<p>This directory contains the Sphinx documentation for CertPathFinder’s 5-agent distributed architecture. The documentation is automatically updated based on git commit activity.</p>
<section id="agent-documentation">
<h2>📋 Agent Documentation<a class="headerlink" href="#agent-documentation" title="Link to this heading"></a></h2>
<section id="core-agents">
<h3>Core Agents<a class="headerlink" href="#core-agents" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong><a class="reference internal" href="agent-1-core-platform-engine.html"><span class="std std-doc">Agent 1: Core Platform Engine</span></a></strong></p>
<ul class="simple">
<li><p>Foundation APIs and certification database</p></li>
<li><p>User management and authentication</p></li>
<li><p>Revenue Target: $15M ARR</p></li>
<li><p>Status: 🟡 In Development</p></li>
</ul>
</li>
<li><p><strong><a class="reference internal" href="agent-2-ai-study-assistant.html"><span class="std std-doc">Agent 2: AI Study Assistant</span></a></strong></p>
<ul class="simple">
<li><p>On-device AI models for personalized learning</p></li>
<li><p>Adaptive learning paths and performance prediction</p></li>
<li><p>Revenue Target: $12M ARR</p></li>
<li><p>Status: 🟡 In Development</p></li>
</ul>
</li>
<li><p><strong><a class="reference internal" href="agent-3-enterprise-analytics-engine.html"><span class="std std-doc">Agent 3: Enterprise &amp; Analytics Engine</span></a></strong></p>
<ul class="simple">
<li><p>Multi-tenant team management and compliance</p></li>
<li><p>Data monetization through industry intelligence</p></li>
<li><p>Revenue Target: $18M ARR</p></li>
<li><p>Status: 🟡 In Development</p></li>
</ul>
</li>
<li><p><strong><a class="reference internal" href="agent-4-career-cost-intelligence.html"><span class="std std-doc">Agent 4: Career &amp; Cost Intelligence</span></a></strong></p>
<ul class="simple">
<li><p>A* pathfinding for career transitions</p></li>
<li><p>Comprehensive ROI analysis and cost modeling</p></li>
<li><p>Revenue Target: $10M ARR</p></li>
<li><p>Status: 🔴 Planning Phase</p></li>
</ul>
</li>
<li><p><strong><a class="reference internal" href="agent-5-marketplace-integration-hub.html"><span class="std std-doc">Agent 5: Marketplace &amp; Integration Hub</span></a></strong></p>
<ul class="simple">
<li><p>Partnership ecosystem with certification bodies</p></li>
<li><p>Training marketplace with commission revenue</p></li>
<li><p>Revenue Target: $12M ARR</p></li>
<li><p>Status: 🔴 Planning Phase</p></li>
</ul>
</li>
</ol>
</section>
</section>
<section id="automatic-documentation-updates">
<h2>🔄 Automatic Documentation Updates<a class="headerlink" href="#automatic-documentation-updates" title="Link to this heading"></a></h2>
<p>The documentation is automatically updated using a sophisticated monitoring system that:</p>
<section id="monitoring-features">
<h3>Monitoring Features<a class="headerlink" href="#monitoring-features" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Git Commit Monitoring</strong>: Tracks all commits for agent-related changes</p></li>
<li><p><strong>Smart Agent Detection</strong>: Identifies which agents are affected by each commit</p></li>
<li><p><strong>Automatic Status Updates</strong>: Updates documentation status based on commit activity</p></li>
<li><p><strong>Real-time Tracking</strong>: Maintains current implementation progress</p></li>
</ul>
</section>
<section id="how-it-works">
<h3>How It Works<a class="headerlink" href="#how-it-works" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Commit Analysis</strong>: The system analyzes commit messages and changed files</p></li>
<li><p><strong>Agent Mapping</strong>: Maps changes to specific agents based on keywords and file paths</p></li>
<li><p><strong>Documentation Updates</strong>: Automatically updates relevant agent documentation</p></li>
<li><p><strong>Status Tracking</strong>: Updates implementation status and progress indicators</p></li>
</ol>
</section>
<section id="monitoring-script-usage">
<h3>Monitoring Script Usage<a class="headerlink" href="#monitoring-script-usage" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check for updates once</span>
python3<span class="w"> </span>scripts/prd_docs_monitor.py<span class="w"> </span>--check

<span class="c1"># Force update all documentation</span>
python3<span class="w"> </span>scripts/prd_docs_monitor.py<span class="w"> </span>--update

<span class="c1"># Run as daemon (continuous monitoring)</span>
python3<span class="w"> </span>scripts/prd_docs_monitor.py<span class="w"> </span>--daemon

<span class="c1"># Quick wrapper script</span>
./update-prd-docs.sh
</pre></div>
</div>
</section>
<section id="agent-detection-keywords">
<h3>Agent Detection Keywords<a class="headerlink" href="#agent-detection-keywords" title="Link to this heading"></a></h3>
<p>The system uses these keywords to identify agent-related commits:</p>
<ul class="simple">
<li><p><strong>Agent 1</strong>: core, platform, foundation, api, user, auth, certification</p></li>
<li><p><strong>Agent 2</strong>: ai, ml, study, recommendation, prediction, assistant</p></li>
<li><p><strong>Agent 3</strong>: enterprise, analytics, compliance, team, organization</p></li>
<li><p><strong>Agent 4</strong>: career, cost, salary, transition, pathfinding</p></li>
<li><p><strong>Agent 5</strong>: marketplace, integration, partnership, api, hub</p></li>
</ul>
</section>
</section>
<section id="documentation-structure">
<h2>📊 Documentation Structure<a class="headerlink" href="#documentation-structure" title="Link to this heading"></a></h2>
<p>Each agent documentation includes:</p>
<section id="standard-sections">
<h3>Standard Sections<a class="headerlink" href="#standard-sections" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Executive Summary</strong>: Mission, value propositions, and key features</p></li>
<li><p><strong>Market Opportunity</strong>: Target market analysis and revenue projections</p></li>
<li><p><strong>Technical Requirements</strong>: APIs, architecture, and performance targets</p></li>
<li><p><strong>Features &amp; Capabilities</strong>: Detailed feature descriptions and code examples</p></li>
<li><p><strong>User Experience</strong>: UI/UX requirements and user flows</p></li>
<li><p><strong>Success Metrics</strong>: KPIs, performance targets, and business metrics</p></li>
<li><p><strong>Integration Strategy</strong>: Inter-agent communication and data flow</p></li>
<li><p><strong>Implementation Roadmap</strong>: Development phases and milestones</p></li>
<li><p><strong>Risk Mitigation</strong>: Technical and business risk management</p></li>
<li><p><strong>Development Workflow</strong>: Testing strategy and quality assurance</p></li>
</ol>
</section>
<section id="dynamic-status-tracking">
<h3>Dynamic Status Tracking<a class="headerlink" href="#dynamic-status-tracking" title="Link to this heading"></a></h3>
<p>Each document includes automatically updated status information:</p>
<ul class="simple">
<li><p><strong>Current Status</strong>: Real-time development status based on commits</p></li>
<li><p><strong>Last Updated</strong>: Automatic timestamp with commit reference</p></li>
<li><p><strong>Next Milestone</strong>: Upcoming development targets and deadlines</p></li>
</ul>
</section>
</section>
<section id="building-documentation">
<h2>🚀 Building Documentation<a class="headerlink" href="#building-documentation" title="Link to this heading"></a></h2>
<section id="prerequisites">
<h3>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pip<span class="w"> </span>install<span class="w"> </span>sphinx<span class="w"> </span>sphinx-rtd-theme<span class="w"> </span>myst-parser
</pre></div>
</div>
</section>
<section id="build-commands">
<h3>Build Commands<a class="headerlink" href="#build-commands" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Navigate to docs directory</span>
<span class="nb">cd</span><span class="w"> </span>docs/sphinx

<span class="c1"># Build HTML documentation</span>
sphinx-build<span class="w"> </span>-b<span class="w"> </span>html<span class="w"> </span>.<span class="w"> </span>_build/html

<span class="c1"># Build with clean rebuild</span>
sphinx-build<span class="w"> </span>-b<span class="w"> </span>html<span class="w"> </span>.<span class="w"> </span>_build/html<span class="w"> </span>-E

<span class="c1"># Serve locally (if sphinx-autobuild is installed)</span>
sphinx-autobuild<span class="w"> </span>.<span class="w"> </span>_build/html
</pre></div>
</div>
</section>
<section id="output-location">
<h3>Output Location<a class="headerlink" href="#output-location" title="Link to this heading"></a></h3>
<p>Built documentation is available at: <code class="docutils literal notranslate"><span class="pre">docs/sphinx/_build/html/index.html</span></code></p>
</section>
</section>
<section id="configuration">
<h2>🔧 Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<section id="monitoring-configuration">
<h3>Monitoring Configuration<a class="headerlink" href="#monitoring-configuration" title="Link to this heading"></a></h3>
<p>The monitoring system can be configured by editing <code class="docutils literal notranslate"><span class="pre">scripts/prd_docs_monitor.py</span></code>:</p>
<ul class="simple">
<li><p><strong>Check Interval</strong>: Modify daemon check frequency</p></li>
<li><p><strong>Agent Keywords</strong>: Update keyword detection for agents</p></li>
<li><p><strong>File Patterns</strong>: Adjust file path patterns for agent detection</p></li>
<li><p><strong>Status Updates</strong>: Customize status update logic</p></li>
</ul>
</section>
<section id="sphinx-configuration">
<h3>Sphinx Configuration<a class="headerlink" href="#sphinx-configuration" title="Link to this heading"></a></h3>
<p>Sphinx settings are in <code class="docutils literal notranslate"><span class="pre">docs/sphinx/conf.py</span></code>:</p>
<ul class="simple">
<li><p><strong>Theme Settings</strong>: Customize appearance and navigation</p></li>
<li><p><strong>Extensions</strong>: Add or remove Sphinx extensions</p></li>
<li><p><strong>Cross-References</strong>: Configure inter-document linking</p></li>
</ul>
</section>
</section>
<section id="metrics-and-analytics">
<h2>📈 Metrics and Analytics<a class="headerlink" href="#metrics-and-analytics" title="Link to this heading"></a></h2>
<p>The system tracks:</p>
<section id="documentation-metrics">
<h3>Documentation Metrics<a class="headerlink" href="#documentation-metrics" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Update Frequency</strong>: How often each agent’s documentation is updated</p></li>
<li><p><strong>Commit Relevance</strong>: Accuracy of agent detection from commits</p></li>
<li><p><strong>Status Accuracy</strong>: Alignment between actual development and documented status</p></li>
</ul>
</section>
<section id="development-metrics">
<h3>Development Metrics<a class="headerlink" href="#development-metrics" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Implementation Progress</strong>: Track development progress across all agents</p></li>
<li><p><strong>Commit Activity</strong>: Monitor development velocity and focus areas</p></li>
<li><p><strong>Milestone Tracking</strong>: Automatic milestone progress updates</p></li>
</ul>
</section>
</section>
<section id="maintenance">
<h2>🛠️ Maintenance<a class="headerlink" href="#maintenance" title="Link to this heading"></a></h2>
<section id="regular-tasks">
<h3>Regular Tasks<a class="headerlink" href="#regular-tasks" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Review Agent Keywords</strong>: Ensure keyword detection remains accurate</p></li>
<li><p><strong>Update Status Logic</strong>: Refine status determination based on commit patterns</p></li>
<li><p><strong>Documentation Quality</strong>: Review and improve documentation content</p></li>
<li><p><strong>Build Verification</strong>: Ensure Sphinx builds complete successfully</p></li>
</ol>
</section>
<section id="troubleshooting">
<h3>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h3>
<section id="common-issues">
<h4>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h4>
<ol class="arabic simple">
<li><p><strong>Missing Updates</strong>: Check git repository status and commit access</p></li>
<li><p><strong>Build Failures</strong>: Verify Sphinx installation and configuration</p></li>
<li><p><strong>Incorrect Agent Detection</strong>: Review and update keyword patterns</p></li>
<li><p><strong>Status Inconsistencies</strong>: Manually update status if automatic detection fails</p></li>
</ol>
</section>
<section id="debug-commands">
<h4>Debug Commands<a class="headerlink" href="#debug-commands" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Test monitoring script</span>
python3<span class="w"> </span>scripts/prd_docs_monitor.py<span class="w"> </span>--check<span class="w"> </span>--verbose

<span class="c1"># Force update specific agent</span>
python3<span class="w"> </span>scripts/prd_docs_monitor.py<span class="w"> </span>--update<span class="w"> </span>--agent<span class="w"> </span>agent-1

<span class="c1"># Check git status</span>
git<span class="w"> </span>status<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>git<span class="w"> </span>log<span class="w"> </span>--oneline<span class="w"> </span>-5
</pre></div>
</div>
</section>
</section>
</section>
<section id="support">
<h2>📞 Support<a class="headerlink" href="#support" title="Link to this heading"></a></h2>
<p>For issues with the PRD documentation system:</p>
<ol class="arabic simple">
<li><p>Check the monitoring logs: <code class="docutils literal notranslate"><span class="pre">/tmp/prd-monitor.log</span></code></p></li>
<li><p>Verify git repository status and permissions</p></li>
<li><p>Test Sphinx build manually: <code class="docutils literal notranslate"><span class="pre">cd</span> <span class="pre">docs/sphinx</span> <span class="pre">&amp;&amp;</span> <span class="pre">sphinx-build</span> <span class="pre">-b</span> <span class="pre">html</span> <span class="pre">.</span> <span class="pre">_build/html</span></code></p></li>
<li><p>Review agent keyword detection in <code class="docutils literal notranslate"><span class="pre">scripts/prd_docs_monitor.py</span></code></p></li>
</ol>
<hr class="docutils" />
<p><strong>Last Updated</strong>: Automatically maintained by the PRD monitoring system<br />
<strong>System Status</strong>: ✅ Active monitoring enabled<br />
<strong>Documentation Coverage</strong>: 5/5 agents documented with automatic updates</p>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>