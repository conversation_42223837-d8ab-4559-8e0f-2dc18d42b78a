Search.setIndex({"alltitles": {"1. Enhanced User Guide (guides/user_guide.rst)": [[0, "enhanced-user-guide-guides-user-guide-rst"]], "1. System Architecture Diagram": [[0, "system-architecture-diagram"]], "2. Application Flows Guide (guides/application_flows.rst)": [[0, "application-flows-guide-guides-application-flows-rst"]], "2. User Journey Flow": [[0, "user-journey-flow"]], "3. Authentication Flow Sequences": [[0, "authentication-flow-sequences"]], "3. Testing Guide (guides/testing_guide.rst)": [[0, "testing-guide-guides-testing-guide-rst"]], "4. Dashboard Data Flows": [[0, "dashboard-data-flows"]], "4. Frontend Implementation Guide (guides/frontend_implementation.rst)": [[0, "frontend-implementation-guide-guides-frontend-implementation-rst"]], "5. Testing Architecture": [[0, "testing-architecture"]], "AI & Intelligence Systems": [[57, null]], "AI Development Workflow": [[53, "id6"]], "AI Models Implementation": [[19, null]], "AI Quality Targets": [[53, "id5"]], "AI Study Assistant": [[59, "ai-study-assistant"]], "AI Study Assistant API": [[5, null], [11, "ai-study-assistant-api"]], "AI/ML Architecture": [[20, "ai-ml-architecture"]], "API Documentation": [[1, "api-documentation"]], "API Documentation Sections": [[11, "api-documentation-sections"]], "API Integration Examples": [[40, "api-integration-examples"]], "API Layer": [[20, "api-layer"]], "API Performance": [[20, "api-performance"]], "API Permissions by Role": [[6, "id1"]], "API Reference": [[11, null]], "Access Control & Permissions": [[6, "access-control-permissions"]], "Accessibility Enhancements": [[0, "accessibility-enhancements"]], "Accessing Your Dashboard": [[46, "accessing-your-dashboard"]], "Account Security": [[7, "account-security"]], "Achievement System": [[14, "achievement-system"]], "Active Sessions": [[7, "active-sessions"]], "Add Team Members": [[13, "add-team-members"]], "Adding New Questions": [[25, "adding-new-questions"]], "Adding New User Types": [[25, "adding-new-user-types"]], "Advanced Features": [[36, "advanced-features"]], "Agent 3 Analytics Integration": [[40, "agent-3-analytics-integration"]], "Agent 3: Enterprise Analytics Engine": [[3, null]], "Agent 4 API Reference": [[4, null]], "Agent 4 Career Intelligence Integration": [[40, "agent-4-career-intelligence-integration"]], "Agent 4 User Guide": [[36, null]], "Agent Architecture Summary": [[57, "id1"]], "Agent Detection Keywords": [[51, "agent-detection-keywords"]], "Agent Overview": [[52, "id1"], [53, "id1"], [54, "id1"], [55, "id1"], [56, "id1"]], "Analyse Career Transition": [[8, "analyse-career-transition"]], "Analytics & Reporting": [[10, "analytics-reporting"]], "Analytics and Insights": [[15, "analytics-and-insights"]], "Analytics and Reporting": [[13, "analytics-and-reporting"]], "Annual Overhauls": [[25, "annual-overhauls"]], "Architecture Overview": [[20, "architecture-overview"]], "Assign Licences": [[10, "assign-licences"]], "Audit Log": [[10, "audit-log"]], "Audit Logs": [[13, "audit-logs"]], "Authentication": [[4, "authentication"], [11, "authentication"]], "Authentication & Authorisation": [[20, "authentication-authorisation"]], "Authentication & Security": [[6, null]], "Authentication Endpoints": [[7, "authentication-endpoints"]], "Authentication Errors": [[6, "authentication-errors"]], "Authentication Integration": [[40, "authentication-integration"]], "Authentication Methods": [[6, "authentication-methods"]], "Automated Training Process": [[19, "automated-training-process"]], "Automation Opportunities": [[25, "automation-opportunities"]], "Available Connectors": [[12, "available-connectors"]], "Base URL": [[11, "base-url"]], "Branch Strategy": [[21, "branch-strategy"]], "Budget Optimization API": [[4, "budget-optimization-api"]], "Budget Optimization Dashboard": [[36, "budget-optimization-dashboard"]], "Budget Planning": [[9, "budget-planning"]], "Bug Reports": [[21, "bug-reports"]], "Build Commands": [[51, "build-commands"]], "Build Testing": [[25, "build-testing"]], "Building Documentation": [[1, "building-documentation"]], "Bulk User Import": [[10, "bulk-user-import"]], "Business Intelligence": [[3, "business-intelligence"]], "CDN Integration": [[1, "cdn-integration"]], "CSS Styling (_static/mermaid.css)": [[0, "css-styling-static-mermaid-css"]], "Calculate Certification Costs": [[9, "calculate-certification-costs"]], "Career & Cost Intelligence": [[59, "career-cost-intelligence"]], "Career & Financial Intelligence": [[57, null]], "Career Analysis": [[8, "career-analysis"]], "Career Framework API": [[8, null]], "Career Intelligence": [[59, "career-intelligence"]], "Career Path Planning": [[8, "career-path-planning"]], "Career Pathfinding API": [[4, "career-pathfinding-api"]], "Career Planning Dashboard": [[36, "career-planning-dashboard"]], "Career Transition API": [[11, "career-transition-api"]], "CertRats Sphinx Documentation System": [[1, null]], "Certification Progress": [[15, "certification-progress"]], "Code Documentation": [[1, "code-documentation"]], "Code Organisation": [[20, "code-organisation"]], "Code Review Guidelines": [[21, "code-review-guidelines"]], "Code Standards": [[21, "code-standards"]], "Code of Conduct": [[21, "code-of-conduct"]], "Common Issues": [[46, "common-issues"], [51, "common-issues"]], "Common Response Formats": [[11, "common-response-formats"]], "Community Guidelines": [[21, "community-guidelines"]], "Compare Certifications": [[9, "compare-certifications"]], "Complete Platform Integration Guide": [[40, null]], "Complete Study Session": [[14, "complete-study-session"]], "Complete Unified Dashboard": [[16, "complete-unified-dashboard"]], "Complete User Profile": [[16, "complete-user-profile"]], "Complete Workflow Integration": [[40, "complete-workflow-integration"]], "Compliance & Audit": [[10, "compliance-audit"]], "Compliance Performance Targets - ACHIEVED": [[54, "id5"]], "Compliance Report": [[10, "compliance-report"], [13, "compliance-report"]], "Compliance and Audit": [[13, "compliance-and-audit"]], "Comprehensive Planning": [[16, "comprehensive-planning"]], "Configure Data Sync": [[12, "configure-data-sync"]], "Configure LMS Integration": [[12, "configure-lms-integration"]], "Configure SSO Provider": [[12, "configure-sso-provider"]], "Confirm Password Reset": [[7, "confirm-password-reset"]], "Containerised Deployment": [[20, "containerised-deployment"]], "Content Contributors": [[25, "content-contributors"]], "Content Organization": [[0, "content-organization"]], "Content Review Checklist": [[25, "content-review-checklist"]], "Content Review Process": [[1, "content-review-process"]], "Contributing Guide": [[21, null]], "Contribution Types": [[21, "contribution-types"]], "Core API Modules": [[11, "core-api-modules"]], "Core Agents": [[51, "core-agents"]], "Core Authentication System": [[7, null]], "Core Calculations": [[9, "core-calculations"]], "Core Components": [[20, "core-components"]], "Core Endpoints": [[5, "core-endpoints"]], "Core Platform & Foundation": [[57, null]], "Core Platform (Foundation)": [[59, "core-platform-foundation"]], "Cost Calculator API": [[4, "cost-calculator-api"], [9, null], [11, "cost-calculator-api"]], "Cost History Tracking": [[9, "cost-history-tracking"]], "Cost Optimization": [[3, "cost-optimization"]], "Create Cost Scenario": [[9, "create-cost-scenario"]], "Create Department": [[10, "create-department"]], "Create Enterprise User": [[10, "create-enterprise-user"]], "Create Learning Goal": [[14, "create-learning-goal"]], "Create Organisation": [[10, "create-organisation"]], "Create Organization": [[13, "create-organization"]], "Create Team": [[13, "create-team"]], "Create Training Budget": [[9, "create-training-budget"]], "Cross-Component Recommendation Engine": [[59, "cross-component-recommendation-engine"]], "Cross-References": [[1, "cross-references"]], "Currency & Localisation": [[9, "currency-localisation"]], "Custom Reports": [[3, "custom-reports"]], "Custom Styling": [[1, "custom-styling"]], "Data Layer": [[20, "data-layer"]], "Data Privacy & Security": [[19, "data-privacy-security"]], "Data Security": [[20, "data-security"]], "Data Synchronisation": [[12, "data-synchronisation"]], "Data Visualization": [[3, "data-visualization"]], "Database Optimisation": [[20, "database-optimisation"]], "Debug Commands": [[51, "debug-commands"]], "Department Analytics": [[10, "department-analytics"]], "Department Management": [[10, "department-management"]], "Deployment Architecture": [[20, "deployment-architecture"]], "Deployment Configuration": [[40, "deployment-configuration"]], "Deployment Readiness": [[55, "id9"]], "Detailed Session History": [[14, "detailed-session-history"]], "Development Metrics": [[51, "development-metrics"]], "Development Workflow": [[20, "development-workflow"], [21, "development-workflow"], [52, "id6"]], "Difficulty Assessment": [[5, "difficulty-assessment"]], "Difficulty Estimator Model": [[19, "difficulty-estimator-model"]], "Docker Deployment": [[1, "docker-deployment"]], "Documentation": [[57, null]], "Documentation Enhancement Summary": [[0, null]], "Documentation Metrics": [[1, "documentation-metrics"], [51, "documentation-metrics"]], "Documentation Standards": [[21, "documentation-standards"]], "Documentation Structure": [[0, "documentation-structure"]], "Dynamic Status Tracking": [[51, "dynamic-status-tracking"]], "Enable MFA": [[7, "enable-mfa"]], "End Study Session": [[15, "end-study-session"]], "Engagement Targets": [[52, "id5"]], "Enhanced Navigation": [[0, "enhanced-navigation"]], "Enhanced Security Taxonomy API": [[11, "enhanced-security-taxonomy-api"]], "Enhanced Sphinx Configuration": [[0, "enhanced-sphinx-configuration"]], "Enterprise & Analytics": [[57, null]], "Enterprise API": [[10, null]], "Enterprise Analytics": [[59, "enterprise-analytics"]], "Enterprise Dashboard API": [[11, "enterprise-dashboard-api"]], "Enterprise Integration": [[7, "enterprise-integration"]], "Enterprise Settings": [[13, "enterprise-settings"]], "Enterprise Training Analysis": [[16, "enterprise-training-analysis"]], "Environment Configuration": [[49, "environment-configuration"]], "Error Codes": [[11, "error-codes"]], "Error Handling": [[4, "error-handling"], [5, "error-handling"], [6, "error-handling"], [7, "error-handling"], [8, "error-handling"], [9, "error-handling"], [10, "error-handling"], [12, "error-handling"], [14, "error-handling"]], "Error Response": [[11, "error-response"]], "Exchange Rates": [[9, "exchange-rates"]], "Executive Dashboard": [[3, "executive-dashboard"]], "External Resources": [[25, "external-resources"]], "FAQ by User Type:": [[30, null]], "Feature Requests": [[21, "feature-requests"]], "File Structure": [[25, "file-structure"]], "Final Implementation Summary": [[54, "id6"]], "For Developers": [[59, "for-developers"]], "For Enterprises": [[59, "for-enterprises"]], "For Users": [[59, "for-users"]], "Frontend Integration": [[40, "frontend-integration"]], "GET /career-transition/roles": [[4, "get-career-transition-roles"]], "Generate Career Roadmap": [[8, "generate-career-roadmap"]], "Generate Report": [[3, "generate-report"]], "Get Comprehensive Recommendations": [[5, "get-comprehensive-recommendations"]], "Get High-Demand Roles": [[8, "get-high-demand-roles"]], "Get Integration Status": [[12, "get-integration-status"]], "Get Organisation Details": [[10, "get-organisation-details"]], "Get Organization Details": [[13, "get-organization-details"]], "Get Progress Overview": [[14, "get-progress-overview"]], "Get Team Details": [[13, "get-team-details"]], "Get User Achievements": [[14, "get-user-achievements"]], "Getting Help": [[21, "getting-help"], [46, "getting-help"]], "Getting Started": [[21, "getting-started"], [36, "getting-started"], [40, "getting-started"]], "GitHub Pages Deployment": [[1, "github-pages-deployment"]], "Goal Management": [[14, "goal-management"], [15, "goal-management"]], "Google Analytics Integration": [[1, "google-analytics-integration"]], "High-Level Architecture": [[20, "high-level-architecture"]], "Historical Analysis": [[9, "historical-analysis"]], "How It Works": [[51, "how-it-works"]], "Implementation Achievement Summary": [[57, "id2"]], "Implementation Completion Status": [[54, "id2"]], "Indices and tables": [[48, "indices-and-tables"]], "Install Connector": [[12, "install-connector"]], "Installation Guide": [[49, null]], "Integration Examples": [[59, "integration-examples"]], "Integration Features": [[13, "integration-features"], [15, "integration-features"]], "Integration Hub API": [[11, "integration-hub-api"], [12, null]], "Integration Monitoring": [[12, "integration-monitoring"]], "Interactive Documentation": [[11, "interactive-documentation"]], "Interactive Elements": [[0, "interactive-elements"]], "Internal Contacts": [[25, "internal-contacts"]], "Internationalization": [[1, "internationalization"]], "Invite Users": [[13, "invite-users"]], "JavaScript Enhancement (_static/mermaid-init.js)": [[0, "javascript-enhancement-static-mermaid-init-js"]], "Job Market Intelligence": [[8, "job-market-intelligence"]], "Key Configuration Features": [[1, "key-configuration-features"]], "Key Metrics": [[46, "key-metrics"]], "Key Performance Indicators": [[57, "id3"]], "Learning Analytics": [[5, "learning-analytics"], [14, "learning-analytics"], [15, "learning-analytics"]], "Learning Effectiveness Analysis": [[14, "learning-effectiveness-analysis"]], "Learning Management System Integration": [[12, "learning-management-system-integration"]], "Learning Performance Metrics": [[3, "learning-performance-metrics"]], "Learning Style Analyser": [[19, "learning-style-analyser"]], "Learning Style Analysis": [[5, "learning-style-analysis"]], "Licence Management": [[10, "licence-management"]], "Licence Usage Overview": [[10, "licence-usage-overview"]], "List Departments": [[10, "list-departments"]], "List Organization Users": [[13, "list-organization-users"]], "Live Metrics Dashboard": [[3, "live-metrics-dashboard"]], "Loading Performance": [[0, "loading-performance"]], "Manual Installation": [[49, "manual-installation"]], "Market Analysis": [[52, "id2"]], "Market Intelligence API": [[4, "market-intelligence-api"]], "Market Intelligence Dashboard": [[36, "market-intelligence-dashboard"]], "Market Segments": [[53, "id2"], [54, "id3"], [55, "id2"], [56, "id2"]], "Marketplace & Integrations": [[57, null]], "Marketplace Hub": [[59, "marketplace-hub"]], "Marketplace Targets": [[56, "id4"]], "Mermaid Diagram Support": [[1, "mermaid-diagram-support"]], "Mobile Enterprise API": [[11, "mobile-enterprise-api"]], "Mobile-First Documentation": [[0, "mobile-first-documentation"]], "Model Architecture": [[19, "model-architecture"]], "Model Encryption": [[19, "model-encryption"]], "Model Performance Tracking": [[19, "model-performance-tracking"]], "Model Training & Customisation": [[5, "model-training-customisation"]], "Model Training Pipeline": [[19, "model-training-pipeline"]], "Modifying Existing Content": [[25, "modifying-existing-content"]], "Monitoring Configuration": [[51, "monitoring-configuration"]], "Monitoring Features": [[51, "monitoring-features"]], "Monitoring Script Usage": [[51, "monitoring-script-usage"]], "Monitoring and Maintenance": [[40, "monitoring-and-maintenance"]], "Monthly Reviews": [[25, "monthly-reviews"]], "Multi-Factor Authentication": [[7, "multi-factor-authentication"]], "Multi-Tenant Security": [[6, "multi-tenant-security"]], "Next Steps": [[49, "next-steps"]], "On-Device AI Processing": [[20, "on-device-ai-processing"]], "Open Graph Meta Tags": [[1, "open-graph-meta-tags"]], "Organisation Analytics": [[10, "organisation-analytics"]], "Organisation Management": [[10, "organisation-management"]], "Organization Administration": [[13, "organization-administration"]], "Organization Analytics": [[13, "organization-analytics"]], "Organization Management": [[13, null]], "Organization Management Setup": [[40, "organization-management-setup"]], "Organization Overview": [[3, "organization-overview"]], "Output Location": [[51, "output-location"]], "Overview": [[3, "overview"], [4, "overview"], [5, "overview"], [7, "overview"], [8, "overview"], [9, "overview"], [10, "overview"], [12, "overview"], [13, "overview"], [14, "overview"], [15, "overview"], [19, "overview"], [40, "overview"]], "PDF Generation": [[1, "pdf-generation"]], "POST /budget-optimization/optimize": [[4, "post-budget-optimization-optimize"]], "POST /career-transition/pathfinding": [[4, "post-career-transition-pathfinding"]], "POST /cost-calculator/calculate": [[4, "post-cost-calculator-calculate"]], "POST /market-intelligence/analysis": [[4, "post-market-intelligence-analysis"]], "POST /salary-intelligence/roi-analysis": [[4, "post-salary-intelligence-roi-analysis"]], "PRD Documentation System": [[51, null]], "Pagination": [[11, "pagination"]], "Password Reset": [[7, "password-reset"]], "Performance Achievements": [[55, "id7"]], "Performance Analytics": [[3, "performance-analytics"], [14, "performance-analytics"]], "Performance Indicators": [[59, "performance-indicators"]], "Performance Metrics": [[15, "performance-metrics"]], "Performance Monitoring": [[19, "performance-monitoring"]], "Performance Optimisation": [[20, "performance-optimisation"]], "Performance Prediction": [[5, "performance-prediction"]], "Performance Predictor Model": [[19, "performance-predictor-model"]], "Performance Targets": [[52, "id4"], [53, "id4"]], "Personalized Recommendations": [[15, "personalized-recommendations"]], "Personalizing Your Dashboard": [[46, "personalizing-your-dashboard"]], "Planned Improvements": [[25, "planned-improvements"]], "Platform Architecture": [[40, "platform-architecture"]], "Predictive Analytics": [[3, "predictive-analytics"]], "Prerequisites": [[1, "prerequisites"], [21, "prerequisites"], [49, "prerequisites"], [51, "prerequisites"]], "Privacy & Security": [[5, "privacy-security"]], "Privacy Settings": [[46, "privacy-settings"]], "Privacy-First AI Architecture": [[19, "privacy-first-ai-architecture"]], "Privacy-Preserving Techniques": [[19, "privacy-preserving-techniques"]], "Progress Tracking": [[15, "progress-tracking"]], "Progress Tracking API": [[11, "progress-tracking-api"], [14, null]], "Progress Tracking Integration": [[5, "progress-tracking-integration"]], "Progressive Enhancement": [[0, "progressive-enhancement"]], "Pull Request Process": [[21, "pull-request-process"]], "Python Example": [[6, "python-example"]], "Quarterly Updates": [[25, "quarterly-updates"]], "Question Format": [[25, "question-format"]], "Quick Start": [[59, "quick-start"]], "Quick Start with Docker (Recommended)": [[49, "quick-start-with-docker-recommended"]], "ROI Analysis": [[3, "roi-analysis"], [16, "roi-analysis"]], "ROI Analysis Dashboard": [[36, "roi-analysis-dashboard"]], "ROI Performance Indicators": [[36, "id1"]], "Rate Limiting": [[4, "rate-limiting"], [6, "rate-limiting"], [11, "rate-limiting"]], "Rate Limits by Endpoint Type": [[6, "id2"]], "Real-Time Monitoring": [[3, "real-time-monitoring"]], "Real-time Recommendations": [[5, "real-time-recommendations"]], "Recent Achievements": [[46, "recent-achievements"]], "Regional Pricing": [[9, "regional-pricing"]], "Register Webhook": [[12, "register-webhook"]], "Regular Maintenance Tasks": [[1, "regular-maintenance-tasks"]], "Regular Tasks": [[51, "regular-tasks"]], "Rendering Performance": [[0, "rendering-performance"]], "Response Time Performance": [[55, "id4"]], "Revenue Projections": [[52, "id3"], [53, "id3"], [54, "id4"], [55, "id3"], [56, "id3"]], "Review Process": [[1, "review-process"], [21, "review-process"], [25, "review-process"]], "Revoke Session": [[7, "revoke-session"]], "Role Salary Analysis": [[16, "role-salary-analysis"]], "Role-Based Access Control": [[7, "role-based-access-control"]], "Role-Based Access Control (RBAC)": [[6, "role-based-access-control-rbac"]], "SDK and Client Libraries": [[11, "sdk-and-client-libraries"]], "SDKs and Libraries": [[4, "sdks-and-libraries"]], "SSO Configuration": [[7, "sso-configuration"]], "Salary Analysis": [[8, "salary-analysis"]], "Salary Intelligence API": [[4, "salary-intelligence-api"]], "Scalability Performance": [[55, "id5"]], "Scenario Modelling": [[9, "scenario-modelling"]], "Search Integration": [[1, "search-integration"]], "Search Optimization": [[0, "search-optimization"]], "Section Organization": [[25, "section-organization"]], "Security Architecture": [[20, "security-architecture"]], "Security Best Practices": [[6, "security-best-practices"], [7, "security-best-practices"]], "Security Career Framework API": [[11, "security-career-framework-api"]], "Security Features": [[7, "security-features"]], "Security Policies": [[13, "security-policies"]], "See Also": [[5, "see-also"], [6, "see-also"], [8, "see-also"], [9, "see-also"], [10, "see-also"], [12, "see-also"], [14, "see-also"], [19, "see-also"], [20, "see-also"], [21, "see-also"]], "Service Health Checks": [[16, "service-health-checks"]], "Service Layer": [[20, "service-layer"]], "Session History": [[15, "session-history"]], "Session Management": [[7, "session-management"], [15, "session-management"]], "Setting Up Development Environment": [[21, "setting-up-development-environment"]], "Single Sign-On (SSO) Integration": [[12, "single-sign-on-sso-integration"]], "Skills Assessment": [[5, "skills-assessment"], [8, "skills-assessment"]], "Skills Development": [[8, "skills-development"]], "Sphinx Configuration": [[51, "sphinx-configuration"]], "Sphinx Configuration Enhancements": [[0, "sphinx-configuration-enhancements"]], "Standard Sections": [[51, "standard-sections"]], "Start Study Session": [[14, "start-study-session"], [15, "start-study-session"]], "Study Plan Generation": [[15, "study-plan-generation"]], "Study Recommendations": [[15, "study-recommendations"]], "Study Session Integration": [[40, "study-session-integration"]], "Study Session Management": [[14, "study-session-management"]], "Study Session Metrics": [[15, "id1"]], "Study Session Tracking": [[15, null]], "Study Techniques": [[5, "study-techniques"]], "Study Timer API": [[11, "study-timer-api"]], "Success Prediction": [[3, "success-prediction"]], "Success Response": [[11, "success-response"]], "Support": [[4, "support"]], "Support and Resources": [[36, "support-and-resources"]], "Support and Troubleshooting": [[40, "support-and-troubleshooting"]], "Synchronise LMS Data": [[12, "synchronise-lms-data"]], "System Architecture": [[20, null]], "Table of Contents": [[5, "table-of-contents"], [6, "table-of-contents"], [8, "table-of-contents"], [9, "table-of-contents"], [10, "table-of-contents"], [11, "table-of-contents"], [12, "table-of-contents"], [14, "table-of-contents"], [18, "table-of-contents"], [19, "table-of-contents"], [20, "table-of-contents"], [21, "table-of-contents"], [24, "table-of-contents"], [49, "table-of-contents"]], "Team Analytics": [[3, "team-analytics"]], "Team Management": [[13, "team-management"]], "Team Performance Report": [[13, "team-performance-report"]], "Technical Performance": [[55, "id8"]], "Technical Review Checklist": [[25, "technical-review-checklist"]], "Test Coverage Metrics": [[55, "id6"]], "Test SSO Configuration": [[12, "test-sso-configuration"]], "Testing Authentication": [[6, "testing-authentication"]], "Testing Requirements": [[21, "testing-requirements"]], "Third-party Connectors": [[12, "third-party-connectors"]], "Token Refresh": [[6, "token-refresh"], [7, "token-refresh"]], "Token Security": [[6, "token-security"]], "Token-Based Authentication": [[6, "token-based-authentication"]], "Tools and Scripts": [[25, "tools-and-scripts"]], "Topic Recommender Model": [[19, "topic-recommender-model"]], "Traefik Integration": [[59, "traefik-integration"]], "Train AI Models": [[5, "train-ai-models"]], "Trend Forecasting": [[3, "trend-forecasting"]], "Troubleshooting": [[36, "troubleshooting"], [49, "troubleshooting"], [51, "troubleshooting"]], "Types of Insights": [[46, "types-of-insights"]], "Types of Recommendations": [[46, "types-of-recommendations"]], "Understanding Insight Priorities": [[46, "understanding-insight-priorities"]], "Understanding ROI Estimates": [[46, "understanding-roi-estimates"]], "Understanding Your Scores": [[46, "understanding-your-scores"]], "Unified Dashboard": [[59, "unified-dashboard"]], "Unified Dashboard Service": [[59, "unified-dashboard-service"]], "Unified Error Handling": [[59, "unified-error-handling"]], "Unified Intelligence Platform": [[59, "unified-intelligence-platform"]], "Unified Metrics": [[59, "unified-metrics"]], "Unified Performance Metrics": [[16, "unified-performance-metrics"]], "Unified User Profile Service": [[59, "unified-user-profile-service"]], "Upcoming Milestones": [[46, "upcoming-milestones"]], "Update Goal Progress": [[14, "update-goal-progress"]], "Update Organisation": [[10, "update-organisation"]], "Update Organization": [[13, "update-organization"]], "Update Session Progress": [[15, "update-session-progress"]], "Update User Role": [[7, "update-user-role"], [13, "update-user-role"]], "User Experience Review": [[25, "user-experience-review"]], "User Login": [[7, "user-login"]], "User Management": [[10, "user-management"], [13, "user-management"]], "User Permissions": [[7, "user-permissions"]], "User Registration": [[7, "user-registration"]], "User Summary": [[46, "user-summary"]], "Validation Process": [[25, "validation-process"]], "Verification": [[49, "verification"]], "Verify MFA": [[7, "verify-mfa"]], "Version Control": [[25, "version-control"]], "Viewing Documentation": [[1, "viewing-documentation"]], "Webhook Event Examples": [[12, "webhook-event-examples"]], "Webhook Management": [[12, "webhook-management"]], "Webhooks": [[11, "webhooks"]], "Welcome to Agent 4": [[36, "welcome-to-agent-4"]], "Writing Guidelines": [[1, "writing-guidelines"], [25, "writing-guidelines"]], "reStructuredText Basics": [[1, "restructuredtext-basics"]], "\u267f Accessibility Testing Framework": [[45, "accessibility-testing-framework"]], "\u2699\ufe0f Configuration Guide": [[18, null]], "\u26a0\ufe0f Error Handling": [[16, "error-handling"]], "\u26a1 5-Minute Setup": [[58, "minute-setup"]], "\u26a1 Performance Excellence": [[48, "performance-excellence"]], "\u26a1 Performance Features": [[43, "performance-features"]], "\u26a1 Performance Improvements": [[22, "performance-improvements"]], "\u26a1 Performance Optimization": [[42, "performance-optimization"]], "\u26a1 Performance Optimization Flow": [[39, "performance-optimization-flow"]], "\u26a1 Performance Testing Strategy": [[45, "performance-testing-strategy"]], "\u26a1 Quick Actions": [[46, "quick-actions"]], "\u2753 FAQ Section": [[1, "faq-section"]], "\u2753 Frequently Asked Questions": [[30, null]], "\u2753 Frequently Asked Questions:": [[48, null]], "\ud83c\udd98 Getting Help & Support": [[58, "getting-help-support"]], "\ud83c\udd98 Troubleshooting": [[43, "troubleshooting"], [46, "troubleshooting"]], "\ud83c\udd98 Troubleshooting & Support": [[47, "troubleshooting-support"]], "\ud83c\udf10 Agent 5: Marketplace & Integration Hub": [[56, null]], "\ud83c\udf10 Marketplace Features & Capabilities": [[56, "marketplace-features-capabilities"]], "\ud83c\udf10 Progressive Web App (PWA)": [[44, "progressive-web-app-pwa"]], "\ud83c\udf10 Technical Requirements": [[56, "technical-requirements"]], "\ud83c\udf10 Translation Management": [[35, "translation-management"]], "\ud83c\udf10 Unified API Endpoints": [[59, "unified-api-endpoints"]], "\ud83c\udf1f AI Success Stories": [[2, "ai-success-stories"]], "\ud83c\udf1f Innovation Leadership": [[48, "innovation-leadership"]], "\ud83c\udf1f Market Leadership Position": [[50, "market-leadership-position"]], "\ud83c\udf1f Success Metrics": [[24, "success-metrics"]], "\ud83c\udf1f Version 1.0.0 - Platform Launch (2023-11-01)": [[17, "version-1-0-0-platform-launch-2023-11-01"]], "\ud83c\udf89 Implementation Complete - Production Ready": [[54, "implementation-complete-production-ready"]], "\ud83c\udf89 Implementation Status - COMPLETE": [[54, "implementation-status-complete"]], "\ud83c\udf89 MASSIVE IMPLEMENTATION MILESTONE ACHIEVED": [[57, "massive-implementation-milestone-achieved"]], "\ud83c\udf89 Summary": [[0, "summary"]], "\ud83c\udf89 Version 2.0.0 - Unified Platform Integration (2024-01-16)": [[17, "version-2-0-0-unified-platform-integration-2024-01-16"]], "\ud83c\udf93 Academics & Researchers FAQ": [[26, null]], "\ud83c\udf93 Adaptive Learning Paths": [[37, "adaptive-learning-paths"]], "\ud83c\udf93 Certification Explorer": [[42, "certification-explorer"], [47, "certification-explorer"]], "\ud83c\udf93 Certification Explorer Flow": [[38, "certification-explorer-flow"]], "\ud83c\udf93 Certification Planning Workflow": [[58, "certification-planning-workflow"]], "\ud83c\udfa8 Custom Styling": [[23, "custom-styling"]], "\ud83c\udfa8 Enterprise User Experience": [[54, "enterprise-user-experience"]], "\ud83c\udfa8 Frontend Implementation Guide": [[42, null]], "\ud83c\udfa8 Mermaid Diagram Integration": [[0, "mermaid-diagram-integration"]], "\ud83c\udfa8 Sphinx Configuration": [[1, "sphinx-configuration"]], "\ud83c\udfa8 UI/UX Enhancements": [[22, "ui-ux-enhancements"]], "\ud83c\udfa8 User Experience Requirements": [[52, "user-experience-requirements"], [53, "user-experience-requirements"], [55, "user-experience-requirements"], [56, "user-experience-requirements"]], "\ud83c\udfa8 User Interface Features": [[43, "user-interface-features"]], "\ud83c\udfad End-to-End Testing with Playwright + BEHAVE": [[45, "end-to-end-testing-with-playwright-behave"]], "\ud83c\udfaf AI Study Assistant Features": [[37, "ai-study-assistant-features"]], "\ud83c\udfaf AI-Powered Career Guidance": [[50, "ai-powered-career-guidance"]], "\ud83c\udfaf AI-Powered Career Transition System": [[58, "ai-powered-career-transition-system"]], "\ud83c\udfaf AI-Powered Features": [[2, "ai-powered-features"]], "\ud83c\udfaf AI-Powered Study Strategies": [[37, "ai-powered-study-strategies"]], "\ud83c\udfaf Academic Collaboration": [[26, "academic-collaboration"]], "\ud83c\udfaf Advanced User Management": [[24, "advanced-user-management"]], "\ud83c\udfaf Architecture Overview": [[57, "architecture-overview"]], "\ud83c\udfaf Best Practices": [[44, "best-practices"]], "\ud83c\udfaf Best Practices & Tips": [[47, "best-practices-tips"]], "\ud83c\udfaf Career Changer Resources": [[28, "career-changer-resources"]], "\ud83c\udfaf Career Planning": [[33, "career-planning"]], "\ud83c\udfaf Career Transition Hub": [[58, "career-transition-hub"]], "\ud83c\udfaf Career Transition System": [[47, "career-transition-system"]], "\ud83c\udfaf Choose Your User Type": [[30, "choose-your-user-type"]], "\ud83c\udfaf Complete User Guide": [[47, null]], "\ud83c\udfaf Content Standards": [[25, "content-standards"]], "\ud83c\udfaf Enterprise Resources": [[29, "enterprise-resources"]], "\ud83c\udfaf Essential Features Tour": [[58, "essential-features-tour"]], "\ud83c\udfaf Essential Workflows for Success": [[58, "essential-workflows-for-success"]], "\ud83c\udfaf Executive Summary": [[52, "executive-summary"], [53, "executive-summary"], [54, "executive-summary"], [55, "executive-summary"], [56, "executive-summary"]], "\ud83c\udfaf Feature Implementation Status": [[55, "feature-implementation-status"]], "\ud83c\udfaf Future Enhancements": [[22, "future-enhancements"]], "\ud83c\udfaf Getting Started Section": [[1, "getting-started-section"]], "\ud83c\udfaf Integration Benefits": [[59, "integration-benefits"]], "\ud83c\udfaf Integration Overview": [[59, "integration-overview"]], "\ud83c\udfaf Job Search Strategy": [[28, "job-search-strategy"]], "\ud83c\udfaf Key Enhancements": [[0, "key-enhancements"]], "\ud83c\udfaf Learning Management": [[41, "learning-management"]], "\ud83c\udfaf Mobile Learning Strategies": [[44, "mobile-learning-strategies"]], "\ud83c\udfaf Overview": [[46, "overview"]], "\ud83c\udfaf Personalization Engine": [[37, "personalization-engine"]], "\ud83c\udfaf Program Development": [[34, "program-development"]], "\ud83c\udfaf Quick Start": [[43, "quick-start"]], "\ud83c\udfaf Quick Start Guide": [[48, "quick-start-guide"]], "\ud83c\udfaf Recommendations Section": [[46, "recommendations-section"]], "\ud83c\udfaf Specialisation Areas": [[32, "specialisation-areas"]], "\ud83c\udfaf Team Development": [[31, "team-development"]], "\ud83c\udfaf Tips and Best Practices": [[43, "tips-and-best-practices"]], "\ud83c\udfaf Training Manager Resources": [[34, "training-manager-resources"]], "\ud83c\udfaf User Experience Improvements": [[0, "user-experience-improvements"]], "\ud83c\udfaf User Journey Optimization": [[39, "user-journey-optimization"]], "\ud83c\udfaf Version 1.1.0 - Initial Platform (2023-12-01)": [[17, "version-1-1-0-initial-platform-2023-12-01"]], "\ud83c\udfaf What is CertPathFinder?": [[58, "what-is-certpathfinder"]], "\ud83c\udfaf Your Next Steps to Success": [[58, "your-next-steps-to-success"]], "\ud83c\udfc6 Enterprise Architecture": [[24, "enterprise-architecture"]], "\ud83c\udfc6 Industry-First Privacy-Preserving AI": [[2, "industry-first-privacy-preserving-ai"]], "\ud83c\udfc6 Milestones & Achievements": [[46, "milestones-achievements"]], "\ud83c\udfc6 Revolutionary AI Architecture": [[50, "revolutionary-ai-architecture"]], "\ud83c\udfc6 Unified Platform Features": [[48, "unified-platform-features"]], "\ud83c\udfd7\ufe0f Agent 1: Core Platform Engine": [[52, null]], "\ud83c\udfd7\ufe0f Architecture Components": [[59, "architecture-components"]], "\ud83c\udfd7\ufe0f Documentation Architecture": [[1, "documentation-architecture"]], "\ud83c\udfd7\ufe0f Enterprise Architecture": [[41, "enterprise-architecture"]], "\ud83c\udfd7\ufe0f Enterprise Deployment": [[29, "enterprise-deployment"]], "\ud83c\udfd7\ufe0f Frontend Architecture Overview": [[42, "frontend-architecture-overview"]], "\ud83c\udfd7\ufe0f Implementation Architecture": [[55, "implementation-architecture"]], "\ud83c\udfd7\ufe0f System Architecture Overview": [[38, "system-architecture-overview"]], "\ud83c\udfd7\ufe0f Technical Architecture": [[22, "technical-architecture"]], "\ud83c\udfd7\ufe0f Testing Architecture Overview": [[45, "testing-architecture-overview"]], "\ud83c\udfd7\ufe0f Version 1.2.0 - Agent 1 Core Platform (2024-01-01)": [[17, "version-1-2-0-agent-1-core-platform-2024-01-01"]], "\ud83c\udfe0 Dashboard - Your Command Center": [[58, "dashboard-your-command-center"]], "\ud83c\udfe0 Dashboard Data Flow": [[38, "dashboard-data-flow"]], "\ud83c\udfe0 Dashboard Implementation": [[42, "dashboard-implementation"]], "\ud83c\udfe0 Dashboard Overview": [[47, "dashboard-overview"]], "\ud83c\udfe0 Homepage Features": [[43, "homepage-features"]], "\ud83c\udfe0 Homepage Flow": [[39, "homepage-flow"]], "\ud83c\udfe2 Agent 3: Enterprise & Analytics Engine": [[54, null]], "\ud83c\udfe2 Enterprise Administrators FAQ": [[29, null]], "\ud83c\udfe2 Enterprise Configuration": [[18, "enterprise-configuration"]], "\ud83c\udfe2 Enterprise Dashboard": [[24, null]], "\ud83c\udfe2 Enterprise Features": [[47, "enterprise-features"]], "\ud83c\udfe2 Enterprise Features:": [[48, null]], "\ud83c\udfe2 Enterprise Guide": [[41, null]], "\ud83c\udfe2 Enterprise Platform Excellence": [[50, "enterprise-platform-excellence"]], "\ud83c\udfe2 Organization Management": [[35, "organization-management"]], "\ud83c\udfe2 Technical Implementation - COMPLETE": [[54, "technical-implementation-complete"]], "\ud83c\udfe5 Health Check Endpoints": [[16, "health-check-endpoints"]], "\ud83d\udc65 Team Dynamics": [[31, "team-dynamics"]], "\ud83d\udc65 Team Management": [[34, "team-management"]], "\ud83d\udc65 User Management": [[29, "user-management"], [35, "user-management"], [41, "user-management"]], "\ud83d\udc68\u200d\ud83c\udf93 Students & Learners FAQ": [[33, null]], "\ud83d\udc68\u200d\ud83c\udfeb Teaching & Instruction": [[26, "teaching-instruction"]], "\ud83d\udc68\u200d\ud83d\udcbc Administrator Guide": [[35, null]], "\ud83d\udc68\u200d\ud83d\udcbc Managers & Team Leads FAQ": [[31, null]], "\ud83d\udc69\u200d\ud83d\udcbc Working Professionals FAQ": [[32, null]], "\ud83d\udc8e Business Value Proposition": [[48, "business-value-proposition"]], "\ud83d\udca1 Motivational Messages": [[46, "motivational-messages"]], "\ud83d\udca1 Pro Tips for Success": [[58, "pro-tips-for-success"]], "\ud83d\udcac Feedback Management": [[35, "feedback-management"]], "\ud83d\udcb0 Agent 4: Career & Cost Intelligence": [[55, null]], "\ud83d\udcb0 Budget & ROI Management": [[34, "budget-roi-management"]], "\ud83d\udcb0 Budget Planning": [[31, "budget-planning"]], "\ud83d\udcb0 Cost Calculator & ROI Analysis": [[47, "cost-calculator-roi-analysis"]], "\ud83d\udcb0 Financial Intelligence Platform": [[50, "financial-intelligence-platform"]], "\ud83d\udcb0 Financial Planning": [[33, "financial-planning"]], "\ud83d\udcb0 License Management": [[41, "license-management"]], "\ud83d\udcb0 Salary Intelligence Endpoints": [[16, "salary-intelligence-endpoints"]], "\ud83d\udcb0 Smart Cost Calculator & ROI Analysis": [[58, "smart-cost-calculator-roi-analysis"]], "\ud83d\udcb0 Technical Implementation": [[55, "technical-implementation"]], "\ud83d\udcbc Job Management": [[35, "job-management"]], "\ud83d\udcbc Leveraging Existing Skills": [[28, "leveraging-existing-skills"]], "\ud83d\udcbc License Management Excellence": [[24, "license-management-excellence"]], "\ud83d\udcbc Workplace Integration": [[32, "workplace-integration"]], "\ud83d\udcc1 Directory Structure": [[23, "directory-structure"]], "\ud83d\udcc8 API Usage Analytics": [[35, "api-usage-analytics"]], "\ud83d\udcc8 Achieved Success Metrics & KPIs": [[55, "achieved-success-metrics-kpis"]], "\ud83d\udcc8 Market Intelligence": [[46, "market-intelligence"]], "\ud83d\udcc8 Metrics and Analytics": [[51, "metrics-and-analytics"]], "\ud83d\udcc8 Migration Benefits": [[22, "migration-benefits"]], "\ud83d\udcc8 Performance Optimizations": [[0, "performance-optimizations"]], "\ud83d\udcc8 Strategic Planning": [[31, "strategic-planning"]], "\ud83d\udcc8 Success Metrics": [[57, "success-metrics"]], "\ud83d\udcc8 Success Metrics & KPIs": [[52, "success-metrics-kpis"], [53, "success-metrics-kpis"], [56, "success-metrics-kpis"]], "\ud83d\udcc8 Success Metrics & KPIs - ACHIEVED": [[54, "success-metrics-kpis-achieved"]], "\ud83d\udcc8 Version 1.5.0 - Agent 4 Career Intelligence (2024-01-15)": [[17, "version-1-5-0-agent-4-career-intelligence-2024-01-15"]], "\ud83d\udcca Admin Dashboard Overview": [[35, "admin-dashboard-overview"]], "\ud83d\udcca Analytics & Reporting": [[29, "analytics-reporting"]], "\ud83d\udcca Analytics Configuration": [[18, "analytics-configuration"]], "\ud83d\udcca Analytics and Monitoring": [[1, "analytics-and-monitoring"], [23, "analytics-and-monitoring"]], "\ud83d\udcca Common API Patterns": [[58, "common-api-patterns"]], "\ud83d\udcca Comprehensive Learning Analytics": [[50, "comprehensive-learning-analytics"]], "\ud83d\udcca Cross-Component Analytics": [[59, "cross-component-analytics"]], "\ud83d\udcca Dashboard Features": [[22, "dashboard-features"], [43, "dashboard-features"]], "\ud83d\udcca Dashboard Flow": [[39, "dashboard-flow"]], "\ud83d\udcca Dashboard Sections": [[46, "dashboard-sections"]], "\ud83d\udcca Development Methodology": [[57, "development-methodology"]], "\ud83d\udcca Documentation Structure": [[51, "documentation-structure"]], "\ud83d\udcca Enterprise Dashboard": [[41, "enterprise-dashboard"]], "\ud83d\udcca Enterprise Features & Capabilities": [[54, "enterprise-features-capabilities"]], "\ud83d\udcca Interactive Diagrams Created": [[0, "interactive-diagrams-created"]], "\ud83d\udcca Market Opportunity & Revenue Model": [[52, "market-opportunity-revenue-model"], [53, "market-opportunity-revenue-model"], [54, "market-opportunity-revenue-model"], [55, "market-opportunity-revenue-model"], [56, "market-opportunity-revenue-model"]], "\ud83d\udcca Migration Overview": [[22, "migration-overview"]], "\ud83d\udcca Mobile Analytics": [[44, "mobile-analytics"]], "\ud83d\udcca Mobile-Optimized Features": [[44, "mobile-optimized-features"]], "\ud83d\udcca Monitoring & Performance": [[27, "monitoring-performance"]], "\ud83d\udcca Performance Management": [[31, "performance-management"]], "\ud83d\udcca Performance Tracking": [[34, "performance-tracking"]], "\ud83d\udcca Professional Resources": [[32, "professional-resources"]], "\ud83d\udcca Progress Tracking & Analytics": [[47, "progress-tracking-analytics"]], "\ud83d\udcca Progress Tracking & Learning Analytics": [[58, "progress-tracking-learning-analytics"]], "\ud83d\udcca Progress Tracking Center": [[58, "progress-tracking-center"]], "\ud83d\udcca Progress Tracking System": [[38, "progress-tracking-system"]], "\ud83d\udcca Quality Assurance": [[25, "quality-assurance"]], "\ud83d\udcca Real-Time Analytics Dashboard": [[24, "real-time-analytics-dashboard"]], "\ud83d\udcca Research Methodologies": [[26, "research-methodologies"]], "\ud83d\udcca Revolutionary Platform Metrics": [[48, "revolutionary-platform-metrics"]], "\ud83d\udcca Test Reporting and Analytics": [[45, "test-reporting-and-analytics"]], "\ud83d\udcca Unified Dashboard Endpoints": [[16, "unified-dashboard-endpoints"]], "\ud83d\udcca Unified Dashboard User Guide": [[46, null]], "\ud83d\udcca Version 1.4.0 - Agent 3 Enterprise Analytics (2024-01-10)": [[17, "version-1-4-0-agent-3-enterprise-analytics-2024-01-10"]], "\ud83d\udccb Agent Documentation": [[51, "agent-documentation"]], "\ud83d\udccb Compliance & Governance": [[41, "compliance-governance"]], "\ud83d\udccb Documentation:": [[48, null]], "\ud83d\udccb Overview": [[23, "overview"], [25, "overview"]], "\ud83d\udccb Product Requirements Documents (PRDs)": [[57, null]], "\ud83d\udccb Product Requirements:": [[48, null]], "\ud83d\udccb Upcoming Features": [[17, "upcoming-features"]], "\ud83d\udcd6 Documentation Structure": [[1, "documentation-structure"]], "\ud83d\udcd6 User Guides:": [[48, null]], "\ud83d\udcda Additional Resources": [[16, "additional-resources"], [59, "additional-resources"]], "\ud83d\udcda Certifications Explorer": [[58, "certifications-explorer"]], "\ud83d\udcda Comprehensive Documentation": [[48, "comprehensive-documentation"]], "\ud83d\udcda Comprehensive Sphinx Documentation Update": [[0, "comprehensive-sphinx-documentation-update"]], "\ud83d\udcda Content Management": [[35, "content-management"]], "\ud83d\udcda Continuing Education": [[32, "continuing-education"]], "\ud83d\udcda Corporate Training Managers FAQ": [[34, null]], "\ud83d\udcda Curriculum Integration": [[26, "curriculum-integration"]], "\ud83d\udcda Documentation Overview": [[1, "documentation-overview"]], "\ud83d\udcda Learning Path (Next 7 Days)": [[58, "learning-path-next-7-days"]], "\ud83d\udcda Learning Strategy": [[28, "learning-strategy"]], "\ud83d\udcda Next Steps": [[46, "next-steps"]], "\ud83d\udcda Study Strategies": [[33, "study-strategies"]], "\ud83d\udcda User Guides Section": [[1, "user-guides-section"]], "\ud83d\udcdd Changelog": [[17, null]], "\ud83d\udcdd Content Update Process": [[25, "content-update-process"]], "\ud83d\udcdd Writing Documentation": [[1, "writing-documentation"], [23, "writing-documentation"]], "\ud83d\udcde Additional Resources for Students": [[33, "additional-resources-for-students"]], "\ud83d\udcde Enterprise Support": [[41, "enterprise-support"]], "\ud83d\udcde Still Need Help?": [[30, "still-need-help"]], "\ud83d\udcde Support": [[51, "support"]], "\ud83d\udcde Support & Feedback": [[17, "support-feedback"]], "\ud83d\udcde Support and Resources": [[25, "support-and-resources"], [43, "support-and-resources"]], "\ud83d\udcf1 Application Overview": [[43, "application-overview"]], "\ud83d\udcf1 Application Structure": [[22, "application-structure"]], "\ud83d\udcf1 Device-Specific Features": [[44, "device-specific-features"]], "\ud83d\udcf1 Mobile & PWA Architecture": [[38, "mobile-pwa-architecture"]], "\ud83d\udcf1 Mobile & Responsive Design": [[0, "mobile-responsive-design"]], "\ud83d\udcf1 Mobile Enterprise Platform": [[24, "mobile-enterprise-platform"]], "\ud83d\udcf1 Mobile Experience": [[43, "mobile-experience"], [46, "mobile-experience"], [47, "mobile-experience"]], "\ud83d\udcf1 Mobile Guide": [[44, null]], "\ud83d\udcf1 Progressive Web App": [[42, "progressive-web-app"]], "\ud83d\udcf1 Responsive Design Flow": [[39, "responsive-design-flow"]], "\ud83d\udcf2 Push Notifications": [[44, "push-notifications"]], "\ud83d\udd04 Application Flows & Architecture": [[38, null]], "\ud83d\udd04 Automatic Documentation Updates": [[51, "automatic-documentation-updates"]], "\ud83d\udd04 Backup & Recovery": [[18, "backup-recovery"]], "\ud83d\udd04 Career Changers FAQ": [[28, null]], "\ud83d\udd04 Commit-Based Documentation Updates": [[57, "commit-based-documentation-updates"]], "\ud83d\udd04 Data Enrichment": [[35, "data-enrichment"]], "\ud83d\udd04 Maintenance": [[23, "maintenance"]], "\ud83d\udd04 Maintenance and Updates": [[1, "maintenance-and-updates"]], "\ud83d\udd04 Next.js Application Flows": [[39, null]], "\ud83d\udd04 Offline Capabilities": [[44, "offline-capabilities"]], "\ud83d\udd04 Real-time Data Synchronization": [[38, "real-time-data-synchronization"]], "\ud83d\udd04 Regular Maintenance Tasks": [[25, "regular-maintenance-tasks"]], "\ud83d\udd04 State Management Flow": [[39, "state-management-flow"]], "\ud83d\udd04 Testing Flow Architecture": [[45, "testing-flow-architecture"]], "\ud83d\udd0d AI Insights & Analytics": [[37, "ai-insights-analytics"]], "\ud83d\udd0d Certification Explorer": [[43, "certification-explorer"]], "\ud83d\udd0d Certification Explorer Flow": [[39, "certification-explorer-flow"]], "\ud83d\udd0d Common Topics Across All User Types": [[30, "common-topics-across-all-user-types"]], "\ud83d\udd0d Insights Section": [[46, "insights-section"]], "\ud83d\udd0d Monitoring Configuration": [[18, "monitoring-configuration"]], "\ud83d\udd0d Quality Assurance": [[23, "quality-assurance"]], "\ud83d\udd0d SEO & Discoverability": [[0, "seo-discoverability"]], "\ud83d\udd0d Search & Discovery Engine": [[38, "search-discovery-engine"]], "\ud83d\udd10 Admin Access & Authentication": [[35, "admin-access-authentication"]], "\ud83d\udd10 Authentication": [[16, "authentication"]], "\ud83d\udd10 Authentication & Security": [[47, "authentication-security"], [58, "authentication-security"], [59, "authentication-security"]], "\ud83d\udd10 Authentication Flow": [[38, "authentication-flow"], [39, "authentication-flow"]], "\ud83d\udd10 Authentication Flow Implementation": [[42, "authentication-flow-implementation"]], "\ud83d\udd10 Authentication Integration": [[22, "authentication-integration"]], "\ud83d\udd10 Authentication System": [[43, "authentication-system"]], "\ud83d\udd10 Mobile Security": [[44, "mobile-security"]], "\ud83d\udd12 Privacy-First Architecture": [[48, "privacy-first-architecture"]], "\ud83d\udd12 Risk Mitigation": [[52, "risk-mitigation"], [53, "risk-mitigation"]], "\ud83d\udd12 Security & Compliance": [[27, "security-compliance"]], "\ud83d\udd12 Security & Governance": [[29, "security-governance"]], "\ud83d\udd12 Security Architecture": [[38, "security-architecture"]], "\ud83d\udd12 Security Testing Framework": [[45, "security-testing-framework"]], "\ud83d\udd12 Security and Privacy": [[43, "security-and-privacy"]], "\ud83d\udd17 API Integration for Developers": [[58, "api-integration-for-developers"]], "\ud83d\udd17 API Reference Section": [[1, "api-reference-section"]], "\ud83d\udd17 API Reference:": [[48, null]], "\ud83d\udd17 Base URL": [[16, "base-url"]], "\ud83d\udd17 Enterprise Integration Hub": [[50, "enterprise-integration-hub"]], "\ud83d\udd17 Enterprise Integrations": [[24, "enterprise-integrations"], [41, "enterprise-integrations"]], "\ud83d\udd17 Essential Documentation": [[58, "essential-documentation"]], "\ud83d\udd17 Integration & Isolation Strategy": [[52, "integration-isolation-strategy"]], "\ud83d\udd17 Integration Architecture": [[57, "integration-architecture"]], "\ud83d\udd17 Integration Strategy": [[53, "integration-strategy"], [56, "integration-strategy"]], "\ud83d\udd17 Integration Testing Flow": [[45, "integration-testing-flow"]], "\ud83d\udd17 Unified Platform Integration": [[59, null]], "\ud83d\udd17 Unified Services": [[59, "unified-services"]], "\ud83d\udd27 Advanced AI Configuration": [[37, "advanced-ai-configuration"]], "\ud83d\udd27 Advanced Features": [[1, "advanced-features"], [23, "advanced-features"], [43, "advanced-features"], [47, "advanced-features"]], "\ud83d\udd27 Configuration": [[51, "configuration"]], "\ud83d\udd27 Configuration Details": [[23, "configuration-details"]], "\ud83d\udd27 Configuration Management": [[27, "configuration-management"]], "\ud83d\udd27 Customization Options": [[46, "customization-options"]], "\ud83d\udd27 Development Guide": [[22, "development-guide"]], "\ud83d\udd27 Development Workflow": [[39, "development-workflow"]], "\ud83d\udd27 Environment Configuration": [[18, "environment-configuration"]], "\ud83d\udd27 Integration & Customization": [[29, "integration-customization"]], "\ud83d\udd27 Migration Process": [[22, "migration-process"]], "\ud83d\udd27 Program Operations": [[34, "program-operations"]], "\ud83d\udd27 System Administrators FAQ": [[27, null]], "\ud83d\udd27 System Maintenance": [[35, "system-maintenance"]], "\ud83d\udd27 Technical Development": [[28, "technical-development"], [32, "technical-development"]], "\ud83d\udd27 Technical Implementation": [[2, "technical-implementation"]], "\ud83d\udd27 Technical Improvements": [[0, "technical-improvements"]], "\ud83d\udd27 Technical Leadership": [[31, "technical-leadership"]], "\ud83d\udd27 Technical Maintenance": [[25, "technical-maintenance"]], "\ud83d\udd27 Technical Questions": [[33, "technical-questions"]], "\ud83d\udd27 Technical Requirements": [[52, "technical-requirements"]], "\ud83d\udd27 Troubleshooting & Tips": [[44, "troubleshooting-tips"]], "\ud83d\udd2c Research Applications": [[26, "research-applications"]], "\ud83d\udd2c Research Resources": [[26, "research-resources"]], "\ud83d\udd2e Predictive Analytics": [[37, "predictive-analytics"]], "\ud83d\udda5\ufe0f Web Interface Mastery": [[58, "web-interface-mastery"]], "\ud83d\uddc4\ufe0f Database Management": [[27, "database-management"]], "\ud83d\ude80 Advanced AI Features": [[48, "advanced-ai-features"]], "\ud83d\ude80 Advanced API Features": [[58, "advanced-api-features"]], "\ud83d\ude80 Agent Documentation": [[57, "agent-documentation"]], "\ud83d\ude80 Build and Deployment": [[23, "build-and-deployment"]], "\ud83d\ude80 Building Documentation": [[51, "building-documentation"]], "\ud83d\ude80 Career Advancement": [[32, "career-advancement"]], "\ud83d\ude80 Career Transition Workflow": [[58, "career-transition-workflow"]], "\ud83d\ude80 Deployment": [[1, "deployment"]], "\ud83d\ude80 Deployment Options": [[41, "deployment-options"]], "\ud83d\ude80 Deployment and Production": [[22, "deployment-and-production"]], "\ud83d\ude80 Frontend Migration to Next.js 14": [[22, null]], "\ud83d\ude80 Future Enhancements": [[25, "future-enhancements"]], "\ud83d\ude80 Getting Started": [[28, "getting-started"], [33, "getting-started"], [46, "getting-started"], [59, "getting-started"]], "\ud83d\ude80 Getting Started in 5 Minutes": [[47, "getting-started-in-5-minutes"]], "\ud83d\ude80 Getting Started with AI Features": [[2, "getting-started-with-ai-features"]], "\ud83d\ude80 Getting Started:": [[48, null]], "\ud83d\ude80 Immediate Actions (Next 24 Hours)": [[58, "immediate-actions-next-24-hours"]], "\ud83d\ude80 Implementation Guide": [[24, "implementation-guide"]], "\ud83d\ude80 Implementation Roadmap": [[52, "implementation-roadmap"], [53, "implementation-roadmap"], [56, "implementation-roadmap"]], "\ud83d\ude80 Installation & Setup": [[27, "installation-setup"]], "\ud83d\ude80 Next.js 14 Frontend User Guide": [[43, null]], "\ud83d\ude80 Performance Optimization": [[18, "performance-optimization"]], "\ud83d\ude80 Platform Overview": [[50, null]], "\ud83d\ude80 Production Deployment Status": [[55, "production-deployment-status"]], "\ud83d\ude80 Quick Setup": [[23, "quick-setup"]], "\ud83d\ude80 Quick Start": [[1, "quick-start"]], "\ud83d\ude80 Quick Start Guide": [[58, null]], "\ud83d\udee0\ufe0f Administrative Tools": [[27, "administrative-tools"]], "\ud83d\udee0\ufe0f Developer Features": [[43, "developer-features"]], "\ud83d\udee0\ufe0f Development Section": [[1, "development-section"]], "\ud83d\udee0\ufe0f Development:": [[48, null]], "\ud83d\udee0\ufe0f FAQ Documentation Maintenance Guide": [[25, null]], "\ud83d\udee0\ufe0f Maintenance": [[51, "maintenance"]], "\ud83d\udee0\ufe0f Sphinx Documentation Setup Guide": [[23, null]], "\ud83d\udee1\ufe0f CertPathFinder Documentation": [[48, null]], "\ud83d\udee1\ufe0f Error Handling Flow": [[39, "error-handling-flow"]], "\ud83d\udee1\ufe0f Security & Compliance": [[57, "security-compliance"]], "\ud83e\udd16 AI & Analytics:": [[48, null]], "\ud83e\udd16 AI Configuration": [[18, "ai-configuration"]], "\ud83e\udd16 AI Features Guide": [[37, null]], "\ud83e\udd16 AI Recommendation Engine": [[38, "ai-recommendation-engine"]], "\ud83e\udd16 AI Study Assistant": [[2, null], [47, "ai-study-assistant"], [58, "ai-study-assistant"]], "\ud83e\udd16 AI Study Assistant - Your Intelligent Learning Companion": [[58, "ai-study-assistant-your-intelligent-learning-companion"]], "\ud83e\udd16 Agent 2: AI Study Assistant": [[53, null]], "\ud83e\udd16 Technical Requirements": [[53, "technical-requirements"]], "\ud83e\udd16 Unified Intelligence API": [[16, null]], "\ud83e\udd16 Unified Intelligence Endpoints": [[16, "unified-intelligence-endpoints"]], "\ud83e\udd16 Version 1.3.0 - Agent 2 AI Study Assistant (2024-01-05)": [[17, "version-1-3-0-agent-2-ai-study-assistant-2024-01-05"]], "\ud83e\udd1d Collaboration Guidelines": [[25, "collaboration-guidelines"]], "\ud83e\udd1d Contributing to Documentation": [[1, "contributing-to-documentation"]], "\ud83e\udde0 AI Features & Capabilities": [[53, "ai-features-capabilities"]], "\ud83e\udde0 Advanced Machine Learning Models": [[2, "advanced-machine-learning-models"]], "\ud83e\udde0 Revolutionary On-Device AI": [[37, "revolutionary-on-device-ai"]], "\ud83e\uddea Comprehensive Testing Guide": [[45, null]], "\ud83e\uddea Comprehensive Testing Implementation": [[55, "comprehensive-testing-implementation"]], "\ud83e\uddea Comprehensive Testing Strategy": [[42, "comprehensive-testing-strategy"]], "\ud83e\uddea Development & Testing Workflow": [[52, "development-testing-workflow"], [53, "development-testing-workflow"]], "\ud83e\uddea Testing and Quality": [[22, "testing-and-quality"]], "\ud83e\uddea Unit Testing Strategy": [[45, "unit-testing-strategy"]]}, "docnames": ["DOCUMENTATION_ENHANCEMENT_SUMMARY", "README", "ai/study_assistant", "api/agent3-enterprise-analytics", "api/agent4-api-reference", "api/ai_assistant", "api/authentication", "api/authentication-system", "api/career_framework", "api/cost_calculator", "api/enterprise", "api/index", "api/integration_hub", "api/organization-management", "api/progress_tracking", "api/study-session-tracking", "api/unified_intelligence", "changelog", "configuration", "development/ai_models", "development/architecture", "development/contributing", "development/frontend_migration", "development/sphinx-setup", "enterprise/dashboard", "faq/MAINTENANCE_GUIDE", "faq/academics_researchers", "faq/administrators", "faq/career_changers", "faq/enterprise_admins", "faq/index", "faq/managers", "faq/professionals", "faq/students", "faq/training_managers", "guides/admin_guide", "guides/agent4_user_guide", "guides/ai_features_guide", "guides/application_flows", "guides/application_flows_nextjs", "guides/complete-platform-integration", "guides/enterprise_guide", "guides/frontend_implementation", "guides/frontend_nextjs_guide", "guides/mobile_guide", "guides/testing_guide", "guides/unified_dashboard_guide", "guides/user_guide", "index", "installation", "platform_overview", "prds/README", "prds/agent-1-core-platform-engine", "prds/agent-2-ai-study-assistant", "prds/agent-3-enterprise-analytics-engine", "prds/agent-4-career-cost-intelligence", "prds/agent-5-marketplace-integration-hub", "prds/index", "quickstart", "unified_platform"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["DOCUMENTATION_ENHANCEMENT_SUMMARY.md", "README.md", "ai/study_assistant.rst", "api/agent3-enterprise-analytics.rst", "api/agent4-api-reference.rst", "api/ai_assistant.rst", "api/authentication.rst", "api/authentication-system.rst", "api/career_framework.rst", "api/cost_calculator.rst", "api/enterprise.rst", "api/index.rst", "api/integration_hub.rst", "api/organization-management.rst", "api/progress_tracking.rst", "api/study-session-tracking.rst", "api/unified_intelligence.rst", "changelog.rst", "configuration.rst", "development/ai_models.rst", "development/architecture.rst", "development/contributing.rst", "development/frontend_migration.rst", "development/sphinx-setup.rst", "enterprise/dashboard.rst", "faq/MAINTENANCE_GUIDE.md", "faq/academics_researchers.rst", "faq/administrators.rst", "faq/career_changers.rst", "faq/enterprise_admins.rst", "faq/index.rst", "faq/managers.rst", "faq/professionals.rst", "faq/students.rst", "faq/training_managers.rst", "guides/admin_guide.rst", "guides/agent4_user_guide.rst", "guides/ai_features_guide.rst", "guides/application_flows.rst", "guides/application_flows_nextjs.rst", "guides/complete-platform-integration.rst", "guides/enterprise_guide.rst", "guides/frontend_implementation.rst", "guides/frontend_nextjs_guide.rst", "guides/mobile_guide.rst", "guides/testing_guide.rst", "guides/unified_dashboard_guide.rst", "guides/user_guide.rst", "index.rst", "installation.rst", "platform_overview.rst", "prds/README.md", "prds/agent-1-core-platform-engine.rst", "prds/agent-2-ai-study-assistant.rst", "prds/agent-3-enterprise-analytics-engine.rst", "prds/agent-4-career-cost-intelligence.rst", "prds/agent-5-marketplace-integration-hub.rst", "prds/index.rst", "quickstart.rst", "unified_platform.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [1, 5, 11, 19, 20, 21, 24, 27, 28, 29, 31, 32, 33, 34, 37, 41, 44, 45, 48, 50, 51, 52, 57, 58], "0": [1, 3, 4, 5, 7, 8, 9, 10, 12, 13, 14, 15, 18, 19, 21, 22, 23, 29, 36, 40, 41, 45, 46, 48, 52, 53, 54, 55], "00": [3, 5, 6, 7, 9, 10, 12, 13, 14, 15, 21], "000": [4, 24, 28, 31, 36, 48, 50, 52, 53, 55, 56], "001": [3, 10], "0047": 14, "00z": [3, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 59], "01": [5, 6, 8, 9, 10, 12, 13, 14, 16, 45, 48, 59], "01t00": [10, 12, 13, 14], "01t10": 14, "02": [3, 12], "03": [5, 14], "03b": [52, 57], "04": [14, 21, 55], "05": [7, 9, 14, 48, 55], "06": [3, 5, 7, 10, 13, 14, 15, 55], "07": [3, 12, 14], "08": [3, 8, 9, 10, 14, 15], "09": [5, 14, 15], "1": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 31, 32, 33, 34, 36, 39, 40, 42, 43, 44, 45, 46, 47, 48, 50, 51, 53, 54, 55, 56, 57, 58, 59], "10": [1, 2, 4, 5, 6, 8, 9, 10, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 31, 32, 33, 34, 36, 48, 49, 50, 52, 53, 54, 55, 56, 58], "100": [2, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 18, 19, 22, 23, 36, 37, 46, 47, 48, 50, 52, 54, 55, 56, 58], "1000": [4, 10, 11, 13, 14, 18, 39, 52, 54, 55], "10000": [8, 11], "100000": [4, 40], "100gb": 27, "100k": 57, "100m": [24, 43, 48, 50, 52, 57], "100mb": 18, "1024": 45, "10gb": 49, "10k": 54, "10m": [51, 54, 55, 57], "10mb": 18, "10t15": 14, "10x": 52, "11": [5, 10, 12, 14, 22, 26, 27, 28, 29, 31, 32, 33, 34, 40, 48, 56], "110000": 8, "11111": [3, 13], "11223344": 7, "11347": 9, "12": [2, 3, 4, 5, 8, 9, 10, 12, 13, 14, 15, 26, 27, 28, 29, 31, 32, 33, 34, 36, 48, 52, 53, 54, 56, 58], "120": [5, 8, 9, 15, 45], "12000": [8, 9], "1200000": 3, "123": [1, 5, 6, 8, 9, 10, 12, 14, 23], "12345": [3, 7, 13, 15], "123456": 7, "12345678": 7, "125": 3, "1250": [5, 8, 10, 12, 13], "12500": [3, 9], "125000": [3, 4, 10], "12890": 10, "129": 9, "12m": [9, 51, 53, 56, 57], "12month": 4, "13": [9, 20, 21, 26, 27, 28, 29, 31, 32, 33, 34, 49, 55], "14": [3, 4, 5, 8, 9, 15, 26, 27, 28, 29, 31, 32, 33, 34, 39, 48, 54], "140": 5, "1400": 4, "1400000": 3, "145": [3, 9, 12, 13, 14], "1450": 15, "14t15": 14, "15": [3, 4, 5, 8, 9, 10, 12, 13, 14, 15, 20, 26, 27, 28, 29, 31, 32, 33, 34, 36, 44, 48, 52, 53, 54, 55, 56, 58], "150": [4, 9, 11, 19, 23, 52, 56], "1500": [10, 21, 54], "15000": [3, 4, 8, 9], "150000": [3, 8], "1500000": 3, "150k": 52, "151": 12, "15129": 9, "15420": 10, "156": [9, 10], "15k": 55, "15m": [51, 52, 54, 57], "15t00": 10, "15t02": 12, "15t09": [10, 12], "15t10": [5, 6, 7, 8, 9, 10, 12, 13, 14], "15t11": [6, 14], "15t16": [9, 15], "15t23": [10, 12, 14], "16": [3, 4, 5, 8, 13, 15, 26, 27, 28, 29, 31, 32, 33, 34, 36, 48, 55], "16000": 9, "1625": 9, "1640995200": 11, "1642694400": 6, "165": 9, "167": 10, "168": [7, 10, 13], "16t02": 12, "16t09": 3, "16t10": [7, 16, 59], "16t14": [7, 13, 15], "16t15": [7, 13, 15], "16t16": [7, 13], "16t17": 13, "16t18": 3, "16t23": 13, "17": [14, 26, 27, 28, 29, 31, 32, 33, 34], "174": 9, "175000": 8, "17t00": 13, "18": [4, 8, 9, 13, 14, 20, 21, 22, 26, 27, 28, 29, 31, 32, 33, 34, 40, 56], "180": [4, 15], "1800": [18, 45], "180deg": 23, "180k": 54, "181": 14, "1823": 9, "185": [3, 4], "18500": 9, "1875": 9, "18750": 9, "18f6ae4": 54, "18m": [51, 54, 57], "19": [3, 14, 15, 26, 27, 28, 29, 31, 32, 33, 34, 46], "192": [7, 10, 13], "195": 4, "198": 10, "199": 36, "1b": 52, "1e9f20b": 56, "1y": [3, 13, 15], "2": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56, 57, 58], "20": [0, 2, 3, 5, 6, 9, 10, 11, 13, 18, 19, 26, 27, 28, 29, 31, 32, 33, 34, 37, 46, 48, 52, 53, 55, 56], "200": [1, 3, 4, 6, 7, 8, 9, 10, 11, 14, 16, 23, 33, 36, 47, 52, 54, 57, 58], "2000": [4, 9], "20000": [4, 9], "200000": 3, "200k": 54, "200m": [52, 54, 57], "201": [11, 58], "202": 9, "2023": 48, "2024": [9, 10, 14, 16, 45, 48, 59], "2025": [1, 3, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 23, 25, 30, 55], "2026": [10, 13], "20gb": 49, "20m": 57, "20px": 23, "21": [3, 14, 15, 26, 27, 28, 29, 31, 32, 33, 34], "211": 10, "2157": 9, "22": [21, 26, 27, 28, 29, 31, 32, 33, 34], "220": 3, "22000": 9, "22222": 13, "225": [3, 9], "2296": 10, "23": [9, 10, 12, 14, 26, 27, 28, 29, 31, 32, 33, 34], "230": [3, 9, 13], "2300": 4, "233": 8, "234": 10, "2350": 14, "23500": 10, "23t16": 13, "23t23": 15, "24": [4, 7, 8, 13, 15, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 41, 46], "240": 9, "245": [3, 4, 10], "25": [2, 3, 4, 5, 9, 14, 15, 17, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 48, 50, 52, 53, 54, 55, 56, 59], "250": [4, 9, 13], "2500": [4, 9, 10, 45], "250k": 52, "255": 9, "2555": [10, 13, 18, 40], "256": [2, 20, 24, 41, 48, 50, 52], "25m": 56, "26": 25, "27": 25, "2708": 3, "2745": 9, "27ae60": 23, "28": [8, 10, 14], "2800": 10, "28000": 9, "285": [3, 4], "2875": 13, "28t14": 14, "29": 52, "2980b9": [1, 23], "299": 36, "2b": [53, 55, 56], "2c3e50": 23, "2fa": [20, 35, 41], "2gb": 49, "2m": [53, 56], "2px": 23, "3": [1, 2, 4, 5, 8, 9, 10, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 31, 32, 33, 34, 36, 39, 43, 44, 46, 48, 49, 51, 52, 53, 55, 56, 57, 58], "30": [2, 3, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 18, 22, 24, 32, 39, 40, 48, 49, 50, 52, 53, 55, 56, 58, 59], "300": [4, 9, 10, 24, 33, 36, 52], "3000": [4, 18, 22, 43, 47], "300k": 57, "30d": [3, 10, 13, 14, 15, 40], "31": [9, 54], "310": 4, "312": 10, "315": 10, "31536000": 18, "315b": 56, "320": [3, 4, 15], "320000": 3, "3250": 9, "32500": 3, "325000": 13, "33": 10, "33333": 13, "3420000": 3, "34495e": 23, "3498db": 23, "35": [2, 3, 8, 10, 13, 23, 52], "3500": 8, "35000": [4, 8], "36": [36, 52, 53, 54, 55, 56], "3600": [6, 7, 18], "365": [15, 18], "366b": [53, 54, 55], "3750": 9, "377": 10, "3782": 9, "39": 46, "3g": 52, "3k": 55, "3m": [52, 54], "3x": 53, "4": [1, 3, 5, 8, 9, 12, 13, 14, 15, 18, 20, 22, 23, 25, 26, 27, 28, 29, 31, 32, 33, 34, 39, 42, 43, 44, 46, 47, 48, 49, 51, 52, 53, 54, 56, 57, 58], "40": [2, 3, 5, 24, 33, 46, 52, 53], "400": [4, 7, 8, 9, 11, 12, 14, 16], "40000": 4, "400000": 3, "401": [1, 4, 6, 7, 11, 12, 16, 23], "403": [6, 7, 10, 11, 12], "404": [1, 4, 8, 9, 10, 11, 14, 16, 23], "409": [10, 14], "41d4": 16, "42": [2, 3, 19], "420": [14, 15], "4200": 8, "4200000": 3, "420m": 52, "422": [5, 6, 8, 9, 10, 11, 12, 14, 16], "429": [4, 5, 6, 7, 8, 9, 10, 11], "4314": 9, "4315": 9, "446655440000": 16, "45": [2, 3, 5, 7, 8, 9, 10, 12, 13, 14, 15, 33, 55, 58], "450": [4, 8], "4500": 4, "45000": [8, 10], "450000": 8, "456": 10, "45670": 9, "45b": [54, 56], "45m": 57, "465": [48, 52, 58], "46e260b": 52, "47": 10, "4750": 4, "480": [10, 12], "4800": 3, "480000": 3, "4875": 9, "49": 22, "4gb": [27, 49], "4px": 23, "5": [1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 18, 19, 20, 22, 23, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 39, 44, 45, 46, 48, 51, 52, 53, 54, 55, 57], "50": [2, 10, 13, 14, 15, 18, 22, 23, 24, 28, 33, 36, 42, 45, 48, 50, 52, 53, 54, 55, 56, 57], "500": [4, 7, 8, 9, 10, 11, 12, 14, 16, 24, 33, 36, 43, 52, 54, 55, 56, 57], "5000": [4, 9, 40, 54, 59], "50000": [3, 10, 13], "500000": [3, 10, 13, 40], "500k": [55, 56, 57], "500m": [17, 43, 52, 53], "500mb": 18, "503": [5, 12], "50gb": [27, 49], "50k": [52, 54, 55], "50m": [56, 57], "50mb": 45, "513": 22, "52": 3, "5200": 3, "525000": 8, "52m": 57, "5432": [18, 49], "54321": [3, 13], "54322": 3, "55": [3, 8, 14], "55000": 8, "550e8400": 16, "58": [3, 14, 15], "58000": 8, "587": [18, 49], "588": 3, "59": [10, 12, 13, 14, 15, 46], "59z": [10, 12, 13, 14, 15], "5b": 54, "5gb": 27, "5m": [53, 54, 55], "6": [1, 3, 5, 8, 9, 12, 14, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 41, 42, 48, 49, 50, 52, 53, 55, 56, 57, 58], "60": [2, 3, 5, 12, 14, 15, 18, 19, 24, 33, 39, 40, 46, 53, 55, 56], "600": 9, "62000": 8, "625": 9, "6250": 9, "63": 14, "6379": [18, 40, 49], "64": 10, "6472": 9, "65": [3, 8, 9, 13, 14, 15, 19, 23], "650": 14, "6500": [8, 9], "65000": 9, "650000": 3, "67": [8, 9, 10, 14, 22], "67890": 13, "67m": 57, "68": [5, 13, 14], "68000": [8, 9], "6_months_ago": 8, "6b": [54, 55], "6m": [53, 56], "6month": 3, "7": [4, 5, 8, 9, 10, 13, 14, 15, 18, 19, 20, 22, 26, 27, 28, 29, 31, 32, 33, 34, 41, 48, 52, 53, 55, 56], "70": [12, 15, 19, 52], "700": 8, "70000": [8, 9], "72": [5, 8, 14], "720": 10, "72000": 8, "720000": 3, "7234": 9, "73": [8, 10], "73500": 9, "749": [9, 21, 43], "75": [5, 8, 9, 14, 15, 18, 19, 24, 43, 53, 55], "750": [14, 55], "7500": [8, 9], "75000": [3, 4, 8, 10], "750000": 3, "751": 21, "755": 10, "75500": 8, "76": [10, 14], "768px": 23, "78": [2, 3, 5, 8, 9, 10, 13, 14, 15, 57], "78000": [8, 9], "7850": 9, "789": 10, "79": [5, 46, 52], "7d": [3, 13, 15], "7xl": 42, "8": [2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 21, 23, 26, 27, 28, 29, 31, 32, 33, 34, 42, 45, 47, 48, 49, 52, 53, 54, 55, 57, 58], "80": [1, 2, 5, 8, 12, 14, 15, 18, 22, 24, 28, 37, 46, 48, 50, 52, 55, 57], "800": [4, 33], "8000": [6, 8, 11, 18, 22, 40, 47, 49, 58], "80000": 3, "800000": 3, "8080": 1, "8140": 32, "82": [2, 3, 4, 5, 13, 14], "8240": 9, "825": 14, "84": [5, 14], "85": [2, 3, 4, 5, 7, 8, 9, 10, 12, 13, 14, 15, 17, 18, 24, 33, 36, 37, 48, 50, 53, 55, 58, 59], "850": 3, "8500": [4, 8, 9], "85000": 8, "8501": [18, 49, 58], "8570": [32, 33], "8629": 9, "87": [5, 22, 43, 48], "875": 3, "8750": 9, "87654321": 7, "88": [3, 4, 13, 14, 17, 43, 48, 55, 59], "89": [3, 4, 8, 9, 10, 12, 13, 14], "890": 10, "8b": [53, 55, 56], "8gb": [27, 49], "8k": 55, "8m": [52, 53, 54, 56], "8px": 23, "9": [1, 3, 4, 5, 8, 9, 10, 12, 14, 23, 26, 27, 28, 29, 31, 32, 33, 34, 52, 54, 55, 56, 57], "90": [2, 4, 7, 13, 14, 15, 18, 19, 24, 43, 52, 53, 54, 55, 56, 57], "9090": 18, "90d": [3, 13, 14, 15], "91": [3, 7, 14], "92": [4, 5, 9, 12, 13, 15, 48, 55], "9250": 9, "92500": 8, "9285": 13, "93": 3, "94": [3, 8], "95": [2, 3, 6, 8, 9, 10, 13, 15, 24, 48, 54, 55, 56], "95000": 8, "95th": 52, "96": 3, "97": [8, 55], "98": [9, 12, 13], "98000": 8, "99": [2, 3, 4, 24, 36, 52, 55, 57], "995": 12, "999": 11, "A": [1, 4, 5, 17, 21, 23, 26, 36, 48, 51, 52, 53, 55, 57, 59], "AND": 6, "And": [42, 45], "As": [21, 33, 42, 45], "Be": 21, "By": [25, 53, 56], "For": [6, 17, 21, 31, 32, 33, 43, 44, 46, 47, 48, 49, 51, 58], "IT": [10, 12, 13, 28, 30, 33, 40, 41, 54], "If": [21, 30, 31, 32, 33], "In": [42, 43, 45, 51, 52, 54, 57], "It": [8, 10, 14, 28, 32, 33, 47], "No": [2, 5, 19, 20, 25, 37, 44, 47, 48, 50, 53, 55], "Not": [4, 5, 11, 16, 44, 46], "On": [2, 7, 11, 13, 17, 18, 21, 24, 30, 35, 41, 44, 47, 48, 49, 50, 51, 53, 55, 57, 58, 59], "One": [43, 44], "Or": [1, 49], "That": 37, "The": [0, 1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 22, 23, 24, 28, 35, 36, 39, 43, 45, 46, 47, 48, 49, 51, 52, 53, 54, 56, 57, 58, 59], "Then": [42, 45], "These": [31, 32], "To": 46, "WITH": 53, "With": [40, 44, 50], "_": [1, 23], "__init__": [2, 6, 19, 20, 40], "_analyse_factor": 19, "_analyse_prefer": 19, "_anonymise_data": 19, "_build": [1, 23, 51], "_calculate_confid": 19, "_calculate_perform": 19, "_calculate_style_confid": 19, "_cluster_to_styl": 19, "_collaborative_filtering_recommend": 19, "_combine_recommend": 19, "_content_based_recommend": 19, "_estimate_study_tim": 19, "_extract_featur": 19, "_extract_learning_featur": 19, "_filter_by_d": 19, "_get_key_factor": 19, "_get_style_recommend": 19, "_get_user_knowledg": 19, "_optimise_sequ": 19, "_prepare_featur": 19, "_prepare_training_data": 19, "_save_model": 19, "_score_to_level": 19, "_static": [1, 23], "_templat": [1, 23], "_trigger_retrain": 19, "_validate_model": 19, "a716": 16, "aa": [0, 22, 42, 43, 45, 47, 52], "abc123": 12, "abet": 26, "abil": 15, "abl": [42, 45], "about": [1, 8, 17, 28, 30, 31, 47, 55], "absolut": [28, 32, 33], "ac": 18, "academ": [1, 25, 30, 33, 48], "academi": 5, "academics_research": [1, 25], "acceler": [9, 28, 37, 44, 46, 47, 58], "accent": [22, 23], "accept": [19, 21, 31, 43], "access": [1, 2, 4, 5, 10, 11, 12, 13, 14, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 34, 36, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 54, 56, 57, 58, 59], "access_ev": 10, "access_polici": 13, "access_token": [3, 6, 7, 11, 13, 15, 40, 58], "access_token_expire_minut": [18, 40, 49], "accessibilityscanresult": [42, 45], "accesstoken": 40, "accommod": [9, 55], "accomplish": 46, "accordingli": [19, 33, 42], "account": [5, 10, 17, 24, 28, 29, 30, 32, 35, 36, 40, 41, 43, 47], "accredit": [35, 48], "accur": [5, 8, 9, 25, 36, 47, 51, 53], "accuraci": [1, 2, 17, 19, 23, 25, 35, 37, 47, 48, 50, 51, 53, 55, 58, 59], "accuracy_percentag": 14, "accuracy_scor": 19, "achiev": [2, 8, 11, 12, 15, 22, 24, 28, 30, 31, 32, 34, 36, 37, 43, 44, 47, 48, 50, 52, 53, 58, 59], "achievement_detail": 12, "achievement_id": 14, "achievement_summari": 14, "achievements_unlock": 14, "acknowledg": [46, 47], "acm": [3, 10, 13], "acquisit": [37, 46, 52], "across": [2, 3, 5, 9, 10, 13, 15, 17, 22, 24, 27, 29, 31, 32, 33, 34, 35, 36, 37, 39, 41, 44, 45, 46, 47, 48, 50, 51, 52, 54, 55, 56, 57, 58, 59], "acs_url": 18, "act": 46, "action": [0, 1, 7, 10, 13, 15, 23, 25, 27, 35, 42, 43, 44, 48, 53, 54], "action_typ": 13, "actionable_step": 59, "activ": [2, 8, 10, 12, 13, 14, 15, 17, 21, 22, 24, 26, 27, 29, 32, 34, 35, 37, 41, 42, 43, 44, 46, 47, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59], "active_certif": [10, 13], "active_go": 58, "active_learn": [3, 10], "active_lic": 10, "active_memb": 13, "active_onli": 12, "active_recal": 14, "active_sess": 12, "active_us": [3, 13], "active_users_30d": 10, "activities_complet": 15, "activities_count": 15, "actormargin": 23, "actual": [21, 51, 55], "actual_duration_minut": 14, "actual_result": 19, "actual_roi": 9, "acumen": [8, 28], "acv": 54, "ad": [0, 10, 12, 14, 24, 29, 30, 36, 43, 54], "adapt": [0, 2, 5, 17, 19, 20, 35, 41, 42, 43, 44, 47, 48, 50, 51, 53, 56, 58, 59], "adaptation_r": 18, "adaptive_learn": 18, "add": [1, 5, 10, 14, 21, 25, 26, 28, 33, 35, 36, 40, 42, 43, 44, 51, 52, 53, 54, 56], "add_practice_quest": 5, "add_practice_test_sess": 14, "add_user_to_organ": 40, "add_user_to_team": 40, "added_at": 13, "added_memb": 13, "addeventlisten": 23, "addit": [3, 9, 13, 18, 29, 30, 31, 34, 36, 39, 41, 43, 44, 47, 48, 56], "additional_book": 9, "address": [21, 25, 26, 28, 29, 31, 32, 34, 36, 37, 42, 43, 44, 52], "adequ": 34, "adjust": [5, 15, 31, 33, 34, 36, 37, 44, 46, 47, 51, 53, 55, 58], "adjusted_recommend": 5, "admin": [1, 6, 7, 12, 13, 18, 20, 25, 27, 41, 48, 54], "admin_guid": [1, 27, 31], "admin_token": [7, 10, 12, 13], "administr": [1, 2, 5, 6, 10, 12, 20, 24, 25, 30, 40, 41, 48, 58], "admonit": [1, 23], "adopt": [24, 29, 32, 35, 41, 52, 53], "advanc": [0, 3, 4, 5, 6, 8, 9, 10, 13, 14, 17, 18, 19, 20, 22, 25, 26, 28, 30, 31, 33, 34, 35, 38, 40, 41, 42, 44, 46, 49, 50, 52, 53, 54, 55, 56, 59], "advanced_analyt": 10, "advanced_certif": 4, "advantag": [31, 34, 37, 41, 50, 53, 58], "advantage_level": [5, 8], "advic": [25, 47], "advisor": [32, 33], "advisori": 26, "advocaci": 35, "ae": [2, 20, 24, 41, 48, 50, 52], "affect": [8, 31, 44, 51, 57], "affected_team": 3, "after": [1, 4, 6, 7, 15, 21, 25, 34, 46, 49, 58], "after_each_cert": 9, "afternoon": 15, "against": [2, 8, 34, 37, 47], "agenc": 26, "agent": 48, "agent 3": 3, "agent 4": [4, 36], "agent3": [3, 40], "agent3_analyt": 40, "agent3_enterprise_analyt": 40, "agent3_rout": 40, "agent4_car": 40, "agent4navig": 55, "aggreg": [16, 17, 35, 41, 46, 54, 59], "aggregate_onli": 18, "aggress": [9, 37, 52], "agreement": [26, 29, 35, 41, 56], "ahead": 14, "ai": [0, 1, 3, 6, 8, 14, 15, 16, 22, 23, 24, 27, 30, 32, 33, 35, 36, 40, 43, 44, 46, 51, 52, 55, 56], "ai_assist": [1, 2, 10], "ai_cache_s": 18, "ai_config": 27, "ai_featur": 18, "ai_features_guid": [1, 32], "ai_insight": [5, 14, 58], "ai_model": [1, 18], "ai_model_path": [18, 27], "ai_prediction_confidence_threshold": 18, "ai_process": 18, "ai_recommend": 14, "ai_study_assist": [2, 20], "aid": 37, "aim": 33, "aimodeltrainingpipelin": 19, "aiohttp": 40, "air": 41, "airecommendationev": 53, "aistudyassistantservic": 2, "alemb": [21, 40, 49], "alert": [3, 7, 18, 24, 25, 27, 29, 32, 35, 41, 44, 46, 47, 52, 54, 55, 57], "alert_123": 3, "alert_id": 3, "alert_threshold": 18, "algorithm": [4, 6, 17, 18, 35, 36, 48, 49, 52, 53, 55, 57, 59], "alic": [3, 10, 13], "align": [2, 5, 23, 26, 31, 32, 34, 36, 37, 41, 46, 51], "all": [0, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], "all_team": 13, "alloc": [4, 10, 17, 24, 31, 35, 36, 37, 41, 46, 47, 48, 50, 54, 55], "allocated_budget": [3, 10], "allocation_request": 54, "allow": [20, 32, 35, 46], "allow_mobile_access": 13, "allowed_domain": [10, 13], "allowed_group": 12, "allowed_ip_rang": 13, "allowlist": 35, "alongsid": 33, "alpin": [1, 20], "alreadi": [14, 28, 30, 47, 49], "also": [11, 33, 48], "altern": [0, 18, 31, 34, 36, 58], "alternative_opt": 9, "alternative_path": 9, "alternative_scenario": 3, "alwai": 33, "am": [14, 18, 42, 45], "amazon": 5, "america": 36, "among": [31, 33, 56], "amount": [4, 36, 37, 56], "amplif": [37, 47], "an": [10, 19, 23, 31, 33, 36, 54, 58], "analog": 37, "analys": [5, 9, 14, 26, 27, 28, 36], "analyse_learning_styl": 19, "analysi": [1, 2, 11, 13, 15, 17, 20, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 37, 40, 41, 43, 44, 45, 46, 48, 50, 51, 53, 54, 55, 56, 57, 59], "analysis_12345": 8, "analysis_id": 8, "analysis_period": 14, "analyst": [4, 8, 9, 10, 12, 13, 28, 36], "analyt": [0, 2, 6, 7, 8, 9, 11, 16, 20, 22, 25, 26, 27, 28, 30, 31, 32, 34, 38, 41, 43, 46, 52, 53, 55, 56], "analytics_anonymize_ip": 23, "analytics_id": 23, "analyticsapi": 56, "analyz": [15, 16, 25, 26, 27, 31, 32, 33, 35, 36, 37, 40, 42, 45, 51, 53, 58], "analyze_skills_gap": 54, "analyzecareerpath": 40, "android": [17, 24, 44, 50], "ani": [14, 17, 44, 47, 50, 58], "anim": [0, 22, 43], "annot": [3, 20, 21, 44], "announc": [24, 35, 45, 47], "annual": [9, 31, 33, 36, 46, 53, 54, 55, 56, 57], "annual_training_budget": 8, "anomaly_detect": 18, "anonym": [11, 26, 41, 53, 55], "anonymis": 26, "anonymised_data": 19, "anonymize_personal_data": 18, "anoth": [1, 23, 30, 49], "ansibl": 27, "answer": [25, 26, 30, 47], "anthrop": [18, 49], "anthropic_api_kei": [18, 49], "anytim": [44, 47], "anywher": [32, 44, 47, 58], "apac": 9, "api": [2, 3, 7, 13, 15, 17, 18, 19, 21, 22, 23, 24, 26, 29, 30, 34, 36, 37, 39, 41, 43, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57], "api_access": 12, "api_host": 18, "api_kei": [10, 11, 18, 40], "api_key_head": 18, "api_port": 18, "api_response_tim": 40, "api_token": [12, 18], "api_url": 18, "api_v1_str": [18, 49], "api_vers": 12, "apiintegr": 56, "apivers": 40, "app": [0, 1, 7, 11, 15, 17, 18, 20, 22, 24, 32, 33, 38, 40, 43, 47, 48, 49, 58], "appear": 51, "append": 53, "appli": [21, 28, 31, 32, 33, 39, 40, 47, 54], "applic": [1, 5, 6, 8, 9, 10, 11, 12, 14, 17, 18, 20, 23, 25, 27, 28, 30, 31, 32, 33, 35, 37, 40, 46, 47, 48, 49, 50, 56, 58, 59], "application_error": [16, 59], "application_flow": 1, "apply_tenant_filt": 54, "appreci": 21, "approach": [0, 5, 19, 20, 22, 25, 26, 27, 28, 31, 32, 33, 34, 39, 42, 45, 46, 47, 52, 53, 58], "appropri": [10, 21, 33, 35, 44, 45], "approv": [1, 21, 25, 29, 35, 54], "approvalworkflow": 54, "approxim": 9, "apt": 1, "aptitud": 32, "ar": [1, 4, 6, 7, 8, 11, 15, 16, 19, 21, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 46, 47, 49, 51, 57], "architect": [5, 8, 9, 58], "architectur": [2, 8, 10, 13, 17, 21, 27, 29, 30, 39, 51, 52, 53, 54, 58], "archiv": [27, 41], "area": [11, 14, 15, 30, 31, 33, 36, 37, 43, 44, 46, 47, 51, 53, 58], "areas_for_focu": 14, "areas_needing_focu": 14, "aren": 33, "arg": 21, "argument": [1, 23], "around": 58, "arpu": 53, "arr": [51, 52, 53, 54, 55, 56, 57], "arrang": [32, 43], "arrow": 44, "artifact": 1, "artifici": 2, "asia": 36, "asian": 56, "ask": [1, 21, 23, 25, 47], "aspect": 45, "aspir": 31, "assert": [6, 21, 45], "assess": [2, 4, 11, 15, 17, 19, 20, 24, 26, 27, 28, 30, 31, 32, 33, 34, 36, 37, 41, 47, 48, 50, 52, 53, 54, 55, 58], "assess_789": 5, "assessment_d": 5, "assessment_data": 58, "assessment_id": [5, 8], "assessment_typ": [5, 8, 58], "asset": [0, 1, 14, 22, 23], "assign": [15, 24, 26, 27, 29, 31, 35, 54], "assist": [1, 6, 8, 14, 16, 19, 20, 24, 26, 28, 29, 30, 33, 35, 36, 40, 41, 43, 44, 46, 48, 51, 57], "associ": [8, 28, 32, 33], "assumpt": 9, "assur": [0, 22, 30, 34, 35, 41, 48, 51, 56, 57], "asymmetr": 14, "async": [1, 23, 40, 42, 45], "async_process": 18, "asynchron": 57, "asyncio": 40, "attempt": [33, 47, 53], "attempt_count": 10, "attempts_remain": 7, "attend": [26, 28, 31, 32], "attent": [15, 46], "attribut": [29, 54], "attribute_map": 12, "attributes_receiv": 12, "auc_scor": 19, "audienc": [34, 47], "audio": 44, "audit": [1, 11, 17, 20, 23, 24, 27, 28, 29, 32, 35, 41, 48, 52, 53, 54, 57], "audit_12345": 10, "audit_ai_decis": 18, "audit_log": [13, 18], "audit_trail": [10, 13], "auditor": [28, 34], "auditori": [2, 17, 37, 53, 59], "australia": 56, "auth": [6, 7, 11, 12, 18, 40, 48, 51, 52, 54, 58], "auth_data": [6, 58], "auth_respons": 11, "auth_rout": 40, "auth_servic": 40, "authcontext": 40, "authent": [1, 5, 8, 9, 10, 12, 14, 17, 21, 23, 24, 27, 29, 41, 44, 45, 48, 51, 52, 54, 56, 57], "authenticate_us": 54, "authentication_fail": 6, "authentication_flow": 12, "authentication_success_r": 12, "authentication_time_m": 12, "authenticationapi": 56, "author": [1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 23, 40, 41, 52, 54, 56, 57, 58, 59], "authorit": 25, "authprovid": 40, "authresult": 54, "authservic": 40, "authstat": 39, "authus": 39, "auto": [27, 29, 36, 41, 42, 52, 53, 54, 56], "auto_create_us": 12, "auto_enroll_certif": 10, "auto_provis": 24, "auto_sc": 18, "auto_sync": 12, "auto_update_us": 12, "autobuild": [23, 51], "autoclass": [1, 23], "autocomplet": 47, "autodoc": [1, 23], "autom": [0, 3, 6, 10, 12, 13, 17, 18, 20, 23, 24, 27, 28, 29, 34, 35, 41, 43, 47, 48, 50, 53, 54, 55, 57, 59], "automat": [0, 1, 6, 7, 11, 15, 20, 22, 23, 24, 25, 27, 29, 35, 36, 37, 41, 42, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57], "automated_insight": 18, "automated_test": 18, "automatic_failov": 18, "automatic_progress_track": 14, "automodul": [1, 23], "avail": [4, 10, 11, 15, 16, 17, 26, 27, 29, 33, 34, 36, 37, 39, 40, 41, 43, 44, 47, 48, 51, 52, 53, 55, 56], "available_achiev": 14, "available_data_point": 9, "available_hours_per_week": [5, 15], "available_lic": 10, "averag": [3, 4, 15, 36, 44, 46, 48, 52, 54, 55], "average_budget": 4, "average_completion_r": 13, "average_completion_tim": [3, 10], "average_completion_time_dai": [10, 13], "average_cost": 9, "average_cost_per_cert": 9, "average_daily_study_minut": 14, "average_difficulti": 15, "average_focus_dur": 15, "average_hours_per_week": 14, "average_roi": 9, "average_salari": [4, 8], "average_salary_increas": [3, 8], "average_salary_with_cert": 9, "average_salary_without_cert": 9, "average_scor": 13, "average_session_dur": [3, 14, 15], "average_session_effect": 14, "average_study_hour": [3, 13], "average_study_hours_per_week": 10, "avoid": [31, 44], "aw": [5, 8, 10, 18, 32, 42], "await": [22, 37, 40, 42, 45], "awar": [28, 31, 37, 44, 46], "aws_access_key_id": 18, "aws_s3_bucket": 18, "aws_s3_region": 18, "aws_secret_access_kei": 18, "ax": [0, 42, 45], "axebuild": [42, 45], "azur": [5, 8, 10, 12, 18, 29, 32, 33, 54], "azure_ad": [12, 18], "azure_ad_connector": 12, "azure_client_id_her": 12, "azure_client_secret_her": 12, "azure_tenant_id_her": 12, "b": [1, 21, 23, 26, 51, 52, 53], "back": 47, "backend": [0, 22, 38, 40, 43, 49, 55], "backend_cors_origin": 18, "background": [0, 2, 18, 19, 23, 28, 33, 42, 43, 44, 49], "backup": [25, 27, 29, 35, 41, 47, 48], "backup_cod": 7, "backup_en": 18, "backup_encryption_en": 18, "backup_encryption_kei": 18, "backup_retention_dai": 18, "backup_schedul": 18, "backup_storage_typ": 18, "backup_strategi": 18, "backward": [21, 52], "bad": [4, 7, 11, 16], "badg": [14, 47, 52, 55], "badge_level": 14, "badges_earn": 14, "balanc": [25, 27, 28, 29, 31, 32, 33, 34, 36, 37, 41, 44, 47, 57], "bank": 9, "banner": 47, "bar": [3, 39, 44], "barrier": 31, "base": [2, 4, 5, 8, 9, 10, 12, 13, 15, 17, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 59], "base64": 7, "base_amount": 56, "base_crud": 20, "base_curr": 9, "base_salari": 8, "base_url": [6, 11, 12, 40], "baselin": 36, "bash": [23, 28], "basi": 23, "basic": [10, 17, 18, 21, 23, 25, 33, 35, 49, 52, 53, 56], "bat": 23, "batch": [2, 20], "batch_siz": 12, "batteri": 44, "bayesian": [17, 53], "bayesianknowledgetrac": 53, "bdd": [48, 52, 53, 54, 55], "bearer": [1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 23, 40, 58], "bearer_token": 12, "beauti": [0, 1, 23, 42], "becom": [8, 28, 33, 35, 44], "bed": 44, "been": [0, 22, 54, 55, 57, 59], "befor": [6, 14, 21, 33, 34, 36, 37, 44, 49, 53, 58], "begin": [8, 14, 15, 50], "beginn": [8, 19, 33, 43, 58], "behav": [0, 42, 48, 52, 53, 57], "behavior": [2, 22, 24, 35, 37, 48, 50, 55], "behaviour": [21, 26, 48], "behind": [33, 59], "being": 25, "below": 3, "benchmark": [2, 3, 8, 24, 25, 29, 31, 34, 36, 37, 41, 47, 50, 53, 54, 55], "benefit": [2, 8, 17, 21, 24, 31, 32, 33, 34, 37, 39, 41, 47, 48, 50, 58], "best": [11, 20, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 41, 46, 48, 59], "best_case_roi": 4, "best_collabor": 3, "best_performing_top": 14, "best_roi": 9, "best_session_dur": 14, "best_study_dai": 15, "best_study_tim": 5, "beta": 26, "better": [2, 3, 15, 22, 25, 31, 33, 42, 44, 46], "between": [10, 13, 14, 17, 23, 24, 27, 31, 32, 33, 35, 37, 41, 44, 50, 51, 54, 57, 59], "beyond": 34, "bg": 42, "bi": [3, 12, 13, 24, 29], "bidirect": 12, "big_tech": 8, "bill": [7, 10, 13, 24, 35], "billing_contact": 10, "billing_email": 13, "billing_integr": 18, "bin": [21, 49], "biometr": [44, 47], "bite": [41, 44, 47, 53], "black": [20, 21], "blackboard": 41, "blend": 41, "block": [1, 23, 33], "blocked_countri": 13, "blocker": 43, "blog": 28, "blue": 22, "blueprint": 31, "board": [8, 24, 26], "bodi": [4, 40, 51, 56], "bold": [1, 23], "bonus": 31, "bonus_averag": 8, "book": [33, 37], "bookmark": [43, 44], "bool": [21, 54], "boolean": 39, "boost": [19, 53], "bootcamp": [28, 33, 56], "bootcamp_cours": 9, "bootcamp_train": 4, "bootstrap": 1, "border": 23, "both": [31, 32, 34, 41], "bottleneck": 52, "bottom": 23, "bound": 47, "boundari": [22, 35, 39, 42, 43], "bounti": 32, "box": 23, "boxmargin": 23, "boxtextmargin": 23, "branch": [1, 23, 40, 48], "brand": [0, 22, 25, 29, 35, 43, 48], "brazil": 56, "breach": [41, 54], "breadcrumb": [0, 23, 43], "breadth": 32, "break": [2, 5, 15, 17, 32, 33, 37, 44, 47], "break_after_minut": 14, "break_duration_minut": 14, "break_even_d": 3, "break_even_month": 9, "break_frequ": 15, "break_frequency_minut": 5, "break_suggest": 5, "breakdown": [13, 21, 36, 55], "breakpoint": [22, 39], "breakthrough": [2, 48], "brief": [21, 31, 32], "brief_walk": 14, "bright": 44, "bring": [28, 31], "british": [0, 1, 11, 21], "broad": [32, 33], "broader": [9, 32, 33], "broken": [1, 23, 25], "bronz": 14, "brown": 10, "brows": [43, 46, 52, 56, 58], "browser": [1, 7, 22, 23, 36, 43, 44, 46, 47, 58], "bu": [52, 57], "budget": [2, 3, 8, 10, 11, 16, 17, 24, 29, 30, 33, 40, 41, 45, 46, 47, 48, 50, 54, 55, 57, 58, 59], "budget optim": [4, 36], "budget_alloc": [9, 13, 40], "budget_allocation_workflow": 54, "budget_amount": 9, "budget_analysi": 13, "budget_annu": 10, "budget_approval_requir": 10, "budget_avail": 8, "budget_def456": 9, "budget_optim": [40, 55], "budget_optimis": 9, "budget_optimization_step": 55, "budget_overrun_risk": 4, "budget_perform": 3, "budget_plan_id": 9, "budget_rang": [5, 58], "budget_realloc": 3, "budget_rout": 40, "budget_track": 10, "budget_tracking_en": 13, "budget_typ": 9, "budget_util": [3, 4, 13], "budget_utilis": [9, 10], "budget_utilised_percentag": 10, "budgetoptim": 55, "budgetrequest": 54, "buffer": [2, 9, 14, 34], "bug": [17, 32], "bugfix": 21, "build": [5, 8, 9, 20, 22, 27, 28, 31, 32, 33, 34, 37, 39, 41, 43, 47, 48, 52, 57, 58], "builder": [24, 26, 29, 41], "built": [1, 11, 12, 20, 22, 26, 27, 29, 43, 48, 51], "bulk": [2, 13, 20, 24, 26, 29, 30, 31, 35, 41, 56], "bulk_assign": 10, "bulk_import_us": 24, "bulkpurchas": 56, "bulkpurchasingapi": 56, "bullet": [1, 23], "bundl": [22, 42, 43, 48], "burst": 4, "busi": [8, 13, 17, 20, 24, 28, 29, 31, 32, 34, 36, 40, 41, 44, 46, 47, 50, 51, 52, 53, 54, 55, 59], "business intellig": 3, "button": [1, 23, 42, 43, 44, 45, 55], "c": [1, 23, 24, 41, 47, 50, 54], "ca": [4, 7], "cach": [0, 17, 18, 20, 21, 22, 35, 36, 39, 41, 42, 43, 44, 46, 47, 48, 49, 52, 55, 58], "cache_max_s": 18, "cache_ttl_second": 18, "cad": 9, "cagr": [53, 54, 55, 56], "calc_12345": 9, "calcul": [1, 16, 20, 21, 24, 27, 29, 31, 32, 33, 34, 36, 48, 50, 54, 55, 56, 59], "calculate_certification_cost": 21, "calculate_commiss": 56, "calculate_cost": 21, "calculate_prediction_confid": 53, "calculate_renewal_pv": 55, "calculate_retake_prob": 55, "calculate_roi": 55, "calculate_total_cost": 55, "calculation_id": 9, "calendar": [15, 28], "calibr": 37, "call": [17, 43], "callback": [7, 12], "came_from": 55, "campu": [24, 48], "can": [19, 26, 28, 29, 30, 31, 32, 33, 42, 44, 45, 46, 49, 51, 53], "can_access_ai_featur": 10, "can_export_data": 10, "can_manage_team": 10, "canada": 56, "cannot": [7, 43], "canva": [18, 41], "capabl": [0, 1, 2, 3, 6, 10, 11, 12, 13, 17, 18, 24, 29, 30, 32, 33, 34, 35, 37, 39, 41, 43, 46, 47, 48, 50, 51, 52, 55, 57, 58], "capac": [24, 27, 35, 36, 41], "capston": 26, "captur": [28, 44, 54], "card": [23, 36, 42, 43, 44, 47, 53, 55], "career": [1, 9, 16, 20, 24, 25, 26, 30, 31, 34, 35, 37, 41, 42, 43, 44, 46, 48, 51, 52, 54], "career pathfind": 4, "career plan": 36, "career_advanc": 9, "career_chang": [1, 25], "career_framework": 1, "career_go": [5, 8, 9, 58], "career_impact": [8, 9], "career_impact_scor": 9, "career_path": 55, "career_pathfind": 55, "career_pathfinding_step": 55, "career_profil": 59, "career_progress": 8, "career_rol": 55, "career_rout": 40, "career_transit": [20, 40, 55, 58], "careerpath": 55, "careerpathfind": 55, "careerplan": [40, 55], "careerprofil": 59, "carefulli": [31, 39], "case": [1, 31, 32, 34, 36, 47], "cash_flow_analysi": 9, "catalog": [17, 41, 56, 59], "catch": 42, "categor": 35, "categori": [0, 3, 4, 6, 12, 14, 36, 39, 43, 46, 52, 55], "categoris": 29, "category_rank": 14, "caus": 35, "cb_recommend": 19, "cbt": 33, "ccna": 33, "ccsp": [5, 8], "cd": [1, 21, 22, 23, 25, 40, 49, 51, 55, 57], "cdn": [0, 22, 23, 27, 29], "ceh": [4, 13, 32], "celebr": [2, 24, 31, 34, 44, 46, 47, 58], "celery_broker_url": 18, "celery_result_backend": 18, "celery_worker_concurr": 18, "center": [23, 35, 41, 42, 46, 47], "central": [4, 35, 41, 46, 52, 57, 59], "centralis": [27, 29, 34], "ceo": 54, "cert": [2, 32, 33, 55, 58], "cert_cost": 55, "cert_tot": 55, "certbodi": 56, "certif": [0, 1, 2, 3, 4, 5, 6, 7, 8, 11, 12, 13, 14, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 44, 45, 46, 48, 50, 51, 52, 53, 54, 55, 56, 57, 59], "certifi": [5, 8, 31, 32, 33], "certification_approval_requir": 13, "certification_data": 19, "certification_demand": 3, "certification_go": [13, 40], "certification_id": [2, 3, 4, 5, 8, 9, 11, 12, 14, 15, 19, 21, 40], "certification_metr": 13, "certification_nam": [5, 9, 14, 15], "certification_path": 55, "certification_popular": 9, "certification_progress": [10, 13], "certification_progress_percentag": 14, "certification_recommend": 8, "certification_remind": 13, "certification_roadmap": 8, "certification_roi": 3, "certification_sequ": 9, "certification_success": 3, "certification_timelin": 9, "certification_typ": 12, "certification_veloc": 13, "certificationbodyapi": 56, "certificationdifficulti": 39, "certificationfilt": 39, "certificationid": [52, 53], "certificationlevel": 39, "certifications_complet": [3, 9, 10, 13], "certifications_impact": 8, "certifications_in_progress": [10, 13], "certifications_start": 10, "certifications_timelin": 8, "certpathfind": [0, 2, 4, 6, 7, 11, 12, 13, 17, 18, 19, 20, 21, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 44, 46, 47, 49, 50, 51, 52, 54, 56, 57, 59], "certpathfinder_integr": 12, "certpathfindercli": [6, 11, 40], "certrat": [0, 22, 23, 38, 39, 42, 43, 45, 47, 48], "certratsagent4": [17, 53, 57], "cf_recommend": 19, "cgeit": 31, "ch": 15, "challeng": [2, 14, 19, 31, 36, 37, 50], "challenging_top": 14, "chang": [1, 8, 11, 17, 18, 21, 24, 25, 27, 28, 32, 33, 34, 35, 36, 37, 41, 42, 45, 46, 47, 49, 51, 52, 53, 54, 56, 57, 58], "changed_bi": 13, "changelog": [21, 43, 48], "changer": [1, 25, 30, 48], "channel": [27, 31, 58], "chapter": 15, "charact": 7, "chart": [3, 22, 36, 37, 44, 47, 52, 53, 58], "chat": [36, 46, 53], "cheat": 53, "check": [1, 6, 12, 17, 19, 21, 22, 23, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 40, 41, 43, 44, 46, 47, 48, 49, 51, 53, 57, 58], "check_agent3_servic": 40, "check_agent4_servic": 40, "check_auth_servic": 40, "check_databas": 40, "check_org_servic": 40, "check_redi": 40, "check_study_servic": 40, "checkbox": 42, "checker": 25, "checklist": [27, 47], "checkout": [1, 21, 23], "checkpoint": 58, "choic": [31, 37], "choos": [32, 33, 36, 44, 46, 48, 58], "chosen": [19, 32], "chrome": [7, 36, 43, 44], "churn": [35, 52], "ci": [55, 57], "cipher_suit": 19, "circumst": 34, "cisa": [9, 32], "cisco": 33, "cism": [4, 8, 9, 13, 31, 32, 33, 36, 40, 56], "ciso": [5, 8, 54, 58], "cissp": [3, 4, 8, 9, 10, 12, 13, 14, 15, 28, 31, 32, 33, 36, 40, 43, 56], "citat": 26, "civilian": 28, "clarifi": 33, "clariti": [25, 46], "class": [2, 6, 19, 20, 21, 40, 53, 54, 55, 56, 59], "classif": [29, 41, 59], "classifi": [2, 19, 50], "classification_model": 19, "classnam": [40, 42], "clean": [1, 11, 21, 23, 51, 53], "cleaner": 22, "cleanup": [35, 43], "clear": [1, 17, 20, 21, 25, 31, 34, 35, 36, 39, 43, 44, 46, 47, 53, 58, 59], "clearanc": 28, "clearer": 33, "clearli": [31, 34, 42, 45], "click": [21, 35, 42, 43, 44, 45], "clickabl": 42, "client": [4, 6, 17, 18, 20, 22, 27, 29, 31, 34, 48, 52, 53, 54, 55, 57], "client_abc123": 7, "client_config": 56, "client_id": [7, 12, 18], "client_secret": [7, 12, 18], "clientconfig": 56, "clientsess": 40, "clipboard": 0, "clock": 41, "clone": [21, 27, 40, 49], "close": [43, 44, 47], "cloud": [3, 4, 5, 8, 10, 13, 32, 33, 37, 41, 53, 58], "cloud_secur": [4, 5, 40], "cloudtrail": 5, "cluster": [2, 19, 27, 29, 50], "clustering_model": 19, "cmd": [1, 40, 43], "cmmc": 54, "cn": [13, 18], "co": 10, "coach": 55, "code": [0, 4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 22, 23, 29, 42, 43, 48, 51, 52, 55, 56], "codebas": 20, "cognit": 37, "coher": 57, "cohes": [16, 22, 31, 59], "cohort": 26, "col": [39, 42], "collabor": [3, 13, 17, 19, 21, 30, 31, 32, 36, 43, 53], "collaboration_scor": [3, 13], "collaps": 0, "collapse_navig": [1, 23], "collect": [2, 20, 26, 34, 35, 40, 43, 48, 53], "collection_interv": 18, "color": [0, 1, 22, 23], "colsample_bytre": 19, "column": 39, "com": [1, 3, 4, 6, 7, 10, 11, 12, 13, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 40, 42, 43, 45, 49, 58], "combin": [16, 17, 19, 33, 37, 41, 42, 46, 47, 48, 50, 55, 57, 59], "come": 33, "comfort": 44, "command": [22, 28, 35, 44, 47], "comment": [21, 36], "commerci": 50, "commiss": [51, 56], "commission_amount": 56, "commission_r": 56, "commissionapi": 56, "commissionengin": 56, "commissiontrack": 56, "commit": [9, 21, 28, 32, 33, 46, 48, 51, 52, 53, 54, 56, 58], "common": [4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 20, 21, 25, 27, 29, 33, 35, 36, 40, 43, 44, 47, 48, 49], "commun": [4, 6, 8, 17, 20, 24, 26, 28, 30, 31, 32, 33, 34, 35, 36, 40, 43, 46, 47, 48, 51, 56, 57, 58], "commut": [32, 44], "comp_67890": 9, "compani": [7, 12, 18, 29, 40, 54], "companion": [37, 44, 47], "company_size_impact": 8, "compar": [2, 26, 31, 34, 36, 37, 47, 52, 55, 58], "comparison": [3, 24, 29, 31, 32, 41, 43, 44, 47, 50, 52, 55, 58], "comparison_criteria": 9, "comparison_id": 9, "compat": [0, 21, 27, 43, 44, 52], "compel": [31, 32, 34], "compens": [8, 31, 35, 36, 41, 54, 55], "compet": [24, 28, 29, 32, 37, 41], "competit": [4, 8, 24, 28, 29, 31, 32, 33, 34, 36, 41, 47, 48, 50, 52, 53, 54, 55], "competition_level": [4, 8], "competitor": [25, 31, 53], "compil": [22, 48], "complet": [0, 1, 2, 3, 5, 7, 8, 9, 10, 11, 12, 13, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 41, 42, 43, 45, 46, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59], "complete system": 40, "complete_health_check": 40, "completed_at": 14, "completed_certif": 5, "completed_d": 12, "completed_go": 15, "completed_this_period": 10, "completeuserprofil": 59, "completion_d": 12, "completion_effici": 3, "completion_month": 9, "completion_percentag": 12, "completion_r": [3, 13, 15], "completion_statu": 15, "completion_trend": 3, "completions_todai": 3, "complex": [3, 5, 21, 24, 29, 35, 36, 37, 38, 47, 48, 52, 53], "complianc": [0, 1, 2, 3, 5, 11, 12, 17, 18, 22, 23, 24, 26, 28, 29, 30, 31, 32, 34, 35, 37, 40, 42, 44, 45, 47, 48, 50, 51, 52, 53, 55, 56, 58, 59], "compliance_123": 13, "compliance_cost_sav": 3, "compliance_requir": 24, "compliance_sav": 3, "compliance_scor": [3, 10, 13], "compliance_standard": 10, "compliance_statu": 24, "compliance_train": 3, "complianceengin": 54, "compliancereport": 54, "complianceservic": 54, "compliancestatu": 54, "compliant": [0, 10, 13, 20, 22, 26, 42, 43, 44, 57], "compon": [0, 16, 17, 22, 39, 40, 42, 45, 46, 48, 52, 54, 55, 57], "component_service_error": 59, "compos": [4, 20, 21, 27, 49], "comprehens": [1, 2, 3, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 22, 23, 24, 25, 26, 29, 30, 34, 35, 36, 37, 38, 39, 40, 41, 43, 46, 47, 51, 52, 53, 54, 56, 57, 58, 59], "compress": [0, 20, 43], "comptia": [28, 33, 42, 56], "comput": 53, "concentr": 15, "concept": [5, 14, 15, 28, 32, 33, 37, 44, 53], "concepts_mast": 15, "conceptu": 33, "concern": [20, 31, 50], "concis": [1, 21], "conclus": 50, "concurr": [7, 17, 24, 35, 48, 50, 52, 55], "condit": [8, 9, 23, 36, 47], "conduct": [26, 30], "conf": [1, 23, 51], "confer": [26, 28, 31, 32], "confid": [2, 3, 5, 14, 19, 36, 37, 45, 46, 47, 48, 50, 53, 55, 58], "confidence_interv": [3, 5, 14, 53], "confidence_level": [5, 8, 14, 58], "confidence_r": 5, "confidence_scor": [4, 5, 15, 19, 59], "confidence_threshold": 18, "config": [1, 18, 20, 23, 40], "configur": [3, 6, 9, 10, 13, 19, 20, 21, 22, 24, 26, 29, 30, 35, 36, 39, 41, 43, 44, 48, 55, 56, 57, 58], "configuration_complex": 12, "configuration_statu": 12, "configure_sso_provid": 54, "configured_at": 7, "confirm": [12, 40, 43, 44], "conflict": [10, 14, 35, 44, 49, 57], "conftest": 6, "congratul": [36, 40, 58], "connect": [0, 2, 3, 12, 13, 22, 24, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 40, 41, 42, 43, 44, 46, 47, 49, 50, 52, 53, 56, 58], "connection_nam": 12, "connection_pool_s": 18, "connection_test": 12, "connector": [11, 29, 40, 48], "connector_count": 12, "connector_id": 12, "connecttorealtimemetr": 40, "consecut": [14, 15, 43, 46, 47], "consent": [26, 54], "conserv": [36, 37], "consid": [3, 4, 7, 8, 9, 12, 13, 14, 15, 17, 27, 28, 31, 32, 33, 34, 36, 37, 46, 47, 53, 55, 58, 59], "consider": [21, 44], "consist": [1, 2, 3, 5, 8, 13, 14, 17, 22, 24, 25, 32, 33, 34, 35, 37, 43, 46, 47, 48, 50, 52, 55, 57, 58, 59], "consistency_scor": 14, "consistency_tip": 14, "consistency_trend": 14, "consistent_learner_30_dai": 14, "consistent_learner_7_dai": 14, "consol": 29, "consortium": 26, "const": [22, 39, 40, 42, 45], "constitut": 37, "constraint": [4, 8, 9, 10, 31, 34, 36, 53, 55, 58], "constraints_analysi": 4, "construct": 21, "consult": [8, 28, 29, 31, 33, 41, 49, 54, 55], "consum": [23, 53], "consumpt": 52, "contact": [26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 41, 46, 47, 58], "contain": [1, 20, 21, 40, 51, 57], "container": 22, "containeris": [21, 49], "containerport": 40, "content": [2, 3, 15, 23, 26, 29, 30, 31, 32, 34, 37, 40, 41, 43, 44, 45, 46, 47, 48, 50, 51, 53, 56, 58, 59], "content_gener": 53, "contentlicens": 56, "contentlicensingapi": 56, "context": [5, 6, 17, 22, 28, 40, 44, 47, 53, 54, 58], "contextu": 53, "conting": [4, 36], "continu": [0, 2, 5, 9, 12, 17, 19, 28, 29, 30, 31, 33, 34, 37, 41, 42, 44, 46, 47, 48, 50, 51, 52, 53, 56, 57, 58], "continuing_educ": 4, "contract": [28, 32, 34, 54, 56], "contrast": [0, 47], "contribut": [19, 20, 25, 26, 28, 32, 48, 57], "contributor": [21, 54], "control": [0, 2, 5, 10, 11, 13, 14, 19, 20, 21, 24, 26, 27, 28, 29, 30, 35, 37, 40, 41, 43, 44, 46, 47, 48, 50, 52, 53, 54, 57], "conveni": [44, 47], "convent": [21, 25], "converg": 50, "convers": [52, 53, 56], "convert": 19, "cooki": [6, 36, 43, 44, 46], "coordin": [28, 34, 36], "copi": [0, 1, 21, 23, 40, 49], "copybutton": 1, "copyright": [1, 23], "cor": [18, 20, 40], "core": [0, 18, 19, 21, 27, 37, 40, 42, 43, 45, 48, 49, 53, 54, 55], "cornerston": 29, "corpor": [10, 13, 24, 30, 48, 53, 54, 55, 56], "correct": [12, 25, 35, 36, 45, 49], "correctli": [12, 25, 45], "correl": [3, 17, 26, 29, 31, 34, 37, 44], "cors_allow_credenti": 18, "cors_allowed_method": 18, "cors_allowed_origin": 18, "cors_origin": 40, "cosine_similar": 19, "cost": [1, 2, 8, 17, 20, 21, 24, 30, 31, 32, 33, 34, 35, 36, 40, 41, 43, 46, 48, 50, 51, 52, 57], "cost_analysi": [10, 11], "cost_breakdown": [4, 9, 55], "cost_calcul": [1, 11, 20, 21, 55], "cost_centr": 10, "cost_estim": 8, "cost_factor": 9, "cost_max": 39, "cost_min": 39, "cost_of_liv": 4, "cost_of_living_adjust": 8, "cost_of_living_multipli": 4, "cost_opt": 9, "cost_optimis": 9, "cost_per_certif": [3, 10, 13], "cost_per_complet": 10, "cost_per_study_hour": 3, "cost_rang": 9, "cost_sav": [3, 4], "costanalysi": 55, "costanalysisengin": 55, "costcalculatorservic": 21, "council": 56, "count": [27, 32, 47], "counter": [42, 47], "countri": [10, 56], "cours": [5, 8, 12, 15, 24, 26, 33, 37, 41, 47, 55, 56], "course_field": 12, "course_nam": 12, "course_sync": 12, "coursecatalog": 56, "coursecatalogapi": 56, "cov": 21, "cover": [0, 6, 18, 26, 27, 28, 29, 33, 34, 35, 41, 42, 43, 45, 55], "coverag": [17, 20, 21, 23, 25, 37, 47, 48, 51, 52, 54, 57], "cp": [21, 40, 49], "cpe": 32, "cpu": [27, 35, 49], "cpu_usag": 18, "cram": 47, "creat": [1, 3, 5, 8, 11, 12, 16, 18, 21, 22, 23, 25, 26, 28, 29, 31, 32, 33, 34, 35, 37, 40, 47, 48, 49, 52, 53, 54, 56, 58, 59], "create_dashboard": 24, "create_organ": 24, "create_organization_hierarchi": 54, "create_topic_recommend": 53, "create_us": 40, "create_user_profil": 40, "created_at": [7, 10, 12, 13, 14, 15], "createdb": 49, "creation": [17, 24, 30, 35, 41, 43, 47, 52, 58], "credenti": [6, 7, 22, 42, 43, 44, 47, 49, 54], "credibl": [8, 28, 32], "credit": [31, 32], "crisc": [31, 32], "criteria": [21, 31, 37, 42, 47], "criterion": 21, "critic": [8, 18, 24, 27, 31, 44, 46, 52, 57, 58], "critical_gap": 8, "cron": 27, "cross": [0, 2, 3, 8, 13, 17, 20, 22, 23, 24, 25, 29, 31, 35, 36, 41, 43, 44, 46, 47, 48, 50, 51, 54, 55, 57], "cross_component_analyt": 59, "cross_tenant_analyt": 18, "crosscomponentrecommendationengin": 17, "crud": [20, 52, 54], "cryptograph": 14, "cryptographi": [14, 15, 19], "css": [1, 22, 23, 39, 48], "csv": [10, 24, 35, 41], "csv_file": 24, "ctf": [28, 32], "ctrl": 43, "ctx": 45, "cultur": [35, 50, 56], "cumul": [43, 46], "cumulative_salary_increas": [8, 9], "curat": [2, 28, 47, 53, 58, 59], "curl": [16, 23, 49, 59], "currenc": [4, 11, 17, 21, 48, 50, 55, 56], "current": [4, 5, 8, 9, 19, 25, 26, 27, 28, 30, 31, 32, 33, 34, 35, 36, 37, 43, 44, 46, 47, 51, 52, 53, 54, 55, 56, 58], "current_alloc": 3, "current_certif": 8, "current_cost": 3, "current_depart": 10, "current_duration_minut": 15, "current_effici": 3, "current_ent": 12, "current_knowledg": 5, "current_knowledge_level": [14, 15], "current_level": [3, 8, 14], "current_org_id": 6, "current_organis": 10, "current_posit": 8, "current_profil": 8, "current_progress": [2, 3, 14, 15, 53], "current_progress_percentag": 14, "current_rol": [8, 9, 40, 55], "current_role_id": [4, 8, 40], "current_role_level": 19, "current_role_titl": 8, "current_salari": [4, 8, 9], "current_sess": 3, "current_skil": [5, 8], "current_streak": 14, "current_study_streak": 14, "current_top": 5, "current_us": [10, 13, 40], "current_valu": 3, "curricula": 34, "curriculum": [30, 33, 41], "curv": [23, 53], "custom": [0, 2, 4, 9, 22, 24, 26, 27, 30, 34, 35, 36, 37, 41, 43, 44, 47, 48, 49, 51, 52, 53, 54, 55, 56, 58], "custom_report": 18, "customis": [11, 26, 29, 48], "cut": [37, 48, 50], "cv": 8, "cybersecur": [2, 4, 8, 10, 21, 24, 25, 26, 28, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 43, 44, 47, 48, 50, 52, 53, 54, 55, 56, 57, 58], "cybrari": 33, "cycl": [31, 36], "cysa": [13, 33], "d": [1, 21, 23, 49, 59], "daemon": [1, 51], "dai": [4, 7, 14, 15, 19, 43, 46, 47, 52, 53], "daili": [12, 15, 18, 27, 35, 36, 46, 48, 50, 52, 53, 57, 58], "daily_average_us": 10, "dark": [0, 22, 23, 43, 52], "dashboard": [1, 2, 10, 12, 17, 23, 26, 27, 29, 30, 31, 34, 37, 40, 45, 48, 50, 53, 54, 55, 56, 57], "dashboard_typ": 24, "dashboarddata": 42, "dashboardhead": 42, "dashboardinsight": 59, "dashboardmetr": 59, "dashboardpag": 42, "dashboardqueri": 39, "dashboardrecommend": 59, "dashboardskeleton": 42, "data": [2, 5, 6, 7, 8, 9, 10, 11, 13, 14, 17, 18, 22, 24, 26, 27, 28, 29, 31, 34, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], "data visu": 3, "data_anonym": 18, "data_collect": 18, "data_deletion_request": 10, "data_ev": 10, "data_export": 18, "data_export_request": 10, "data_govern": 10, "data_isol": 18, "data_points_process": 5, "data_retent": 13, "data_retention_dai": [10, 13, 18, 19, 40], "data_retention_polici": 10, "databas": [17, 18, 21, 28, 29, 30, 35, 40, 41, 43, 48, 49, 51, 52, 54, 55, 57, 58, 59], "database_connect": 18, "database_perform": 40, "database_url": [18, 20, 27, 40, 49], "datadog": [18, 27], "datadog_api_kei": 18, "dataintelligenceengin": 54, "dataintelligenceservic": 54, "datalay": [1, 23], "dataset": [26, 27, 42], "datasync": 56, "datasynchronizationapi": 56, "date": [1, 3, 13, 14, 15, 23, 25, 33, 34, 37, 43, 45, 46, 58], "date_rang": 12, "daterang": 54, "datetim": [6, 21, 40, 58], "days_ahead_of_schedul": 12, "days_remain": 14, "days_studi": 14, "days_with_studi": 14, "db": [18, 49, 52], "db_max_overflow": 18, "db_pool_recycl": 18, "db_pool_siz": 18, "db_pool_timeout": 18, "dc": 18, "deactiv": [24, 35, 41], "deadlin": [2, 15, 24, 34, 37, 44, 50, 51, 58], "deal": 56, "debounc": [0, 42, 47], "debug": [17, 18, 22, 27, 43], "decid": 36, "decis": [19, 24, 31, 36, 47, 55, 58], "decisiontreeclassifi": 19, "declin": 34, "decod": 6, "decomposit": 19, "decreas": 24, "dedic": [4, 15, 29, 32, 41, 47, 52], "deep": [2, 3, 28, 32, 36, 37, 41, 48, 50], "deeper": 33, "def": [2, 6, 19, 21, 40, 53, 54, 55, 56, 59], "default": [1, 6, 15, 18, 20, 23, 35], "default_organ": 18, "default_rol": [10, 12, 24], "defin": [27, 35, 36, 37, 46, 57, 58], "definit": [22, 47, 55], "degrad": [35, 36, 43, 50, 52, 53], "degre": [26, 28, 33], "delai": 2, "deleg": 29, "delet": [6, 7, 18, 20, 27, 41], "deleted_at": 6, "deliv": [2, 22, 24, 35, 37, 41, 44, 48, 50, 52, 53, 54, 55, 57], "deliver": [8, 54, 55], "deliveri": [0, 2, 3, 24, 29, 34, 37, 41, 43, 47, 48, 55, 56], "delivery_attempt": 12, "delivery_schedul": 3, "delivery_set": 12, "demand": [3, 4, 5, 24, 31, 32, 34, 35, 36, 41, 42, 43, 46, 47, 50, 54, 55, 58], "demand_chang": 4, "demand_level": 4, "demand_scor": 8, "demand_trend": 8, "demo": [22, 43, 45, 48], "demonstr": [28, 30, 31, 32, 33, 34, 43, 52, 53], "deni": [6, 14], "depart": [11, 12, 13, 29, 30, 31, 34, 35, 40, 41, 48, 54], "department_id": 10, "department_nam": [10, 12], "depend": [1, 2, 5, 19, 20, 21, 22, 23, 27, 28, 31, 32, 33, 37, 40, 47, 48, 49, 50, 53, 57], "deploi": [1, 23, 24, 27, 29, 30, 35, 41, 48, 53], "deploy": [2, 6, 10, 18, 24, 27, 30, 48, 49, 50, 52, 54, 57, 58, 59], "deprovis": [29, 41], "dept_789": 10, "dept_id": 10, "depth": [28, 32, 37], "describ": [21, 42], "descript": [5, 9, 10, 12, 13, 14, 15, 21, 23, 26, 36, 40, 43, 47, 51], "design": [1, 3, 8, 17, 22, 23, 24, 26, 28, 33, 37, 42, 43, 44, 47, 48, 50, 52, 57, 58], "desir": [32, 36], "desired_timeline_month": 8, "desktop": [7, 24, 39, 43, 44, 47], "destin": 36, "destruct": 31, "detail": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15, 16, 17, 20, 21, 22, 24, 25, 27, 29, 32, 33, 34, 35, 36, 39, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 59], "detect": [0, 7, 35, 37, 43, 48], "determin": [25, 33, 37, 51, 58], "dev": 22, "develop": [2, 6, 11, 14, 16, 17, 18, 19, 23, 24, 26, 27, 29, 30, 33, 36, 37, 41, 42, 44, 46, 47, 49, 50, 54, 55, 56], "development_path": 8, "development_plan": 8, "development_recommend": 8, "development_year": 8, "devic": [0, 2, 5, 15, 17, 18, 19, 24, 27, 30, 39, 42, 46, 47, 48, 50, 51, 52, 53, 57, 58, 59], "device_typ": 7, "devop": 3, "devsecop": [4, 8, 10, 32], "diagnost": 27, "diagram": [23, 27, 38, 39, 48], "diagrammargini": 23, "diagrammarginx": 23, "dict": [21, 54, 55, 59], "dictionari": 21, "differ": [9, 12, 17, 27, 29, 31, 32, 33, 34, 35, 36, 37, 43, 44, 46, 47, 48, 49, 50, 55, 59], "differenti": [41, 53, 54], "difficult": [14, 33], "difficulti": [2, 14, 20, 31, 32, 37, 39, 43, 47, 48, 50, 53], "difficulty_adjust": 14, "difficulty_assess": 5, "difficulty_estim": [5, 18, 19, 53], "difficulty_experienc": [5, 14], "difficulty_level": [5, 14, 19], "difficulty_model": 2, "difficulty_r": 15, "difficulty_scor": [4, 5, 9, 19], "difficultyestimatormodel": 19, "diminishing_returns_aft": 14, "direct": [0, 12, 24, 25, 35, 36, 41, 46, 47, 56, 58, 59], "directli": 28, "director": 8, "directori": [1, 12, 13, 24, 26, 27, 29, 41, 48, 50, 51, 54], "disabl": [35, 36, 42, 43, 46], "disast": [18, 27, 29, 41], "disaster_recoveri": 18, "disciplin": 28, "disclosur": 44, "disconnect": 41, "discord": [28, 33], "discount": [3, 26, 29, 31, 33, 34, 59], "discount_r": 9, "discourag": 33, "discov": [30, 33, 47, 58], "discover": [25, 48], "discoveri": [0, 2, 39, 48, 50, 52, 56, 57], "discuss": [21, 31, 32, 36, 41, 43], "disk": 27, "disk_usag": 18, "displai": [0, 36, 39, 42, 43, 44, 45, 46, 53, 58], "display_nam": 12, "display_vers": 23, "displaynam": 12, "dist": [1, 23], "distract": [44, 47], "distribut": [3, 24, 25, 35, 36, 37, 41, 46, 51, 52, 53, 56, 57], "disturb": 44, "div": [40, 42], "dive": [36, 58], "divers": [8, 21, 28], "diversifi": 32, "diversity_factor": 18, "divis": 41, "do": [26, 27, 28, 29, 31, 32, 33, 34, 44], "doc": [1, 4, 11, 17, 20, 21, 23, 25, 26, 27, 28, 29, 31, 32, 33, 34, 47, 49, 51, 54, 58], "docker": [20, 21, 22, 27, 40, 48], "dockerfil": [1, 20], "docstr": [1, 20, 21, 23], "doctre": 23, "document": [4, 10, 17, 19, 20, 22, 24, 26, 27, 28, 29, 30, 31, 32, 36, 38, 39, 40, 41, 43, 46, 47, 49, 50, 52, 54, 55, 56, 59], "dod": [32, 33], "doe": [7, 21, 26, 28, 33], "doesn": [16, 32], "dom": 0, "domain": [2, 5, 7, 10, 13, 14, 15, 16, 18, 19, 24, 28, 32, 33, 34, 37, 39, 40, 43, 46, 47, 48, 49, 50, 52, 58, 59], "domain_experi": [18, 19], "domain_id": 15, "domain_nam": 15, "domain_scor": 5, "domcontentload": [23, 45], "domcontentloadedeventend": 45, "domcontentloadedeventstart": 45, "don": [25, 31, 33], "dot": 44, "down": [3, 24, 44], "download": [1, 23, 27, 33, 44], "download_url": 13, "downtim": [27, 35, 44, 52], "dpia": 54, "dr": [27, 29], "draft": 25, "drag": [29, 41], "drain": 44, "dramat": 53, "drawer": 44, "drill": [3, 24], "drive": [37, 44, 52, 54, 56], "driven": [2, 53, 55, 57], "drop": [15, 29, 41], "dropdown": 55, "dsn": 18, "due": [8, 9, 32], "dummi": [1, 23], "dump": 19, "duplic": [10, 35], "durat": [2, 36, 44, 47, 52, 53], "duration_minut": [5, 14, 15], "duration_month": 10, "duration_week": 8, "duration_year": 8, "dure": [4, 7, 14, 15, 23, 28, 31, 32, 33, 34, 35, 36, 42, 43, 44, 46, 47], "dynam": [0, 2, 17, 24, 30, 37, 47, 50, 53], "e": [6, 19, 21, 23, 51], "e2": [0, 22, 42, 45, 52, 53, 55], "e29b": 16, "e74c3c": 23, "each": [2, 6, 25, 26, 27, 29, 32, 37, 38, 43, 46, 47, 48, 51, 57], "earli": [32, 33, 34], "earn": [32, 33, 46], "earned_at": 14, "easi": 46, "easier": [27, 28], "easili": 25, "east": 56, "eastern": 56, "ec": 56, "ecf0f1": 23, "econom": [8, 31, 33, 36], "ecosystem": [40, 48, 50, 51, 56, 57], "edg": [36, 37, 43, 44, 48, 50, 53], "edinburgh": [8, 9], "edit": [21, 35, 40, 49, 51, 52], "editori": 25, "edtech": 53, "educ": [2, 19, 21, 26, 30, 31, 33, 34, 37, 41, 48, 50, 53, 56], "education_level": 19, "effect": [0, 2, 3, 15, 17, 19, 21, 24, 25, 26, 29, 31, 33, 35, 36, 37, 44, 46, 47, 48, 50, 52, 53, 59], "effective_d": 13, "effective_study_time_minut": 14, "effectiveness_at_optim": 14, "effectiveness_metr": 14, "effectiveness_r": 15, "effectiveness_scor": [5, 14, 15], "effectiveness_threshold": 5, "effectiveness_trend": 14, "effici": [0, 2, 3, 4, 14, 17, 22, 24, 28, 29, 31, 32, 35, 36, 37, 39, 41, 42, 43, 46, 48, 50, 53, 55, 59], "efficiency_improv": 3, "efficiency_metr": 3, "efficiency_rank": 3, "efficiency_scor": 4, "effort": [21, 26, 31, 37, 58], "elabor": 37, "elast": [24, 41], "element": [1, 14, 23, 25, 40, 42, 43, 45, 47, 48], "elif": [6, 19], "elig": 47, "elimin": [50, 53, 59], "elk": 27, "els": [6, 19, 21, 58], "email": [1, 4, 6, 7, 10, 11, 12, 13, 18, 23, 27, 30, 35, 36, 40, 42, 43, 45, 47, 49, 52], "email_from_address": 18, "email_from_nam": 18, "email_notif": 18, "email_recipi": 3, "email_verifi": 7, "emailinput": [42, 45], "emerg": [3, 13, 24, 32, 36, 37, 44, 54, 56], "emoji": 25, "emot": 31, "emphas": 37, "emphasi": 37, "emphasis": 28, "emploi": 57, "employ": [9, 28, 32, 33, 35, 58], "employe": [2, 12, 24, 29, 34, 41, 47, 48, 50, 52, 54], "employee_data": 24, "employee_id": 12, "employer_reimburs": 9, "employer_reimbursement_r": 9, "employer_reimbursement_receiv": 9, "empti": 55, "en": 1, "en_gb": [1, 23], "enabl": [2, 12, 14, 18, 26, 27, 35, 36, 40, 43, 46, 51, 52, 55, 56], "enable_agent3_analyt": 40, "enable_agent4_car": 40, "enable_ai_recommend": 18, "enable_all_featur": 40, "enable_api_rate_limit": 18, "enable_automated_insight": 18, "enable_metr": 18, "enable_multi_ten": 18, "enable_org_manag": 40, "enable_predictive_analyt": 18, "enable_query_cach": 18, "enable_sso": [10, 18], "enable_study_track": 40, "encod": 19, "encourag": [17, 31, 37, 46], "encrypt": [2, 5, 6, 14, 18, 20, 24, 27, 29, 41, 43, 44, 48, 50, 52, 55, 57], "encrypted_data": 19, "encryptedmodelstorag": 19, "encryption_en": 12, "encryption_kei": 19, "end": [13, 14, 21, 40, 41, 48, 52, 55, 57], "end_dat": [10, 12, 13, 14, 15], "ended_at": 15, "endif": [1, 23], "endpoint": [3, 4, 10, 11, 13, 17, 20, 21, 26, 29, 35, 40, 48, 52, 53, 54, 55, 56, 57], "endpoint_access": 12, "energi": 37, "energy_level": [5, 14], "energy_level_end": 14, "enforc": [6, 44], "engag": [0, 1, 2, 23, 24, 26, 34, 35, 37, 41, 44, 46, 47, 53, 56, 58, 59], "engagement_scor": [3, 10], "engagement_trend": 3, "engin": [0, 5, 8, 10, 12, 17, 28, 29, 34, 35, 36, 47, 48, 51, 53, 55, 56, 57], "england": 9, "english": [0, 1, 11, 21, 56], "enhanc": [1, 2, 3, 7, 8, 17, 21, 23, 24, 30, 32, 35, 37, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 53], "enough": 5, "enrich": 48, "enrol": [10, 56], "enrollmentapi": 56, "enrollmentmanag": 56, "ensembl": 19, "ensur": [1, 5, 7, 11, 18, 19, 21, 23, 25, 26, 27, 29, 31, 32, 34, 35, 36, 37, 40, 43, 44, 45, 46, 47, 49, 51, 52, 53, 54, 55, 58], "enter": [35, 42, 43, 45], "enterpris": [1, 2, 4, 6, 8, 9, 12, 14, 20, 22, 23, 25, 30, 34, 35, 36, 37, 40, 43, 44, 46, 51, 52, 53, 55, 56, 58], "enterprise analyt": 3, "enterprise deploy": 40, "enterprise_admin": [1, 25], "enterprise_analyt": 24, "enterprise_budget": 55, "enterprise_config": 18, "enterprise_guid": [1, 26, 34], "enterprise_id": [4, 55], "enterprise_profil": 59, "enterprise_unlimit": 24, "enterprise_user_manag": 24, "enterpriseanalyt": 40, "enterpriseauthservic": 54, "enterpriseprofil": 59, "entir": [22, 47, 56], "entiti": [11, 12, 16], "entities_to_sync": 12, "entity_id": 18, "entity_typ": 12, "entri": [4, 9, 21, 28, 30, 33, 35, 47], "env": [18, 21, 40, 49], "environ": [5, 6, 14, 20, 22, 27, 28, 31, 32, 33, 40, 41, 44, 47, 48, 50, 56], "epub": [0, 23], "equity_valu": 8, "equival": 8, "ergonom": 44, "erp": 29, "error": [0, 17, 18, 19, 21, 22, 25, 27, 35, 42, 43, 47, 48, 49, 52, 55], "error_cod": [16, 17, 59], "error_handl": 12, "error_id": [16, 17, 59], "error_r": 3, "error_rate_perc": 18, "errorboundari": 42, "escal": [27, 29, 34], "eslint": [22, 43], "especi": 33, "essenti": [26, 27, 28, 29, 30, 31, 32, 33, 34, 47, 48, 52, 53], "establish": [31, 34, 58], "estim": [2, 5, 20, 31, 33, 36, 37, 47, 48, 50, 53, 55], "estimate_difficulti": 19, "estimate_study_hour": 55, "estimate_training_cost": 55, "estimated_budget_need": 10, "estimated_complet": [3, 10, 12, 14], "estimated_completion_d": [3, 14, 15], "estimated_cost": 9, "estimated_duration_month": [4, 5], "estimated_hour": 5, "estimated_invest": 9, "estimated_learning_time_hour": 8, "estimated_quest": 14, "estimated_roi": 59, "estimated_study_hour": 5, "estimated_study_tim": 19, "estimated_study_time_week": 8, "estimated_study_week": [5, 58], "estimated_weeks_to_complet": 14, "etc": [1, 25, 36, 54], "ethic": [26, 32], "eu": 9, "eur": 9, "europ": [12, 36, 56], "european": [41, 56], "evalu": [5, 8, 17, 19, 20, 24, 27, 31, 32, 34, 37, 41, 45, 47, 48, 58], "even": 44, "event": [10, 11, 27, 28, 29, 32, 33, 37, 40, 41, 52, 53, 56, 57, 58], "event_typ": 10, "events_subscrib": 12, "eventsourc": 40, "eventu": 57, "ever": 37, "everi": [3, 7, 24, 36, 47], "everyon": 21, "everyth": [18, 35, 40, 43, 47, 49, 58], "evolut": [4, 36, 54], "evolv": [18, 47], "exam": [3, 4, 5, 9, 14, 19, 20, 26, 31, 32, 33, 34, 35, 37, 43, 46, 47, 53, 55, 56], "exam_cost": 55, "exam_fe": [4, 9, 21, 55], "exam_readiness_scor": 14, "exam_score_target": 14, "examin": 44, "exampl": [0, 1, 7, 11, 16, 21, 22, 23, 28, 42, 43, 45, 48, 49, 51, 52, 56, 58], "examregistr": 56, "examregistrationapi": 56, "exce": 46, "exceed": [4, 5, 6, 7, 8, 9, 10, 11, 55], "excel": [2, 14, 17, 23, 29, 36, 41, 42, 44, 46, 55, 57], "except": [2, 6, 19, 24, 48], "exchang": [24, 48, 50, 55, 56], "exclus": 56, "execut": [5, 8, 10, 17, 24, 29, 31, 32, 34, 36, 40, 41, 47, 48, 50, 51, 57, 58, 59], "executive_data": 40, "executive_summari": 3, "exercis": [14, 26, 32], "exist": [5, 16, 21, 24, 26, 29, 30, 31, 34, 41, 47], "exp": 6, "expand": [0, 3, 25, 28, 32], "expans": [17, 25, 36, 56], "expect": [3, 17, 21, 25, 28, 31, 34, 36, 42, 45, 46, 47, 58], "expected_attribut": 12, "expected_benefit": 15, "expected_complet": 9, "expected_exam_cost": 55, "expected_outcom": 8, "expected_roi": 9, "expected_salary_increas": [4, 8, 9], "expedit": 41, "expens": [31, 32, 33, 34, 47], "experi": [2, 3, 5, 8, 17, 19, 22, 23, 24, 28, 30, 32, 33, 34, 35, 36, 37, 39, 42, 44, 45, 48, 50, 51, 57, 58, 59], "experience_align": 8, "experience_level": [4, 5, 8, 55], "experience_requir": 8, "experience_year": [4, 5, 8, 9, 19], "experiment": 26, "expert": [14, 19, 25, 31, 32, 35, 41, 43, 46, 47, 58], "expertis": [8, 32, 37, 47], "expir": [4, 6, 7, 20, 40, 58], "expiredsignatureerror": 6, "expires_at": [6, 10, 13, 58], "expires_in": [6, 7], "expires_in_dai": 13, "expiri": [6, 21], "explain": 28, "explan": [0, 14, 21, 38, 39, 53], "explor": [0, 1, 22, 23, 32, 33, 35, 36, 46, 48, 49, 50], "exponenti": 12, "exponentialsmooth": 53, "export": [3, 12, 22, 24, 26, 27, 29, 35, 36, 41, 43, 47, 52, 57], "export_data": 10, "expos": [1, 40], "exposur": 17, "ext": [1, 23], "extend": [17, 34, 44, 47], "extend_sess": 5, "extens": [0, 1, 23, 36, 43, 51], "extern": [0, 1, 2, 5, 12, 18, 19, 20, 23, 27, 29, 31, 35, 37, 43, 44, 47, 48, 49, 50, 52, 53, 56, 57], "external_api": 18, "external_id": 12, "external_system": 12, "extra": [1, 33], "extract": [26, 59], "extract_prediction_featur": 53, "extrem": [27, 33], "ey": 44, "eyj0exaioijkv1qilcjhbgcioijiuzi1nij9": 6, "eyjhbgcioijiuzi1niisinr5cci6ikpxvcj9": 7, "f": [6, 11, 19, 40, 49, 58], "f39c12": 23, "f5": 43, "f8f9fa": 23, "f99d1c4": 53, "f_score": 55, "face": [21, 31, 44, 47, 52], "facilit": 56, "factor": [2, 3, 9, 12, 17, 20, 24, 29, 31, 32, 34, 35, 36, 37, 40, 41, 44, 48, 50, 53, 54, 55, 57], "factori": 20, "faculti": 26, "fail": [6, 12, 31, 33, 43, 51, 58], "failed_addit": 13, "failed_import": 10, "failed_integr": 12, "failed_invit": 13, "failed_sync": 12, "failed_valid": 19, "failov": [18, 24], "failover_threshold_minut": 18, "failur": [31, 34, 35, 51], "fair": [11, 31, 36], "fallback": [0, 22], "fals": [1, 4, 5, 7, 8, 9, 10, 12, 13, 18, 23, 42], "famili": 22, "familiar": [28, 37], "faq": [23, 47, 48, 58], "fast": [0, 8, 43, 46, 47], "fastapi": [11, 20, 40, 49], "faster": [2, 22, 28, 37, 43, 48, 53], "fastest": [36, 49], "fastest_growing_domain": 8, "fastest_payback": 9, "fatigu": 15, "favourit": 31, "fc": 42, "fcp": 45, "feasibility_r": 8, "feasibl": [8, 28], "feat": [21, 52, 53, 56], "featur": [0, 3, 5, 6, 10, 12, 14, 18, 19, 20, 24, 25, 26, 27, 29, 30, 35, 39, 40, 41, 42, 45, 46, 50, 52, 57, 59], "feature1": 23, "feature2": 23, "feature_usag": 40, "features_en": 10, "fee": [9, 31, 33, 34, 35, 46, 47, 55, 56], "feedback": [1, 21, 22, 23, 25, 26, 30, 34, 36, 37, 42, 43, 46, 48, 53], "fernet": 19, "ferpa": [2, 24, 26, 27, 37, 41, 48, 50], "fetch": [22, 23, 39, 40], "fetchdashboarddata": 39, "few": [44, 58], "field": [4, 7, 11, 20, 28, 29, 30, 33, 42, 46], "field_map": 12, "file": [1, 6, 18, 21, 22, 23, 27, 35, 41, 49, 51], "file_path": 19, "fill": [35, 42, 43, 45, 46], "filter": [0, 3, 6, 12, 13, 15, 19, 22, 23, 33, 36, 37, 38, 39, 42, 43, 44, 46, 47, 52, 53, 54, 56, 58], "final": [15, 25, 42, 55], "final_not": 15, "final_scor": 12, "financ": [28, 54], "financi": [8, 9, 10, 24, 28, 29, 30, 31, 34, 36, 47, 48, 52, 53, 54, 55, 56], "financial_analysi": 8, "financial_impact": 3, "financial_project": 8, "financial_summari": 9, "find": [4, 13, 26, 30, 35, 36, 42, 45, 55], "find_optimal_path": 55, "findabl": 0, "findbytext": [42, 45], "fine": [24, 35, 57, 58], "fingerprint": [44, 47], "finish": 58, "fireev": [42, 45], "firefox": [36, 43], "firewal": 27, "first": [4, 5, 14, 17, 20, 21, 22, 24, 28, 33, 37, 39, 40, 41, 42, 45, 47, 50, 52, 53, 54, 56, 57, 58, 59], "first_nam": [7, 12, 13], "first_sess": 14, "firstcontentfulpaint": 45, "firstpaint": 45, "fiscal": 34, "fit": [19, 53], "five": [36, 46], "five_year_roi": 4, "fix": [17, 21], "fixtur": 21, "flag": [18, 28, 35, 40], "flake8": [20, 21], "flashcard": [14, 33], "flex": 42, "flexibl": [13, 24, 34, 37, 41], "float": [19, 43, 59], "flow": [1, 12, 17, 21, 22, 41, 43, 47, 48, 51, 52, 59], "flowchart": [1, 23], "fn": [42, 45], "focu": [0, 3, 4, 5, 8, 13, 14, 15, 20, 21, 25, 28, 31, 32, 33, 36, 42, 44, 45, 46, 47, 51, 52, 53], "focus": [25, 31, 33, 34, 37, 53], "focus_area": [5, 14], "focus_domain": 15, "focus_scor": 15, "foldabl": 44, "folder": 22, "follow": [11, 17, 20, 21, 25, 26, 27, 28, 32, 46, 49, 52, 53, 57, 58, 59], "font": [0, 1, 22], "footer": 39, "forbidden": [6, 7, 11], "forc": 51, "force_authent": 12, "force_upd": 12, "forecast": [2, 17, 24, 32, 37, 41, 48, 50, 55, 59], "forecast_period": 3, "forest": [2, 19, 50, 53], "fork": 21, "forkrul": [21, 40, 49], "form": [22, 36, 42, 43, 45, 52, 55], "formal": 33, "format": [1, 3, 4, 10, 12, 17, 18, 20, 21, 23, 26, 29, 32, 33, 35, 37, 40, 42, 43, 44, 47, 48, 52], "formstat": 42, "fortun": [24, 52, 54], "fortune500corp": 24, "forum": [17, 26, 28, 30, 36, 40, 43, 46, 47, 58], "forward": [16, 17, 59], "found": [1, 4, 8, 9, 10, 11, 14, 16, 23, 55, 58], "foundat": [3, 4, 17, 22, 31, 32, 33, 43, 47, 51, 52, 53, 56, 58], "foundational_train": 4, "four": 14, "framer": [22, 43], "framework": [0, 1, 2, 17, 20, 22, 24, 26, 29, 31, 34, 48, 50, 54, 56, 58], "franc": 56, "francisco": [4, 7], "free": [5, 26, 31, 33, 44, 47, 52, 53], "freemium": 52, "frequenc": [12, 14, 25, 33, 37, 44, 46, 51, 52, 57], "frequent": [1, 17, 20, 23, 25, 33, 43, 47], "fresh": [25, 27, 55], "fridai": [14, 15], "friendli": [0, 1, 32, 33, 38, 39, 42, 43, 46, 47, 55, 59], "from": [0, 1, 2, 3, 6, 10, 11, 15, 16, 17, 18, 19, 21, 22, 23, 24, 25, 28, 30, 31, 32, 33, 35, 36, 37, 38, 40, 41, 44, 46, 47, 48, 51, 52, 53, 56, 58, 59], "from_role_id": 4, "fromisoformat": 6, "fromtimestamp": 6, "frontend": [1, 18, 21, 27, 39, 48, 49, 52, 55], "frontend_host": 18, "frontend_implement": 1, "frontend_port": 18, "frontend_url": 27, "fulfil": 55, "full": [2, 5, 6, 11, 12, 17, 20, 21, 23, 24, 27, 32, 36, 37, 41, 42, 43, 44, 47, 48, 50, 52, 53, 54, 55], "full_backup_frequ": 18, "full_flow": 12, "full_nam": [10, 12, 58], "fulli": [31, 37, 41, 46, 54], "fullnam": 12, "fullscreen": 0, "function": [0, 1, 2, 8, 11, 17, 18, 20, 21, 22, 23, 24, 25, 29, 33, 35, 37, 40, 42, 43, 44, 45, 47, 48, 52, 53, 57, 59], "fund": [9, 26, 32], "fundament": [5, 8, 15, 28, 32, 33, 43], "further": 36, "futur": [2, 3, 24, 30, 31, 32, 35, 36, 37, 48, 50, 54], "future_project": 9, "g": [1, 21, 23], "g_score": 55, "gain": [2, 3, 8, 15, 22, 24, 28, 32, 33, 41], "galleri": 58, "gamif": [2, 14, 24, 48, 50, 52], "gamifi": [14, 47, 53, 58], "gap": [8, 17, 26, 28, 29, 31, 32, 33, 34, 37, 41, 42, 47, 48, 50, 53, 54, 55, 58, 59], "gap_analysi": 8, "gap_percentag": 3, "gap_sever": 5, "gate": 52, "gatewai": [12, 41, 52, 57], "gather": 34, "gaug": 3, "gbp": [9, 21], "gcih": 32, "gcp": [18, 32], "gdpr": [2, 10, 17, 20, 24, 27, 29, 37, 41, 44, 48, 50, 53, 54, 55, 57, 59], "gdpr_compliant": 10, "gdpr_enabl": 18, "gdprreport": 54, "gem": 4, "gener": [0, 2, 4, 5, 6, 7, 10, 13, 16, 22, 23, 24, 26, 28, 29, 31, 33, 34, 35, 36, 37, 40, 41, 43, 46, 47, 48, 50, 52, 53, 54, 56, 58, 59], "generalist": 32, "generate_enterprise_insight": 40, "generate_executive_report": 40, "generate_gdpr_report": 54, "generate_hipaa_report": 54, "generate_improvement_recommend": 53, "generate_market_trend": 54, "generate_recommend": 53, "generate_salary_intellig": 54, "generate_sox_report": 54, "generate_study_plan": 2, "generated_at": [3, 5, 8, 9, 10, 12, 13, 14], "gentl": 44, "genuin": 28, "geograph": [7, 29, 32, 35, 36, 55], "germani": 56, "gestur": [0, 43, 44], "get": [3, 6, 7, 9, 11, 15, 16, 18, 19, 22, 23, 25, 29, 30, 31, 34, 41, 43, 45, 49, 52, 53, 54, 55, 56], "get_agent3_analyt": 40, "get_ai_recommend": 40, "get_api_metr": 40, "get_career_neighbor": 55, "get_career_recommend": 40, "get_certification_cost": 55, "get_commission_r": 56, "get_current_us": 40, "get_db_metr": 40, "get_executive_overview": 40, "get_feature_metr": 40, "get_head": 6, "get_learning_analyt": [2, 40], "get_optimal_difficulti": 2, "get_organization_analyt": 40, "get_organization_context": 54, "get_payment_schedul": 56, "get_session_analyt": 40, "get_study_recommend": 2, "get_user_hourly_r": 55, "get_user_id_from_traefik": 59, "get_user_metr": 40, "getbylabeltext": [42, 45], "getbyplacehold": 42, "getbyrol": [42, 45], "getbytestid": 45, "getbytext": [42, 45], "getentriesbytyp": 45, "gettext": 23, "gettext_compact": [1, 23], "gh": [1, 23], "git": [21, 25, 40, 49, 51], "github": [11, 17, 21, 23, 27, 40, 49], "github_token": [1, 23], "given": [37, 42, 45], "givennam": 12, "glanc": [44, 58], "global": [17, 24, 29, 34, 36, 39, 41, 43, 48, 50, 53, 54, 55, 56], "gmail": [18, 49], "gmv": 56, "go": [4, 11, 21, 43, 44, 55], "goal": [2, 5, 11, 12, 21, 31, 32, 33, 34, 36, 37, 43, 44, 46, 47, 48, 50, 52, 53, 54, 55, 58, 59], "goal_1": 15, "goal_789": [12, 14], "goal_achiev": 13, "goal_detail": 14, "goal_feas": 14, "goal_id": [14, 15], "goal_notif": 13, "goal_typ": [14, 15], "goal_weekly_123": 15, "goals_complet": 15, "goals_tot": 15, "gold": 14, "good": [5, 9, 14, 15, 31, 32, 36, 44, 46], "googl": [3, 15, 23, 27, 32], "googletagmanag": [1, 23], "goto": [42, 45], "govern": [14, 28, 30, 32, 33, 35, 36, 48], "governance_risk_compli": 5, "gpen": 32, "gpu_acceler": 18, "grace": [42, 43], "grade": [2, 4, 7, 12, 15, 17, 20, 24, 26, 36, 37, 40, 41, 43, 44, 47, 48, 50, 52, 54, 55, 57, 58, 59], "grade_passback": 12, "gradebook": 24, "gradient": [19, 23, 53], "gradientboostingregressor": 53, "gradual": [18, 28, 35, 37, 41, 44, 53], "graduat": 33, "grafana": 27, "grai": 42, "grain": [24, 57], "grammar": 21, "grant": [24, 26], "granular": [7, 13, 24, 29, 41, 44, 47, 50, 54, 57], "graph": [0, 36], "graphql": 29, "grc": 28, "greater": 4, "greet": 47, "grep": 23, "grid": [23, 39, 42], "gross": 56, "group": [3, 4, 5, 17, 24, 27, 28, 29, 31, 32, 33, 34, 35, 41, 47], "group_access": 12, "group_membership": 12, "group_sync": 12, "groups_match": 12, "grow": [4, 21, 32, 35], "growth": [22, 24, 32, 36, 41, 46, 47, 48, 52, 53, 54, 55, 56, 58, 59], "growth_project": [4, 8], "growth_rat": [4, 8], "gsec": 33, "gtag": [1, 23], "guarante": 4, "guest": [6, 20], "guid": [2, 5, 6, 8, 9, 10, 12, 14, 15, 16, 17, 19, 20, 26, 27, 28, 29, 30, 31, 32, 33, 34, 38, 50, 53, 54, 55, 59], "guidanc": [0, 2, 8, 11, 25, 27, 30, 31, 32, 37, 40, 41, 47, 48, 53, 55, 58, 59], "guidelin": [20, 26, 30, 34, 48], "guru": 5, "gzip": 20, "h": [16, 42, 59], "ha": [0, 12, 22, 29, 32, 33, 54, 55, 59], "habit": [2, 3, 13, 37, 44, 46, 47], "hack": 32, "hand": [3, 5, 8, 9, 14, 26, 28, 31, 32, 33, 34, 44, 47, 53, 58], "handl": [0, 11, 17, 22, 27, 29, 31, 32, 34, 35, 37, 42, 43, 44, 45, 48, 52], "handleclick": 45, "handlesubmit": 42, "handoff": 44, "hands_on": [5, 8, 58], "hands_on_practic": 5, "happen": [21, 44, 53], "happi": 47, "harden": [18, 27], "hardwar": 49, "harmoni": 2, "hash": 52, "have": [1, 21, 28, 32, 33, 36, 40, 44, 47, 49, 57, 58], "head": [21, 31, 40, 49], "header": [3, 4, 6, 7, 11, 13, 15, 16, 17, 18, 25, 40, 58, 59], "health": [12, 17, 27, 29, 35, 40, 44, 48, 49, 52, 55], "health_check": 18, "health_check_interv": 18, "health_check_interval_second": 18, "health_check_url": 12, "healthcar": [36, 41, 54], "healthi": [12, 40], "healthy_integr": 12, "heat": 47, "heatmap": 3, "heavi": [24, 27], "heavili": 5, "height": 23, "help": [1, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 40, 43, 47, 48, 49, 53], "here": [21, 30, 49], "hero": [39, 43], "heurist": 55, "hidden": [4, 48, 55], "hidden_cost": 4, "hierarch": [0, 6, 10, 13, 24, 29, 35, 41, 48, 50, 54], "hierarchi": [0, 13, 20, 43, 54], "high": [0, 1, 3, 4, 5, 9, 13, 14, 15, 24, 29, 31, 32, 34, 35, 36, 37, 38, 41, 46, 47, 48, 52, 53, 54, 57, 58, 59], "high_risk_ev": 10, "higher": [2, 3, 28, 32, 33, 34, 47, 53], "highest": [3, 9, 36, 37, 42, 45, 46, 54], "highest_perform": 3, "highest_roi": 3, "highli": [28, 32, 46], "highlight": [1, 23, 28, 32, 43, 46], "hint": [20, 21], "hipaa": [2, 17, 24, 27, 29, 37, 41, 44, 48, 50, 54, 59], "hipaa_en": 18, "hipaareport": 54, "hire": 31, "hire_d": 12, "hiring_veloc": 8, "histor": [3, 11, 19, 36, 37, 48, 50, 53, 55], "histori": [17, 35, 36, 52, 58], "historical_perform": 3, "historical_spend": 9, "hit": [35, 55], "hmac": 12, "holidai": 10, "holist": [17, 46, 48, 59], "holisticrecommend": 59, "home": [28, 33, 42, 43, 44], "home_offic": 14, "homepag": [22, 48, 53], "hook": [18, 22, 39], "horizont": 52, "host": [1, 23, 40], "hot": [20, 22, 39, 43, 46], "hour": [4, 7, 11, 15, 28, 30, 31, 33, 34, 36, 43, 44, 45, 46, 47, 52], "hourli": 18, "hourly_r": [9, 55], "hours_per_week": [5, 14], "hover": 0, "how": [26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 44, 46, 48, 58], "howev": 32, "hr": [3, 11, 12, 13, 24, 29, 34, 41, 47, 50, 54], "hr_system": 18, "hr_system_api_token": 12, "hri": 41, "hs256": [6, 18, 40, 49], "html": [0, 1, 21, 23, 25, 51], "html_css_file": [1, 23], "html_extra_path": 1, "html_js_file": [1, 23], "html_search_languag": 1, "html_search_opt": 1, "html_search_scor": 1, "html_static_path": [1, 23], "html_theme": [1, 23], "html_theme_opt": [1, 23], "htmllabel": 23, "http": [1, 4, 6, 7, 11, 12, 13, 16, 17, 18, 20, 21, 22, 23, 27, 36, 40, 43, 47, 49, 58, 59], "httperror": 6, "httpexcept": 6, "hub": [1, 16, 46, 47, 48, 51, 55, 57], "human": [35, 41], "hybrid": [19, 41], "hybrid_recommend": 19, "i": [1, 6, 11, 12, 15, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 57], "iam": 5, "icon": [0, 22, 44], "iconographi": 22, "id": [1, 2, 4, 5, 6, 8, 9, 10, 11, 12, 16, 17, 18, 21, 23, 24, 27, 40, 45, 52, 54, 56, 59], "idea": 26, "ideal": 28, "ident": [5, 12, 20, 22, 24, 41, 43, 54], "identif": [2, 20, 25, 29, 31, 35, 37, 47, 48, 50, 54, 55], "identifi": [3, 5, 8, 19, 21, 25, 26, 28, 31, 32, 33, 34, 35, 36, 37, 47, 51, 53, 58], "identify_key_factor": 53, "identify_knowledge_gap": 53, "idle_timeout_minut": 13, "idnumb": 12, "idp": 18, "ifconfig": 23, "ii": [41, 57], "illustr": [36, 38], "imag": [1, 7, 20, 23, 40, 42, 43], "img": 23, "immedi": [8, 9, 24, 28, 34, 46, 48, 50], "immediate_act": 8, "impact": [2, 4, 5, 9, 10, 24, 26, 29, 31, 34, 35, 36, 37, 41, 47, 48, 50, 53, 54, 55, 58], "implement": [1, 3, 4, 5, 6, 8, 13, 14, 17, 18, 20, 21, 22, 27, 28, 29, 32, 34, 36, 38, 40, 41, 43, 45, 48, 50, 51, 58], "implementation_effort": 3, "implementation_timelin": 4, "implic": 21, "import": [2, 6, 11, 12, 15, 19, 21, 23, 24, 27, 29, 32, 33, 35, 40, 41, 44, 46, 49, 58], "import_abc123": 10, "import_format": 10, "import_id": 10, "importance_scor": 5, "improv": [1, 2, 3, 5, 14, 15, 17, 21, 23, 24, 26, 29, 31, 32, 34, 35, 36, 37, 41, 43, 44, 46, 47, 48, 50, 51, 52, 53, 54, 58], "improvement_area": [3, 13], "improvement_potenti": 8, "improvement_r": 14, "improvement_trend": 13, "in_progress": [10, 13, 14], "inaccur": 36, "inact": [7, 13, 59], "incent": [31, 34], "incid": [5, 8, 10, 13, 28, 31, 34, 54, 58], "incident_respons": 5, "includ": [0, 1, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 17, 21, 23, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 38, 40, 42, 45, 46, 47, 51, 55, 59], "include_analyt": 14, "include_books_materi": 9, "include_hidden_cost": 4, "include_materi": 21, "include_memb": 3, "include_metadata": 12, "include_opportunity_cost": 9, "include_practice_exam": 9, "include_predict": [10, 14, 58], "include_progress": 14, "include_project": [3, 4, 9], "include_recommend": 14, "include_rout": 40, "include_sect": 13, "include_stat": 10, "include_team": [3, 13], "include_training_cours": 9, "include_us": 10, "includehidden": 23, "inclus": [21, 52], "incognito": [36, 43], "incom": 47, "incomplet": 35, "inconsist": [17, 25, 51], "incorpor": [1, 23, 25, 35, 37], "incorrect": [46, 51], "increas": [2, 3, 4, 8, 9, 13, 14, 33, 36, 37, 46, 47, 53], "increase_difficulti": 5, "increase_premium_by_50": 10, "increase_weekly_hour": 14, "increased_study_tim": 3, "increasingli": 33, "increment": [2, 12, 53], "incremental_backup_frequ": 18, "independ": [29, 52, 57], "index": [1, 20, 23, 25, 27, 29, 35, 41, 48, 51, 52, 55], "india": 56, "indic": [15, 16, 17, 22, 34, 35, 42, 44, 45, 46, 47, 51, 54], "indirect": 47, "individu": [2, 3, 5, 8, 9, 19, 21, 24, 29, 31, 34, 36, 41, 48, 50, 52, 53, 54], "industri": [4, 8, 10, 13, 24, 25, 26, 29, 31, 32, 34, 35, 36, 37, 40, 41, 43, 46, 47, 48, 50, 51, 54, 55, 58], "industry_average_cost": 3, "infer": [2, 27, 53], "infinit": 42, "influenc": 36, "info": [18, 27], "infograph": 44, "inform": [1, 4, 7, 8, 9, 10, 12, 13, 17, 19, 20, 23, 25, 26, 30, 31, 32, 33, 35, 36, 37, 39, 41, 43, 44, 46, 47, 51, 52, 56, 58], "infrastructur": [22, 35, 36, 41, 52, 56], "infrastructure_cost": 3, "inherit": [1, 23], "ini": 18, "init": [1, 23], "initi": [0, 2, 7, 10, 13, 22, 23, 24, 26, 27, 28, 30, 36, 40, 43, 48, 55, 56, 58], "initial_plan": 14, "inlin": [1, 22, 23], "innov": [26, 50], "input": [11, 19, 25, 26, 33, 36, 42, 43, 45, 47, 52, 55], "insert": 20, "insight": [2, 3, 9, 10, 13, 14, 16, 17, 24, 29, 30, 33, 34, 35, 36, 40, 41, 43, 44, 47, 48, 50, 53, 54, 55, 58, 59], "inspect": 19, "inspir": 21, "instal": [1, 2, 4, 6, 9, 11, 21, 22, 23, 24, 25, 30, 40, 41, 42, 43, 44, 47, 48, 50, 51, 58], "instanc": 35, "instant": [2, 5, 22, 37, 42, 43, 44, 47, 55, 58], "instead": 17, "institut": [12, 26, 48, 56], "instruct": [1, 23, 25, 27, 30, 33, 40, 49], "instructor": [26, 31, 34, 41], "instructur": 18, "insuffici": [5, 6, 7, 8, 9, 10, 11, 12], "insufficient_data": 5, "insufficient_experience_data": 8, "insufficient_market_data": 9, "int": [19, 21, 54], "integr": [2, 3, 16, 18, 20, 21, 23, 25, 27, 30, 31, 33, 34, 35, 36, 37, 43, 44, 46, 47, 48, 51, 54, 55], "integrate_training_provid": 56, "integration_configuration_invalid": 12, "integration_hub": 1, "integration_id": 12, "integration_pattern": [16, 59], "integration_typ": 12, "intellig": [2, 5, 11, 13, 14, 20, 24, 29, 34, 37, 38, 41, 42, 43, 44, 47, 48, 51, 53, 54, 56], "intellisens": [22, 43], "intens": [9, 28, 36, 47], "intensive_study_hour": 5, "inter": [22, 51, 57], "interact": [1, 2, 3, 5, 21, 22, 23, 24, 25, 36, 38, 39, 42, 43, 44, 47, 48, 49, 53, 55, 56, 58, 59], "interest": [2, 21, 32, 33, 37, 43, 47], "interfac": [11, 17, 20, 22, 24, 29, 33, 35, 36, 39, 42, 44, 45, 46, 47, 48, 49, 52, 53, 55, 56, 59], "interim": 8, "interleav": 37, "intermedi": [5, 8, 14, 15, 19, 33, 37, 43, 55, 58], "intern": [4, 7, 11, 16, 17, 29, 31, 38, 56], "internation": [22, 23, 52], "internet": [2, 24, 36, 37, 42, 43, 44, 46, 47, 53], "internship": 33, "interpret": 36, "interrog": 37, "interrupt": 14, "intersphinx": [0, 1, 23], "interv": [2, 36, 37, 48, 50, 51, 53, 55], "intervent": [26, 34], "interview": [26, 28, 32, 33], "intl": 23, "introduct": [1, 39, 43], "intuit": [0, 17, 24, 25, 36, 39, 43, 44, 56, 58], "inv_abc123": 13, "invalid": [4, 6, 7, 8, 9, 10, 11, 12, 14, 16, 21, 35, 42], "invalid_credenti": 7, "invalid_password": 10, "invalid_request": 4, "invalidtokenerror": 6, "inventori": 41, "invest": [3, 8, 9, 16, 17, 24, 31, 32, 34, 35, 36, 37, 41, 46, 47, 48, 50, 53, 55, 58, 59], "investment_breakdown": 3, "investment_cost": 4, "invit": [40, 52, 54], "invitation_data": 40, "invitation_id": 13, "invitation_s": 10, "invitations_s": 13, "io": [17, 24, 44, 50], "ip": [15, 29, 35], "ip_address": [7, 10, 13], "irb": 26, "is_curr": 7, "isauthent": [22, 39], "isc": [33, 56], "isc2": [12, 43], "island": 9, "isload": [22, 39, 42], "isn": 23, "isoformat": 40, "isol": [6, 10, 13, 17, 24, 29, 35, 41, 48, 49, 50, 54, 57, 59], "isort": [20, 21], "issu": [11, 17, 21, 25, 27, 29, 30, 31, 35, 36, 40, 43, 44, 47, 49, 58], "issuanc": 56, "isvalid": 42, "ital": [1, 23], "item": [1, 11, 19, 23, 42, 54], "ivborw0kggoaaaansuheugaa": 7, "j": [1, 21, 23, 27, 48], "japan": 56, "java": 4, "javascript": [4, 11, 22, 23, 36, 43, 48], "jbswy3dpehpk3pxp": 7, "jerimi": 11, "jest": [0, 42, 45], "job": [1, 11, 19, 23, 30, 32, 33, 34, 36, 41, 47, 48, 54, 55, 58], "job_avail": 8, "job_count": 4, "job_market_demand": 9, "job_open": 4, "job_openings_count": 8, "job_titl": [8, 10, 12], "jobtitl": 12, "john": [1, 7, 10, 12, 13, 23], "john_do": [1, 23], "johnson": [3, 10, 13], "join": [3, 8, 13, 21, 26, 28, 30, 32, 33, 58], "joined_at": 13, "journei": [1, 30, 32, 33, 37, 38, 40, 43, 44, 45, 47, 48, 52, 53, 55, 56, 58], "jsdelivr": [1, 23], "json": [1, 5, 6, 8, 9, 10, 11, 12, 14, 18, 22, 23, 27, 40, 45, 58, 59], "june": [25, 30], "junior": [4, 8, 28, 32, 33], "just": [21, 32, 37, 41, 44, 58], "justif": [32, 34, 54], "justifi": [31, 32, 42], "jwt": [0, 6, 7, 11, 17, 18, 20, 22, 40, 42, 43, 47, 48, 52, 54], "jwt_algorithm": 40, "jwt_secret_kei": 40, "k": [2, 19, 50], "kb": [22, 43, 48], "keep": [17, 26, 27, 28, 31, 32, 41, 43, 44, 53], "kei": [3, 4, 7, 11, 13, 15, 16, 18, 20, 22, 23, 26, 27, 29, 34, 35, 36, 39, 40, 42, 43, 44, 45, 47, 48, 49, 51, 52, 53, 56, 58], "key_factor": [8, 19, 53], "key_metr": [3, 59], "key_mileston": 8, "key_recommend": 5, "key_requir": 8, "key_trend": 4, "keyboard": [0, 42, 44, 45, 47], "kind": 40, "kinesthet": [2, 5, 17, 37, 53, 59], "kingdom": [8, 9, 10, 21], "kmean": [2, 19], "knowledg": [2, 3, 5, 13, 14, 15, 17, 19, 26, 28, 31, 32, 33, 34, 37, 47, 50, 53], "knowledge_expert": 14, "knowledge_gain": [14, 15], "knowledge_gain_estim": 14, "knowledge_gap": 5, "knowledge_growth_r": 14, "knowledge_level": 14, "knowledge_level_curr": 14, "knowledge_level_target": 14, "knowledge_retent": [3, 13, 14, 15], "knowledge_retention_r": 14, "knowledge_track": 53, "korea": 56, "kpi": [3, 36, 51, 57], "kubernet": [8, 27, 29, 40], "kwarg": 6, "l": 23, "lab": [5, 8, 26, 28, 32, 33, 34, 37, 47, 53, 58], "label": [21, 40, 41, 42, 48, 53, 56], "land": [22, 39, 43], "landscap": [28, 36, 44], "languag": [1, 17, 20, 21, 22, 23, 25, 28, 29, 35, 48, 50, 52, 58], "larg": [10, 13, 20, 24, 27, 29, 36, 39, 40, 42, 43, 47, 56], "larger": 44, "last": [7, 15, 25, 30, 51, 52, 53, 54, 55, 56, 57], "last_act": [7, 13], "last_assess": 10, "last_audit_d": 10, "last_authent": 12, "last_error": 12, "last_nam": [7, 12, 13], "last_password_chang": 7, "last_studi": [14, 15], "last_sync": 12, "last_upd": 9, "last_us": 8, "late": 28, "latenc": 37, "later": [28, 33, 44], "latest": [1, 5, 19, 23, 40, 46, 52, 53, 56], "latex": [0, 1, 23], "latexpdf": [1, 23], "latin": 36, "launch": [40, 44, 48, 56], "layer": [0, 38, 40, 42, 44, 45, 52, 54, 59], "layout": [0, 1, 22, 23, 42, 43, 44], "lazi": [0, 42], "lcp": 45, "ldap": [7, 11, 13, 18, 24, 29, 41, 50, 54], "ldap_bind_dn": 18, "ldap_bind_password": 18, "ldap_serv": 18, "ldap_user_search_bas": 18, "lead": [8, 9, 24, 28, 30, 32, 33, 37, 41, 48, 54], "leader": [30, 31, 50], "leaderboard": 31, "leaderboard_posit": 14, "leadership": [8, 13, 24, 30, 32, 36, 37, 41, 47], "leak": 0, "learn": [1, 6, 11, 13, 17, 20, 21, 22, 23, 24, 26, 30, 31, 32, 33, 34, 35, 36, 38, 40, 42, 43, 45, 46, 47, 48, 51, 52, 53, 56, 57, 59], "learner": [2, 14, 24, 30, 34, 37, 41, 47, 48, 56, 58], "learning insight": 15, "learning_curve_analyz": 53, "learning_effect": 24, "learning_insight": 15, "learning_metr": 3, "learning_path_suggest": 5, "learning_pattern": [3, 14], "learning_prefer": [5, 19], "learning_progress": [10, 18], "learning_r": 19, "learning_sequ": 19, "learning_styl": [4, 5, 8, 15, 19, 55], "learning_style_analys": 19, "learning_style_match": 5, "learning_style_profil": 5, "learning_veloc": [3, 14, 15, 18], "learningpath": [22, 42, 45], "learningpathssect": 42, "learningstyleanalys": 19, "leav": [2, 31, 37, 47], "led": 41, "left": [23, 44], "legaci": 41, "legitimaci": 35, "len": 58, "length": [2, 14, 15, 44], "less": [28, 32], "lesson": 26, "let": [27, 47, 58], "level": [0, 1, 2, 3, 4, 6, 10, 13, 14, 18, 19, 24, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 48, 50, 52, 53, 54, 58], "leverag": [30, 31, 34, 35, 36, 37, 41, 43, 47, 53, 58], "lg": [39, 42], "lib": [20, 22], "librari": [0, 22, 29, 41, 42, 48, 55], "licenc": [11, 48], "licence_summari": 10, "licence_typ": 10, "licens": [18, 26, 29, 35, 47, 48, 50, 52, 53, 55, 56], "license_typ": 24, "life": [32, 37, 44], "lifecycl": [13, 29], "lifelong": [37, 50], "lifetim": 47, "light": [22, 43, 44], "lighthous": 22, "like": [20, 21, 22, 26, 27, 29, 31, 32, 33, 34, 43, 49, 58], "likelihood": [19, 37, 47], "limit": [2, 3, 5, 7, 8, 9, 10, 13, 15, 18, 19, 20, 24, 31, 35, 36, 37, 45, 46, 48, 52, 54, 56, 57], "line": 3, "lineag": 41, "linear": [23, 24, 41, 48, 50, 53, 55], "linearprogram": 53, "linecolor": 23, "link": [1, 23, 25, 37, 39, 51, 57, 58], "linkcheck": [1, 23], "linkedin": [8, 28, 33], "lint": [20, 21, 22, 23], "linux": [5, 28], "list": [1, 11, 12, 19, 21, 23, 25, 27, 35, 52, 53, 54, 55, 56, 59], "listen": 44, "live": [15, 17, 22, 23, 24, 36, 38, 39, 42, 43, 46, 47, 48, 55, 57], "live_metr": 3, "livehtml": 1, "lm": [11, 13, 15, 18, 24, 26, 29, 34, 41, 47, 50], "lms_integration_789": 12, "lms_provid": 12, "load": [5, 20, 22, 24, 27, 29, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 50, 52, 57], "loader": 42, "loadeventend": 45, "loadeventstart": 45, "loadtim": 45, "local": [0, 1, 2, 5, 14, 17, 18, 19, 20, 21, 23, 24, 28, 29, 33, 34, 37, 43, 44, 50, 51, 53, 56], "locale_dir": [1, 23], "localhost": [6, 11, 18, 22, 40, 43, 47, 49, 58], "localis": [11, 21, 48], "locat": [4, 7, 8, 9, 11, 21, 24, 28, 29, 34, 35, 36, 54, 55], "location_adjust": 4, "log": [5, 6, 12, 17, 18, 19, 20, 24, 25, 26, 27, 28, 29, 35, 41, 43, 45, 47, 49, 51, 52, 54], "log_abc123": 13, "log_file_path": 18, "log_format": 18, "log_id": 13, "log_level": [18, 27], "log_retention_dai": 18, "log_rotation_s": 18, "logic": [0, 20, 21, 25, 51, 52, 53, 54], "login": [0, 1, 6, 11, 22, 23, 35, 39, 40, 41, 42, 43, 44, 45, 46, 47, 52, 54, 58], "login_fail": 10, "login_url": 12, "loginform": 45, "loginformdata": 42, "loginpag": 42, "loginschema": 42, "logo": 22, "logo_onli": 23, "logout": [0, 12, 22, 43], "logout_url": 12, "logs_url": 12, "london": [8, 9, 12], "long": [4, 9, 28, 32, 33, 34, 36, 37, 41, 47, 58], "longest_streak": 14, "longitudin": 26, "look": [9, 30, 32, 33, 44], "lookup": 55, "lost": [44, 47], "low": [3, 4, 15, 34, 46, 59], "lower": [3, 9, 47], "lowercas": 7, "loyalti": 35, "lstmnetwork": 53, "ltd": 13, "lucid": 22, "lunch": 32, "m": [1, 21, 33, 49], "machin": [1, 5, 11, 17, 33, 37, 48, 50, 53], "machine_learn": 18, "made": 0, "mai": [8, 9, 28, 31, 32, 33, 36], "mail": 12, "main": [1, 17, 21, 23, 25, 35, 36, 40, 42, 43, 46, 49, 58], "maintain": [1, 5, 8, 12, 14, 15, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 31, 32, 34, 35, 37, 39, 41, 42, 44, 45, 47, 48, 50, 51, 53, 57, 58], "maintain_current_level": 14, "mainten": [20, 24, 27, 30, 31, 32, 33, 34, 47, 48, 55, 57], "maintenance_guid": 25, "major": [0, 17, 21, 25, 32, 34, 46, 48, 56, 57], "make": [1, 6, 14, 21, 23, 24, 25, 31, 32, 34, 36, 38, 44, 46, 47, 50, 55, 58], "make_request": 6, "makefil": 23, "manag": [0, 1, 2, 3, 4, 5, 6, 8, 11, 17, 20, 22, 25, 26, 28, 30, 32, 37, 38, 42, 43, 44, 47, 48, 50, 51, 52, 53, 54, 56, 57, 58, 59], "manage_user_permiss": 54, "management_interest": 8, "management_train": 3, "manager_email": 12, "manager_id": 13, "manager_nam": 10, "manager_user_id": 10, "manchest": [8, 9], "mandatori": [24, 31, 34, 41, 48], "mandatory_train": 10, "mani": [4, 6, 7, 11, 28, 31, 33, 36], "manipul": [0, 47], "manual": [1, 14, 17, 29, 35, 48, 51, 54], "map": [0, 2, 5, 20, 26, 27, 28, 29, 31, 34, 35, 37, 41, 47, 50, 51, 54, 55, 56], "margin": 23, "markdown": [1, 23], "market": [5, 9, 11, 17, 26, 32, 33, 35, 37, 41, 43, 47, 48, 51, 57, 58, 59], "market_analysi": 9, "market_competit": 8, "market_condit": 4, "market_demand": 8, "market_intellig": [8, 55, 59], "market_requir": 8, "market_summari": 8, "market_trend": [8, 55], "market_valu": 8, "marketintellig": 55, "marketplac": [16, 46, 48, 50, 51, 52], "marketplace_profil": 59, "marketplaceprofil": 59, "markettrendreport": 54, "markovchain": 53, "markup": 0, "massiv": 48, "master": [14, 15, 23, 33, 35, 37, 44, 47, 58], "master_doc": 23, "masteri": [5, 14, 15, 18, 37, 46, 47, 48, 53], "mastery_level": [14, 15], "match": [5, 8, 26, 28, 29, 30, 31, 32, 35, 36, 37, 56], "matchlabel": 40, "materi": [2, 3, 9, 14, 21, 26, 31, 32, 33, 34, 35, 37, 41, 44, 47, 54, 55, 56, 59], "materials_cost_rang": 9, "matrix": [6, 31], "matter": [25, 31], "maven": 4, "max": [8, 9, 23, 42], "max_age_dai": 13, "max_budget": [4, 40, 59], "max_certifications_per_year": 9, "max_concurrent_sess": [13, 18], "max_depart": 10, "max_depth": [2, 19], "max_difficulti": 4, "max_difficulty_jump": 18, "max_failur": 12, "max_organ": 18, "max_overflow": 18, "max_request_s": 18, "max_session_duration_hour": 13, "max_timeline_month": [4, 40], "max_us": [10, 13], "max_year": 8, "maxim": [31, 36, 44, 47, 58], "maximis": [31, 34], "maximum": [2, 4, 7, 10, 14, 17, 27, 31, 35, 36, 37, 47, 50, 58], "maximum_allow": 10, "mb": 42, "mba": 8, "md": [23, 25, 39, 42], "mdm": 44, "me": [42, 45, 47], "mean": [2, 19, 50], "meaning": [44, 47], "measur": [2, 24, 26, 27, 29, 31, 32, 34, 35, 36, 37, 41, 47, 48, 50, 58], "mechan": [22, 59], "media": [0, 1, 23], "median_salari": 8, "medium": [3, 4, 5, 9, 10, 12, 14, 15, 27, 55, 56, 57], "meet": [0, 2, 21, 24, 26, 29, 31, 32, 33, 41, 42, 44, 45, 50], "meets_requir": 8, "meetup": [26, 28], "member": [1, 3, 21, 23, 24, 31, 32, 52, 54], "member_count": [3, 13], "member_perform": 13, "membership": [13, 33], "membership_fe": 4, "memori": [0, 35, 37, 44, 45], "memoris": 33, "memory_usag": 18, "memoryusag": 45, "mental": 37, "mentor": [3, 8, 13, 28, 32, 34, 36], "mentorship": [28, 31, 32, 33], "menu": [35, 36, 43, 44], "merchandis": 56, "merg": [1, 35, 40, 48], "mermaid": [23, 48], "messag": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 17, 22, 42, 43, 45, 47, 48, 49, 51, 53, 57, 59], "messagemargin": 23, "messer": 33, "meta": [0, 22, 23], "metacognit": 37, "metadata": [12, 18, 40], "metadata_url": [12, 18], "metasploit": 28, "method": [11, 12, 14, 19, 20, 21, 26, 34, 37, 40, 46, 48, 49], "method_improv": 14, "methodologi": [30, 48, 52], "metric": [12, 13, 14, 17, 18, 19, 22, 23, 25, 26, 27, 29, 31, 32, 34, 35, 36, 37, 39, 40, 41, 43, 44, 45, 47, 50], "metrics_port": 18, "metrics_url": 12, "mexico": 56, "mfa": [20, 24, 41], "mfa_en": 7, "mfa_requir": [13, 40], "mfa_token": 7, "mfa_verifi": 7, "micro": [32, 44], "microlearn": 41, "microservic": [41, 52], "microsoft": [27, 32, 33], "mid": [4, 8, 28, 32, 33], "mid_siz": 8, "middl": 56, "migrat": [17, 20, 21, 27, 29, 40, 41, 48, 49, 52], "mileston": [2, 8, 14, 15, 24, 28, 31, 32, 34, 37, 44, 47, 48, 50, 51, 52, 53, 55, 56, 58], "milestone_1": 14, "milestone_id": 14, "milestone_notif": 14, "milestone_upd": 14, "militari": [2, 24, 28, 37, 41, 47, 48, 50, 55], "min": [1, 8, 9, 23, 42], "min_confid": 18, "min_length": 13, "min_samples_leaf": 19, "min_samples_split": 19, "min_year": 8, "mind": [5, 26], "minif": 42, "minifi": 0, "minim": [27, 33, 35, 36, 43, 46, 53], "minimis": [19, 20], "minimum": [7, 19, 27, 37, 49], "minimum_experience_year": 8, "minimum_requir": 9, "minimum_scor": 12, "minimum_study_hour": 5, "minimum_valu": 4, "minut": [1, 2, 4, 5, 6, 12, 14, 15, 32, 33, 36, 39, 44, 48, 52, 54, 55], "misalign": 31, "miscellan": 9, "miss": [4, 6, 16, 17, 25, 35, 36, 46, 51], "missing_field": 8, "mission": [51, 52, 53, 54, 55, 56], "mitig": [17, 36, 41, 51, 55, 57], "mitigation_strategi": 9, "mix": [4, 26, 31, 37, 47], "ml": [8, 17, 23, 48, 51, 53], "mobil": [1, 3, 15, 17, 22, 32, 33, 39, 42, 48, 50, 52, 53, 55, 56, 58], "mobile_guid": 1, "mock": [21, 42, 45], "mocklogin": 42, "mockresolvedvalu": 42, "mockreturnvalu": 42, "mocksubmit": 45, "modal": [2, 17, 34, 53, 59], "mode": [0, 22, 23, 36, 42, 43, 44, 52], "model": [1, 3, 11, 14, 17, 18, 20, 23, 26, 27, 29, 36, 37, 41, 44, 47, 48, 50, 51, 57, 58], "model_accuracy_improv": 5, "model_cache_s": 18, "model_data": 19, "model_encrypt": 18, "model_nam": 19, "model_path": 18, "model_select": 19, "modelperformancemonitor": 19, "models_upd": 5, "moder": [5, 8, 14, 28, 35, 36, 46, 58, 59], "modern": [0, 1, 20, 22, 23, 36, 41, 42, 43, 45, 47, 48], "modif": [20, 52, 53], "modifi": [10, 46, 51], "modul": [3, 20, 41, 44, 48], "modular": 20, "momentum": [28, 32], "mondai": [14, 15], "monei": 47, "monet": [51, 52, 54], "monitor": [4, 5, 10, 11, 13, 14, 15, 16, 17, 22, 24, 26, 29, 30, 31, 34, 35, 36, 37, 41, 42, 43, 44, 46, 47, 48, 52, 54, 55, 57, 58], "monitor_compliance_statu": 54, "monitor_model_perform": 19, "monitor_platform_perform": 40, "monitoring_": 27, "month": [3, 4, 8, 9, 14, 28, 33, 34, 36, 43, 47, 52, 53, 54, 55, 56, 57], "monthli": [2, 3, 14, 15, 18, 27, 30, 35, 36, 47, 48, 50, 52, 56, 57, 58], "monthly_breakdown": 9, "monthly_cash_flow": 9, "monthly_growth": 10, "monthly_improv": 14, "monthly_rank": 14, "moodl": [12, 18, 41], "moodle_api_token_her": 12, "moodle_connector": 12, "moodle_token_her": 12, "moodle_vers": 12, "more": [5, 8, 14, 15, 21, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 44, 46, 47, 58], "morn": [14, 32], "most": [11, 14, 15, 24, 27, 28, 30, 31, 32, 33, 35, 36, 37, 44, 46, 47, 48], "most_cost_effect": 9, "most_effective_dai": 3, "most_effective_method": 14, "most_improv": 3, "most_productive_dai": 15, "most_productive_tim": 14, "most_studied_top": 14, "motion": [22, 43], "motiv": [2, 17, 24, 28, 31, 32, 33, 34, 47, 48, 53, 58], "mous": 0, "move": [5, 17, 31, 44, 46, 47, 58], "movement": [31, 36], "mozilla": [10, 13], "much": [31, 33], "multi": [0, 2, 4, 8, 9, 10, 11, 12, 13, 17, 18, 20, 22, 23, 24, 25, 26, 29, 30, 31, 34, 35, 36, 38, 40, 41, 42, 44, 45, 47, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], "multi_ten": [10, 18, 58], "multimedia": 25, "multipl": [3, 6, 9, 10, 20, 24, 29, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43, 46, 47, 49, 52, 56, 58], "multitenantservic": 54, "must": [4, 7, 21], "mvp": [22, 53], "mx": 42, "my": [26, 28, 31, 32, 33, 34, 42, 45], "myst": [0, 1, 51], "myst_pars": [1, 23], "n_cluster": [2, 19], "n_compon": 19, "n_estim": [2, 19], "name": [1, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 17, 18, 21, 23, 24, 25, 35, 40, 42, 43, 44, 45, 49, 58, 59], "napoleon": [1, 23], "nativ": [17, 22, 24, 41, 43, 44, 50], "natur": [31, 58], "natural_language_process": 18, "nav": 23, "navig": [1, 22, 23, 25, 30, 35, 36, 39, 40, 42, 43, 44, 45, 46, 47, 48, 51, 55, 58], "navigation_depth": [1, 23], "nearestneighbor": 53, "necessari": [20, 33, 43], "need": [8, 9, 14, 15, 16, 17, 18, 19, 21, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 40, 41, 43, 46, 47, 48, 49, 58, 59], "needs_attent": 59, "negoti": [3, 32, 36, 55], "neighbor": 55, "net": [1, 13, 23, 35, 52], "net_benefit_18_month": 9, "net_benefit_5_year": 8, "net_career_valu": 8, "net_flow": 9, "net_personal_cost": 9, "net_personal_invest": 9, "net_present_valu": 9, "net_roi": 3, "netherland": 56, "network": [2, 5, 8, 12, 14, 15, 26, 28, 32, 33, 34, 37, 41, 43, 46, 50, 56, 58], "network_secur": 5, "networkidl": 45, "neutral": [31, 32, 33], "never": [2, 6, 28, 37], "new": [0, 1, 2, 7, 10, 13, 14, 15, 17, 21, 23, 27, 28, 30, 31, 32, 33, 35, 36, 37, 40, 41, 42, 43, 45, 46, 47, 48, 56, 57], "new_enrol": 13, "new_jwt_token_her": 6, "new_password": 7, "new_rol": 13, "newcom": 21, "newli": 31, "newsecurepassword123": 7, "newtier": 52, "next": [3, 9, 15, 32, 47, 48, 50, 51, 52, 53, 55, 56], "next_12_month": [3, 9], "next_audit_du": 10, "next_deliveri": 3, "next_level": 14, "next_level_rol": 8, "next_level_salari": 8, "next_public_api_url": 22, "next_public_app_url": 22, "next_retry_backoff": 12, "next_session_plan": 15, "next_session_recommend": 14, "next_study_suggest": 5, "next_sync": 12, "next_sync_schedul": 12, "next_upd": 9, "nginx": 1, "nice": 26, "nmap": 28, "nmf": 19, "nmf_model": 19, "node": [21, 27], "node_env": 22, "non": [28, 31], "none": [6, 8, 19, 23, 40, 54, 55, 59], "nonprofit": 28, "nordic": 56, "norepli": 18, "north": 36, "north_america": 4, "notabl": 17, "note": [1, 14, 15, 17, 23, 25, 36, 44], "notemargin": 23, "notic": 47, "notif": [0, 3, 11, 12, 13, 15, 17, 24, 27, 29, 35, 41, 42, 46, 47, 48, 50, 53, 54, 55, 56, 58], "notification_email": 12, "notification_prefer": 13, "now": [0, 6, 14, 17, 22, 36, 40, 45, 47, 48, 57, 58], "np": [19, 52], "npm": [1, 4, 11, 22, 23, 40], "nugget": 33, "null": [6, 9, 10, 12, 13, 39, 40], "num_recommend": 19, "number": [1, 7, 10, 13, 15, 17, 19, 23, 25, 36, 39, 52, 53], "numer": 19, "numpi": [1, 19, 23], "o": 21, "oauth": [13, 18, 24, 27, 29, 41, 47, 50], "oauth2": 54, "oauth_": 27, "oauth_client_id": 18, "oauth_client_secret": 18, "object": [14, 18, 20, 26, 31, 32, 34, 36, 37, 46, 47, 53, 58], "observ": [17, 27], "obsolet": 35, "obtain": [6, 52], "occasion": 43, "occur": [2, 5, 14, 36, 37, 43, 50], "off": [1, 25, 44], "offboard": 29, "offer": [5, 8, 9, 26, 31, 32, 33, 34, 36, 40], "offic": 26, "offici": [4, 11, 14, 15, 33, 35, 47, 56], "official_study_guid": 9, "offlin": [0, 2, 11, 15, 17, 22, 24, 33, 37, 42, 43, 46, 47, 48, 50, 53], "often": [28, 31, 32, 33, 37, 44, 46, 51], "og": 1, "ogp_descript": 1, "ogp_imag": 1, "ogp_site_nam": 1, "ogp_site_url": 1, "oidc": [17, 54], "ok": [1, 4, 6, 7, 11, 23], "okta": [7, 29, 54], "oldtier": 52, "on_device_process": 18, "on_error": 12, "on_track": 59, "onboard": [29, 31, 35, 40, 41, 52, 55, 56], "onboard_us": 40, "onc": [44, 51], "onchang": 42, "onclick": 45, "one": [28, 31, 33, 37, 52, 53], "onelin": 51, "ones": 31, "ongo": [2, 31, 33, 34, 41, 50, 58], "onli": [6, 7, 20, 36, 42, 43, 53], "onlin": [15, 28, 32, 33, 44, 52, 56], "online_train": 9, "onmessag": 40, "onsubmit": [42, 45], "open": [0, 19, 21, 23, 28, 36, 44], "open_set": 55, "openai": [18, 49], "openai_api_kei": [18, 49], "openapi": [11, 20, 52, 54], "opengraph": [0, 1, 23], "openid": [12, 24, 29, 41, 47, 50], "oper": [3, 10, 11, 12, 13, 17, 20, 24, 27, 28, 29, 30, 33, 35, 38, 41, 44, 48, 54, 57, 58], "opportun": [3, 9, 24, 26, 28, 31, 32, 33, 34, 35, 36, 37, 44, 46, 47, 48, 51, 57, 58], "opportunity_cost": 9, "opt": [46, 53], "optim": [1, 2, 5, 9, 11, 14, 15, 16, 17, 19, 22, 23, 24, 25, 29, 33, 35, 37, 40, 41, 43, 46, 47, 48, 50, 52, 53, 55, 56, 57, 58, 59], "optimal_break_schedul": 14, "optimal_minut": 14, "optimal_session_dur": 15, "optimal_session_length": [3, 14], "optimal_study_hour": 5, "optimal_study_pattern": 5, "optimal_study_tim": 14, "optimis": [5, 10, 30, 31, 32, 34, 48, 58], "optimist": 36, "optimization_opportun": 3, "optimization_result": 40, "optimize_budget": 40, "optimize_organization_budget": 40, "optimize_study_schedul": 53, "optimized_alloc": 4, "optimized_cost": 3, "option": [0, 1, 3, 6, 10, 12, 13, 15, 18, 20, 21, 24, 26, 27, 28, 29, 33, 36, 47, 48, 49, 52, 58, 59], "optional_certif": 8, "order": 37, "org": [1, 13, 23, 24, 54], "org_12345": [6, 10, 12], "org_data": 40, "org_id": [10, 40, 54], "org_key_abcd1234": 10, "org_structur": 54, "organ": [1, 2, 7, 11, 17, 18, 22, 23, 24, 29, 30, 33, 34, 39, 41, 43, 47, 48, 50, 51, 52, 54, 58], "organis": [6, 11, 12, 24, 27, 29, 30, 32, 34, 48, 58], "organisation_id": [6, 10, 12], "organisation_kei": 10, "organisation_limit_exceed": 10, "organiz": [2, 3, 13, 22, 24, 41, 44, 47, 48, 50, 54, 55, 58], "organization manag": 13, "organization_data": 40, "organization_id": [3, 7, 13, 24, 40], "organization_mgmt": 40, "organization_permiss": 7, "organizationcontext": 54, "organizationmanag": 40, "orient": [31, 44], "origin": [20, 21], "orm": 20, "orphan": 23, "oscp": 32, "other": [1, 21, 23, 26, 28, 29, 33, 36, 37, 46, 47, 52, 53, 58], "ou": 18, "our": [13, 26, 27, 28, 29, 30, 31, 32, 33, 34, 41, 46, 47, 48, 58], "out": [21, 26, 27, 28, 29, 30, 31, 32, 33, 34, 43, 46, 49, 53, 57], "outag": 37, "outcom": [2, 3, 17, 24, 26, 34, 35, 36, 37, 41, 47, 48, 50, 55, 59], "outdat": 25, "outlook": 15, "output": [0, 1, 23, 36], "outsid": 32, "outstand": 36, "over": [3, 9, 15, 24, 26, 32, 33, 34, 35, 36, 37, 41, 43, 44, 46, 48, 50, 53], "overal": [2, 19, 36, 44, 46, 57, 58, 59], "overall_assess": 8, "overall_difficulti": 5, "overall_effect": 14, "overall_effectiveness_scor": 14, "overall_knowledge_level": 14, "overall_progress": [15, 58], "overall_progress_percentag": 14, "overall_rank": 14, "overall_scor": 8, "overall_session_r": 14, "overall_skill_level": [5, 58], "overall_statu": 12, "overhead": [2, 24, 29], "overlap": 32, "overrid": 44, "oversight": [24, 35, 41], "overview": [0, 11, 16, 21, 30, 36, 41, 48, 58], "overwhelm": 37, "own": [5, 19, 20, 31], "owner": [52, 53, 54, 55, 56], "ownership": [2, 4, 5, 37, 48, 50, 55, 57], "p": [23, 45, 49], "p0": [52, 57], "p1": [53, 54, 57], "p10": 4, "p2": [55, 56, 57], "p50": 4, "p90": 4, "pace": [2, 37, 41, 47, 50], "pace_compared_to_plan": 14, "pacif": 36, "packag": [4, 22], "page": [4, 11, 13, 22, 23, 25, 36, 39, 42, 43, 44, 45, 47, 48, 52, 55], "pagerduti": 27, "pagin": [13, 20], "pai": [28, 31, 32, 33], "paid": [33, 52], "paint": 45, "pairwis": 19, "palac": 37, "panel": 27, "paradigm": 50, "paramet": [3, 4, 8, 9, 13, 14, 15, 16, 27, 35, 36], "parent_department_id": 10, "parent_team_id": 13, "pareto": 37, "pars": 40, "parser": [0, 1, 51], "part": 28, "parti": [1, 11, 32, 40, 43, 48, 50, 52, 53, 56], "particip": [2, 26, 28, 31, 32, 34, 46], "partit": 27, "partner": [17, 52, 56, 59], "partner_config": 56, "partner_id": 56, "partnerconfig": 56, "partnership": [3, 26, 33, 35, 51, 56, 57], "partnershipintegr": 56, "pass": [2, 12, 24, 25, 26, 34, 37, 40, 41, 47, 53, 54], "pass_threshold": 12, "passback": [24, 41], "passion": 28, "passiv": 37, "password": [6, 11, 18, 27, 35, 40, 42, 43, 45, 47, 49, 52, 58], "password123": [22, 42, 43, 45], "password_histori": 13, "password_min_length": 18, "password_polici": [10, 13], "password_require_lowercas": 18, "password_require_numb": 18, "password_require_speci": 18, "password_require_uppercas": 18, "password_strength": 7, "passwordinput": 45, "patch": [10, 14, 21, 35, 41], "path": [1, 2, 4, 5, 9, 11, 17, 22, 23, 26, 28, 30, 31, 32, 33, 34, 35, 36, 39, 40, 41, 42, 43, 46, 47, 48, 50, 51, 52, 53, 55, 59], "path_001": 4, "path_id": 4, "path_nam": 5, "path_opt": 4, "pathdata": 40, "pathfind": [17, 36, 40, 43, 48, 51, 55, 57, 59], "pathwai": [0, 26, 28, 33, 35, 53, 55, 58], "patient": 21, "pattern": [0, 1, 2, 3, 5, 6, 9, 14, 15, 16, 17, 19, 20, 22, 23, 24, 35, 37, 43, 44, 46, 47, 48, 50, 51, 53, 57, 59], "paul": 11, "payback": [9, 36, 46, 47, 58], "payback_month": 9, "payback_period": 9, "payback_period_month": [3, 4, 8], "payload": [6, 12], "payment": [47, 56], "payment_schedul": 56, "pdf": [3, 13, 23, 24, 29, 36, 41, 50], "peaceiri": [1, 23], "peak": [37, 41, 44, 47], "peak_learning_hour": 3, "peak_negative_cash_flow": 9, "peer": [3, 13, 17, 26, 28, 29, 30, 32, 34], "pend": [13, 14, 15], "penetr": [28, 32, 33, 52], "pep": 21, "per": [4, 7, 11, 13, 15, 20, 24, 25, 27, 29, 31, 33, 34, 35, 37, 48, 50, 52, 53, 54, 56, 57], "per_organ": 18, "per_us": 18, "percentag": [15, 36, 37, 46], "percentil": 52, "percentile_25": 8, "percentile_50": 8, "percentile_75": 8, "percentile_90": 8, "perfect": [2, 24, 44, 47, 58], "perform": [2, 4, 9, 11, 17, 21, 24, 25, 29, 30, 32, 35, 37, 40, 41, 44, 46, 47, 50, 51, 56, 58], "performance_analysi": 14, "performance_heatmap": 3, "performance_metr": [14, 18], "performance_model": 2, "performance_optim": 18, "performance_predictor": [5, 18, 19, 53], "performance_scor": [3, 13, 19], "performance_threshold": 19, "performance_trend": [3, 14], "performance_vs_industri": 3, "performancepredict": 53, "performancepredictormodel": 19, "performancescor": 53, "period": [2, 3, 4, 6, 9, 10, 13, 14, 15, 19, 20, 28, 32, 36, 37, 40, 44, 46, 47, 48, 50, 54, 58], "period_end": 13, "period_start": 13, "perman": 28, "permiss": [10, 11, 12, 13, 24, 26, 27, 29, 35, 40, 41, 44, 47, 48, 50, 51, 54, 57], "persist": [36, 46, 47, 52], "person": [2, 3, 6, 14, 17, 19, 20, 24, 28, 30, 33, 35, 41, 43, 44, 45, 47, 48, 50, 51, 53, 54, 55, 57, 58, 59], "personal_cost": 9, "personalis": [5, 8, 9, 19, 20, 32, 34, 48, 58], "personalised_factor": [5, 19], "personalised_recommend": 5, "personalization_weight": 18, "personnel": 36, "personnel_cost": 3, "perspect": [21, 28, 32, 33], "pg_dump": 27, "pgadmin": 27, "phase": [22, 36, 41, 51, 52, 53, 54, 55, 56], "phase_1": 4, "phase_2": 4, "phase_3": 4, "phi": 54, "phone": [36, 41, 44, 47], "php": [4, 12, 18], "pickl": 19, "pictur": [46, 47], "pie": 3, "pinch": 44, "pinpoint": 37, "pioneer": [2, 37, 50], "pip": [1, 4, 11, 21, 23, 40, 49, 51], "pipelin": [0, 45, 48, 50, 53, 55, 57], "pkl": 18, "placehold": [17, 42], "placement": [28, 44], "plain": 6, "plan": [1, 2, 5, 10, 11, 13, 17, 19, 20, 22, 24, 26, 27, 28, 29, 30, 32, 34, 35, 37, 40, 41, 46, 47, 48, 50, 51, 52, 53, 55, 56, 57, 59], "plan_abc123": 15, "planned_certif": 9, "planned_duration_minut": [14, 15, 40], "planned_spend": 9, "planner": [28, 33, 58], "planning_period": 9, "platform": [0, 1, 2, 7, 11, 15, 16, 18, 20, 21, 23, 25, 26, 27, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 41, 43, 44, 45, 46, 47, 51, 53, 54, 55, 56, 58], "platform guid": 40, "platform_cost": 3, "platform_metr": 59, "platform_overview": 1, "platformmetr": 59, "platinum": 14, "playwright": [0, 42, 48, 52, 53, 55, 57], "pleas": [6, 21, 42], "plot": 3, "plu": 33, "plugin": 41, "png": [1, 7, 23], "pocket": 44, "podcast": 28, "point": [1, 9, 14, 18, 21, 23, 27, 28, 29, 33, 41, 49, 59], "points_award": 14, "points_to_next_level": 14, "polici": [5, 20, 24, 27, 28, 29, 34, 35, 41, 43, 44, 47, 54], "polish": 52, "pool": 18, "pool_recycl": 18, "pool_timeout": 18, "poor": [36, 44], "popular": [1, 12, 15, 23, 27, 29, 33, 34, 35, 46, 54], "port": [40, 49], "portal": [17, 26, 54, 56], "portfolio": [8, 28, 32, 55], "portrait": 44, "posit": [13, 21, 28, 31, 32, 33, 36, 41, 46, 47, 48, 54, 55], "position_titl": 12, "positive_factor": 3, "possibl": [20, 31, 43], "post": [3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 28, 32, 33, 40, 52, 53, 54, 55, 56, 58, 59], "postgr": 20, "postgres_data": 20, "postgresql": [18, 20, 21, 27, 40, 49, 52], "postur": [32, 44], "potenti": [24, 28, 31, 33, 36, 37, 46], "potential_challeng": 5, "potential_sav": 3, "power": [1, 3, 8, 11, 14, 15, 17, 20, 22, 23, 24, 29, 33, 36, 40, 43, 44, 46, 47, 48, 53, 55, 56, 59], "powerhous": 44, "powershel": [28, 33], "practic": [1, 3, 5, 8, 11, 14, 15, 19, 20, 25, 26, 30, 31, 32, 33, 34, 35, 36, 37, 41, 46, 48, 53, 56, 58, 59], "practice_exam": 4, "practice_hour": 5, "practice_quest": [14, 15], "practice_result": 14, "practice_scor": 18, "practice_test": [5, 9], "practice_test_averag": 14, "practice_test_scor": 19, "practis": [27, 28, 29, 30, 31, 32, 33, 34], "practition": 33, "prd": [1, 23, 48, 55], "prd_docs_monitor": 51, "pre": [12, 27, 29, 44], "precis": [19, 31, 33, 47, 55, 58], "pred_abc123": 3, "predefin": [35, 48], "predict": [2, 10, 14, 17, 19, 20, 24, 26, 29, 34, 41, 47, 48, 50, 51, 53, 54, 55, 58, 59], "predict_certification_success": 2, "predict_proba": [19, 53], "predict_success_prob": [19, 53], "predicted_effect": 14, "predicted_exam_readi": 14, "predicted_exam_scor": 14, "predicted_success_r": 58, "predicted_valu": 3, "prediction_batch_s": 18, "prediction_factor": 5, "prediction_id": 3, "prediction_typ": 3, "predictionev": 53, "predictionfactor": 53, "predictionresult": 53, "predictive_analyt": 18, "predictive_model": 18, "predictor": [2, 37, 48, 50, 58], "prefer": [2, 5, 8, 17, 19, 20, 31, 32, 33, 34, 35, 36, 37, 43, 44, 46, 47, 50, 52, 53, 58], "preferred_content_typ": 3, "preferred_dai": 14, "preferred_industri": 8, "preferred_learning_styl": [5, 58], "preferred_study_dai": 15, "preferred_study_intens": 9, "preferred_study_method": [9, 14], "preferred_study_tim": 15, "preferred_study_typ": 15, "preferred_tim": 14, "preferred_year": 8, "prefix": 40, "premis": 41, "premium": [6, 10, 20, 41, 52, 53, 54, 55], "prep": [26, 32], "prepar": [3, 4, 8, 14, 15, 19, 26, 28, 32, 33, 47], "preparation_recommend": 5, "prepare_training_data": 19, "preprocess": 19, "prerend": 22, "prerequisit": [2, 23, 30, 33, 35, 47, 48, 50, 52, 53, 58], "prerequisites_met": 5, "presenc": [8, 56], "present": [8, 26, 31, 32, 34, 36, 55], "preserv": [25, 37, 44, 48, 50], "press": [42, 45], "prettier": [22, 43], "prev_next_buttons_loc": 23, "prevent": [0, 31, 37, 43, 54], "preview": 1, "previou": [14, 28, 35], "previous_certif": 19, "previous_rol": 13, "previous_session_perform": 14, "price": [21, 26, 29, 33, 52, 53, 55, 56], "primari": [6, 18, 20, 23, 25, 49, 57], "primary_learning_styl": 19, "primary_styl": 5, "primarybordercolor": 23, "primarycolor": 23, "primarytextcolor": 23, "primit": 22, "principl": [19, 33, 37], "print": [0, 6, 58], "priorit": [4, 28, 33, 35, 37, 39, 46, 47, 58], "prioriti": [5, 8, 9, 15, 35, 36, 37, 41, 44, 47, 52, 53, 54, 55, 56, 57, 58], "prioritis": [8, 19, 31, 33, 34], "priority_recommend": 59, "priority_top": 5, "priorityqueu": 55, "privaci": [11, 14, 17, 18, 20, 26, 27, 32, 35, 37, 41, 44, 47, 50, 53, 54, 57, 58, 59], "privacy_policy_vers": 10, "privacy_set": 18, "privacypreservingai": 19, "privat": [41, 43, 53], "privileg": 13, "proactiv": [52, 53], "probability_distribut": 4, "probabl": [2, 5, 17, 19, 20, 24, 36, 37, 47, 48, 50, 53, 55, 58, 59], "problem": [21, 34, 40, 43, 44, 47], "procedur": [1, 18, 27, 29, 35, 54, 55], "process": [0, 2, 5, 7, 10, 17, 24, 26, 27, 30, 32, 34, 35, 36, 37, 38, 39, 41, 43, 44, 47, 48, 50, 53, 54, 55, 56, 57, 58, 59], "processed_record": 12, "processed_us": 10, "procur": 29, "product": [1, 4, 7, 11, 15, 18, 20, 21, 23, 25, 27, 33, 35, 40, 41, 44, 47, 49, 56], "productivity_gain": 3, "profession": [0, 1, 8, 21, 22, 25, 26, 28, 29, 30, 31, 33, 35, 40, 41, 43, 44, 46, 47, 48, 50, 52, 53, 54, 55, 58], "professor": 33, "profici": [8, 14], "proficiency_level": 8, "profil": [6, 7, 8, 12, 17, 19, 20, 22, 30, 33, 35, 40, 41, 43, 46, 47, 48, 52, 56, 58], "program": [3, 13, 20, 24, 26, 28, 29, 30, 31, 32, 33, 41, 50, 53, 56], "programm": [8, 24, 26, 28, 30, 31, 34], "programmat": [24, 36, 41], "progress": [1, 2, 7, 8, 12, 13, 17, 20, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 43, 46, 48, 50, 51, 52, 53, 54, 55, 56, 57, 59], "progress track": 15, "progress_data": [2, 40], "progress_percentag": [14, 15], "progress_r": 13, "progress_rate_per_hour": 14, "progress_since_last_sess": 14, "progress_summari": 14, "progress_sync": 12, "progress_track": [1, 20], "progress_upd": [5, 14], "progression_requir": 8, "progression_timeline_year": 8, "progresspercentag": 53, "progresstrack": 56, "progresstrackingapi": 56, "project": [1, 3, 8, 9, 17, 20, 21, 22, 23, 24, 26, 28, 32, 34, 36, 37, 41, 46, 48, 51, 59], "project_experi": 8, "project_nam": [18, 49], "projected_completions_next_30d": 10, "projected_invest": 3, "projected_return": 3, "projected_roi": [3, 4, 9], "projected_salary_impact": 9, "projected_spend": 10, "projected_usage_6m": 10, "projected_vs_actu": 9, "prometheu": 27, "promot": [9, 26, 31, 35, 41, 47, 52], "prompt": 35, "promptli": 21, "proper": [21, 26, 36, 52], "properli": [13, 17, 25, 49], "propos": [21, 26, 32], "proposit": [39, 51, 52, 53, 54, 55, 56], "proprietari": 41, "prospect": [32, 33], "protect": [2, 6, 14, 17, 18, 20, 26, 31, 40, 41, 43, 44, 47, 54, 56], "protected_rout": 40, "protocol": [14, 15, 20, 24, 41, 50, 54], "prove": 32, "proven": [31, 34, 58], "provid": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 46, 47, 48, 49, 52, 53, 54, 55, 56, 58, 59], "provided_valu": 4, "provider_config": [54, 56], "providerconfig": 56, "provis": [12, 13, 24, 29, 35, 41, 47, 50, 54], "provisioning_set": 12, "public": [6, 20, 21, 26, 32, 35], "publicli": 31, "publish": [52, 53], "publish_dir": [1, 23], "pull": [1, 43], "purchas": [29, 31, 56, 59], "purchasing_power_adjust": 9, "purpos": [26, 35, 36, 43], "pursu": [9, 31, 32, 34], "push": [0, 1, 15, 17, 21, 23, 24, 42, 46, 47, 48, 50], "put": [7, 13, 15, 18, 52, 54, 55], "pwa": [0, 42, 48], "px": 42, "py": [1, 18, 20, 21, 23, 25, 40, 42, 49, 51, 55], "pydant": [20, 52], "pyramid": [0, 45], "pytest": [6, 20, 21, 49, 52, 53], "python": [1, 4, 11, 20, 21, 23, 25, 26, 27, 28, 33, 40, 49], "python3": 51, "q1": [9, 10], "q2": [3, 9, 17], "q3": [9, 17], "q4": [9, 17], "qr_code": 7, "qualifi": 26, "qualit": [26, 31, 34], "qualiti": [0, 1, 19, 20, 21, 26, 30, 33, 34, 35, 37, 41, 43, 45, 48, 51, 52, 55, 56, 57], "quantif": [2, 50], "quantifi": [34, 41], "quantit": [26, 31, 34], "quantiti": 33, "quarterli": [1, 13, 15, 23, 27, 31, 36, 41, 48, 50], "quarterly_breakdown": 9, "queri": [1, 3, 6, 13, 15, 20, 22, 23, 27, 28, 29, 35, 39, 41, 43, 47, 48, 52, 54, 55, 58], "query_cache_s": 18, "query_timeout": 18, "queryfn": 39, "querykei": 39, "question": [1, 14, 15, 17, 21, 23, 26, 27, 28, 29, 31, 32, 34, 37, 44, 47, 53, 58], "questions_attempt": 14, "questions_correct": 14, "quick": [0, 2, 22, 24, 30, 35, 44, 47, 51, 53, 55], "quick_act": 59, "quicker": 2, "quickli": 33, "quickstart": 1, "quickstat": [42, 45], "quickstatscard": 42, "quiet": 44, "quit": 28, "quizz": 44, "r": [1, 21, 23, 26, 33, 40, 43, 49], "radiu": 23, "radix": [22, 48], "rais": [6, 21], "raise_for_statu": 6, "ram": [27, 49], "ramp": 37, "random": [2, 19, 50, 53], "random_st": [2, 19], "randomforestclassifi": 2, "randomforestregressor": [2, 19, 53], "randomis": 26, "rang": [36, 37, 43, 54, 58], "rank": 46, "rapid": [44, 52], "rapidli": [28, 32], "rare": 14, "rariti": 14, "rate": [2, 3, 5, 7, 8, 10, 13, 15, 18, 20, 24, 26, 27, 29, 31, 34, 35, 36, 37, 41, 46, 47, 48, 50, 52, 53, 54, 55, 56, 57, 58, 59], "rate_limit_burst": 18, "rate_limit_requests_per_minut": 18, "ratelimit": [4, 6, 11], "rather": [21, 31, 32, 33], "ratio": 55, "rational": 3, "raw": 36, "rbac": [27, 52, 54], "re": [30, 32, 33, 36, 44, 45, 46, 58], "reach": 10, "react": [0, 20, 22, 39, 40, 42, 43, 48, 55, 57], "read": [6, 7, 12, 13, 15, 20, 27, 33, 34, 37, 40, 44, 49, 53], "readabl": [1, 11, 21], "reader": [0, 42, 45, 47], "readi": [2, 3, 4, 5, 7, 14, 18, 21, 22, 23, 24, 26, 32, 33, 34, 35, 36, 37, 40, 41, 43, 44, 47, 48, 50, 52, 56, 57, 58], "readiness_for_target_rol": 8, "reading_text": 5, "readm": 21, "real": [0, 2, 4, 8, 9, 10, 11, 12, 13, 15, 17, 22, 25, 29, 33, 35, 36, 37, 39, 40, 41, 42, 43, 44, 46, 47, 48, 50, 51, 53, 54, 55, 56, 57, 58, 59], "real_tim": 24, "real_time_dashboard": 18, "real_time_recommend": 18, "realist": [14, 32, 33, 34, 36, 47, 58], "realiti": 3, "realiz": 53, "realtim": [3, 40], "reason": [5, 9, 10, 36, 37, 53], "rebuild": [23, 51], "rec": 53, "recal": 37, "receiv": [4, 7, 37, 44], "recent": [15, 22, 33, 40, 43, 47, 58], "recent_achiev": 59, "recent_act": 7, "recent_data": 19, "recentact": [22, 42, 45], "recentactivitysect": 42, "recipi": 3, "recogn": 33, "recognis": 31, "recognit": [2, 8, 31, 32, 34, 37, 44, 46, 47, 48, 50], "recommend": [0, 1, 2, 3, 4, 6, 7, 8, 9, 11, 12, 13, 14, 17, 18, 20, 21, 22, 24, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 47, 48, 50, 51, 52, 53, 54, 55, 56, 58], "recommend_top": 19, "recommendation_count": 18, "recommendation_engin": 18, "recommendation_request": 58, "recommendationssect": 42, "recommendationtyp": 53, "recommended_adjust": 14, "recommended_alloc": 3, "recommended_certif": 9, "recommended_exam_d": 14, "recommended_focus_tim": 14, "recommended_licence_adjust": 10, "recommended_resourc": 8, "recommended_study_hour": 5, "recommended_study_tim": 15, "recommended_techniqu": [5, 19], "recommended_timeline_week": 5, "recommended_top": 19, "recommended_train": 10, "recommendedcertif": 22, "reconstruct_path": 55, "record": [14, 24, 35, 41], "recov": [9, 36, 46, 47], "recoveri": [0, 27, 29, 35, 39, 41, 42, 43, 47, 48, 59], "recovery_test_frequ": 18, "recur": [35, 52, 56, 57], "reddit": [28, 33], "redi": [18, 20, 21, 27, 29, 40, 49], "redirect": [43, 45], "redirect_uri": [7, 12], "redis_connect": 18, "redis_db": 18, "redis_max_connect": 18, "redis_password": 18, "redis_pool_s": 18, "redis_socket_connect_timeout": 18, "redis_socket_timeout": 18, "redis_url": [18, 20, 27, 40, 49], "redoc": 11, "reduc": [2, 9, 15, 22, 24, 26, 28, 29, 31, 32, 34, 36, 44, 46], "reduced_hiring_cost": 3, "reduced_security_incid": 3, "reduct": [2, 24, 31, 34, 41, 48, 54, 55], "ref": [1, 23], "refer": [16, 17, 20, 21, 23, 25, 27, 29, 30, 36, 40, 43, 44, 46, 49, 51, 57, 58, 59], "referenc": [0, 1, 23], "referr": 56, "refetchinterv": 39, "refin": [2, 51], "reflect": 30, "refresh": [4, 22, 24, 35, 36, 42, 43, 44, 46, 47, 48, 50, 52, 55], "refresh_interv": 24, "refresh_token": 7, "refresh_token_expire_dai": [18, 40], "refreshdashboard": 22, "region": [4, 17, 28, 29, 32, 35, 36, 41, 55, 56], "regional_breakdown": 8, "regional_demand": 8, "regional_pr": [4, 9], "regional_salary_data": 9, "regional_salary_multipli": 9, "regist": [7, 11, 22, 27, 42, 43, 52, 56, 58], "register_certification_bodi": 56, "registr": [17, 22, 35, 39, 40, 43, 47, 52, 56], "regress": 26, "regressor": [2, 19, 50], "regul": [5, 29, 41, 53], "regular": [5, 14, 19, 20, 23, 30, 34, 35, 41, 43, 44, 46, 47, 52, 53], "regularli": [7, 27, 32, 33, 37, 43, 44, 46, 58], "regulatori": [10, 13, 24, 26, 29, 41, 48, 50, 53, 54, 56], "reimburs": [9, 58], "reinforc": [37, 44], "reject": 54, "relat": [0, 1, 8, 20, 25, 26, 42, 51, 57], "relationship": [0, 4, 17, 20, 31, 34, 37, 47, 55], "releas": [1, 17, 23, 25, 52], "relev": [19, 21, 25, 28, 31, 32, 33, 35, 36, 42, 46, 47, 51, 57], "relevance_scor": 8, "relevant_skil": 5, "reliabl": [2, 24, 35, 43, 45, 52, 53, 59], "relic": 27, "reload": [20, 22, 23, 39, 40, 43], "remain": [4, 6, 11, 25, 33, 37, 51], "remaining_budget": 9, "remedi": [26, 34, 37, 47, 54], "rememb": [42, 47], "rememberm": 42, "remind": [14, 15, 17, 24, 34, 42, 44, 47, 50, 53, 58], "remot": [4, 9, 16, 17, 21, 44, 48], "remote_work_avail": 8, "remote_work_impact": 8, "remov": [17, 19, 35, 44, 51, 54], "render": [22, 25, 42, 43, 45, 48], "renew": [24, 31, 33, 34, 41, 55], "renewal_cost": 55, "renewal_fe": 4, "renumb": 25, "repetit": [14, 17, 37, 44], "replac": [6, 9, 17], "replic": 29, "replica": [27, 40], "replit": [21, 40, 49], "report": [0, 2, 11, 17, 18, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 47, 48, 50, 52, 53, 54, 56, 59], "report_compliance_123": 13, "report_custom_123": 3, "report_d": 10, "report_id": [3, 13], "report_nam": 3, "report_typ": [3, 13], "repositori": [21, 27, 40, 49, 51], "repres": [2, 22, 37, 48, 50, 57], "represent": 47, "reproduc": [21, 26], "reput": 35, "req": 45, "req_123456789": 4, "request": [1, 4, 6, 7, 11, 16, 17, 20, 23, 24, 33, 35, 43, 52, 54, 55, 57, 58], "request_id": 4, "request_timeout": 18, "requir": [1, 2, 3, 4, 7, 8, 9, 10, 11, 12, 13, 14, 18, 20, 23, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 45, 46, 47, 49, 51, 54, 58], "require_2fa": 10, "require_api_kei": 18, "require_device_registr": 13, "require_group_membership": 12, "require_lowercas": 13, "require_mfa": 13, "require_numb": 13, "require_special_char": 13, "require_uppercas": 13, "required_certif": 8, "required_level": 8, "required_permiss": 12, "requirements_met": 10, "requisit": 29, "research": [1, 30, 32, 35, 48, 54, 55, 58], "resent": 31, "reset": [4, 6, 11, 47], "reset_abc123xyz": 7, "reset_at": 7, "reset_token": 7, "reset_token_expir": 7, "resid": [24, 29], "resili": 39, "resist": 31, "resolut": [35, 41, 44, 57], "resolv": [17, 21, 42], "resourc": [0, 4, 5, 10, 11, 13, 15, 20, 24, 27, 30, 31, 35, 37, 41, 44, 46, 47, 48, 54, 56, 58], "resource_id": 54, "respect": [2, 21, 31, 32, 36], "respond": [21, 35], "respons": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 20, 22, 23, 24, 25, 27, 28, 30, 31, 32, 34, 35, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 52, 54, 57, 58, 59], "response_time_m": [3, 18], "rest": [2, 4, 5, 11, 12, 13, 17, 18, 19, 20, 29, 37, 40, 41, 44, 45, 47, 52, 54], "rest_api": 12, "restart": [44, 47], "restor": [27, 43], "restrict": [2, 35, 37], "restructuredtext": [21, 23], "result": [1, 13, 19, 20, 21, 22, 23, 36, 37, 41, 42, 43, 44, 45, 47, 52, 58], "resum": [28, 33, 46], "retain": [15, 32], "retak": [33, 34, 47, 55], "retake_insur": 9, "retake_prob": 55, "retent": [2, 5, 13, 14, 15, 17, 19, 20, 26, 27, 29, 31, 34, 35, 37, 41, 44, 52, 53, 54], "retention_dai": 18, "retention_period_dai": 18, "retention_predictor": 53, "retention_r": 15, "retrain": [19, 53], "retrain_interval_dai": 18, "retri": [6, 43], "retriev": [4, 5, 8, 10, 13, 14, 41], "retry_attempt": 12, "retry_delay_second": 12, "return": [3, 4, 6, 9, 13, 15, 19, 21, 24, 34, 35, 36, 37, 39, 40, 41, 42, 45, 46, 47, 48, 50, 53, 55, 56], "return_breakdown": 3, "reus": 7, "reusabl": [22, 55], "reveal": 44, "revenu": [48, 51, 57], "review": [3, 7, 13, 14, 15, 17, 23, 24, 26, 27, 31, 32, 33, 34, 35, 36, 37, 41, 43, 44, 46, 47, 48, 49, 51, 54, 58], "review_and_not": 5, "revoked_at": 7, "revolution": 55, "revolutionari": [2, 24, 47, 57, 58], "reward": [2, 32], "rgba": 23, "rich": [37, 47], "right": [20, 30, 31, 44], "risk": [4, 8, 10, 14, 15, 17, 28, 31, 32, 34, 36, 37, 41, 51, 54, 55, 57], "risk_analysi": 9, "risk_assess": 4, "risk_factor": [3, 4, 8, 9, 10], "risk_level": [3, 10], "risk_mitig": 8, "roadmap": [22, 25, 28, 31, 32, 34, 37, 47, 50, 51, 55, 57, 58], "roadmap_456": 8, "roadmap_id": 8, "robot": 1, "robust": [52, 53], "roc_auc_scor": 19, "roi": [1, 9, 11, 13, 17, 20, 24, 29, 30, 31, 32, 35, 37, 41, 43, 48, 50, 51, 54, 55, 57, 59], "roi analysi": [4, 36], "roi_3_year": 9, "roi_analysi": [3, 9, 24, 55], "roi_analysis_step": 55, "roi_dashboard": 3, "roi_percentag": [3, 8, 9, 10], "roi_perform": 9, "roi_realis": 9, "roi_scor": [5, 9, 58], "roi_summari": 3, "roianalysi": 55, "role": [10, 20, 24, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 40, 41, 46, 47, 48, 50, 52, 54, 55, 57, 58, 59], "role_assign": 12, "role_id": [8, 59], "role_titl": 8, "rollback": 27, "rollout": 53, "room": 46, "root": [22, 23, 35], "rotat": [7, 18, 27, 35, 44], "round": 41, "rout": [17, 22, 35, 36, 40, 42, 48, 52, 55, 59], "router": [17, 22, 40], "routin": 35, "rpo_hour": 18, "rst": [1, 23, 25], "rtd": [1, 23, 51], "rtl": 52, "rto_hour": 18, "ru": 13, "rubi": 4, "rubric": 26, "rule": 27, "run": [1, 12, 19, 21, 22, 23, 25, 40, 42, 45, 47, 49, 51, 58], "run_api": [21, 49], "runbook": 27, "runtim": 22, "rush": 33, "r\u00b2": 19, "s3": 18, "saa": 41, "sabbat": 28, "sabsa": 8, "safari": [36, 43, 44], "safe": 27, "safeti": [22, 43], "salari": [5, 9, 17, 28, 31, 32, 33, 35, 36, 41, 46, 47, 48, 50, 51, 54, 55, 58, 59], "salary_data": [8, 55], "salary_factor": 8, "salary_growth_r": 8, "salary_impact": 9, "salary_increas": 9, "salary_increase_tim": 9, "salary_increases_receiv": 9, "salary_intellig": 55, "salary_rang": 8, "salary_target": 8, "salary_trend": 4, "salaryreport": 54, "sale": 56, "same": [32, 33], "saml": [13, 17, 18, 24, 29, 41, 47, 50, 54], "saml_metadata_url": 18, "sampl": 29, "san": [4, 7, 32, 56], "sandbox": 56, "sanitis": 27, "sarah": 10, "satisfact": [2, 24, 31, 34, 35, 41, 53, 55, 56], "satisfaction_trend": 3, "satur": [4, 36], "save": [2, 17, 19, 24, 36, 44, 46, 48, 54, 55, 59], "save_model": 19, "savings_percentag": 3, "scalabl": [2, 17, 20, 22, 23, 24, 35, 37, 41, 48, 50, 52, 53, 57, 59], "scale": [13, 24, 27, 29, 34, 36, 37, 41, 48, 50, 52, 55, 56], "scaled_featur": 19, "scaler": 19, "scan": [27, 28, 42, 45], "scatter": 3, "scenario": [3, 5, 11, 27, 29, 31, 33, 36, 37, 42, 45, 47, 48, 50, 52, 53, 55], "scenario_abc123": 9, "scenario_id": 9, "scenario_nam": 9, "schedul": [2, 3, 4, 5, 8, 9, 12, 14, 15, 17, 24, 27, 29, 32, 33, 34, 35, 36, 37, 41, 43, 44, 46, 47, 53, 55, 56, 58], "schedule_adjust": 14, "schedule_optim": 53, "schedule_optimis": 14, "scheduled_report": 18, "schema": [11, 20, 27, 52, 54, 55], "scheme": [1, 22, 23], "scholar": 14, "school": 33, "scope": 12, "score": [2, 8, 12, 13, 14, 15, 19, 24, 31, 33, 34, 35, 36, 47, 48, 50, 52, 53, 54, 55, 56, 58, 59], "scorecard": 34, "scorereport": 56, "scorereportingapi": 56, "screen": [0, 23, 42, 43, 44, 45, 47], "screenshot": [1, 36], "script": [1, 21, 23, 27, 33, 40, 43, 49], "scroll": 42, "scrollbar": 0, "sdk": [29, 48], "seamless": [2, 16, 17, 24, 34, 39, 41, 42, 43, 44, 47, 48, 50, 56, 59], "seamlessli": [17, 36, 40, 48, 59], "search": [13, 21, 23, 25, 30, 33, 35, 39, 42, 43, 47, 48, 52, 56, 58], "searchabl": [1, 23], "searchbox": 23, "searchtool": 1, "season": 10, "sec": [10, 55], "second": [1, 2, 4, 9, 23, 24, 36, 37, 39, 41, 43, 48, 50, 52, 53, 55], "secondari": 23, "secondary_styl": 5, "secondarycolor": 23, "secret": [1, 11, 12, 18, 23, 27, 40, 49, 58], "secret_kei": [6, 7, 18, 20, 27, 49], "secret_xyz789": 7, "section": [0, 6, 13, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34, 39, 43, 47, 48, 57, 58], "sector": [32, 36, 55], "secur": [0, 2, 3, 4, 8, 9, 10, 12, 14, 15, 16, 17, 18, 21, 22, 24, 28, 30, 31, 32, 33, 34, 35, 36, 40, 41, 42, 48, 49, 50, 52, 53, 54, 55], "secure_browser_xss_filt": 18, "secure_content_type_nosniff": 18, "secure_hsts_include_subdomain": 18, "secure_hsts_second": 18, "secure_password": 6, "secure_ssl_redirect": 18, "securepassword123": 7, "security_career_framework": 20, "security_ev": 10, "security_polici": 13, "security_scor": 7, "security_set": 12, "security_team_s": 4, "see": [3, 11, 27, 32, 33, 42, 45, 46, 48, 59], "seed": 40, "seed_data": 40, "seek": 8, "segment": 52, "select": [2, 6, 20, 30, 35, 36, 37, 44, 46, 55], "selector": 40, "self": [2, 6, 19, 20, 21, 24, 29, 33, 40, 41, 53, 54, 55, 56], "self_assess": 8, "self_studi": 9, "semant": [0, 17, 52], "send": [17, 35, 40], "send_email": 13, "send_invit": 10, "send_verification_email": 40, "senior": [4, 8, 9, 10, 13, 19, 32, 33, 36], "senior_analyst": 13, "sensit": [17, 20, 27, 29, 36, 41, 43, 55], "sensitivity_analysi": 4, "sent": [5, 7, 13, 19, 20], "sentiment": 35, "sentri": 18, "sentry_dsn": 18, "seo": [1, 22, 23, 48], "separ": [20, 21, 24, 27, 29, 35, 40, 41, 44, 50], "sequenc": [2, 5, 19, 20, 23, 37, 47, 50, 53, 58], "sequenti": 25, "seri": [33, 36], "serialis": 19, "serv": [1, 51, 52, 53], "server": [1, 4, 7, 11, 12, 16, 18, 21, 22, 27, 41, 43, 45, 48, 49], "servic": [0, 2, 5, 8, 10, 17, 19, 21, 24, 26, 27, 28, 29, 33, 34, 35, 36, 37, 40, 41, 42, 43, 48, 49, 50, 52, 53, 54, 55, 56, 57], "sess_abc123": 7, "session": [0, 1, 2, 3, 5, 11, 13, 17, 18, 19, 20, 22, 27, 29, 31, 32, 33, 35, 36, 37, 41, 42, 43, 44, 46, 47, 48, 52, 53, 54, 58], "session manag": 7, "session_12345": 14, "session_abc123": 15, "session_cache_ttl": 18, "session_context": 14, "session_dur": 15, "session_duration_minut": 5, "session_id": [7, 14, 15], "session_length_minut": 5, "session_not_found": 14, "session_polici": 13, "session_r": 14, "session_rating_averag": 14, "session_summari": [14, 15], "session_timeout_minut": [10, 12, 18], "sessiondata": 40, "sessions_complet": [12, 14], "sessions_count": 15, "set": [6, 10, 12, 14, 15, 18, 20, 23, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 40, 41, 44, 47, 48, 49, 51, 52, 54, 57, 58], "setisload": 42, "setorgan": 40, "setup": [1, 9, 10, 12, 18, 21, 22, 24, 30, 33, 35, 36, 44, 46, 47, 48, 49, 52, 55, 56], "setup_api_integr": 56, "setup_method": 21, "setup_time_estim": 12, "setus": 40, "sever": [3, 52, 57], "sh": 51, "sha256": 12, "shadcn": 22, "shadow": 23, "shake": [22, 42], "shard": 27, "share": [0, 1, 3, 13, 20, 22, 24, 26, 28, 30, 31, 32, 33, 34, 35, 36, 43, 44, 46, 48, 52, 56, 57], "sheet": 53, "shift": [43, 50], "shortcut": 44, "shorter": 15, "shortest": 9, "should": [21, 25, 28, 31, 32, 33, 34, 37, 42, 45], "show": [0, 1, 3, 23, 26, 28, 29, 31, 32, 33, 34, 36, 37, 42, 47, 53], "showcas": [28, 39, 43], "side": [16, 22, 23, 28, 32, 43, 48, 58], "siem": 41, "sign": [7, 11, 13, 20, 24, 25, 29, 35, 41, 42, 43, 44, 45, 47, 48, 50], "signatur": 12, "signature_head": 12, "signature_method": 12, "signature_valid": 12, "signific": [8, 9, 22, 32, 46], "significant_gap": 8, "significantli": 26, "signup": 22, "silhouett": 19, "silo": 59, "similar": [2, 34, 37, 47, 53], "simpl": [17, 53], "simul": [5, 55], "simultan": [17, 35, 52], "singapor": 56, "singl": [7, 11, 13, 17, 20, 24, 27, 29, 35, 41, 44, 46, 47, 48, 50, 59], "site": [29, 43], "sitemap": [0, 1], "six": [10, 48], "size": [0, 10, 11, 13, 22, 31, 36, 40, 41, 42, 43, 44, 47, 52, 53, 54, 55, 56], "skeleton": 42, "skill": [2, 3, 4, 11, 17, 24, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 41, 43, 46, 47, 48, 50, 53, 54, 55, 58, 59], "skill_assess": [5, 58], "skill_categori": 8, "skill_develop": [5, 10], "skill_gap": 3, "skill_gap_risk": 4, "skill_gaps_identifi": 10, "skill_id": [5, 58], "skill_improv": 3, "skill_level": [5, 58], "skill_nam": [5, 8, 58], "skill_strength": 8, "skills_analysi": 8, "skills_assess_789": 8, "skills_develop": 8, "skills_evolut": 8, "skills_gap_analysi": 8, "skills_premium": 8, "skills_requir": 8, "skills_shortage_area": 8, "skills_transfer": 8, "skillsgapreport": 54, "skip_dupl": 10, "skip_valid": 12, "sklearn": 19, "sla": [4, 24, 52], "slack": [18, 27], "slack_webhook": 18, "slim": 40, "slow": [27, 35, 36, 43, 46], "small": [27, 28, 41, 44, 56], "smaller": 22, "smart": [2, 17, 42, 44, 47, 51, 53, 57], "smartphon": 44, "smartwatch": 44, "smith": [10, 12, 13], "smoke": [42, 45], "smooth": [0, 17, 22, 43, 59], "smoothli": 34, "smtp": [18, 27, 49], "smtp_": 27, "smtp_host": [18, 27, 49], "smtp_password": [18, 27, 49], "smtp_port": [18, 27, 49], "smtp_ssl": 18, "smtp_tl": [18, 27, 49], "smtp_user": [18, 27, 49], "so": [21, 42, 45], "soc": [2, 24, 27, 29, 37, 41, 44, 48, 50, 57], "soc2": [10, 24], "soc2_compli": 10, "soc2_en": 18, "social": [0, 1, 23, 43], "softwar": [14, 21, 26, 27, 33, 41, 54, 55], "solid": [23, 36, 37], "solut": [8, 21, 29, 36, 43, 48, 54, 56], "solv": 21, "some": [17, 31, 33, 36, 46], "somehow": 32, "someon": [31, 33], "sophist": [2, 19, 24, 48, 50, 51, 53, 55, 57], "sort": 21, "sourc": [1, 9, 14, 21, 23, 28, 35, 48, 49, 56], "source_compon": 59, "source_endpoint": 12, "source_suffix": 23, "sourcecod": [1, 23], "south": 56, "sovereignti": [24, 41], "sox": [17, 54, 59], "sox_compli": 13, "space": [14, 17, 27, 31, 37, 44, 47, 56], "speak": 56, "spec": 40, "special": [3, 4, 7, 31, 32, 37, 47, 52, 57], "specialis": [29, 30, 33], "specialist": 32, "specialized_train": 4, "specialti": [5, 8, 10], "specif": [1, 3, 5, 7, 8, 9, 10, 11, 13, 15, 16, 17, 18, 21, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 41, 43, 46, 47, 48, 51, 52, 53, 54, 55, 57, 58], "specifi": 20, "specific_skil": 8, "speed": [14, 37, 41, 43, 46], "speed_learn": 14, "spell": [21, 23], "spend": [9, 33, 36, 37, 54], "spending_trend": 9, "spent_to_d": 10, "sphinx": [17, 21, 25, 48], "sphinx_copybutton": [1, 23], "sphinx_design": [0, 1, 23], "sphinx_inline_tab": 23, "sphinx_rtd_them": [1, 23], "sphinx_tab": [1, 23], "sphinx_togglebutton": 23, "sphinxext": [0, 1, 23], "split": [22, 42, 43, 44], "splunk": [27, 28], "sponsor": 26, "sponsorship": 33, "spread": [9, 31, 34], "spss": 26, "sql": 28, "sqlalchemi": 20, "sqlite": [18, 49], "squar": 44, "src": [1, 22, 23, 55], "sscp": 33, "ssl": 27, "ssl_cert_path": 27, "ssl_key_path": 27, "sso": [11, 13, 17, 18, 20, 24, 29, 35, 40, 41, 44, 47, 48, 50, 52, 54, 57], "sso_authent": 12, "sso_azure_123": 12, "sso_config_123": 7, "sso_config_id": 7, "sso_en": [13, 40], "sso_endpoint": 12, "sso_integration_id": 12, "sso_provid": 10, "sso_test_789": 12, "ssoconfig": 54, "ssointegrationservic": 54, "stabil": [2, 36, 43], "stabl": [3, 14, 36, 43, 44, 47], "stack": [20, 22, 27, 31, 34, 42], "staff": [4, 31], "stage": [9, 18, 20, 27, 35], "stagger": [31, 34], "stai": [9, 28, 31, 32, 33, 47], "stakehold": [25, 31, 34, 36, 54, 56], "staletim": 39, "stand": 33, "standard": [0, 4, 10, 12, 16, 17, 19, 20, 22, 26, 27, 29, 30, 34, 35, 41, 42, 43, 45, 48, 49, 52, 59], "standardis": 34, "standardscal": 19, "standbi": 27, "star": 53, "start": [9, 11, 13, 18, 22, 24, 27, 30, 32, 34, 35, 41, 44, 50], "start_dat": [10, 12, 13, 14, 15], "start_month": 9, "start_study_sess": 40, "started_at": [12, 14, 15], "starter": 54, "startonload": 23, "startstudysess": 40, "starttim": 45, "startup": 8, "stat": [22, 39, 42, 47], "state": [0, 11, 14, 19, 22, 38, 42, 43, 46, 47, 48, 58], "stateless": 20, "statement": 21, "static": [1, 22, 23, 43, 48], "statist": [1, 10, 13, 23, 26, 35, 36, 37, 39, 42, 43, 55], "statistics_upd": 14, "statu": [3, 4, 7, 10, 11, 13, 14, 15, 16, 22, 29, 35, 36, 40, 41, 52, 53, 56, 57], "status_cod": [6, 58], "statuscod": [1, 23], "steadi": 46, "step": [1, 8, 21, 23, 25, 27, 29, 33, 36, 47, 48, 55, 56], "stick": 58, "sticky_navig": [1, 23], "still": [33, 48], "stop": 49, "storag": [6, 7, 19, 20, 27, 35, 43, 49], "store": [6, 19, 20, 22, 27, 43, 44], "stori": [21, 28, 48, 52, 53, 55, 57], "storybook": 22, "str": [19, 21, 54, 55, 59], "strateg": [3, 8, 9, 20, 24, 30, 32, 34, 36, 41, 47, 56, 58], "strategi": [0, 2, 9, 17, 20, 27, 30, 31, 34, 35, 36, 39, 41, 47, 48, 50, 51, 55, 57, 58], "strategic_insight": 3, "strategic_prior": [4, 40], "stratifi": 19, "streak": [15, 22, 43, 46, 47], "stream": [2, 40, 41, 52, 53, 54, 55, 56], "streamlin": [2, 24, 35, 47], "streamlit": 49, "strength": [3, 5, 7, 13, 15, 37, 43, 47, 53, 58], "strengthen": 5, "strict": [18, 19, 22, 43, 48, 52], "string": [39, 40, 52, 53, 59], "stringifi": 40, "strong": [3, 5, 7, 8, 9, 10, 13, 14, 27, 28, 35, 36, 37, 43, 47], "strongarea": 53, "structur": [10, 13, 17, 20, 24, 27, 29, 32, 33, 35, 41, 42, 43, 48, 54, 56, 57], "struggl": [26, 34, 53], "student": [1, 2, 24, 25, 26, 27, 30, 48], "studi": [1, 3, 8, 9, 13, 16, 19, 20, 21, 22, 24, 26, 28, 30, 31, 32, 34, 35, 42, 43, 44, 45, 46, 48, 51, 52, 55, 56, 57], "study sess": 15, "study_adjust": 5, "study_consist": [14, 19], "study_duration_dai": 12, "study_effici": [12, 14], "study_efficiency_improv": 9, "study_featur": 19, "study_hour": [3, 5, 13, 14, 15, 18, 53, 55], "study_hours_complet": 14, "study_hours_per_week": [3, 4, 9], "study_hours_plan": [5, 14], "study_hours_target": 14, "study_intens": 9, "study_materi": [4, 14], "study_method": 14, "study_method_effect": 19, "study_pattern": [14, 15], "study_pattern_insight": 14, "study_phas": 4, "study_plan": [2, 5, 9], "study_plan_adher": 15, "study_plan_id": 15, "study_profil": 59, "study_schedul": [14, 15], "study_schedule_templ": 5, "study_sess": [5, 11, 12, 40, 58], "study_statist": 12, "study_streak_dai": 15, "study_time_hour": 9, "study_time_per_week": 8, "study_time_plan": 5, "study_time_week": 8, "study_track": 40, "study_typ": [14, 15, 40], "studyassistantai": 53, "studyhour": 45, "studyprofil": 59, "studyprogress": 53, "studysessionev": 53, "studysessionloggedev": 52, "studysessiontrack": 40, "style": [2, 20, 21, 22, 25, 33, 34, 36, 37, 43, 46, 47, 48, 53, 58, 59], "style_confid": 19, "style_external_link": 23, "style_nav_header_background": [1, 23], "stylu": 44, "sub": [2, 24, 37, 41, 48, 50], "subject": [25, 31], "submiss": 45, "submit": [1, 42, 43, 53, 54, 58], "submitbutton": [42, 45], "subnet": 15, "subsampl": 19, "subscrib": 27, "subscript": [13, 46, 54, 56], "subscriptionchangedev": 52, "subscriptionti": 52, "subsect": [1, 23], "subsequ": [6, 58], "subset": [54, 56], "success": [1, 4, 5, 6, 7, 9, 12, 13, 16, 17, 19, 20, 23, 28, 29, 31, 33, 34, 35, 36, 37, 41, 45, 47, 48, 49, 50, 51, 59], "success_factor": [5, 8, 14], "success_prob": [3, 4, 5, 8, 9, 14, 15, 19, 53], "success_r": [3, 4, 10, 13], "success_strategi": 8, "successfactor": 29, "successful_import": 10, "successful_sync": 12, "successful_sync_operations_24h": 12, "successfulli": [5, 7, 11, 15, 22, 51, 54, 55], "successprob": 53, "sudo": 1, "suffici": [12, 27], "suggest": [0, 1, 2, 5, 8, 9, 12, 17, 19, 21, 22, 23, 25, 30, 31, 33, 34, 37, 43, 44, 46, 47, 50, 52, 58], "suggested_certif": 5, "suggested_techniqu": 14, "suit": [17, 19, 20, 21, 22, 29, 49, 54, 55], "summar": 0, "summari": [9, 10, 12, 21, 34, 36, 39, 44, 48, 51], "sundai": 14, "super": [6, 18, 24, 41], "super_admin": 20, "superior": 52, "support": [0, 3, 7, 9, 10, 11, 13, 20, 22, 23, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 42, 44, 46, 48, 49, 50, 52, 54, 55, 56, 57, 59], "surnam": 12, "survei": [26, 32], "suspend": 35, "suspici": 7, "suspicious_act": 7, "sustain": [2, 55], "swagger": [11, 20], "swipe": [0, 43, 44], "switch": [22, 43, 44], "symmetr": 14, "sync": [1, 15, 23, 24, 29, 41, 42, 43, 44, 47, 50, 56], "sync_cap": 12, "sync_config_123": 12, "sync_configur": 12, "sync_configuration_id": 12, "sync_cours": 12, "sync_direct": 12, "sync_ent": 12, "sync_frequ": 12, "sync_job_456": 12, "sync_job_id": 12, "sync_nam": 12, "sync_progress": 12, "sync_schedul": 12, "sync_set": 12, "sync_success_r": 12, "sync_summari": 12, "sync_typ": 12, "sync_us": 12, "synchron": [0, 13, 15, 17, 24, 29, 39, 41, 43, 44, 47, 48, 50, 56, 59], "synchronis": [11, 48], "syntax": [1, 23], "system": [2, 6, 11, 13, 15, 16, 17, 18, 19, 21, 22, 23, 24, 28, 29, 30, 31, 32, 33, 34, 36, 37, 39, 40, 41, 44, 48, 49, 50, 52, 53, 54, 55, 56, 59], "system_perform": 3, "system_usag": 18, "systemat": 27, "t": [16, 23, 25, 30, 31, 32, 33, 55], "tab": [0, 1, 23, 36, 42, 43, 45, 47, 55], "tabl": 0, "tableau": [3, 29], "tablet": [39, 43, 44, 47], "tag": [0, 22, 23, 40], "tailor": [41, 46, 47], "tailwind": [22, 39, 48], "take": [5, 33, 36, 44, 46, 47, 58], "talent": 31, "talk": [28, 33], "tap": 44, "target": [3, 4, 14, 17, 25, 26, 28, 32, 33, 36, 37, 42, 43, 45, 46, 47, 50, 51, 55, 57, 58], "target_certif": 2, "target_certification_id": 59, "target_complet": 8, "target_completion_month": 9, "target_curr": 9, "target_d": [12, 14], "target_domain": 5, "target_exam_d": [5, 15], "target_knowledge_level": 14, "target_level": 3, "target_masteri": 15, "target_metr": 14, "target_profil": 8, "target_rol": [8, 40, 55, 58], "target_role_id": [4, 8, 40], "target_role_titl": 8, "target_salari": 8, "target_salary_rang": 8, "target_study_hour": 15, "target_timeline_year": 8, "target_top": 15, "task": [18, 23, 30, 35, 43, 44, 46, 49], "taxonomi": 8, "tcp": 15, "td": [1, 23], "teach": [30, 37], "team": [1, 7, 8, 9, 10, 12, 23, 24, 25, 26, 29, 30, 32, 35, 36, 40, 41, 46, 47, 48, 51, 52, 53, 54, 55, 56, 57, 58], "team administr": 13, "team_comparison": 3, "team_constraint": 4, "team_data": [13, 40], "team_engagement_scor": 10, "team_id": [3, 13, 40], "team_manag": 13, "team_memb": 13, "team_nam": [3, 13], "team_perform": [3, 10, 13], "team_roi": 3, "team_scor": 13, "team_upd": 13, "teammanag": 54, "technic": [3, 4, 8, 12, 13, 24, 27, 30, 34, 36, 41, 43, 46, 47, 48, 50, 51, 57, 58, 59], "technical_train": 3, "techniqu": [0, 32, 37], "technologi": [2, 3, 4, 13, 20, 22, 24, 25, 31, 32, 33, 34, 36, 37, 40, 48, 50, 56], "telemetri": [2, 5, 48], "templat": [18, 21, 22, 23, 25, 26, 27, 28, 31, 34, 40], "temporari": 24, "temporarili": [5, 36, 43], "ten_year_roi": 4, "tenant": [10, 11, 13, 17, 18, 20, 24, 29, 30, 35, 41, 48, 50, 51, 52, 54, 58, 59], "tenant_id": [12, 18], "tenant_url": 18, "tentative_g_scor": 55, "term": [4, 9, 25, 28, 29, 32, 33, 34, 36, 37, 41, 43, 47, 58], "termin": [40, 49], "terminologi": [25, 35], "terraform": 27, "tertiarycolor": 23, "test": [1, 8, 11, 14, 17, 18, 19, 20, 23, 26, 27, 28, 29, 32, 33, 37, 43, 44, 46, 47, 48, 49, 51, 54, 56, 57, 58, 59], "test_agent4_complete_valid": 55, "test_agent4_integr": 55, "test_authentication_success": 6, "test_budget_optimization_api": 55, "test_calculate_basic_cost": 21, "test_career_pathfind": 55, "test_client": 6, "test_cost_calcul": [21, 55], "test_cross_service_consist": 55, "test_detail": 12, "test_endpoint": 12, "test_environ": 18, "test_id": 12, "test_invalid_credenti": 6, "test_password": 6, "test_password_123": 6, "test_performance_under_load": 55, "test_result": 12, "test_roi_analysi": 55, "test_salary_intellig": 55, "test_siz": 19, "test_typ": 12, "test_us": [6, 12], "testabl": 52, "testclient": 52, "testcostcalculatorservic": 21, "testid": 42, "testing_guid": 1, "texliv": 1, "text": [0, 1, 6, 18, 23, 43, 45, 47, 52, 53], "than": [4, 21, 31, 32, 33, 37, 47], "thank": 21, "thei": [32, 33, 58], "them": [31, 32, 33, 58], "themat": 25, "theme": [0, 1, 22, 23, 29, 43, 51, 52], "theme_analytics_id": [1, 23], "themevari": 23, "theoret": [31, 32], "theory_review": 5, "thi": [0, 1, 2, 6, 7, 9, 10, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 46, 47, 49, 50, 51, 54, 55, 57, 58], "think": [28, 31, 32], "third": [1, 11, 40, 43, 48, 50, 52, 53, 56], "thirdpartyintegr": 56, "thorough": 37, "thoroughli": [25, 33], "those": [28, 31, 33], "thought": [8, 47], "thousand": [29, 48], "threat": 28, "three": [2, 44, 48], "threshold": [27, 37, 41, 54], "throttl": 13, "through": [1, 5, 16, 19, 24, 28, 29, 31, 32, 33, 35, 37, 39, 44, 45, 46, 48, 51, 52, 53, 54, 55, 56, 57, 58, 59], "throughout": [25, 53], "thursdai": [3, 15], "ticket": [17, 24, 46], "tie": 31, "tier": [5, 41, 46, 48, 52, 56], "time": [0, 1, 2, 4, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 22, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 46, 47, 48, 50, 51, 52, 53, 54, 56, 57, 58, 59], "time_alloc": 5, "time_cost": 55, "time_estim": 5, "time_horizon_year": 9, "time_invested_hour": 14, "time_opportunity_cost": 4, "time_per_question_second": 14, "time_period": 2, "time_rang": 14, "time_spent_minut": [14, 15], "time_to_complete_month": 9, "time_week": 8, "timedelta": 6, "timefram": [4, 36], "timelin": [2, 4, 9, 22, 24, 28, 31, 33, 34, 36, 37, 43, 46, 47, 50, 52, 53, 54, 55, 56, 57, 58], "timeline_effici": 4, "timeline_month": [4, 58], "timeline_plan": 8, "timeline_r": 8, "timeline_risk": 4, "timeline_week": 2, "timeout": 35, "timeout_second": 12, "timer": 47, "timestamp": [3, 6, 7, 10, 12, 13, 16, 17, 40, 51, 52, 59], "timestamp_head": 12, "timezon": 12, "timing_advic": 9, "tip": [23, 30, 36, 48, 53], "titl": [1, 8, 11, 12, 14, 15, 23, 59], "titles_onli": 23, "tl": [20, 27, 52], "tmp": 51, "to_role_id": 4, "tobefocus": [42, 45], "tobeinthedocu": [42, 45], "tobelessthan": 45, "tobevis": 42, "toctre": 25, "todai": 44, "todo": 23, "toequal": [42, 45], "togaf": 8, "togeth": [17, 21, 36, 40, 48], "toggl": 23, "tohavebeencalledtim": 45, "tohavebeencalledwith": [42, 45], "tohaveurl": 45, "token": [0, 1, 4, 5, 8, 9, 11, 12, 14, 17, 18, 20, 21, 22, 23, 24, 39, 40, 42, 43, 47, 52, 58], "token_data": [6, 58], "token_expir": 6, "token_typ": [6, 7], "toler": 37, "tone": 25, "too": [4, 6, 7, 11, 28], "tool": [1, 3, 9, 12, 13, 17, 22, 26, 28, 29, 30, 31, 32, 33, 34, 35, 41, 43, 44, 47, 52, 54, 55, 56, 58], "toolkit": [26, 28, 31, 32, 34], "tooltip": [0, 25], "top": [3, 23, 36, 44, 46], "top_certif": [4, 10, 13], "top_insight": 59, "top_perform": 3, "top_skills_develop": 10, "topic": [2, 5, 14, 15, 20, 25, 33, 37, 40, 46, 47, 48, 50, 53], "topic_breakdown": 14, "topic_focu": 15, "topic_id": 2, "topic_perform": 15, "topic_progress": 14, "topic_recommend": [2, 5, 18, 19, 53], "topic_similarity_matrix": 19, "topic_suggest": 5, "topicrecommendermodel": 19, "topics_complet": 15, "topics_cov": [14, 15], "topics_per_week": 15, "topics_studi": 5, "topicscov": 53, "total": [4, 9, 11, 19, 21, 33, 34, 35, 36, 43, 46, 47, 52, 55, 57, 58], "total_achiev": 14, "total_active_us": 10, "total_budget": [4, 9, 13, 40], "total_certif": 13, "total_certification_cost": 9, "total_certifications_compar": 9, "total_compens": 8, "total_cost": [4, 9, 21, 55], "total_depart": 10, "total_duration_minut": [14, 15], "total_duration_month": 8, "total_ev": 10, "total_go": 15, "total_high_demand_rol": 8, "total_integr": 12, "total_invest": [3, 8, 9], "total_investment_requir": 8, "total_learning_hours_requir": 8, "total_lic": 10, "total_log": 13, "total_memb": 13, "total_packag": 8, "total_pag": 13, "total_point": 14, "total_record": 12, "total_reimburs": 9, "total_requir": 10, "total_return": 3, "total_s": 13, "total_sess": [7, 14, 15], "total_study_hour": [3, 12, 14, 15, 19], "total_study_hours_requir": 14, "total_study_time_minut": [14, 15], "total_sync_operations_24h": 12, "total_team": 13, "total_team_memb": 10, "total_time_minut": 14, "total_timeline_month": 9, "total_timeline_year": 8, "total_training_invest": 10, "total_us": [10, 13], "total_value_gener": 3, "total_week": 5, "touch": [0, 42, 43, 44, 46, 47], "tour": 48, "toward": [14, 21, 31, 32, 37, 44, 46, 47, 58], "trace": [17, 22, 53], "track": [0, 1, 3, 7, 10, 17, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 35, 36, 37, 40, 41, 42, 43, 44, 46, 48, 50, 52, 53, 54, 55, 56, 57, 59], "track_learning_journei": 40, "tracker": [31, 32, 33], "tracking_setup": 14, "tradit": [26, 53], "traefik": [16, 17, 49], "traffic": 57, "trail": [24, 27, 29, 41, 52, 54], "train": [1, 2, 3, 4, 11, 13, 17, 20, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 41, 46, 47, 48, 50, 51, 53, 54, 55, 56, 58, 59], "train_all_model": 19, "train_test_split": 19, "training_cost": [3, 55], "training_cost_per_employe": 3, "training_cost_rang": 9, "training_d": 5, "training_data": 19, "training_deliveri": 3, "training_duration_second": 5, "training_manag": [1, 25], "training_metr": 5, "training_prior": 13, "training_provid": 9, "training_result": 19, "trainingproviderapi": 56, "trajectori": 37, "transact": [56, 57, 59], "transaction_id": 56, "transaction_log_backup_frequ": 18, "transfer": [8, 28, 37, 58], "transform": [2, 19, 24, 27, 28, 41, 44, 47, 48, 50, 53, 54, 56, 58, 59], "transit": [1, 17, 20, 22, 28, 29, 30, 31, 32, 33, 34, 36, 40, 41, 43, 44, 46, 48, 50, 51, 52, 55, 56, 59], "transition_cost": 55, "transition_feas": 8, "translat": [23, 28, 48, 56], "transmiss": [37, 44, 57], "transmit": 43, "transpar": [19, 20, 43, 57], "travel": [34, 44, 47, 55], "travel_cost": 4, "travel_expens": 9, "treat": 21, "tree": [19, 22, 42], "trend": [2, 4, 8, 9, 13, 24, 25, 26, 32, 35, 36, 37, 41, 43, 46, 47, 48, 50, 53, 54, 55, 58, 59], "trend_analysi": 3, "trend_chart": 3, "trending_metr": 3, "trial": 26, "trigger": [12, 57], "triggered_at": 3, "troubleshoot": [27, 28, 30, 48, 58], "true": [1, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 18, 21, 23, 24, 40, 42, 49, 58], "trust": [8, 10, 24, 58, 59], "truth": 48, "try": [6, 19, 36, 42, 43, 46, 58], "tsx": [22, 55], "ttl": 20, "tuesdai": [3, 15], "tune": [18, 35, 37, 58], "tutor": 58, "tutori": [30, 36, 46, 47, 48, 52, 55, 58], "two": 35, "txt": [1, 21, 23, 40, 49], "type": [1, 2, 3, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 22, 23, 24, 33, 35, 36, 37, 40, 41, 42, 43, 44, 45, 47, 48, 50, 56, 57, 58, 59], "typescript": [0, 11, 22, 42, 43, 48, 57], "typic": [28, 32, 33, 34], "typical_certif": 8, "typical_duration_month": 4, "typographi": 22, "u": [9, 18, 56], "ubuntu": [1, 21, 23], "ui": [1, 11, 20, 43, 47, 48, 51, 52, 53, 55, 57], "uk": [8, 9, 10, 56], "ultim": [47, 48], "unabl": [8, 9], "unauthor": [1, 4, 6, 7, 11, 16, 23, 54], "unavail": [5, 12], "uncertainti": [2, 50], "unclear": 21, "under": [11, 14, 16, 17, 24, 50], "understand": [0, 5, 14, 28, 30, 31, 32, 33, 37, 38, 43, 47, 58], "undoc": [1, 23], "unexpect": 36, "unif": 17, "unifi": [24, 41, 53, 55, 56, 57], "unified_dashboard_service_error": 16, "unified_deploy": 59, "unifieddashboard": 59, "unifieddashboardservic": 17, "unifiederrorhandl": 17, "unifieduserprofileservic": 17, "uniqu": [21, 26, 28, 29, 31, 37, 47, 48, 50, 58], "unit": [0, 8, 9, 10, 11, 21, 22, 29, 42, 48, 52, 53, 54, 55, 57], "unittest": 21, "univers": 33, "unlimit": [2, 10, 18, 24, 29, 37, 41, 48, 50], "unlock": [2, 24, 37, 43, 48], "unnecessari": [43, 47], "unpreced": [2, 24, 48, 50, 55], "unprocess": [11, 16], "unrespons": 43, "until": [33, 43, 47], "up": [1, 6, 12, 14, 23, 25, 27, 28, 29, 32, 33, 35, 40, 43, 44, 46, 47, 49, 58], "upcom": [48, 51, 58], "upcoming_mileston": 59, "updat": [2, 3, 5, 8, 12, 17, 20, 21, 22, 23, 24, 27, 30, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 46, 47, 48, 52, 53, 54, 55, 56, 58], "update_session_progress": 40, "updated_at": [7, 10, 13, 15], "updated_predict": 5, "updatedashboard": 40, "upfront": [31, 36, 47], "upgrad": [1, 21, 22, 23, 27, 40, 49, 53], "upgrade_requir": 10, "upload": 6, "upper": [3, 31], "uppercas": 7, "upstream": 21, "uptim": [2, 4, 24, 52, 55, 57], "uptime_percentag": 3, "urgenc": 34, "urgent": 44, "url": [0, 1, 4, 6, 12, 48, 49, 58], "us": [0, 1, 4, 5, 6, 7, 9, 10, 11, 12, 16, 17, 19, 20, 21, 23, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 42, 43, 44, 45, 46, 47, 49, 51, 52, 53, 55, 58, 59], "usabl": [24, 25, 42, 50], "usag": [1, 2, 5, 6, 11, 23, 24, 25, 27, 37, 41, 43, 44, 46, 47, 48, 49, 50, 56, 58, 59], "usage_track": 18, "usage_trend": 10, "usd": [4, 9], "useauth": [22, 42], "usedashboard": 22, "usedashboarddata": 42, "usedjsheaps": 45, "useeffect": 40, "useform": 42, "usemaxwidth": 23, "usenavig": 42, "usequeri": 39, "user": [2, 5, 6, 8, 9, 11, 12, 17, 18, 19, 20, 21, 22, 23, 26, 27, 38, 40, 42, 44, 45, 49, 50, 51, 57, 58], "user manag": 13, "user123": 16, "user_access_control": 13, "user_act": 40, "user_ag": [10, 13], "user_count": 10, "user_cr": 12, "user_data": [19, 40, 53], "user_email": [10, 13], "user_engag": [3, 10, 13], "user_experi": 20, "user_featur": 19, "user_field": 12, "user_growth_r": [10, 13], "user_guid": [1, 23, 28, 33], "user_id": [2, 3, 5, 6, 7, 8, 9, 10, 13, 14, 15, 19, 40, 54, 58, 59], "user_interact": 18, "user_knowledg": 19, "user_manag": 6, "user_profil": [5, 8, 9, 19, 20, 53, 55], "user_progress_upd": 12, "user_provis": 12, "user_role_upd": 13, "user_satisfact": 3, "user_study_data": 19, "user_sync": 12, "user_topic_matrix": 19, "userdata": 53, "userid": [52, 53], "usernam": [1, 6, 11, 12, 18, 23, 58], "userprofil": [53, 55], "userprogressev": 53, "userregisteredev": 52, "usest": [40, 42], "usr": 1, "utcnow": [6, 40], "util": [3, 20, 22, 24, 27, 35, 40, 41, 43, 46, 54, 55], "utilis": [9, 10], "utilisation_r": 10, "utilization_r": [3, 13], "utilized_budget": [3, 13], "uuid": 59, "uvicorn": 40, "ux": [1, 42, 48, 51, 52, 53, 57], "v": [14, 26, 31, 32, 44, 53, 55, 58], "v1": [1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 23, 24, 40, 49, 52, 53, 54, 55, 56, 58, 59], "v3": [1, 23], "v4": [1, 23], "vacuum": 27, "valid": [1, 6, 7, 11, 12, 16, 17, 19, 20, 22, 23, 24, 26, 27, 33, 35, 40, 41, 42, 43, 45, 47, 48, 52, 53, 54, 55, 57, 59], "validate_cross_tenant_access": 54, "validate_faq": 25, "validate_token": 6, "validation_error": [11, 12], "validation_result": [12, 19], "valu": [3, 8, 28, 31, 32, 33, 34, 35, 36, 39, 40, 42, 45, 47, 50, 51, 52, 53, 54, 55, 56, 57, 58], "valuabl": [25, 28, 31, 32, 33, 36, 46, 54], "valueerror": 21, "var": [20, 23], "vari": [28, 36], "variabl": [18, 21, 22, 27, 36, 40], "varianc": 9, "varieti": 47, "variou": [29, 37, 44], "vcs_pageview_mod": 23, "ve": [31, 46], "veloc": [14, 15, 37, 41, 47, 51, 58], "velocity_trend": 14, "vendor": [28, 31, 32, 33, 34, 56], "venv": [21, 49], "verbos": 51, "veri": [33, 36, 46], "verif": [22, 24, 27, 35, 37, 40, 47, 48, 51, 54, 56], "verifi": [12, 21, 25, 35, 36, 40, 43, 46, 49, 51, 56], "version": [1, 12, 18, 20, 21, 23, 35, 36, 41, 43, 48, 52, 57], "vertic": [27, 54], "very_high": 8, "via": [17, 29, 41, 49], "vibrat": 44, "video": [3, 25, 30, 33, 34, 36, 37, 53], "video_cont": 5, "video_cours": 9, "view": [0, 7, 12, 23, 31, 35, 43, 46, 47, 49, 59], "viewcod": [1, 23], "violat": [42, 45], "virtual": [3, 21, 26, 28, 32, 33, 34, 41, 49], "virtualbox": 33, "virtualis": 33, "visibl": [1, 23, 36, 42, 45, 46, 54], "visit": 49, "visual": [0, 1, 2, 5, 15, 17, 22, 24, 28, 29, 31, 32, 33, 34, 36, 37, 40, 42, 43, 44, 47, 48, 52, 53, 55, 58, 59], "visual_diagram": 5, "visualis": 33, "vital": [0, 42, 45], "vlan": 15, "vmware": 33, "voic": [44, 47, 53], "volum": [3, 20, 29, 35, 37], "volunt": [28, 32], "voluntari": 34, "voucher": [31, 33, 56, 59], "vulner": [22, 27, 52, 57], "w": [23, 42], "wa": [14, 31], "wai": [27, 28, 29, 31, 32, 34, 46, 49], "wait": [6, 45, 58], "waitfor": 42, "waitforloadst": 45, "walk": 44, "walkthrough": [30, 36, 52], "want": [21, 31, 42, 44, 45], "wareh": 41, "warehous": 41, "warn": [1, 18, 23, 25, 34], "warning_integr": 12, "warrior": 14, "wb": 19, "wcag": [0, 1, 22, 23, 42, 43, 45, 47, 52], "wcag21aa": [42, 45], "wcag2a": [42, 45], "wcag2aa": [42, 45], "we": [17, 21, 26, 53, 56], "weak": [14, 15, 33, 36, 37, 47, 53, 58], "weak_area": 15, "weak_top": 53, "weak_topic_count": 19, "weakarea": 53, "web": [0, 5, 6, 20, 22, 24, 27, 35, 36, 38, 43, 45, 47, 48, 49], "webhook": [3, 13, 18, 29, 40, 48, 52, 56, 58], "webhook_456": 12, "webhook_config": 58, "webhook_data": 11, "webhook_endpoint": 12, "webhook_id": [12, 58], "webhook_secret": 10, "webhook_secret_key_her": 12, "webhook_url": 12, "webhookmanagementapi": 56, "webinar": [28, 32, 36], "webservic": [12, 18], "websocket": 22, "wednesdai": [3, 14, 15], "week": [3, 5, 14, 15, 33, 52, 58], "weekend": 44, "weekli": [15, 18, 27, 35, 46, 48, 50, 53], "weekly_pattern": 5, "weekly_progress_report": 14, "weekly_schedul": [5, 15], "weekly_study_dai": 5, "weekly_study_hour": 14, "weeks_to_exam": 15, "weight": 37, "welcom": [13, 21, 26, 28, 30, 37, 45, 47, 48, 58], "welcome_messag": 13, "welcomebann": 42, "well": [20, 21, 31, 44, 46, 57], "went": 31, "west": 18, "what": [21, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 46, 48], "wheel": 0, "when": [4, 15, 19, 21, 25, 32, 33, 35, 37, 42, 43, 44, 45, 46, 47, 53], "where": [1, 2, 6, 20, 21, 24, 31, 33, 44, 46, 50, 57], "whether": [21, 31, 44], "which": [25, 28, 32, 33, 37, 44, 46, 51], "while": [2, 25, 28, 31, 32, 33, 35, 37, 41, 42, 44, 50, 53, 54, 55, 56, 57], "whilst": [28, 31, 32, 33], "white": [48, 53, 56], "whitelist": 29, "who": [26, 31, 33, 34], "whsec_5678efgh": 10, "why": [33, 58], "wide": [2, 10, 13, 33, 36, 41, 57], "widget": 43, "width": 23, "wifi": 44, "wildcard": 27, "willing_to_reloc": 8, "win": [31, 32], "wind": 44, "window": [1, 4, 6, 21, 23, 27, 28, 47, 49], "wipe": 44, "wireshark": 28, "within": [9, 10, 13, 14, 30, 31, 32, 34, 39, 45, 52, 55], "without": [2, 17, 24, 28, 37, 42, 43, 44, 46, 47, 50, 52, 53], "withtag": [42, 45], "wizard": 56, "work": [1, 2, 5, 8, 12, 17, 21, 25, 28, 30, 31, 33, 34, 36, 37, 38, 40, 43, 44, 46, 48, 49, 53], "work_email": 12, "workdai": [18, 29], "workdir": 40, "worker": [0, 42], "worker_connect": 18, "worker_process": 18, "workflow": [0, 1, 17, 22, 29, 35, 48, 51, 54, 55, 57, 59], "workforc": [17, 29, 54, 59], "workload": [17, 27], "workplac": 30, "workshop": [26, 29], "workspac": 36, "world": [5, 41, 48, 50], "worldwid": [24, 41, 48], "worst": 36, "worst_case_roi": 4, "worth": [32, 33], "wrap": 23, "wrapper": 51, "wrist": 44, "write": [6, 7, 13, 19, 21, 28, 48, 53], "wrong": 31, "wrong_password": 6, "www": [1, 23], "wy": 23, "x": [4, 6, 11, 12, 16, 17, 18, 19, 23, 47, 59], "x_forwarded_us": 59, "x_remote_us": 59, "x_test": 19, "x_train": 19, "x_user": 59, "xgb": 19, "xgbclassifi": 19, "xgboost": 19, "xl": 39, "xml": 1, "xss": 43, "xxxxxxxxxx": 23, "y": [19, 47], "y_pred": 19, "y_pred_proba": 19, "y_test": 19, "y_train": 19, "yaml": 27, "ye": [26, 28, 29, 31, 32, 33], "year": [8, 9, 17, 18, 19, 24, 31, 34, 36, 46, 48, 50, 52, 53, 54, 55, 56, 57, 59], "year_1": 9, "year_3": 9, "year_5": 9, "yearli": [15, 36, 48, 50], "years_experi": [5, 8, 58], "yml": [1, 18, 20, 49], "you": [18, 21, 28, 30, 31, 32, 33, 35, 36, 37, 40, 44, 46, 47, 49, 58], "your": [2, 5, 11, 14, 15, 16, 18, 21, 24, 26, 28, 29, 31, 32, 33, 34, 35, 36, 37, 40, 41, 43, 44, 47, 48, 49, 59], "your_api_token_her": 4, "your_current_token": 6, "your_password": 11, "your_redis_password": 18, "your_secure_password": [6, 58], "your_usernam": 21, "your_webhook_secret_kei": 58, "yourcompani": 40, "z": 6, "zero": [2, 5, 8, 10, 19, 20, 22, 24, 37, 48, 50, 52, 57], "zod": 22, "zodresolv": 42, "zoom": [0, 44], "zustand": 22, "\u00b2": [33, 56]}, "titles": ["Documentation Enhancement Summary", "CertRats Sphinx Documentation System", "\ud83e\udd16 AI Study Assistant", "Agent 3: Enterprise Analytics Engine", "Agent 4 API Reference", "AI Study Assistant API", "Authentication &amp; Security", "Core Authentication System", "Career Framework API", "Cost Calculator API", "Enterprise API", "API Reference", "Integration Hub API", "Organization Management", "Progress Tracking API", "Study Session Tracking", "\ud83e\udd16 Unified Intelligence API", "\ud83d\udcdd Changelog", "\u2699\ufe0f Configuration Guide", "AI Models Implementation", "System Architecture", "Contributing Guide", "\ud83d\ude80 Frontend Migration to Next.js 14", "\ud83d\udee0\ufe0f Sphinx Documentation Setup Guide", "\ud83c\udfe2 Enterprise Dashboard", "\ud83d\udee0\ufe0f FAQ Documentation Maintenance Guide", "\ud83c\udf93 Academics &amp; Researchers FAQ", "\ud83d\udd27 System Administrators FAQ", "\ud83d\udd04 Career Changers FAQ", "\ud83c\udfe2 Enterprise Administrators FAQ", "\u2753 Frequently Asked Questions", "\ud83d\udc68\u200d\ud83d\udcbc Managers &amp; Team Leads FAQ", "\ud83d\udc69\u200d\ud83d\udcbc Working Professionals FAQ", "\ud83d\udc68\u200d\ud83c\udf93 Students &amp; Learners FAQ", "\ud83d\udcda Corporate Training Managers FAQ", "\ud83d\udc68\u200d\ud83d\udcbc Administrator Guide", "Agent 4 User Guide", "\ud83e\udd16 AI Features Guide", "\ud83d\udd04 Application Flows &amp; Architecture", "\ud83d\udd04 Next.js Application Flows", "Complete Platform Integration Guide", "\ud83c\udfe2 Enterprise Guide", "\ud83c\udfa8 Frontend Implementation Guide", "\ud83d\ude80 Next.js 14 Frontend User Guide", "\ud83d\udcf1 Mobile Guide", "\ud83e\uddea Comprehensive Testing Guide", "\ud83d\udcca Unified Dashboard User Guide", "\ud83c\udfaf Complete User Guide", "\ud83d\udee1\ufe0f CertPathFinder Documentation", "Installation Guide", "\ud83d\ude80 Platform Overview", "PRD Documentation System", "\ud83c\udfd7\ufe0f Agent 1: Core Platform Engine", "\ud83e\udd16 Agent 2: AI Study Assistant", "\ud83c\udfe2 Agent 3: Enterprise &amp; Analytics Engine", "\ud83d\udcb0 Agent 4: Career &amp; Cost Intelligence", "\ud83c\udf10 Agent 5: Marketplace &amp; Integration Hub", "\ud83d\udccb Product Requirements Documents (PRDs)", "\ud83d\ude80 Quick Start Guide", "\ud83d\udd17 Unified Platform Integration"], "titleterms": {"0": 17, "01": 17, "05": 17, "1": [0, 17, 52], "10": 17, "11": 17, "12": 17, "14": [22, 43], "15": 17, "16": 17, "2": [0, 17, 53], "2023": 17, "2024": 17, "24": 58, "3": [0, 3, 17, 40, 54], "4": [0, 4, 17, 36, 40, 55], "5": [0, 17, 47, 56, 58], "7": 58, "For": 59, "It": 51, "On": [12, 20, 37], "_static": 0, "academ": 26, "access": [0, 6, 7, 35, 45, 46], "account": 7, "achiev": [14, 46, 54, 55, 57], "across": 30, "action": [46, 58], "activ": 7, "ad": 25, "adapt": 37, "add": 13, "addit": [16, 33, 59], "admin": 35, "administr": [13, 27, 29, 35], "advanc": [1, 2, 23, 24, 32, 36, 37, 43, 47, 48, 58], "agent": [3, 4, 17, 36, 40, 51, 52, 53, 54, 55, 56, 57], "ai": [2, 5, 11, 17, 18, 19, 20, 37, 38, 47, 48, 50, 53, 57, 58, 59], "all": 30, "also": [5, 6, 8, 9, 10, 12, 14, 19, 20, 21], "analys": [8, 19], "analysi": [3, 4, 5, 8, 9, 14, 16, 36, 47, 52, 58], "analyt": [1, 3, 5, 10, 13, 14, 15, 17, 18, 23, 24, 29, 35, 37, 40, 44, 45, 47, 48, 50, 51, 54, 57, 58, 59], "annual": 25, "api": [1, 4, 5, 6, 8, 9, 10, 11, 12, 14, 16, 20, 35, 40, 48, 58, 59], "app": [42, 44], "applic": [0, 22, 26, 38, 39, 43], "application_flow": 0, "architectur": [0, 1, 19, 20, 22, 24, 38, 40, 41, 42, 45, 48, 50, 55, 57, 59], "area": 32, "ask": [30, 48], "assess": [5, 8], "assign": 10, "assist": [2, 5, 11, 17, 37, 47, 53, 58, 59], "assur": [23, 25], "audit": [10, 13], "authent": [0, 4, 6, 7, 11, 16, 20, 22, 35, 38, 39, 40, 42, 43, 47, 58, 59], "authoris": 20, "autom": [19, 25], "automat": 51, "avail": 12, "backup": 18, "base": [6, 7, 11, 16, 57], "basic": 1, "behav": 45, "benefit": [22, 59], "best": [6, 7, 43, 44, 47], "branch": 21, "budget": [4, 9, 31, 34, 36], "bug": 21, "build": [1, 23, 25, 51], "bulk": 10, "busi": [3, 48], "calcul": [4, 9, 11, 47, 58], "capabl": [44, 53, 54, 56], "career": [4, 8, 11, 17, 28, 32, 33, 36, 40, 47, 50, 55, 57, 58, 59], "cdn": 1, "center": 58, "certif": [9, 15, 38, 39, 42, 43, 47, 58], "certpathfind": [48, 58], "certrat": 1, "changelog": 17, "changer": 28, "check": 16, "checklist": 25, "choos": 30, "client": 11, "code": [1, 11, 20, 21], "collabor": [25, 26], "command": [51, 58], "commit": 57, "common": [11, 30, 46, 51, 58], "commun": 21, "companion": 58, "compar": 9, "complet": [14, 16, 40, 47, 54], "complianc": [10, 13, 27, 41, 54, 57], "compon": [20, 59], "comprehens": [0, 5, 16, 42, 45, 48, 50, 55], "conduct": 21, "configur": [0, 1, 7, 12, 18, 23, 27, 37, 40, 49, 51], "confirm": 7, "connector": 12, "contact": 25, "containeris": 20, "content": [0, 1, 5, 6, 8, 9, 10, 11, 12, 14, 18, 19, 20, 21, 24, 25, 35, 49], "continu": 32, "contribut": [1, 21], "contributor": 25, "control": [6, 7, 25], "core": [5, 7, 9, 11, 17, 20, 51, 52, 57, 59], "corpor": 34, "cost": [3, 4, 9, 11, 47, 55, 58, 59], "coverag": 55, "creat": [0, 9, 10, 13, 14], "cross": [1, 59], "css": 0, "currenc": 9, "curriculum": 26, "custom": [1, 3, 23, 29, 46], "customis": 5, "dai": 58, "dashboard": [0, 3, 11, 16, 22, 24, 35, 36, 38, 39, 41, 42, 43, 46, 47, 58, 59], "data": [0, 3, 12, 19, 20, 35, 38], "databas": [20, 27], "debug": 51, "demand": 8, "depart": 10, "deploy": [1, 20, 22, 23, 29, 40, 41, 55], "design": [0, 39], "detail": [10, 13, 14, 23], "detect": 51, "develop": [1, 8, 20, 21, 22, 28, 31, 32, 34, 39, 43, 48, 51, 52, 53, 57, 58, 59], "devic": [20, 37, 44], "diagram": [0, 1], "difficulti": [5, 19], "directori": 23, "discover": 0, "discoveri": 38, "docker": [1, 49], "document": [0, 1, 11, 21, 23, 25, 48, 51, 57, 58], "dynam": [31, 51], "educ": 32, "effect": 14, "element": 0, "enabl": 7, "encrypt": 19, "end": [15, 45], "endpoint": [5, 6, 7, 16, 59], "engag": 52, "engin": [3, 37, 38, 52, 54, 59], "enhanc": [0, 11, 22, 25], "enrich": 35, "enterpris": [3, 7, 10, 11, 13, 16, 17, 18, 24, 29, 41, 47, 48, 50, 54, 57, 59], "environ": [18, 21, 49], "error": [4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 39, 59], "essenti": 58, "estim": [19, 46], "event": 12, "exampl": [6, 12, 40, 59], "excel": [24, 48, 50], "exchang": 9, "execut": [3, 52, 53, 54, 55, 56], "exist": [25, 28], "experi": [0, 25, 43, 46, 47, 52, 53, 54, 55, 56], "explor": [38, 39, 42, 43, 47, 58], "extern": 25, "factor": 7, "faq": [1, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34], "featur": [1, 2, 7, 13, 15, 17, 21, 22, 23, 36, 37, 43, 44, 47, 48, 51, 53, 54, 55, 56, 58], "feedback": [17, 35], "file": 25, "final": 54, "financi": [33, 50, 57], "first": [0, 2, 19, 48], "flow": [0, 38, 39, 42, 45], "forecast": 3, "format": [11, 25], "foundat": [57, 59], "framework": [8, 11, 45], "frequent": [30, 48], "frontend": [0, 22, 40, 42, 43], "frontend_implement": 0, "futur": [22, 25], "gener": [1, 3, 8, 15], "get": [1, 2, 4, 5, 8, 10, 12, 13, 14, 21, 28, 33, 36, 40, 46, 47, 48, 58, 59], "github": 1, "goal": [14, 15], "googl": 1, "govern": [29, 41], "graph": 1, "guid": [0, 1, 18, 21, 22, 23, 24, 25, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 58], "guidanc": 50, "guidelin": [1, 21, 25], "handl": [4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 39, 59], "health": 16, "help": [21, 30, 46, 58], "high": [8, 20], "histor": 9, "histori": [9, 14, 15], "homepag": [39, 43], "hour": 58, "how": 51, "hub": [11, 12, 50, 56, 58, 59], "i": 58, "immedi": 58, "implement": [0, 2, 19, 24, 42, 52, 53, 54, 55, 56, 57], "import": 10, "improv": [0, 22, 25], "indic": [36, 48, 57, 59], "industri": 2, "init": 0, "initi": 17, "innov": 48, "insight": [15, 37, 46], "instal": [12, 27, 49], "instruct": 26, "integr": [0, 1, 5, 7, 11, 12, 13, 15, 17, 22, 24, 26, 29, 32, 40, 41, 45, 50, 52, 53, 56, 57, 58, 59], "intellig": [3, 4, 8, 16, 17, 36, 40, 46, 50, 55, 57, 58, 59], "interact": [0, 11], "interfac": [43, 58], "intern": 25, "internation": 1, "invit": 13, "isol": 52, "issu": [46, 51], "j": [0, 22, 39, 43], "javascript": 0, "job": [8, 28, 35], "journei": [0, 39], "kei": [0, 1, 46, 57], "keyword": 51, "kpi": [52, 53, 54, 55, 56], "launch": 17, "layer": 20, "lead": 31, "leadership": [31, 48, 50], "learn": [2, 3, 5, 12, 14, 15, 19, 28, 37, 41, 44, 50, 58], "learner": 33, "level": 20, "leverag": 28, "librari": [4, 11], "licenc": 10, "licens": [24, 41], "limit": [4, 6, 11], "list": [10, 13], "live": 3, "lm": 12, "load": 0, "localis": 9, "locat": 51, "log": [10, 13], "login": 7, "machin": 2, "mainten": [1, 23, 25, 35, 40, 51], "manag": [7, 10, 12, 13, 14, 15, 24, 27, 29, 31, 34, 35, 39, 40, 41], "manual": 49, "market": [4, 8, 36, 46, 50, 52, 53, 54, 55, 56], "marketplac": [56, 57, 59], "massiv": 57, "masteri": 58, "member": 13, "mermaid": [0, 1], "messag": 46, "meta": 1, "method": 6, "methodologi": [26, 57], "metric": [1, 3, 15, 16, 24, 46, 48, 51, 52, 53, 54, 55, 56, 57, 59], "mfa": 7, "migrat": 22, "mileston": [46, 57], "minut": [47, 58], "mitig": [52, 53], "ml": 20, "mobil": [0, 11, 24, 38, 43, 44, 46, 47], "model": [2, 5, 9, 19, 52, 53, 54, 55, 56], "modifi": 25, "modul": 11, "monitor": [1, 3, 12, 18, 19, 23, 27, 40, 51], "monthli": 25, "motiv": 46, "multi": [6, 7], "navig": 0, "need": 30, "new": 25, "next": [22, 39, 43, 46, 49, 58], "notif": 44, "offlin": 44, "open": 1, "oper": 34, "opportun": [25, 52, 53, 54, 55, 56], "optim": [0, 3, 4, 18, 36, 39, 42, 44], "optimis": 20, "option": [41, 46], "organ": [0, 3, 13, 25, 35, 40], "organis": [10, 20], "output": 51, "overhaul": 25, "overview": [1, 3, 4, 5, 7, 8, 9, 10, 12, 13, 14, 15, 19, 20, 22, 23, 25, 35, 38, 40, 42, 43, 45, 46, 47, 50, 52, 53, 54, 55, 56, 57, 59], "page": 1, "pagin": 11, "parti": 12, "password": 7, "path": [8, 37, 58], "pathfind": 4, "pattern": 58, "pdf": 1, "perform": [0, 3, 5, 13, 14, 15, 16, 18, 19, 20, 22, 27, 31, 34, 36, 39, 42, 43, 45, 48, 52, 53, 54, 55, 57, 59], "permiss": [6, 7], "person": [15, 37, 46], "pipelin": 19, "plan": [8, 9, 15, 16, 25, 31, 33, 36, 58], "platform": [17, 24, 40, 48, 50, 52, 57, 59], "playwright": 45, "polici": 13, "posit": 50, "post": 4, "power": [2, 37, 50, 58], "practic": [6, 7, 43, 44, 47], "prd": [51, 57], "predict": [3, 5, 37], "predictor": 19, "prerequisit": [1, 21, 49, 51], "preserv": [2, 19], "price": 9, "prioriti": 46, "privaci": [2, 5, 19, 43, 46, 48], "pro": 58, "process": [1, 19, 20, 21, 22, 25], "product": [22, 48, 54, 55, 57], "profession": 32, "profil": [16, 59], "program": 34, "progress": [0, 5, 11, 14, 15, 38, 42, 44, 47, 58], "project": [52, 53, 54, 55, 56], "proposit": 48, "provid": 12, "pull": 21, "push": 44, "pwa": [38, 44], "python": 6, "qualiti": [22, 23, 25, 53], "quarterli": 25, "question": [25, 30, 33, 48], "quick": [1, 23, 43, 46, 48, 49, 58, 59], "rate": [4, 6, 9, 11], "rbac": 6, "readi": [54, 55], "real": [3, 5, 24, 38], "recent": 46, "recommend": [5, 15, 19, 38, 46, 49, 59], "recoveri": 18, "refer": [1, 4, 11, 48], "refresh": [6, 7], "region": 9, "regist": 12, "registr": 7, "regular": [1, 25, 51], "render": 0, "report": [3, 10, 13, 21, 29, 45], "request": 21, "requir": [21, 48, 52, 53, 55, 56, 57], "research": 26, "reset": 7, "resourc": [16, 25, 26, 28, 29, 32, 33, 34, 36, 43, 59], "respons": [0, 11, 39, 55], "restructuredtext": 1, "revenu": [52, 53, 54, 55, 56], "review": [1, 21, 25], "revok": 7, "revolutionari": [37, 48, 50], "risk": [52, 53], "roadmap": [8, 52, 53, 56], "roi": [3, 4, 16, 34, 36, 46, 47, 58], "role": [4, 6, 7, 8, 13, 16], "rst": 0, "salari": [4, 8, 16], "scalabl": 55, "scenario": 9, "score": 46, "script": [25, 51], "sdk": [4, 11], "search": [0, 1, 28, 38], "section": [1, 11, 25, 46, 51], "secur": [5, 6, 7, 11, 13, 19, 20, 27, 29, 38, 43, 44, 45, 47, 57, 58, 59], "see": [5, 6, 8, 9, 10, 12, 14, 19, 20, 21], "segment": [53, 54, 55, 56], "seo": 0, "sequenc": 0, "servic": [16, 20, 59], "session": [7, 14, 15, 40], "set": [13, 21, 46], "setup": [23, 27, 40, 58], "sign": 12, "singl": 12, "skill": [5, 8, 28], "smart": 58, "specialis": 32, "specif": 44, "sphinx": [0, 1, 23, 51], "sso": [7, 12], "standard": [21, 25, 51], "start": [1, 2, 14, 15, 21, 28, 33, 36, 40, 43, 46, 47, 48, 49, 58, 59], "state": 39, "statu": [12, 51, 54, 55], "step": [46, 49, 58], "still": 30, "stori": 2, "strateg": 31, "strategi": [21, 28, 33, 37, 42, 44, 45, 52, 53, 56], "structur": [0, 1, 22, 23, 25, 51], "student": 33, "studi": [2, 5, 11, 14, 15, 17, 33, 37, 40, 47, 53, 58, 59], "style": [0, 1, 5, 19, 23], "success": [2, 3, 11, 24, 52, 53, 54, 55, 56, 57, 58], "summari": [0, 46, 52, 53, 54, 55, 56, 57], "support": [1, 4, 17, 25, 36, 40, 41, 43, 47, 51, 58], "sync": 12, "synchron": 38, "synchronis": 12, "system": [0, 1, 7, 12, 14, 20, 27, 35, 38, 43, 47, 51, 57, 58], "tabl": [5, 6, 8, 9, 10, 11, 12, 14, 18, 19, 20, 21, 24, 48, 49], "tag": 1, "target": [52, 53, 54, 56], "task": [1, 25, 51], "taxonomi": 11, "teach": 26, "team": [3, 13, 31, 34], "technic": [0, 2, 22, 25, 28, 31, 32, 33, 52, 53, 54, 55, 56], "techniqu": [5, 19], "tenant": 6, "test": [0, 6, 12, 21, 22, 25, 42, 45, 52, 53, 55], "testing_guid": 0, "third": 12, "time": [3, 5, 24, 38, 55], "timer": 11, "tip": [43, 44, 47, 58], "token": [6, 7], "tool": [25, 27], "topic": [19, 30], "tour": 58, "track": [5, 9, 11, 14, 15, 19, 34, 38, 47, 51, 58], "traefik": 59, "train": [5, 9, 16, 19, 34], "transit": [4, 8, 11, 47, 58], "translat": 35, "trend": 3, "troubleshoot": [36, 40, 43, 44, 46, 47, 49, 51], "type": [6, 21, 25, 30, 46], "ui": 22, "understand": 46, "unifi": [16, 17, 46, 48, 59], "unit": 45, "up": 21, "upcom": [17, 46], "updat": [0, 1, 7, 10, 13, 14, 15, 25, 51, 57], "url": [11, 16], "usag": [10, 35, 51], "user": [0, 1, 7, 10, 13, 14, 16, 24, 25, 29, 30, 35, 36, 39, 41, 43, 46, 47, 48, 52, 53, 54, 55, 56, 59], "user_guid": 0, "ux": 22, "valid": 25, "valu": 48, "verif": 49, "verifi": 7, "version": [17, 25], "view": 1, "visual": 3, "web": [42, 44, 58], "webhook": [11, 12], "welcom": 36, "what": 58, "work": [32, 51], "workflow": [20, 21, 39, 40, 52, 53, 58], "workplac": 32, "write": [1, 23, 25], "your": [30, 46, 58]}})