/* Custom CSS for CertPathFinder Documentation */

/* Brand colors */
:root {
    --primary-color: #2980B9;
    --secondary-color: #27AE60;
    --accent-color: #E74C3C;
    --dark-bg: #2C3E50;
    --light-bg: #ECF0F1;
}

/* Header customization */
.wy-nav-top {
    background-color: var(--primary-color) !important;
}

.wy-nav-top a {
    color: white !important;
    font-weight: bold;
}

/* Sidebar customization */
.wy-nav-side {
    background-color: var(--dark-bg) !important;
}

.wy-menu-vertical a {
    color: #BDC3C7 !important;
}

.wy-menu-vertical a:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

.wy-menu-vertical li.current a {
    background-color: var(--secondary-color) !important;
    color: white !important;
}

/* Content area */
.wy-nav-content {
    background-color: white;
}

/* Code blocks */
.highlight {
    background-color: #F8F9FA !important;
    border: 1px solid #E9ECEF;
    border-radius: 4px;
    padding: 1rem;
}

.highlight pre {
    background-color: transparent !important;
    border: none !important;
    padding: 0 !important;
}

/* Admonitions */
.admonition {
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
}

.admonition.note {
    border-left: 4px solid var(--primary-color);
    background-color: #EBF3FD;
}

.admonition.warning {
    border-left: 4px solid #F39C12;
    background-color: #FEF9E7;
}

.admonition.danger {
    border-left: 4px solid var(--accent-color);
    background-color: #FDEDEC;
}

.admonition.tip {
    border-left: 4px solid var(--secondary-color);
    background-color: #EAFAF1;
}

/* Tables */
.wy-table-responsive table td,
.wy-table-responsive table th {
    white-space: normal !important;
}

table.docutils {
    border-collapse: collapse;
    border-spacing: 0;
    empty-cells: show;
    margin-bottom: 24px;
    border: 1px solid #E1E4E5;
}

table.docutils th,
table.docutils td {
    border: 1px solid #E1E4E5;
    padding: 8px 16px;
}

table.docutils th {
    background-color: var(--light-bg);
    font-weight: bold;
}

/* Links */
a {
    color: var(--primary-color) !important;
}

a:hover {
    color: var(--secondary-color) !important;
}

/* Buttons */
.btn {
    border-radius: 4px;
    padding: 8px 16px;
    text-decoration: none;
    display: inline-block;
    margin: 4px 2px;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border: 1px solid var(--secondary-color);
}

/* Feature badges */
.feature-badge {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    margin: 2px;
}

.feature-badge.new {
    background-color: var(--accent-color);
}

.feature-badge.beta {
    background-color: #F39C12;
}

/* API endpoint styling */
.http-method {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 0.8em;
    margin-right: 8px;
}

.http-method.get {
    background-color: #27AE60;
    color: white;
}

.http-method.post {
    background-color: #3498DB;
    color: white;
}

.http-method.put {
    background-color: #F39C12;
    color: white;
}

.http-method.delete {
    background-color: #E74C3C;
    color: white;
}

/* Statistics boxes */
.stats-box {
    background-color: var(--light-bg);
    border: 1px solid #BDC3C7;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
    text-align: center;
}

.stats-number {
    font-size: 2em;
    font-weight: bold;
    color: var(--primary-color);
}

.stats-label {
    font-size: 0.9em;
    color: #7F8C8D;
    margin-top: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
    .wy-nav-content {
        margin-left: 0;
    }
    
    .wy-nav-side {
        left: -300px;
    }
    
    .wy-nav-side.shift {
        left: 0;
    }
}

/* Mermaid diagram integration */
.mermaid-diagram {
    margin: 1.5rem 0;
    border: 1px solid #E1E4E5;
    border-radius: 4px;
    background: white;
}

.mermaid-diagram.rst-mermaid {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* Enhanced code blocks for better diagram integration */
.highlight-mermaid {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
}

/* Flow diagram styling integration */
.section h1, .section h2, .section h3 {
    color: var(--dark-bg);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

/* Enhanced navigation for diagram-heavy pages */
.wy-menu-vertical li.toctree-l2 a {
    padding-left: 2.4em;
    font-size: 0.9em;
}

.wy-menu-vertical li.toctree-l3 a {
    padding-left: 3.6em;
    font-size: 0.85em;
}

/* Diagram section styling */
.diagram-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
    border-left: 4px solid var(--primary-color);
}

.diagram-section h3 {
    color: var(--primary-color);
    margin-top: 0;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
}

/* Enhanced tooltips for interactive elements */
.tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
    border-bottom: 1px dotted var(--primary-color);
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: var(--dark-bg);
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.85em;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }

    .wy-nav-content {
        margin-left: 0 !important;
    }

    .mermaid-diagram {
        break-inside: avoid;
        border: 1px solid #000;
        background: white !important;
    }
}
