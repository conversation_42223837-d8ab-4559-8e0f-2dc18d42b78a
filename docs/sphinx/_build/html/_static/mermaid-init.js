/**
 * Mermaid initialization for Sphinx documentation
 * Automatically renders Mermaid diagrams in code blocks
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Mermaid with configuration
    mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        themeVariables: {
            primaryColor: '#2980B9',
            primaryTextColor: '#2C3E50',
            primaryBorderColor: '#3498DB',
            lineColor: '#34495E',
            secondaryColor: '#ECF0F1',
            tertiaryColor: '#F8F9FA',
            background: '#FFFFFF',
            mainBkg: '#FFFFFF',
            secondBkg: '#F8F9FA',
            tertiaryBkg: '#ECF0F1'
        },
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        },
        sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
        },
        gantt: {
            titleTopMargin: 25,
            barHeight: 20,
            fontFamily: '"Open Sans", sans-serif',
            fontSize: 11,
            fontWeight: 'normal',
            gridLineStartPadding: 35,
            bottomPadding: 5,
            leftPadding: 75,
            topPadding: 50,
            rightPadding: 25
        }
    });

    // Function to render Mermaid diagrams
    function renderMermaidDiagrams() {
        // Find all code blocks with mermaid class
        const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid, .highlight-mermaid pre, div.mermaid');
        
        mermaidBlocks.forEach((block, index) => {
            // Create a unique ID for each diagram
            const diagramId = `mermaid-diagram-${index}`;
            
            // Get the mermaid code
            let mermaidCode;
            if (block.tagName === 'CODE') {
                mermaidCode = block.textContent;
                // Replace the code block with a div for mermaid
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid-diagram';
                mermaidDiv.id = diagramId;
                mermaidDiv.textContent = mermaidCode;
                block.parentNode.parentNode.replaceChild(mermaidDiv, block.parentNode);
            } else if (block.classList.contains('mermaid')) {
                mermaidCode = block.textContent;
                block.id = diagramId;
                block.className = 'mermaid-diagram';
            } else {
                // Handle highlight-mermaid pre blocks
                const codeElement = block.querySelector('code');
                if (codeElement) {
                    mermaidCode = codeElement.textContent;
                    const mermaidDiv = document.createElement('div');
                    mermaidDiv.className = 'mermaid-diagram';
                    mermaidDiv.id = diagramId;
                    mermaidDiv.textContent = mermaidCode;
                    block.parentNode.replaceChild(mermaidDiv, block);
                }
            }
            
            // Render the diagram
            if (mermaidCode && mermaidCode.trim()) {
                try {
                    mermaid.render(diagramId + '-svg', mermaidCode.trim(), (svgCode) => {
                        const diagramElement = document.getElementById(diagramId);
                        if (diagramElement) {
                            diagramElement.innerHTML = svgCode;
                            diagramElement.classList.add('mermaid-rendered');
                        }
                    });
                } catch (error) {
                    console.error('Error rendering Mermaid diagram:', error);
                    const diagramElement = document.getElementById(diagramId);
                    if (diagramElement) {
                        diagramElement.innerHTML = `<div class="mermaid-error">Error rendering diagram: ${error.message}</div>`;
                        diagramElement.classList.add('mermaid-error-container');
                    }
                }
            }
        });
    }

    // Function to handle RST mermaid directives
    function handleRSTMermaidDirectives() {
        // Look for RST mermaid directives (.. mermaid::)
        const mermaidDirectives = document.querySelectorAll('.highlight-mermaid');
        
        mermaidDirectives.forEach((directive, index) => {
            const pre = directive.querySelector('pre');
            if (pre) {
                const code = pre.textContent.trim();
                if (code) {
                    const diagramId = `rst-mermaid-${index}`;
                    const mermaidDiv = document.createElement('div');
                    mermaidDiv.className = 'mermaid-diagram rst-mermaid';
                    mermaidDiv.id = diagramId;
                    
                    try {
                        mermaid.render(diagramId + '-svg', code, (svgCode) => {
                            mermaidDiv.innerHTML = svgCode;
                            mermaidDiv.classList.add('mermaid-rendered');
                        });
                    } catch (error) {
                        console.error('Error rendering RST Mermaid diagram:', error);
                        mermaidDiv.innerHTML = `<div class="mermaid-error">Error rendering diagram: ${error.message}</div>`;
                        mermaidDiv.classList.add('mermaid-error-container');
                    }
                    
                    directive.parentNode.replaceChild(mermaidDiv, directive);
                }
            }
        });
    }

    // Function to add zoom functionality to diagrams
    function addZoomFunctionality() {
        const diagrams = document.querySelectorAll('.mermaid-diagram.mermaid-rendered');
        
        diagrams.forEach(diagram => {
            const svg = diagram.querySelector('svg');
            if (svg) {
                // Add zoom controls
                const zoomContainer = document.createElement('div');
                zoomContainer.className = 'mermaid-zoom-controls';
                
                const zoomInBtn = document.createElement('button');
                zoomInBtn.textContent = '+';
                zoomInBtn.className = 'zoom-btn zoom-in';
                zoomInBtn.title = 'Zoom In';
                
                const zoomOutBtn = document.createElement('button');
                zoomOutBtn.textContent = '−';
                zoomOutBtn.className = 'zoom-btn zoom-out';
                zoomOutBtn.title = 'Zoom Out';
                
                const resetBtn = document.createElement('button');
                resetBtn.textContent = '⌂';
                resetBtn.className = 'zoom-btn zoom-reset';
                resetBtn.title = 'Reset Zoom';
                
                zoomContainer.appendChild(zoomInBtn);
                zoomContainer.appendChild(zoomOutBtn);
                zoomContainer.appendChild(resetBtn);
                
                diagram.appendChild(zoomContainer);
                
                // Add zoom functionality
                let scale = 1;
                const maxScale = 3;
                const minScale = 0.5;
                const scaleStep = 0.2;
                
                function updateScale() {
                    svg.style.transform = `scale(${scale})`;
                    svg.style.transformOrigin = 'top left';
                }
                
                zoomInBtn.addEventListener('click', () => {
                    if (scale < maxScale) {
                        scale += scaleStep;
                        updateScale();
                    }
                });
                
                zoomOutBtn.addEventListener('click', () => {
                    if (scale > minScale) {
                        scale -= scaleStep;
                        updateScale();
                    }
                });
                
                resetBtn.addEventListener('click', () => {
                    scale = 1;
                    updateScale();
                });
                
                // Add wheel zoom
                diagram.addEventListener('wheel', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        const delta = e.deltaY > 0 ? -scaleStep : scaleStep;
                        const newScale = Math.max(minScale, Math.min(maxScale, scale + delta));
                        if (newScale !== scale) {
                            scale = newScale;
                            updateScale();
                        }
                    }
                });
            }
        });
    }

    // Function to add fullscreen functionality
    function addFullscreenFunctionality() {
        const diagrams = document.querySelectorAll('.mermaid-diagram.mermaid-rendered');
        
        diagrams.forEach(diagram => {
            const fullscreenBtn = document.createElement('button');
            fullscreenBtn.textContent = '⛶';
            fullscreenBtn.className = 'fullscreen-btn';
            fullscreenBtn.title = 'View Fullscreen';
            
            diagram.appendChild(fullscreenBtn);
            
            fullscreenBtn.addEventListener('click', () => {
                if (diagram.requestFullscreen) {
                    diagram.requestFullscreen();
                } else if (diagram.webkitRequestFullscreen) {
                    diagram.webkitRequestFullscreen();
                } else if (diagram.msRequestFullscreen) {
                    diagram.msRequestFullscreen();
                }
            });
        });
    }

    // Wait for mermaid to be available
    if (typeof mermaid !== 'undefined') {
        // Render diagrams
        renderMermaidDiagrams();
        handleRSTMermaidDirectives();
        
        // Add enhanced functionality after a short delay
        setTimeout(() => {
            addZoomFunctionality();
            addFullscreenFunctionality();
        }, 500);
    } else {
        console.warn('Mermaid library not loaded');
    }

    // Handle dynamic content loading (for SPAs)
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if new mermaid diagrams were added
                const hasNewMermaid = Array.from(mutation.addedNodes).some(node => {
                    return node.nodeType === Node.ELEMENT_NODE && 
                           (node.classList.contains('highlight-mermaid') || 
                            node.querySelector && node.querySelector('.highlight-mermaid, code.language-mermaid'));
                });
                
                if (hasNewMermaid) {
                    setTimeout(() => {
                        renderMermaidDiagrams();
                        handleRSTMermaidDirectives();
                        addZoomFunctionality();
                        addFullscreenFunctionality();
                    }, 100);
                }
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
