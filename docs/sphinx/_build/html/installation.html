<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Installation Guide &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/installation.html" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=c913fcab"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="🚀 Quick Start Guide" href="quickstart.html" />
    <link rel="prev" title="🛡️ CertPathFinder Documentation" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="#quick-start-with-docker-recommended">Quick Start with Docker (Recommended)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#manual-installation">Manual Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="#environment-configuration">Environment Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="#verification">Verification</a></li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Installation Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/installation.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="installation-guide">
<h1>Installation Guide<a class="headerlink" href="#installation-guide" title="Link to this heading"></a></h1>
<p>This guide will help you install and set up CertPathFinder on your system. CertPathFinder supports multiple deployment methods to suit different environments and requirements.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#prerequisites" id="id1">Prerequisites</a></p></li>
<li><p><a class="reference internal" href="#quick-start-with-docker-recommended" id="id2">Quick Start with Docker (Recommended)</a></p></li>
<li><p><a class="reference internal" href="#manual-installation" id="id3">Manual Installation</a></p></li>
<li><p><a class="reference internal" href="#environment-configuration" id="id4">Environment Configuration</a></p></li>
<li><p><a class="reference internal" href="#verification" id="id5">Verification</a></p></li>
<li><p><a class="reference internal" href="#troubleshooting" id="id6">Troubleshooting</a></p></li>
<li><p><a class="reference internal" href="#next-steps" id="id7">Next Steps</a></p></li>
</ul>
</nav>
<section id="prerequisites">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Prerequisites</a><a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h2>
<p>Before installing CertPathFinder, ensure you have the following prerequisites:</p>
<p><strong>System Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Python 3.10+</strong> - Required for running the application</p></li>
<li><p><strong>PostgreSQL 13+</strong> - Primary database (SQLite supported for development)</p></li>
<li><p><strong>Redis 6+</strong> - For caching and background tasks</p></li>
<li><p><strong>Docker &amp; Docker Compose</strong> - For containerised deployment (recommended)</p></li>
<li><p><strong>Git</strong> - For cloning the repository</p></li>
</ul>
<p><strong>Hardware Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Minimum</strong>: 2GB RAM, 2 CPU cores, 10GB storage</p></li>
<li><p><strong>Recommended</strong>: 4GB RAM, 4 CPU cores, 20GB storage</p></li>
<li><p><strong>Production</strong>: 8GB+ RAM, 8+ CPU cores, 50GB+ storage</p></li>
</ul>
</section>
<section id="quick-start-with-docker-recommended">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Quick Start with Docker (Recommended)</a><a class="headerlink" href="#quick-start-with-docker-recommended" title="Link to this heading"></a></h2>
<p>The fastest way to get CertPathFinder running is using Docker Compose. This method provides a complete, isolated environment with all dependencies.</p>
<p><strong>Standard Deployment:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/replit-CertPathFinder.git
<span class="nb">cd</span><span class="w"> </span>replit-CertPathFinder

<span class="c1"># Copy environment configuration</span>
cp<span class="w"> </span>.env.example<span class="w"> </span>.env
<span class="c1"># Edit .env with your settings (optional for development)</span>

<span class="c1"># Start all services</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Verify services are running</span>
docker-compose<span class="w"> </span>ps

<span class="c1"># View logs if needed</span>
docker-compose<span class="w"> </span>logs<span class="w"> </span>-f
</pre></div>
</div>
<p><strong>Access Points:</strong></p>
<ul class="simple">
<li><p><strong>API Documentation</strong>: <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a></p></li>
<li><p><strong>Web Application</strong>: <a class="reference external" href="http://localhost:8501">http://localhost:8501</a></p></li>
<li><p><strong>API Base URL</strong>: <a class="reference external" href="http://localhost:8000/api/v1">http://localhost:8000/api/v1</a></p></li>
</ul>
<p><strong>Traefik Deployment (Advanced):</strong></p>
<p>For production-like environments with automatic HTTPS:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Use Traefik configuration</span>
docker-compose<span class="w"> </span>-f<span class="w"> </span>docker-compose.traefik.yml<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Access via domain names</span>
<span class="c1"># API: https://api.certpathfinder.docker.localhost</span>
<span class="c1"># Web: https://app.certpathfinder.docker.localhost</span>
</pre></div>
</div>
</section>
<section id="manual-installation">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">Manual Installation</a><a class="headerlink" href="#manual-installation" title="Link to this heading"></a></h2>
<p>For development or custom deployments, you can install manually:</p>
<ol class="arabic simple">
<li><p><strong>Clone the Repository</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/replit-CertPathFinder.git
<span class="nb">cd</span><span class="w"> </span>replit-CertPathFinder
</pre></div>
</div>
<ol class="arabic simple" start="2">
<li><p><strong>Create Virtual Environment</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate<span class="w">  </span><span class="c1"># On Windows: venv\Scripts\activate</span>
</pre></div>
</div>
<ol class="arabic simple" start="3">
<li><p><strong>Install Dependencies</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements.txt
</pre></div>
</div>
<ol class="arabic simple" start="4">
<li><p><strong>Set Up Database</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># For PostgreSQL</span>
createdb<span class="w"> </span>certpathfinder

<span class="c1"># For SQLite (development)</span>
<span class="c1"># Database will be created automatically</span>
</pre></div>
</div>
<ol class="arabic simple" start="5">
<li><p><strong>Configure Environment</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>cp<span class="w"> </span>.env.example<span class="w"> </span>.env
<span class="c1"># Edit .env with your configuration</span>
</pre></div>
</div>
<ol class="arabic simple" start="6">
<li><p><strong>Run Database Migrations</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head
</pre></div>
</div>
<ol class="arabic simple" start="7">
<li><p><strong>Start the Services</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start FastAPI backend</span>
python<span class="w"> </span>run_api.py

<span class="c1"># Start Streamlit frontend (in another terminal)</span>
streamlit<span class="w"> </span>run<span class="w"> </span>main.py
</pre></div>
</div>
</section>
<section id="environment-configuration">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Environment Configuration</a><a class="headerlink" href="#environment-configuration" title="Link to this heading"></a></h2>
<p>Create a <cite>.env</cite> file with the following configuration:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Database Configuration</span>
<span class="nv">DATABASE_URL</span><span class="o">=</span>postgresql://user:password@localhost:5432/certpathfinder
<span class="c1"># Or for SQLite: DATABASE_URL=sqlite:///./certpathfinder.db</span>

<span class="c1"># Redis Configuration</span>
<span class="nv">REDIS_URL</span><span class="o">=</span>redis://localhost:6379

<span class="c1"># Security</span>
<span class="nv">SECRET_KEY</span><span class="o">=</span>your-secret-key-here
<span class="nv">ALGORITHM</span><span class="o">=</span>HS256
<span class="nv">ACCESS_TOKEN_EXPIRE_MINUTES</span><span class="o">=</span><span class="m">30</span>

<span class="c1"># API Configuration</span>
<span class="nv">API_V1_STR</span><span class="o">=</span>/api/v1
<span class="nv">PROJECT_NAME</span><span class="o">=</span>CertPathFinder

<span class="c1"># External APIs (optional)</span>
<span class="nv">OPENAI_API_KEY</span><span class="o">=</span>your-openai-key
<span class="nv">ANTHROPIC_API_KEY</span><span class="o">=</span>your-anthropic-key

<span class="c1"># Email Configuration (optional)</span>
<span class="nv">SMTP_TLS</span><span class="o">=</span>True
<span class="nv">SMTP_PORT</span><span class="o">=</span><span class="m">587</span>
<span class="nv">SMTP_HOST</span><span class="o">=</span>smtp.gmail.com
<span class="nv">SMTP_USER</span><span class="o">=</span><EMAIL>
<span class="nv">SMTP_PASSWORD</span><span class="o">=</span>your-app-password
</pre></div>
</div>
</section>
<section id="verification">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Verification</a><a class="headerlink" href="#verification" title="Link to this heading"></a></h2>
<p>After installation, verify that everything is working:</p>
<ol class="arabic simple">
<li><p><strong>Check API Health</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8000/health
</pre></div>
</div>
<ol class="arabic" start="2">
<li><p><strong>Access API Documentation</strong></p>
<p>Visit <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a> for interactive API documentation</p>
</li>
<li><p><strong>Access Frontend</strong></p>
<p>Visit <a class="reference external" href="http://localhost:8501">http://localhost:8501</a> for the Streamlit interface</p>
</li>
<li><p><strong>Run Tests</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pytest<span class="w"> </span>tests/
</pre></div>
</div>
</section>
<section id="troubleshooting">
<h2><a class="toc-backref" href="#id6" role="doc-backlink">Troubleshooting</a><a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<p><strong>Common Issues:</strong></p>
<ul class="simple">
<li><p><strong>Database Connection Error</strong>: Ensure PostgreSQL is running and credentials are correct</p></li>
<li><p><strong>Redis Connection Error</strong>: Ensure Redis server is running</p></li>
<li><p><strong>Port Already in Use</strong>: Change ports in configuration or stop conflicting services</p></li>
<li><p><strong>Import Errors</strong>: Ensure all dependencies are installed in the virtual environment</p></li>
</ul>
<p><strong>Getting Help:</strong></p>
<ul class="simple">
<li><p>Check the logs for detailed error messages</p></li>
<li><p>Review the configuration in <cite>.env</cite> file</p></li>
<li><p>Ensure all prerequisites are properly installed</p></li>
<li><p>Consult the development documentation for advanced setup</p></li>
</ul>
</section>
<section id="next-steps">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">Next Steps</a><a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After successful installation:</p>
<ul class="simple">
<li><p>Read the <a class="reference internal" href="quickstart.html"><span class="doc">🚀 Quick Start Guide</span></a> guide for basic usage</p></li>
<li><p>Review the <a class="reference internal" href="configuration.html"><span class="doc">⚙️ Configuration Guide</span></a> for advanced settings</p></li>
<li><p>Explore the <a class="reference internal" href="api/index.html"><span class="doc">API Reference</span></a> for API documentation</p></li>
<li><p>Check out <a class="reference internal" href="guides/user_guide.html"><span class="doc">🎯 Complete User Guide</span></a> for detailed usage instructions</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="🛡️ CertPathFinder Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="quickstart.html" class="btn btn-neutral float-right" title="🚀 Quick Start Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>