<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Enterprise API &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/enterprise.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Cost Calculator API" href="cost_calculator.html" />
    <link rel="prev" title="AI Study Assistant API" href="ai_assistant.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Enterprise API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#organisation-management">Organisation Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-organisation">Create Organisation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#get-organisation-details">Get Organisation Details</a></li>
<li class="toctree-l3"><a class="reference internal" href="#update-organisation">Update Organisation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#department-management">Department Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-department">Create Department</a></li>
<li class="toctree-l3"><a class="reference internal" href="#list-departments">List Departments</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#user-management">User Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-enterprise-user">Create Enterprise User</a></li>
<li class="toctree-l3"><a class="reference internal" href="#bulk-user-import">Bulk User Import</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#analytics-reporting">Analytics &amp; Reporting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#organisation-analytics">Organisation Analytics</a></li>
<li class="toctree-l3"><a class="reference internal" href="#department-analytics">Department Analytics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#licence-management">Licence Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#licence-usage-overview">Licence Usage Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="#assign-licences">Assign Licences</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#compliance-audit">Compliance &amp; Audit</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#compliance-report">Compliance Report</a></li>
<li class="toctree-l3"><a class="reference internal" href="#audit-log">Audit Log</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Enterprise API</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/enterprise.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="enterprise-api">
<h1>Enterprise API<a class="headerlink" href="#enterprise-api" title="Link to this heading"></a></h1>
<p>The Enterprise API provides comprehensive organisational management capabilities for multi-tenant deployments. It supports unlimited organisations with complete data isolation and advanced analytics.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id1">Overview</a></p></li>
<li><p><a class="reference internal" href="#organisation-management" id="id2">Organisation Management</a></p>
<ul>
<li><p><a class="reference internal" href="#create-organisation" id="id3">Create Organisation</a></p></li>
<li><p><a class="reference internal" href="#get-organisation-details" id="id4">Get Organisation Details</a></p></li>
<li><p><a class="reference internal" href="#update-organisation" id="id5">Update Organisation</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#department-management" id="id6">Department Management</a></p>
<ul>
<li><p><a class="reference internal" href="#create-department" id="id7">Create Department</a></p></li>
<li><p><a class="reference internal" href="#list-departments" id="id8">List Departments</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#user-management" id="id9">User Management</a></p>
<ul>
<li><p><a class="reference internal" href="#create-enterprise-user" id="id10">Create Enterprise User</a></p></li>
<li><p><a class="reference internal" href="#bulk-user-import" id="id11">Bulk User Import</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#analytics-reporting" id="id12">Analytics &amp; Reporting</a></p>
<ul>
<li><p><a class="reference internal" href="#organisation-analytics" id="id13">Organisation Analytics</a></p></li>
<li><p><a class="reference internal" href="#department-analytics" id="id14">Department Analytics</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#licence-management" id="id15">Licence Management</a></p>
<ul>
<li><p><a class="reference internal" href="#licence-usage-overview" id="id16">Licence Usage Overview</a></p></li>
<li><p><a class="reference internal" href="#assign-licences" id="id17">Assign Licences</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#compliance-audit" id="id18">Compliance &amp; Audit</a></p>
<ul>
<li><p><a class="reference internal" href="#compliance-report" id="id19">Compliance Report</a></p></li>
<li><p><a class="reference internal" href="#audit-log" id="id20">Audit Log</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-handling" id="id21">Error Handling</a></p></li>
<li><p><a class="reference internal" href="#see-also" id="id22">See Also</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Enterprise features include:</p>
<ul class="simple">
<li><p><strong>Multi-tenant Architecture</strong> - Complete data isolation between organisations</p></li>
<li><p><strong>Role-based Access Control</strong> - Six hierarchical permission levels</p></li>
<li><p><strong>Organisation Management</strong> - Department structure and user administration</p></li>
<li><p><strong>Advanced Analytics</strong> - Real-time insights and executive reporting</p></li>
<li><p><strong>Licence Management</strong> - Usage tracking and optimisation</p></li>
<li><p><strong>Compliance Reporting</strong> - Automated regulatory compliance</p></li>
</ul>
<p>All enterprise endpoints require appropriate administrative permissions.</p>
</section>
<section id="organisation-management">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Organisation Management</a><a class="headerlink" href="#organisation-management" title="Link to this heading"></a></h2>
<section id="create-organisation">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">Create Organisation</a><a class="headerlink" href="#create-organisation" title="Link to this heading"></a></h3>
<p>Create a new organisation with initial configuration.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/enterprise/organisations</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>

<span class="err">{</span>
<span class="err">  &quot;name&quot;: &quot;Acme Corporation&quot;,</span>
<span class="err">  &quot;domain&quot;: &quot;acme.com&quot;,</span>
<span class="err">  &quot;industry&quot;: &quot;Financial Services&quot;,</span>
<span class="err">  &quot;size&quot;: &quot;large&quot;,</span>
<span class="err">  &quot;country&quot;: &quot;United Kingdom&quot;,</span>
<span class="err">  &quot;settings&quot;: {</span>
<span class="err">    &quot;enable_sso&quot;: true,</span>
<span class="err">    &quot;require_2fa&quot;: true,</span>
<span class="err">    &quot;data_retention_days&quot;: 2555,</span>
<span class="err">    &quot;allowed_domains&quot;: [&quot;acme.com&quot;, &quot;acme.co.uk&quot;]</span>
<span class="err">  },</span>
<span class="err">  &quot;billing&quot;: {</span>
<span class="err">    &quot;plan&quot;: &quot;enterprise&quot;,</span>
<span class="err">    &quot;max_users&quot;: 1000,</span>
<span class="err">    &quot;billing_contact&quot;: &quot;<EMAIL>&quot;</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Acme Corporation&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;domain&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;enable_sso&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;require_2fa&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data_retention_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2555</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allowed_domains&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;acme.co.uk&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;limits&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;max_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;current_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_departments&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;current_departments&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;api_keys&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;organisation_key&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_key_abcd1234&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;webhook_secret&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;whsec_5678efgh&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="get-organisation-details">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Get Organisation Details</a><a class="headerlink" href="#get-organisation-details" title="Link to this heading"></a></h3>
<p>Retrieve comprehensive organisation information.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/enterprise/organisations/{org_id}</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Acme Corporation&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;domain&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;statistics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">245</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;active_users_30d&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">198</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_departments&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certifications_in_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">89</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certifications_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">156</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;enable_sso&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;sso_provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Azure AD&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;require_2fa&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;password_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;strong&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;session_timeout_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">480</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;compliance&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;gdpr_compliant&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;soc2_compliant&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;last_audit_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-12-15T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;next_audit_due&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-15T00:00:00Z&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="update-organisation">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Update Organisation</a><a class="headerlink" href="#update-organisation" title="Link to this heading"></a></h3>
<p>Modify organisation settings and configuration.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">PATCH /api/v1/enterprise/organisations/{org_id}</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;settings&quot;: {</span>
<span class="err">    &quot;require_2fa&quot;: false,</span>
<span class="err">    &quot;session_timeout_minutes&quot;: 720</span>
<span class="err">  },</span>
<span class="err">  &quot;billing&quot;: {</span>
<span class="err">    &quot;max_users&quot;: 1500</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
</section>
</section>
<section id="department-management">
<h2><a class="toc-backref" href="#id6" role="doc-backlink">Department Management</a><a class="headerlink" href="#department-management" title="Link to this heading"></a></h2>
<section id="create-department">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Create Department</a><a class="headerlink" href="#create-department" title="Link to this heading"></a></h3>
<p>Create a new department within an organisation.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/enterprise/organisations/{org_id}/departments</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;name&quot;: &quot;Information Security&quot;,</span>
<span class="err">  &quot;description&quot;: &quot;Cybersecurity and risk management team&quot;,</span>
<span class="err">  &quot;parent_department_id&quot;: null,</span>
<span class="err">  &quot;manager_user_id&quot;: 456,</span>
<span class="err">  &quot;budget_annual&quot;: 500000,</span>
<span class="err">  &quot;cost_centre&quot;: &quot;IT-SEC-001&quot;,</span>
<span class="err">  &quot;settings&quot;: {</span>
<span class="err">    &quot;auto_enroll_certifications&quot;: [&quot;Security+&quot;, &quot;CISSP&quot;],</span>
<span class="err">    &quot;mandatory_training&quot;: true,</span>
<span class="err">    &quot;budget_approval_required&quot;: true</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;dept_789&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Information Security&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Cybersecurity and risk management team&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;parent_department_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;manager&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">456</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Sarah Johnson&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;statistics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;budget_utilised&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certifications_in_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="list-departments">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">List Departments</a><a class="headerlink" href="#list-departments" title="Link to this heading"></a></h3>
<p>Get all departments within an organisation.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/enterprise/organisations/{org_id}/departments</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>

<span class="err">?include_stats=true&amp;include_users=false</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;departments&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;dept_789&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Information Security&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;manager_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Sarah Johnson&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;user_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;budget_utilised_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">23.5</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;active_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;total_departments&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="user-management">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">User Management</a><a class="headerlink" href="#user-management" title="Link to this heading"></a></h2>
<section id="create-enterprise-user">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Create Enterprise User</a><a class="headerlink" href="#create-enterprise-user" title="Link to this heading"></a></h3>
<p>Add a new user to the organisation.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/enterprise/organisations/{org_id}/users</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;email&quot;: &quot;<EMAIL>&quot;,</span>
<span class="err">  &quot;full_name&quot;: &quot;John Smith&quot;,</span>
<span class="err">  &quot;role&quot;: &quot;user&quot;,</span>
<span class="err">  &quot;department_id&quot;: &quot;dept_789&quot;,</span>
<span class="err">  &quot;job_title&quot;: &quot;Security Analyst&quot;,</span>
<span class="err">  &quot;manager_user_id&quot;: 456,</span>
<span class="err">  &quot;start_date&quot;: &quot;2025-01-20&quot;,</span>
<span class="err">  &quot;permissions&quot;: {</span>
<span class="err">    &quot;can_access_ai_features&quot;: true,</span>
<span class="err">    &quot;can_export_data&quot;: false,</span>
<span class="err">    &quot;can_manage_team&quot;: false</span>
<span class="err">  },</span>
<span class="err">  &quot;licence_type&quot;: &quot;premium&quot;</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">789</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;full_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;John Smith&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;department&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;dept_789&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Information Security&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;licence&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;premium&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;expires_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2026-01-15T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;features_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;ai_assistant&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;advanced_analytics&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;export_data&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;invitation_sent&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="bulk-user-import">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Bulk User Import</a><a class="headerlink" href="#bulk-user-import" title="Link to this heading"></a></h3>
<p>Import multiple users from CSV or JSON data.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/enterprise/organisations/{org_id}/users/bulk-import</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;import_format&quot;: &quot;json&quot;,</span>
<span class="err">  &quot;users&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;email&quot;: &quot;<EMAIL>&quot;,</span>
<span class="err">      &quot;full_name&quot;: &quot;Alice Brown&quot;,</span>
<span class="err">      &quot;department_name&quot;: &quot;Information Security&quot;,</span>
<span class="err">      &quot;job_title&quot;: &quot;Senior Security Engineer&quot;,</span>
<span class="err">      &quot;licence_type&quot;: &quot;premium&quot;</span>
<span class="err">    }</span>
<span class="err">  ],</span>
<span class="err">  &quot;options&quot;: {</span>
<span class="err">    &quot;send_invitations&quot;: true,</span>
<span class="err">    &quot;skip_duplicates&quot;: true,</span>
<span class="err">    &quot;default_role&quot;: &quot;user&quot;</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;import_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;import_abc123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;processing&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;processed_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;successful_imports&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;failed_imports&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[],</span>
<span class="w">  </span><span class="nt">&quot;estimated_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:35:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="analytics-reporting">
<h2><a class="toc-backref" href="#id12" role="doc-backlink">Analytics &amp; Reporting</a><a class="headerlink" href="#analytics-reporting" title="Link to this heading"></a></h2>
<section id="organisation-analytics">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">Organisation Analytics</a><a class="headerlink" href="#organisation-analytics" title="Link to this heading"></a></h3>
<p>Get comprehensive organisation-wide analytics.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/enterprise/organisations/{org_id}/analytics</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>

<span class="err">?period=30d&amp;include_predictions=true</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;30d&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;user_engagement&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_active_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">198</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;daily_average_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">156</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;user_growth_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;engagement_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.7</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;learning_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;certifications_started&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certifications_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">23</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_completion_time_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">89</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;success_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;cost_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_training_investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">125000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;cost_per_certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2500</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;roi_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">245</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;budget_utilisation&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.67</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;predictions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;projected_completions_next_30d&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">28</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;estimated_budget_needed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">75000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;risk_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Q1 budget constraints&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Holiday season impact&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;top_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;enrollments&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;success_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.73</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="department-analytics">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Department Analytics</a><a class="headerlink" href="#department-analytics" title="Link to this heading"></a></h3>
<p>Get detailed analytics for specific departments.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/enterprise/departments/{dept_id}/analytics</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;department_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;dept_789&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;department_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Information Security&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;30d&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;team_performance&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_team_members&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;active_learners&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;team_engagement_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">9.2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_study_hours_per_week&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.5</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;certification_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;in_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;completed_this_period&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;success_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_completion_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">76</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;budget_tracking&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;allocated_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;spent_to_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">23500</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;projected_spend&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;cost_per_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2800</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;skill_development&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;top_skills_developed&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Cloud Security&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Incident Response&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;skill_gaps_identified&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Zero Trust Architecture&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;DevSecOps&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;recommended_training&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;AWS Security Specialty&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="licence-management">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">Licence Management</a><a class="headerlink" href="#licence-management" title="Link to this heading"></a></h2>
<section id="licence-usage-overview">
<h3><a class="toc-backref" href="#id16" role="doc-backlink">Licence Usage Overview</a><a class="headerlink" href="#licence-usage-overview" title="Link to this heading"></a></h3>
<p>Monitor licence utilisation across the organisation.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/enterprise/organisations/{org_id}/licences</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;licence_summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_licences&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;active_licences&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">245</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;available_licences&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">755</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;utilisation_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.245</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;licence_types&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;basic&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;allocated&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;used&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;available&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">377</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;premium&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;allocated&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">300</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;used&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">89</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;available&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">211</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;enterprise&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;allocated&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">200</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;used&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">33</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;available&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">167</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;usage_trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;monthly_growth&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.08</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;projected_usage_6m&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">312</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;recommended_licence_adjustment&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;increase_premium_by_50&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="assign-licences">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Assign Licences</a><a class="headerlink" href="#assign-licences" title="Link to this heading"></a></h3>
<p>Assign or modify user licences.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/enterprise/organisations/{org_id}/licences/assign</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;assignments&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;user_id&quot;: 789,</span>
<span class="err">      &quot;licence_type&quot;: &quot;premium&quot;,</span>
<span class="err">      &quot;expires_at&quot;: &quot;2026-01-15T00:00:00Z&quot;</span>
<span class="err">    }</span>
<span class="err">  ],</span>
<span class="err">  &quot;bulk_assignment&quot;: {</span>
<span class="err">    &quot;department_id&quot;: &quot;dept_789&quot;,</span>
<span class="err">    &quot;licence_type&quot;: &quot;premium&quot;,</span>
<span class="err">    &quot;duration_months&quot;: 12</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
</section>
</section>
<section id="compliance-audit">
<h2><a class="toc-backref" href="#id18" role="doc-backlink">Compliance &amp; Audit</a><a class="headerlink" href="#compliance-audit" title="Link to this heading"></a></h2>
<section id="compliance-report">
<h3><a class="toc-backref" href="#id19" role="doc-backlink">Compliance Report</a><a class="headerlink" href="#compliance-report" title="Link to this heading"></a></h3>
<p>Generate comprehensive compliance reports.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/enterprise/organisations/{org_id}/compliance/report</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>

<span class="err">?standards=gdpr,soc2&amp;format=json</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;report_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;compliance_standards&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;gdpr&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;compliant&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_assessment&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-12-01T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;requirements_met&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">47</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total_requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">47</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;compliance_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;soc2&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;compliant&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_assessment&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-11-15T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;requirements_met&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">64</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total_requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">67</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;compliance_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">95.5</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;data_governance&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;data_retention_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data_deletion_requests&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data_export_requests&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;privacy_policy_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2.1&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;audit_trail&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15420</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;security_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">234</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;access_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12890</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2296</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="audit-log">
<h3><a class="toc-backref" href="#id20" role="doc-backlink">Audit Log</a><a class="headerlink" href="#audit-log" title="Link to this heading"></a></h3>
<p>Access detailed audit logs for compliance and security monitoring.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/enterprise/organisations/{org_id}/audit-log</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>

<span class="err">?start_date=2025-01-01&amp;end_date=2025-01-15&amp;event_type=security</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;start_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-01T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;end_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T23:59:59Z&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;total_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1250</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;events&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;audit_12345&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T09:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;event_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;security&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;action&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;login_failed&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">456</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;user_email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;ip_address&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;*************&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;user_agent&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Mozilla/5.0...&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;invalid_password&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;attempt_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;risk_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;medium&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;security_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;access_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">890</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">315</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;high_risk_events&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="error-handling">
<h2><a class="toc-backref" href="#id21" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>Enterprise API error responses:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;organisation_limit_exceeded&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Maximum number of organisations reached for this account&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">403</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;current_organisations&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;maximum_allowed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;upgrade_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Common Error Codes</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">403</span></code> - Insufficient permissions or limits exceeded</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">404</span></code> - Organisation or resource not found</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">409</span></code> - Conflict (duplicate organisation domain)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">422</span></code> - Invalid organisation configuration</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">429</span></code> - Rate limit exceeded for enterprise operations</p></li>
</ul>
</section>
<section id="see-also">
<h2><a class="toc-backref" href="#id22" role="doc-backlink">See Also</a><a class="headerlink" href="#see-also" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../enterprise/dashboard.html"><span class="doc">🏢 Enterprise Dashboard</span></a> - Enterprise dashboard guide</p></li>
<li><p><span class="xref std std-doc">../enterprise/multi_tenant</span> - Multi-tenant setup</p></li>
<li><p><a class="reference internal" href="../guides/enterprise_guide.html"><span class="doc">🏢 Enterprise Guide</span></a> - Enterprise user guide</p></li>
<li><p><a class="reference internal" href="authentication.html"><span class="doc">Authentication &amp; Security</span></a> - API authentication</p></li>
<li><p><span class="xref std std-doc">../enterprise/compliance</span> - Compliance documentation</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="ai_assistant.html" class="btn btn-neutral float-left" title="AI Study Assistant API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="cost_calculator.html" class="btn btn-neutral float-right" title="Cost Calculator API" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>