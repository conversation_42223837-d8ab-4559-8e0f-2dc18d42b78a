<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta content="Complete enterprise organization and team management system" name="description" />
<meta content="organization management, enterprise, team administration, user management" name="keywords" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Organization Management &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/organization-management.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Agent 3: Enterprise Analytics Engine" href="agent3-enterprise-analytics.html" />
    <link rel="prev" title="Study Session Tracking" href="study-session-tracking.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Organization Management</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#organization-administration">Organization Administration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-organization">Create Organization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#get-organization-details">Get Organization Details</a></li>
<li class="toctree-l3"><a class="reference internal" href="#update-organization">Update Organization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#team-management">Team Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-team">Create Team</a></li>
<li class="toctree-l3"><a class="reference internal" href="#get-team-details">Get Team Details</a></li>
<li class="toctree-l3"><a class="reference internal" href="#add-team-members">Add Team Members</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#user-management">User Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#list-organization-users">List Organization Users</a></li>
<li class="toctree-l3"><a class="reference internal" href="#invite-users">Invite Users</a></li>
<li class="toctree-l3"><a class="reference internal" href="#update-user-role">Update User Role</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#analytics-and-reporting">Analytics and Reporting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#organization-analytics">Organization Analytics</a></li>
<li class="toctree-l3"><a class="reference internal" href="#team-performance-report">Team Performance Report</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#enterprise-settings">Enterprise Settings</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#security-policies">Security Policies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#compliance-and-audit">Compliance and Audit</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#audit-logs">Audit Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="#compliance-report">Compliance Report</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#integration-features">Integration Features</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Organization Management</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/organization-management.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="organization-management">
<h1>Organization Management<a class="headerlink" href="#organization-management" title="Link to this heading"></a></h1>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Organization Management system provides comprehensive enterprise user and team administration capabilities. Manage organizational hierarchies, team structures, user roles, and enterprise-wide settings with advanced analytics and reporting.</p>
<p><strong>🏢 Key Features:</strong></p>
<ul class="simple">
<li><p><strong>Multi-Tenant Architecture</strong>: Complete data isolation between organizations</p></li>
<li><p><strong>Hierarchical Team Management</strong>: Flexible organizational structures and reporting</p></li>
<li><p><strong>Advanced User Administration</strong>: Comprehensive user lifecycle management</p></li>
<li><p><strong>Role-Based Access Control</strong>: Granular permissions and authorization</p></li>
<li><p><strong>Enterprise Analytics</strong>: Organization-wide insights and reporting</p></li>
</ul>
</section>
<section id="organization-administration">
<h2>Organization Administration<a class="headerlink" href="#organization-administration" title="Link to this heading"></a></h2>
<section id="create-organization">
<h3>Create Organization<a class="headerlink" href="#create-organization" title="Link to this heading"></a></h3>
<p>Create a new organization with initial configuration.</p>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{admin_token}</span></code></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Acme Corporation&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;domain&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;industry&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;technology&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;size&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;large&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;sso_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;mfa_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data_retention_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2555</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allowed_domains&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;acme.org&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;billing&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;plan&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;enterprise&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;billing_email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Acme Corporation&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;domain&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;industry&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;technology&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;size&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;large&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T15:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;sso_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;mfa_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data_retention_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2555</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allowed_domains&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;acme.org&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;subscription&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;plan&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;enterprise&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;current_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;expires_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2026-06-16T15:00:00Z&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="get-organization-details">
<h3>Get Organization Details<a class="headerlink" href="#get-organization-details" title="Link to this heading"></a></h3>
<p>Retrieve comprehensive organization information.</p>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{access_token}</span></code></p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Acme Corporation&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;domain&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;industry&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;technology&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;size&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;large&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T15:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T15:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;statistics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">250</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;active_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">230</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_teams&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;completion_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;sso_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;mfa_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data_retention_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2555</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allowed_domains&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;acme.org&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;certification_approval_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;budget_tracking_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="update-organization">
<h3>Update Organization<a class="headerlink" href="#update-organization" title="Link to this heading"></a></h3>
<p>Update organization settings and configuration.</p>
<p><strong>PUT</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{admin_token}</span></code></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Acme Corporation Ltd&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;mfa_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certification_approval_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;budget_tracking_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allowed_domains&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;acme.org&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;acme.net&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Acme Corporation Ltd&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T16:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;mfa_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certification_approval_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;budget_tracking_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allowed_domains&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;acme.com&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;acme.org&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;acme.net&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="team-management">
<h2>Team Management<a class="headerlink" href="#team-management" title="Link to this heading"></a></h2>
<section id="create-team">
<h3>Create Team<a class="headerlink" href="#create-team" title="Link to this heading"></a></h3>
<p>Create a new team within the organization.</p>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/teams</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{access_token}</span></code></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Operations Team&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Responsible for security monitoring and incident response&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;department&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;IT Security&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;manager_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">67890</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;parent_team_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;budget_allocation&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certification_goals&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CISM&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CEH&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;training_priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;team_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">54321</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Operations Team&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Responsible for security monitoring and incident response&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;department&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;IT Security&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;manager_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">67890</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;parent_team_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T16:15:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;member_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;budget_allocation&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certification_goals&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CISM&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CEH&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;training_priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="get-team-details">
<h3>Get Team Details<a class="headerlink" href="#get-team-details" title="Link to this heading"></a></h3>
<p>Retrieve comprehensive team information including members and analytics.</p>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/teams/{team_id}</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{access_token}</span></code></p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;team_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">54321</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Operations Team&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Responsible for security monitoring and incident response&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;department&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;IT Security&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;manager&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">67890</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;John Smith&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;manager&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;members&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">11111</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Alice Johnson&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;senior_analyst&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;joined_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T16:20:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Security+&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CySA+&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;active_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;completion_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;statistics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_members&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;active_members&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_completion_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.82</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;budget_utilization&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.65</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;performance&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;team_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">4.2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certification_velocity&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">2.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;knowledge_retention&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.88</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;collaboration_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">4.5</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="add-team-members">
<h3>Add Team Members<a class="headerlink" href="#add-team-members" title="Link to this heading"></a></h3>
<p>Add users to a team with specific roles and permissions.</p>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/teams/{team_id}/members</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{access_token}</span></code></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;user_ids&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="mi">11111</span><span class="p">,</span><span class="w"> </span><span class="mi">22222</span><span class="p">,</span><span class="w"> </span><span class="mi">33333</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;analyst&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;permissions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;read:team_data&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;write:progress&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;read:certifications&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;notification_preferences&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;team_updates&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certification_reminders&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;goal_notifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;team_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">54321</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;added_members&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">11111</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Alice Johnson&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;analyst&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;added_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T16:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;total_members&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;failed_additions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="user-management">
<h2>User Management<a class="headerlink" href="#user-management" title="Link to this heading"></a></h2>
<section id="list-organization-users">
<h3>List Organization Users<a class="headerlink" href="#list-organization-users" title="Link to this heading"></a></h3>
<p>Get all users within the organization with filtering and pagination.</p>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/users</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{access_token}</span></code></p>
<p><strong>Query Parameters:</strong>
- <code class="docutils literal notranslate"><span class="pre">role</span></code> (optional): Filter by user role
- <code class="docutils literal notranslate"><span class="pre">team_id</span></code> (optional): Filter by team membership
- <code class="docutils literal notranslate"><span class="pre">status</span></code> (optional): Filter by user status (active, inactive, pending)
- <code class="docutils literal notranslate"><span class="pre">search</span></code> (optional): Search by name or email
- <code class="docutils literal notranslate"><span class="pre">page</span></code> (optional): Page number for pagination
- <code class="docutils literal notranslate"><span class="pre">limit</span></code> (optional): Number of users per page</p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;users&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">11111</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;first_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Alice&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Johnson&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;analyst&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;teams&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;team_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">54321</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;team_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Operations Team&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;analyst&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;in_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;planned&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;last_activity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T14:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;joined_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:00:00Z&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;total_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">250</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;page&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;limit&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total_pages&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="invite-users">
<h3>Invite Users<a class="headerlink" href="#invite-users" title="Link to this heading"></a></h3>
<p>Invite new users to join the organization.</p>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/users/invite</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{access_token}</span></code></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;invitations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;first_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;New&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;User&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;team_ids&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="mi">54321</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;welcome_message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Welcome to our security training program!&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;send_email&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expires_in_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;invitations_sent&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;invitation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;inv_abc123&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;expires_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-23T16:45:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sent&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;failed_invitations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[],</span>
<span class="w">  </span><span class="nt">&quot;total_sent&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="update-user-role">
<h3>Update User Role<a class="headerlink" href="#update-user-role" title="Link to this heading"></a></h3>
<p>Update user role and permissions within the organization.</p>
<p><strong>PUT</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/users/{user_id}/role</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{admin_token}</span></code></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;manager&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;permissions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;read:all_teams&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;write:team_management&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;read:analytics&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;manage:team_members&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;effective_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-17T00:00:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">11111</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;previous_role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;analyst&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;new_role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;manager&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;permissions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;read:all_teams&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;write:team_management&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;read:analytics&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;manage:team_members&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;effective_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-17T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T16:50:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="analytics-and-reporting">
<h2>Analytics and Reporting<a class="headerlink" href="#analytics-and-reporting" title="Link to this heading"></a></h2>
<section id="organization-analytics">
<h3>Organization Analytics<a class="headerlink" href="#organization-analytics" title="Link to this heading"></a></h3>
<p>Get comprehensive organization-wide analytics and insights.</p>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/analytics</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{access_token}</span></code></p>
<p><strong>Query Parameters:</strong>
- <code class="docutils literal notranslate"><span class="pre">period</span></code> (optional): Analysis period (7d, 30d, 90d, 1y)
- <code class="docutils literal notranslate"><span class="pre">include_teams</span></code> (optional): Include team-level breakdowns</p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;30d&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;overview&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">250</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;active_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">230</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;user_growth_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.15</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_teams&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;completion_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_study_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">12.5</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;certification_metrics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;certifications_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">35</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certifications_in_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">68</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_completion_time_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;success_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.89</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;top_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;completions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;in_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;success_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.92</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;team_performance&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;team_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">54321</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;team_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Operations Team&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completion_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;average_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">4.2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;budget_utilization&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.65</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;member_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;budget_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">500000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;utilized_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">325000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;utilization_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.65</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">2.8</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;cost_per_certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">9285</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;user_engagement&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;active_users&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">230</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;study_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2875</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;sessions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">145</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;certification_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;completions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;new_enrollments&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;progress_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="team-performance-report">
<h3>Team Performance Report<a class="headerlink" href="#team-performance-report" title="Link to this heading"></a></h3>
<p>Generate detailed performance report for specific team.</p>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/teams/{team_id}/performance</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{access_token}</span></code></p>
<p><strong>Query Parameters:</strong>
- <code class="docutils literal notranslate"><span class="pre">period</span></code> (optional): Analysis period (7d, 30d, 90d, 1y)</p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;team_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">54321</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;team_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Operations Team&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;30d&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;performance_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">4.2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;metrics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;completion_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_study_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">15.2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certification_velocity&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">2.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;knowledge_retention&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.88</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;collaboration_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">4.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;goal_achievement&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.92</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;member_performance&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">11111</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Alice Johnson&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completion_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.90</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;study_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certifications_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;performance_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">4.5</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;improvement_trend&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;positive&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;strengths&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;High collaboration and knowledge sharing&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Consistent study habits across team members&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Strong performance in technical certifications&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;improvement_areas&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Management certification completion rates&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Cross-training in emerging technologies&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Consider advanced leadership training for senior members&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Implement peer mentoring program&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Increase focus on cloud security certifications&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="enterprise-settings">
<h2>Enterprise Settings<a class="headerlink" href="#enterprise-settings" title="Link to this heading"></a></h2>
<section id="security-policies">
<h3>Security Policies<a class="headerlink" href="#security-policies" title="Link to this heading"></a></h3>
<p>Configure organization-wide security policies.</p>
<p><strong>PUT</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/security-policies</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{admin_token}</span></code></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;password_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;min_length&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;require_uppercase&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;require_lowercase&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;require_numbers&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;require_special_chars&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;password_history&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_age_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">90</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;session_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;max_session_duration_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;idle_timeout_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_concurrent_sessions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;require_mfa&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;access_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;allowed_ip_ranges&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;***********/24&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;10.0.0.0/8&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;blocked_countries&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CN&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;RU&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;require_device_registration&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allow_mobile_access&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;security_policies&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;password_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;min_length&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;require_uppercase&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;require_lowercase&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;require_numbers&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;require_special_chars&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;password_history&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;max_age_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">90</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;session_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;max_session_duration_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;idle_timeout_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;max_concurrent_sessions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;require_mfa&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;access_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;allowed_ip_ranges&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;***********/24&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;10.0.0.0/8&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;blocked_countries&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CN&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;RU&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;require_device_registration&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;allow_mobile_access&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T17:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;effective_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-17T00:00:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="compliance-and-audit">
<h2>Compliance and Audit<a class="headerlink" href="#compliance-and-audit" title="Link to this heading"></a></h2>
<section id="audit-logs">
<h3>Audit Logs<a class="headerlink" href="#audit-logs" title="Link to this heading"></a></h3>
<p>Retrieve comprehensive audit logs for compliance and security monitoring.</p>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/audit-logs</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{admin_token}</span></code></p>
<p><strong>Query Parameters:</strong>
- <code class="docutils literal notranslate"><span class="pre">start_date</span></code> (optional): Start date for log retrieval
- <code class="docutils literal notranslate"><span class="pre">end_date</span></code> (optional): End date for log retrieval
- <code class="docutils literal notranslate"><span class="pre">user_id</span></code> (optional): Filter by specific user
- <code class="docutils literal notranslate"><span class="pre">action_type</span></code> (optional): Filter by action type
- <code class="docutils literal notranslate"><span class="pre">limit</span></code> (optional): Number of logs to return</p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;audit_logs&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;log_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;log_abc123&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T17:15:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">11111</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;user_email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;action&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user_role_updated&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;resource&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user:22222&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;previous_role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;new_role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;analyst&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;changed_by&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;admin:67890&quot;</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;ip_address&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;*************&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;user_agent&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Mozilla/5.0...&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;result&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;success&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;total_logs&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1250</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;page&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;limit&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="compliance-report">
<h3>Compliance Report<a class="headerlink" href="#compliance-report" title="Link to this heading"></a></h3>
<p>Generate compliance reports for regulatory requirements.</p>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/organizations/{organization_id}/compliance/report</span></code></p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">{admin_token}</span></code></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;report_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sox_compliance&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period_start&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-01T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period_end&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T23:59:59Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;include_sections&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;user_access_controls&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;data_retention&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;audit_trails&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;security_policies&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;report_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;report_compliance_123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organization_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12345</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;report_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sox_compliance&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T17:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;start&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-01T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;end&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-16T23:59:59Z&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;compliance_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.95</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;findings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;section&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user_access_controls&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;compliant&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.98</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;All user access controls properly implemented&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Review and update data retention policies quarterly&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Implement additional monitoring for privileged access&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;download_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://api.certpathfinder.com/reports/compliance_123.pdf&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="integration-features">
<h2>Integration Features<a class="headerlink" href="#integration-features" title="Link to this heading"></a></h2>
<p><strong>Enterprise Integrations:</strong>
- Active Directory / LDAP synchronization
- Single Sign-On (SSO) with SAML 2.0 and OAuth 2.0
- HR system integration for automated user provisioning
- Learning Management System (LMS) connectivity
- Business Intelligence (BI) tool integration</p>
<p><strong>API Features:</strong>
- RESTful API with comprehensive endpoints
- Webhook support for real-time notifications
- Bulk operations for large-scale management
- Rate limiting and throttling for enterprise scale
- Comprehensive audit logging and monitoring</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="study-session-tracking.html" class="btn btn-neutral float-left" title="Study Session Tracking" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="agent3-enterprise-analytics.html" class="btn btn-neutral float-right" title="Agent 3: Enterprise Analytics Engine" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>