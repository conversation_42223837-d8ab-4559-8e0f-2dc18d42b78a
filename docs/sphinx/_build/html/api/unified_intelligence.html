<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>🤖 Unified Intelligence API &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/unified_intelligence.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Core Authentication System" href="authentication-system.html" />
    <link rel="prev" title="Integration Hub API" href="integration_hub.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">🤖 Unified Intelligence API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#base-url">🔗 <strong>Base URL</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="#authentication">🔐 <strong>Authentication</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="#unified-dashboard-endpoints">📊 <strong>Unified Dashboard Endpoints</strong></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#complete-unified-dashboard">Complete Unified Dashboard</a></li>
<li class="toctree-l3"><a class="reference internal" href="#complete-user-profile">Complete User Profile</a></li>
<li class="toctree-l3"><a class="reference internal" href="#unified-performance-metrics">Unified Performance Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#unified-intelligence-endpoints">🤖 <strong>Unified Intelligence Endpoints</strong></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#comprehensive-planning">Comprehensive Planning</a></li>
<li class="toctree-l3"><a class="reference internal" href="#enterprise-training-analysis">Enterprise Training Analysis</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#salary-intelligence-endpoints">💰 <strong>Salary Intelligence Endpoints</strong></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#role-salary-analysis">Role Salary Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="#roi-analysis">ROI Analysis</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#health-check-endpoints">🏥 <strong>Health Check Endpoints</strong></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#service-health-checks">Service Health Checks</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">⚠️ <strong>Error Handling</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="#additional-resources">📚 <strong>Additional Resources</strong></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">🤖 Unified Intelligence API</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/unified_intelligence.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="unified-intelligence-api">
<h1>🤖 Unified Intelligence API<a class="headerlink" href="#unified-intelligence-api" title="Link to this heading"></a></h1>
<p><strong>Complete API Reference for Unified Platform</strong></p>
<p>The Unified Intelligence API provides seamless access to all platform components through standardized endpoints. This API combines insights from AI Study Assistant, Career Intelligence, Enterprise Analytics, and Marketplace Hub into a cohesive, intelligent system.</p>
<section id="base-url">
<h2>🔗 <strong>Base URL</strong><a class="headerlink" href="#base-url" title="Link to this heading"></a></h2>
<p>All unified intelligence endpoints are available under:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>https://your-domain/api/v1/
</pre></div>
</div>
</section>
<section id="authentication">
<h2>🔐 <strong>Authentication</strong><a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>The API uses Traefik-based authentication with header forwarding:</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">X-Forwarded-User: your-user-id</span>
<span class="err">X-Remote-User: your-user-id</span>
<span class="err">X-User: your-user-id</span>
</pre></div>
</div>
<p>Example request:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;X-Forwarded-User: user123&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>https://your-domain/api/v1/dashboard/
</pre></div>
</div>
</section>
<section id="unified-dashboard-endpoints">
<h2>📊 <strong>Unified Dashboard Endpoints</strong><a class="headerlink" href="#unified-dashboard-endpoints" title="Link to this heading"></a></h2>
<section id="complete-unified-dashboard">
<h3>Complete Unified Dashboard<a class="headerlink" href="#complete-unified-dashboard" title="Link to this heading"></a></h3>
<p>Get comprehensive dashboard combining all platform components.</p>
</section>
<section id="complete-user-profile">
<h3>Complete User Profile<a class="headerlink" href="#complete-user-profile" title="Link to this heading"></a></h3>
<p>Get complete user profile aggregated from all platform components.</p>
</section>
<section id="unified-performance-metrics">
<h3>Unified Performance Metrics<a class="headerlink" href="#unified-performance-metrics" title="Link to this heading"></a></h3>
<p>Get key performance metrics and indicators.</p>
</section>
</section>
<section id="unified-intelligence-endpoints">
<h2>🤖 <strong>Unified Intelligence Endpoints</strong><a class="headerlink" href="#unified-intelligence-endpoints" title="Link to this heading"></a></h2>
<section id="comprehensive-planning">
<h3>Comprehensive Planning<a class="headerlink" href="#comprehensive-planning" title="Link to this heading"></a></h3>
<p>Create comprehensive certification and career plan using all platform components.</p>
</section>
<section id="enterprise-training-analysis">
<h3>Enterprise Training Analysis<a class="headerlink" href="#enterprise-training-analysis" title="Link to this heading"></a></h3>
<p>Analyze enterprise training needs and budget optimization.</p>
</section>
</section>
<section id="salary-intelligence-endpoints">
<h2>💰 <strong>Salary Intelligence Endpoints</strong><a class="headerlink" href="#salary-intelligence-endpoints" title="Link to this heading"></a></h2>
<section id="role-salary-analysis">
<h3>Role Salary Analysis<a class="headerlink" href="#role-salary-analysis" title="Link to this heading"></a></h3>
<p>Comprehensive salary analysis for specific roles.</p>
</section>
<section id="roi-analysis">
<h3>ROI Analysis<a class="headerlink" href="#roi-analysis" title="Link to this heading"></a></h3>
<p>Calculate ROI for certification investments.</p>
</section>
</section>
<section id="health-check-endpoints">
<h2>🏥 <strong>Health Check Endpoints</strong><a class="headerlink" href="#health-check-endpoints" title="Link to this heading"></a></h2>
<section id="service-health-checks">
<h3>Service Health Checks<a class="headerlink" href="#service-health-checks" title="Link to this heading"></a></h3>
<p>Monitor the health of all unified platform services.</p>
</section>
</section>
<section id="error-handling">
<h2>⚠️ <strong>Error Handling</strong><a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>All endpoints use standardized error responses:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;550e8400-e29b-41d4-a716-446655440000&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;error_code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;UNIFIED_DASHBOARD_SERVICE_ERROR&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Error generating dashboard&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-16T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;application_error&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Common HTTP status codes:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">200</span></code> - Success</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">400</span></code> - Bad Request (invalid parameters)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">401</span></code> - Unauthorized (missing or invalid authentication)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">404</span></code> - Not Found (resource doesn’t exist)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">422</span></code> - Unprocessable Entity (validation error)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">500</span></code> - Internal Server Error (server-side error)</p></li>
</ul>
</section>
<section id="additional-resources">
<h2>📚 <strong>Additional Resources</strong><a class="headerlink" href="#additional-resources" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../unified_platform.html"><span class="doc">🔗 Unified Platform Integration</span></a> - Unified platform overview</p></li>
<li><p><a class="reference internal" href="../guides/unified_dashboard_guide.html"><span class="doc">📊 Unified Dashboard User Guide</span></a> - Dashboard integration guide</p></li>
<li><p><span class="xref std std-doc">../development/integration_patterns</span> - Development patterns</p></li>
<li><p><a class="reference internal" href="authentication.html"><span class="doc">Authentication &amp; Security</span></a> - Authentication details</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="integration_hub.html" class="btn btn-neutral float-left" title="Integration Hub API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="authentication-system.html" class="btn btn-neutral float-right" title="Core Authentication System" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>