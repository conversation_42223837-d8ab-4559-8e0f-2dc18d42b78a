<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta content="Complete API reference for Agent 4 Career &amp; Cost Intelligence" name="description" />
<meta content="API, Agent 4, career pathfinding, ROI analysis, budget optimization" name="keywords" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Agent 4 API Reference &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/agent4-api-reference.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="❓ Frequently Asked Questions" href="../faq/index.html" />
    <link rel="prev" title="Agent 3: Enterprise Analytics Engine" href="agent3-enterprise-analytics.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Agent 4 API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#career-pathfinding-api">Career Pathfinding API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#post-career-transition-pathfinding">POST /career-transition/pathfinding</a></li>
<li class="toctree-l3"><a class="reference internal" href="#get-career-transition-roles">GET /career-transition/roles</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#cost-calculator-api">Cost Calculator API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#post-cost-calculator-calculate">POST /cost-calculator/calculate</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#salary-intelligence-api">Salary Intelligence API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#post-salary-intelligence-roi-analysis">POST /salary-intelligence/roi-analysis</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#budget-optimization-api">Budget Optimization API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#post-budget-optimization-optimize">POST /budget-optimization/optimize</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#market-intelligence-api">Market Intelligence API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#post-market-intelligence-analysis">POST /market-intelligence/analysis</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="#sdks-and-libraries">SDKs and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="#support">Support</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Agent 4 API Reference</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/agent4-api-reference.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="agent-4-api-reference">
<h1>Agent 4 API Reference<a class="headerlink" href="#agent-4-api-reference" title="Link to this heading"></a></h1>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Agent 4 Career &amp; Cost Intelligence provides a comprehensive REST API for career pathfinding, cost analysis, ROI calculations, budget optimization, and market intelligence. All endpoints are production-ready with enterprise-grade performance and security.</p>
<p><strong>Base URL</strong>: <code class="docutils literal notranslate"><span class="pre">https://api.certpathfinder.com/api/v1</span></code></p>
<p><strong>Authentication</strong>: Bearer token required for all endpoints</p>
<p><strong>Rate Limiting</strong>: 10,000 requests per minute per API key</p>
</section>
<section id="career-pathfinding-api">
<h2>Career Pathfinding API<a class="headerlink" href="#career-pathfinding-api" title="Link to this heading"></a></h2>
<p>Find optimal career transition paths using advanced A* algorithms.</p>
<section id="post-career-transition-pathfinding">
<h3>POST /career-transition/pathfinding<a class="headerlink" href="#post-career-transition-pathfinding" title="Link to this heading"></a></h3>
<p>Generate optimal career transition paths with multi-constraint optimization.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;current_role_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;target_role_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;max_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;max_timeline_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;max_difficulty&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Medium&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;learning_style&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;mixed&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;study_hours_per_week&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;USD&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;path_options&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;path_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;path_001&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;estimated_duration_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">16</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CISM&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;cost_breakdown&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;exam_fees&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1400</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;study_materials&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">800</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;training&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2300</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;timeline&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;preparation&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;study_phase&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;constraints_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;budget_utilization&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.90</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;timeline_efficiency&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.89</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Consider CISSP first for maximum ROI&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Schedule exams during low-demand periods&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Performance</strong>: &lt; 3 seconds response time</p>
</section>
<section id="get-career-transition-roles">
<h3>GET /career-transition/roles<a class="headerlink" href="#get-career-transition-roles" title="Link to this heading"></a></h3>
<p>Retrieve available career roles and transition relationships.</p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;roles&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Analyst&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Entry&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Analysis&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;average_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">75000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;growth_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.15</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;transitions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;from_role_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;to_role_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;difficulty_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.7</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;typical_duration_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;success_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.82</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="cost-calculator-api">
<h2>Cost Calculator API<a class="headerlink" href="#cost-calculator-api" title="Link to this heading"></a></h2>
<p>Comprehensive cost calculation with hidden cost analysis.</p>
<section id="post-cost-calculator-calculate">
<h3>POST /cost-calculator/calculate<a class="headerlink" href="#post-cost-calculator-calculate" title="Link to this heading"></a></h3>
<p>Calculate total cost of ownership for certification paths.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;certification_ids&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;location&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;San Francisco, CA&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;USD&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;include_hidden_costs&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;study_materials&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;practice_exams&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;bootcamp_training&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;total_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4750</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;USD&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;cost_breakdown&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;exam_fees&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1400</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;study_materials&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">800</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;practice_exams&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">300</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;travel_costs&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">250</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;time_opportunity_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2000</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;location_adjustments&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;cost_of_living_multiplier&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.25</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;regional_pricing&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">150</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;hidden_costs&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;renewal_fees&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">200</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;continuing_education&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">300</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;membership_fees&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">150</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Performance</strong>: &lt; 2 seconds response time</p>
</section>
</section>
<section id="salary-intelligence-api">
<h2>Salary Intelligence API<a class="headerlink" href="#salary-intelligence-api" title="Link to this heading"></a></h2>
<p>Advanced salary analysis and ROI calculations.</p>
<section id="post-salary-intelligence-roi-analysis">
<h3>POST /salary-intelligence/roi-analysis<a class="headerlink" href="#post-salary-intelligence-roi-analysis" title="Link to this heading"></a></h3>
<p>Comprehensive ROI analysis with risk assessment.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;current_role_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;target_role_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;investment_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;location&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;remote&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;experience_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;current_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">75000</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;investment_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expected_salary_increase&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;payback_period_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">14</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;five_year_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">285</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;ten_year_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">450</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;confidence_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.88</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;risk_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Market saturation in target role&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Technology evolution impact&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;market_conditions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;demand_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;competition_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;medium&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;growth_projection&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.12</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;sensitivity_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;best_case_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">320</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;worst_case_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">180</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;probability_distribution&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;p90&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">310</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;p50&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">285</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;p10&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">195</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Performance</strong>: &lt; 2 seconds response time</p>
</section>
</section>
<section id="budget-optimization-api">
<h2>Budget Optimization API<a class="headerlink" href="#budget-optimization-api" title="Link to this heading"></a></h2>
<p>Enterprise budget allocation and optimization.</p>
<section id="post-budget-optimization-optimize">
<h3>POST /budget-optimization/optimize<a class="headerlink" href="#post-budget-optimization-optimize" title="Link to this heading"></a></h3>
<p>Optimize enterprise training budget allocation.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;enterprise_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;strategic_priorities&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;cybersecurity&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;cloud_security&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;timeline_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;team_constraints&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;security_team_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">25</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;experience_levels&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;junior&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;mid&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;senior&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;enterprise_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;optimized_allocation&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;foundational_training&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">35000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;advanced_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">40000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;specialized_training&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">20000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;contingency&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5000</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;projected_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">245</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;cost_savings&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;efficiency_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">92</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Prioritize CISSP training for senior staff&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Implement group training for cost efficiency&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;risk_assessment&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;budget_overrun_risk&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;low&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;timeline_risk&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;medium&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;skill_gap_risk&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;low&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;implementation_timeline&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;phase_1&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Months 1-3: Foundational training&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;phase_2&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Months 4-8: Advanced certifications&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;phase_3&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Months 9-12: Specialized training&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Performance</strong>: &lt; 5 seconds response time</p>
</section>
</section>
<section id="market-intelligence-api">
<h2>Market Intelligence API<a class="headerlink" href="#market-intelligence-api" title="Link to this heading"></a></h2>
<p>Real-time market trends and competitive intelligence.</p>
<section id="post-market-intelligence-analysis">
<h3>POST /market-intelligence/analysis<a class="headerlink" href="#post-market-intelligence-analysis" title="Link to this heading"></a></h3>
<p>Comprehensive market trend analysis.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;region&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;north_america&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;industry&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;technology&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;timeframe&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;12months&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;include_projections&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;demand_change&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;salary_trend&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;job_openings&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;competition_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;medium&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;growth_projection&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;locations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;location&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;San Francisco, CA&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;average_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">125000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;job_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">450</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;cost_of_living&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">185</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;demand_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;industries&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;industry&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;technology&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;top_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CISM&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CEH&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;average_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;growth_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;key_trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">&quot;Increased focus on cloud security&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;Growing demand for DevSecOps skills&quot;</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Performance</strong>: &lt; 1 second response time</p>
</section>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>All APIs use standard HTTP status codes and return detailed error information.</p>
<p><strong>Error Response Format:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;INVALID_REQUEST&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Budget amount must be greater than 1000&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;field&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;total_budget&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;provided_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;minimum_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;request_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;req_123456789&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Common Status Codes:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">200</span> <span class="pre">OK</span></code>: Request successful</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">400</span> <span class="pre">Bad</span> <span class="pre">Request</span></code>: Invalid request parameters</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">401</span> <span class="pre">Unauthorized</span></code>: Invalid or missing authentication</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">404</span> <span class="pre">Not</span> <span class="pre">Found</span></code>: Resource not found</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">429</span> <span class="pre">Too</span> <span class="pre">Many</span> <span class="pre">Requests</span></code>: Rate limit exceeded</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">500</span> <span class="pre">Internal</span> <span class="pre">Server</span> <span class="pre">Error</span></code>: Server error</p></li>
</ul>
</section>
<section id="rate-limiting">
<h2>Rate Limiting<a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<p><strong>Limits:</strong>
- 10,000 requests per minute per API key
- 100,000 requests per day per API key
- Burst limit: 100 requests per second</p>
<p><strong>Headers:</strong>
- <code class="docutils literal notranslate"><span class="pre">X-RateLimit-Limit</span></code>: Request limit per minute
- <code class="docutils literal notranslate"><span class="pre">X-RateLimit-Remaining</span></code>: Remaining requests in current window
- <code class="docutils literal notranslate"><span class="pre">X-RateLimit-Reset</span></code>: Time when rate limit resets</p>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>All API endpoints require authentication using Bearer tokens.</p>
<p><strong>Header Format:</strong></p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">Authorization: Bearer your_api_token_here</span>
</pre></div>
</div>
<p><strong>Token Management:</strong>
- Tokens expire after 24 hours
- Refresh tokens available for long-term access
- Enterprise customers receive dedicated API keys</p>
</section>
<section id="sdks-and-libraries">
<h2>SDKs and Libraries<a class="headerlink" href="#sdks-and-libraries" title="Link to this heading"></a></h2>
<p><strong>Official SDKs:</strong>
- Python SDK: <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span> <span class="pre">certpathfinder-sdk</span></code>
- JavaScript SDK: <code class="docutils literal notranslate"><span class="pre">npm</span> <span class="pre">install</span> <span class="pre">&#64;certpathfinder/sdk</span></code>
- Java SDK: Available on Maven Central</p>
<p><strong>Community Libraries:</strong>
- Go client library
- Ruby gem
- PHP composer package</p>
</section>
<section id="support">
<h2>Support<a class="headerlink" href="#support" title="Link to this heading"></a></h2>
<p><strong>Technical Support:</strong>
- Email: <a class="reference external" href="mailto:api-support&#37;&#52;&#48;certpathfinder&#46;com">api-support<span>&#64;</span>certpathfinder<span>&#46;</span>com</a>
- Documentation: <a class="reference external" href="https://docs.certpathfinder.com">https://docs.certpathfinder.com</a>
- Status Page: <a class="reference external" href="https://status.certpathfinder.com">https://status.certpathfinder.com</a></p>
<p><strong>SLA:</strong>
- 99.9% uptime guarantee
- &lt; 2 second average response time
- 24/7 monitoring and support</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="agent3-enterprise-analytics.html" class="btn btn-neutral float-left" title="Agent 3: Enterprise Analytics Engine" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../faq/index.html" class="btn btn-neutral float-right" title="❓ Frequently Asked Questions" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>