<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Progress Tracking API &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/progress_tracking.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Integration Hub API" href="integration_hub.html" />
    <link rel="prev" title="Career Framework API" href="career_framework.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Progress Tracking API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#study-session-management">Study Session Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#start-study-session">Start Study Session</a></li>
<li class="toctree-l3"><a class="reference internal" href="#complete-study-session">Complete Study Session</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#learning-analytics">Learning Analytics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#get-progress-overview">Get Progress Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="#detailed-session-history">Detailed Session History</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#achievement-system">Achievement System</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#get-user-achievements">Get User Achievements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#goal-management">Goal Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-learning-goal">Create Learning Goal</a></li>
<li class="toctree-l3"><a class="reference internal" href="#update-goal-progress">Update Goal Progress</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#performance-analytics">Performance Analytics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#learning-effectiveness-analysis">Learning Effectiveness Analysis</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Progress Tracking API</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/progress_tracking.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="progress-tracking-api">
<h1>Progress Tracking API<a class="headerlink" href="#progress-tracking-api" title="Link to this heading"></a></h1>
<p>The Progress Tracking API provides comprehensive learning analytics, study session management, and achievement tracking. It enables detailed monitoring of learning progress with gamification elements and predictive insights.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id1">Overview</a></p></li>
<li><p><a class="reference internal" href="#study-session-management" id="id2">Study Session Management</a></p>
<ul>
<li><p><a class="reference internal" href="#start-study-session" id="id3">Start Study Session</a></p></li>
<li><p><a class="reference internal" href="#complete-study-session" id="id4">Complete Study Session</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#learning-analytics" id="id5">Learning Analytics</a></p>
<ul>
<li><p><a class="reference internal" href="#get-progress-overview" id="id6">Get Progress Overview</a></p></li>
<li><p><a class="reference internal" href="#detailed-session-history" id="id7">Detailed Session History</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#achievement-system" id="id8">Achievement System</a></p>
<ul>
<li><p><a class="reference internal" href="#get-user-achievements" id="id9">Get User Achievements</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#goal-management" id="id10">Goal Management</a></p>
<ul>
<li><p><a class="reference internal" href="#create-learning-goal" id="id11">Create Learning Goal</a></p></li>
<li><p><a class="reference internal" href="#update-goal-progress" id="id12">Update Goal Progress</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#performance-analytics" id="id13">Performance Analytics</a></p>
<ul>
<li><p><a class="reference internal" href="#learning-effectiveness-analysis" id="id14">Learning Effectiveness Analysis</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-handling" id="id15">Error Handling</a></p></li>
<li><p><a class="reference internal" href="#see-also" id="id16">See Also</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Progress Tracking features include:</p>
<ul class="simple">
<li><p><strong>Study Session Management</strong> - Detailed tracking of all study activities</p></li>
<li><p><strong>Learning Analytics</strong> - Comprehensive progress analysis and insights</p></li>
<li><p><strong>Achievement System</strong> - Gamified milestones and badges</p></li>
<li><p><strong>Performance Metrics</strong> - Learning velocity and effectiveness tracking</p></li>
<li><p><strong>Predictive Analytics</strong> - AI-powered exam readiness predictions</p></li>
<li><p><strong>Goal Management</strong> - Personal learning objectives and targets</p></li>
</ul>
<p>All tracking occurs locally with complete privacy protection.</p>
</section>
<section id="study-session-management">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Study Session Management</a><a class="headerlink" href="#study-session-management" title="Link to this heading"></a></h2>
<section id="start-study-session">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">Start Study Session</a><a class="headerlink" href="#start-study-session" title="Link to this heading"></a></h3>
<p>Begin a new study session with intelligent recommendations.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/progress-tracking/sessions/start</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">{</span>
<span class="err">  &quot;user_id&quot;: 123,</span>
<span class="err">  &quot;certification_id&quot;: 45,</span>
<span class="err">  &quot;planned_duration_minutes&quot;: 60,</span>
<span class="err">  &quot;study_type&quot;: &quot;practice_questions&quot;,</span>
<span class="err">  &quot;topics&quot;: [&quot;Network Security&quot;, &quot;Cryptography&quot;],</span>
<span class="err">  &quot;difficulty_level&quot;: &quot;intermediate&quot;,</span>
<span class="err">  &quot;study_method&quot;: &quot;active_recall&quot;,</span>
<span class="err">  &quot;environment&quot;: &quot;home_office&quot;,</span>
<span class="err">  &quot;energy_level&quot;: &quot;high&quot;</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;session_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;session_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;certification_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;started_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;planned_duration_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">60</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;study_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;practice_questions&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;ai_recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;focus_areas&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Access Control Models&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Cryptographic Protocols&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;suggested_techniques&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Spaced repetition for weak areas&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Practice questions with explanations&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;optimal_break_schedule&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;break_after_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">25</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;break_duration_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;activity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;brief_walk&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;difficulty_adjustment&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;maintain_current_level&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;session_context&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;previous_session_performance&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;current_knowledge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.65</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;recommended_focus_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;predicted_effectiveness&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.82</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;study_materials&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;practice_questions&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;source&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Official CISSP Practice Tests&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;difficulty&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;estimated_questions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="complete-study-session">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Complete Study Session</a><a class="headerlink" href="#complete-study-session" title="Link to this heading"></a></h3>
<p>End a study session and record progress.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/progress-tracking/sessions/complete</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;session_id&quot;: &quot;session_12345&quot;,</span>
<span class="err">  &quot;actual_duration_minutes&quot;: 55,</span>
<span class="err">  &quot;topics_covered&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;topic&quot;: &quot;Access Control Models&quot;,</span>
<span class="err">      &quot;time_spent_minutes&quot;: 25,</span>
<span class="err">      &quot;confidence_level&quot;: 7,</span>
<span class="err">      &quot;difficulty_experienced&quot;: &quot;moderate&quot;</span>
<span class="err">    },</span>
<span class="err">    {</span>
<span class="err">      &quot;topic&quot;: &quot;Cryptographic Protocols&quot;,</span>
<span class="err">      &quot;time_spent_minutes&quot;: 30,</span>
<span class="err">      &quot;confidence_level&quot;: 6,</span>
<span class="err">      &quot;difficulty_experienced&quot;: &quot;challenging&quot;</span>
<span class="err">    }</span>
<span class="err">  ],</span>
<span class="err">  &quot;overall_session_rating&quot;: 8,</span>
<span class="err">  &quot;notes&quot;: &quot;Good progress on access control concepts. Need more practice with cryptographic implementations.&quot;,</span>
<span class="err">  &quot;interruptions&quot;: 2,</span>
<span class="err">  &quot;energy_level_end&quot;: &quot;medium&quot;,</span>
<span class="err">  &quot;practice_results&quot;: {</span>
<span class="err">    &quot;questions_attempted&quot;: 28,</span>
<span class="err">    &quot;questions_correct&quot;: 21,</span>
<span class="err">    &quot;accuracy_percentage&quot;: 75,</span>
<span class="err">    &quot;time_per_question_seconds&quot;: 90</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;session_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;session_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;completed_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T11:25:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;session_summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_duration_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">55</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;effective_study_time_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;topics_covered&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;overall_effectiveness&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;knowledge_gain_estimated&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.12</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;progress_update&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;certification_progress_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">67.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;topic_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;Access Control Models&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.75</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;Cryptographic Protocols&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.58</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;overall_knowledge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.68</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;progress_since_last_session&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.03</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;achievements_unlocked&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;achievement_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;consistent_learner_7_days&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Week Warrior&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Completed study sessions for 7 consecutive days&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;points_awarded&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;badge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bronze&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;ai_insights&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;performance_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Strong improvement in access control understanding&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;areas_for_focus&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Cryptographic implementations need more practice&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Consider reviewing symmetric vs asymmetric encryption&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;next_session_recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Focus on cryptography hands-on exercises&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Review previous session notes before starting&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;study_pattern_insights&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Most effective during morning sessions&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;predicted_exam_readiness&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.72</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;statistics_updated&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_study_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">145.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;sessions_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">23</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_session_duration&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">63</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;current_study_streak&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="learning-analytics">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Learning Analytics</a><a class="headerlink" href="#learning-analytics" title="Link to this heading"></a></h2>
<section id="get-progress-overview">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Get Progress Overview</a><a class="headerlink" href="#get-progress-overview" title="Link to this heading"></a></h3>
<p>Retrieve comprehensive progress analytics for a user.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/progress-tracking/analytics/overview</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?period=30d&amp;certification_id=45&amp;include_predictions=true</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;30d&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;certification_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;progress_summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;overall_progress_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">67.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;knowledge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.68</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;study_hours_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">145.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;study_hours_planned&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">200</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;sessions_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">23</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;days_studied&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;current_streak&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;longest_streak&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;learning_velocity&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;average_hours_per_week&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">12.1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;progress_rate_per_hour&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.0047</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;estimated_completion_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-04-15&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;pace_compared_to_plan&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ahead&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;velocity_trend&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;increasing&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;topic_breakdown&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;topic&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security and Risk Management&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;progress_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">85</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;confidence_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;time_invested_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">25</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_studied&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-14T15:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;mastery_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;proficient&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;topic&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Asset Security&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;progress_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">72</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;confidence_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">7.1</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;time_invested_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_studied&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;mastery_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;developing&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;performance_metrics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;average_session_effectiveness&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;knowledge_retention_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;practice_test_average&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">76.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;improvement_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;consistency_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.7</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;study_patterns&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;most_productive_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;09:00-11:00&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;optimal_session_length&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">55</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;preferred_study_methods&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;practice_questions&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;flashcards&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;best_performing_topics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Risk Management&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Governance&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;challenging_topics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Cryptography&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Software Development Security&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;predictions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;exam_readiness_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.72</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;predicted_exam_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">78</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;confidence_interval&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="mi">72</span><span class="p">,</span><span class="w"> </span><span class="mi">84</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;recommended_exam_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-05-01&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.82</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;areas_needing_focus&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Cryptography implementation&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Software security testing&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="detailed-session-history">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Detailed Session History</a><a class="headerlink" href="#detailed-session-history" title="Link to this heading"></a></h3>
<p>Get comprehensive study session history with analytics.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/progress-tracking/sessions/history</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?start_date=2025-01-01&amp;end_date=2025-01-15&amp;include_analytics=true</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;start_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-01T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;end_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T23:59:59Z&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;total_sessions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total_study_time_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">825</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;sessions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;session_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;session_12345&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;duration_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">55</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;topics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Access Control&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Cryptography&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;study_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;practice_questions&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;effectiveness_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;knowledge_gain&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.12</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;session_rating&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;achievements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Week Warrior&quot;</span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;analytics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;average_session_duration&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">55</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;most_studied_topics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;topic&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Management&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;sessions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;total_time_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">420</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;study_consistency&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;days_with_study&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;longest_streak&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;average_daily_study_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">68.75</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;performance_trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;effectiveness_trend&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;improving&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;knowledge_retention&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;session_rating_average&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">7.8</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="achievement-system">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Achievement System</a><a class="headerlink" href="#achievement-system" title="Link to this heading"></a></h2>
<section id="get-user-achievements">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Get User Achievements</a><a class="headerlink" href="#get-user-achievements" title="Link to this heading"></a></h3>
<p>Retrieve all achievements and progress towards goals.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/progress-tracking/achievements</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?include_progress=true&amp;category=all</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;achievement_summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_achievements&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_points&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2350</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;current_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Advanced Learner&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;next_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Expert Scholar&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;points_to_next_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">650</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;badges_earned&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;achievements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;achievement_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;first_session&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Getting Started&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Complete your first study session&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;milestone&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;points&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;badge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bronze&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;earned_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-12-01T10:00:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;rarity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;common&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;achievement_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;consistent_learner_30_days&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Monthly Master&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Study consistently for 30 days&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;consistency&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;points&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;badge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;gold&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;earned_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-10T15:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;rarity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;rare&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;in_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;achievement_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;knowledge_expert&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Knowledge Expert&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Achieve 90% knowledge level in any certification&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;mastery&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;points&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;badge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;platinum&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;current_progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.75</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;target_knowledge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.9</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;current_knowledge_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.68</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;estimated_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-03-15&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;available_achievements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;achievement_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;speed_learner&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Speed Learner&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Complete a certification in under 3 months&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;efficiency&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;points&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">750</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Complete any certification within 90 days&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;difficulty&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;challenging&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;leaderboard_position&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;overall_rank&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">23</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;monthly_rank&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;category_ranks&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;study_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;consistency&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;achievements&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">23</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="goal-management">
<h2><a class="toc-backref" href="#id10" role="doc-backlink">Goal Management</a><a class="headerlink" href="#goal-management" title="Link to this heading"></a></h2>
<section id="create-learning-goal">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Create Learning Goal</a><a class="headerlink" href="#create-learning-goal" title="Link to this heading"></a></h3>
<p>Set up a new learning objective with tracking.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/progress-tracking/goals</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;goal_type&quot;: &quot;certification&quot;,</span>
<span class="err">  &quot;title&quot;: &quot;Achieve CISSP Certification&quot;,</span>
<span class="err">  &quot;description&quot;: &quot;Complete CISSP certification within 6 months with 85% exam score target&quot;,</span>
<span class="err">  &quot;certification_id&quot;: 45,</span>
<span class="err">  &quot;target_date&quot;: &quot;2025-07-15&quot;,</span>
<span class="err">  &quot;target_metrics&quot;: {</span>
<span class="err">    &quot;exam_score_target&quot;: 85,</span>
<span class="err">    &quot;study_hours_target&quot;: 200,</span>
<span class="err">    &quot;knowledge_level_target&quot;: 0.9</span>
<span class="err">  },</span>
<span class="err">  &quot;study_schedule&quot;: {</span>
<span class="err">    &quot;hours_per_week&quot;: 12,</span>
<span class="err">    &quot;preferred_days&quot;: [&quot;monday&quot;, &quot;wednesday&quot;, &quot;friday&quot;, &quot;sunday&quot;],</span>
<span class="err">    &quot;preferred_times&quot;: [&quot;09:00-11:00&quot;, &quot;19:00-21:00&quot;]</span>
<span class="err">  },</span>
<span class="err">  &quot;milestones&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;title&quot;: &quot;Complete Domain 1-4&quot;,</span>
<span class="err">      &quot;target_date&quot;: &quot;2025-04-01&quot;,</span>
<span class="err">      &quot;description&quot;: &quot;Master first four CISSP domains&quot;</span>
<span class="err">    },</span>
<span class="err">    {</span>
<span class="err">      &quot;title&quot;: &quot;Practice Test Readiness&quot;,</span>
<span class="err">      &quot;target_date&quot;: &quot;2025-06-01&quot;,</span>
<span class="err">      &quot;description&quot;: &quot;Consistently score 80%+ on practice tests&quot;</span>
<span class="err">    }</span>
<span class="err">  ]</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;goal_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;goal_789&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;goal_details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Achieve CISSP Certification&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;progress_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;target_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-07-15&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;days_remaining&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">181</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;estimated_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-07-10&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;tracking_setup&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;automatic_progress_tracking&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;milestone_notifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;weekly_progress_reports&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;ai_recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;initial_plan&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_study_hours_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">200</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;weekly_study_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;estimated_weeks_to_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">17</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;milestones&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;milestone_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;milestone_1&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Complete Domain 1-4&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;target_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-04-01&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;pending&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;ai_insights&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;goal_feasibility&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;realistic&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;recommended_adjustments&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Consider adding buffer time for difficult topics&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Schedule regular practice test sessions&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;success_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Consistent study schedule&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Regular progress reviews&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Active practice testing&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="update-goal-progress">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Update Goal Progress</a><a class="headerlink" href="#update-goal-progress" title="Link to this heading"></a></h3>
<p>Manually update progress towards learning goals.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">PATCH /api/v1/progress-tracking/goals/{goal_id}</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;progress_update&quot;: {</span>
<span class="err">    &quot;current_progress_percentage&quot;: 45,</span>
<span class="err">    &quot;study_hours_completed&quot;: 90,</span>
<span class="err">    &quot;knowledge_level_current&quot;: 0.65</span>
<span class="err">  },</span>
<span class="err">  &quot;milestone_updates&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;milestone_id&quot;: &quot;milestone_1&quot;,</span>
<span class="err">      &quot;status&quot;: &quot;completed&quot;,</span>
<span class="err">      &quot;completed_at&quot;: &quot;2025-03-28T14:00:00Z&quot;,</span>
<span class="err">      &quot;notes&quot;: &quot;Completed all four domains ahead of schedule&quot;</span>
<span class="err">    }</span>
<span class="err">  ],</span>
<span class="err">  &quot;notes&quot;: &quot;Making excellent progress. Cryptography domain was challenging but now confident.&quot;,</span>
<span class="err">  &quot;schedule_adjustments&quot;: {</span>
<span class="err">    &quot;increase_weekly_hours&quot;: 2,</span>
<span class="err">    &quot;add_practice_test_sessions&quot;: true</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-analytics">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">Performance Analytics</a><a class="headerlink" href="#performance-analytics" title="Link to this heading"></a></h2>
<section id="learning-effectiveness-analysis">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Learning Effectiveness Analysis</a><a class="headerlink" href="#learning-effectiveness-analysis" title="Link to this heading"></a></h3>
<p>Analyse learning patterns and effectiveness.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/progress-tracking/analytics/effectiveness</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?period=90d&amp;include_recommendations=true</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;analysis_period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;90d&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;effectiveness_metrics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;overall_effectiveness_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;knowledge_retention_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;study_efficiency&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.72</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;consistency_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.7</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;improvement_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.12</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;learning_patterns&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;optimal_study_times&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;time_range&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;09:00-11:00&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;effectiveness_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.89</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;frequency&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.65</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;best_session_duration&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;optimal_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">55</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;effectiveness_at_optimal&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.82</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;diminishing_returns_after&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">75</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;most_effective_methods&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;method&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;practice_questions&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;effectiveness&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;knowledge_retention&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.88</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;method&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active_recall&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;effectiveness&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.82</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;knowledge_retention&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.91</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;performance_trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;effectiveness_trend&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;improving&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;monthly_improvement&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.08</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;consistency_trend&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;stable&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;knowledge_growth_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.15</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;schedule_optimisation&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Focus study sessions between 9-11 AM for maximum effectiveness&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Maintain 55-minute session length for optimal retention&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;method_improvements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Increase practice question sessions to 60% of study time&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Add spaced repetition for challenging topics&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;consistency_tips&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Set up study session reminders&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Prepare study materials in advance&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="error-handling">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>Progress Tracking API error responses:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;session_not_found&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Study session not found or access denied&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">404</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;session_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;session_12345&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Common Error Codes</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">400</span></code> - Invalid session or goal parameters</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">404</span></code> - Session, goal, or achievement not found</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">409</span></code> - Conflicting session state (already completed)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">422</span></code> - Invalid progress data or metrics</p></li>
</ul>
</section>
<section id="see-also">
<h2><a class="toc-backref" href="#id16" role="doc-backlink">See Also</a><a class="headerlink" href="#see-also" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../guides/user_guide.html"><span class="doc">🎯 Complete User Guide</span></a> - Progress tracking user guide</p></li>
<li><p><a class="reference internal" href="ai_assistant.html"><span class="doc">AI Study Assistant API</span></a> - AI-powered learning insights</p></li>
<li><p><a class="reference internal" href="authentication.html"><span class="doc">Authentication &amp; Security</span></a> - API authentication</p></li>
<li><p><span class="xref std std-doc">../enterprise/analytics</span> - Enterprise progress analytics</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="career_framework.html" class="btn btn-neutral float-left" title="Career Framework API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="integration_hub.html" class="btn btn-neutral float-right" title="Integration Hub API" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>