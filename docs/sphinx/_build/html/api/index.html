<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>API Reference &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/index.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Authentication &amp; Security" href="authentication.html" />
    <link rel="prev" title="🏢 Enterprise Dashboard" href="../enterprise/dashboard.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="#interactive-documentation">Interactive Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="#api-documentation-sections">API Documentation Sections</a><ul>
<li class="toctree-l3"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l3"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l3"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l3"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l3"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l3"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l3"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#core-api-modules">Core API Modules</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#cost-calculator-api">Cost Calculator API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#enhanced-security-taxonomy-api">Enhanced Security Taxonomy API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#security-career-framework-api">Security Career Framework API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#study-timer-api">Study Timer API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#integration-hub-api">Integration Hub API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#ai-study-assistant-api">AI Study Assistant API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#progress-tracking-api">Progress Tracking API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#enterprise-dashboard-api">Enterprise Dashboard API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#mobile-enterprise-api">Mobile Enterprise API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#career-transition-api">Career Transition API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#common-response-formats">Common Response Formats</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#success-response">Success Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="#error-response">Error Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pagination">Pagination</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="#error-codes">Error Codes</a></li>
<li class="toctree-l2"><a class="reference internal" href="#sdk-and-client-libraries">SDK and Client Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="#webhooks">Webhooks</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../DOCUMENTATION_ENHANCEMENT_SUMMARY.html">Documentation Enhancement Summary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/sphinx-setup.html">🛠️ Sphinx Documentation Setup Guide</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">API Reference</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/index.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="api-reference">
<h1>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading"></a></h1>
<p>CertPathFinder provides a comprehensive REST API for all platform functionality. The API is built with FastAPI and provides automatic OpenAPI documentation with full British English localisation.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#base-url" id="id1">Base URL</a></p></li>
<li><p><a class="reference internal" href="#interactive-documentation" id="id2">Interactive Documentation</a></p></li>
<li><p><a class="reference internal" href="#authentication" id="id3">Authentication</a></p></li>
<li><p><a class="reference internal" href="#api-documentation-sections" id="id4">API Documentation Sections</a></p></li>
<li><p><a class="reference internal" href="#core-api-modules" id="id5">Core API Modules</a></p>
<ul>
<li><p><a class="reference internal" href="#cost-calculator-api" id="id6">Cost Calculator API</a></p></li>
<li><p><a class="reference internal" href="#enhanced-security-taxonomy-api" id="id7">Enhanced Security Taxonomy API</a></p></li>
<li><p><a class="reference internal" href="#security-career-framework-api" id="id8">Security Career Framework API</a></p></li>
<li><p><a class="reference internal" href="#study-timer-api" id="id9">Study Timer API</a></p></li>
<li><p><a class="reference internal" href="#integration-hub-api" id="id10">Integration Hub API</a></p></li>
<li><p><a class="reference internal" href="#ai-study-assistant-api" id="id11">AI Study Assistant API</a></p></li>
<li><p><a class="reference internal" href="#progress-tracking-api" id="id12">Progress Tracking API</a></p></li>
<li><p><a class="reference internal" href="#enterprise-dashboard-api" id="id13">Enterprise Dashboard API</a></p></li>
<li><p><a class="reference internal" href="#mobile-enterprise-api" id="id14">Mobile Enterprise API</a></p></li>
<li><p><a class="reference internal" href="#career-transition-api" id="id15">Career Transition API</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#common-response-formats" id="id16">Common Response Formats</a></p>
<ul>
<li><p><a class="reference internal" href="#success-response" id="id17">Success Response</a></p></li>
<li><p><a class="reference internal" href="#error-response" id="id18">Error Response</a></p></li>
<li><p><a class="reference internal" href="#pagination" id="id19">Pagination</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#rate-limiting" id="id20">Rate Limiting</a></p></li>
<li><p><a class="reference internal" href="#error-codes" id="id21">Error Codes</a></p></li>
<li><p><a class="reference internal" href="#sdk-and-client-libraries" id="id22">SDK and Client Libraries</a></p></li>
<li><p><a class="reference internal" href="#webhooks" id="id23">Webhooks</a></p></li>
</ul>
</nav>
<section id="base-url">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Base URL</a><a class="headerlink" href="#base-url" title="Link to this heading"></a></h2>
<p>All API endpoints are available under the base URL:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http://localhost:8000/api/v1
</pre></div>
</div>
</section>
<section id="interactive-documentation">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Interactive Documentation</a><a class="headerlink" href="#interactive-documentation" title="Link to this heading"></a></h2>
<p>CertPathFinder provides interactive API documentation:</p>
<ul class="simple">
<li><p><strong>Swagger UI</strong>: <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a> - Interactive API testing interface</p></li>
<li><p><strong>ReDoc</strong>: <a class="reference external" href="http://localhost:8000/redoc">http://localhost:8000/redoc</a> - Clean, responsive API documentation</p></li>
<li><p><strong>OpenAPI Schema</strong>: <a class="reference external" href="http://localhost:8000/openapi.json">http://localhost:8000/openapi.json</a> - Machine-readable API specification</p></li>
</ul>
</section>
<section id="authentication">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">Authentication</a><a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>Most API endpoints require authentication using JWT tokens. See <a class="reference internal" href="authentication.html"><span class="doc">Authentication &amp; Security</span></a> for comprehensive details.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">requests</span>

<span class="c1"># Authenticate to get access token</span>
<span class="n">auth_response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/auth/login&quot;</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;username&quot;</span><span class="p">:</span> <span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
        <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;your_password&quot;</span>
    <span class="p">}</span>
<span class="p">)</span>

<span class="n">token</span> <span class="o">=</span> <span class="n">auth_response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s2">&quot;access_token&quot;</span><span class="p">]</span>

<span class="c1"># Use token for all API requests</span>
<span class="n">headers</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Bearer </span><span class="si">{</span><span class="n">token</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
    <span class="s2">&quot;Content-Type&quot;</span><span class="p">:</span> <span class="s2">&quot;application/json&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="api-documentation-sections">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">API Documentation Sections</a><a class="headerlink" href="#api-documentation-sections" title="Link to this heading"></a></h2>
<p>The API documentation is organised into the following sections:</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a><ul>
<li class="toctree-l2"><a class="reference internal" href="authentication.html#authentication-methods">Authentication Methods</a></li>
<li class="toctree-l2"><a class="reference internal" href="authentication.html#access-control-permissions">Access Control &amp; Permissions</a></li>
<li class="toctree-l2"><a class="reference internal" href="authentication.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="authentication.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="authentication.html#testing-authentication">Testing Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="authentication.html#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ai_assistant.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="ai_assistant.html#core-endpoints">Core Endpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="ai_assistant.html#learning-analytics">Learning Analytics</a></li>
<li class="toctree-l2"><a class="reference internal" href="ai_assistant.html#model-training-customisation">Model Training &amp; Customisation</a></li>
<li class="toctree-l2"><a class="reference internal" href="ai_assistant.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="ai_assistant.html#privacy-security">Privacy &amp; Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="ai_assistant.html#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#organisation-management">Organisation Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#department-management">Department Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#user-management">User Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#analytics-reporting">Analytics &amp; Reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#licence-management">Licence Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#compliance-audit">Compliance &amp; Audit</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="enterprise.html#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="cost_calculator.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="cost_calculator.html#core-calculations">Core Calculations</a></li>
<li class="toctree-l2"><a class="reference internal" href="cost_calculator.html#scenario-modelling">Scenario Modelling</a></li>
<li class="toctree-l2"><a class="reference internal" href="cost_calculator.html#budget-planning">Budget Planning</a></li>
<li class="toctree-l2"><a class="reference internal" href="cost_calculator.html#historical-analysis">Historical Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="cost_calculator.html#currency-localisation">Currency &amp; Localisation</a></li>
<li class="toctree-l2"><a class="reference internal" href="cost_calculator.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="cost_calculator.html#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="career_framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_framework.html#career-analysis">Career Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_framework.html#job-market-intelligence">Job Market Intelligence</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_framework.html#skills-development">Skills Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_framework.html#career-path-planning">Career Path Planning</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_framework.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="career_framework.html#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="progress_tracking.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="progress_tracking.html#study-session-management">Study Session Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="progress_tracking.html#learning-analytics">Learning Analytics</a></li>
<li class="toctree-l2"><a class="reference internal" href="progress_tracking.html#achievement-system">Achievement System</a></li>
<li class="toctree-l2"><a class="reference internal" href="progress_tracking.html#goal-management">Goal Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="progress_tracking.html#performance-analytics">Performance Analytics</a></li>
<li class="toctree-l2"><a class="reference internal" href="progress_tracking.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="progress_tracking.html#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#learning-management-system-integration">Learning Management System Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#single-sign-on-sso-integration">Single Sign-On (SSO) Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#webhook-management">Webhook Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#data-synchronisation">Data Synchronisation</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#third-party-connectors">Third-party Connectors</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#integration-monitoring">Integration Monitoring</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="integration_hub.html#see-also">See Also</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="core-api-modules">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Core API Modules</a><a class="headerlink" href="#core-api-modules" title="Link to this heading"></a></h2>
<section id="cost-calculator-api">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Cost Calculator API</a><a class="headerlink" href="#cost-calculator-api" title="Link to this heading"></a></h3>
<p>Calculate certification costs and ROI analysis.</p>
</section>
<section id="enhanced-security-taxonomy-api">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Enhanced Security Taxonomy API</a><a class="headerlink" href="#enhanced-security-taxonomy-api" title="Link to this heading"></a></h3>
<p>Comprehensive security taxonomy with skills, certifications, and job titles.</p>
</section>
<section id="security-career-framework-api">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Security Career Framework API</a><a class="headerlink" href="#security-career-framework-api" title="Link to this heading"></a></h3>
<p>Career progression and framework based on Paul Jerimy’s security areas.</p>
</section>
<section id="study-timer-api">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Study Timer API</a><a class="headerlink" href="#study-timer-api" title="Link to this heading"></a></h3>
<p>Study session tracking and productivity management.</p>
</section>
<section id="integration-hub-api">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Integration Hub API</a><a class="headerlink" href="#integration-hub-api" title="Link to this heading"></a></h3>
<p>Enterprise integrations for SSO, LDAP, LMS, and HR systems.</p>
</section>
<section id="ai-study-assistant-api">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">AI Study Assistant API</a><a class="headerlink" href="#ai-study-assistant-api" title="Link to this heading"></a></h3>
<p>AI-powered study recommendations and assistance.</p>
</section>
<section id="progress-tracking-api">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Progress Tracking API</a><a class="headerlink" href="#progress-tracking-api" title="Link to this heading"></a></h3>
<p>Learning progress, goals, and achievement tracking.</p>
</section>
<section id="enterprise-dashboard-api">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">Enterprise Dashboard API</a><a class="headerlink" href="#enterprise-dashboard-api" title="Link to this heading"></a></h3>
<p>Multi-tenant organization management and analytics.</p>
</section>
<section id="mobile-enterprise-api">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Mobile Enterprise API</a><a class="headerlink" href="#mobile-enterprise-api" title="Link to this heading"></a></h3>
<p>Mobile-optimized endpoints with offline capabilities.</p>
</section>
<section id="career-transition-api">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">Career Transition API</a><a class="headerlink" href="#career-transition-api" title="Link to this heading"></a></h3>
<p>Career change planning and guidance.</p>
</section>
</section>
<section id="common-response-formats">
<h2><a class="toc-backref" href="#id16" role="doc-backlink">Common Response Formats</a><a class="headerlink" href="#common-response-formats" title="Link to this heading"></a></h2>
<section id="success-response">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Success Response</a><a class="headerlink" href="#success-response" title="Link to this heading"></a></h3>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;success&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Example Resource&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Operation completed successfully&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="error-response">
<h3><a class="toc-backref" href="#id18" role="doc-backlink">Error Response</a><a class="headerlink" href="#error-response" title="Link to this heading"></a></h3>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;error&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Invalid input data&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;field&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;email&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;issue&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Invalid email format&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="pagination">
<h3><a class="toc-backref" href="#id19" role="doc-backlink">Pagination</a><a class="headerlink" href="#pagination" title="Link to this heading"></a></h3>
<p>List endpoints support pagination:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Item 1&quot;</span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Item 2&quot;</span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">150</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;page&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">20</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;pages&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="rate-limiting">
<h2><a class="toc-backref" href="#id20" role="doc-backlink">Rate Limiting</a><a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<p>API endpoints are rate limited to ensure fair usage:</p>
<ul class="simple">
<li><p><strong>Authenticated users</strong>: 1000 requests per hour</p></li>
<li><p><strong>Anonymous users</strong>: 100 requests per hour</p></li>
<li><p><strong>Enterprise users</strong>: 10000 requests per hour</p></li>
</ul>
<p>Rate limit headers are included in responses:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
</pre></div>
</div>
</section>
<section id="error-codes">
<h2><a class="toc-backref" href="#id21" role="doc-backlink">Error Codes</a><a class="headerlink" href="#error-codes" title="Link to this heading"></a></h2>
<p>Common HTTP status codes used by the API:</p>
<ul class="simple">
<li><p><strong>200 OK</strong>: Request successful</p></li>
<li><p><strong>201 Created</strong>: Resource created successfully</p></li>
<li><p><strong>400 Bad Request</strong>: Invalid request data</p></li>
<li><p><strong>401 Unauthorized</strong>: Authentication required</p></li>
<li><p><strong>403 Forbidden</strong>: Insufficient permissions</p></li>
<li><p><strong>404 Not Found</strong>: Resource not found</p></li>
<li><p><strong>422 Unprocessable Entity</strong>: Validation error</p></li>
<li><p><strong>429 Too Many Requests</strong>: Rate limit exceeded</p></li>
<li><p><strong>500 Internal Server Error</strong>: Server error</p></li>
</ul>
</section>
<section id="sdk-and-client-libraries">
<h2><a class="toc-backref" href="#id22" role="doc-backlink">SDK and Client Libraries</a><a class="headerlink" href="#sdk-and-client-libraries" title="Link to this heading"></a></h2>
<p>Official client libraries are available for:</p>
<ul class="simple">
<li><p><strong>Python</strong>: <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span> <span class="pre">certpathfinder-client</span></code></p></li>
<li><p><strong>JavaScript/TypeScript</strong>: <code class="docutils literal notranslate"><span class="pre">npm</span> <span class="pre">install</span> <span class="pre">certpathfinder-client</span></code></p></li>
<li><p><strong>Go</strong>: <code class="docutils literal notranslate"><span class="pre">go</span> <span class="pre">get</span> <span class="pre">github.com/certpathfinder/go-client</span></code></p></li>
</ul>
<p>Example usage with Python client:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">certpathfinder</span> <span class="kn">import</span> <span class="n">CertPathFinderClient</span>

<span class="n">client</span> <span class="o">=</span> <span class="n">CertPathFinderClient</span><span class="p">(</span>
    <span class="n">base_url</span><span class="o">=</span><span class="s2">&quot;http://localhost:8000&quot;</span><span class="p">,</span>
    <span class="n">api_key</span><span class="o">=</span><span class="s2">&quot;your-api-key&quot;</span>
<span class="p">)</span>

<span class="c1"># Get certifications</span>
<span class="n">certifications</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">certifications</span><span class="o">.</span><span class="n">list</span><span class="p">()</span>

<span class="c1"># Calculate costs</span>
<span class="n">cost_analysis</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">cost_calculator</span><span class="o">.</span><span class="n">calculate</span><span class="p">(</span>
    <span class="n">certification_id</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
    <span class="n">location</span><span class="o">=</span><span class="s2">&quot;United States&quot;</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="webhooks">
<h2><a class="toc-backref" href="#id23" role="doc-backlink">Webhooks</a><a class="headerlink" href="#webhooks" title="Link to this heading"></a></h2>
<p>CertPathFinder supports webhooks for real-time notifications:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Register webhook</span>
<span class="n">webhook_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;url&quot;</span><span class="p">:</span> <span class="s2">&quot;https://your-app.com/webhooks/certpathfinder&quot;</span><span class="p">,</span>
    <span class="s2">&quot;events&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;certification.completed&quot;</span><span class="p">,</span> <span class="s2">&quot;goal.achieved&quot;</span><span class="p">],</span>
    <span class="s2">&quot;secret&quot;</span><span class="p">:</span> <span class="s2">&quot;your-webhook-secret&quot;</span>
<span class="p">}</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/api/v1/webhooks&quot;</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">webhook_data</span><span class="p">)</span>
</pre></div>
</div>
<p>Supported webhook events:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user.registered</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">certification.started</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">certification.completed</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">goal.created</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">goal.achieved</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">study_session.completed</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">assessment.completed</span></code></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../enterprise/dashboard.html" class="btn btn-neutral float-left" title="🏢 Enterprise Dashboard" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="authentication.html" class="btn btn-neutral float-right" title="Authentication &amp; Security" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>