<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Authentication &amp; Security &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/authentication.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="AI Study Assistant API" href="ai_assistant.html" />
    <link rel="prev" title="API Reference" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Authentication &amp; Security</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#authentication-methods">Authentication Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#token-based-authentication">Token-Based Authentication</a></li>
<li class="toctree-l3"><a class="reference internal" href="#python-example">Python Example</a></li>
<li class="toctree-l3"><a class="reference internal" href="#token-refresh">Token Refresh</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#access-control-permissions">Access Control &amp; Permissions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#role-based-access-control-rbac">Role-Based Access Control (RBAC)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#multi-tenant-security">Multi-Tenant Security</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security-best-practices">Security Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#token-security">Token Security</a></li>
<li class="toctree-l3"><a class="reference internal" href="#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#authentication-errors">Authentication Errors</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#testing-authentication">Testing Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Authentication &amp; Security</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/authentication.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="authentication-security">
<h1>Authentication &amp; Security<a class="headerlink" href="#authentication-security" title="Link to this heading"></a></h1>
<p>CertPathFinder uses token-based authentication to secure API access. This section covers authentication methods, security best practices, and access control.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#authentication-methods" id="id3">Authentication Methods</a></p>
<ul>
<li><p><a class="reference internal" href="#token-based-authentication" id="id4">Token-Based Authentication</a></p></li>
<li><p><a class="reference internal" href="#python-example" id="id5">Python Example</a></p></li>
<li><p><a class="reference internal" href="#token-refresh" id="id6">Token Refresh</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#access-control-permissions" id="id7">Access Control &amp; Permissions</a></p>
<ul>
<li><p><a class="reference internal" href="#role-based-access-control-rbac" id="id8">Role-Based Access Control (RBAC)</a></p></li>
<li><p><a class="reference internal" href="#multi-tenant-security" id="id9">Multi-Tenant Security</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#security-best-practices" id="id10">Security Best Practices</a></p>
<ul>
<li><p><a class="reference internal" href="#token-security" id="id11">Token Security</a></p></li>
<li><p><a class="reference internal" href="#rate-limiting" id="id12">Rate Limiting</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-handling" id="id13">Error Handling</a></p>
<ul>
<li><p><a class="reference internal" href="#authentication-errors" id="id14">Authentication Errors</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#testing-authentication" id="id15">Testing Authentication</a></p></li>
<li><p><a class="reference internal" href="#see-also" id="id16">See Also</a></p></li>
</ul>
</nav>
<section id="authentication-methods">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">Authentication Methods</a><a class="headerlink" href="#authentication-methods" title="Link to this heading"></a></h2>
<section id="token-based-authentication">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Token-Based Authentication</a><a class="headerlink" href="#token-based-authentication" title="Link to this heading"></a></h3>
<p>The primary authentication method uses JWT (JSON Web Tokens) for secure API access.</p>
<p><strong>Login Endpoint</strong></p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/auth/login</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;username&quot;: &quot;<EMAIL>&quot;,</span>
<span class="err">  &quot;password&quot;: &quot;your_secure_password&quot;</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;access_token&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;token_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bearer&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expires_in&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3600</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expires_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;permissions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;read&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;write&quot;</span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Using the Token</strong></p>
<p>Include the token in the Authorization header for all subsequent requests:</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/certifications</span>
<span class="err">Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...</span>
<span class="err">Content-Type: application/json</span>
</pre></div>
</div>
</section>
<section id="python-example">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Python Example</a><a class="headerlink" href="#python-example" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">requests</span>
<span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timedelta</span>

<span class="k">class</span> <span class="nc">CertPathFinderClient</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">base_url</span><span class="p">,</span> <span class="n">username</span><span class="p">,</span> <span class="n">password</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">base_url</span> <span class="o">=</span> <span class="n">base_url</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">username</span> <span class="o">=</span> <span class="n">username</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">password</span> <span class="o">=</span> <span class="n">password</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">token</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">token_expires</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">authenticate</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Authenticate and obtain access token&quot;&quot;&quot;</span>
        <span class="n">auth_data</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;username&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">username</span><span class="p">,</span>
            <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">password</span>
        <span class="p">}</span>

        <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
            <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">base_url</span><span class="si">}</span><span class="s2">/api/v1/auth/login&quot;</span><span class="p">,</span>
            <span class="n">json</span><span class="o">=</span><span class="n">auth_data</span>
        <span class="p">)</span>

        <span class="k">if</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">200</span><span class="p">:</span>
            <span class="n">token_data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">token</span> <span class="o">=</span> <span class="n">token_data</span><span class="p">[</span><span class="s2">&quot;access_token&quot;</span><span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">token_expires</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">fromisoformat</span><span class="p">(</span>
                <span class="n">token_data</span><span class="p">[</span><span class="s2">&quot;expires_at&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s1">&#39;Z&#39;</span><span class="p">,</span> <span class="s1">&#39;+00:00&#39;</span><span class="p">)</span>
            <span class="p">)</span>
            <span class="k">return</span> <span class="kc">True</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Authentication failed: </span><span class="si">{</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">get_headers</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get headers with valid token&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">token</span> <span class="ow">or</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">token_expires</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">authenticate</span><span class="p">()</span>

        <span class="k">return</span> <span class="p">{</span>
            <span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Bearer </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">token</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="s2">&quot;Content-Type&quot;</span><span class="p">:</span> <span class="s2">&quot;application/json&quot;</span>
        <span class="p">}</span>

    <span class="k">def</span> <span class="nf">make_request</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">method</span><span class="p">,</span> <span class="n">endpoint</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Make authenticated request&quot;&quot;&quot;</span>
        <span class="n">headers</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_headers</span><span class="p">()</span>
        <span class="n">url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">base_url</span><span class="si">}</span><span class="s2">/api/v1</span><span class="si">{</span><span class="n">endpoint</span><span class="si">}</span><span class="s2">&quot;</span>

        <span class="k">return</span> <span class="n">requests</span><span class="o">.</span><span class="n">request</span><span class="p">(</span><span class="n">method</span><span class="p">,</span> <span class="n">url</span><span class="p">,</span> <span class="n">headers</span><span class="o">=</span><span class="n">headers</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>

<span class="c1"># Usage example</span>
<span class="n">client</span> <span class="o">=</span> <span class="n">CertPathFinderClient</span><span class="p">(</span>
    <span class="n">base_url</span><span class="o">=</span><span class="s2">&quot;http://localhost:8000&quot;</span><span class="p">,</span>
    <span class="n">username</span><span class="o">=</span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
    <span class="n">password</span><span class="o">=</span><span class="s2">&quot;secure_password&quot;</span>
<span class="p">)</span>

<span class="c1"># Make authenticated requests</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">make_request</span><span class="p">(</span><span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;/certifications&quot;</span><span class="p">)</span>
<span class="n">certifications</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="token-refresh">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Token Refresh</a><a class="headerlink" href="#token-refresh" title="Link to this heading"></a></h3>
<p>Tokens expire after a configured period (default: 30 minutes). Refresh tokens before expiry:</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/auth/refresh</span>
<span class="err">Authorization: Bearer your_current_token</span>
</pre></div>
</div>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;access_token&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;new_jwt_token_here&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expires_in&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3600</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expires_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T11:30:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="access-control-permissions">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">Access Control &amp; Permissions</a><a class="headerlink" href="#access-control-permissions" title="Link to this heading"></a></h2>
<section id="role-based-access-control-rbac">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Role-Based Access Control (RBAC)</a><a class="headerlink" href="#role-based-access-control-rbac" title="Link to this heading"></a></h3>
<p>CertPathFinder implements a hierarchical permission system:</p>
<p><strong>Permission Levels</strong></p>
<ol class="arabic simple">
<li><p><strong>Guest</strong> - Read-only access to public certifications</p></li>
<li><p><strong>User</strong> - Full personal profile and learning features</p></li>
<li><p><strong>Premium</strong> - Advanced AI features and analytics</p></li>
<li><p><strong>Admin</strong> - Organisation management capabilities</p></li>
<li><p><strong>Super Admin</strong> - Full system administration</p></li>
<li><p><strong>Enterprise</strong> - Multi-tenant organisation control</p></li>
</ol>
<p><strong>Permission Matrix</strong></p>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">API Permissions by Role</span><a class="headerlink" href="#id1" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 30.0%" />
<col style="width: 15.0%" />
<col style="width: 15.0%" />
<col style="width: 15.0%" />
<col style="width: 15.0%" />
<col style="width: 10.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Endpoint</p></th>
<th class="head"><p>Guest</p></th>
<th class="head"><p>User</p></th>
<th class="head"><p>Premium</p></th>
<th class="head"><p>Admin</p></th>
<th class="head"><p>Super Admin</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">GET</span> <span class="pre">/certifications</span></code></p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">POST</span> <span class="pre">/user/profile</span></code></p></td>
<td><p>✗</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">GET</span> <span class="pre">/ai-assistant/*</span></code></p></td>
<td><p>✗</p></td>
<td><p>Limited</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">POST</span> <span class="pre">/enterprise/*</span></code></p></td>
<td><p>✗</p></td>
<td><p>✗</p></td>
<td><p>✗</p></td>
<td><p>✓</p></td>
<td><p>✓</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DELETE</span> <span class="pre">/admin/*</span></code></p></td>
<td><p>✗</p></td>
<td><p>✗</p></td>
<td><p>✗</p></td>
<td><p>✗</p></td>
<td><p>✓</p></td>
</tr>
</tbody>
</table>
</section>
<section id="multi-tenant-security">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Multi-Tenant Security</a><a class="headerlink" href="#multi-tenant-security" title="Link to this heading"></a></h3>
<p>For enterprise deployments, data isolation is enforced at multiple levels:</p>
<p><strong>Organisation Isolation</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Each request includes organisation context</span>
<span class="n">headers</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer token&quot;</span><span class="p">,</span>
    <span class="s2">&quot;X-Organisation-ID&quot;</span><span class="p">:</span> <span class="s2">&quot;org_12345&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Data Access Patterns</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- All queries automatically include organisation filter</span>
<span class="k">SELECT</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">certifications</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">organisation_id</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">:</span><span class="n">current_org_id</span>
<span class="k">AND</span><span class="w"> </span><span class="n">deleted_at</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span>
</pre></div>
</div>
</section>
</section>
<section id="security-best-practices">
<h2><a class="toc-backref" href="#id10" role="doc-backlink">Security Best Practices</a><a class="headerlink" href="#security-best-practices" title="Link to this heading"></a></h2>
<section id="token-security">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Token Security</a><a class="headerlink" href="#token-security" title="Link to this heading"></a></h3>
<p><strong>Storage Recommendations</strong></p>
<ul class="simple">
<li><p>Store tokens securely (encrypted storage, secure cookies)</p></li>
<li><p>Never log tokens in plain text</p></li>
<li><p>Implement automatic token refresh</p></li>
<li><p>Use HTTPS for all API communications</p></li>
</ul>
<p><strong>Token Validation</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">validate_token</span><span class="p">(</span><span class="n">token</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Validate JWT token&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">payload</span> <span class="o">=</span> <span class="n">jwt</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span>
            <span class="n">token</span><span class="p">,</span>
            <span class="n">SECRET_KEY</span><span class="p">,</span>
            <span class="n">algorithms</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;HS256&quot;</span><span class="p">]</span>
        <span class="p">)</span>

        <span class="c1"># Check expiration</span>
        <span class="k">if</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span> <span class="o">&gt;</span> <span class="n">datetime</span><span class="o">.</span><span class="n">fromtimestamp</span><span class="p">(</span><span class="n">payload</span><span class="p">[</span><span class="s1">&#39;exp&#39;</span><span class="p">]):</span>
            <span class="k">raise</span> <span class="n">jwt</span><span class="o">.</span><span class="n">ExpiredSignatureError</span>

        <span class="k">return</span> <span class="n">payload</span>

    <span class="k">except</span> <span class="n">jwt</span><span class="o">.</span><span class="n">InvalidTokenError</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">401</span><span class="p">,</span>
            <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Invalid authentication token&quot;</span>
        <span class="p">)</span>
</pre></div>
</div>
</section>
<section id="rate-limiting">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Rate Limiting</a><a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h3>
<p>API endpoints are protected by rate limiting:</p>
<table class="docutils align-default" id="id2">
<caption><span class="caption-text">Rate Limits by Endpoint Type</span><a class="headerlink" href="#id2" title="Link to this table"></a></caption>
<thead>
<tr class="row-odd"><th class="head"><p>Endpoint Category</p></th>
<th class="head"><p>Rate Limit</p></th>
<th class="head"><p>Window</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Authentication</p></td>
<td><p>5 requests</p></td>
<td><p>1 minute</p></td>
</tr>
<tr class="row-odd"><td><p>General API</p></td>
<td><p>100 requests</p></td>
<td><p>1 minute</p></td>
</tr>
<tr class="row-even"><td><p>AI Assistant</p></td>
<td><p>20 requests</p></td>
<td><p>1 minute</p></td>
</tr>
<tr class="row-odd"><td><p>File Uploads</p></td>
<td><p>10 requests</p></td>
<td><p>5 minutes</p></td>
</tr>
</tbody>
</table>
<p><strong>Rate Limit Headers</strong></p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="kr">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
<span class="na">X-RateLimit-Limit</span><span class="o">:</span> <span class="l">100</span>
<span class="na">X-RateLimit-Remaining</span><span class="o">:</span> <span class="l">95</span>
<span class="na">X-RateLimit-Reset</span><span class="o">:</span> <span class="l">1642694400</span>
</pre></div>
</div>
</section>
</section>
<section id="error-handling">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<section id="authentication-errors">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Authentication Errors</a><a class="headerlink" href="#authentication-errors" title="Link to this heading"></a></h3>
<p><strong>Common Error Responses</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;authentication_failed&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Invalid credentials provided&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">401</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Error Codes</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">401</span></code> - Unauthorized (invalid/missing token)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">403</span></code> - Forbidden (insufficient permissions)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">429</span></code> - Too Many Requests (rate limit exceeded)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">422</span></code> - Validation Error (invalid request data)</p></li>
</ul>
<p><strong>Python Error Handling</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="p">:</span>
    <span class="n">response</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">make_request</span><span class="p">(</span><span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;/certifications&quot;</span><span class="p">)</span>
    <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>

<span class="k">except</span> <span class="n">requests</span><span class="o">.</span><span class="n">exceptions</span><span class="o">.</span><span class="n">HTTPError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="k">if</span> <span class="n">e</span><span class="o">.</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">401</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Authentication failed - please check credentials&quot;</span><span class="p">)</span>
    <span class="k">elif</span> <span class="n">e</span><span class="o">.</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">403</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Access denied - insufficient permissions&quot;</span><span class="p">)</span>
    <span class="k">elif</span> <span class="n">e</span><span class="o">.</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">429</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Rate limit exceeded - please wait before retrying&quot;</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;API error: </span><span class="si">{</span><span class="n">e</span><span class="o">.</span><span class="n">response</span><span class="o">.</span><span class="n">status_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="testing-authentication">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">Testing Authentication</a><a class="headerlink" href="#testing-authentication" title="Link to this heading"></a></h2>
<p><strong>Test Credentials</strong></p>
<p>For development and testing environments:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Development environment</span>
<span class="nv">USERNAME</span><span class="o">=</span><EMAIL>
<span class="nv">PASSWORD</span><span class="o">=</span>test_password_123
</pre></div>
</div>
<p><strong>Automated Testing</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">from</span> <span class="nn">tests.conftest</span> <span class="kn">import</span> <span class="n">test_client</span><span class="p">,</span> <span class="n">test_user</span>

<span class="k">def</span> <span class="nf">test_authentication_success</span><span class="p">(</span><span class="n">test_client</span><span class="p">,</span> <span class="n">test_user</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test successful authentication&quot;&quot;&quot;</span>
    <span class="n">auth_data</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;username&quot;</span><span class="p">:</span> <span class="n">test_user</span><span class="o">.</span><span class="n">email</span><span class="p">,</span>
        <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;test_password&quot;</span>
    <span class="p">}</span>

    <span class="n">response</span> <span class="o">=</span> <span class="n">test_client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/api/v1/auth/login&quot;</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">auth_data</span><span class="p">)</span>

    <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">200</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
    <span class="k">assert</span> <span class="s2">&quot;access_token&quot;</span> <span class="ow">in</span> <span class="n">data</span>
    <span class="k">assert</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;token_type&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;bearer&quot;</span>

<span class="k">def</span> <span class="nf">test_invalid_credentials</span><span class="p">(</span><span class="n">test_client</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test authentication with invalid credentials&quot;&quot;&quot;</span>
    <span class="n">auth_data</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;username&quot;</span><span class="p">:</span> <span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
        <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;wrong_password&quot;</span>
    <span class="p">}</span>

    <span class="n">response</span> <span class="o">=</span> <span class="n">test_client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/api/v1/auth/login&quot;</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">auth_data</span><span class="p">)</span>

    <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">401</span>
    <span class="k">assert</span> <span class="s2">&quot;authentication_failed&quot;</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s2">&quot;error&quot;</span><span class="p">]</span>
</pre></div>
</div>
</section>
<section id="see-also">
<h2><a class="toc-backref" href="#id16" role="doc-backlink">See Also</a><a class="headerlink" href="#see-also" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../installation.html"><span class="doc">Installation Guide</span></a> - Setting up authentication</p></li>
<li><p><a class="reference internal" href="../configuration.html"><span class="doc">⚙️ Configuration Guide</span></a> - Security configuration options</p></li>
<li><p><a class="reference internal" href="../guides/admin_guide.html"><span class="doc">👨‍💼 Administrator Guide</span></a> - User management</p></li>
<li><p><span class="xref std std-doc">../enterprise/user_management</span> - Enterprise user administration</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="API Reference" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="ai_assistant.html" class="btn btn-neutral float-right" title="AI Study Assistant API" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>