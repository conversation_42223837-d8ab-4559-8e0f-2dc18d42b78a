<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Integration Hub API &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/integration_hub.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="🤖 Unified Intelligence API" href="unified_intelligence.html" />
    <link rel="prev" title="Progress Tracking API" href="progress_tracking.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Integration Hub API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#learning-management-system-integration">Learning Management System Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#configure-lms-integration">Configure LMS Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#synchronise-lms-data">Synchronise LMS Data</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#single-sign-on-sso-integration">Single Sign-On (SSO) Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#configure-sso-provider">Configure SSO Provider</a></li>
<li class="toctree-l3"><a class="reference internal" href="#test-sso-configuration">Test SSO Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#webhook-management">Webhook Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#register-webhook">Register Webhook</a></li>
<li class="toctree-l3"><a class="reference internal" href="#webhook-event-examples">Webhook Event Examples</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#data-synchronisation">Data Synchronisation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#configure-data-sync">Configure Data Sync</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#third-party-connectors">Third-party Connectors</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#available-connectors">Available Connectors</a></li>
<li class="toctree-l3"><a class="reference internal" href="#install-connector">Install Connector</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#integration-monitoring">Integration Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#get-integration-status">Get Integration Status</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Integration Hub API</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/integration_hub.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="integration-hub-api">
<h1>Integration Hub API<a class="headerlink" href="#integration-hub-api" title="Link to this heading"></a></h1>
<p>The Integration Hub API provides comprehensive integration capabilities for connecting CertPathFinder with external systems, learning management systems, and enterprise tools.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id1">Overview</a></p></li>
<li><p><a class="reference internal" href="#learning-management-system-integration" id="id2">Learning Management System Integration</a></p>
<ul>
<li><p><a class="reference internal" href="#configure-lms-integration" id="id3">Configure LMS Integration</a></p></li>
<li><p><a class="reference internal" href="#synchronise-lms-data" id="id4">Synchronise LMS Data</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#single-sign-on-sso-integration" id="id5">Single Sign-On (SSO) Integration</a></p>
<ul>
<li><p><a class="reference internal" href="#configure-sso-provider" id="id6">Configure SSO Provider</a></p></li>
<li><p><a class="reference internal" href="#test-sso-configuration" id="id7">Test SSO Configuration</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#webhook-management" id="id8">Webhook Management</a></p>
<ul>
<li><p><a class="reference internal" href="#register-webhook" id="id9">Register Webhook</a></p></li>
<li><p><a class="reference internal" href="#webhook-event-examples" id="id10">Webhook Event Examples</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#data-synchronisation" id="id11">Data Synchronisation</a></p>
<ul>
<li><p><a class="reference internal" href="#configure-data-sync" id="id12">Configure Data Sync</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#third-party-connectors" id="id13">Third-party Connectors</a></p>
<ul>
<li><p><a class="reference internal" href="#available-connectors" id="id14">Available Connectors</a></p></li>
<li><p><a class="reference internal" href="#install-connector" id="id15">Install Connector</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#integration-monitoring" id="id16">Integration Monitoring</a></p>
<ul>
<li><p><a class="reference internal" href="#get-integration-status" id="id17">Get Integration Status</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-handling" id="id18">Error Handling</a></p></li>
<li><p><a class="reference internal" href="#see-also" id="id19">See Also</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Integration Hub features include:</p>
<ul class="simple">
<li><p><strong>LMS Integration</strong> - Connect with learning management systems</p></li>
<li><p><strong>SSO Authentication</strong> - Single sign-on with enterprise identity providers</p></li>
<li><p><strong>Webhook Management</strong> - Real-time event notifications</p></li>
<li><p><strong>Data Synchronisation</strong> - Bi-directional data sync with external systems</p></li>
<li><p><strong>API Gateway</strong> - Secure external API access management</p></li>
<li><p><strong>Third-party Connectors</strong> - Pre-built integrations with popular tools</p></li>
</ul>
<p>All integrations maintain enterprise-grade security and compliance standards.</p>
</section>
<section id="learning-management-system-integration">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Learning Management System Integration</a><a class="headerlink" href="#learning-management-system-integration" title="Link to this heading"></a></h2>
<section id="configure-lms-integration">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">Configure LMS Integration</a><a class="headerlink" href="#configure-lms-integration" title="Link to this heading"></a></h3>
<p>Set up integration with learning management systems.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/integration-hub/lms/configure</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>

<span class="err">{</span>
<span class="err">  &quot;organisation_id&quot;: &quot;org_12345&quot;,</span>
<span class="err">  &quot;lms_provider&quot;: &quot;moodle&quot;,</span>
<span class="err">  &quot;configuration&quot;: {</span>
<span class="err">    &quot;base_url&quot;: &quot;https://lms.company.com&quot;,</span>
<span class="err">    &quot;api_version&quot;: &quot;3.9&quot;,</span>
<span class="err">    &quot;authentication&quot;: {</span>
<span class="err">      &quot;method&quot;: &quot;token&quot;,</span>
<span class="err">      &quot;api_token&quot;: &quot;moodle_api_token_here&quot;,</span>
<span class="err">      &quot;username&quot;: &quot;certpathfinder_integration&quot;</span>
<span class="err">    },</span>
<span class="err">    &quot;sync_settings&quot;: {</span>
<span class="err">      &quot;sync_users&quot;: true,</span>
<span class="err">      &quot;sync_courses&quot;: true,</span>
<span class="err">      &quot;sync_progress&quot;: true,</span>
<span class="err">      &quot;sync_frequency&quot;: &quot;daily&quot;,</span>
<span class="err">      &quot;sync_direction&quot;: &quot;bidirectional&quot;</span>
<span class="err">    }</span>
<span class="err">  },</span>
<span class="err">  &quot;field_mappings&quot;: {</span>
<span class="err">    &quot;user_fields&quot;: {</span>
<span class="err">      &quot;email&quot;: &quot;email&quot;,</span>
<span class="err">      &quot;full_name&quot;: &quot;fullname&quot;,</span>
<span class="err">      &quot;department&quot;: &quot;department&quot;,</span>
<span class="err">      &quot;job_title&quot;: &quot;institution&quot;</span>
<span class="err">    },</span>
<span class="err">    &quot;course_fields&quot;: {</span>
<span class="err">      &quot;certification_id&quot;: &quot;idnumber&quot;,</span>
<span class="err">      &quot;course_name&quot;: &quot;fullname&quot;,</span>
<span class="err">      &quot;description&quot;: &quot;summary&quot;</span>
<span class="err">    }</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;integration_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;lms_integration_789&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;lms_provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;moodle&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;configured&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;configuration_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;connection_test&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;successful&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;authentication&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;verified&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;permissions&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sufficient&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;field_mapping&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;validated&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;sync_capabilities&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;users&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;import&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;export&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;update&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;courses&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;import&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;export&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;update&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;import&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;export&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;update&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;next_sync_scheduled&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-16T02:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;webhook_endpoints&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;event&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user_progress_updated&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://lms.company.com/webservice/rest/server.php&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;method&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;POST&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="synchronise-lms-data">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Synchronise LMS Data</a><a class="headerlink" href="#synchronise-lms-data" title="Link to this heading"></a></h3>
<p>Trigger data synchronisation with connected LMS.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/integration-hub/lms/{integration_id}/sync</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;sync_type&quot;: &quot;incremental&quot;,</span>
<span class="err">  &quot;sync_entities&quot;: [&quot;users&quot;, &quot;progress&quot;],</span>
<span class="err">  &quot;date_range&quot;: {</span>
<span class="err">    &quot;start_date&quot;: &quot;2025-01-01T00:00:00Z&quot;,</span>
<span class="err">    &quot;end_date&quot;: &quot;2025-01-15T23:59:59Z&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;options&quot;: {</span>
<span class="err">    &quot;force_update&quot;: false,</span>
<span class="err">    &quot;skip_validation&quot;: false,</span>
<span class="err">    &quot;batch_size&quot;: 100</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;sync_job_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sync_job_456&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;integration_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;lms_integration_789&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;running&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;started_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;estimated_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:45:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_records&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">500</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;processed_records&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;successful_syncs&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;failed_syncs&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;current_entity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;users&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;sync_summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;entities_to_sync&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;users&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;progress&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;sync_direction&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bidirectional&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;batch_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="single-sign-on-sso-integration">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Single Sign-On (SSO) Integration</a><a class="headerlink" href="#single-sign-on-sso-integration" title="Link to this heading"></a></h2>
<section id="configure-sso-provider">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Configure SSO Provider</a><a class="headerlink" href="#configure-sso-provider" title="Link to this heading"></a></h3>
<p>Set up SSO integration with enterprise identity providers.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/integration-hub/sso/configure</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;organisation_id&quot;: &quot;org_12345&quot;,</span>
<span class="err">  &quot;provider&quot;: &quot;azure_ad&quot;,</span>
<span class="err">  &quot;configuration&quot;: {</span>
<span class="err">    &quot;tenant_id&quot;: &quot;azure_tenant_id_here&quot;,</span>
<span class="err">    &quot;client_id&quot;: &quot;azure_client_id_here&quot;,</span>
<span class="err">    &quot;client_secret&quot;: &quot;azure_client_secret_here&quot;,</span>
<span class="err">    &quot;redirect_uri&quot;: &quot;https://certpathfinder.company.com/auth/callback&quot;,</span>
<span class="err">    &quot;scopes&quot;: [&quot;openid&quot;, &quot;profile&quot;, &quot;email&quot;, &quot;User.Read&quot;]</span>
<span class="err">  },</span>
<span class="err">  &quot;attribute_mapping&quot;: {</span>
<span class="err">    &quot;email&quot;: &quot;mail&quot;,</span>
<span class="err">    &quot;full_name&quot;: &quot;displayName&quot;,</span>
<span class="err">    &quot;first_name&quot;: &quot;givenName&quot;,</span>
<span class="err">    &quot;last_name&quot;: &quot;surname&quot;,</span>
<span class="err">    &quot;department&quot;: &quot;department&quot;,</span>
<span class="err">    &quot;job_title&quot;: &quot;jobTitle&quot;,</span>
<span class="err">    &quot;manager&quot;: &quot;manager&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;provisioning_settings&quot;: {</span>
<span class="err">    &quot;auto_create_users&quot;: true,</span>
<span class="err">    &quot;auto_update_users&quot;: true,</span>
<span class="err">    &quot;default_role&quot;: &quot;user&quot;,</span>
<span class="err">    &quot;require_group_membership&quot;: true,</span>
<span class="err">    &quot;allowed_groups&quot;: [&quot;CertPathFinder-Users&quot;, &quot;Security-Team&quot;]</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;sso_integration_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sso_azure_123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;azure_ad&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;configured&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;configuration_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;connection_test&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;successful&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;authentication&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;verified&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;attribute_mapping&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;validated&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;group_access&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;configured&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;sso_endpoints&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;login_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://certpathfinder.company.com/auth/sso/azure&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;logout_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://certpathfinder.company.com/auth/logout&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;metadata_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://certpathfinder.company.com/auth/sso/azure/metadata&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;security_settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;encryption_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;signature_validation&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;session_timeout_minutes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">480</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;force_authentication&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="test-sso-configuration">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Test SSO Configuration</a><a class="headerlink" href="#test-sso-configuration" title="Link to this heading"></a></h3>
<p>Validate SSO setup and user authentication flow.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/integration-hub/sso/{sso_integration_id}/test</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;test_type&quot;: &quot;full_flow&quot;,</span>
<span class="err">  &quot;test_user&quot;: {</span>
<span class="err">    &quot;email&quot;: &quot;<EMAIL>&quot;,</span>
<span class="err">    &quot;expected_attributes&quot;: {</span>
<span class="err">      &quot;department&quot;: &quot;Information Security&quot;,</span>
<span class="err">      &quot;job_title&quot;: &quot;Security Analyst&quot;</span>
<span class="err">    }</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;test_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sso_test_789&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;sso_integration_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sso_azure_123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;test_results&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;overall_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;successful&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;authentication_flow&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;passed&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;attribute_mapping&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;passed&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;user_provisioning&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;passed&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;group_membership&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;passed&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;test_details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;authentication_time_ms&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1250</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;attributes_received&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;full_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Test User&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;department&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Information Security&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;job_title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Analyst&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;groups_matched&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CertPathFinder-Users&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Security-Team&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;user_created&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;role_assigned&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;SSO configuration is working correctly&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Consider enabling multi-factor authentication&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="webhook-management">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Webhook Management</a><a class="headerlink" href="#webhook-management" title="Link to this heading"></a></h2>
<section id="register-webhook">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Register Webhook</a><a class="headerlink" href="#register-webhook" title="Link to this heading"></a></h3>
<p>Set up webhooks for real-time event notifications.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/integration-hub/webhooks</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;organisation_id&quot;: &quot;org_12345&quot;,</span>
<span class="err">  &quot;webhook_url&quot;: &quot;https://external-system.company.com/webhooks/certpathfinder&quot;,</span>
<span class="err">  &quot;events&quot;: [</span>
<span class="err">    &quot;user.certification.completed&quot;,</span>
<span class="err">    &quot;user.goal.achieved&quot;,</span>
<span class="err">    &quot;user.study_session.completed&quot;,</span>
<span class="err">    &quot;organisation.user.created&quot;</span>
<span class="err">  ],</span>
<span class="err">  &quot;authentication&quot;: {</span>
<span class="err">    &quot;method&quot;: &quot;signature&quot;,</span>
<span class="err">    &quot;secret&quot;: &quot;webhook_secret_key_here&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;configuration&quot;: {</span>
<span class="err">    &quot;retry_attempts&quot;: 3,</span>
<span class="err">    &quot;retry_delay_seconds&quot;: 30,</span>
<span class="err">    &quot;timeout_seconds&quot;: 10,</span>
<span class="err">    &quot;include_metadata&quot;: true</span>
<span class="err">  },</span>
<span class="err">  &quot;filters&quot;: {</span>
<span class="err">    &quot;departments&quot;: [&quot;Information Security&quot;, &quot;IT Operations&quot;],</span>
<span class="err">    &quot;certification_types&quot;: [&quot;technical&quot;, &quot;management&quot;],</span>
<span class="err">    &quot;minimum_score&quot;: 80</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;webhook_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;webhook_456&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;webhook_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://external-system.company.com/webhooks/certpathfinder&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;active&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;events_subscribed&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;user.certification.completed&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;user.goal.achieved&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;user.study_session.completed&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;organisation.user.created&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;security&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;signature_method&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;HMAC-SHA256&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;signature_header&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;X-CertPathFinder-Signature&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;timestamp_header&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;X-CertPathFinder-Timestamp&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;delivery_settings&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;retry_attempts&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;retry_delay_seconds&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">30</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;timeout_seconds&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;next_retry_backoff&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;exponential&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;test_endpoint&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/integration-hub/webhooks/webhook_456/test&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="webhook-event-examples">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Webhook Event Examples</a><a class="headerlink" href="#webhook-event-examples" title="Link to this heading"></a></h3>
<p>Example webhook payloads for different events:</p>
<p><strong>User Certification Completed</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;event&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user.certification.completed&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;user&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;full_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;John Smith&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;department&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Information Security&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ISC2&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completion_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">85</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;pass_threshold&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">70</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;study_statistics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;total_study_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">145</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;study_duration_days&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">89</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;sessions_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">23</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;metadata&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;webhook_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;webhook_456&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;delivery_attempt&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;signature&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sha256=abc123...&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>User Goal Achieved</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;event&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;user.goal.achieved&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;user&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;full_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;John Smith&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;goal&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;goal_789&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Achieve CISSP Certification&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;certification&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;target_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-07-15&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completed_date&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completion_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;achievement_details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;days_ahead_of_schedule&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">151</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;final_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">85</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;study_efficiency&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.92</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="data-synchronisation">
<h2><a class="toc-backref" href="#id11" role="doc-backlink">Data Synchronisation</a><a class="headerlink" href="#data-synchronisation" title="Link to this heading"></a></h2>
<section id="configure-data-sync">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Configure Data Sync</a><a class="headerlink" href="#configure-data-sync" title="Link to this heading"></a></h3>
<p>Set up automated data synchronisation with external systems.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/integration-hub/data-sync/configure</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;organisation_id&quot;: &quot;org_12345&quot;,</span>
<span class="err">  &quot;sync_name&quot;: &quot;HR System Integration&quot;,</span>
<span class="err">  &quot;external_system&quot;: {</span>
<span class="err">    &quot;type&quot;: &quot;rest_api&quot;,</span>
<span class="err">    &quot;base_url&quot;: &quot;https://hr-system.company.com/api/v1&quot;,</span>
<span class="err">    &quot;authentication&quot;: {</span>
<span class="err">      &quot;method&quot;: &quot;bearer_token&quot;,</span>
<span class="err">      &quot;token&quot;: &quot;hr_system_api_token&quot;</span>
<span class="err">    }</span>
<span class="err">  },</span>
<span class="err">  &quot;sync_configuration&quot;: {</span>
<span class="err">    &quot;entities&quot;: [</span>
<span class="err">      {</span>
<span class="err">        &quot;entity_type&quot;: &quot;users&quot;,</span>
<span class="err">        &quot;sync_direction&quot;: &quot;import&quot;,</span>
<span class="err">        &quot;source_endpoint&quot;: &quot;/employees&quot;,</span>
<span class="err">        &quot;field_mapping&quot;: {</span>
<span class="err">          &quot;employee_id&quot;: &quot;external_id&quot;,</span>
<span class="err">          &quot;email&quot;: &quot;work_email&quot;,</span>
<span class="err">          &quot;full_name&quot;: &quot;display_name&quot;,</span>
<span class="err">          &quot;department&quot;: &quot;department_name&quot;,</span>
<span class="err">          &quot;job_title&quot;: &quot;position_title&quot;,</span>
<span class="err">          &quot;manager_email&quot;: &quot;manager_email&quot;,</span>
<span class="err">          &quot;start_date&quot;: &quot;hire_date&quot;</span>
<span class="err">        },</span>
<span class="err">        &quot;filters&quot;: {</span>
<span class="err">          &quot;active_only&quot;: true,</span>
<span class="err">          &quot;departments&quot;: [&quot;IT&quot;, &quot;Security&quot;, &quot;Engineering&quot;]</span>
<span class="err">        }</span>
<span class="err">      }</span>
<span class="err">    ],</span>
<span class="err">    &quot;schedule&quot;: {</span>
<span class="err">      &quot;frequency&quot;: &quot;daily&quot;,</span>
<span class="err">      &quot;time&quot;: &quot;02:00&quot;,</span>
<span class="err">      &quot;timezone&quot;: &quot;Europe/London&quot;</span>
<span class="err">    },</span>
<span class="err">    &quot;error_handling&quot;: {</span>
<span class="err">      &quot;on_error&quot;: &quot;continue&quot;,</span>
<span class="err">      &quot;notification_email&quot;: &quot;<EMAIL>&quot;,</span>
<span class="err">      &quot;max_failures&quot;: 5</span>
<span class="err">    }</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;sync_configuration_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sync_config_123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;sync_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;HR System Integration&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;configured&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;validation_results&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;connection_test&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;successful&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;authentication&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;verified&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;endpoint_access&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;confirmed&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;field_mapping&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;validated&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;sync_schedule&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;next_sync&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-16T02:00:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;frequency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;daily&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;timezone&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Europe/London&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;monitoring&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;health_check_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/integration-hub/data-sync/sync_config_123/health&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;logs_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/integration-hub/data-sync/sync_config_123/logs&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;metrics_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/integration-hub/data-sync/sync_config_123/metrics&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="third-party-connectors">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">Third-party Connectors</a><a class="headerlink" href="#third-party-connectors" title="Link to this heading"></a></h2>
<section id="available-connectors">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Available Connectors</a><a class="headerlink" href="#available-connectors" title="Link to this heading"></a></h3>
<p>Get list of available pre-built integrations.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/integration-hub/connectors</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>

<span class="err">?category=lms&amp;status=available</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;connectors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;connector_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;moodle_connector&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Moodle LMS&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;lms&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Full integration with Moodle learning management system&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2.1.0&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;available&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;capabilities&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">&quot;user_sync&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;course_sync&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;progress_sync&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;grade_passback&quot;</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;moodle_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;3.9+&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;required_permissions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;webservice/rest:use&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;moodle/course:view&quot;</span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;api_access&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;configuration_complexity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;medium&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;setup_time_estimate&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;30-60 minutes&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;connector_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;azure_ad_connector&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Azure Active Directory&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;identity&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Single sign-on and user provisioning with Azure AD&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1.5.0&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;available&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;capabilities&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">&quot;sso_authentication&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;user_provisioning&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;group_sync&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;attribute_mapping&quot;</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;categories&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;lms&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Learning Management Systems&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;connector_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;identity&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Identity Providers&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;connector_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;hr&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;HR Systems&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;connector_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="install-connector">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">Install Connector</a><a class="headerlink" href="#install-connector" title="Link to this heading"></a></h3>
<p>Install and configure a pre-built connector.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/integration-hub/connectors/{connector_id}/install</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;organisation_id&quot;: &quot;org_12345&quot;,</span>
<span class="err">  &quot;configuration&quot;: {</span>
<span class="err">    &quot;connection_name&quot;: &quot;Company Moodle LMS&quot;,</span>
<span class="err">    &quot;base_url&quot;: &quot;https://lms.company.com&quot;,</span>
<span class="err">    &quot;api_token&quot;: &quot;moodle_token_here&quot;,</span>
<span class="err">    &quot;sync_settings&quot;: {</span>
<span class="err">      &quot;auto_sync&quot;: true,</span>
<span class="err">      &quot;sync_frequency&quot;: &quot;daily&quot;</span>
<span class="err">    }</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
</section>
</section>
<section id="integration-monitoring">
<h2><a class="toc-backref" href="#id16" role="doc-backlink">Integration Monitoring</a><a class="headerlink" href="#integration-monitoring" title="Link to this heading"></a></h2>
<section id="get-integration-status">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Get Integration Status</a><a class="headerlink" href="#get-integration-status" title="Link to this heading"></a></h3>
<p>Monitor the health and status of all integrations.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/integration-hub/status</span>
<span class="err">Authorization: Bearer &lt;admin_token&gt;</span>

<span class="err">?organisation_id=org_12345</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;organisation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;overall_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;integrations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;integration_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;lms_integration_789&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;lms&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Moodle LMS&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_sync&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T02:00:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;next_sync&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-16T02:00:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;sync_success_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.98</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_error&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;integration_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sso_azure_123&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sso&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Azure AD SSO&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;last_authentication&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T09:45:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;authentication_success_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.995</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;active_sessions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;metrics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_integrations&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;healthy_integrations&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;warning_integrations&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;failed_integrations&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_sync_operations_24h&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;successful_sync_operations_24h&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">11</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="error-handling">
<h2><a class="toc-backref" href="#id18" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>Integration Hub API error responses:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;integration_configuration_invalid&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;LMS integration configuration failed validation&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">422</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;validation_errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Invalid API token format&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Base URL not accessible&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;integration_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;lms&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;moodle&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;suggestions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Verify API token has correct permissions&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Check network connectivity to LMS&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Common Error Codes</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">400</span></code> - Invalid integration configuration</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">401</span></code> - Authentication failed with external system</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">403</span></code> - Insufficient permissions for integration</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">422</span></code> - Integration validation failed</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">503</span></code> - External system unavailable</p></li>
</ul>
</section>
<section id="see-also">
<h2><a class="toc-backref" href="#id19" role="doc-backlink">See Also</a><a class="headerlink" href="#see-also" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../enterprise/dashboard.html"><span class="doc">🏢 Enterprise Dashboard</span></a> - Enterprise integration management</p></li>
<li><p><a class="reference internal" href="authentication.html"><span class="doc">Authentication &amp; Security</span></a> - API authentication</p></li>
<li><p><a class="reference internal" href="../guides/admin_guide.html"><span class="doc">👨‍💼 Administrator Guide</span></a> - Integration setup guide</p></li>
<li><p><span class="xref std std-doc">../enterprise/compliance</span> - Integration compliance requirements</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="progress_tracking.html" class="btn btn-neutral float-left" title="Progress Tracking API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="unified_intelligence.html" class="btn btn-neutral float-right" title="🤖 Unified Intelligence API" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>