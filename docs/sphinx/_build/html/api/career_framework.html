<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Career Framework API &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/career_framework.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Progress Tracking API" href="progress_tracking.html" />
    <link rel="prev" title="Cost Calculator API" href="cost_calculator.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Career Framework API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#career-analysis">Career Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#analyse-career-transition">Analyse Career Transition</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#job-market-intelligence">Job Market Intelligence</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#get-high-demand-roles">Get High-Demand Roles</a></li>
<li class="toctree-l3"><a class="reference internal" href="#salary-analysis">Salary Analysis</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#skills-development">Skills Development</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#skills-assessment">Skills Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#career-path-planning">Career Path Planning</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#generate-career-roadmap">Generate Career Roadmap</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Career Framework API</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/career_framework.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="career-framework-api">
<h1>Career Framework API<a class="headerlink" href="#career-framework-api" title="Link to this heading"></a></h1>
<p>The Career Framework API provides comprehensive career guidance, transition planning, and skills analysis for cybersecurity professionals. It includes job market intelligence, career path recommendations, and skills gap analysis.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id1">Overview</a></p></li>
<li><p><a class="reference internal" href="#career-analysis" id="id2">Career Analysis</a></p>
<ul>
<li><p><a class="reference internal" href="#analyse-career-transition" id="id3">Analyse Career Transition</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#job-market-intelligence" id="id4">Job Market Intelligence</a></p>
<ul>
<li><p><a class="reference internal" href="#get-high-demand-roles" id="id5">Get High-Demand Roles</a></p></li>
<li><p><a class="reference internal" href="#salary-analysis" id="id6">Salary Analysis</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#skills-development" id="id7">Skills Development</a></p>
<ul>
<li><p><a class="reference internal" href="#skills-assessment" id="id8">Skills Assessment</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#career-path-planning" id="id9">Career Path Planning</a></p>
<ul>
<li><p><a class="reference internal" href="#generate-career-roadmap" id="id10">Generate Career Roadmap</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-handling" id="id11">Error Handling</a></p></li>
<li><p><a class="reference internal" href="#see-also" id="id12">See Also</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Career Framework offers:</p>
<ul class="simple">
<li><p><strong>Career Path Planning</strong> - Strategic roadmaps for career advancement</p></li>
<li><p><strong>Skills Gap Analysis</strong> - Identify and prioritise skill development needs</p></li>
<li><p><strong>Job Market Intelligence</strong> - Real-time market data and trends</p></li>
<li><p><strong>Transition Planning</strong> - Step-by-step career change guidance</p></li>
<li><p><strong>Salary Analysis</strong> - Compensation benchmarking and projections</p></li>
<li><p><strong>Role Matching</strong> - AI-powered job role recommendations</p></li>
</ul>
<p>All recommendations are personalised based on individual profiles and market conditions.</p>
</section>
<section id="career-analysis">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Career Analysis</a><a class="headerlink" href="#career-analysis" title="Link to this heading"></a></h2>
<section id="analyse-career-transition">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">Analyse Career Transition</a><a class="headerlink" href="#analyse-career-transition" title="Link to this heading"></a></h3>
<p>Evaluate the feasibility and requirements for career transitions.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/career-transition/analyse</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">{</span>
<span class="err">  &quot;current_profile&quot;: {</span>
<span class="err">    &quot;current_role_id&quot;: 15,</span>
<span class="err">    &quot;current_role_title&quot;: &quot;Security Analyst&quot;,</span>
<span class="err">    &quot;experience_years&quot;: 3,</span>
<span class="err">    &quot;current_skills&quot;: [</span>
<span class="err">      {</span>
<span class="err">        &quot;skill_name&quot;: &quot;Network Security&quot;,</span>
<span class="err">        &quot;proficiency_level&quot;: &quot;intermediate&quot;,</span>
<span class="err">        &quot;years_experience&quot;: 2</span>
<span class="err">      },</span>
<span class="err">      {</span>
<span class="err">        &quot;skill_name&quot;: &quot;Incident Response&quot;,</span>
<span class="err">        &quot;proficiency_level&quot;: &quot;beginner&quot;,</span>
<span class="err">        &quot;years_experience&quot;: 1</span>
<span class="err">      }</span>
<span class="err">    ],</span>
<span class="err">    &quot;current_certifications&quot;: [&quot;Security+&quot;, &quot;Network+&quot;],</span>
<span class="err">    &quot;current_salary&quot;: 45000,</span>
<span class="err">    &quot;location&quot;: &quot;United Kingdom&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;target_profile&quot;: {</span>
<span class="err">    &quot;target_role_id&quot;: 28,</span>
<span class="err">    &quot;target_role_title&quot;: &quot;Security Architect&quot;,</span>
<span class="err">    &quot;desired_timeline_months&quot;: 18,</span>
<span class="err">    &quot;target_salary_range&quot;: {</span>
<span class="err">      &quot;min&quot;: 75000,</span>
<span class="err">      &quot;max&quot;: 95000</span>
<span class="err">    }</span>
<span class="err">  },</span>
<span class="err">  &quot;preferences&quot;: {</span>
<span class="err">    &quot;study_time_per_week&quot;: 10,</span>
<span class="err">    &quot;budget_available&quot;: 15000,</span>
<span class="err">    &quot;learning_style&quot;: &quot;hands_on&quot;,</span>
<span class="err">    &quot;willing_to_relocate&quot;: false</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;analysis_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;analysis_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;transition_feasibility&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;overall_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">7.8</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;confidence_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;feasibility_rating&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;key_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;experience_alignment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.7</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;skills_transferability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.8</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;market_demand&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.9</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;timeline_realism&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.75</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;skills_gap_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;critical_gaps&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;skill_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Enterprise Architecture&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;current_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;none&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;required_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;estimated_learning_time_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">120</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;recommended_resources&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;TOGAF Certification&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Enterprise Architecture Fundamentals Course&quot;</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;skill_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Cloud Security Architecture&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;current_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;beginner&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;required_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;advanced&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;estimated_learning_time_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">200</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;skill_strengths&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;skill_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Network Security&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;advantage_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;moderate&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;transferability&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;total_learning_hours_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">450</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;certification_roadmap&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;required_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;critical&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;estimated_study_time_weeks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">16</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cost_estimate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8500</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;target_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Month 8&quot;</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">67</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;SABSA&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;estimated_study_time_weeks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cost_estimate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6500</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;target_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Month 14&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;optional_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">89</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;AWS Solutions Architect&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;benefit&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Enhances cloud architecture credibility&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cost_estimate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3500</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;timeline_plan&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_duration_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;milestones&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;milestone&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Complete CISSP study preparation&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;deliverables&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Study plan completion&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Practice test scores &gt;80%&quot;</span><span class="p">]</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;milestone&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP Certification achieved&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;deliverables&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP certificate&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Updated CV/LinkedIn&quot;</span><span class="p">]</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;milestone&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Architecture experience gained&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;deliverables&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Architecture project portfolio&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Technical presentations&quot;</span><span class="p">]</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;financial_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_investment_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;expected_salary_increase&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">35000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;roi_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">233</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;payback_period_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">5.1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;net_benefit_5_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">175000</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;market_intelligence&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;job_availability&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;competition_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;moderate&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;growth_projection&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.15</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;regional_demand&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;london&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;very_high&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;manchester&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;edinburgh&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;moderate&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;immediate_actions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Begin CISSP study programme immediately&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Seek architecture-related projects in current role&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Join professional architecture communities&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;risk_mitigation&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Build portfolio of architecture work&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Network with senior architects&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Consider interim roles to gain experience&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;success_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Consistent study schedule&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Practical experience application&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Professional networking&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="job-market-intelligence">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Job Market Intelligence</a><a class="headerlink" href="#job-market-intelligence" title="Link to this heading"></a></h2>
<section id="get-high-demand-roles">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Get High-Demand Roles</a><a class="headerlink" href="#get-high-demand-roles" title="Link to this heading"></a></h3>
<p>Retrieve information about high-demand cybersecurity roles.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/enhanced-taxonomy/job-titles/high-demand</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?location=UK&amp;experience_level=mid&amp;limit=10</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;location&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;United Kingdom&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;experience_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;mid&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;job_titles&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">28</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Cloud Security Engineer&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;demand_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">9.2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;average_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">68000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;salary_range&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;min&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">55000</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;max&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">85000</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;growth_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.18</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;job_openings_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1250</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;skills_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">&quot;AWS Security&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;Azure Security&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;Cloud Architecture&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;DevSecOps&quot;</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;typical_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">&quot;AWS Certified Security - Specialty&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;Azure Security Engineer Associate&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;CCSP&quot;</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;experience_requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;min_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;max_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;preferred_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;market_trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;hiring_velocity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;fast&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;competition_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;moderate&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;remote_work_availability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.75</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;market_summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_high_demand_roles&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_salary_increase&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;fastest_growing_domain&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Cloud Security&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;skills_shortage_areas&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Zero Trust&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;DevSecOps&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;AI Security&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="salary-analysis">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Salary Analysis</a><a class="headerlink" href="#salary-analysis" title="Link to this heading"></a></h3>
<p>Get detailed salary information for specific roles and locations.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/career-transition/salary-analysis</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?role_id=28&amp;location=UK&amp;experience_years=5</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;role_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">28</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;role_title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Cloud Security Engineer&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;location&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;United Kingdom&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;experience_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;salary_data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;median_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">72000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">75500</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;salary_range&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;percentile_25&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">62000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;percentile_50&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">72000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;percentile_75&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">85000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;percentile_90&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">98000</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;total_compensation&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;base_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">72000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;bonus_average&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;equity_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total_package&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">92500</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;regional_breakdown&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;london&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;median_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">85000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;cost_of_living_adjustment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.18</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;manchester&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;median_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">68000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;cost_of_living_adjustment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.94</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;edinburgh&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;median_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">70000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;cost_of_living_adjustment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.97</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;salary_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;certifications_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;AWS Security Specialty&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;CISSP&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;CCSP&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6500</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;skills_premium&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;Zero Trust Architecture&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;Kubernetes Security&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;DevSecOps&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10000</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;company_size_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;startup&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;mid_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;enterprise&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.15</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;big_tech&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.35</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;career_progression&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;next_level_role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Senior Cloud Security Architect&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;progression_timeline_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;next_level_salary&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">95000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;progression_requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Team leadership experience&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Architecture design portfolio&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Advanced certifications&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;market_trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;salary_growth_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.08</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;demand_trend&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;increasing&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;remote_work_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.95</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;skills_evolution&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;AI/ML Security becoming critical&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Zero Trust implementation experience valued&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Multi-cloud expertise in demand&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="skills-development">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">Skills Development</a><a class="headerlink" href="#skills-development" title="Link to this heading"></a></h2>
<section id="skills-assessment">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Skills Assessment</a><a class="headerlink" href="#skills-assessment" title="Link to this heading"></a></h3>
<p>Evaluate current skills against market requirements.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/career-transition/skills-assessment</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;user_profile&quot;: {</span>
<span class="err">    &quot;current_role&quot;: &quot;Security Analyst&quot;,</span>
<span class="err">    &quot;experience_years&quot;: 3,</span>
<span class="err">    &quot;target_roles&quot;: [&quot;Security Architect&quot;, &quot;Security Consultant&quot;]</span>
<span class="err">  },</span>
<span class="err">  &quot;current_skills&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;skill_name&quot;: &quot;Network Security&quot;,</span>
<span class="err">      &quot;self_assessment&quot;: 7,</span>
<span class="err">      &quot;years_experience&quot;: 2,</span>
<span class="err">      &quot;last_used&quot;: &quot;current&quot;</span>
<span class="err">    },</span>
<span class="err">    {</span>
<span class="err">      &quot;skill_name&quot;: &quot;Cloud Security&quot;,</span>
<span class="err">      &quot;self_assessment&quot;: 4,</span>
<span class="err">      &quot;years_experience&quot;: 1,</span>
<span class="err">      &quot;last_used&quot;: &quot;6_months_ago&quot;</span>
<span class="err">    }</span>
<span class="err">  ],</span>
<span class="err">  &quot;assessment_type&quot;: &quot;comprehensive&quot;</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;assessment_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;skills_assess_789&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;overall_assessment&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;current_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;market_competitiveness&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">6.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;readiness_for_target_roles&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;Security Architect&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.65</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;Security Consultant&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.72</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;skills_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;skill_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Network Security&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;current_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;market_requirement&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;gap_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;meets_requirement&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;market_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;improvement_potential&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;moderate&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;skill_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Cloud Security&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;current_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;beginner&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;market_requirement&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;advanced&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;gap_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;significant_gap&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;market_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;very_high&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;improvement_potential&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;critical&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;development_recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;skill_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Cloud Security&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;development_path&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;step&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;activity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;AWS Security Fundamentals Course&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;duration_weeks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">500</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;step&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;activity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Hands-on Cloud Labs&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;duration_weeks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">200</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;step&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;activity&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;AWS Security Specialty Certification&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;duration_weeks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3500</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;total_investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;time_weeks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">24</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4200</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;expected_outcome&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Advanced cloud security proficiency&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;certification_recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;relevance_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">9.2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;career_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;immediate&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="career-path-planning">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Career Path Planning</a><a class="headerlink" href="#career-path-planning" title="Link to this heading"></a></h2>
<section id="generate-career-roadmap">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Generate Career Roadmap</a><a class="headerlink" href="#generate-career-roadmap" title="Link to this heading"></a></h3>
<p>Create a detailed career progression plan.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/career-transition/roadmap</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;current_position&quot;: {</span>
<span class="err">    &quot;role&quot;: &quot;Security Analyst&quot;,</span>
<span class="err">    &quot;experience_years&quot;: 3,</span>
<span class="err">    &quot;current_salary&quot;: 45000</span>
<span class="err">  },</span>
<span class="err">  &quot;career_goals&quot;: {</span>
<span class="err">    &quot;target_role&quot;: &quot;CISO&quot;,</span>
<span class="err">    &quot;target_timeline_years&quot;: 10,</span>
<span class="err">    &quot;target_salary&quot;: 150000,</span>
<span class="err">    &quot;preferred_industry&quot;: &quot;Financial Services&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;constraints&quot;: {</span>
<span class="err">    &quot;study_time_per_week&quot;: 8,</span>
<span class="err">    &quot;annual_training_budget&quot;: 10000,</span>
<span class="err">    &quot;willing_to_relocate&quot;: false,</span>
<span class="err">    &quot;management_interest&quot;: true</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;roadmap_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;roadmap_456&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;career_progression&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_timeline_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.73</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;key_milestones&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Senior Security Analyst&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;salary_target&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">58000</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;key_requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;CISSP Certification&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Team leadership experience&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Incident response expertise&quot;</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Manager&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;salary_target&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">78000</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;key_requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;CISM Certification&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Budget management experience&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Risk assessment skills&quot;</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.75</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security Director&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;salary_target&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">110000</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;key_requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;MBA or equivalent&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Board presentation experience&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Strategic planning skills&quot;</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.65</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISO&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;salary_target&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">150000</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;key_requirements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;Executive leadership experience&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Industry recognition&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Business acumen&quot;</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.55</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;development_plan&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;certifications_timeline&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8500</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;study_time_weeks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">16</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISM&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7500</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;study_time_weeks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">14</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;MBA (Cybersecurity Focus)&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45000</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;duration_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;skills_development&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;skill_category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Technical Leadership&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;development_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1-3&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;activities&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;Lead security projects&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Mentor junior analysts&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Present to senior management&quot;</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;skill_category&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Business Acumen&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;development_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;4-7&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;activities&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;MBA programme&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Cross-functional projects&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;Industry networking&quot;</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;financial_projection&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">75000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;cumulative_salary_increase&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">525000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;roi_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">700</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;net_career_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">450000</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;risk_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Competitive CISO market&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Requires significant time investment&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Economic conditions may affect progression&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;success_strategies&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Build strong professional network&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Gain diverse industry experience&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Develop thought leadership presence&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Maintain technical credibility&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="error-handling">
<h2><a class="toc-backref" href="#id11" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>Career Framework API error responses:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;insufficient_experience_data&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Unable to provide accurate career transition analysis due to limited experience information&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">422</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;missing_fields&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;specific_skills&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;project_experience&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;minimum_experience_years&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;suggestions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Complete detailed skills assessment&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Provide more specific experience information&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Common Error Codes</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">400</span></code> - Invalid career analysis parameters</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">404</span></code> - Role or career path not found</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">422</span></code> - Insufficient data for analysis</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">429</span></code> - Rate limit exceeded for career analysis</p></li>
</ul>
</section>
<section id="see-also">
<h2><a class="toc-backref" href="#id12" role="doc-backlink">See Also</a><a class="headerlink" href="#see-also" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../guides/user_guide.html"><span class="doc">🎯 Complete User Guide</span></a> - Career planning user guide</p></li>
<li><p><a class="reference internal" href="ai_assistant.html"><span class="doc">AI Study Assistant API</span></a> - AI-powered career recommendations</p></li>
<li><p><a class="reference internal" href="authentication.html"><span class="doc">Authentication &amp; Security</span></a> - API authentication</p></li>
<li><p><span class="xref std std-doc">../enterprise/analytics</span> - Enterprise career analytics</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="cost_calculator.html" class="btn btn-neutral float-left" title="Cost Calculator API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="progress_tracking.html" class="btn btn-neutral float-right" title="Progress Tracking API" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>