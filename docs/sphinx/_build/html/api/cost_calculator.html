<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Cost Calculator API &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/api/cost_calculator.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Career Framework API" href="career_framework.html" />
    <link rel="prev" title="Enterprise API" href="enterprise.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="enterprise.html">Enterprise API</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Cost Calculator API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#core-calculations">Core Calculations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#calculate-certification-costs">Calculate Certification Costs</a></li>
<li class="toctree-l3"><a class="reference internal" href="#compare-certifications">Compare Certifications</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#scenario-modelling">Scenario Modelling</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-cost-scenario">Create Cost Scenario</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#budget-planning">Budget Planning</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-training-budget">Create Training Budget</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#historical-analysis">Historical Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#cost-history-tracking">Cost History Tracking</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#currency-localisation">Currency &amp; Localisation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#exchange-rates">Exchange Rates</a></li>
<li class="toctree-l3"><a class="reference internal" href="#regional-pricing">Regional Pricing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Cost Calculator API</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/api/cost_calculator.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="cost-calculator-api">
<h1>Cost Calculator API<a class="headerlink" href="#cost-calculator-api" title="Link to this heading"></a></h1>
<p>The Cost Calculator API provides comprehensive financial analysis for certification investments, including ROI calculations, cost scenarios, and budget planning tools.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id1">Overview</a></p></li>
<li><p><a class="reference internal" href="#core-calculations" id="id2">Core Calculations</a></p>
<ul>
<li><p><a class="reference internal" href="#calculate-certification-costs" id="id3">Calculate Certification Costs</a></p></li>
<li><p><a class="reference internal" href="#compare-certifications" id="id4">Compare Certifications</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#scenario-modelling" id="id5">Scenario Modelling</a></p>
<ul>
<li><p><a class="reference internal" href="#create-cost-scenario" id="id6">Create Cost Scenario</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#budget-planning" id="id7">Budget Planning</a></p>
<ul>
<li><p><a class="reference internal" href="#create-training-budget" id="id8">Create Training Budget</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#historical-analysis" id="id9">Historical Analysis</a></p>
<ul>
<li><p><a class="reference internal" href="#cost-history-tracking" id="id10">Cost History Tracking</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#currency-localisation" id="id11">Currency &amp; Localisation</a></p>
<ul>
<li><p><a class="reference internal" href="#exchange-rates" id="id12">Exchange Rates</a></p></li>
<li><p><a class="reference internal" href="#regional-pricing" id="id13">Regional Pricing</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#error-handling" id="id14">Error Handling</a></p></li>
<li><p><a class="reference internal" href="#see-also" id="id15">See Also</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Cost Calculator offers:</p>
<ul class="simple">
<li><p><strong>Comprehensive Cost Analysis</strong> - Exam fees, materials, training, and opportunity costs</p></li>
<li><p><strong>ROI Calculations</strong> - Return on investment with salary impact analysis</p></li>
<li><p><strong>Multi-currency Support</strong> - Real-time exchange rates and localised pricing</p></li>
<li><p><strong>Scenario Modelling</strong> - Compare different investment strategies</p></li>
<li><p><strong>Budget Planning</strong> - Long-term financial planning for certification paths</p></li>
<li><p><strong>Historical Tracking</strong> - Cost trends and budget utilisation analysis</p></li>
</ul>
<p>All calculations use real-time market data and personalised factors.</p>
</section>
<section id="core-calculations">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Core Calculations</a><a class="headerlink" href="#core-calculations" title="Link to this heading"></a></h2>
<section id="calculate-certification-costs">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">Calculate Certification Costs</a><a class="headerlink" href="#calculate-certification-costs" title="Link to this heading"></a></h3>
<p>Perform comprehensive cost analysis for a specific certification.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/cost-calculator/calculate</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">{</span>
<span class="err">  &quot;certification_id&quot;: 45,</span>
<span class="err">  &quot;user_profile&quot;: {</span>
<span class="err">    &quot;location&quot;: &quot;United Kingdom&quot;,</span>
<span class="err">    &quot;current_salary&quot;: 65000,</span>
<span class="err">    &quot;currency&quot;: &quot;GBP&quot;,</span>
<span class="err">    &quot;experience_years&quot;: 3,</span>
<span class="err">    &quot;current_role&quot;: &quot;Security Analyst&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;study_plan&quot;: {</span>
<span class="err">    &quot;study_hours_per_week&quot;: 10,</span>
<span class="err">    &quot;target_completion_months&quot;: 6,</span>
<span class="err">    &quot;preferred_study_method&quot;: &quot;self_study&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;cost_options&quot;: {</span>
<span class="err">    &quot;include_training_course&quot;: false,</span>
<span class="err">    &quot;include_practice_exams&quot;: true,</span>
<span class="err">    &quot;include_books_materials&quot;: true,</span>
<span class="err">    &quot;include_opportunity_cost&quot;: true,</span>
<span class="err">    &quot;training_provider&quot;: null</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;certification_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;calculation_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;calc_12345&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;GBP&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;cost_breakdown&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;exam_fee&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">749</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;materials&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;official_study_guide&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">65</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;practice_tests&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;video_course&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;additional_books&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">120</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">230</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;training&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;bootcamp_course&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;online_training&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;opportunity_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;study_time_hours&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">240</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;hourly_rate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">31.25</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7500</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;miscellaneous&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;travel_expenses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;accommodation&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;retake_insurance&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">150</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">150</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;total_investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8629</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;roi_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;expected_salary_increase&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;immediate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;year_1&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;year_3&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;year_5&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">22000</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;payback_period&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">12.2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Investment recovered in approximately 1 year&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;roi_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;year_1&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">98.5</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;year_3&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">174.0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;year_5&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">255.0</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;net_present_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45670</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;career_impact_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.7</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;market_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;average_salary_with_cert&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">73500</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_salary_without_cert&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">65000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;job_market_demand&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certification_popularity&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;regional_salary_data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;london&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">78000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;manchester&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">68000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;edinburgh&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">70000</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;cost_optimisation&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Consider second-hand study materials to reduce costs&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Look for employer training budget reimbursement&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;timing_advice&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Optimal time to pursue based on career stage and market conditions&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;alternative_paths&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security+&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2500</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">145</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Lower cost entry point with good ROI&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="compare-certifications">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Compare Certifications</a><a class="headerlink" href="#compare-certifications" title="Link to this heading"></a></h3>
<p>Compare costs and ROI across multiple certifications.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/cost-calculator/compare</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;certification_ids&quot;: [45, 67, 89],</span>
<span class="err">  &quot;user_profile&quot;: {</span>
<span class="err">    &quot;location&quot;: &quot;United Kingdom&quot;,</span>
<span class="err">    &quot;current_salary&quot;: 65000,</span>
<span class="err">    &quot;currency&quot;: &quot;GBP&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;comparison_criteria&quot;: {</span>
<span class="err">    &quot;include_opportunity_cost&quot;: true,</span>
<span class="err">    &quot;time_horizon_years&quot;: 3,</span>
<span class="err">    &quot;discount_rate&quot;: 0.05</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;comparison_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;comp_67890&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8629</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;roi_3_year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">174.0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;payback_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">12.2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;career_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.7</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;difficulty_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">7.5</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;time_to_complete_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">67</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISM&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;total_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7850</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;roi_3_year&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">156.0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;payback_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">13.8</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;career_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;difficulty_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">7.0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;time_to_complete_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;best_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Highest 3-year ROI and career impact&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;most_cost_effective&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">67</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISM&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Lower total investment with strong returns&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;fastest_payback&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Shortest payback period&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_certifications_compared&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8240</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">165.0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;cost_range&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;min&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7850</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;max&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8629</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="scenario-modelling">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Scenario Modelling</a><a class="headerlink" href="#scenario-modelling" title="Link to this heading"></a></h2>
<section id="create-cost-scenario">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Create Cost Scenario</a><a class="headerlink" href="#create-cost-scenario" title="Link to this heading"></a></h3>
<p>Model different cost scenarios for strategic planning.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/cost-calculator/scenarios</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;scenario_name&quot;: &quot;Aggressive Certification Path&quot;,</span>
<span class="err">  &quot;description&quot;: &quot;Multiple certifications over 18 months&quot;,</span>
<span class="err">  &quot;user_profile&quot;: {</span>
<span class="err">    &quot;location&quot;: &quot;United Kingdom&quot;,</span>
<span class="err">    &quot;current_salary&quot;: 65000,</span>
<span class="err">    &quot;currency&quot;: &quot;GBP&quot;</span>
<span class="err">  },</span>
<span class="err">  &quot;certification_sequence&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;certification_id&quot;: 23,</span>
<span class="err">      &quot;start_month&quot;: 1,</span>
<span class="err">      &quot;study_intensity&quot;: &quot;high&quot;</span>
<span class="err">    },</span>
<span class="err">    {</span>
<span class="err">      &quot;certification_id&quot;: 45,</span>
<span class="err">      &quot;start_month&quot;: 7,</span>
<span class="err">      &quot;study_intensity&quot;: &quot;medium&quot;</span>
<span class="err">    },</span>
<span class="err">    {</span>
<span class="err">      &quot;certification_id&quot;: 67,</span>
<span class="err">      &quot;start_month&quot;: 13,</span>
<span class="err">      &quot;study_intensity&quot;: &quot;medium&quot;</span>
<span class="err">    }</span>
<span class="err">  ],</span>
<span class="err">  &quot;assumptions&quot;: {</span>
<span class="err">    &quot;salary_increase_timing&quot;: &quot;after_each_cert&quot;,</span>
<span class="err">    &quot;employer_reimbursement&quot;: 0.5,</span>
<span class="err">    &quot;study_efficiency_improvement&quot;: 0.15</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;scenario_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;scenario_abc123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;scenario_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Aggressive Certification Path&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total_timeline_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;financial_summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18500</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;employer_reimbursement&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">9250</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;net_personal_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">9250</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;cumulative_salary_increase&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">28000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;net_benefit_18_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">18750</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;roi_percentage&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">202.7</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;certification_timeline&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Security+&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completion_month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;salary_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5000</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">7</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certification&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8629</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completion_month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;salary_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12000</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;cash_flow_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;monthly_cash_flow&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">-625</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;salary_increase&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;net_flow&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">-625</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;break_even_month&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">14</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;peak_negative_cash_flow&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">-9250</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;risk_analysis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;success_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.78</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;risk_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Aggressive timeline may impact success rates&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;High study intensity requires significant time commitment&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;mitigation_strategies&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Build buffer time into schedule&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Consider reducing study intensity if needed&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="budget-planning">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">Budget Planning</a><a class="headerlink" href="#budget-planning" title="Link to this heading"></a></h2>
<section id="create-training-budget">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Create Training Budget</a><a class="headerlink" href="#create-training-budget" title="Link to this heading"></a></h3>
<p>Plan annual training budgets for individuals or teams.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/cost-calculator/budget-plan</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;budget_type&quot;: &quot;individual&quot;,</span>
<span class="err">  &quot;planning_period&quot;: &quot;annual&quot;,</span>
<span class="err">  &quot;budget_amount&quot;: 15000,</span>
<span class="err">  &quot;currency&quot;: &quot;GBP&quot;,</span>
<span class="err">  &quot;user_profile&quot;: {</span>
<span class="err">    &quot;current_role&quot;: &quot;Security Analyst&quot;,</span>
<span class="err">    &quot;experience_years&quot;: 3,</span>
<span class="err">    &quot;career_goals&quot;: [&quot;Security Architect&quot;, &quot;Team Lead&quot;]</span>
<span class="err">  },</span>
<span class="err">  &quot;constraints&quot;: {</span>
<span class="err">    &quot;max_certifications_per_year&quot;: 2,</span>
<span class="err">    &quot;preferred_study_intensity&quot;: &quot;medium&quot;,</span>
<span class="err">    &quot;employer_reimbursement_rate&quot;: 0.75</span>
<span class="err">  }</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;budget_plan_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;budget_def456&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;planning_period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;GBP&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;recommended_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;estimated_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8629</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;employer_reimbursement&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6472</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;personal_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2157</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;expected_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Q2 2025&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;roi_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">8.7</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">78</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISA&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;priority&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;estimated_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;employer_reimbursement&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4875</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;personal_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1625</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;expected_completion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Q4 2025&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;roi_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">7.9</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;budget_allocation&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_certification_costs&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15129</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total_reimbursement&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">11347</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;net_personal_investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3782</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;budget_utilisation&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">100.9</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;remaining_budget&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">-129</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;quarterly_breakdown&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;Q1&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;planned_spending&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4315</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP - Start&quot;</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Q2&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;planned_spending&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">4314</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP - Complete&quot;</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Q3&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;planned_spending&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3250</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISA - Start&quot;</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Q4&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;planned_spending&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3250</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISA - Complete&quot;</span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;budget_optimisation&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Consider spreading CISA to next year to stay within budget&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Look for additional employer training funds&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;alternative_options&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Replace CISA with lower-cost Security+ for immediate impact&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="historical-analysis">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Historical Analysis</a><a class="headerlink" href="#historical-analysis" title="Link to this heading"></a></h2>
<section id="cost-history-tracking">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Cost History Tracking</a><a class="headerlink" href="#cost-history-tracking" title="Link to this heading"></a></h3>
<p>Track historical costs and analyse spending patterns.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/cost-calculator/history</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?period=12m&amp;include_projections=true</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;user_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;12m&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;generated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;historical_spending&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;total_invested&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">12500</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;certifications_completed&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;average_cost_per_cert&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6250</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;employer_reimbursement_received&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8750</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;net_personal_investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3750</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;monthly_breakdown&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;month&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;spending&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2500</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Security+&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;reimbursement&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1875</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;roi_realised&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;salary_increases_received&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;career_advancement&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Promoted to Senior Security Analyst&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;actual_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">300.0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;projected_vs_actual&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;projected_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">250.0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;variance&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">20.0</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;future_projections&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;next_12_months&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;planned_certifications&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;CISM&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;estimated_investment&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">16000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;projected_salary_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">20000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;expected_roi&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">225.0</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;insights&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;spending_trends&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Increasing investment in advanced certifications&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;roi_performance&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Exceeding projected returns&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="s2">&quot;Continue current certification strategy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;Consider accelerating timeline for CISSP&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="currency-localisation">
<h2><a class="toc-backref" href="#id11" role="doc-backlink">Currency &amp; Localisation</a><a class="headerlink" href="#currency-localisation" title="Link to this heading"></a></h2>
<section id="exchange-rates">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Exchange Rates</a><a class="headerlink" href="#exchange-rates" title="Link to this heading"></a></h3>
<p>Get current exchange rates for cost calculations.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/cost-calculator/exchange-rates</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?base_currency=GBP&amp;target_currencies=USD,EUR,CAD</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;base_currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;GBP&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;rates&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;USD&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.2745</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;EUR&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.1823</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;CAD&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.7234</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;last_updated&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T10:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;source&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Bank of England&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;next_update&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-01-15T16:00:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="regional-pricing">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">Regional Pricing</a><a class="headerlink" href="#regional-pricing" title="Link to this heading"></a></h3>
<p>Get localised pricing for certifications by region.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/cost-calculator/regional-pricing</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">?certification_id=45&amp;regions=UK,US,EU,APAC</span>
</pre></div>
</div>
<p><strong>Response</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;certification_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">45</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;certification_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CISSP&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;regional_pricing&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;UK&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;exam_fee&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">749</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;GBP&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;materials_cost_range&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;min&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">200</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;max&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">500</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;training_cost_range&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;min&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2000</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;max&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5000</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;US&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;exam_fee&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">749</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;USD&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;materials_cost_range&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;min&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">250</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;max&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">600</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;cost_factors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;regional_salary_multipliers&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;UK&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;US&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.15</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;EU&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.95</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;purchasing_power_adjustments&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;UK&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.0</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;US&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">1.08</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;EU&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.92</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="error-handling">
<h2><a class="toc-backref" href="#id14" role="doc-backlink">Error Handling</a><a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>Cost Calculator API error responses:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;insufficient_market_data&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Unable to calculate accurate ROI due to limited salary data for this region&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">422</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;region&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Remote Islands&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;available_data_points&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;minimum_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;suggestions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Use broader regional data&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Provide custom salary information&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Common Error Codes</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">400</span></code> - Invalid calculation parameters</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">404</span></code> - Certification or region not found</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">422</span></code> - Insufficient data for accurate calculations</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">429</span></code> - Rate limit exceeded for calculations</p></li>
</ul>
</section>
<section id="see-also">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">See Also</a><a class="headerlink" href="#see-also" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../guides/user_guide.html"><span class="doc">🎯 Complete User Guide</span></a> - Cost calculator user guide</p></li>
<li><p><a class="reference internal" href="authentication.html"><span class="doc">Authentication &amp; Security</span></a> - API authentication</p></li>
<li><p><a class="reference internal" href="../installation.html"><span class="doc">Installation Guide</span></a> - Setup and configuration</p></li>
<li><p><span class="xref std std-doc">../enterprise/analytics</span> - Enterprise cost analytics</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="enterprise.html" class="btn btn-neutral float-left" title="Enterprise API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="career_framework.html" class="btn btn-neutral float-right" title="Career Framework API" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>