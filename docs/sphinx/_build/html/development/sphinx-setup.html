<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>🛠️ Sphinx Documentation Setup Guide &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/development/sphinx-setup.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">🛠️ Sphinx Documentation Setup Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/development/sphinx-setup.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="sphinx-documentation-setup-guide">
<h1>🛠️ Sphinx Documentation Setup Guide<a class="headerlink" href="#sphinx-documentation-setup-guide" title="Link to this heading"></a></h1>
<p><strong>Complete Guide to CertRats Sphinx Documentation System</strong></p>
<p>This guide provides comprehensive instructions for setting up, configuring, and maintaining the Sphinx documentation system for the CertRats platform.</p>
<section id="overview">
<h2>📋 Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The CertRats documentation system uses Sphinx, a powerful documentation generator that creates beautiful, searchable, and cross-referenced documentation from reStructuredText files.</p>
<p><strong>Key Features:</strong></p>
<ul class="simple">
<li><p><strong>Modern Theme</strong> - Responsive design with dark mode support</p></li>
<li><p><strong>Interactive Elements</strong> - Mermaid diagrams, tabs, and code blocks</p></li>
<li><p><strong>API Documentation</strong> - Automatic API reference generation</p></li>
<li><p><strong>Multi-format Output</strong> - HTML, PDF, and ePub generation</p></li>
<li><p><strong>Search Integration</strong> - Full-text search with advanced filtering</p></li>
<li><p><strong>Cross-references</strong> - Automatic linking between documents</p></li>
<li><p><strong>Internationalization</strong> - Multi-language support ready</p></li>
</ul>
</section>
<section id="quick-setup">
<h2>🚀 Quick Setup<a class="headerlink" href="#quick-setup" title="Link to this heading"></a></h2>
<p><strong>Prerequisites:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Python 3.8+ required</span>
python<span class="w"> </span>--version

<span class="c1"># Install Sphinx and extensions</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>docs/requirements-docs.txt
</pre></div>
</div>
<p><strong>Build Documentation:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Navigate to Sphinx directory</span>
<span class="nb">cd</span><span class="w"> </span>docs/sphinx

<span class="c1"># Build HTML documentation</span>
make<span class="w"> </span>html

<span class="c1"># Open in browser</span>
open<span class="w"> </span>_build/html/index.html
</pre></div>
</div>
</section>
<section id="configuration-details">
<h2>🔧 Configuration Details<a class="headerlink" href="#configuration-details" title="Link to this heading"></a></h2>
<p><strong>Sphinx Configuration (conf.py):</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Project information</span>
<span class="n">project</span> <span class="o">=</span> <span class="s1">&#39;CertRats&#39;</span>
<span class="n">copyright</span> <span class="o">=</span> <span class="s1">&#39;2025, CertRats Team&#39;</span>
<span class="n">author</span> <span class="o">=</span> <span class="s1">&#39;CertRats Team&#39;</span>
<span class="n">release</span> <span class="o">=</span> <span class="s1">&#39;1.0.0&#39;</span>
<span class="n">version</span> <span class="o">=</span> <span class="s1">&#39;1.0&#39;</span>

<span class="c1"># Extensions for enhanced functionality</span>
<span class="n">extensions</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;sphinx.ext.autodoc&#39;</span><span class="p">,</span>          <span class="c1"># Automatic documentation from docstrings</span>
    <span class="s1">&#39;sphinx.ext.viewcode&#39;</span><span class="p">,</span>         <span class="c1"># Source code links</span>
    <span class="s1">&#39;sphinx.ext.napoleon&#39;</span><span class="p">,</span>         <span class="c1"># Google/NumPy docstring support</span>
    <span class="s1">&#39;sphinx.ext.intersphinx&#39;</span><span class="p">,</span>      <span class="c1"># Cross-project references</span>
    <span class="s1">&#39;sphinx.ext.todo&#39;</span><span class="p">,</span>             <span class="c1"># TODO items support</span>
    <span class="s1">&#39;sphinx.ext.coverage&#39;</span><span class="p">,</span>         <span class="c1"># Documentation coverage</span>
    <span class="s1">&#39;sphinx.ext.ifconfig&#39;</span><span class="p">,</span>         <span class="c1"># Conditional content</span>
    <span class="s1">&#39;sphinx_tabs.tabs&#39;</span><span class="p">,</span>            <span class="c1"># Tabbed content</span>
    <span class="s1">&#39;sphinx_copybutton&#39;</span><span class="p">,</span>           <span class="c1"># Copy button for code blocks</span>
    <span class="s1">&#39;myst_parser&#39;</span><span class="p">,</span>                 <span class="c1"># Markdown support</span>
    <span class="s1">&#39;sphinxext.opengraph&#39;</span><span class="p">,</span>         <span class="c1"># Social media meta tags</span>
    <span class="s1">&#39;sphinx_design&#39;</span><span class="p">,</span>               <span class="c1"># Modern design elements</span>
    <span class="s1">&#39;sphinx_togglebutton&#39;</span><span class="p">,</span>         <span class="c1"># Toggle buttons</span>
    <span class="s1">&#39;sphinx_inline_tabs&#39;</span><span class="p">,</span>          <span class="c1"># Inline tabs</span>
<span class="p">]</span>

<span class="c1"># Source file configuration</span>
<span class="n">source_suffix</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s1">&#39;.rst&#39;</span><span class="p">:</span> <span class="kc">None</span><span class="p">,</span>
    <span class="s1">&#39;.md&#39;</span><span class="p">:</span> <span class="s1">&#39;myst_parser&#39;</span><span class="p">,</span>
<span class="p">}</span>

<span class="c1"># Master document</span>
<span class="n">master_doc</span> <span class="o">=</span> <span class="s1">&#39;index&#39;</span>

<span class="c1"># Language and localization</span>
<span class="n">language</span> <span class="o">=</span> <span class="s1">&#39;en_GB&#39;</span>
<span class="n">locale_dirs</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;locale/&#39;</span><span class="p">]</span>
<span class="n">gettext_compact</span> <span class="o">=</span> <span class="kc">False</span>
</pre></div>
</div>
<p><strong>Theme Configuration:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># HTML theme settings</span>
<span class="n">html_theme</span> <span class="o">=</span> <span class="s1">&#39;sphinx_rtd_theme&#39;</span>
<span class="n">html_theme_options</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s1">&#39;analytics_id&#39;</span><span class="p">:</span> <span class="s1">&#39;G-XXXXXXXXXX&#39;</span><span class="p">,</span>
    <span class="s1">&#39;analytics_anonymize_ip&#39;</span><span class="p">:</span> <span class="kc">False</span><span class="p">,</span>
    <span class="s1">&#39;logo_only&#39;</span><span class="p">:</span> <span class="kc">False</span><span class="p">,</span>
    <span class="s1">&#39;display_version&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
    <span class="s1">&#39;prev_next_buttons_location&#39;</span><span class="p">:</span> <span class="s1">&#39;bottom&#39;</span><span class="p">,</span>
    <span class="s1">&#39;style_external_links&#39;</span><span class="p">:</span> <span class="kc">False</span><span class="p">,</span>
    <span class="s1">&#39;vcs_pageview_mode&#39;</span><span class="p">:</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
    <span class="s1">&#39;style_nav_header_background&#39;</span><span class="p">:</span> <span class="s1">&#39;#2980B9&#39;</span><span class="p">,</span>
    <span class="s1">&#39;collapse_navigation&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
    <span class="s1">&#39;sticky_navigation&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
    <span class="s1">&#39;navigation_depth&#39;</span><span class="p">:</span> <span class="mi">4</span><span class="p">,</span>
    <span class="s1">&#39;includehidden&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
    <span class="s1">&#39;titles_only&#39;</span><span class="p">:</span> <span class="kc">False</span>
<span class="p">}</span>

<span class="c1"># Custom CSS and JavaScript</span>
<span class="n">html_static_path</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;_static&#39;</span><span class="p">]</span>
<span class="n">html_css_files</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;custom.css&#39;</span><span class="p">,</span>
    <span class="s1">&#39;mermaid.css&#39;</span><span class="p">,</span>
<span class="p">]</span>
<span class="n">html_js_files</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js&#39;</span><span class="p">,</span>
    <span class="s1">&#39;mermaid-init.js&#39;</span><span class="p">,</span>
    <span class="s1">&#39;custom.js&#39;</span><span class="p">,</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
<section id="directory-structure">
<h2>📁 Directory Structure<a class="headerlink" href="#directory-structure" title="Link to this heading"></a></h2>
<p><strong>Documentation Organization:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>docs/sphinx/
├── conf.py                     # Sphinx configuration
├── index.rst                   # Main documentation index
├── Makefile                    # Build automation
├── make.bat                    # Windows build script
├── requirements-docs.txt       # Python dependencies
├── _static/                    # Static assets
│   ├── custom.css             # Custom styling
│   ├── custom.js              # Custom JavaScript
│   ├── mermaid.css            # Mermaid diagram styling
│   ├── mermaid-init.js        # Mermaid initialization
│   └── images/                # Documentation images
├── _templates/                 # Custom templates
│   ├── layout.html            # Base layout template
│   ├── breadcrumbs.html       # Navigation breadcrumbs
│   └── searchbox.html         # Custom search box
├── _build/                     # Generated output
│   ├── html/                  # HTML documentation
│   ├── latex/                 # LaTeX output
│   └── doctrees/              # Sphinx doctrees
├── api/                        # API reference documentation
├── guides/                     # User and developer guides
├── faq/                        # Frequently asked questions
├── prds/                       # Product requirement documents
├── development/                # Development documentation
├── enterprise/                 # Enterprise features
└── ai/                         # AI and ML documentation
</pre></div>
</div>
</section>
<section id="custom-styling">
<h2>🎨 Custom Styling<a class="headerlink" href="#custom-styling" title="Link to this heading"></a></h2>
<p><strong>Custom CSS (_static/custom.css):</strong></p>
<div class="highlight-css notranslate"><div class="highlight"><pre><span></span><span class="c">/* CertRats Documentation Custom Styles */</span>

<span class="c">/* Color scheme */</span>
<span class="p">:</span><span class="nd">root</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nv">--certrats-primary</span><span class="p">:</span><span class="w"> </span><span class="mh">#2980B9</span><span class="p">;</span>
<span class="w">    </span><span class="nv">--certrats-secondary</span><span class="p">:</span><span class="w"> </span><span class="mh">#3498DB</span><span class="p">;</span>
<span class="w">    </span><span class="nv">--certrats-accent</span><span class="p">:</span><span class="w"> </span><span class="mh">#E74C3C</span><span class="p">;</span>
<span class="w">    </span><span class="nv">--certrats-success</span><span class="p">:</span><span class="w"> </span><span class="mh">#27AE60</span><span class="p">;</span>
<span class="w">    </span><span class="nv">--certrats-warning</span><span class="p">:</span><span class="w"> </span><span class="mh">#F39C12</span><span class="p">;</span>
<span class="w">    </span><span class="nv">--certrats-dark</span><span class="p">:</span><span class="w"> </span><span class="mh">#2C3E50</span><span class="p">;</span>
<span class="p">}</span>

<span class="c">/* Enhanced navigation */</span>
<span class="p">.</span><span class="nc">wy-nav-side</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">background</span><span class="p">:</span><span class="w"> </span><span class="nb">linear-gradient</span><span class="p">(</span><span class="mi">180</span><span class="kt">deg</span><span class="p">,</span><span class="w"> </span><span class="nf">var</span><span class="p">(</span><span class="nv">--certrats-primary</span><span class="p">)</span><span class="w"> </span><span class="mi">0</span><span class="kt">%</span><span class="p">,</span><span class="w"> </span><span class="nf">var</span><span class="p">(</span><span class="nv">--certrats-dark</span><span class="p">)</span><span class="w"> </span><span class="mi">100</span><span class="kt">%</span><span class="p">);</span>
<span class="p">}</span>

<span class="c">/* Improved code blocks */</span>
<span class="p">.</span><span class="nc">highlight</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">border-radius</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="kt">px</span><span class="p">;</span>
<span class="w">    </span><span class="k">box-shadow</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">2</span><span class="kt">px</span><span class="w"> </span><span class="mi">4</span><span class="kt">px</span><span class="w"> </span><span class="nb">rgba</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mf">0.1</span><span class="p">);</span>
<span class="p">}</span>

<span class="c">/* Responsive design improvements */</span>
<span class="p">@</span><span class="k">media</span><span class="w"> </span><span class="nt">screen</span><span class="w"> </span><span class="nt">and</span><span class="w"> </span><span class="o">(</span><span class="nt">max-width</span><span class="o">:</span><span class="w"> </span><span class="nt">768px</span><span class="o">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="p">.</span><span class="nc">wy-nav-content-wrap</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">margin-left</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="c">/* Mermaid diagram styling */</span>
<span class="p">.</span><span class="nc">mermaid</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">text-align</span><span class="p">:</span><span class="w"> </span><span class="kc">center</span><span class="p">;</span>
<span class="w">    </span><span class="k">margin</span><span class="p">:</span><span class="w"> </span><span class="mi">20</span><span class="kt">px</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="p">}</span>

<span class="c">/* Custom admonitions */</span>
<span class="p">.</span><span class="nc">admonition</span><span class="p">.</span><span class="nc">tip</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">border-left</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span><span class="kt">px</span><span class="w"> </span><span class="kc">solid</span><span class="w"> </span><span class="nf">var</span><span class="p">(</span><span class="nv">--certrats-success</span><span class="p">);</span>
<span class="p">}</span>

<span class="p">.</span><span class="nc">admonition</span><span class="p">.</span><span class="nc">warning</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">border-left</span><span class="p">:</span><span class="w"> </span><span class="mi">4</span><span class="kt">px</span><span class="w"> </span><span class="kc">solid</span><span class="w"> </span><span class="nf">var</span><span class="p">(</span><span class="nv">--certrats-warning</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Mermaid Integration (_static/mermaid-init.js):</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Initialize Mermaid diagrams</span>
<span class="nb">document</span><span class="p">.</span><span class="nx">addEventListener</span><span class="p">(</span><span class="s1">&#39;DOMContentLoaded&#39;</span><span class="p">,</span><span class="w"> </span><span class="kd">function</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">mermaid</span><span class="p">.</span><span class="nx">initialize</span><span class="p">({</span>
<span class="w">        </span><span class="nx">startOnLoad</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nx">theme</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;default&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">themeVariables</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nx">primaryColor</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;#2980B9&#39;</span><span class="p">,</span>
<span class="w">            </span><span class="nx">primaryTextColor</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;#2C3E50&#39;</span><span class="p">,</span>
<span class="w">            </span><span class="nx">primaryBorderColor</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;#3498DB&#39;</span><span class="p">,</span>
<span class="w">            </span><span class="nx">lineColor</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;#34495E&#39;</span><span class="p">,</span>
<span class="w">            </span><span class="nx">secondaryColor</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;#ECF0F1&#39;</span><span class="p">,</span>
<span class="w">            </span><span class="nx">tertiaryColor</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;#F8F9FA&#39;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nx">flowchart</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nx">useMaxWidth</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">            </span><span class="nx">htmlLabels</span><span class="o">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">            </span><span class="nx">curve</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;basis&#39;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nx">sequence</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nx">diagramMarginX</span><span class="o">:</span><span class="w"> </span><span class="mf">50</span><span class="p">,</span>
<span class="w">            </span><span class="nx">diagramMarginY</span><span class="o">:</span><span class="w"> </span><span class="mf">10</span><span class="p">,</span>
<span class="w">            </span><span class="nx">actorMargin</span><span class="o">:</span><span class="w"> </span><span class="mf">50</span><span class="p">,</span>
<span class="w">            </span><span class="nx">width</span><span class="o">:</span><span class="w"> </span><span class="mf">150</span><span class="p">,</span>
<span class="w">            </span><span class="nx">height</span><span class="o">:</span><span class="w"> </span><span class="mf">65</span><span class="p">,</span>
<span class="w">            </span><span class="nx">boxMargin</span><span class="o">:</span><span class="w"> </span><span class="mf">10</span><span class="p">,</span>
<span class="w">            </span><span class="nx">boxTextMargin</span><span class="o">:</span><span class="w"> </span><span class="mf">5</span><span class="p">,</span>
<span class="w">            </span><span class="nx">noteMargin</span><span class="o">:</span><span class="w"> </span><span class="mf">10</span><span class="p">,</span>
<span class="w">            </span><span class="nx">messageMargin</span><span class="o">:</span><span class="w"> </span><span class="mf">35</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">});</span>
<span class="p">});</span>
</pre></div>
</div>
</section>
<section id="writing-documentation">
<h2>📝 Writing Documentation<a class="headerlink" href="#writing-documentation" title="Link to this heading"></a></h2>
<p><strong>reStructuredText Basics:</strong></p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span><span class="gh">Main Title</span>
<span class="gh">==========</span>

<span class="gh">Section Title</span>
<span class="gh">-------------</span>

<span class="gh">Subsection</span>
<span class="gh">~~~~~~~~~~</span>

<span class="gs">**Bold text**</span> and <span class="ge">*italic text*</span>

<span class="m">-</span> Bullet point
<span class="m">-</span> Another point

<span class="m">1.</span> Numbered list
<span class="m">2.</span> Second item

<span class="nv">`Inline code`</span> and<span class="se">::</span>

<span class="s">    Code block</span>
<span class="s">    with syntax highlighting</span>

<span class="p">..</span> <span class="ow">note</span><span class="p">::</span>
   This is a note admonition

<span class="p">..</span> <span class="ow">warning</span><span class="p">::</span>
   This is a warning admonition
</pre></div>
</div>
<p><strong>Cross-References:</strong></p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span># Link to other documents
<span class="na">:doc:</span><span class="nv">`user_guide`</span>
<span class="na">:doc:</span><span class="nv">`api/authentication`</span>

# Link to sections
<span class="na">:ref:</span><span class="nv">`installation-guide`</span>

# External links
<span class="s">`Sphinx Documentation </span><span class="si">&lt;https://www.sphinx-doc.org/&gt;</span><span class="s">`_</span>
</pre></div>
</div>
<p><strong>API Documentation:</strong></p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span><span class="p">..</span> <span class="ow">automodule</span><span class="p">::</span> certrats.api.authentication
   <span class="nc">:members:</span>
   <span class="nc">:undoc-members:</span>
   <span class="nc">:show-inheritance:</span>

<span class="p">..</span> <span class="ow">autoclass</span><span class="p">::</span> certrats.models.User
   <span class="nc">:members:</span>
   <span class="nc">:inherited-members:</span>
</pre></div>
</div>
<p><strong>HTTP API Documentation:</strong></p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span><span class="p">..</span> <span class="ow">http:get</span><span class="p">::</span> /api/v1/users

   Get user information

   <span class="gs">**Example request**</span>:

<span class="p">   ..</span> <span class="ow">sourcecode</span><span class="p">::</span> <span class="k">http</span>

      <span class="nf">GET</span> <span class="nn">/api/v1/users</span> <span class="kr">HTTP</span><span class="o">/</span><span class="m">1.1</span>
      <span class="na">Host</span><span class="o">:</span> <span class="l">api.certrats.com</span>
      <span class="na">Authorization</span><span class="o">:</span> <span class="l">Bearer &lt;token&gt;</span>

   <span class="gs">**Example response**</span>:

<span class="p">   ..</span> <span class="ow">sourcecode</span><span class="p">::</span> <span class="k">http</span>

      <span class="kr">HTTP</span><span class="o">/</span><span class="m">1.1</span> <span class="m">200</span> <span class="ne">OK</span>
      <span class="na">Content-Type</span><span class="o">:</span> <span class="l">application/json</span>

      <span class="p">{</span>
      <span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
      <span class="w">  </span><span class="nt">&quot;username&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;john_doe&quot;</span><span class="p">,</span>
      <span class="w">  </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span>
      <span class="p">}</span>

   <span class="nc">:statuscode 200:</span> Success
   <span class="nc">:statuscode 401:</span> Unauthorized
   <span class="nc">:statuscode 404:</span> User not found
</pre></div>
</div>
</section>
<section id="advanced-features">
<h2>🔧 Advanced Features<a class="headerlink" href="#advanced-features" title="Link to this heading"></a></h2>
<p><strong>Mermaid Diagrams:</strong></p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span><span class="p">..</span> <span class="ow">mermaid</span><span class="p">::</span>

   flowchart TD
       A[User Login] --&gt; B[Dashboard]
       B --&gt; C[Certification Explorer]
       C --&gt; D[Learning Path]
       D --&gt; E[Progress Tracking]
</pre></div>
</div>
<p><strong>Tabbed Content:</strong></p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span><span class="p">..</span> <span class="ow">tabs</span><span class="p">::</span>

<span class="p">   ..</span> <span class="ow">tab</span><span class="p">::</span> Python

<span class="p">      ..</span> <span class="ow">code-block</span><span class="p">::</span> <span class="k">python</span>

         <span class="kn">import</span> <span class="nn">requests</span>
         <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;/api/users&#39;</span><span class="p">)</span>

<span class="p">   ..</span> <span class="ow">tab</span><span class="p">::</span> JavaScript

<span class="p">      ..</span> <span class="ow">code-block</span><span class="p">::</span> <span class="k">javascript</span>

         <span class="nx">fetch</span><span class="p">(</span><span class="s1">&#39;/api/users&#39;</span><span class="p">)</span>
         <span class="w">  </span><span class="p">.</span><span class="nx">then</span><span class="p">(</span><span class="nx">response</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">response</span><span class="p">.</span><span class="nx">json</span><span class="p">())</span>

<span class="p">   ..</span> <span class="ow">tab</span><span class="p">::</span> cURL

<span class="p">      ..</span> <span class="ow">code-block</span><span class="p">::</span> bash

         curl -X GET /api/users
</pre></div>
</div>
<p><strong>Design Elements:</strong></p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span><span class="p">..</span> <span class="ow">grid</span><span class="p">::</span> 2

<span class="p">   ..</span> <span class="ow">grid-item-card</span><span class="p">::</span> Feature 1
      <span class="nc">:img-top:</span> _static/images/feature1.png

      Description of feature 1

<span class="p">   ..</span> <span class="ow">grid-item-card</span><span class="p">::</span> Feature 2
      <span class="nc">:img-top:</span> _static/images/feature2.png

      Description of feature 2
</pre></div>
</div>
</section>
<section id="build-and-deployment">
<h2>🚀 Build and Deployment<a class="headerlink" href="#build-and-deployment" title="Link to this heading"></a></h2>
<p><strong>Local Development:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements-docs.txt

<span class="c1"># Build documentation</span>
<span class="nb">cd</span><span class="w"> </span>docs/sphinx
make<span class="w"> </span>html

<span class="c1"># Live reload during development</span>
sphinx-autobuild<span class="w"> </span>.<span class="w"> </span>_build/html

<span class="c1"># Clean build</span>
make<span class="w"> </span>clean<span class="w"> </span>html
</pre></div>
</div>
<p><strong>Production Build:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Build all formats</span>
make<span class="w"> </span>html<span class="w"> </span>latexpdf<span class="w"> </span>epub

<span class="c1"># Check for broken links</span>
make<span class="w"> </span>linkcheck

<span class="c1"># Check spelling</span>
make<span class="w"> </span>spelling
</pre></div>
</div>
<p><strong>GitHub Actions Deployment:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build and Deploy Documentation</span>

<span class="nt">on</span><span class="p">:</span>
<span class="w">  </span><span class="nt">push</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">main</span><span class="p p-Indicator">]</span>
<span class="w">    </span><span class="nt">paths</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&#39;docs/sphinx/**&#39;</span><span class="p p-Indicator">]</span>

<span class="nt">jobs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">build-and-deploy</span><span class="p">:</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Setup Python</span>
<span class="w">        </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/setup-python@v4</span>
<span class="w">        </span><span class="nt">with</span><span class="p">:</span>
<span class="w">          </span><span class="nt">python-version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.9&#39;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Install dependencies</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">          </span><span class="no">pip install -r docs/requirements-docs.txt</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build documentation</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">          </span><span class="no">cd docs/sphinx</span>
<span class="w">          </span><span class="no">make html</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to GitHub Pages</span>
<span class="w">        </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">peaceiris/actions-gh-pages@v3</span>
<span class="w">        </span><span class="nt">with</span><span class="p">:</span>
<span class="w">          </span><span class="nt">github_token</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${{ secrets.GITHUB_TOKEN }}</span>
<span class="w">          </span><span class="nt">publish_dir</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docs/sphinx/_build/html</span>
</pre></div>
</div>
</section>
<section id="quality-assurance">
<h2>🔍 Quality Assurance<a class="headerlink" href="#quality-assurance" title="Link to this heading"></a></h2>
<p><strong>Documentation Testing:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Test documentation build</span>
sphinx-build<span class="w"> </span>-b<span class="w"> </span>dummy<span class="w"> </span>.<span class="w"> </span>_build/dummy

<span class="c1"># Check for warnings</span>
sphinx-build<span class="w"> </span>-W<span class="w"> </span>-b<span class="w"> </span>html<span class="w"> </span>.<span class="w"> </span>_build/html

<span class="c1"># Validate links</span>
sphinx-build<span class="w"> </span>-b<span class="w"> </span>linkcheck<span class="w"> </span>.<span class="w"> </span>_build/linkcheck

<span class="c1"># Check spelling</span>
sphinx-build<span class="w"> </span>-b<span class="w"> </span>spelling<span class="w"> </span>.<span class="w"> </span>_build/spelling
</pre></div>
</div>
<p><strong>Content Validation:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Validate reStructuredText syntax</span>
rst-lint<span class="w"> </span>*.rst

<span class="c1"># Check for orphaned files</span>
sphinx-build<span class="w"> </span>-b<span class="w"> </span>dummy<span class="w"> </span>.<span class="w"> </span>_build/dummy<span class="w"> </span><span class="m">2</span>&gt;<span class="p">&amp;</span><span class="m">1</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span><span class="s2">&quot;WARNING: document isn&#39;t included&quot;</span>

<span class="c1"># Generate coverage report</span>
sphinx-build<span class="w"> </span>-b<span class="w"> </span>coverage<span class="w"> </span>.<span class="w"> </span>_build/coverage
</pre></div>
</div>
</section>
<section id="analytics-and-monitoring">
<h2>📊 Analytics and Monitoring<a class="headerlink" href="#analytics-and-monitoring" title="Link to this heading"></a></h2>
<p><strong>Google Analytics Integration:</strong></p>
<div class="highlight-html notranslate"><div class="highlight"><pre><span></span><span class="cm">&lt;!-- _templates/layout.html --&gt;</span>
{% if theme_analytics_id %}
<span class="p">&lt;</span><span class="nt">script</span> <span class="na">async</span> <span class="na">src</span><span class="o">=</span><span class="s">&quot;https://www.googletagmanager.com/gtag/js?id={{ theme_analytics_id }}&quot;</span><span class="p">&gt;&lt;/</span><span class="nt">script</span><span class="p">&gt;</span>
<span class="p">&lt;</span><span class="nt">script</span><span class="p">&gt;</span>
<span class="w">  </span><span class="nb">window</span><span class="p">.</span><span class="nx">dataLayer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">window</span><span class="p">.</span><span class="nx">dataLayer</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="p">[];</span>
<span class="w">  </span><span class="kd">function</span><span class="w"> </span><span class="nx">gtag</span><span class="p">(){</span><span class="nx">dataLayer</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">arguments</span><span class="p">);}</span>
<span class="w">  </span><span class="nx">gtag</span><span class="p">(</span><span class="s1">&#39;js&#39;</span><span class="p">,</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">());</span>
<span class="w">  </span><span class="nx">gtag</span><span class="p">(</span><span class="s1">&#39;config&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;{{ theme_analytics_id }}&#39;</span><span class="p">);</span>
<span class="p">&lt;/</span><span class="nt">script</span><span class="p">&gt;</span>
{% endif %}
</pre></div>
</div>
<p><strong>Documentation Metrics:</strong></p>
<ul class="simple">
<li><p>Page views and popular content</p></li>
<li><p>Search queries and results</p></li>
<li><p>User engagement and time on page</p></li>
<li><p>Download statistics for PDFs</p></li>
<li><p>Cross-reference usage patterns</p></li>
</ul>
</section>
<section id="maintenance">
<h2>🔄 Maintenance<a class="headerlink" href="#maintenance" title="Link to this heading"></a></h2>
<p><strong>Regular Tasks:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Update dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>--upgrade<span class="w"> </span>sphinx<span class="w"> </span>sphinx-rtd-theme

<span class="c1"># Rebuild search index</span>
make<span class="w"> </span>clean<span class="w"> </span>html

<span class="c1"># Update translations</span>
sphinx-build<span class="w"> </span>-b<span class="w"> </span>gettext<span class="w"> </span>.<span class="w"> </span>_build/gettext
sphinx-intl<span class="w"> </span>update<span class="w"> </span>-p<span class="w"> </span>_build/gettext<span class="w"> </span>-l<span class="w"> </span>en_GB
</pre></div>
</div>
<p><strong>Content Review:</strong></p>
<ol class="arabic simple">
<li><p><strong>Quarterly Reviews</strong> - Update content for accuracy</p></li>
<li><p><strong>Version Updates</strong> - Sync with platform releases</p></li>
<li><p><strong>User Feedback</strong> - Incorporate user suggestions</p></li>
<li><p><strong>SEO Optimization</strong> - Improve search visibility</p></li>
<li><p><strong>Accessibility Audit</strong> - Ensure WCAG compliance</p></li>
</ol>
<p>—</p>
<p>This comprehensive Sphinx setup guide ensures that the CertRats documentation system is maintainable, scalable, and provides an excellent user experience for all documentation consumers.</p>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>