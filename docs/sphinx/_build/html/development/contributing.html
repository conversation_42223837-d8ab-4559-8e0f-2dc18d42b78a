<!DOCTYPE html>
<html class="writer-html5" lang="en-GB" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Contributing Guide &mdash; CertRats Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ca6ce64f" />
      <link rel="stylesheet" type="text/css" href="../_static/mermaid.css?v=7935d3ac" />

  
    <link rel="canonical" href="https://docs.certrats.com/development/contributing.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=c913fcab"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script src="../_static/translations.js?v=e16899fb"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
        <script src="../_static/mermaid-init.js?v=dd87ccd9"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="📝 Changelog" href="../changelog.html" />
    <link rel="prev" title="AI Models Implementation" href="ai_models.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertRats
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🚀 Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">🚀 Quick Start Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../platform_overview.html">🚀 Platform Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="../unified_platform.html">🔗 Unified Platform Integration</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">⚙️ Configuration Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🤖 AI &amp; Analytics:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../ai/study_assistant.html">🤖 AI Study Assistant</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏢 Enterprise Features:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../enterprise/dashboard.html">🏢 Enterprise Dashboard</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🔗 API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/unified_intelligence.html">🤖 Unified Intelligence API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication.html">Authentication &amp; Security</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/authentication-system.html">Core Authentication System</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/study-session-tracking.html">Study Session Tracking</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/organization-management.html">Organization Management</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent3-enterprise-analytics.html">Agent 3: Enterprise Analytics Engine</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/agent4-api-reference.html">Agent 4 API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/ai_assistant.html">AI Study Assistant API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/enterprise.html">Enterprise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/cost_calculator.html">Cost Calculator API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/career_framework.html">Career Framework API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/progress_tracking.html">Progress Tracking API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/integration_hub.html">Integration Hub API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">❓ Frequently Asked Questions:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../faq/index.html">❓ Frequently Asked Questions</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guides:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../guides/user_guide.html">🎯 Complete User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_nextjs_guide.html">🚀 Next.js 14 Frontend User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows_nextjs.html">🔄 Next.js Application Flows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/frontend_implementation.html">🎨 Frontend Implementation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/application_flows.html">🔄 Application Flows &amp; Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/testing_guide.html">🧪 Comprehensive Testing Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/unified_dashboard_guide.html">📊 Unified Dashboard User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/ai_features_guide.html">🤖 AI Features Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/agent4_user_guide.html">Agent 4 User Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/complete-platform-integration.html">Complete Platform Integration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/mobile_guide.html">📱 Mobile Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/admin_guide.html">👨‍💼 Administrator Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../guides/enterprise_guide.html">🏢 Enterprise Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Product Requirements:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prds/index.html">📋 Product Requirements Documents (PRDs)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🛠️ Development:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="architecture.html">System Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="frontend_migration.html">🚀 Frontend Migration to Next.js 14</a></li>
<li class="toctree-l1"><a class="reference internal" href="ai_models.html">AI Models Implementation</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Contributing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#prerequisites">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="#setting-up-development-environment">Setting Up Development Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#development-workflow">Development Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#branch-strategy">Branch Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="#code-standards">Code Standards</a></li>
<li class="toctree-l3"><a class="reference internal" href="#testing-requirements">Testing Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="#documentation-standards">Documentation Standards</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#contribution-types">Contribution Types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#bug-reports">Bug Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="#feature-requests">Feature Requests</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pull-request-process">Pull Request Process</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#review-process">Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#code-review-guidelines">Code Review Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#community-guidelines">Community Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#code-of-conduct">Code of Conduct</a></li>
<li class="toctree-l3"><a class="reference internal" href="#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📋 Documentation:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">📝 Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-2-0-0-unified-platform-integration-2024-01-16">🎉 <strong>Version 2.0.0 - Unified Platform Integration</strong> (2024-01-16)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-5-0-agent-4-career-intelligence-2024-01-15">📈 <strong>Version 1.5.0 - Agent 4 Career Intelligence</strong> (2024-01-15)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-4-0-agent-3-enterprise-analytics-2024-01-10">📊 <strong>Version 1.4.0 - Agent 3 Enterprise Analytics</strong> (2024-01-10)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-3-0-agent-2-ai-study-assistant-2024-01-05">🤖 <strong>Version 1.3.0 - Agent 2 AI Study Assistant</strong> (2024-01-05)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-2-0-agent-1-core-platform-2024-01-01">🏗️ <strong>Version 1.2.0 - Agent 1 Core Platform</strong> (2024-01-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-1-0-initial-platform-2023-12-01">🎯 <strong>Version 1.1.0 - Initial Platform</strong> (2023-12-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#version-1-0-0-platform-launch-2023-11-01">🌟 <strong>Version 1.0.0 - Platform Launch</strong> (2023-11-01)</a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#upcoming-features">📋 <strong>Upcoming Features</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html#support-feedback">📞 <strong>Support &amp; Feedback</strong></a></li>
<li class="toctree-l1"><a class="reference internal" href="../README.html">CertRats Sphinx Documentation System</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertRats</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Contributing Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/replit-CertPathFinder/blob/master/docs/sphinx/development/contributing.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="contributing-guide">
<h1>Contributing Guide<a class="headerlink" href="#contributing-guide" title="Link to this heading"></a></h1>
<p>Welcome to the CertPathFinder project! We appreciate your interest in contributing to this open-source cybersecurity education platform.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#getting-started" id="id1">Getting Started</a></p>
<ul>
<li><p><a class="reference internal" href="#prerequisites" id="id2">Prerequisites</a></p></li>
<li><p><a class="reference internal" href="#setting-up-development-environment" id="id3">Setting Up Development Environment</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#development-workflow" id="id4">Development Workflow</a></p>
<ul>
<li><p><a class="reference internal" href="#branch-strategy" id="id5">Branch Strategy</a></p></li>
<li><p><a class="reference internal" href="#code-standards" id="id6">Code Standards</a></p></li>
<li><p><a class="reference internal" href="#testing-requirements" id="id7">Testing Requirements</a></p></li>
<li><p><a class="reference internal" href="#documentation-standards" id="id8">Documentation Standards</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#contribution-types" id="id9">Contribution Types</a></p>
<ul>
<li><p><a class="reference internal" href="#bug-reports" id="id10">Bug Reports</a></p></li>
<li><p><a class="reference internal" href="#feature-requests" id="id11">Feature Requests</a></p></li>
<li><p><a class="reference internal" href="#pull-request-process" id="id12">Pull Request Process</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#review-process" id="id13">Review Process</a></p>
<ul>
<li><p><a class="reference internal" href="#code-review-guidelines" id="id14">Code Review Guidelines</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#community-guidelines" id="id15">Community Guidelines</a></p>
<ul>
<li><p><a class="reference internal" href="#code-of-conduct" id="id16">Code of Conduct</a></p></li>
<li><p><a class="reference internal" href="#getting-help" id="id17">Getting Help</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#see-also" id="id18">See Also</a></p></li>
</ul>
</nav>
<section id="getting-started">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Getting Started</a><a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<section id="prerequisites">
<h3><a class="toc-backref" href="#id2" role="doc-backlink">Prerequisites</a><a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<p>Before contributing, ensure you have the following installed:</p>
<p><strong>Required Software:</strong></p>
<ul class="simple">
<li><p><strong>Python 3.10+</strong> - Core development language</p></li>
<li><p><strong>Node.js 18+</strong> - Frontend development</p></li>
<li><p><strong>Docker &amp; Docker Compose</strong> - Containerised development environment</p></li>
<li><p><strong>Git</strong> - Version control system</p></li>
<li><p><strong>PostgreSQL 13+</strong> - Database (or use Docker)</p></li>
<li><p><strong>Redis 6+</strong> - Caching (or use Docker)</p></li>
</ul>
</section>
<section id="setting-up-development-environment">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">Setting Up Development Environment</a><a class="headerlink" href="#setting-up-development-environment" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Fork and Clone the Repository</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Fork the repository on GitHub, then clone your fork</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/YOUR_USERNAME/replit-CertPathFinder.git
<span class="nb">cd</span><span class="w"> </span>replit-CertPathFinder

<span class="c1"># Add upstream remote</span>
git<span class="w"> </span>remote<span class="w"> </span>add<span class="w"> </span>upstream<span class="w"> </span>https://github.com/forkrul/replit-CertPathFinder.git
</pre></div>
</div>
<ol class="arabic simple" start="2">
<li><p><strong>Set Up Python Environment</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create virtual environment</span>
python<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate<span class="w">  </span><span class="c1"># On Windows: venv\Scripts\activate</span>

<span class="c1"># Install dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements.txt
</pre></div>
</div>
<ol class="arabic simple" start="3">
<li><p><strong>Configure Environment Variables</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Copy example environment file</span>
cp<span class="w"> </span>.env.example<span class="w"> </span>.env

<span class="c1"># Edit .env with your local configuration</span>
</pre></div>
</div>
<ol class="arabic simple" start="4">
<li><p><strong>Set Up Database</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Using Docker (recommended)</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d<span class="w"> </span>database<span class="w"> </span>redis

<span class="c1"># Apply database migrations</span>
alembic<span class="w"> </span>upgrade<span class="w"> </span>head
</pre></div>
</div>
<ol class="arabic simple" start="5">
<li><p><strong>Verify Installation</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run tests to verify setup</span>
pytest<span class="w"> </span>tests/

<span class="c1"># Start development server</span>
python<span class="w"> </span>run_api.py
</pre></div>
</div>
</section>
</section>
<section id="development-workflow">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Development Workflow</a><a class="headerlink" href="#development-workflow" title="Link to this heading"></a></h2>
<section id="branch-strategy">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Branch Strategy</a><a class="headerlink" href="#branch-strategy" title="Link to this heading"></a></h3>
<p>We use a Git flow-inspired branching strategy:</p>
<p><strong>Branch Types:</strong></p>
<ul class="simple">
<li><p><strong>main</strong> - Production-ready code</p></li>
<li><p><strong>develop</strong> - Integration branch for features</p></li>
<li><p><strong>feature/*</strong> - New features and enhancements</p></li>
<li><p><strong>bugfix/*</strong> - Bug fixes</p></li>
<li><p><strong>docs/*</strong> - Documentation updates</p></li>
</ul>
<p><strong>Creating a Feature Branch:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Update your local repository</span>
git<span class="w"> </span>checkout<span class="w"> </span>develop
git<span class="w"> </span>pull<span class="w"> </span>upstream<span class="w"> </span>develop

<span class="c1"># Create feature branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/your-feature-name

<span class="c1"># Make your changes and commit</span>
git<span class="w"> </span>add<span class="w"> </span>.
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: add new feature description&quot;</span>

<span class="c1"># Push to your fork</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/your-feature-name
</pre></div>
</div>
</section>
<section id="code-standards">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Code Standards</a><a class="headerlink" href="#code-standards" title="Link to this heading"></a></h3>
<p><strong>Python Code Standards:</strong></p>
<ul class="simple">
<li><p><strong>PEP 8</strong> - Follow Python style guide</p></li>
<li><p><strong>Type Hints</strong> - Use type annotations for all functions</p></li>
<li><p><strong>Docstrings</strong> - Document all public functions and classes</p></li>
<li><p><strong>Black</strong> - Code formatting (run <cite>black .</cite>)</p></li>
<li><p><strong>isort</strong> - Import sorting (run <cite>isort .</cite>)</p></li>
<li><p><strong>flake8</strong> - Linting (run <cite>flake8</cite>)</p></li>
</ul>
<p><strong>Example Python Code:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">datetime</span>

<span class="k">def</span> <span class="nf">calculate_certification_cost</span><span class="p">(</span>
    <span class="n">certification_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">location</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
    <span class="n">include_materials</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">dict</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Calculate the total cost for a certification.</span>

<span class="sd">    Args:</span>
<span class="sd">        certification_id: Unique identifier for the certification</span>
<span class="sd">        location: User&#39;s location for localised pricing</span>
<span class="sd">        include_materials: Whether to include study materials cost</span>

<span class="sd">    Returns:</span>
<span class="sd">        Dictionary containing cost breakdown and total</span>

<span class="sd">    Raises:</span>
<span class="sd">        ValueError: If certification_id is invalid</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="n">certification_id</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Certification ID must be positive&quot;</span><span class="p">)</span>

    <span class="c1"># Implementation here</span>
    <span class="k">return</span> <span class="p">{</span>
        <span class="s2">&quot;total_cost&quot;</span><span class="p">:</span> <span class="mf">1500.00</span><span class="p">,</span>
        <span class="s2">&quot;currency&quot;</span><span class="p">:</span> <span class="s2">&quot;GBP&quot;</span><span class="p">,</span>
        <span class="s2">&quot;breakdown&quot;</span><span class="p">:</span> <span class="p">{</span>
            <span class="s2">&quot;exam_fee&quot;</span><span class="p">:</span> <span class="mf">749.00</span><span class="p">,</span>
            <span class="s2">&quot;materials&quot;</span><span class="p">:</span> <span class="mf">751.00</span> <span class="k">if</span> <span class="n">include_materials</span> <span class="k">else</span> <span class="mf">0.00</span>
        <span class="p">}</span>
    <span class="p">}</span>
</pre></div>
</div>
</section>
<section id="testing-requirements">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Testing Requirements</a><a class="headerlink" href="#testing-requirements" title="Link to this heading"></a></h3>
<p>All contributions must include appropriate tests:</p>
<p><strong>Test Types Required:</strong></p>
<ul class="simple">
<li><p><strong>Unit Tests</strong> - Test individual functions and methods</p></li>
<li><p><strong>Integration Tests</strong> - Test API endpoints and service interactions</p></li>
<li><p><strong>End-to-End Tests</strong> - Test complete user workflows (for major features)</p></li>
</ul>
<p><strong>Running Tests:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run all tests</span>
pytest<span class="w"> </span>tests/

<span class="c1"># Run tests with coverage</span>
pytest<span class="w"> </span>tests/<span class="w"> </span>--cov<span class="o">=</span>.<span class="w"> </span>--cov-report<span class="o">=</span>html

<span class="c1"># Run specific test file</span>
pytest<span class="w"> </span>tests/test_cost_calculator.py
</pre></div>
</div>
<p><strong>Writing Tests:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">from</span> <span class="nn">unittest.mock</span> <span class="kn">import</span> <span class="n">Mock</span><span class="p">,</span> <span class="n">patch</span>
<span class="kn">from</span> <span class="nn">services.cost_calculator</span> <span class="kn">import</span> <span class="n">CostCalculatorService</span>

<span class="k">class</span> <span class="nc">TestCostCalculatorService</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">setup_method</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Set up test fixtures&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">service</span> <span class="o">=</span> <span class="n">CostCalculatorService</span><span class="p">()</span>

    <span class="k">def</span> <span class="nf">test_calculate_basic_cost</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test basic cost calculation&quot;&quot;&quot;</span>
        <span class="n">result</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">service</span><span class="o">.</span><span class="n">calculate_cost</span><span class="p">(</span>
            <span class="n">certification_id</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
            <span class="n">location</span><span class="o">=</span><span class="s2">&quot;United Kingdom&quot;</span>
        <span class="p">)</span>

        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;total_cost&quot;</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span>
        <span class="k">assert</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;currency&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;GBP&quot;</span>
        <span class="k">assert</span> <span class="s2">&quot;breakdown&quot;</span> <span class="ow">in</span> <span class="n">result</span>
</pre></div>
</div>
</section>
<section id="documentation-standards">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Documentation Standards</a><a class="headerlink" href="#documentation-standards" title="Link to this heading"></a></h3>
<p><strong>Documentation Requirements:</strong></p>
<ul class="simple">
<li><p><strong>API Documentation</strong> - All endpoints must be documented</p></li>
<li><p><strong>Code Comments</strong> - Complex logic should be commented</p></li>
<li><p><strong>README Updates</strong> - Update relevant README files</p></li>
<li><p><strong>Changelog</strong> - Add entries for user-facing changes</p></li>
</ul>
<p><strong>Documentation Style:</strong></p>
<ul class="simple">
<li><p>Use British English spelling and grammar</p></li>
<li><p>Write clear, concise explanations</p></li>
<li><p>Include code examples where helpful</p></li>
<li><p>Use proper reStructuredText formatting for Sphinx docs</p></li>
</ul>
</section>
</section>
<section id="contribution-types">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Contribution Types</a><a class="headerlink" href="#contribution-types" title="Link to this heading"></a></h2>
<section id="bug-reports">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Bug Reports</a><a class="headerlink" href="#bug-reports" title="Link to this heading"></a></h3>
<p>When reporting bugs, please include:</p>
<p><strong>Bug Report Template:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>**Bug Description**
A clear description of what the bug is.

**Steps to Reproduce**
1. Go to &#39;...&#39;
2. Click on &#39;....&#39;
3. See error

**Expected Behaviour**
What you expected to happen.

**Actual Behaviour**
What actually happened.

**Environment**
- OS: [e.g. Ubuntu 22.04]
- Python Version: [e.g. 3.10.8]
- CertPathFinder Version: [e.g. 1.0.0]
</pre></div>
</div>
</section>
<section id="feature-requests">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Feature Requests</a><a class="headerlink" href="#feature-requests" title="Link to this heading"></a></h3>
<p>For new features, please provide:</p>
<p><strong>Feature Request Template:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>**Feature Summary**
A brief summary of the feature you&#39;d like to see.

**Problem Statement**
What problem does this feature solve?

**Proposed Solution**
Describe your proposed solution in detail.

**User Stories**
- As a [user type], I want [goal] so that [benefit]

**Acceptance Criteria**
- [ ] Criterion 1
- [ ] Criterion 2
</pre></div>
</div>
</section>
<section id="pull-request-process">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Pull Request Process</a><a class="headerlink" href="#pull-request-process" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Create Feature Branch</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/descriptive-name
</pre></div>
</div>
<ol class="arabic simple" start="2">
<li><p><strong>Make Changes</strong>
- Write clean, well-documented code
- Add appropriate tests
- Update documentation</p></li>
<li><p><strong>Test Your Changes</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run full test suite</span>
pytest<span class="w"> </span>tests/

<span class="c1"># Run linting</span>
black<span class="w"> </span>.
isort<span class="w"> </span>.
flake8
</pre></div>
</div>
<ol class="arabic simple" start="4">
<li><p><strong>Commit Changes</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Use conventional commit format</span>
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: add certification cost calculator API&quot;</span>
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;fix: resolve authentication token expiry issue&quot;</span>
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;docs: update API documentation for new endpoints&quot;</span>
</pre></div>
</div>
<ol class="arabic simple" start="5">
<li><p><strong>Push and Create Pull Request</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/descriptive-name
<span class="c1"># Create pull request on GitHub</span>
</pre></div>
</div>
</section>
</section>
<section id="review-process">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">Review Process</a><a class="headerlink" href="#review-process" title="Link to this heading"></a></h2>
<section id="code-review-guidelines">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Code Review Guidelines</a><a class="headerlink" href="#code-review-guidelines" title="Link to this heading"></a></h3>
<p><strong>For Contributors:</strong></p>
<ul class="simple">
<li><p>Respond to feedback promptly and professionally</p></li>
<li><p>Make requested changes in separate commits</p></li>
<li><p>Ask questions if feedback is unclear</p></li>
<li><p>Test your changes after addressing feedback</p></li>
</ul>
<p><strong>For Reviewers:</strong></p>
<ul class="simple">
<li><p>Be constructive and specific in feedback</p></li>
<li><p>Focus on code quality, security, and maintainability</p></li>
<li><p>Suggest improvements rather than just pointing out problems</p></li>
<li><p>Approve when code meets project standards</p></li>
</ul>
<p><strong>Review Criteria:</strong></p>
<ul class="simple">
<li><p>Code quality and readability</p></li>
<li><p>Test coverage and quality</p></li>
<li><p>Documentation completeness</p></li>
<li><p>Security considerations</p></li>
<li><p>Performance implications</p></li>
<li><p>Backwards compatibility</p></li>
</ul>
</section>
</section>
<section id="community-guidelines">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">Community Guidelines</a><a class="headerlink" href="#community-guidelines" title="Link to this heading"></a></h2>
<section id="code-of-conduct">
<h3><a class="toc-backref" href="#id16" role="doc-backlink">Code of Conduct</a><a class="headerlink" href="#code-of-conduct" title="Link to this heading"></a></h3>
<p>We are committed to providing a welcoming and inclusive environment:</p>
<ul class="simple">
<li><p><strong>Be respectful</strong> - Treat all community members with respect</p></li>
<li><p><strong>Be inclusive</strong> - Welcome newcomers and diverse perspectives</p></li>
<li><p><strong>Be collaborative</strong> - Work together towards common goals</p></li>
<li><p><strong>Be patient</strong> - Help others learn and grow</p></li>
<li><p><strong>Be constructive</strong> - Provide helpful feedback and suggestions</p></li>
</ul>
</section>
<section id="getting-help">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Getting Help</a><a class="headerlink" href="#getting-help" title="Link to this heading"></a></h3>
<p>If you need help with contributing:</p>
<ol class="arabic simple">
<li><p>Check this documentation first</p></li>
<li><p>Search existing GitHub issues</p></li>
<li><p>Create a new issue with the “question” label</p></li>
<li><p>Join community discussions on GitHub</p></li>
</ol>
<p>Thank you for contributing to CertPathFinder! Your efforts help make cybersecurity education more accessible and effective for everyone.</p>
</section>
</section>
<section id="see-also">
<h2><a class="toc-backref" href="#id18" role="doc-backlink">See Also</a><a class="headerlink" href="#see-also" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="architecture.html"><span class="doc">System Architecture</span></a> - System architecture overview</p></li>
<li><p><a class="reference internal" href="../installation.html"><span class="doc">Installation Guide</span></a> - Installation and setup guide</p></li>
<li><p><a class="reference internal" href="../api/index.html"><span class="doc">API Reference</span></a> - API documentation and reference</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="ai_models.html" class="btn btn-neutral float-left" title="AI Models Implementation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../changelog.html" class="btn btn-neutral float-right" title="📝 Changelog" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, CertRats Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>