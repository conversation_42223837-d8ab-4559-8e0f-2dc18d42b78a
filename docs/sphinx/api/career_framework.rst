Career Framework API
====================

The Career Framework API provides comprehensive career guidance, transition planning, and skills analysis for cybersecurity professionals. It includes job market intelligence, career path recommendations, and skills gap analysis.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Career Framework offers:

- **Career Path Planning** - Strategic roadmaps for career advancement
- **Skills Gap Analysis** - Identify and prioritise skill development needs
- **Job Market Intelligence** - Real-time market data and trends
- **Transition Planning** - Step-by-step career change guidance
- **Salary Analysis** - Compensation benchmarking and projections
- **Role Matching** - AI-powered job role recommendations

All recommendations are personalised based on individual profiles and market conditions.

Career Analysis
---------------

Analyse Career Transition
~~~~~~~~~~~~~~~~~~~~~~~~~

Evaluate the feasibility and requirements for career transitions.

.. code-block:: http

   POST /api/v1/career-transition/analyse
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "current_profile": {
       "current_role_id": 15,
       "current_role_title": "Security Analyst",
       "experience_years": 3,
       "current_skills": [
         {
           "skill_name": "Network Security",
           "proficiency_level": "intermediate",
           "years_experience": 2
         },
         {
           "skill_name": "Incident Response",
           "proficiency_level": "beginner",
           "years_experience": 1
         }
       ],
       "current_certifications": ["Security+", "Network+"],
       "current_salary": 45000,
       "location": "United Kingdom"
     },
     "target_profile": {
       "target_role_id": 28,
       "target_role_title": "Security Architect",
       "desired_timeline_months": 18,
       "target_salary_range": {
         "min": 75000,
         "max": 95000
       }
     },
     "preferences": {
       "study_time_per_week": 10,
       "budget_available": 15000,
       "learning_style": "hands_on",
       "willing_to_relocate": false
     }
   }

**Response**

.. code-block:: json

   {
     "analysis_id": "analysis_12345",
     "generated_at": "2025-01-15T10:30:00Z",
     "transition_feasibility": {
       "overall_score": 7.8,
       "success_probability": 0.78,
       "confidence_level": 0.85,
       "feasibility_rating": "high",
       "key_factors": {
         "experience_alignment": 0.7,
         "skills_transferability": 0.8,
         "market_demand": 0.9,
         "timeline_realism": 0.75
       }
     },
     "skills_gap_analysis": {
       "critical_gaps": [
         {
           "skill_name": "Enterprise Architecture",
           "current_level": "none",
           "required_level": "intermediate",
           "priority": "high",
           "estimated_learning_time_hours": 120,
           "recommended_resources": [
             "TOGAF Certification",
             "Enterprise Architecture Fundamentals Course"
           ]
         },
         {
           "skill_name": "Cloud Security Architecture",
           "current_level": "beginner",
           "required_level": "advanced",
           "priority": "high",
           "estimated_learning_time_hours": 200
         }
       ],
       "skill_strengths": [
         {
           "skill_name": "Network Security",
           "advantage_level": "moderate",
           "transferability": "high"
         }
       ],
       "total_learning_hours_required": 450
     },
     "certification_roadmap": {
       "required_certifications": [
         {
           "certification_id": 45,
           "name": "CISSP",
           "priority": "critical",
           "estimated_study_time_weeks": 16,
           "cost_estimate": 8500,
           "target_completion": "Month 8"
         },
         {
           "certification_id": 67,
           "name": "SABSA",
           "priority": "high",
           "estimated_study_time_weeks": 12,
           "cost_estimate": 6500,
           "target_completion": "Month 14"
         }
       ],
       "optional_certifications": [
         {
           "certification_id": 89,
           "name": "AWS Solutions Architect",
           "benefit": "Enhances cloud architecture credibility",
           "cost_estimate": 3500
         }
       ]
     },
     "timeline_plan": {
       "total_duration_months": 18,
       "milestones": [
         {
           "month": 3,
           "milestone": "Complete CISSP study preparation",
           "deliverables": ["Study plan completion", "Practice test scores >80%"]
         },
         {
           "month": 8,
           "milestone": "CISSP Certification achieved",
           "deliverables": ["CISSP certificate", "Updated CV/LinkedIn"]
         },
         {
           "month": 12,
           "milestone": "Architecture experience gained",
           "deliverables": ["Architecture project portfolio", "Technical presentations"]
         }
       ]
     },
     "financial_analysis": {
       "total_investment_required": 15000,
       "expected_salary_increase": 35000,
       "roi_percentage": 233,
       "payback_period_months": 5.1,
       "net_benefit_5_years": 175000
     },
     "market_intelligence": {
       "job_availability": "high",
       "competition_level": "moderate",
       "growth_projection": 0.15,
       "regional_demand": {
         "london": "very_high",
         "manchester": "high",
         "edinburgh": "moderate"
       }
     },
     "recommendations": {
       "immediate_actions": [
         "Begin CISSP study programme immediately",
         "Seek architecture-related projects in current role",
         "Join professional architecture communities"
       ],
       "risk_mitigation": [
         "Build portfolio of architecture work",
         "Network with senior architects",
         "Consider interim roles to gain experience"
       ],
       "success_factors": [
         "Consistent study schedule",
         "Practical experience application",
         "Professional networking"
       ]
     }
   }

Job Market Intelligence
-----------------------

Get High-Demand Roles
~~~~~~~~~~~~~~~~~~~~~

Retrieve information about high-demand cybersecurity roles.

.. code-block:: http

   GET /api/v1/enhanced-taxonomy/job-titles/high-demand
   Authorization: Bearer <token>

   ?location=UK&experience_level=mid&limit=10

**Response**

.. code-block:: json

   {
     "location": "United Kingdom",
     "experience_level": "mid",
     "generated_at": "2025-01-15T10:30:00Z",
     "job_titles": [
       {
         "id": 28,
         "title": "Cloud Security Engineer",
         "demand_score": 9.2,
         "average_salary": 68000,
         "salary_range": {
           "min": 55000,
           "max": 85000
         },
         "growth_rate": 0.18,
         "job_openings_count": 1250,
         "skills_required": [
           "AWS Security",
           "Azure Security",
           "Cloud Architecture",
           "DevSecOps"
         ],
         "typical_certifications": [
           "AWS Certified Security - Specialty",
           "Azure Security Engineer Associate",
           "CCSP"
         ],
         "experience_requirements": {
           "min_years": 2,
           "max_years": 6,
           "preferred_years": 4
         },
         "market_trends": {
           "hiring_velocity": "fast",
           "competition_level": "moderate",
           "remote_work_availability": 0.75
         }
       }
     ],
     "market_summary": {
       "total_high_demand_roles": 15,
       "average_salary_increase": 0.12,
       "fastest_growing_domain": "Cloud Security",
       "skills_shortage_areas": ["Zero Trust", "DevSecOps", "AI Security"]
     }
   }

Salary Analysis
~~~~~~~~~~~~~~~

Get detailed salary information for specific roles and locations.

.. code-block:: http

   GET /api/v1/career-transition/salary-analysis
   Authorization: Bearer <token>

   ?role_id=28&location=UK&experience_years=5

**Response**

.. code-block:: json

   {
     "role_id": 28,
     "role_title": "Cloud Security Engineer",
     "location": "United Kingdom",
     "experience_years": 5,
     "generated_at": "2025-01-15T10:30:00Z",
     "salary_data": {
       "median_salary": 72000,
       "average_salary": 75500,
       "salary_range": {
         "percentile_25": 62000,
         "percentile_50": 72000,
         "percentile_75": 85000,
         "percentile_90": 98000
       },
       "total_compensation": {
         "base_salary": 72000,
         "bonus_average": 8500,
         "equity_value": 12000,
         "total_package": 92500
       }
     },
     "regional_breakdown": {
       "london": {
         "median_salary": 85000,
         "cost_of_living_adjustment": 1.18
       },
       "manchester": {
         "median_salary": 68000,
         "cost_of_living_adjustment": 0.94
       },
       "edinburgh": {
         "median_salary": 70000,
         "cost_of_living_adjustment": 0.97
       }
     },
     "salary_factors": {
       "certifications_impact": {
         "AWS Security Specialty": 8500,
         "CISSP": 12000,
         "CCSP": 6500
       },
       "skills_premium": {
         "Zero Trust Architecture": 15000,
         "Kubernetes Security": 8000,
         "DevSecOps": 10000
       },
       "company_size_impact": {
         "startup": 0.85,
         "mid_size": 1.0,
         "enterprise": 1.15,
         "big_tech": 1.35
       }
     },
     "career_progression": {
       "next_level_role": "Senior Cloud Security Architect",
       "progression_timeline_years": 3,
       "next_level_salary": 95000,
       "progression_requirements": [
         "Team leadership experience",
         "Architecture design portfolio",
         "Advanced certifications"
       ]
     },
     "market_trends": {
       "salary_growth_rate": 0.08,
       "demand_trend": "increasing",
       "remote_work_impact": 0.95,
       "skills_evolution": [
         "AI/ML Security becoming critical",
         "Zero Trust implementation experience valued",
         "Multi-cloud expertise in demand"
       ]
     }
   }

Skills Development
------------------

Skills Assessment
~~~~~~~~~~~~~~~~~

Evaluate current skills against market requirements.

.. code-block:: http

   POST /api/v1/career-transition/skills-assessment
   Content-Type: application/json

   {
     "user_profile": {
       "current_role": "Security Analyst",
       "experience_years": 3,
       "target_roles": ["Security Architect", "Security Consultant"]
     },
     "current_skills": [
       {
         "skill_name": "Network Security",
         "self_assessment": 7,
         "years_experience": 2,
         "last_used": "current"
       },
       {
         "skill_name": "Cloud Security",
         "self_assessment": 4,
         "years_experience": 1,
         "last_used": "6_months_ago"
       }
     ],
     "assessment_type": "comprehensive"
   }

**Response**

.. code-block:: json

   {
     "assessment_id": "skills_assess_789",
     "user_id": 123,
     "generated_at": "2025-01-15T10:30:00Z",
     "overall_assessment": {
       "current_level": "intermediate",
       "market_competitiveness": 6.5,
       "readiness_for_target_roles": {
         "Security Architect": 0.65,
         "Security Consultant": 0.72
       }
     },
     "skills_analysis": [
       {
         "skill_name": "Network Security",
         "current_level": "intermediate",
         "market_requirement": "intermediate",
         "gap_analysis": "meets_requirement",
         "market_value": "high",
         "improvement_potential": "moderate"
       },
       {
         "skill_name": "Cloud Security",
         "current_level": "beginner",
         "market_requirement": "advanced",
         "gap_analysis": "significant_gap",
         "market_value": "very_high",
         "improvement_potential": "high",
         "priority": "critical"
       }
     ],
     "development_recommendations": [
       {
         "skill_name": "Cloud Security",
         "priority": "high",
         "development_path": [
           {
             "step": 1,
             "activity": "AWS Security Fundamentals Course",
             "duration_weeks": 4,
             "cost": 500
           },
           {
             "step": 2,
             "activity": "Hands-on Cloud Labs",
             "duration_weeks": 8,
             "cost": 200
           },
           {
             "step": 3,
             "activity": "AWS Security Specialty Certification",
             "duration_weeks": 12,
             "cost": 3500
           }
         ],
         "total_investment": {
           "time_weeks": 24,
           "cost": 4200
         },
         "expected_outcome": "Advanced cloud security proficiency"
       }
     ],
     "certification_recommendations": [
       {
         "certification": "CISSP",
         "relevance_score": 9.2,
         "career_impact": "high",
         "priority": "immediate"
       }
     ]
   }

Career Path Planning
--------------------

Generate Career Roadmap
~~~~~~~~~~~~~~~~~~~~~~~

Create a detailed career progression plan.

.. code-block:: http

   POST /api/v1/career-transition/roadmap
   Content-Type: application/json

   {
     "current_position": {
       "role": "Security Analyst",
       "experience_years": 3,
       "current_salary": 45000
     },
     "career_goals": {
       "target_role": "CISO",
       "target_timeline_years": 10,
       "target_salary": 150000,
       "preferred_industry": "Financial Services"
     },
     "constraints": {
       "study_time_per_week": 8,
       "annual_training_budget": 10000,
       "willing_to_relocate": false,
       "management_interest": true
     }
   }

**Response**

.. code-block:: json

   {
     "roadmap_id": "roadmap_456",
     "generated_at": "2025-01-15T10:30:00Z",
     "career_progression": {
       "total_timeline_years": 10,
       "success_probability": 0.73,
       "key_milestones": [
         {
           "year": 2,
           "role": "Senior Security Analyst",
           "salary_target": 58000,
           "key_requirements": [
             "CISSP Certification",
             "Team leadership experience",
             "Incident response expertise"
           ],
           "success_probability": 0.85
         },
         {
           "year": 5,
           "role": "Security Manager",
           "salary_target": 78000,
           "key_requirements": [
             "CISM Certification",
             "Budget management experience",
             "Risk assessment skills"
           ],
           "success_probability": 0.75
         },
         {
           "year": 8,
           "role": "Security Director",
           "salary_target": 110000,
           "key_requirements": [
             "MBA or equivalent",
             "Board presentation experience",
             "Strategic planning skills"
           ],
           "success_probability": 0.65
         },
         {
           "year": 10,
           "role": "CISO",
           "salary_target": 150000,
           "key_requirements": [
             "Executive leadership experience",
             "Industry recognition",
             "Business acumen"
           ],
           "success_probability": 0.55
         }
       ]
     },
     "development_plan": {
       "certifications_timeline": [
         {
           "year": 1,
           "certification": "CISSP",
           "cost": 8500,
           "study_time_weeks": 16
         },
         {
           "year": 3,
           "certification": "CISM",
           "cost": 7500,
           "study_time_weeks": 14
         },
         {
           "year": 6,
           "certification": "MBA (Cybersecurity Focus)",
           "cost": 45000,
           "duration_years": 2
         }
       ],
       "skills_development": [
         {
           "skill_category": "Technical Leadership",
           "development_years": "1-3",
           "activities": [
             "Lead security projects",
             "Mentor junior analysts",
             "Present to senior management"
           ]
         },
         {
           "skill_category": "Business Acumen",
           "development_years": "4-7",
           "activities": [
             "MBA programme",
             "Cross-functional projects",
             "Industry networking"
           ]
         }
       ]
     },
     "financial_projection": {
       "total_investment": 75000,
       "cumulative_salary_increase": 525000,
       "roi_percentage": 700,
       "net_career_value": 450000
     },
     "risk_factors": [
       "Competitive CISO market",
       "Requires significant time investment",
       "Economic conditions may affect progression"
     ],
     "success_strategies": [
       "Build strong professional network",
       "Gain diverse industry experience",
       "Develop thought leadership presence",
       "Maintain technical credibility"
     ]
   }

Error Handling
--------------

Career Framework API error responses:

.. code-block:: json

   {
     "error": "insufficient_experience_data",
     "message": "Unable to provide accurate career transition analysis due to limited experience information",
     "code": 422,
     "details": {
       "missing_fields": ["specific_skills", "project_experience"],
       "minimum_experience_years": 1
     },
     "suggestions": [
       "Complete detailed skills assessment",
       "Provide more specific experience information"
     ]
   }

**Common Error Codes**

- ``400`` - Invalid career analysis parameters
- ``404`` - Role or career path not found
- ``422`` - Insufficient data for analysis
- ``429`` - Rate limit exceeded for career analysis

See Also
--------

- :doc:`../guides/user_guide` - Career planning user guide
- :doc:`ai_assistant` - AI-powered career recommendations
- :doc:`authentication` - API authentication
- :doc:`../enterprise/analytics` - Enterprise career analytics
