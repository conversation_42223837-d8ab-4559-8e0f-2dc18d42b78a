Organization Management
=======================

.. meta::
   :description: Complete enterprise organization and team management system
   :keywords: organization management, enterprise, team administration, user management

Overview
--------

The Organization Management system provides comprehensive enterprise user and team administration capabilities. Manage organizational hierarchies, team structures, user roles, and enterprise-wide settings with advanced analytics and reporting.

**🏢 Key Features:**

- **Multi-Tenant Architecture**: Complete data isolation between organizations
- **Hierarchical Team Management**: Flexible organizational structures and reporting
- **Advanced User Administration**: Comprehensive user lifecycle management
- **Role-Based Access Control**: Granular permissions and authorization
- **Enterprise Analytics**: Organization-wide insights and reporting

Organization Administration
---------------------------

Create Organization
~~~~~~~~~~~~~~~~~~~

Create a new organization with initial configuration.

**POST** ``/api/v1/organizations``

**Headers:**
``Authorization: Bearer {admin_token}``

.. code-block:: json

   {
     "name": "Acme Corporation",
     "domain": "acme.com",
     "industry": "technology",
     "size": "large",
     "settings": {
       "sso_enabled": true,
       "mfa_required": true,
       "data_retention_days": 2555,
       "allowed_domains": ["acme.com", "acme.org"]
     },
     "billing": {
       "plan": "enterprise",
       "max_users": 1000,
       "billing_email": "<EMAIL>"
     }
   }

**Response:**

.. code-block:: json

   {
     "organization_id": 12345,
     "name": "Acme Corporation",
     "domain": "acme.com",
     "industry": "technology",
     "size": "large",
     "status": "active",
     "created_at": "2025-06-16T15:00:00Z",
     "settings": {
       "sso_enabled": true,
       "mfa_required": true,
       "data_retention_days": 2555,
       "allowed_domains": ["acme.com", "acme.org"]
     },
     "subscription": {
       "plan": "enterprise",
       "max_users": 1000,
       "current_users": 0,
       "expires_at": "2026-06-16T15:00:00Z"
     }
   }

Get Organization Details
~~~~~~~~~~~~~~~~~~~~~~~~

Retrieve comprehensive organization information.

**GET** ``/api/v1/organizations/{organization_id}``

**Headers:**
``Authorization: Bearer {access_token}``

**Response:**

.. code-block:: json

   {
     "organization_id": 12345,
     "name": "Acme Corporation",
     "domain": "acme.com",
     "industry": "technology",
     "size": "large",
     "status": "active",
     "created_at": "2025-06-16T15:00:00Z",
     "updated_at": "2025-06-16T15:30:00Z",
     "statistics": {
       "total_users": 250,
       "active_users": 230,
       "total_teams": 15,
       "total_certifications": 45,
       "completion_rate": 0.78
     },
     "settings": {
       "sso_enabled": true,
       "mfa_required": true,
       "data_retention_days": 2555,
       "allowed_domains": ["acme.com", "acme.org"],
       "certification_approval_required": true,
       "budget_tracking_enabled": true
     }
   }

Update Organization
~~~~~~~~~~~~~~~~~~~

Update organization settings and configuration.

**PUT** ``/api/v1/organizations/{organization_id}``

**Headers:**
``Authorization: Bearer {admin_token}``

.. code-block:: json

   {
     "name": "Acme Corporation Ltd",
     "settings": {
       "mfa_required": true,
       "certification_approval_required": false,
       "budget_tracking_enabled": true,
       "allowed_domains": ["acme.com", "acme.org", "acme.net"]
     }
   }

**Response:**

.. code-block:: json

   {
     "organization_id": 12345,
     "name": "Acme Corporation Ltd",
     "updated_at": "2025-06-16T16:00:00Z",
     "settings": {
       "mfa_required": true,
       "certification_approval_required": false,
       "budget_tracking_enabled": true,
       "allowed_domains": ["acme.com", "acme.org", "acme.net"]
     }
   }

Team Management
---------------

Create Team
~~~~~~~~~~~

Create a new team within the organization.

**POST** ``/api/v1/organizations/{organization_id}/teams``

**Headers:**
``Authorization: Bearer {access_token}``

.. code-block:: json

   {
     "name": "Security Operations Team",
     "description": "Responsible for security monitoring and incident response",
     "department": "IT Security",
     "manager_id": 67890,
     "parent_team_id": null,
     "settings": {
       "budget_allocation": 50000,
       "certification_goals": ["CISSP", "CISM", "CEH"],
       "training_priority": "high"
     }
   }

**Response:**

.. code-block:: json

   {
     "team_id": 54321,
     "organization_id": 12345,
     "name": "Security Operations Team",
     "description": "Responsible for security monitoring and incident response",
     "department": "IT Security",
     "manager_id": 67890,
     "parent_team_id": null,
     "created_at": "2025-06-16T16:15:00Z",
     "member_count": 0,
     "settings": {
       "budget_allocation": 50000,
       "certification_goals": ["CISSP", "CISM", "CEH"],
       "training_priority": "high"
     }
   }

Get Team Details
~~~~~~~~~~~~~~~~

Retrieve comprehensive team information including members and analytics.

**GET** ``/api/v1/organizations/{organization_id}/teams/{team_id}``

**Headers:**
``Authorization: Bearer {access_token}``

**Response:**

.. code-block:: json

   {
     "team_id": 54321,
     "organization_id": 12345,
     "name": "Security Operations Team",
     "description": "Responsible for security monitoring and incident response",
     "department": "IT Security",
     "manager": {
       "user_id": 67890,
       "name": "John Smith",
       "email": "<EMAIL>",
       "role": "manager"
     },
     "members": [
       {
         "user_id": 11111,
         "name": "Alice Johnson",
         "email": "<EMAIL>",
         "role": "senior_analyst",
         "joined_at": "2025-06-16T16:20:00Z",
         "certifications": ["Security+", "CySA+"],
         "progress": {
           "active_certifications": 2,
           "completion_rate": 0.85
         }
       }
     ],
     "statistics": {
       "total_members": 8,
       "active_members": 7,
       "average_completion_rate": 0.82,
       "total_certifications": 15,
       "budget_utilization": 0.65
     },
     "performance": {
       "team_score": 4.2,
       "certification_velocity": 2.5,
       "knowledge_retention": 0.88,
       "collaboration_score": 4.5
     }
   }

Add Team Members
~~~~~~~~~~~~~~~~

Add users to a team with specific roles and permissions.

**POST** ``/api/v1/organizations/{organization_id}/teams/{team_id}/members``

**Headers:**
``Authorization: Bearer {access_token}``

.. code-block:: json

   {
     "user_ids": [11111, 22222, 33333],
     "role": "analyst",
     "permissions": ["read:team_data", "write:progress", "read:certifications"],
     "notification_preferences": {
       "team_updates": true,
       "certification_reminders": true,
       "goal_notifications": true
     }
   }

**Response:**

.. code-block:: json

   {
     "team_id": 54321,
     "added_members": [
       {
         "user_id": 11111,
         "name": "Alice Johnson",
         "role": "analyst",
         "added_at": "2025-06-16T16:30:00Z",
         "status": "active"
       }
     ],
     "total_members": 3,
     "failed_additions": []
   }

User Management
---------------

List Organization Users
~~~~~~~~~~~~~~~~~~~~~~~

Get all users within the organization with filtering and pagination.

**GET** ``/api/v1/organizations/{organization_id}/users``

**Headers:**
``Authorization: Bearer {access_token}``

**Query Parameters:**
- ``role`` (optional): Filter by user role
- ``team_id`` (optional): Filter by team membership
- ``status`` (optional): Filter by user status (active, inactive, pending)
- ``search`` (optional): Search by name or email
- ``page`` (optional): Page number for pagination
- ``limit`` (optional): Number of users per page

**Response:**

.. code-block:: json

   {
     "users": [
       {
         "user_id": 11111,
         "email": "<EMAIL>",
         "first_name": "Alice",
         "last_name": "Johnson",
         "role": "analyst",
         "status": "active",
         "teams": [
           {
             "team_id": 54321,
             "team_name": "Security Operations Team",
             "role": "analyst"
           }
         ],
         "certifications": {
           "completed": 3,
           "in_progress": 2,
           "planned": 1
         },
         "last_activity": "2025-06-16T14:30:00Z",
         "joined_at": "2025-01-15T10:00:00Z"
       }
     ],
     "total_users": 250,
     "page": 1,
     "limit": 50,
     "total_pages": 5
   }

Invite Users
~~~~~~~~~~~~

Invite new users to join the organization.

**POST** ``/api/v1/organizations/{organization_id}/users/invite``

**Headers:**
``Authorization: Bearer {access_token}``

.. code-block:: json

   {
     "invitations": [
       {
         "email": "<EMAIL>",
         "first_name": "New",
         "last_name": "User",
         "role": "user",
         "team_ids": [54321],
         "welcome_message": "Welcome to our security training program!"
       }
     ],
     "send_email": true,
     "expires_in_days": 7
   }

**Response:**

.. code-block:: json

   {
     "invitations_sent": [
       {
         "email": "<EMAIL>",
         "invitation_id": "inv_abc123",
         "expires_at": "2025-06-23T16:45:00Z",
         "status": "sent"
       }
     ],
     "failed_invitations": [],
     "total_sent": 1
   }

Update User Role
~~~~~~~~~~~~~~~~

Update user role and permissions within the organization.

**PUT** ``/api/v1/organizations/{organization_id}/users/{user_id}/role``

**Headers:**
``Authorization: Bearer {admin_token}``

.. code-block:: json

   {
     "role": "manager",
     "permissions": [
       "read:all_teams",
       "write:team_management",
       "read:analytics",
       "manage:team_members"
     ],
     "effective_date": "2025-06-17T00:00:00Z"
   }

**Response:**

.. code-block:: json

   {
     "user_id": 11111,
     "organization_id": 12345,
     "previous_role": "analyst",
     "new_role": "manager",
     "permissions": [
       "read:all_teams",
       "write:team_management",
       "read:analytics",
       "manage:team_members"
     ],
     "effective_date": "2025-06-17T00:00:00Z",
     "updated_at": "2025-06-16T16:50:00Z"
   }

Analytics and Reporting
-----------------------

Organization Analytics
~~~~~~~~~~~~~~~~~~~~~~

Get comprehensive organization-wide analytics and insights.

**GET** ``/api/v1/organizations/{organization_id}/analytics``

**Headers:**
``Authorization: Bearer {access_token}``

**Query Parameters:**
- ``period`` (optional): Analysis period (7d, 30d, 90d, 1y)
- ``include_teams`` (optional): Include team-level breakdowns

**Response:**

.. code-block:: json

   {
     "organization_id": 12345,
     "period": "30d",
     "overview": {
       "total_users": 250,
       "active_users": 230,
       "user_growth_rate": 0.15,
       "total_teams": 15,
       "total_certifications": 45,
       "completion_rate": 0.78,
       "average_study_hours": 12.5
     },
     "certification_metrics": {
       "certifications_completed": 35,
       "certifications_in_progress": 68,
       "average_completion_time_days": 45,
       "success_rate": 0.89,
       "top_certifications": [
         {
           "certification": "CISSP",
           "completions": 12,
           "in_progress": 18,
           "success_rate": 0.92
         }
       ]
     },
     "team_performance": [
       {
         "team_id": 54321,
         "team_name": "Security Operations Team",
         "completion_rate": 0.85,
         "average_score": 4.2,
         "budget_utilization": 0.65,
         "member_count": 8
       }
     ],
     "budget_analysis": {
       "total_budget": 500000,
       "utilized_budget": 325000,
       "utilization_rate": 0.65,
       "roi": 2.8,
       "cost_per_certification": 9285
     },
     "trends": {
       "user_engagement": [
         {
           "date": "2025-06-16",
           "active_users": 230,
           "study_hours": 2875,
           "sessions": 145
         }
       ],
       "certification_progress": [
         {
           "date": "2025-06-16",
           "completions": 3,
           "new_enrollments": 8,
           "progress_rate": 0.78
         }
       ]
     }
   }

Team Performance Report
~~~~~~~~~~~~~~~~~~~~~~~

Generate detailed performance report for specific team.

**GET** ``/api/v1/organizations/{organization_id}/teams/{team_id}/performance``

**Headers:**
``Authorization: Bearer {access_token}``

**Query Parameters:**
- ``period`` (optional): Analysis period (7d, 30d, 90d, 1y)

**Response:**

.. code-block:: json

   {
     "team_id": 54321,
     "team_name": "Security Operations Team",
     "period": "30d",
     "performance_score": 4.2,
     "metrics": {
       "completion_rate": 0.85,
       "average_study_hours": 15.2,
       "certification_velocity": 2.5,
       "knowledge_retention": 0.88,
       "collaboration_score": 4.5,
       "goal_achievement": 0.92
     },
     "member_performance": [
       {
         "user_id": 11111,
         "name": "Alice Johnson",
         "completion_rate": 0.90,
         "study_hours": 18,
         "certifications_completed": 2,
         "performance_score": 4.5,
         "improvement_trend": "positive"
       }
     ],
     "strengths": [
       "High collaboration and knowledge sharing",
       "Consistent study habits across team members",
       "Strong performance in technical certifications"
     ],
     "improvement_areas": [
       "Management certification completion rates",
       "Cross-training in emerging technologies"
     ],
     "recommendations": [
       "Consider advanced leadership training for senior members",
       "Implement peer mentoring program",
       "Increase focus on cloud security certifications"
     ]
   }

Enterprise Settings
-------------------

Security Policies
~~~~~~~~~~~~~~~~~

Configure organization-wide security policies.

**PUT** ``/api/v1/organizations/{organization_id}/security-policies``

**Headers:**
``Authorization: Bearer {admin_token}``

.. code-block:: json

   {
     "password_policy": {
       "min_length": 12,
       "require_uppercase": true,
       "require_lowercase": true,
       "require_numbers": true,
       "require_special_chars": true,
       "password_history": 5,
       "max_age_days": 90
     },
     "session_policy": {
       "max_session_duration_hours": 8,
       "idle_timeout_minutes": 30,
       "max_concurrent_sessions": 3,
       "require_mfa": true
     },
     "access_policy": {
       "allowed_ip_ranges": ["***********/24", "10.0.0.0/8"],
       "blocked_countries": ["CN", "RU"],
       "require_device_registration": true,
       "allow_mobile_access": true
     }
   }

**Response:**

.. code-block:: json

   {
     "organization_id": 12345,
     "security_policies": {
       "password_policy": {
         "min_length": 12,
         "require_uppercase": true,
         "require_lowercase": true,
         "require_numbers": true,
         "require_special_chars": true,
         "password_history": 5,
         "max_age_days": 90
       },
       "session_policy": {
         "max_session_duration_hours": 8,
         "idle_timeout_minutes": 30,
         "max_concurrent_sessions": 3,
         "require_mfa": true
       },
       "access_policy": {
         "allowed_ip_ranges": ["***********/24", "10.0.0.0/8"],
         "blocked_countries": ["CN", "RU"],
         "require_device_registration": true,
         "allow_mobile_access": true
       }
     },
     "updated_at": "2025-06-16T17:00:00Z",
     "effective_date": "2025-06-17T00:00:00Z"
   }

Compliance and Audit
--------------------

Audit Logs
~~~~~~~~~~

Retrieve comprehensive audit logs for compliance and security monitoring.

**GET** ``/api/v1/organizations/{organization_id}/audit-logs``

**Headers:**
``Authorization: Bearer {admin_token}``

**Query Parameters:**
- ``start_date`` (optional): Start date for log retrieval
- ``end_date`` (optional): End date for log retrieval
- ``user_id`` (optional): Filter by specific user
- ``action_type`` (optional): Filter by action type
- ``limit`` (optional): Number of logs to return

**Response:**

.. code-block:: json

   {
     "audit_logs": [
       {
         "log_id": "log_abc123",
         "timestamp": "2025-06-16T17:15:00Z",
         "user_id": 11111,
         "user_email": "<EMAIL>",
         "action": "user_role_updated",
         "resource": "user:22222",
         "details": {
           "previous_role": "user",
           "new_role": "analyst",
           "changed_by": "admin:67890"
         },
         "ip_address": "*************",
         "user_agent": "Mozilla/5.0...",
         "result": "success"
       }
     ],
     "total_logs": 1250,
     "page": 1,
     "limit": 100
   }

Compliance Report
~~~~~~~~~~~~~~~~~

Generate compliance reports for regulatory requirements.

**POST** ``/api/v1/organizations/{organization_id}/compliance/report``

**Headers:**
``Authorization: Bearer {admin_token}``

.. code-block:: json

   {
     "report_type": "sox_compliance",
     "period_start": "2025-01-01T00:00:00Z",
     "period_end": "2025-06-16T23:59:59Z",
     "include_sections": [
       "user_access_controls",
       "data_retention",
       "audit_trails",
       "security_policies"
     ]
   }

**Response:**

.. code-block:: json

   {
     "report_id": "report_compliance_123",
     "organization_id": 12345,
     "report_type": "sox_compliance",
     "generated_at": "2025-06-16T17:30:00Z",
     "period": {
       "start": "2025-01-01T00:00:00Z",
       "end": "2025-06-16T23:59:59Z"
     },
     "compliance_score": 0.95,
     "findings": [
       {
         "section": "user_access_controls",
         "status": "compliant",
         "score": 0.98,
         "details": "All user access controls properly implemented"
       }
     ],
     "recommendations": [
       "Review and update data retention policies quarterly",
       "Implement additional monitoring for privileged access"
     ],
     "download_url": "https://api.certpathfinder.com/reports/compliance_123.pdf"
   }

Integration Features
--------------------

**Enterprise Integrations:**
- Active Directory / LDAP synchronization
- Single Sign-On (SSO) with SAML 2.0 and OAuth 2.0
- HR system integration for automated user provisioning
- Learning Management System (LMS) connectivity
- Business Intelligence (BI) tool integration

**API Features:**
- RESTful API with comprehensive endpoints
- Webhook support for real-time notifications
- Bulk operations for large-scale management
- Rate limiting and throttling for enterprise scale
- Comprehensive audit logging and monitoring
