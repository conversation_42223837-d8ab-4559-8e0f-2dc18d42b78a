Authentication & Security
==========================

CertPathFinder uses token-based authentication to secure API access. This section covers authentication methods, security best practices, and access control.

.. contents:: Table of Contents
   :local:
   :depth: 2

Authentication Methods
----------------------

Token-Based Authentication
~~~~~~~~~~~~~~~~~~~~~~~~~~

The primary authentication method uses JWT (JSON Web Tokens) for secure API access.

**Login Endpoint**

.. code-block:: http

   POST /api/v1/auth/login
   Content-Type: application/json

   {
     "username": "<EMAIL>",
     "password": "your_secure_password"
   }

**Response**

.. code-block:: json

   {
     "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     "token_type": "bearer",
     "expires_in": 3600,
     "expires_at": "2025-01-15T10:30:00Z",
     "user_id": 123,
     "permissions": ["read", "write"]
   }

**Using the Token**

Include the token in the Authorization header for all subsequent requests:

.. code-block:: http

   GET /api/v1/certifications
   Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
   Content-Type: application/json

Python Example
~~~~~~~~~~~~~~

.. code-block:: python

   import requests
   from datetime import datetime, timedelta

   class CertPathFinderClient:
       def __init__(self, base_url, username, password):
           self.base_url = base_url
           self.username = username
           self.password = password
           self.token = None
           self.token_expires = None
           
       def authenticate(self):
           """Authenticate and obtain access token"""
           auth_data = {
               "username": self.username,
               "password": self.password
           }
           
           response = requests.post(
               f"{self.base_url}/api/v1/auth/login",
               json=auth_data
           )
           
           if response.status_code == 200:
               token_data = response.json()
               self.token = token_data["access_token"]
               self.token_expires = datetime.fromisoformat(
                   token_data["expires_at"].replace('Z', '+00:00')
               )
               return True
           else:
               raise Exception(f"Authentication failed: {response.status_code}")
       
       def get_headers(self):
           """Get headers with valid token"""
           if not self.token or datetime.now() >= self.token_expires:
               self.authenticate()
           
           return {
               "Authorization": f"Bearer {self.token}",
               "Content-Type": "application/json"
           }
       
       def make_request(self, method, endpoint, **kwargs):
           """Make authenticated request"""
           headers = self.get_headers()
           url = f"{self.base_url}/api/v1{endpoint}"
           
           return requests.request(method, url, headers=headers, **kwargs)

   # Usage example
   client = CertPathFinderClient(
       base_url="http://localhost:8000",
       username="<EMAIL>",
       password="secure_password"
   )
   
   # Make authenticated requests
   response = client.make_request("GET", "/certifications")
   certifications = response.json()

Token Refresh
~~~~~~~~~~~~~

Tokens expire after a configured period (default: 30 minutes). Refresh tokens before expiry:

.. code-block:: http

   POST /api/v1/auth/refresh
   Authorization: Bearer your_current_token

.. code-block:: json

   {
     "access_token": "new_jwt_token_here",
     "expires_in": 3600,
     "expires_at": "2025-01-15T11:30:00Z"
   }

Access Control & Permissions
-----------------------------

Role-Based Access Control (RBAC)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

CertPathFinder implements a hierarchical permission system:

**Permission Levels**

1. **Guest** - Read-only access to public certifications
2. **User** - Full personal profile and learning features
3. **Premium** - Advanced AI features and analytics
4. **Admin** - Organisation management capabilities
5. **Super Admin** - Full system administration
6. **Enterprise** - Multi-tenant organisation control

**Permission Matrix**

.. list-table:: API Permissions by Role
   :header-rows: 1
   :widths: 30 15 15 15 15 10

   * - Endpoint
     - Guest
     - User
     - Premium
     - Admin
     - Super Admin
   * - ``GET /certifications``
     - ✓
     - ✓
     - ✓
     - ✓
     - ✓
   * - ``POST /user/profile``
     - ✗
     - ✓
     - ✓
     - ✓
     - ✓
   * - ``GET /ai-assistant/*``
     - ✗
     - Limited
     - ✓
     - ✓
     - ✓
   * - ``POST /enterprise/*``
     - ✗
     - ✗
     - ✗
     - ✓
     - ✓
   * - ``DELETE /admin/*``
     - ✗
     - ✗
     - ✗
     - ✗
     - ✓

Multi-Tenant Security
~~~~~~~~~~~~~~~~~~~~~

For enterprise deployments, data isolation is enforced at multiple levels:

**Organisation Isolation**

.. code-block:: python

   # Each request includes organisation context
   headers = {
       "Authorization": "Bearer token",
       "X-Organisation-ID": "org_12345"
   }

**Data Access Patterns**

.. code-block:: sql

   -- All queries automatically include organisation filter
   SELECT * FROM certifications 
   WHERE organisation_id = :current_org_id
   AND deleted_at IS NULL;

Security Best Practices
------------------------

Token Security
~~~~~~~~~~~~~~

**Storage Recommendations**

- Store tokens securely (encrypted storage, secure cookies)
- Never log tokens in plain text
- Implement automatic token refresh
- Use HTTPS for all API communications

**Token Validation**

.. code-block:: python

   def validate_token(token):
       """Validate JWT token"""
       try:
           payload = jwt.decode(
               token, 
               SECRET_KEY, 
               algorithms=["HS256"]
           )
           
           # Check expiration
           if datetime.utcnow() > datetime.fromtimestamp(payload['exp']):
               raise jwt.ExpiredSignatureError
               
           return payload
           
       except jwt.InvalidTokenError:
           raise HTTPException(
               status_code=401,
               detail="Invalid authentication token"
           )

Rate Limiting
~~~~~~~~~~~~~

API endpoints are protected by rate limiting:

.. list-table:: Rate Limits by Endpoint Type
   :header-rows: 1

   * - Endpoint Category
     - Rate Limit
     - Window
   * - Authentication
     - 5 requests
     - 1 minute
   * - General API
     - 100 requests
     - 1 minute
   * - AI Assistant
     - 20 requests
     - 1 minute
   * - File Uploads
     - 10 requests
     - 5 minutes

**Rate Limit Headers**

.. code-block:: http

   HTTP/1.1 200 OK
   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1642694400

Error Handling
--------------

Authentication Errors
~~~~~~~~~~~~~~~~~~~~~

**Common Error Responses**

.. code-block:: json

   {
     "error": "authentication_failed",
     "message": "Invalid credentials provided",
     "code": 401,
     "timestamp": "2025-01-15T10:30:00Z"
   }

**Error Codes**

- ``401`` - Unauthorized (invalid/missing token)
- ``403`` - Forbidden (insufficient permissions)
- ``429`` - Too Many Requests (rate limit exceeded)
- ``422`` - Validation Error (invalid request data)

**Python Error Handling**

.. code-block:: python

   try:
       response = client.make_request("GET", "/certifications")
       response.raise_for_status()
       data = response.json()
       
   except requests.exceptions.HTTPError as e:
       if e.response.status_code == 401:
           print("Authentication failed - please check credentials")
       elif e.response.status_code == 403:
           print("Access denied - insufficient permissions")
       elif e.response.status_code == 429:
           print("Rate limit exceeded - please wait before retrying")
       else:
           print(f"API error: {e.response.status_code}")

Testing Authentication
----------------------

**Test Credentials**

For development and testing environments:

.. code-block:: bash

   # Development environment
   USERNAME=<EMAIL>
   PASSWORD=test_password_123

**Automated Testing**

.. code-block:: python

   import pytest
   from tests.conftest import test_client, test_user

   def test_authentication_success(test_client, test_user):
       """Test successful authentication"""
       auth_data = {
           "username": test_user.email,
           "password": "test_password"
       }
       
       response = test_client.post("/api/v1/auth/login", json=auth_data)
       
       assert response.status_code == 200
       data = response.json()
       assert "access_token" in data
       assert data["token_type"] == "bearer"

   def test_invalid_credentials(test_client):
       """Test authentication with invalid credentials"""
       auth_data = {
           "username": "<EMAIL>",
           "password": "wrong_password"
       }
       
       response = test_client.post("/api/v1/auth/login", json=auth_data)
       
       assert response.status_code == 401
       assert "authentication_failed" in response.json()["error"]

See Also
--------

- :doc:`../installation` - Setting up authentication
- :doc:`../configuration` - Security configuration options
- :doc:`../guides/admin_guide` - User management
- :doc:`../enterprise/user_management` - Enterprise user administration
