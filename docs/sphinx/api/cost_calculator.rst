Cost Calculator API
===================

The Cost Calculator API provides comprehensive financial analysis for certification investments, including ROI calculations, cost scenarios, and budget planning tools.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Cost Calculator offers:

- **Comprehensive Cost Analysis** - Exam fees, materials, training, and opportunity costs
- **ROI Calculations** - Return on investment with salary impact analysis
- **Multi-currency Support** - Real-time exchange rates and localised pricing
- **Scenario Modelling** - Compare different investment strategies
- **Budget Planning** - Long-term financial planning for certification paths
- **Historical Tracking** - Cost trends and budget utilisation analysis

All calculations use real-time market data and personalised factors.

Core Calculations
-----------------

Calculate Certification Costs
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Perform comprehensive cost analysis for a specific certification.

.. code-block:: http

   POST /api/v1/cost-calculator/calculate
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "certification_id": 45,
     "user_profile": {
       "location": "United Kingdom",
       "current_salary": 65000,
       "currency": "GBP",
       "experience_years": 3,
       "current_role": "Security Analyst"
     },
     "study_plan": {
       "study_hours_per_week": 10,
       "target_completion_months": 6,
       "preferred_study_method": "self_study"
     },
     "cost_options": {
       "include_training_course": false,
       "include_practice_exams": true,
       "include_books_materials": true,
       "include_opportunity_cost": true,
       "training_provider": null
     }
   }

**Response**

.. code-block:: json

   {
     "certification_id": 45,
     "certification_name": "CISSP",
     "calculation_id": "calc_12345",
     "currency": "GBP",
     "generated_at": "2025-01-15T10:30:00Z",
     "cost_breakdown": {
       "exam_fee": 749,
       "materials": {
         "official_study_guide": 65,
         "practice_tests": 45,
         "video_course": 0,
         "additional_books": 120,
         "total": 230
       },
       "training": {
         "bootcamp_course": 0,
         "online_training": 0,
         "total": 0
       },
       "opportunity_cost": {
         "study_time_hours": 240,
         "hourly_rate": 31.25,
         "total": 7500
       },
       "miscellaneous": {
         "travel_expenses": 0,
         "accommodation": 0,
         "retake_insurance": 150,
         "total": 150
       }
     },
     "total_investment": 8629,
     "roi_analysis": {
       "expected_salary_increase": {
         "immediate": 8500,
         "year_1": 8500,
         "year_3": 15000,
         "year_5": 22000
       },
       "payback_period": {
         "months": 12.2,
         "description": "Investment recovered in approximately 1 year"
       },
       "roi_percentage": {
         "year_1": 98.5,
         "year_3": 174.0,
         "year_5": 255.0
       },
       "net_present_value": 45670,
       "career_impact_score": 8.7
     },
     "market_analysis": {
       "average_salary_with_cert": 73500,
       "average_salary_without_cert": 65000,
       "job_market_demand": "high",
       "certification_popularity": 0.85,
       "regional_salary_data": {
         "london": 78000,
         "manchester": 68000,
         "edinburgh": 70000
       }
     },
     "recommendations": {
       "cost_optimisation": [
         "Consider second-hand study materials to reduce costs",
         "Look for employer training budget reimbursement"
       ],
       "timing_advice": "Optimal time to pursue based on career stage and market conditions",
       "alternative_paths": [
         {
           "certification": "Security+",
           "cost": 2500,
           "roi": 145,
           "description": "Lower cost entry point with good ROI"
         }
       ]
     }
   }

Compare Certifications
~~~~~~~~~~~~~~~~~~~~~~

Compare costs and ROI across multiple certifications.

.. code-block:: http

   POST /api/v1/cost-calculator/compare
   Content-Type: application/json

   {
     "certification_ids": [45, 67, 89],
     "user_profile": {
       "location": "United Kingdom",
       "current_salary": 65000,
       "currency": "GBP"
     },
     "comparison_criteria": {
       "include_opportunity_cost": true,
       "time_horizon_years": 3,
       "discount_rate": 0.05
     }
   }

**Response**

.. code-block:: json

   {
     "comparison_id": "comp_67890",
     "generated_at": "2025-01-15T10:30:00Z",
     "certifications": [
       {
         "id": 45,
         "name": "CISSP",
         "total_cost": 8629,
         "roi_3_year": 174.0,
         "payback_months": 12.2,
         "career_impact": 8.7,
         "difficulty_score": 7.5,
         "time_to_complete_months": 6
       },
       {
         "id": 67,
         "name": "CISM",
         "total_cost": 7850,
         "roi_3_year": 156.0,
         "payback_months": 13.8,
         "career_impact": 8.2,
         "difficulty_score": 7.0,
         "time_to_complete_months": 5
       }
     ],
     "recommendations": {
       "best_roi": {
         "certification_id": 45,
         "name": "CISSP",
         "reason": "Highest 3-year ROI and career impact"
       },
       "most_cost_effective": {
         "certification_id": 67,
         "name": "CISM",
         "reason": "Lower total investment with strong returns"
       },
       "fastest_payback": {
         "certification_id": 45,
         "name": "CISSP",
         "reason": "Shortest payback period"
       }
     },
     "summary": {
       "total_certifications_compared": 3,
       "average_cost": 8240,
       "average_roi": 165.0,
       "cost_range": {
         "min": 7850,
         "max": 8629
       }
     }
   }

Scenario Modelling
------------------

Create Cost Scenario
~~~~~~~~~~~~~~~~~~~~

Model different cost scenarios for strategic planning.

.. code-block:: http

   POST /api/v1/cost-calculator/scenarios
   Content-Type: application/json

   {
     "scenario_name": "Aggressive Certification Path",
     "description": "Multiple certifications over 18 months",
     "user_profile": {
       "location": "United Kingdom",
       "current_salary": 65000,
       "currency": "GBP"
     },
     "certification_sequence": [
       {
         "certification_id": 23,
         "start_month": 1,
         "study_intensity": "high"
       },
       {
         "certification_id": 45,
         "start_month": 7,
         "study_intensity": "medium"
       },
       {
         "certification_id": 67,
         "start_month": 13,
         "study_intensity": "medium"
       }
     ],
     "assumptions": {
       "salary_increase_timing": "after_each_cert",
       "employer_reimbursement": 0.5,
       "study_efficiency_improvement": 0.15
     }
   }

**Response**

.. code-block:: json

   {
     "scenario_id": "scenario_abc123",
     "scenario_name": "Aggressive Certification Path",
     "total_timeline_months": 18,
     "generated_at": "2025-01-15T10:30:00Z",
     "financial_summary": {
       "total_investment": 18500,
       "employer_reimbursement": 9250,
       "net_personal_cost": 9250,
       "cumulative_salary_increase": 28000,
       "net_benefit_18_months": 18750,
       "roi_percentage": 202.7
     },
     "certification_timeline": [
       {
         "month": 1,
         "certification": "Security+",
         "cost": 2500,
         "completion_month": 4,
         "salary_impact": 5000
       },
       {
         "month": 7,
         "certification": "CISSP",
         "cost": 8629,
         "completion_month": 12,
         "salary_impact": 12000
       }
     ],
     "cash_flow_analysis": {
       "monthly_cash_flow": [
         {
           "month": 1,
           "investment": -625,
           "salary_increase": 0,
           "net_flow": -625
         }
       ],
       "break_even_month": 14,
       "peak_negative_cash_flow": -9250
     },
     "risk_analysis": {
       "success_probability": 0.78,
       "risk_factors": [
         "Aggressive timeline may impact success rates",
         "High study intensity requires significant time commitment"
       ],
       "mitigation_strategies": [
         "Build buffer time into schedule",
         "Consider reducing study intensity if needed"
       ]
     }
   }

Budget Planning
---------------

Create Training Budget
~~~~~~~~~~~~~~~~~~~~~~

Plan annual training budgets for individuals or teams.

.. code-block:: http

   POST /api/v1/cost-calculator/budget-plan
   Content-Type: application/json

   {
     "budget_type": "individual",
     "planning_period": "annual",
     "budget_amount": 15000,
     "currency": "GBP",
     "user_profile": {
       "current_role": "Security Analyst",
       "experience_years": 3,
       "career_goals": ["Security Architect", "Team Lead"]
     },
     "constraints": {
       "max_certifications_per_year": 2,
       "preferred_study_intensity": "medium",
       "employer_reimbursement_rate": 0.75
     }
   }

**Response**

.. code-block:: json

   {
     "budget_plan_id": "budget_def456",
     "planning_period": "2025",
     "total_budget": 15000,
     "currency": "GBP",
     "generated_at": "2025-01-15T10:30:00Z",
     "recommended_certifications": [
       {
         "certification_id": 45,
         "name": "CISSP",
         "priority": 1,
         "estimated_cost": 8629,
         "employer_reimbursement": 6472,
         "personal_cost": 2157,
         "expected_completion": "Q2 2025",
         "roi_score": 8.7
       },
       {
         "certification_id": 78,
         "name": "CISA",
         "priority": 2,
         "estimated_cost": 6500,
         "employer_reimbursement": 4875,
         "personal_cost": 1625,
         "expected_completion": "Q4 2025",
         "roi_score": 7.9
       }
     ],
     "budget_allocation": {
       "total_certification_costs": 15129,
       "total_reimbursement": 11347,
       "net_personal_investment": 3782,
       "budget_utilisation": 100.9,
       "remaining_budget": -129
     },
     "quarterly_breakdown": {
       "Q1": {
         "planned_spending": 4315,
         "certifications": ["CISSP - Start"]
       },
       "Q2": {
         "planned_spending": 4314,
         "certifications": ["CISSP - Complete"]
       },
       "Q3": {
         "planned_spending": 3250,
         "certifications": ["CISA - Start"]
       },
       "Q4": {
         "planned_spending": 3250,
         "certifications": ["CISA - Complete"]
       }
     },
     "recommendations": {
       "budget_optimisation": [
         "Consider spreading CISA to next year to stay within budget",
         "Look for additional employer training funds"
       ],
       "alternative_options": [
         "Replace CISA with lower-cost Security+ for immediate impact"
       ]
     }
   }

Historical Analysis
-------------------

Cost History Tracking
~~~~~~~~~~~~~~~~~~~~~

Track historical costs and analyse spending patterns.

.. code-block:: http

   GET /api/v1/cost-calculator/history
   Authorization: Bearer <token>

   ?period=12m&include_projections=true

**Response**

.. code-block:: json

   {
     "user_id": 123,
     "period": "12m",
     "generated_at": "2025-01-15T10:30:00Z",
     "historical_spending": {
       "total_invested": 12500,
       "certifications_completed": 2,
       "average_cost_per_cert": 6250,
       "employer_reimbursement_received": 8750,
       "net_personal_investment": 3750
     },
     "monthly_breakdown": [
       {
         "month": "2024-01",
         "spending": 2500,
         "certifications": ["Security+"],
         "reimbursement": 1875
       }
     ],
     "roi_realised": {
       "salary_increases_received": 15000,
       "career_advancement": "Promoted to Senior Security Analyst",
       "actual_roi": 300.0,
       "projected_vs_actual": {
         "projected_roi": 250.0,
         "variance": 20.0
       }
     },
     "future_projections": {
       "next_12_months": {
         "planned_certifications": ["CISSP", "CISM"],
         "estimated_investment": 16000,
         "projected_salary_impact": 20000,
         "expected_roi": 225.0
       }
     },
     "insights": {
       "spending_trends": "Increasing investment in advanced certifications",
       "roi_performance": "Exceeding projected returns",
       "recommendations": [
         "Continue current certification strategy",
         "Consider accelerating timeline for CISSP"
       ]
     }
   }

Currency & Localisation
-----------------------

Exchange Rates
~~~~~~~~~~~~~~

Get current exchange rates for cost calculations.

.. code-block:: http

   GET /api/v1/cost-calculator/exchange-rates
   Authorization: Bearer <token>

   ?base_currency=GBP&target_currencies=USD,EUR,CAD

**Response**

.. code-block:: json

   {
     "base_currency": "GBP",
     "rates": {
       "USD": 1.2745,
       "EUR": 1.1823,
       "CAD": 1.7234
     },
     "last_updated": "2025-01-15T10:00:00Z",
     "source": "Bank of England",
     "next_update": "2025-01-15T16:00:00Z"
   }

Regional Pricing
~~~~~~~~~~~~~~~~

Get localised pricing for certifications by region.

.. code-block:: http

   GET /api/v1/cost-calculator/regional-pricing
   Authorization: Bearer <token>

   ?certification_id=45&regions=UK,US,EU,APAC

**Response**

.. code-block:: json

   {
     "certification_id": 45,
     "certification_name": "CISSP",
     "regional_pricing": {
       "UK": {
         "exam_fee": 749,
         "currency": "GBP",
         "materials_cost_range": {
           "min": 200,
           "max": 500
         },
         "training_cost_range": {
           "min": 2000,
           "max": 5000
         }
       },
       "US": {
         "exam_fee": 749,
         "currency": "USD",
         "materials_cost_range": {
           "min": 250,
           "max": 600
         }
       }
     },
     "cost_factors": {
       "regional_salary_multipliers": {
         "UK": 1.0,
         "US": 1.15,
         "EU": 0.95
       },
       "purchasing_power_adjustments": {
         "UK": 1.0,
         "US": 1.08,
         "EU": 0.92
       }
     }
   }

Error Handling
--------------

Cost Calculator API error responses:

.. code-block:: json

   {
     "error": "insufficient_market_data",
     "message": "Unable to calculate accurate ROI due to limited salary data for this region",
     "code": 422,
     "details": {
       "region": "Remote Islands",
       "available_data_points": 2,
       "minimum_required": 10
     },
     "suggestions": [
       "Use broader regional data",
       "Provide custom salary information"
     ]
   }

**Common Error Codes**

- ``400`` - Invalid calculation parameters
- ``404`` - Certification or region not found
- ``422`` - Insufficient data for accurate calculations
- ``429`` - Rate limit exceeded for calculations

See Also
--------

- :doc:`../guides/user_guide` - Cost calculator user guide
- :doc:`authentication` - API authentication
- :doc:`../installation` - Setup and configuration
- :doc:`../enterprise/analytics` - Enterprise cost analytics
