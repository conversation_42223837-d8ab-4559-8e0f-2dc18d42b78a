#!/usr/bin/env python3
"""
FAQ Documentation Validation Script
===================================

This script validates the FAQ documentation structure and content
to ensure all files are properly formatted and linked.
"""

import os
import re
from pathlib import Path

def validate_rst_file(file_path):
    """Validate a single RST file for common issues."""
    issues = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    # Check for title underlines
    title_pattern = re.compile(r'^[=\-~^"\'`#*+<>]{3,}$')
    has_main_title = False
    
    for i, line in enumerate(lines):
        if title_pattern.match(line):
            if i > 0 and lines[i-1].strip():
                has_main_title = True
                break
    
    if not has_main_title:
        issues.append("No main title found")
    
    # Check for proper question numbering
    question_pattern = re.compile(r'^\*\*(\d+)\.\s+')
    question_numbers = []
    
    for line in lines:
        match = question_pattern.match(line)
        if match:
            question_numbers.append(int(match.group(1)))
    
    if question_numbers:
        # Check if we have 25 questions
        if len(question_numbers) != 25:
            issues.append(f"Expected 25 questions, found {len(question_numbers)}")
        
        # Check if questions are numbered sequentially
        expected = list(range(1, len(question_numbers) + 1))
        if question_numbers != expected:
            issues.append("Questions are not numbered sequentially")
    
    # Check for proper section headers
    section_pattern = re.compile(r'^[🚀📚🎯💰👥🔧📊🤝💼🔒📞🎓🔬👨‍🏫🏗️🏢🔄💡📋🛠️🌟].*$')
    sections = [line for line in lines if section_pattern.match(line)]
    
    if len(sections) < 3:
        issues.append("Insufficient section organization (expected at least 3 sections)")
    
    return issues

def validate_faq_structure():
    """Validate the overall FAQ structure."""
    faq_dir = Path(__file__).parent / 'faq'
    
    # Expected FAQ files
    expected_files = [
        'index.rst',
        'students.rst',
        'professionals.rst',
        'managers.rst',
        'administrators.rst',
        'enterprise_admins.rst',
        'career_changers.rst',
        'academics_researchers.rst',
        'training_managers.rst'
    ]
    
    print("🔍 Validating FAQ Documentation Structure")
    print("=" * 50)
    
    # Check if all expected files exist
    missing_files = []
    for file_name in expected_files:
        file_path = faq_dir / file_name
        if not file_path.exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("✅ All expected FAQ files found")
    
    # Validate each file
    all_valid = True
    for file_name in expected_files:
        file_path = faq_dir / file_name
        print(f"\n📄 Validating {file_name}...")
        
        issues = validate_rst_file(file_path)
        if issues:
            print(f"❌ Issues found in {file_name}:")
            for issue in issues:
                print(f"   - {issue}")
            all_valid = False
        else:
            print(f"✅ {file_name} is valid")
    
    # Check index.rst toctree
    index_path = faq_dir / 'index.rst'
    with open(index_path, 'r', encoding='utf-8') as f:
        index_content = f.read()
    
    print(f"\n📋 Validating index.rst toctree...")
    
    # Check if all user type files are referenced in toctree
    user_type_files = [f.replace('.rst', '') for f in expected_files[1:]]  # Exclude index.rst
    missing_refs = []
    
    for user_type in user_type_files:
        if user_type not in index_content:
            missing_refs.append(user_type)
    
    if missing_refs:
        print(f"❌ Missing toctree references: {', '.join(missing_refs)}")
        all_valid = False
    else:
        print("✅ All user types referenced in toctree")
    
    print("\n" + "=" * 50)
    if all_valid:
        print("🎉 All FAQ documentation is valid!")
        print("\n📊 Summary:")
        print(f"   - {len(expected_files)} FAQ files created")
        print(f"   - 8 user types covered")
        print(f"   - ~25 questions per user type")
        print(f"   - ~200 total questions")
        print(f"   - Comprehensive coverage of all user needs")
    else:
        print("❌ Some issues found. Please review and fix.")
    
    return all_valid

if __name__ == "__main__":
    validate_faq_structure()
