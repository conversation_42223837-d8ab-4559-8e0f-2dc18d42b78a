# CertRats Platform Monitoring Guide

This guide covers comprehensive monitoring, observability, and alerting for the CertRats certification platform.

## 📊 Monitoring Architecture

### Components Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Applications  │───▶│   Prometheus    │───▶│     Grafana     │
│                 │    │                 │    │                 │
│ • API           │    │ • Metrics       │    │ • Dashboards    │
│ • Frontend      │    │ • <PERSON><PERSON><PERSON>        │    │ • Visualization │
│ • Database      │    │ • Storage       │    │ • Alerting      │
│ • Redis         │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      Logs       │    │   Health Checks │    │   Notifications │
│                 │    │                 │    │                 │
│ • Centralized   │    │ • Automated     │    │ • Email         │
│ • Structured    │    │ • Comprehensive │    │ • Slack         │
│ • Searchable    │    │ • Real-time     │    │ • PagerDuty     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔍 Metrics Collection

### Application Metrics

#### API Metrics
```python
# Example metrics exposed by FastAPI
http_requests_total{method="GET", endpoint="/api/v1/users", status="200"}
http_request_duration_seconds{method="GET", endpoint="/api/v1/users"}
active_connections_total
database_connections_active
cache_hit_rate
```

#### Business Metrics
```python
# Custom business metrics
user_registrations_total
certification_completions_total
learning_path_progress
user_engagement_score
revenue_metrics
```

### Infrastructure Metrics

#### System Metrics
- CPU usage per container
- Memory usage and limits
- Disk I/O and space usage
- Network traffic and latency

#### Database Metrics
- Connection pool status
- Query performance
- Lock contention
- Replication lag

#### Cache Metrics
- Hit/miss ratios
- Memory usage
- Eviction rates
- Connection counts

## 📈 Grafana Dashboards

### Platform Overview Dashboard

Access: `http://grafana.certrats.docker.localhost`

**Key Panels:**
- Service availability overview
- Request rate and response times
- Error rate trends
- Resource utilization summary

### Service-Specific Dashboards

#### API Dashboard
```
Panels:
├── Request Rate (RPS)
├── Response Time Percentiles
├── Error Rate by Endpoint
├── Database Query Performance
├── Cache Hit Rates
└── Active User Sessions
```

#### Database Dashboard
```
Panels:
├── Connection Pool Status
├── Query Performance
├── Lock Contention
├── Disk Usage
├── Replication Status
└── Backup Status
```

#### Infrastructure Dashboard
```
Panels:
├── CPU Usage by Container
├── Memory Usage and Limits
├── Disk I/O and Space
├── Network Traffic
├── Container Health Status
└── Resource Alerts
```

### Custom Dashboard Creation

```json
{
  "dashboard": {
    "title": "CertRats Custom Dashboard",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"certrats-api\"}[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

## 🚨 Alerting and Notifications

### Alert Rules Configuration

#### Critical Alerts
```yaml
# prometheus/alerts.yml
groups:
  - name: certrats-critical
    rules:
      - alert: ServiceDown
        expr: up{job=~"certrats-.*"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "CertRats service {{ $labels.job }} is down"
          description: "Service has been down for more than 5 minutes"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      - alert: DatabaseConnectionFailure
        expr: postgresql_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "Cannot connect to PostgreSQL database"
```

#### Warning Alerts
```yaml
  - name: certrats-warning
    rules:
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.8
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Container {{ $labels.name }} memory usage is {{ $value }}%"
```

### Notification Channels

#### Slack Integration
```yaml
# alertmanager.yml
route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'slack-notifications'

receivers:
  - name: 'slack-notifications'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#certrats-alerts'
        title: 'CertRats Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
```

#### Email Notifications
```yaml
receivers:
  - name: 'email-notifications'
    email_configs:
      - to: '<EMAIL>'
        from: '<EMAIL>'
        subject: 'CertRats Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
```

## 🏥 Health Checks

### Automated Health Monitoring

```bash
# Run comprehensive health checks
python scripts/test-health-checks.py --environment production

# Continuous monitoring
watch -n 30 'python scripts/test-health-checks.py --environment production --format human'

# Integration with monitoring
python scripts/test-health-checks.py --environment production --format prometheus > /var/lib/prometheus/health-metrics.prom
```

### Health Check Endpoints

#### API Health Check
```
GET /health
Response:
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z",
  "version": "1.0.0",
  "checks": {
    "database": "healthy",
    "redis": "healthy",
    "storage": "healthy"
  }
}
```

#### Database Health Check
```bash
# PostgreSQL health check
docker exec certrats_database pg_isready -U certrats

# Connection test
docker exec certrats_api python -c "
import psycopg2
try:
    conn = psycopg2.connect('********************************************/certrats_db')
    print('Database: healthy')
except:
    print('Database: unhealthy')
"
```

### Custom Health Checks

```python
# health_checks.py
import asyncio
import aiohttp
import asyncpg

async def check_api_health():
    async with aiohttp.ClientSession() as session:
        async with session.get('http://api:8000/health') as response:
            return response.status == 200

async def check_database_health():
    try:
        conn = await asyncpg.connect('************************************/db')
        await conn.execute('SELECT 1')
        await conn.close()
        return True
    except:
        return False

async def comprehensive_health_check():
    checks = {
        'api': await check_api_health(),
        'database': await check_database_health(),
    }
    return all(checks.values()), checks
```

## 📝 Log Management

### Centralized Logging

#### Log Aggregation with Loki
```yaml
# docker-compose.yml
loki:
  image: grafana/loki:latest
  ports:
    - "3100:3100"
  volumes:
    - ./loki-config.yml:/etc/loki/local-config.yaml
    - loki_data:/loki
```

#### Log Collection with Promtail
```yaml
promtail:
  image: grafana/promtail:latest
  volumes:
    - /var/log:/var/log:ro
    - /var/lib/docker/containers:/var/lib/docker/containers:ro
    - ./promtail-config.yml:/etc/promtail/config.yml
```

### Log Analysis

#### Structured Logging
```python
# Python logging configuration
import structlog

logger = structlog.get_logger()
logger.info("User login", user_id=123, ip_address="***********", success=True)
```

#### Log Queries
```
# Grafana Loki queries
{job="certrats-api"} |= "ERROR"
{job="certrats-api"} | json | level="error"
rate({job="certrats-api"}[5m])
```

## 📊 Performance Monitoring

### Application Performance Monitoring (APM)

#### Response Time Tracking
```python
# FastAPI middleware for response time tracking
import time
from fastapi import Request

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

#### Database Query Monitoring
```python
# SQLAlchemy query monitoring
from sqlalchemy import event
from sqlalchemy.engine import Engine

@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    logger.info("Query executed", duration=total, query=statement[:100])
```

### Resource Monitoring

#### Container Resource Usage
```bash
# Monitor container resources
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# Historical resource data
docker exec prometheus promtool query instant 'container_memory_usage_bytes{name=~"certrats_.*"}'
```

#### System Resource Monitoring
```bash
# System monitoring commands
htop                    # Interactive process viewer
iotop                   # I/O monitoring
nethogs                 # Network usage by process
df -h                   # Disk usage
free -h                 # Memory usage
```

## 🔧 Monitoring Tools and Scripts

### Custom Monitoring Scripts

```bash
#!/bin/bash
# monitor-certrats.sh
while true; do
    echo "=== $(date) ==="
    ./scripts/docker/status-certrats.sh dev --json | jq '.services[] | select(.status != "healthy")'
    sleep 60
done
```

### Monitoring Automation

```bash
# Cron job for regular health checks
# Add to crontab: crontab -e
*/5 * * * * /opt/certrats/scripts/monitor/health-check.sh
0 */6 * * * /opt/certrats/scripts/monitor/performance-report.sh
0 2 * * * /opt/certrats/scripts/monitor/daily-report.sh
```

## 📱 Mobile and Remote Monitoring

### Mobile Dashboards
- Grafana mobile app
- Custom mobile dashboard views
- Push notifications for critical alerts

### Remote Access
```bash
# SSH tunnel for secure access
ssh -L 3000:localhost:3000 user@certrats-server

# VPN access for monitoring tools
# Configure OpenVPN or WireGuard
```

## 🎯 Best Practices

### Monitoring Strategy
1. **Monitor what matters**: Focus on user-impacting metrics
2. **Set meaningful thresholds**: Avoid alert fatigue
3. **Document runbooks**: Clear response procedures
4. **Regular reviews**: Update monitoring as system evolves

### Alert Management
1. **Severity levels**: Critical, warning, info
2. **Escalation procedures**: Clear ownership and escalation paths
3. **Alert correlation**: Group related alerts
4. **Regular testing**: Test alert channels and procedures

### Performance Optimization
1. **Baseline establishment**: Know normal performance patterns
2. **Proactive monitoring**: Identify issues before they impact users
3. **Capacity planning**: Monitor trends for resource planning
4. **Regular optimization**: Continuous performance improvements

---

This monitoring guide provides comprehensive coverage of observability, alerting, and performance monitoring for the CertRats platform, ensuring reliable operation and quick issue resolution.
