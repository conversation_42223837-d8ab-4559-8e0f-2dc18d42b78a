# CertRats Docker Infrastructure

This directory contains comprehensive Docker infrastructure for the CertRats certification platform, including containerized services, monitoring, and management tools.

## 🏗️ Architecture Overview

The CertRats platform uses a microservices architecture with Docker containers orchestrated through Docker Compose and managed by <PERSON><PERSON>fik reverse proxy.

### Core Components

- **API Service**: FastAPI backend with authentication and business logic
- **Frontend**: React.js application with modern UI components
- **Database**: PostgreSQL with optimized configuration
- **Cache**: Redis for session management and caching
- **Storage**: MinIO object storage for file management
- **Background Processing**: Celery workers and beat scheduler
- **Monitoring**: Prometheus metrics and Grafana dashboards
- **Reverse Proxy**: Traefik with SSL termination and service discovery

### Network Architecture

```
Internet → Traefik → Services
                  ↓
            Internal Network
                  ↓
         Database & Cache Layer
```

## 🚀 Quick Start

### Prerequisites

- Docker 20.10+ and Docker Compose 2.0+
- Git for version control
- Python 3.9+ (for management scripts)
- 4GB+ RAM and 20GB+ disk space

### Basic Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/forkrul/replit-CertPathFinder.git
   cd certrats
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the platform**:
   ```bash
   ./scripts/docker/start-certrats.sh dev
   ```

4. **Access services**:
   - Frontend: http://app.certrats.docker.localhost
   - API: http://api.certrats.docker.localhost
   - Traefik Dashboard: http://traefik.certrats.docker.localhost

## 📁 Directory Structure

```
docker/
├── monitoring/           # Monitoring configurations
│   ├── prometheus.yml   # Prometheus scraping config
│   └── grafana/         # Grafana dashboards and datasources
├── traefik/             # Traefik reverse proxy config
│   ├── traefik.yml     # Main Traefik configuration
│   ├── dynamic.yml     # Dynamic routing rules
│   └── certs/          # SSL certificates
scripts/docker/          # Management scripts
├── start-certrats.sh   # Platform startup script
├── stop-certrats.sh    # Platform shutdown script
├── status-certrats.sh  # Status monitoring script
└── troubleshoot-certrats.sh # Diagnostic script
```

## 🔧 Management Scripts

### Start Platform
```bash
# Development environment
./scripts/docker/start-certrats.sh dev

# Production environment
./scripts/docker/start-certrats.sh prod

# With options
./scripts/docker/start-certrats.sh dev --verbose --no-build
```

### Stop Platform
```bash
# Graceful shutdown
./scripts/docker/stop-certrats.sh dev

# With data backup
./scripts/docker/stop-certrats.sh prod --backup

# Complete cleanup
./scripts/docker/stop-certrats.sh dev --cleanup
```

### Monitor Status
```bash
# Basic status
./scripts/docker/status-certrats.sh dev

# Detailed view
./scripts/docker/status-certrats.sh prod --detailed

# Watch mode
./scripts/docker/status-certrats.sh dev --watch
```

### Troubleshooting
```bash
# Diagnose issues
./scripts/docker/troubleshoot-certrats.sh dev

# Auto-fix problems
./scripts/docker/troubleshoot-certrats.sh prod --fix

# Generate report
./scripts/docker/troubleshoot-certrats.sh staging --report
```

## 🌍 Environment Configurations

### Development (`docker-compose.dev.yml`)
- Hot reload enabled
- Debug mode active
- Development tools included (pgAdmin, Redis Commander, MailHog)
- Exposed ports for direct access
- Relaxed security settings

### Staging (`docker-compose.staging.yml`)
- Production-like environment
- SSL certificates with Let's Encrypt
- Moderate resource allocation
- Enhanced logging and monitoring

### Production (`docker-compose.prod.yml`)
- Optimized for performance and security
- Resource limits and health checks
- Backup and disaster recovery
- High availability configuration
- Comprehensive monitoring

## 🔍 Service Discovery

The platform uses Traefik for automatic service discovery with the following URL patterns:

### Development
- `*.certrats.docker.localhost` - Local development services

### Staging
- `*.staging.certrats.dev` - Staging environment services

### Production
- `*.certrats.com` - Production services

## 📊 Monitoring and Observability

### Prometheus Metrics
- Application performance metrics
- Infrastructure monitoring
- Custom business metrics
- Alert rules and thresholds

### Grafana Dashboards
- Platform overview dashboard
- Service-specific dashboards
- Infrastructure monitoring
- Business intelligence views

### Health Checks
- Automated health monitoring
- Service dependency validation
- Performance threshold testing
- Comprehensive reporting

## 🔐 Security

### SSL/TLS
- Automatic certificate management with Let's Encrypt
- SSL termination at Traefik level
- Secure internal communication

### Network Security
- Isolated internal networks
- Service-to-service authentication
- Rate limiting and DDoS protection

### Data Protection
- Encrypted data at rest
- Secure backup procedures
- Access control and audit logging

## 🚨 Troubleshooting

### Common Issues

1. **Services not starting**:
   ```bash
   ./scripts/docker/troubleshoot-certrats.sh dev --fix
   ```

2. **Network connectivity issues**:
   ```bash
   docker network ls
   docker network inspect traefik_network
   ```

3. **SSL certificate problems**:
   ```bash
   docker logs traefik_proxy
   ```

4. **Database connection issues**:
   ```bash
   docker exec certrats_api nc -z database 5432
   ```

### Log Analysis
```bash
# View service logs
docker-compose logs -f api

# Search for errors
docker-compose logs api | grep -i error

# Follow logs in real-time
./scripts/docker/status-certrats.sh dev --logs
```

## 🔄 Backup and Recovery

### Automated Backups
- Daily database backups
- Redis data snapshots
- MinIO object storage backups
- Configuration backups

### Manual Backup
```bash
# Backup before shutdown
./scripts/docker/stop-certrats.sh prod --backup

# Manual database backup
docker exec certrats_database pg_dump -U certrats certrats_prod > backup.sql
```

### Recovery Procedures
```bash
# Restore database
docker exec -i certrats_database psql -U certrats certrats_prod < backup.sql

# Restore volumes
docker run --rm -v certrats_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_data.tar.gz -C /data
```

## 📈 Performance Optimization

### Resource Allocation
- CPU and memory limits per service
- Horizontal scaling for high-load services
- Load balancing configuration

### Database Optimization
- Connection pooling
- Query optimization
- Index management
- Performance monitoring

### Caching Strategy
- Redis caching layers
- CDN integration
- Static asset optimization

## 🧪 Testing

### Health Check Testing
```bash
# Run comprehensive health checks
python scripts/test-health-checks.py --environment dev

# Performance testing
python scripts/test-health-checks.py --environment prod --format prometheus
```

### Load Testing
```bash
# API load testing
docker run --rm -i grafana/k6 run - < tests/load/api-test.js

# Database performance testing
docker exec certrats_database pgbench -c 10 -j 2 -t 1000 certrats_dev
```

## 📚 Additional Resources

- [Deployment Guide](deployment.md)
- [Monitoring Guide](monitoring.md)
- [Troubleshooting Guide](troubleshooting.md)
- [Security Guide](security.md)
- [API Documentation](../api/README.md)
- [Frontend Documentation](../frontend/README.md)

## 🤝 Contributing

1. Follow the development workflow
2. Test changes in development environment
3. Update documentation as needed
4. Submit pull requests with comprehensive descriptions

## 📞 Support

For issues and questions:
- Check the troubleshooting guide
- Run diagnostic scripts
- Review service logs
- Contact the development team

---

**Note**: This infrastructure is designed for scalability and maintainability. Regular updates and monitoring ensure optimal performance and security.
