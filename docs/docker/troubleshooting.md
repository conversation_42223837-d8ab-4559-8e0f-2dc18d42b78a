# CertRats Platform Troubleshooting Guide

This guide provides comprehensive troubleshooting procedures for the CertRats certification platform Docker infrastructure.

## 🔧 Quick Diagnostic Tools

### Automated Troubleshooting
```bash
# Run comprehensive diagnostics
./scripts/docker/troubleshoot-certrats.sh dev

# Auto-fix common issues
./scripts/docker/troubleshoot-certrats.sh prod --fix

# Generate detailed report
./scripts/docker/troubleshoot-certrats.sh staging --report
```

### Manual Status Check
```bash
# Check service status
./scripts/docker/status-certrats.sh dev --detailed

# Monitor in real-time
./scripts/docker/status-certrats.sh dev --watch

# View recent logs
./scripts/docker/status-certrats.sh dev --logs
```

## 🚨 Common Issues and Solutions

### 1. Services Not Starting

#### Symptoms
- Containers exit immediately
- Services show "unhealthy" status
- Connection refused errors

#### Diagnosis
```bash
# Check container status
docker ps -a --filter name=certrats_

# View container logs
docker logs certrats_api

# Check resource usage
docker stats --no-stream
```

#### Solutions

**Issue: Port conflicts**
```bash
# Find processes using ports
sudo netstat -tulpn | grep :8000
sudo lsof -i :8000

# Kill conflicting processes
sudo kill -9 <PID>
```

**Issue: Insufficient resources**
```bash
# Check system resources
free -h
df -h

# Clean up Docker resources
docker system prune -f
docker volume prune -f
```

**Issue: Environment variables**
```bash
# Verify .env file exists
ls -la .env

# Check required variables
grep -E "SECRET_KEY|POSTGRES_PASSWORD|REDIS_PASSWORD" .env
```

### 2. Database Connection Issues

#### Symptoms
- API cannot connect to database
- "Connection refused" errors
- Database container not starting

#### Diagnosis
```bash
# Check database container
docker logs certrats_database

# Test database connectivity
docker exec certrats_api nc -z database 5432

# Check database status
docker exec certrats_database pg_isready -U certrats
```

#### Solutions

**Issue: Database not initialized**
```bash
# Recreate database volume
docker volume rm certrats_postgres_data
./scripts/docker/start-certrats.sh dev --force-recreate
```

**Issue: Wrong credentials**
```bash
# Verify database credentials in .env
grep POSTGRES_ .env

# Reset database password
docker exec -it certrats_database psql -U postgres -c "ALTER USER certrats PASSWORD 'new_password';"
```

**Issue: Database corruption**
```bash
# Check database integrity
docker exec certrats_database pg_dump -U certrats certrats_db > /dev/null

# Restore from backup
./scripts/backup/restore-database.sh latest-backup.sql
```

### 3. Network Connectivity Problems

#### Symptoms
- Services cannot communicate
- Traefik routing not working
- External access issues

#### Diagnosis
```bash
# Check Docker networks
docker network ls
docker network inspect traefik_network

# Test internal connectivity
docker exec certrats_api ping certrats_database
docker exec certrats_api curl -I http://certrats_frontend:3000
```

#### Solutions

**Issue: Missing Traefik network**
```bash
# Create Traefik network
docker network create traefik_network

# Restart services
./scripts/docker/stop-certrats.sh dev
./scripts/docker/start-certrats.sh dev
```

**Issue: DNS resolution**
```bash
# Check /etc/hosts for local development
echo "127.0.0.1 app.certrats.docker.localhost" | sudo tee -a /etc/hosts
echo "127.0.0.1 api.certrats.docker.localhost" | sudo tee -a /etc/hosts
```

**Issue: Firewall blocking**
```bash
# Check firewall status
sudo ufw status

# Allow Docker networks
sudo ufw allow from **********/12
sudo ufw allow from ***********/16
```

### 4. SSL/TLS Certificate Issues

#### Symptoms
- HTTPS not working
- Certificate errors in browser
- Let's Encrypt failures

#### Diagnosis
```bash
# Check Traefik logs
docker logs traefik_proxy | grep -i cert

# Verify certificate status
curl -I https://app.certrats.docker.localhost

# Check ACME configuration
docker exec traefik_proxy cat /etc/traefik/acme.json
```

#### Solutions

**Issue: Let's Encrypt rate limits**
```bash
# Use staging environment for testing
# Edit traefik.yml to use staging ACME server
--certificatesresolvers.letsencrypt.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory
```

**Issue: DNS challenge failures**
```bash
# Verify Cloudflare API credentials
docker exec traefik_proxy env | grep CF_

# Test DNS propagation
dig TXT _acme-challenge.certrats.com
```

**Issue: Self-signed certificates for development**
```bash
# Generate development certificates
cd docker/traefik/certs
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout certrats.key \
  -out certrats.crt \
  -subj "/CN=*.certrats.docker.localhost"
```

### 5. Performance Issues

#### Symptoms
- Slow response times
- High CPU/memory usage
- Timeouts

#### Diagnosis
```bash
# Monitor resource usage
docker stats

# Check service response times
./scripts/test-health-checks.py --environment dev

# Analyze logs for performance issues
docker logs certrats_api | grep -E "slow|timeout|performance"
```

#### Solutions

**Issue: High memory usage**
```bash
# Increase memory limits in docker-compose.yml
deploy:
  resources:
    limits:
      memory: 2G

# Restart services
docker-compose restart
```

**Issue: Database performance**
```bash
# Check database connections
docker exec certrats_database psql -U certrats -c "SELECT count(*) FROM pg_stat_activity;"

# Optimize database configuration
# Edit postgresql.conf for better performance
```

**Issue: Redis performance**
```bash
# Check Redis memory usage
docker exec certrats_redis redis-cli info memory

# Clear Redis cache if needed
docker exec certrats_redis redis-cli flushall
```

### 6. Storage and Volume Issues

#### Symptoms
- Data not persisting
- Volume mount errors
- Disk space issues

#### Diagnosis
```bash
# Check volume status
docker volume ls | grep certrats
docker volume inspect certrats_postgres_data

# Check disk usage
df -h
docker system df
```

#### Solutions

**Issue: Volume permissions**
```bash
# Fix volume permissions
docker exec -it certrats_database chown -R postgres:postgres /var/lib/postgresql/data
```

**Issue: Disk space full**
```bash
# Clean up Docker resources
docker system prune -a -f
docker volume prune -f

# Remove old images
docker image prune -a -f
```

**Issue: Volume corruption**
```bash
# Backup and recreate volume
docker run --rm -v certrats_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .
docker volume rm certrats_postgres_data
docker volume create certrats_postgres_data
docker run --rm -v certrats_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_backup.tar.gz -C /data
```

## 🔍 Advanced Debugging

### Container Debugging

```bash
# Enter container for debugging
docker exec -it certrats_api bash

# Check container filesystem
docker exec certrats_api ls -la /app

# Monitor container processes
docker exec certrats_api ps aux

# Check container environment
docker exec certrats_api env
```

### Log Analysis

```bash
# Centralized log viewing
docker-compose logs -f --tail=100

# Search for specific errors
docker-compose logs | grep -i "error\|exception\|failed"

# Export logs for analysis
docker-compose logs > certrats-logs-$(date +%Y%m%d).txt
```

### Network Debugging

```bash
# Test network connectivity
docker run --rm --network certrats_network alpine ping certrats_database

# Check port accessibility
docker run --rm --network certrats_network alpine nc -zv certrats_api 8000

# Inspect network configuration
docker network inspect certrats_network
```

## 📊 Monitoring and Alerting

### Health Check Monitoring

```bash
# Continuous health monitoring
watch -n 30 './scripts/docker/status-certrats.sh dev'

# Set up alerts for critical services
./scripts/monitoring/setup-alerts.sh
```

### Performance Monitoring

```bash
# Monitor resource usage over time
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" > stats.log

# Track response times
curl -w "@curl-format.txt" -o /dev/null -s http://api.certrats.docker.localhost/health
```

## 🚨 Emergency Procedures

### Service Recovery

```bash
# Emergency restart all services
./scripts/docker/stop-certrats.sh dev --force
./scripts/docker/start-certrats.sh dev --force-recreate

# Rollback to previous version
git checkout HEAD~1
./scripts/docker/start-certrats.sh prod
```

### Data Recovery

```bash
# Restore from latest backup
./scripts/backup/restore-latest.sh

# Emergency database recovery
docker exec certrats_database pg_resetwal /var/lib/postgresql/data
```

### Security Incident Response

```bash
# Immediate isolation
docker network disconnect traefik_network certrats_api

# Audit logs
docker logs certrats_api | grep -E "unauthorized|attack|suspicious"

# Reset credentials
./scripts/security/reset-credentials.sh
```

## 📞 Getting Help

### Diagnostic Information Collection

```bash
# Generate comprehensive diagnostic report
./scripts/docker/troubleshoot-certrats.sh dev --report

# Collect system information
./scripts/diagnostics/collect-info.sh
```

### Support Channels

1. **Internal Documentation**: Check docs/ directory
2. **Log Analysis**: Use provided diagnostic tools
3. **Community Support**: GitHub issues and discussions
4. **Professional Support**: Contact development team

### Escalation Procedures

1. **Level 1**: Use automated diagnostic tools
2. **Level 2**: Manual troubleshooting with this guide
3. **Level 3**: Contact system administrators
4. **Level 4**: Engage development team

---

This troubleshooting guide covers the most common issues and provides systematic approaches to diagnosis and resolution. Regular monitoring and proactive maintenance help prevent many issues from occurring.

## 📋 Quick Reference

### Essential Commands
```bash
# Start platform
./scripts/docker/start-certrats.sh dev

# Check status
./scripts/docker/status-certrats.sh dev --detailed

# Troubleshoot issues
./scripts/docker/troubleshoot-certrats.sh dev --fix

# Stop platform
./scripts/docker/stop-certrats.sh dev
```

### Emergency Contacts
- **System Admin**: <EMAIL>
- **Development Team**: <EMAIL>
- **Security Team**: <EMAIL>
