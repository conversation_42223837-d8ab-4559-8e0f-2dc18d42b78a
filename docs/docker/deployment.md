# CertRats Platform Deployment Guide

This guide provides comprehensive instructions for deploying the CertRats certification platform across different environments using Docker and Traefik.

## 🎯 Deployment Overview

The CertRats platform supports three deployment environments:
- **Development**: Local development with hot reload and debugging tools
- **Staging**: Production-like environment for testing and validation
- **Production**: Optimized for performance, security, and scalability

## 🔧 Prerequisites

### System Requirements

#### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **Network**: 100Mbps

#### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Network**: 1Gbps

### Software Dependencies

```bash
# Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Python 3.9+ (for management scripts)
sudo apt update
sudo apt install python3 python3-pip

# Git
sudo apt install git

# Additional tools
sudo apt install curl jq nc
```

## 🚀 Development Deployment

### Quick Start

1. **Clone and setup**:
   ```bash
   git clone https://github.com/forkrul/replit-CertPathFinder.git
   cd certrats
   cp .env.example .env
   ```

2. **Configure environment variables**:
   ```bash
   # Edit .env file
   nano .env
   
   # Key variables for development
   CERTRATS_ENV=development
   DEBUG=true
   SECRET_KEY=dev-secret-key-change-in-production
   POSTGRES_PASSWORD=dev_password
   REDIS_PASSWORD=dev_redis_password
   ```

3. **Start development environment**:
   ```bash
   ./scripts/docker/start-certrats.sh dev --verbose
   ```

4. **Verify deployment**:
   ```bash
   ./scripts/docker/status-certrats.sh dev --detailed
   ```

### Development Features

- **Hot Reload**: Code changes automatically reflected
- **Debug Tools**: pgAdmin, Redis Commander, MailHog, Jupyter
- **Exposed Ports**: Direct access to services for debugging
- **Relaxed Security**: Simplified authentication for development

### Development URLs

```
Frontend:           http://app.certrats.docker.localhost
API:               http://api.certrats.docker.localhost
API Docs:          http://api.certrats.docker.localhost/docs
Traefik Dashboard: http://traefik.certrats.docker.localhost
Grafana:           http://grafana.certrats.docker.localhost
Prometheus:        http://metrics.certrats.docker.localhost
pgAdmin:           http://pgadmin.certrats.docker.localhost
Redis Commander:   http://redis.certrats.docker.localhost
MailHog:           http://mail.certrats.docker.localhost
Jupyter:           http://jupyter.certrats.docker.localhost
```

## 🧪 Staging Deployment

### Prerequisites

1. **Domain Setup**:
   ```bash
   # Configure DNS for staging.certrats.dev
   # Point *.staging.certrats.dev to your server IP
   ```

2. **SSL Certificate Setup**:
   ```bash
   # Let's Encrypt will automatically provision certificates
   # Ensure ports 80 and 443 are open
   ```

### Deployment Steps

1. **Server preparation**:
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # Add user to docker group
   sudo usermod -aG docker $USER
   newgrp docker
   ```

2. **Clone and configure**:
   ```bash
   git clone https://github.com/forkrul/replit-CertPathFinder.git
   cd certrats
   cp .env.example .env
   ```

3. **Configure staging environment**:
   ```bash
   # Edit .env for staging
   CERTRATS_ENV=staging
   DEBUG=false
   SECRET_KEY=staging-secret-key-generate-secure-key
   POSTGRES_PASSWORD=staging_secure_password
   REDIS_PASSWORD=staging_redis_secure_password
   LETSENCRYPT_EMAIL=<EMAIL>
   ```

4. **Deploy staging**:
   ```bash
   ./scripts/docker/start-certrats.sh staging --verbose
   ```

5. **Verify deployment**:
   ```bash
   ./scripts/docker/status-certrats.sh staging --detailed
   curl -I https://app.staging.certrats.dev
   ```

### Staging URLs

```
Frontend:    https://app.staging.certrats.dev
API:         https://api.staging.certrats.dev
Grafana:     https://grafana.staging.certrats.dev
Prometheus:  https://metrics.staging.certrats.dev
```

## 🏭 Production Deployment

### Security Preparation

1. **Firewall Configuration**:
   ```bash
   # UFW firewall setup
   sudo ufw default deny incoming
   sudo ufw default allow outgoing
   sudo ufw allow ssh
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   sudo ufw enable
   ```

2. **SSL Certificate Setup**:
   ```bash
   # Configure Cloudflare DNS (recommended)
   # Set up DNS API tokens for automatic certificate renewal
   ```

3. **Backup Configuration**:
   ```bash
   # Set up automated backups
   # Configure S3 or similar for backup storage
   ```

### Production Deployment

1. **Server hardening**:
   ```bash
   # Disable root login
   sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
   sudo systemctl restart ssh
   
   # Update system
   sudo apt update && sudo apt upgrade -y
   sudo apt autoremove -y
   ```

2. **Docker installation**:
   ```bash
   # Install Docker with production settings
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # Configure Docker daemon
   sudo tee /etc/docker/daemon.json > /dev/null <<EOF
   {
     "log-driver": "json-file",
     "log-opts": {
       "max-size": "10m",
       "max-file": "3"
     },
     "storage-driver": "overlay2"
   }
   EOF
   
   sudo systemctl restart docker
   ```

3. **Application deployment**:
   ```bash
   # Clone repository
   git clone https://github.com/forkrul/replit-CertPathFinder.git
   cd certrats
   
   # Configure production environment
   cp .env.example .env
   ```

4. **Production environment configuration**:
   ```bash
   # Generate secure secrets
   SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
   POSTGRES_PASSWORD=$(python3 -c "import secrets; print(secrets.token_urlsafe(16))")
   REDIS_PASSWORD=$(python3 -c "import secrets; print(secrets.token_urlsafe(16))")
   
   # Configure .env
   cat > .env << EOF
   CERTRATS_ENV=production
   DEBUG=false
   SECRET_KEY=$SECRET_KEY
   POSTGRES_PASSWORD=$POSTGRES_PASSWORD
   REDIS_PASSWORD=$REDIS_PASSWORD
   LETSENCRYPT_EMAIL=<EMAIL>
   
   # Production domains
   PRODUCTION_DOMAIN=certrats.com
   
   # Monitoring passwords
   GRAFANA_ADMIN_PASSWORD=$(python3 -c "import secrets; print(secrets.token_urlsafe(16))")
   FLOWER_PASSWORD=$(python3 -c "import secrets; print(secrets.token_urlsafe(16))")
   
   # External services
   OPENAI_API_KEY=your-openai-api-key
   SMTP_HOST=smtp.gmail.com
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-smtp-password
   
   # Backup configuration
   AWS_ACCESS_KEY_ID=your-aws-access-key
   AWS_SECRET_ACCESS_KEY=your-aws-secret-key
   BACKUP_S3_BUCKET=certrats-backups
   EOF
   ```

5. **Deploy production**:
   ```bash
   ./scripts/docker/start-certrats.sh prod
   ```

6. **Verify production deployment**:
   ```bash
   ./scripts/docker/status-certrats.sh prod --detailed
   curl -I https://app.certrats.com
   ```

### Production URLs

```
Frontend:    https://app.certrats.com
API:         https://api.certrats.com
Admin:       https://admin.certrats.com
```

## 🔄 Continuous Deployment

### GitHub Actions Setup

1. **Create deployment workflow**:
   ```yaml
   # .github/workflows/deploy.yml
   name: Deploy CertRats Platform
   
   on:
     push:
       branches: [main]
   
   jobs:
     deploy:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - name: Deploy to production
           run: |
             ssh user@server 'cd /opt/certrats && git pull && ./scripts/docker/start-certrats.sh prod'
   ```

2. **Configure secrets**:
   ```bash
   # Add to GitHub repository secrets
   SSH_PRIVATE_KEY
   SERVER_HOST
   SERVER_USER
   ```

### Blue-Green Deployment

1. **Setup blue-green infrastructure**:
   ```bash
   # Create separate environments
   ./scripts/docker/start-certrats.sh prod-blue
   ./scripts/docker/start-certrats.sh prod-green
   ```

2. **Switch traffic**:
   ```bash
   # Update Traefik routing
   # Switch DNS or load balancer
   ```

## 📊 Monitoring and Alerting

### Prometheus Alerts

```yaml
# alerts.yml
groups:
  - name: certrats
    rules:
      - alert: ServiceDown
        expr: up{job=~"certrats-.*"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "CertRats service is down"
```

### Grafana Dashboards

- Platform overview dashboard
- Service-specific monitoring
- Infrastructure metrics
- Business intelligence

## 🔐 Security Considerations

### SSL/TLS Configuration

- Automatic certificate renewal
- Strong cipher suites
- HSTS headers
- Certificate transparency

### Network Security

- Isolated internal networks
- Service mesh security
- Rate limiting
- DDoS protection

### Data Protection

- Encryption at rest
- Secure backup procedures
- Access control
- Audit logging

## 🚨 Disaster Recovery

### Backup Strategy

1. **Automated daily backups**:
   ```bash
   # Database backups
   0 2 * * * /opt/certrats/scripts/backup/database.sh
   
   # File system backups
   0 3 * * * /opt/certrats/scripts/backup/volumes.sh
   ```

2. **Backup verification**:
   ```bash
   # Test backup integrity
   ./scripts/backup/verify-backups.sh
   ```

### Recovery Procedures

1. **Database recovery**:
   ```bash
   # Restore from backup
   ./scripts/backup/restore-database.sh backup-20231201.sql
   ```

2. **Full system recovery**:
   ```bash
   # Complete system restoration
   ./scripts/backup/restore-system.sh
   ```

## 📈 Performance Optimization

### Resource Scaling

- Horizontal pod autoscaling
- Load balancer configuration
- Database connection pooling
- CDN integration

### Monitoring and Tuning

- Performance metrics collection
- Query optimization
- Cache hit rate monitoring
- Resource utilization tracking

## 🧪 Testing Deployment

### Automated Testing

```bash
# Run deployment tests
python scripts/test-deployment.py --environment staging

# Load testing
./scripts/load-test/run-tests.sh
```

### Manual Verification

1. **Functional testing**
2. **Performance testing**
3. **Security testing**
4. **Backup testing**

---

This deployment guide ensures reliable, secure, and scalable deployment of the CertRats platform across all environments.
