# Relationship Integrity Validation

## Overview
The relationship integrity validation system ensures data consistency across related models in the application. It provides automated checks for:
- Foreign key relationships
- Domain references
- Version integrity
- Soft delete consistency

## Usage

### Basic Validation
To add relationship validation to a model:

```python
from models.validation import RelationshipValidationMixin

class YourModel(Base, RelationshipValidationMixin):
    # ... model definition ...
    
    def validate_all_relationships(self, session):
        validation_results = self.validate_relationships(session)
        # Add custom validations if needed
        return validation_results
```

### Available Validation Methods

#### 1. validate_relationships()
Validates all relationships defined in the model:
- Checks foreign key references
- Validates domain references
- Returns a dictionary of validation results

#### 2. validate_foreign_key()
Validates specific foreign key relationships:
```python
result = self.validate_foreign_key(session, 'organization_id', Organization, self.organization_id)
```

#### 3. validate_domain_reference()
Validates security domain references:
```python
result = self.validate_domain_reference(session, self.domain)
```

#### 4. validate_version_integrity()
Ensures version numbers are sequential and complete.

#### 5. validate_soft_delete_integrity()
Maintains consistency of soft delete status across related records.

## API Integration

The relationship validation is integrated into API endpoints:

1. Health Check Endpoint (`/health`)
   - Provides system-wide relationship validation status
   - Reports any integrity issues found

2. Data Retrieval Endpoints
   - Filter out records with invalid relationships
   - Log validation failures for monitoring

## Example Implementation

```python
# Model Definition
class Certification(Base, SoftDeleteMixin, RelationshipValidationMixin):
    def validate_all_relationships(self, session):
        validation_results = self.validate_relationships(session)
        validation_results.update({
            'version_integrity': self.validate_version_integrity(session),
            'soft_delete_integrity': self.validate_soft_delete_integrity(session),
            'prerequisites_integrity': self.validate_prerequisites(session)
        })
        return validation_results

# API Usage
@router.get("/certifications")
async def get_certifications():
    certifications = db.query(Certification).all()
    valid_certs = [cert for cert in certifications 
                   if all(cert.validate_all_relationships(db).values())]
    return valid_certs
```

## Troubleshooting

### Common Issues

1. Missing Foreign Keys
   - Check if referenced records exist
   - Verify foreign key column values

2. Domain Reference Errors
   - Ensure security domains are properly defined
   - Check for case sensitivity in domain names

3. Version Sequence Gaps
   - Verify version numbers are sequential
   - Check for missing version records

4. Soft Delete Inconsistencies
   - Validate parent-child deletion status
   - Check for orphaned records

### Logging

The validation system includes comprehensive logging:
- Validation failures are logged at ERROR level
- Missing relationships are logged at WARNING level
- Successful validations are logged at INFO level

Monitor these logs to identify and resolve data integrity issues.

## Best Practices

1. Always validate relationships when:
   - Creating new records
   - Updating existing records
   - Before serving data through API endpoints

2. Handle validation failures gracefully:
   - Log detailed error information
   - Return appropriate error responses
   - Filter out invalid records when possible

3. Regular Integrity Checks:
   - Use the health check endpoint to monitor system-wide integrity
   - Schedule periodic validation of all records
   - Address validation issues promptly

4. Custom Validations:
   - Extend the validation system for specific needs
   - Implement domain-specific validation rules
   - Maintain consistent validation patterns
