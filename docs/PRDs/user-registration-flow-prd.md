# User Registration Flow PRD
## Complete Implementation Specification

**Flow:** User Registration  
**Version:** 1.0  
**Date:** 2025-01-19  

---

## 🎯 Flow Overview

**User Journey**: New user discovers platform → Creates account → Email verification → Profile setup → Dashboard access

**Primary Goal**: Enable new users to create accounts quickly and securely while gathering essential profile information for personalized experiences.

## 📊 Flow Diagram

```mermaid
flowchart TD
    A[User visits /register] --> B[Registration Form]
    B --> C{Form Valid?}
    C -->|No| D[Show Validation Errors]
    C -->|Yes| E[Submit Registration]
    D --> B
    E --> F[Create User Account]
    F --> G[Send Verification Email]
    G --> H[Show Verification Message]
    H --> I[User Checks Email]
    I --> J[Click Verification Link]
    J --> K[Verify Email Token]
    K --> L{Token Valid?}
    L -->|No| M[Show Error Message]
    L -->|Yes| N[Activate Account]
    N --> O[Redirect to Profile Setup]
    O --> P[Complete Profile]
    P --> Q[Redirect to Dashboard]
```

## 🔗 API Endpoints

### 1. POST /auth/register
**Purpose**: Create new user account

**Request Body**:
```typescript
interface RegisterRequest {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}
```

**Response**:
```typescript
interface RegisterResponse {
  success: boolean;
  message: string;
  userId?: string;
  verificationRequired: boolean;
}
```

**Validation Rules**:
- Email: Valid format, unique in database
- Password: Min 8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char
- Names: Min 2 chars, max 50 chars, letters only
- Terms: Must be accepted

### 2. POST /auth/verify-email
**Purpose**: Verify email address with token

**Request Body**:
```typescript
interface VerifyEmailRequest {
  token: string;
  email: string;
}
```

**Response**:
```typescript
interface VerifyEmailResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
}
```

### 3. POST /auth/resend-verification
**Purpose**: Resend verification email

**Request Body**:
```typescript
interface ResendVerificationRequest {
  email: string;
}
```

### 4. GET /auth/check-email
**Purpose**: Check if email is available

**Query Parameters**: `email: string`

**Response**:
```typescript
interface EmailCheckResponse {
  available: boolean;
  suggestions?: string[];
}
```

### 5. POST /users/profile/setup
**Purpose**: Complete initial profile setup

**Request Body**:
```typescript
interface ProfileSetupRequest {
  currentRole?: string;
  experienceLevel?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  interests?: string[];
  goals?: string[];
  timezone?: string;
  notifications?: NotificationPreferences;
}
```

## 🎨 UX Components

### 1. RegistrationPage Component
**Location**: `src/pages/RegistrationPage.tsx`

**Features**:
- Multi-step form with progress indicator
- Real-time validation with error messages
- Password strength indicator
- Terms and conditions modal
- Social registration options (future)

**Props**:
```typescript
interface RegistrationPageProps {
  redirectTo?: string;
  referralCode?: string;
}
```

### 2. RegistrationForm Component
**Location**: `src/components/forms/RegistrationForm.tsx`

**Features**:
- Form validation with React Hook Form
- Accessible form controls
- Loading states during submission
- Error handling and display

### 3. EmailVerificationPage Component
**Location**: `src/pages/EmailVerificationPage.tsx`

**Features**:
- Verification status display
- Resend verification option
- Countdown timer for resend
- Success/error state handling

### 4. ProfileSetupWizard Component
**Location**: `src/components/profile/ProfileSetupWizard.tsx`

**Features**:
- Multi-step wizard interface
- Progress tracking
- Skip options for optional fields
- Personalized recommendations

### 5. PasswordStrengthIndicator Component
**Location**: `src/components/forms/PasswordStrengthIndicator.tsx`

**Features**:
- Real-time strength calculation
- Visual strength meter
- Improvement suggestions
- Accessibility announcements

### 6. TermsModal Component
**Location**: `src/components/modals/TermsModal.tsx`

**Features**:
- Scrollable terms content
- Accept/decline actions
- Keyboard navigation
- Focus management

### 7. EmailSuggestions Component
**Location**: `src/components/forms/EmailSuggestions.tsx`

**Features**:
- Email availability checking
- Domain suggestions
- Typo corrections
- Accessibility support

### 8. VerificationResendButton Component
**Location**: `src/components/auth/VerificationResendButton.tsx`

**Features**:
- Cooldown timer
- Loading states
- Success feedback
- Rate limiting display

## 🧪 Playwright Testing

### Test File: `frontend/tests/e2e/registration.spec.ts`

### Test Cases:

#### 1. Successful Registration Flow
```typescript
test('complete registration flow with email verification', async ({ page }) => {
  await page.goto('/register');
  
  // Fill registration form
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'SecurePass123!');
  await page.fill('[data-testid="confirm-password-input"]', 'SecurePass123!');
  await page.fill('[data-testid="first-name-input"]', 'John');
  await page.fill('[data-testid="last-name-input"]', 'Doe');
  await page.check('[data-testid="terms-checkbox"]');
  
  // Submit form
  await page.click('[data-testid="register-button"]');
  
  // Verify success message
  await expect(page.getByText(/verification email sent/i)).toBeVisible();
  
  // Simulate email verification
  await page.goto('/verify-email?token=mock-token&email=<EMAIL>');
  await expect(page.getByText(/email verified successfully/i)).toBeVisible();
  
  // Complete profile setup
  await page.selectOption('[data-testid="experience-level"]', 'intermediate');
  await page.click('[data-testid="complete-setup-button"]');
  
  // Verify redirect to dashboard
  await expect(page).toHaveURL('/dashboard');
});
```

#### 2. Form Validation Testing
```typescript
test('validates registration form inputs', async ({ page }) => {
  await page.goto('/register');
  
  // Test email validation
  await page.fill('[data-testid="email-input"]', 'invalid-email');
  await page.blur('[data-testid="email-input"]');
  await expect(page.getByText(/please enter a valid email/i)).toBeVisible();
  
  // Test password strength
  await page.fill('[data-testid="password-input"]', 'weak');
  await expect(page.getByText(/password too weak/i)).toBeVisible();
  
  // Test password confirmation
  await page.fill('[data-testid="password-input"]', 'SecurePass123!');
  await page.fill('[data-testid="confirm-password-input"]', 'DifferentPass123!');
  await expect(page.getByText(/passwords do not match/i)).toBeVisible();
});
```

#### 3. Email Availability Check
```typescript
test('checks email availability in real-time', async ({ page }) => {
  await page.goto('/register');
  
  // Mock existing email
  await page.route('**/auth/check-email*', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({ available: false, suggestions: ['<EMAIL>'] })
    });
  });
  
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.blur('[data-testid="email-input"]');
  
  await expect(page.getByText(/email already exists/i)).toBeVisible();
  await expect(page.getByText(/<EMAIL>/)).toBeVisible();
});
```

#### 4. Accessibility Testing
```typescript
test('registration form is accessible', async ({ page }) => {
  await page.goto('/register');
  
  // Test keyboard navigation
  await page.keyboard.press('Tab');
  await expect(page.getByTestId('email-input')).toBeFocused();
  
  // Test form labels
  await expect(page.getByLabelText(/email address/i)).toBeVisible();
  await expect(page.getByLabelText(/password/i)).toBeVisible();
  
  // Test ARIA attributes
  const emailInput = page.getByTestId('email-input');
  await expect(emailInput).toHaveAttribute('aria-required', 'true');
  
  // Test error announcements
  await page.fill('[data-testid="email-input"]', 'invalid');
  await page.blur('[data-testid="email-input"]');
  
  const errorMessage = page.getByRole('alert');
  await expect(errorMessage).toBeVisible();
});
```

#### 5. Performance Testing
```typescript
test('registration page loads within performance budget', async ({ page }) => {
  const startTime = Date.now();
  
  await page.goto('/register');
  await page.waitForLoadState('networkidle');
  
  const loadTime = Date.now() - startTime;
  expect(loadTime).toBeLessThan(2500); // 2.5s LCP target
  
  // Check form responsiveness
  const inputStartTime = Date.now();
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  const inputTime = Date.now() - inputStartTime;
  
  expect(inputTime).toBeLessThan(100); // Input should be responsive
});
```

## 🎭 BEHAVE Testing

### Feature File: `tests/features/auth/registration.feature`

```gherkin
Feature: User Registration
  As a new user
  I want to create an account on CertRats
  So that I can access the platform and track my certifications

  Background:
    Given I am on the CertRats platform
    And I navigate to the registration page

  @smoke @registration
  Scenario: Successful user registration
    Given I am on the registration page
    When I enter valid registration details:
      | field            | value                |
      | email           | <EMAIL>  |
      | password        | SecurePass123!       |
      | confirmPassword | SecurePass123!       |
      | firstName       | John                 |
      | lastName        | Doe                  |
    And I accept the terms and conditions
    And I click the "Register" button
    Then I should see a verification email message
    And I should receive a verification email
    When I click the verification link in the email
    Then my account should be activated
    And I should be redirected to the profile setup page

  @validation @registration
  Scenario Outline: Registration form validation
    Given I am on the registration page
    When I enter "<field>" as "<value>"
    And I submit the registration form
    Then I should see the error message "<error>"

    Examples:
      | field           | value              | error                        |
      | email          | invalid-email      | Please enter a valid email   |
      | password       | weak               | Password too weak            |
      | confirmPassword| different          | Passwords do not match       |
      | firstName      | J                  | First name too short         |
      | lastName       |                    | Last name is required        |

  @accessibility @registration
  Scenario: Registration form accessibility
    Given I am on the registration page
    When I navigate using only the keyboard
    Then I should be able to access all form fields
    And focus indicators should be clearly visible
    And form labels should be properly associated
    And error messages should be announced to screen readers

  @performance @registration
  Scenario: Registration performance
    Given I am on the registration page
    When I measure the page load time
    Then the page should load within 2.5 seconds
    And form interactions should be responsive
    And the registration process should complete within 5 seconds

  @security @registration
  Scenario: Registration security measures
    Given I am on the registration page
    When I attempt to register with a weak password
    Then the system should reject the password
    And provide password strength guidance
    When I attempt to register with an existing email
    Then the system should prevent duplicate registration
    And suggest alternative email addresses
```

### Step Definitions: `tests/steps/registration_steps.py`

```python
@when('I enter valid registration details')
def step_enter_registration_details(context):
    for row in context.table:
        field = row['field']
        value = row['value']
        
        if context.use_playwright:
            context.page.fill(f'[data-testid="{field.lower()}-input"]', value)
        else:
            element = context.driver.find_element(By.CSS_SELECTOR, f'[data-testid="{field.lower()}-input"]')
            element.send_keys(value)

@when('I accept the terms and conditions')
def step_accept_terms(context):
    if context.use_playwright:
        context.page.check('[data-testid="terms-checkbox"]')
    else:
        checkbox = context.driver.find_element(By.CSS_SELECTOR, '[data-testid="terms-checkbox"]')
        checkbox.click()

@then('I should see a verification email message')
def step_see_verification_message(context):
    if context.use_playwright:
        expect(context.page.get_by_text(/verification email sent/i)).to_be_visible()
    else:
        message = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'verification email sent')]"))
        )
        assert message.is_displayed()
```

## ♿ Accessibility Testing

### Accessibility Requirements:
- **WCAG 2.1 AA Compliance**: All form elements meet accessibility standards
- **Keyboard Navigation**: Complete keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and announcements
- **Color Contrast**: Minimum 4.5:1 contrast ratio
- **Focus Management**: Clear focus indicators and logical tab order
- **Error Handling**: Accessible error messages with proper ARIA attributes

### Accessibility Test Cases:
1. **Keyboard Navigation Test**: Verify all interactive elements are keyboard accessible
2. **Screen Reader Test**: Ensure proper announcements and labels
3. **Color Contrast Test**: Verify sufficient contrast ratios
4. **Focus Management Test**: Check focus indicators and tab order
5. **Error Accessibility Test**: Verify error messages are properly announced

## 📊 Success Metrics

### Functional Metrics:
- **Registration Completion Rate**: >85%
- **Email Verification Rate**: >90%
- **Profile Setup Completion**: >75%
- **Form Validation Accuracy**: 100%

### Performance Metrics:
- **Page Load Time**: <2.5s LCP
- **Form Responsiveness**: <100ms input delay
- **API Response Time**: <200ms average

### Accessibility Metrics:
- **WCAG 2.1 AA Compliance**: 100%
- **Keyboard Navigation**: 100% accessible
- **Screen Reader Compatibility**: Full support

This comprehensive specification ensures the User Registration Flow is implemented with complete API coverage, modern UX design, comprehensive testing, and full accessibility compliance.
