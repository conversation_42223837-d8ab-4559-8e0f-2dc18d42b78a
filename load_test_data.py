#!/usr/bin/env python3
"""
Load a few test certifications to verify the API works
"""
import os
import sys
from pathlib import Path

# Force SQLite database
os.environ['DATABASE_URL'] = 'sqlite:///./certpathfinder.db'

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import init_db, get_db
from models.certification import Certification, Organization
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data():
    """Create a few test certifications"""
    logger.info("Creating test certification data...")
    
    # Initialize database
    init_db()
    db = next(get_db())
    
    try:
        # Clear existing data
        db.query(Certification).delete()
        db.query(Organization).delete()
        db.commit()
        
        # Create test organizations
        orgs = [
            Organization(name="CompTIA", description="Computing Technology Industry Association", website="https://www.comptia.org"),
            Organization(name="Amazon Web Services", description="Amazon Web Services", website="https://aws.amazon.com"),
            Organization(name="Microsoft", description="Microsoft Corporation", website="https://www.microsoft.com"),
            Organization(name="Cisco", description="Cisco Systems", website="https://www.cisco.com"),
            Organization(name="ISC2", description="International Information System Security Certification Consortium", website="https://www.isc2.org"),
        ]
        
        for org in orgs:
            db.add(org)
        db.commit()
        
        # Create test certifications
        certs = [
            Certification(
                name="CompTIA Security+",
                description="CompTIA Security+ is a global certification that validates the baseline skills necessary to perform core security functions and pursue an IT security career.",
                level="Associate",
                difficulty=2,
                focus="Technical",
                domain="Cybersecurity",
                category="Security",
                organization_id=1,  # CompTIA
                cost=370.0,
                currency="USD",
                exam_code="SY0-701",
                validity_period=36,
                url="https://www.comptia.org/certifications/security",
                is_active=True,
                is_deleted=False
            ),
            Certification(
                name="AWS Certified Solutions Architect - Associate",
                description="This certification validates the ability to design and deploy scalable, highly available, and fault-tolerant systems on AWS.",
                level="Associate",
                difficulty=3,
                focus="Technical",
                domain="Cloud Computing",
                category="Cloud",
                organization_id=2,  # AWS
                cost=150.0,
                currency="USD",
                exam_code="SAA-C03",
                validity_period=36,
                url="https://aws.amazon.com/certification/certified-solutions-architect-associate/",
                is_active=True,
                is_deleted=False
            ),
            Certification(
                name="Microsoft Azure Security Engineer Associate",
                description="Candidates for this exam should have subject matter expertise implementing security controls and threat protection, managing identity and access, and protecting data, applications, and networks in cloud and hybrid environments.",
                level="Associate",
                difficulty=3,
                focus="Technical",
                domain="Cloud Security",
                category="Cloud Security",
                organization_id=3,  # Microsoft
                cost=165.0,
                currency="USD",
                exam_code="AZ-500",
                validity_period=24,
                url="https://docs.microsoft.com/en-us/learn/certifications/azure-security-engineer/",
                is_active=True,
                is_deleted=False
            ),
            Certification(
                name="Cisco Certified Network Associate (CCNA)",
                description="CCNA certification demonstrates your skills in network fundamentals, network access, IP connectivity, IP services, security fundamentals, and automation and programmability.",
                level="Associate",
                difficulty=2,
                focus="Technical",
                domain="Network Security",
                category="Networking",
                organization_id=4,  # Cisco
                cost=300.0,
                currency="USD",
                exam_code="200-301",
                validity_period=36,
                url="https://www.cisco.com/c/en/us/training-events/training-certifications/certifications/associate/ccna.html",
                is_active=True,
                is_deleted=False
            ),
            Certification(
                name="Certified Information Systems Security Professional (CISSP)",
                description="The CISSP is ideal for experienced security practitioners, managers and executives interested in proving their knowledge across a wide array of security practices and principles.",
                level="Expert",
                difficulty=5,
                focus="Management",
                domain="Information Security",
                category="Security Management",
                organization_id=5,  # ISC2
                cost=749.0,
                currency="USD",
                exam_code="CISSP",
                validity_period=36,
                url="https://www.isc2.org/Certifications/CISSP",
                is_active=True,
                is_deleted=False
            ),
            Certification(
                name="CompTIA Network+",
                description="CompTIA Network+ validates the technical skills needed to securely establish, maintain and troubleshoot the essential networks that businesses rely on.",
                level="Associate",
                difficulty=2,
                focus="Technical",
                domain="Network Security",
                category="Networking",
                organization_id=1,  # CompTIA
                cost=370.0,
                currency="USD",
                exam_code="N10-008",
                validity_period=36,
                url="https://www.comptia.org/certifications/network",
                is_active=True,
                is_deleted=False
            ),
            Certification(
                name="AWS Certified Cloud Practitioner",
                description="This certification validates foundational, high-level understanding of AWS Cloud, services, and terminology.",
                level="Beginner",
                difficulty=1,
                focus="Technical",
                domain="Cloud Computing",
                category="Cloud",
                organization_id=2,  # AWS
                cost=100.0,
                currency="USD",
                exam_code="CLF-C01",
                validity_period=36,
                url="https://aws.amazon.com/certification/certified-cloud-practitioner/",
                is_active=True,
                is_deleted=False
            ),
            Certification(
                name="Microsoft Azure Fundamentals",
                description="This certification validates foundational knowledge of cloud services and how those services are provided with Microsoft Azure.",
                level="Beginner",
                difficulty=1,
                focus="Technical",
                domain="Cloud Computing",
                category="Cloud",
                organization_id=3,  # Microsoft
                cost=99.0,
                currency="USD",
                exam_code="AZ-900",
                validity_period=24,
                url="https://docs.microsoft.com/en-us/learn/certifications/azure-fundamentals/",
                is_active=True,
                is_deleted=False
            ),
        ]
        
        for cert in certs:
            db.add(cert)
        db.commit()
        
        logger.info(f"Successfully created {len(certs)} test certifications")
        return len(certs)
        
    except Exception as e:
        logger.error(f"Error creating test data: {str(e)}")
        db.rollback()
        return 0
    finally:
        db.close()

if __name__ == "__main__":
    count = create_test_data()
    logger.info(f"Test data creation complete. Created {count} certifications")
