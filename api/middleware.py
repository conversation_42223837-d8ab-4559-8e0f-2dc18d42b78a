"""
FastAPI middleware for request/response processing.

This module provides comprehensive middleware for logging, authentication,
rate limiting, CORS, and other cross-cutting concerns.
"""

import time
import uuid
import json
from typing import Callable, Dict, Any, Optional
from fastapi import Fast<PERSON><PERSON>, Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from starlette.middleware.sessions import SessionMiddleware
from starlette.responses import JSONResponse
import logging
from utils.logging_config import log_api_request, log_security_event, log_performance_metric

logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for comprehensive request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log details."""
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Extract user information if available
        user_id = getattr(request.state, 'user_id', None)
        
        # Log request start
        start_time = time.time()
        
        logger.info(
            f"Request started: {request.method} {request.url.path}",
            extra={
                'request_id': request_id,
                'method': request.method,
                'path': request.url.path,
                'query_params': str(request.query_params),
                'user_agent': request.headers.get('user-agent'),
                'client_ip': request.client.host,
                'user_id': user_id
            }
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log successful response
            logger.info(
                f"Request completed: {request.method} {request.url.path}",
                extra={
                    'request_id': request_id,
                    'method': request.method,
                    'path': request.url.path,
                    'status_code': response.status_code,
                    'process_time': process_time,
                    'user_id': user_id
                }
            )
            
            # Log performance metric
            log_performance_metric(
                'request_duration',
                process_time,
                {
                    'method': request.method,
                    'path': request.url.path,
                    'status_code': str(response.status_code)
                }
            )
            
            # Add response headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # Calculate processing time for error case
            process_time = time.time() - start_time
            
            # Log error
            logger.error(
                f"Request failed: {request.method} {request.url.path}",
                extra={
                    'request_id': request_id,
                    'method': request.method,
                    'path': request.url.path,
                    'error': str(e),
                    'process_time': process_time,
                    'user_id': user_id
                },
                exc_info=True
            )
            
            # Return error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "request_id": request_id
                },
                headers={"X-Request-ID": request_id}
            )

class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware for security headers and monitoring."""
    
    def __init__(self, app: FastAPI, allowed_hosts: list = None):
        super().__init__(app)
        self.allowed_hosts = allowed_hosts or ["*"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers and monitor for suspicious activity."""
        
        # Check allowed hosts
        if self.allowed_hosts != ["*"]:
            host = request.headers.get("host")
            if host not in self.allowed_hosts:
                log_security_event(
                    "invalid_host",
                    {
                        "host": host,
                        "client_ip": request.client.host,
                        "user_agent": request.headers.get("user-agent")
                    },
                    "WARNING"
                )
                return JSONResponse(
                    status_code=403,
                    content={"error": "Forbidden"}
                )
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )
        
        return response

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple in-memory rate limiting middleware."""
    
    def __init__(self, app: FastAPI, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.request_counts: Dict[str, Dict[str, Any]] = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting based on client IP."""
        client_ip = request.client.host
        current_time = time.time()
        
        # Clean old entries
        self._cleanup_old_entries(current_time)
        
        # Check rate limit
        if client_ip in self.request_counts:
            client_data = self.request_counts[client_ip]
            if current_time - client_data["window_start"] < 60:  # 1 minute window
                if client_data["count"] >= self.requests_per_minute:
                    log_security_event(
                        "rate_limit_exceeded",
                        {
                            "client_ip": client_ip,
                            "requests_count": client_data["count"],
                            "user_agent": request.headers.get("user-agent")
                        },
                        "WARNING"
                    )
                    return JSONResponse(
                        status_code=429,
                        content={"error": "Rate limit exceeded"},
                        headers={"Retry-After": "60"}
                    )
                else:
                    client_data["count"] += 1
            else:
                # Reset window
                self.request_counts[client_ip] = {
                    "count": 1,
                    "window_start": current_time
                }
        else:
            # First request from this IP
            self.request_counts[client_ip] = {
                "count": 1,
                "window_start": current_time
            }
        
        return await call_next(request)
    
    def _cleanup_old_entries(self, current_time: float):
        """Remove old rate limit entries."""
        expired_ips = [
            ip for ip, data in self.request_counts.items()
            if current_time - data["window_start"] > 300  # 5 minutes
        ]
        for ip in expired_ips:
            del self.request_counts[ip]

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for centralized error handling."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Handle errors and return appropriate responses."""
        try:
            return await call_next(request)
        except ValueError as e:
            logger.warning(f"Validation error: {str(e)}")
            return JSONResponse(
                status_code=400,
                content={"error": "Bad request", "detail": str(e)}
            )
        except PermissionError as e:
            logger.warning(f"Permission error: {str(e)}")
            return JSONResponse(
                status_code=403,
                content={"error": "Forbidden", "detail": str(e)}
            )
        except FileNotFoundError as e:
            logger.warning(f"Not found error: {str(e)}")
            return JSONResponse(
                status_code=404,
                content={"error": "Not found", "detail": str(e)}
            )
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}", exc_info=True)
            return JSONResponse(
                status_code=500,
                content={"error": "Internal server error"}
            )

def setup_middleware(app: FastAPI, settings) -> None:
    """Set up all middleware for the FastAPI application."""

    # Import caching middleware
    try:
        from api.middleware.cache import CacheMiddleware, CacheInvalidationMiddleware
        cache_available = True
    except ImportError:
        cache_available = False
        logger.warning("Cache middleware not available - Redis may not be configured")

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )

    # Add compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # Add session middleware
    app.add_middleware(
        SessionMiddleware,
        secret_key=settings.SECRET_KEY
    )

    # Add caching middleware if available
    if cache_available and settings.REDIS_URL:
        app.add_middleware(
            CacheInvalidationMiddleware,
            invalidation_patterns={
                "/api/v1/certifications/": ["api_cache:GET:/api/v1/certifications/*"],
                "/api/v1/domains/": ["api_cache:GET:/api/v1/domains/*"],
                "/api/v1/jobs/": ["api_cache:GET:/api/v1/jobs/*"],
                "/api/v1/cost-calculator/": ["api_cache:GET:/api/v1/cost-calculator/*"]
            }
        )
        app.add_middleware(
            CacheMiddleware,
            default_ttl=settings.REDIS_TTL,
            cacheable_methods=["GET"],
            include_paths=[
                "/api/v1/certifications/",
                "/api/v1/domains/",
                "/api/v1/jobs/",
                "/api/v1/cost-calculator/"
            ],
            exclude_paths=[
                "/docs", "/redoc", "/openapi.json", "/health",
                "/api/v1/user/", "/api/v1/auth/", "/api/v1/progress/"
            ]
        )
        logger.info("Cache middleware configured successfully")

    # Add custom middleware (order matters - last added is executed first)
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(SecurityMiddleware, allowed_hosts=["*"])  # Configure as needed

    # Add rate limiting if enabled
    if hasattr(settings, 'RATE_LIMIT_PER_MINUTE') and settings.RATE_LIMIT_PER_MINUTE:
        app.add_middleware(
            RateLimitMiddleware,
            requests_per_minute=settings.RATE_LIMIT_PER_MINUTE
        )

    app.add_middleware(RequestLoggingMiddleware)

    logger.info("All middleware configured successfully")

# Utility functions for middleware
def get_client_ip(request: Request) -> str:
    """Get the real client IP address."""
    # Check for forwarded headers (when behind proxy)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    return request.client.host

def is_internal_request(request: Request) -> bool:
    """Check if request is from internal network."""
    client_ip = get_client_ip(request)
    internal_ranges = [
        "127.0.0.1",
        "localhost",
        "10.",
        "192.168.",
        "172.16.",
        "172.17.",
        "172.18.",
        "172.19.",
        "172.20.",
        "172.21.",
        "172.22.",
        "172.23.",
        "172.24.",
        "172.25.",
        "172.26.",
        "172.27.",
        "172.28.",
        "172.29.",
        "172.30.",
        "172.31."
    ]
    
    return any(client_ip.startswith(range_) for range_ in internal_ranges)
