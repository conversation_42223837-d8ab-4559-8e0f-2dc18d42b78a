"""
Skills Assessment API endpoints for algorithmic skills gap analysis
Feature 1.1: Skills Vector Representation & Scoring
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field, validator
from sqlalchemy.orm import Session
from database import get_db
from models.user import User
from api.deps import get_current_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/skills", tags=["Skills Assessment"])

# Skill level enumeration
SKILL_LEVELS = {
    'none': 0.0,
    'basic': 0.25,
    'intermediate': 0.5,
    'advanced': 0.75,
    'expert': 1.0
}

# Confidence weighting
CONFIDENCE_WEIGHTS = {
    'very_confident': 1.0,
    'confident': 0.8,
    'somewhat_confident': 0.6,
    'not_confident': 0.4
}

# 8-Domain Cybersecurity Framework
CYBERSECURITY_DOMAINS = {
    'communication_network_security': {
        'name': 'Communication and Network Security',
        'skills': [
            'network_protocols', 'firewall_configuration', 'vpn_technologies',
            'network_monitoring', 'cloud_network_security', 'wireless_security',
            'network_architecture', 'intrusion_detection', 'packet_analysis'
        ]
    },
    'identity_access_management': {
        'name': 'Identity and Access Management (IAM)',
        'skills': [
            'authentication_mechanisms', 'authorization_rbac', 'privileged_access_management',
            'identity_governance', 'zero_trust_principles', 'single_sign_on',
            'multi_factor_authentication', 'identity_federation', 'access_controls'
        ]
    },
    'security_architecture_engineering': {
        'name': 'Security Architecture and Engineering',
        'skills': [
            'security_frameworks', 'threat_modeling', 'secure_system_design',
            'risk_assessment', 'security_controls', 'security_patterns',
            'defense_in_depth', 'security_by_design', 'architecture_review'
        ]
    },
    'asset_security': {
        'name': 'Asset Security',
        'skills': [
            'data_classification', 'data_loss_prevention', 'endpoint_security',
            'asset_inventory', 'privacy_compliance', 'data_governance',
            'information_lifecycle', 'data_retention', 'asset_management'
        ]
    },
    'security_risk_management': {
        'name': 'Security and Risk Management',
        'skills': [
            'governance_frameworks', 'risk_assessment_methodologies', 'compliance_management',
            'policy_development', 'business_continuity', 'disaster_recovery',
            'risk_mitigation', 'regulatory_compliance', 'security_governance'
        ]
    },
    'security_assessment_testing': {
        'name': 'Security Assessment and Testing',
        'skills': [
            'vulnerability_assessment', 'penetration_testing', 'security_auditing',
            'red_team_operations', 'security_metrics', 'code_review',
            'security_testing', 'compliance_auditing', 'threat_hunting'
        ]
    },
    'software_security': {
        'name': 'Software Security',
        'skills': [
            'secure_coding_practices', 'application_security_testing', 'devsecops',
            'api_security', 'software_supply_chain', 'code_analysis',
            'secure_development_lifecycle', 'application_hardening', 'container_security'
        ]
    },
    'security_operations': {
        'name': 'Security Operations',
        'skills': [
            'incident_response', 'siem_soar', 'threat_hunting_techniques',
            'digital_forensics', 'threat_intelligence', 'security_monitoring',
            'log_analysis', 'malware_analysis', 'security_orchestration'
        ]
    }
}

# Pydantic models
class SkillAssessmentItem(BaseModel):
    """Individual skill assessment item"""
    skill_name: str = Field(..., description="Name of the skill")
    level: str = Field(..., description="Skill level: none, basic, intermediate, advanced, expert")
    confidence: str = Field(..., description="Confidence level: very_confident, confident, somewhat_confident, not_confident")
    
    @validator('level')
    def validate_level(cls, v):
        if v not in SKILL_LEVELS:
            raise ValueError(f"Level must be one of: {list(SKILL_LEVELS.keys())}")
        return v
    
    @validator('confidence')
    def validate_confidence(cls, v):
        if v not in CONFIDENCE_WEIGHTS:
            raise ValueError(f"Confidence must be one of: {list(CONFIDENCE_WEIGHTS.keys())}")
        return v

class CertificationItem(BaseModel):
    """Certification item for skill inference"""
    name: str = Field(..., description="Certification name")
    provider: str = Field(..., description="Certification provider")
    year_obtained: Optional[int] = Field(None, description="Year certification was obtained")

class SkillAssessmentRequest(BaseModel):
    """Skills assessment request model"""
    user_id: int = Field(..., description="User ID")
    skills: List[SkillAssessmentItem] = Field(..., description="List of skill assessments")
    certifications: Optional[List[CertificationItem]] = Field(default=[], description="User certifications")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": 1,
                "skills": [
                    {
                        "skill_name": "network_protocols",
                        "level": "intermediate",
                        "confidence": "confident"
                    },
                    {
                        "skill_name": "incident_response",
                        "level": "advanced",
                        "confidence": "very_confident"
                    }
                ],
                "certifications": [
                    {
                        "name": "Security+",
                        "provider": "CompTIA",
                        "year_obtained": 2023
                    }
                ]
            }
        }

class SkillScore(BaseModel):
    """Individual skill score"""
    skill_name: str
    raw_score: float
    confidence_weighted_score: float
    certification_boost: float
    final_score: float

class SkillAssessmentResponse(BaseModel):
    """Skills assessment response model"""
    assessment_id: str
    user_id: int
    skill_scores: List[SkillScore]
    domain_scores: Dict[str, float]
    overall_profile: Dict[str, Any]
    timestamp: datetime

class UserSkillProfile(BaseModel):
    """User skill profile model"""
    user_id: int
    skill_vector: Dict[str, float]
    domain_scores: Dict[str, float]
    last_updated: datetime
    confidence_scores: Dict[str, float]

# Helper functions
def calculate_weighted_skill_score(skill_level: str, confidence: str, certifications: List[str] = None) -> Dict[str, float]:
    """Calculate final skill score with confidence weighting and certification boost"""
    base_score = SKILL_LEVELS[skill_level]
    confidence_weight = CONFIDENCE_WEIGHTS[confidence]
    
    # Calculate certification boost
    cert_boost = 0.0
    if certifications:
        # Simple boost based on number of relevant certifications
        cert_boost = min(0.3, len(certifications) * 0.1)
    
    # Apply confidence weighting and certification boost
    confidence_weighted = base_score * confidence_weight
    final_score = min(1.0, confidence_weighted + cert_boost)
    
    return {
        'raw_score': base_score,
        'confidence_weighted_score': confidence_weighted,
        'certification_boost': cert_boost,
        'final_score': final_score
    }

def calculate_domain_scores(skill_scores: Dict[str, float]) -> Dict[str, float]:
    """Calculate domain-level scores from individual skill scores"""
    domain_scores = {}
    
    for domain_id, domain_info in CYBERSECURITY_DOMAINS.items():
        domain_skills = domain_info['skills']
        relevant_scores = [skill_scores.get(skill, 0.0) for skill in domain_skills if skill in skill_scores]
        
        if relevant_scores:
            domain_scores[domain_id] = sum(relevant_scores) / len(relevant_scores)
        else:
            domain_scores[domain_id] = 0.0
    
    return domain_scores

# API Endpoints
@router.post("/assess", response_model=SkillAssessmentResponse)
async def assess_skills(
    request: SkillAssessmentRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Assess user skills and generate skill vector representation
    
    This endpoint processes user skill assessments and generates:
    - Individual skill scores with confidence weighting
    - Domain-level aggregated scores
    - Overall skill profile for recommendations
    """
    try:
        logger.info(f"Processing skills assessment for user {request.user_id}")
        
        # Verify user exists and has permission
        if current_user.id != request.user_id and current_user.role.value != 'admin':
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to assess skills for this user"
            )
        
        # Process skill assessments
        skill_scores = []
        skill_vector = {}
        confidence_scores = {}
        
        # Get user certifications for boost calculation
        user_certs = [cert.name for cert in request.certifications]
        
        for skill_item in request.skills:
            # Calculate weighted score
            score_data = calculate_weighted_skill_score(
                skill_item.level,
                skill_item.confidence,
                user_certs
            )
            
            # Create skill score object
            skill_score = SkillScore(
                skill_name=skill_item.skill_name,
                raw_score=score_data['raw_score'],
                confidence_weighted_score=score_data['confidence_weighted_score'],
                certification_boost=score_data['certification_boost'],
                final_score=score_data['final_score']
            )
            
            skill_scores.append(skill_score)
            skill_vector[skill_item.skill_name] = score_data['final_score']
            confidence_scores[skill_item.skill_name] = CONFIDENCE_WEIGHTS[skill_item.confidence]
        
        # Calculate domain scores
        domain_scores = calculate_domain_scores(skill_vector)
        
        # Generate assessment ID
        assessment_id = f"assess_{request.user_id}_{int(datetime.utcnow().timestamp())}"
        
        # Create overall profile
        overall_profile = {
            'total_skills_assessed': len(skill_scores),
            'average_skill_level': sum(skill_vector.values()) / len(skill_vector) if skill_vector else 0.0,
            'strongest_domain': max(domain_scores.items(), key=lambda x: x[1])[0] if domain_scores else None,
            'certifications_count': len(request.certifications),
            'assessment_completeness': len(skill_vector) / sum(len(domain['skills']) for domain in CYBERSECURITY_DOMAINS.values())
        }
        
        # TODO: Store assessment in database (will be implemented in integration tests phase)
        
        response = SkillAssessmentResponse(
            assessment_id=assessment_id,
            user_id=request.user_id,
            skill_scores=skill_scores,
            domain_scores=domain_scores,
            overall_profile=overall_profile,
            timestamp=datetime.utcnow()
        )
        
        logger.info(f"Successfully processed skills assessment {assessment_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing skills assessment: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing skills assessment"
        )

@router.get("/profile/{user_id}", response_model=UserSkillProfile)
async def get_user_skill_profile(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user's current skill profile
    
    Returns the user's latest skill vector and domain scores
    """
    try:
        # Verify user has permission
        if current_user.id != user_id and current_user.role.value != 'admin':
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to view this user's profile"
            )
        
        # TODO: Retrieve from database (will be implemented in integration tests phase)
        # For now, return a placeholder response
        
        logger.info(f"Retrieved skill profile for user {user_id}")
        return UserSkillProfile(
            user_id=user_id,
            skill_vector={},
            domain_scores={},
            last_updated=datetime.utcnow(),
            confidence_scores={}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving skill profile: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving skill profile"
        )

@router.put("/profile/{user_id}", response_model=Dict[str, Any])
async def update_user_skill_profile(
    user_id: int,
    skills: List[SkillAssessmentItem],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update user's skill profile
    
    Updates specific skills in the user's profile
    """
    try:
        # Verify user has permission
        if current_user.id != user_id and current_user.role.value != 'admin':
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this user's profile"
            )
        
        # TODO: Update database (will be implemented in integration tests phase)
        
        logger.info(f"Updated skill profile for user {user_id}")
        return {
            "message": "Skill profile updated successfully",
            "user_id": user_id,
            "updated_skills": len(skills),
            "timestamp": datetime.utcnow()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating skill profile: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating skill profile"
        )

@router.get("/domains", response_model=Dict[str, Any])
async def get_skill_domains():
    """
    Get available skill domains and their associated skills
    
    Returns the 8-domain cybersecurity framework with all available skills
    """
    try:
        return {
            "domains": CYBERSECURITY_DOMAINS,
            "total_domains": len(CYBERSECURITY_DOMAINS),
            "total_skills": sum(len(domain['skills']) for domain in CYBERSECURITY_DOMAINS.values())
        }
    except Exception as e:
        logger.error(f"Error retrieving skill domains: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving skill domains"
        )
