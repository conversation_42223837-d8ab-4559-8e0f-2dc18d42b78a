"""FastAPI application for certification path creator functionality"""
import sys
from pathlib import Path
import logging
from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from datetime import datetime
from sqlalchemy import text
import traceback

# Add parent directory to path if needed
parent_dir = str(Path(__file__).parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from api.config import settings, logger
from database import init_db, get_db
from api.routes import router as api_router
from api.v1.job_search import router as job_search_router
from api.middleware import setup_middleware
from utils.logging_config import setup_logging, get_logger

# Set up comprehensive logging
setup_logging(
    log_level="INFO",
    log_format="json" if settings.is_production else "text",
    enable_console=True
)
logger = get_logger(__name__)

def create_app():
    """Create and configure the FastAPI application"""
    # Create FastAPI app without overriding docs settings from kwargs
    kwargs = settings.fastapi_kwargs.copy()
    kwargs.pop('docs_url', None)
    kwargs.pop('redoc_url', None)
    kwargs.pop('openapi_url', None)

    app = FastAPI(
        **kwargs,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )

    # Set up all middleware (includes CORS, logging, security, etc.)
    setup_middleware(app, settings)

    # Include API router with prefix
    app.include_router(api_router, prefix=settings.API_V1_PREFIX)
    # Include job search router
    app.include_router(job_search_router, prefix=f"{settings.API_V1_PREFIX}/jobs", tags=["jobs"])

    @app.on_event("startup")
    async def startup_event():
        """Initialize services on startup"""
        logger.info("Starting FastAPI application")
        logger.info(f"API will be available at: http://{settings.HOST}:{settings.PORT}/api/v1")
        logger.info(f"Swagger docs will be available at: http://{settings.HOST}:{settings.PORT}/docs")

        if not settings.DATABASE_URL:
            logger.error("DATABASE_URL environment variable is not set")
            raise RuntimeError("Database configuration missing")

        logger.info(f"Using database at {settings.DATABASE_URL.split('@')[-1]}")

        try:
            # Initialize database
            logger.info("Initializing database connection...")
            init_db()

            # Test database connection
            db = next(get_db())
            try:
                db.execute(text("SELECT 1"))
                logger.info("Database connection test successful")
            except Exception as e:
                logger.error(f"Database connection test failed: {str(e)}")
                logger.error(traceback.format_exc())
                raise
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Critical startup error: {str(e)}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"Critical startup error: {str(e)}")

    @app.get("/")
    async def root():
        """Root endpoint that provides API information"""
        return {
            "message": "Security Certification Path Creator API",
            "version": settings.VERSION,
            "documentation_url": "/docs"
        }

    @app.get("/health")
    async def health_check():
        """Enhanced health check endpoint with detailed service status"""
        try:
            health_status = {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "version": settings.VERSION,
                "services": {
                    "api": {
                        "status": "up",
                        "port": settings.PORT
                    }
                }
            }

            # Test database connection
            db = next(get_db())
            try:
                db.execute(text("SELECT 1"))
                health_status["services"]["database"] = {
                    "status": "connected",
                    "last_checked": datetime.utcnow().isoformat()
                }
            except Exception as e:
                logger.error(f"Health check - Database error: {str(e)}")
                health_status["services"]["database"] = {
                    "status": "error",
                    "error": str(e),
                    "last_checked": datetime.utcnow().isoformat()
                }
                health_status["status"] = "degraded"
            finally:
                db.close()

            # Check if job search endpoints are registered
            job_search_endpoints = [
                route for route in app.routes 
                if route.path.startswith(f"{settings.API_V1_PREFIX}/jobs")
                and route.path != f"{settings.API_V1_PREFIX}/jobs/health"
            ]

            health_status["services"]["job_search"] = {
                "status": "ready" if job_search_endpoints else "not_configured",
                "last_checked": datetime.utcnow().isoformat(),
                "endpoints": len(job_search_endpoints) if job_search_endpoints else 0,
                "routes": [str(route.path) for route in job_search_endpoints] if job_search_endpoints else []
            }

            logger.info(f"Health check response: {health_status}")
            return health_status

        except Exception as e:
            error_msg = f"Health check failed: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )

    return app

# Create the FastAPI application instance
app = create_app()