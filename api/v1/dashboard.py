"""Dashboard API endpoints"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
import logging

from database import get_db
from api.v1.auth import get_current_user
from models.user import User
from models.certification import Certification
from models.learning_path import LearningPath
from models.progress_tracking import ProgressTracking

# Dashboard request/response schemas
class QuickActionRequest(BaseModel):
    action: str  # 'add_to_path', 'start_study', 'mark_complete', 'set_goal'
    certification_id: Optional[str] = None
    learning_path_id: Optional[str] = None
    goal_data: Optional[Dict[str, Any]] = None

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/dashboard", tags=["Dashboard"])


@router.get("/overview")
async def get_dashboard_overview(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get dashboard overview data"""
    try:
        # Get user's learning paths
        learning_paths = db.query(LearningPath).filter(
            LearningPath.user_id == current_user.id
        ).all()
        
        # Get user's progress
        progress_records = db.query(ProgressTracking).filter(
            ProgressTracking.user_id == current_user.id
        ).all()
        
        # Get recommended certifications
        recommended_certs = db.query(Certification).limit(5).all()
        
        # Calculate statistics
        total_certifications = len([p for p in progress_records if p.status == 'completed'])
        in_progress = len([p for p in progress_records if p.status == 'in_progress'])
        total_paths = len(learning_paths)
        
        # Calculate completion percentage
        if progress_records:
            completed_count = len([p for p in progress_records if p.status == 'completed'])
            completion_percentage = (completed_count / len(progress_records)) * 100
        else:
            completion_percentage = 0
        
        return {
            "user": {
                "id": current_user.id,
                "name": current_user.full_name,
                "email": current_user.email
            },
            "statistics": {
                "total_certifications": total_certifications,
                "in_progress": in_progress,
                "total_paths": total_paths,
                "completion_percentage": round(completion_percentage, 1)
            },
            "recent_activity": [
                {
                    "id": p.id,
                    "certification_name": p.certification.name if p.certification else "Unknown",
                    "status": p.status,
                    "updated_at": p.updated_at.isoformat() if p.updated_at else None
                }
                for p in progress_records[-5:]  # Last 5 activities
            ],
            "learning_paths": [
                {
                    "id": path.id,
                    "name": path.name,
                    "description": path.description,
                    "progress": path.progress_percentage,
                    "created_at": path.created_at.isoformat() if path.created_at else None
                }
                for path in learning_paths
            ],
            "recommended_certifications": [
                {
                    "id": cert.id,
                    "name": cert.name,
                    "provider": cert.provider,
                    "difficulty": cert.difficulty_level,
                    "estimated_hours": cert.estimated_study_hours
                }
                for cert in recommended_certs
            ]
        }
        
    except Exception as e:
        logger.error(f"Dashboard overview error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dashboard data"
        )


@router.get("/notifications")
async def get_user_notifications(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    limit: int = 10,
    unread_only: bool = False
):
    """Get user notifications"""
    try:
        # This would typically query a notifications table
        # For now, return mock data
        notifications = [
            {
                "id": 1,
                "type": "achievement",
                "title": "Certification Completed!",
                "message": "Congratulations on completing AWS Solutions Architect",
                "read": False,
                "created_at": "2023-12-01T10:00:00Z"
            },
            {
                "id": 2,
                "type": "reminder",
                "title": "Study Reminder",
                "message": "Don't forget to study for your upcoming exam",
                "read": True,
                "created_at": "2023-11-30T09:00:00Z"
            },
            {
                "id": 3,
                "type": "update",
                "title": "New Certification Available",
                "message": "Check out the new Azure DevOps certification path",
                "read": False,
                "created_at": "2023-11-29T14:30:00Z"
            }
        ]
        
        if unread_only:
            notifications = [n for n in notifications if not n["read"]]
        
        return {
            "notifications": notifications[:limit],
            "total_count": len(notifications),
            "unread_count": len([n for n in notifications if not n["read"]])
        }
        
    except Exception as e:
        logger.error(f"Notifications error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load notifications"
        )


@router.post("/notifications/{notification_id}/mark-read")
async def mark_notification_read(
    notification_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mark a notification as read"""
    try:
        # This would typically update the notification in the database
        # For now, return success
        return {"message": "Notification marked as read"}
        
    except Exception as e:
        logger.error(f"Mark notification read error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to mark notification as read"
        )


@router.get("/quick-stats")
async def get_quick_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get quick statistics for dashboard widgets"""
    try:
        # Get user's progress records
        progress_records = db.query(ProgressTracking).filter(
            ProgressTracking.user_id == current_user.id
        ).all()
        
        # Calculate quick stats
        completed_this_month = len([
            p for p in progress_records 
            if p.status == 'completed' and p.updated_at and 
            p.updated_at.month == 12  # Current month (mock)
        ])
        
        study_streak = 7  # Mock data - would calculate actual streak
        next_exam_days = 15  # Mock data - would calculate from user's schedule
        
        return {
            "completed_this_month": completed_this_month,
            "study_streak_days": study_streak,
            "next_exam_in_days": next_exam_days,
            "total_study_hours": sum([
                p.study_hours_logged or 0 for p in progress_records
            ])
        }
        
    except Exception as e:
        logger.error(f"Quick stats error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load quick statistics"
        )


@router.get("/recent-activity")
async def get_recent_activity(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    limit: int = 10
):
    """Get user's recent activity"""
    try:
        # Get recent progress updates
        recent_progress = db.query(ProgressTracking).filter(
            ProgressTracking.user_id == current_user.id
        ).order_by(ProgressTracking.updated_at.desc()).limit(limit).all()
        
        activities = []
        for progress in recent_progress:
            activity = {
                "id": progress.id,
                "type": "progress_update",
                "certification_id": progress.certification_id,
                "certification_name": progress.certification.name if progress.certification else "Unknown",
                "status": progress.status,
                "progress_percentage": progress.progress_percentage,
                "updated_at": progress.updated_at.isoformat() if progress.updated_at else None
            }
            activities.append(activity)
        
        return {
            "activities": activities,
            "total_count": len(activities)
        }
        
    except Exception as e:
        logger.error(f"Recent activity error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load recent activity"
        )


@router.get("/learning-paths")
async def get_learning_paths(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's learning paths with detailed progress"""
    try:
        learning_paths = db.query(LearningPath).filter(
            LearningPath.user_id == current_user.id
        ).all()

        enhanced_paths = []
        for path in learning_paths:
            # Get certifications in this path
            path_certifications = []
            if hasattr(path, 'certifications'):
                for cert in path.certifications:
                    # Get progress for this certification
                    progress = db.query(ProgressTracking).filter(
                        ProgressTracking.user_id == current_user.id,
                        ProgressTracking.certification_id == cert.id
                    ).first()

                    path_certifications.append({
                        "id": cert.id,
                        "name": cert.name,
                        "provider": cert.provider,
                        "difficulty": cert.difficulty_level,
                        "progress": progress.progress_percentage if progress else 0,
                        "status": progress.status if progress else "not_started"
                    })

            # Calculate overall path progress
            if path_certifications:
                total_progress = sum(cert["progress"] for cert in path_certifications)
                avg_progress = total_progress / len(path_certifications)
                completed_count = len([cert for cert in path_certifications if cert["status"] == "completed"])
            else:
                avg_progress = 0
                completed_count = 0

            enhanced_paths.append({
                "id": path.id,
                "name": path.name,
                "description": path.description,
                "progress": round(avg_progress, 1),
                "total_certifications": len(path_certifications),
                "completed_certifications": completed_count,
                "estimated_completion": None,  # Would calculate based on study pace
                "certifications": path_certifications,
                "is_active": path.is_active if hasattr(path, 'is_active') else True,
                "created_at": path.created_at.isoformat() if path.created_at else None,
                "updated_at": path.updated_at.isoformat() if path.updated_at else None
            })

        return enhanced_paths

    except Exception as e:
        logger.error(f"Learning paths error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load learning paths"
        )


@router.get("/recommendations")
async def get_recommendations(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    limit: int = 5
):
    """Get personalized certification recommendations"""
    try:
        # Get user's current certifications and interests
        user_progress = db.query(ProgressTracking).filter(
            ProgressTracking.user_id == current_user.id
        ).all()

        completed_cert_ids = [p.certification_id for p in user_progress if p.status == "completed"]

        # Get recommended certifications (excluding completed ones)
        recommended_certs = db.query(Certification).filter(
            ~Certification.id.in_(completed_cert_ids)
        ).limit(limit * 2).all()  # Get more to filter and rank

        recommendations = []
        for cert in recommended_certs[:limit]:
            # Calculate recommendation score based on user profile
            priority = "medium"  # Would use ML model in production
            reason = "Based on your current progress and industry trends"

            # Mock market data - would come from external APIs
            market_demand = 85  # Percentage
            salary_impact = 15000  # USD increase

            recommendations.append({
                "id": cert.id,
                "certification": {
                    "id": cert.id,
                    "name": cert.name,
                    "provider": cert.provider,
                    "difficulty": cert.difficulty_level,
                    "estimated_study_hours": cert.estimated_study_hours,
                    "description": cert.description
                },
                "reason": reason,
                "priority": priority,
                "estimated_study_time": cert.estimated_study_hours,
                "prerequisites": [],  # Would parse from certification data
                "market_demand": market_demand,
                "salary_impact": salary_impact
            })

        return recommendations

    except Exception as e:
        logger.error(f"Recommendations error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load recommendations"
        )


@router.post("/quick-action")
async def handle_quick_action(
    action_request: QuickActionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Handle quick actions from dashboard"""
    try:
        action = action_request.action

        if action == "add_to_path":
            if not action_request.certification_id or not action_request.learning_path_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="certification_id and learning_path_id are required for add_to_path action"
                )

            # Add certification to learning path
            # This would involve updating the learning path's certifications
            # For now, return success
            return {
                "success": True,
                "message": "Certification added to learning path successfully"
            }

        elif action == "start_study":
            if not action_request.certification_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="certification_id is required for start_study action"
                )

            # Start or update study session
            # This would create/update progress tracking record
            return {
                "success": True,
                "message": "Study session started",
                "study_session_id": "session_123"
            }

        elif action == "mark_complete":
            if not action_request.certification_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="certification_id is required for mark_complete action"
                )

            # Mark certification as complete
            progress = db.query(ProgressTracking).filter(
                ProgressTracking.user_id == current_user.id,
                ProgressTracking.certification_id == action_request.certification_id
            ).first()

            if progress:
                progress.status = "completed"
                progress.progress_percentage = 100
                progress.completed_at = datetime.utcnow()
                db.commit()

            return {
                "success": True,
                "message": "Certification marked as complete"
            }

        elif action == "set_goal":
            if not action_request.goal_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="goal_data is required for set_goal action"
                )

            # Set user goal
            # This would create/update user goals in database
            return {
                "success": True,
                "message": "Goal set successfully"
            }

        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown action: {action}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Quick action error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process quick action"
        )
