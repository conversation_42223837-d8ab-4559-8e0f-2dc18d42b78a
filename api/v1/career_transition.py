"""FastAPI endpoints for career transition planning and pathfinding.

This module provides comprehensive API endpoints for career transition planning,
budget-aware pathfinding, and transition plan management.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from fastapi.responses import Response
from sqlalchemy.orm import Session
from datetime import datetime

from database import get_db
from schemas.career_transition import (
    CareerPathfindingRequest, CareerPathfindingResponse,
    CareerTransitionPlanCreate, CareerTransitionPlanUpdate, CareerTransitionPlanResponse,
    StepProgressUpdate, TransitionStepResponse,
    CareerRoleResponse, CareerTransitionSummary
)
from services.career_transition import CareerTransitionService, PathfindingConstraints
from services.pdf_report_generator import EnhancedPDFReportGenerator
from models.career_transition import CareerRole, CareerTransitionPlan, CareerTransitionStep

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/career-transition", tags=["Career Transition"])


def get_current_user_id() -> str:
    """Get current user ID from authentication context."""
    # TODO: Implement proper authentication
    return "test_user_1"


@router.post("/pathfinding", response_model=CareerPathfindingResponse)
async def find_career_paths(
    request: CareerPathfindingRequest,
    db: Session = Depends(get_db)
):
    """Find optimal career transition paths with budget and timeline constraints."""
    try:
        logger.info(f"Finding career paths from {request.current_role_id} to {request.target_role_id}")
        
        service = CareerTransitionService(db)
        
        # Create constraints from request
        constraints = PathfindingConstraints(
            max_budget=request.max_budget,
            max_timeline_months=request.max_timeline_months,
            max_difficulty=request.max_difficulty.value,
            preferred_learning_style=request.learning_style.value,
            study_hours_per_week=request.study_hours_per_week,
            currency=request.currency
        )
        
        # Find paths
        path_options = service.find_career_paths(
            source_role_id=request.current_role_id,
            target_role_id=request.target_role_id,
            constraints=constraints,
            max_paths=request.max_paths
        )
        
        if not path_options:
            raise HTTPException(
                status_code=404,
                detail="No viable career transition paths found with the given constraints"
            )
        
        # Get role information
        target_role = db.query(CareerRole).filter(CareerRole.id == request.target_role_id).first()
        if not target_role:
            raise HTTPException(status_code=404, detail="Target role not found")
        
        source_role = None
        if request.current_role_id:
            source_role = db.query(CareerRole).filter(CareerRole.id == request.current_role_id).first()
        
        # Calculate summary statistics
        costs = [p.total_cost for p in path_options]
        durations = [p.total_duration_months for p in path_options]
        
        # Convert to response format
        path_responses = []
        for i, path in enumerate(path_options):
            steps = [
                {
                    "name": step["name"],
                    "description": step.get("description", ""),
                    "step_type": step.get("type", "transition"),
                    "duration_months": step.get("duration_months", 1),
                    "cost": step.get("cost", 0.0),
                    "certifications": step.get("certifications", []),
                    "prerequisites": step.get("prerequisites", [])
                }
                for step in path.steps
            ]
            
            path_responses.append({
                "path_id": str(path.path_id),
                "name": f"Path {i+1}",
                "description": f"Career transition path with {len(path.steps)} steps",
                "total_cost": path.total_cost,
                "total_duration_months": path.total_duration_months,
                "difficulty_level": ["Easy", "Medium", "Hard", "Expert"][min(int(path.difficulty_score), 3)],
                "success_probability": path.success_probability,
                "steps": steps,
                "certifications_required": path.certifications_required,
                "estimated_salary_increase": path.estimated_salary_increase,
                "cost_score": path.total_cost / max(costs) if costs else 0.0,
                "time_score": path.total_duration_months / max(durations) if durations else 0.0,
                "difficulty_score": path.difficulty_score / 4.0,
                "overall_score": service._calculate_path_score(path, constraints)
            })
        
        # Generate recommendations
        recommendations = []
        if path_options:
            best_path = path_options[0]
            if best_path.total_cost < (request.max_budget or float('inf')) * 0.8:
                recommendations.append("This path fits well within your budget with room for unexpected costs")
            if best_path.success_probability > 0.8:
                recommendations.append("This path has a high success rate based on historical data")
            if len(best_path.certifications_required) <= 3:
                recommendations.append("This path requires a manageable number of certifications")
        
        return CareerPathfindingResponse(
            source_role=source_role.to_dict() if source_role else None,
            target_role=target_role.to_dict(),
            path_options=path_responses,
            total_paths_found=len(path_options),
            cost_range={
                "min": min(costs) if costs else 0.0,
                "max": max(costs) if costs else 0.0,
                "currency": request.currency
            },
            duration_range={
                "estimated_months": int(sum(durations) / len(durations)) if durations else 0,
                "min_months": min(durations) if durations else None,
                "max_months": max(durations) if durations else None
            },
            recommended_path_id=str(path_options[0].path_id) if path_options else None,
            recommendations=recommendations,
            generated_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in career pathfinding: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during pathfinding")


@router.get("/roles", response_model=List[CareerRoleResponse])
async def get_career_roles(
    domain: Optional[str] = Query(None, description="Filter by domain"),
    level: Optional[str] = Query(None, description="Filter by career level"),
    min_salary: Optional[float] = Query(None, description="Minimum salary filter"),
    max_salary: Optional[float] = Query(None, description="Maximum salary filter"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get available career roles with filtering options."""
    try:
        query = db.query(CareerRole).filter(CareerRole.is_active == True)
        
        # Apply filters
        if domain:
            query = query.filter(CareerRole.domain.ilike(f"%{domain}%"))
        if level:
            query = query.filter(CareerRole.level == level)
        if min_salary:
            query = query.filter(CareerRole.salary_min >= min_salary)
        if max_salary:
            query = query.filter(CareerRole.salary_max <= max_salary)
        
        # Apply pagination
        offset = (page - 1) * page_size
        roles = query.offset(offset).limit(page_size).all()
        
        return [role.to_dict() for role in roles]
        
    except Exception as e:
        logger.error(f"Error retrieving career roles: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving career roles")


@router.post("/plans", response_model=CareerTransitionPlanResponse)
async def create_transition_plan(
    request: CareerTransitionPlanCreate,
    db: Session = Depends(get_db)
):
    """Create a new career transition plan."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Creating transition plan for user {user_id}")
        
        service = CareerTransitionService(db)
        
        # Create constraints from request
        constraints = PathfindingConstraints(
            max_budget=request.budget_max,
            max_timeline_months=request.max_timeline_months,
            max_difficulty=request.difficulty_preference.value,
            preferred_learning_style=request.learning_style.value,
            study_hours_per_week=request.study_hours_per_week,
            currency=request.currency
        )
        
        # Create the plan
        plan = service.create_transition_plan(
            user_id=user_id,
            name=request.name,
            current_role_id=request.current_role_id,
            target_role_id=request.target_role_id,
            constraints=constraints,
            description=request.description
        )
        
        # Get plan with related data
        return await get_transition_plan(plan.id, db)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating transition plan: {e}")
        raise HTTPException(status_code=500, detail="Error creating transition plan")


@router.get("/plans", response_model=List[CareerTransitionPlanResponse])
async def get_user_transition_plans(
    status: Optional[str] = Query(None, description="Filter by plan status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get user's career transition plans."""
    try:
        user_id = get_current_user_id()
        
        query = db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.user_id == user_id,
            CareerTransitionPlan.is_active == True
        )
        
        if status:
            query = query.filter(CareerTransitionPlan.status == status)
        
        # Apply pagination
        offset = (page - 1) * page_size
        plans = query.offset(offset).limit(page_size).all()
        
        # Convert to response format
        plan_responses = []
        for plan in plans:
            plan_dict = plan.to_dict()
            
            # Add related data
            if plan.current_role:
                plan_dict['current_role'] = plan.current_role.to_dict()
            if plan.target_role:
                plan_dict['target_role'] = plan.target_role.to_dict()
            
            # Get steps
            steps = db.query(CareerTransitionStep).filter(
                CareerTransitionStep.plan_id == plan.id
            ).order_by(CareerTransitionStep.sequence).all()
            plan_dict['steps'] = [step.to_dict() for step in steps]
            
            plan_responses.append(plan_dict)
        
        return plan_responses
        
    except Exception as e:
        logger.error(f"Error retrieving transition plans: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving transition plans")


@router.get("/plans/{plan_id}", response_model=CareerTransitionPlanResponse)
async def get_transition_plan(
    plan_id: int = Path(..., description="Transition plan ID"),
    db: Session = Depends(get_db)
):
    """Get a specific career transition plan."""
    try:
        user_id = get_current_user_id()
        
        plan = db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.id == plan_id,
            CareerTransitionPlan.user_id == user_id,
            CareerTransitionPlan.is_active == True
        ).first()
        
        if not plan:
            raise HTTPException(status_code=404, detail="Transition plan not found")
        
        plan_dict = plan.to_dict()
        
        # Add related data
        if plan.current_role:
            plan_dict['current_role'] = plan.current_role.to_dict()
        if plan.target_role:
            plan_dict['target_role'] = plan.target_role.to_dict()
        
        # Get steps
        steps = db.query(CareerTransitionStep).filter(
            CareerTransitionStep.plan_id == plan.id
        ).order_by(CareerTransitionStep.sequence).all()
        plan_dict['steps'] = [step.to_dict() for step in steps]
        
        return plan_dict
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving transition plan {plan_id}: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving transition plan")


@router.put("/plans/{plan_id}", response_model=CareerTransitionPlanResponse)
async def update_transition_plan(
    plan_id: int,
    request: CareerTransitionPlanUpdate,
    db: Session = Depends(get_db)
):
    """Update a career transition plan."""
    try:
        user_id = get_current_user_id()
        
        plan = db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.id == plan_id,
            CareerTransitionPlan.user_id == user_id,
            CareerTransitionPlan.is_active == True
        ).first()
        
        if not plan:
            raise HTTPException(status_code=404, detail="Transition plan not found")
        
        # Update fields
        update_data = request.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(plan, field):
                setattr(plan, field, value)
        
        db.commit()
        
        return await get_transition_plan(plan_id, db)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating transition plan {plan_id}: {e}")
        raise HTTPException(status_code=500, detail="Error updating transition plan")


@router.put("/plans/{plan_id}/steps/{step_id}/progress")
async def update_step_progress(
    plan_id: int,
    step_id: int,
    request: StepProgressUpdate,
    db: Session = Depends(get_db)
):
    """Update progress for a transition plan step."""
    try:
        user_id = get_current_user_id()
        
        # Verify plan ownership
        plan = db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.id == plan_id,
            CareerTransitionPlan.user_id == user_id,
            CareerTransitionPlan.is_active == True
        ).first()
        
        if not plan:
            raise HTTPException(status_code=404, detail="Transition plan not found")
        
        service = CareerTransitionService(db)
        success = service.update_plan_progress(plan_id, step_id, request.progress_percentage)
        
        if not success:
            raise HTTPException(status_code=404, detail="Transition step not found")
        
        return {"message": "Step progress updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating step progress: {e}")
        raise HTTPException(status_code=500, detail="Error updating step progress")


@router.delete("/plans/{plan_id}")
async def delete_transition_plan(
    plan_id: int,
    db: Session = Depends(get_db)
):
    """Delete (deactivate) a career transition plan."""
    try:
        user_id = get_current_user_id()
        
        plan = db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.id == plan_id,
            CareerTransitionPlan.user_id == user_id,
            CareerTransitionPlan.is_active == True
        ).first()
        
        if not plan:
            raise HTTPException(status_code=404, detail="Transition plan not found")
        
        plan.is_active = False
        db.commit()
        
        return {"message": "Transition plan deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting transition plan {plan_id}: {e}")
        raise HTTPException(status_code=500, detail="Error deleting transition plan")


@router.get("/summary", response_model=CareerTransitionSummary)
async def get_transition_summary(
    db: Session = Depends(get_db)
):
    """Get career transition summary statistics for the user."""
    try:
        user_id = get_current_user_id()
        
        # Get user's plans
        plans = db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.user_id == user_id,
            CareerTransitionPlan.is_active == True
        ).all()
        
        total_plans = len(plans)
        active_plans = len([p for p in plans if p.status == 'active'])
        completed_plans = len([p for p in plans if p.status == 'completed'])
        
        total_budget_allocated = sum(p.budget_max or 0.0 for p in plans)
        
        # Calculate average completion time for completed plans
        completed_durations = []
        for plan in plans:
            if plan.status == 'completed' and plan.created_at:
                # This is a simplified calculation - in practice, you'd track actual completion dates
                completed_durations.append(plan.timeline_months or 12)
        
        avg_completion_time = sum(completed_durations) / len(completed_durations) if completed_durations else None
        success_rate = completed_plans / total_plans if total_plans > 0 else None
        
        return CareerTransitionSummary(
            total_plans=total_plans,
            active_plans=active_plans,
            completed_plans=completed_plans,
            total_budget_allocated=total_budget_allocated,
            total_budget_spent=0.0,  # TODO: Calculate actual spending
            average_completion_time_months=avg_completion_time,
            success_rate=success_rate,
            popular_target_roles=[],  # TODO: Implement analytics
            trending_certifications=[]  # TODO: Implement analytics
        )
        
    except Exception as e:
        logger.error(f"Error generating transition summary: {e}")
        raise HTTPException(status_code=500, detail="Error generating transition summary")


@router.get("/reports/career-summary")
async def generate_career_summary_pdf(
    include_transition_plans: bool = Query(True, description="Include transition plans in report"),
    include_cost_analysis: bool = Query(True, description="Include cost analysis in report"),
    include_recommendations: bool = Query(True, description="Include recommendations in report"),
    db: Session = Depends(get_db)
):
    """Generate comprehensive career summary PDF report."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Generating career summary PDF for user {user_id}")

        pdf_generator = EnhancedPDFReportGenerator(db)

        pdf_bytes = pdf_generator.generate_career_summary_report(
            user_id=user_id,
            include_transition_plans=include_transition_plans,
            include_cost_analysis=include_cost_analysis,
            include_recommendations=include_recommendations
        )

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"career_summary_{user_id}_{timestamp}.pdf"

        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(pdf_bytes))
            }
        )

    except Exception as e:
        logger.error(f"Error generating career summary PDF: {e}")
        raise HTTPException(status_code=500, detail="Error generating career summary PDF")


@router.get("/plans/{plan_id}/report")
async def generate_transition_plan_pdf(
    plan_id: int = Path(..., description="Transition plan ID"),
    include_detailed_steps: bool = Query(True, description="Include detailed steps in report"),
    include_cost_projections: bool = Query(True, description="Include cost projections in report"),
    db: Session = Depends(get_db)
):
    """Generate detailed PDF report for a specific transition plan."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Generating transition plan PDF for plan {plan_id}")

        # Verify plan ownership
        plan = db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.id == plan_id,
            CareerTransitionPlan.user_id == user_id,
            CareerTransitionPlan.is_active == True
        ).first()

        if not plan:
            raise HTTPException(status_code=404, detail="Transition plan not found")

        pdf_generator = EnhancedPDFReportGenerator(db)

        pdf_bytes = pdf_generator.generate_transition_plan_report(
            plan_id=plan_id,
            user_id=user_id,
            include_detailed_steps=include_detailed_steps,
            include_cost_projections=include_cost_projections
        )

        # Generate filename with plan name and timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_plan_name = "".join(c for c in plan.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_plan_name = safe_plan_name.replace(' ', '_')[:30]  # Limit length
        filename = f"transition_plan_{safe_plan_name}_{timestamp}.pdf"

        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(pdf_bytes))
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating transition plan PDF: {e}")
        raise HTTPException(status_code=500, detail="Error generating transition plan PDF")


@router.get("/reports/comparison")
async def generate_path_comparison_pdf(
    plan_ids: List[int] = Query(..., description="List of plan IDs to compare"),
    comparison_currency: str = Query("USD", description="Currency for cost comparison"),
    db: Session = Depends(get_db)
):
    """Generate PDF report comparing multiple career transition paths."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Generating path comparison PDF for plans {plan_ids}")

        if len(plan_ids) < 2:
            raise HTTPException(status_code=400, detail="At least 2 plans required for comparison")

        if len(plan_ids) > 5:
            raise HTTPException(status_code=400, detail="Maximum 5 plans allowed for comparison")

        # Verify plan ownership
        plans = db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.id.in_(plan_ids),
            CareerTransitionPlan.user_id == user_id,
            CareerTransitionPlan.is_active == True
        ).all()

        if len(plans) != len(plan_ids):
            raise HTTPException(status_code=404, detail="One or more transition plans not found")

        # Generate comparison report (simplified version)
        pdf_generator = EnhancedPDFReportGenerator(db)

        # For now, generate individual reports for each plan
        # TODO: Implement dedicated comparison report
        pdf_bytes = pdf_generator.generate_career_summary_report(
            user_id=user_id,
            include_transition_plans=True,
            include_cost_analysis=True,
            include_recommendations=True
        )

        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"path_comparison_{len(plans)}_plans_{timestamp}.pdf"

        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(pdf_bytes))
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating path comparison PDF: {e}")
        raise HTTPException(status_code=500, detail="Error generating path comparison PDF")
