"""Budget Optimization API endpoints for enterprise training budget management.

This module provides comprehensive API endpoints for:
- Training budget optimization and allocation
- ROI analysis for training investments
- Budget utilization analytics and reporting
- Cost-benefit analysis for certification programs
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from database import get_db
from services.enterprise_budget_optimizer import EnterpriseBudgetOptimizer
from schemas.enterprise import (
    BudgetOptimizationRequest, BudgetOptimizationResponse,
    BudgetAllocationRequest, BudgetAllocationResponse,
    ROICalculationRequest, ROICalculationResponse,
    BudgetAnalyticsResponse, BudgetRecommendationResponse
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/budget-optimization", tags=["Budget Optimization"])


@router.post("/optimize", response_model=BudgetOptimizationResponse)
async def optimize_training_budget(
    request: BudgetOptimizationRequest,
    db: Session = Depends(get_db)
):
    """Optimize training budget allocation for maximum ROI."""
    try:
        logger.info(f"Optimizing budget for enterprise {request.enterprise_id}")
        
        optimizer = EnterpriseBudgetOptimizer(db)
        optimization = optimizer.optimize_budget_allocation(
            enterprise_id=request.enterprise_id,
            total_budget=request.total_budget,
            strategic_priorities=request.strategic_priorities,
            team_constraints=request.team_constraints,
            timeline_months=request.timeline_months
        )
        
        return BudgetOptimizationResponse(
            enterprise_id=request.enterprise_id,
            total_budget=request.total_budget,
            optimized_allocation=optimization.allocation,
            projected_roi=optimization.projected_roi,
            cost_savings=optimization.cost_savings,
            efficiency_score=optimization.efficiency_score,
            recommendations=optimization.recommendations,
            risk_assessment=optimization.risk_assessment,
            timeline_months=request.timeline_months,
            optimization_date=datetime.utcnow().isoformat()
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error optimizing budget: {e}")
        raise HTTPException(status_code=500, detail="Error optimizing training budget")


@router.get("/recommendations/{enterprise_id}", response_model=BudgetRecommendationResponse)
async def get_budget_recommendations(
    enterprise_id: int = Path(..., description="Enterprise ID"),
    budget_amount: float = Query(..., ge=1000, description="Available budget amount"),
    priority_focus: Optional[str] = Query(None, description="Priority focus area"),
    db: Session = Depends(get_db)
):
    """Get budget allocation recommendations for an enterprise."""
    try:
        logger.info(f"Getting budget recommendations for enterprise {enterprise_id}")
        
        optimizer = EnterpriseBudgetOptimizer(db)
        recommendations = optimizer.generate_budget_recommendations(
            enterprise_id=enterprise_id,
            budget_amount=budget_amount,
            priority_focus=priority_focus
        )
        
        return BudgetRecommendationResponse(
            enterprise_id=enterprise_id,
            budget_amount=budget_amount,
            recommendations=recommendations.recommendations,
            priority_allocations=recommendations.priority_allocations,
            expected_outcomes=recommendations.expected_outcomes,
            implementation_timeline=recommendations.implementation_timeline,
            success_metrics=recommendations.success_metrics,
            generated_at=datetime.utcnow().isoformat()
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting budget recommendations: {e}")
        raise HTTPException(status_code=500, detail="Error getting budget recommendations")


@router.post("/roi/calculate", response_model=ROICalculationResponse)
async def calculate_training_roi(
    request: ROICalculationRequest,
    db: Session = Depends(get_db)
):
    """Calculate ROI for training investments."""
    try:
        logger.info(f"Calculating training ROI for enterprise {request.enterprise_id}")
        
        optimizer = EnterpriseBudgetOptimizer(db)
        roi_analysis = optimizer.calculate_training_roi(
            enterprise_id=request.enterprise_id,
            investment_amount=request.investment_amount,
            training_programs=request.training_programs,
            timeline_months=request.timeline_months,
            success_metrics=request.success_metrics
        )
        
        return ROICalculationResponse(
            enterprise_id=request.enterprise_id,
            investment_amount=request.investment_amount,
            projected_return=roi_analysis.projected_return,
            roi_percentage=roi_analysis.roi_percentage,
            payback_period_months=roi_analysis.payback_period_months,
            net_present_value=roi_analysis.net_present_value,
            risk_adjusted_roi=roi_analysis.risk_adjusted_roi,
            confidence_interval=roi_analysis.confidence_interval,
            key_assumptions=roi_analysis.key_assumptions,
            sensitivity_analysis=roi_analysis.sensitivity_analysis,
            calculation_date=datetime.utcnow().isoformat()
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating training ROI: {e}")
        raise HTTPException(status_code=500, detail="Error calculating training ROI")


@router.get("/analytics/{enterprise_id}", response_model=BudgetAnalyticsResponse)
async def get_budget_analytics(
    enterprise_id: int = Path(..., description="Enterprise ID"),
    period_months: int = Query(12, ge=1, le=36, description="Analysis period in months"),
    include_projections: bool = Query(True, description="Include future projections"),
    db: Session = Depends(get_db)
):
    """Get comprehensive budget utilization analytics."""
    try:
        logger.info(f"Getting budget analytics for enterprise {enterprise_id}")
        
        optimizer = EnterpriseBudgetOptimizer(db)
        analytics = optimizer.analyze_budget_utilization(
            enterprise_id=enterprise_id,
            period_months=period_months,
            include_projections=include_projections
        )
        
        return BudgetAnalyticsResponse(
            enterprise_id=enterprise_id,
            analysis_period_months=period_months,
            total_budget_allocated=analytics.total_budget_allocated,
            total_budget_utilized=analytics.total_budget_utilized,
            utilization_rate=analytics.utilization_rate,
            cost_per_certification=analytics.cost_per_certification,
            roi_by_program=analytics.roi_by_program,
            efficiency_metrics=analytics.efficiency_metrics,
            trend_analysis=analytics.trend_analysis,
            benchmark_comparison=analytics.benchmark_comparison,
            optimization_opportunities=analytics.optimization_opportunities,
            projections=analytics.projections if include_projections else None,
            analysis_date=datetime.utcnow().isoformat()
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting budget analytics: {e}")
        raise HTTPException(status_code=500, detail="Error getting budget analytics")


@router.get("/benchmarks")
async def get_industry_benchmarks(
    industry: Optional[str] = Query(None, description="Industry sector"),
    company_size: Optional[str] = Query(None, description="Company size category"),
    db: Session = Depends(get_db)
):
    """Get industry benchmarks for training budget allocation."""
    try:
        logger.info(f"Getting industry benchmarks for {industry}, size: {company_size}")
        
        optimizer = EnterpriseBudgetOptimizer(db)
        benchmarks = optimizer.get_industry_benchmarks(
            industry=industry,
            company_size=company_size
        )
        
        return {
            'industry': industry,
            'company_size': company_size,
            'benchmarks': benchmarks,
            'data_sources': [
                'Industry Training Reports',
                'Cybersecurity Workforce Studies',
                'Enterprise Training Surveys'
            ],
            'last_updated': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting industry benchmarks: {e}")
        raise HTTPException(status_code=500, detail="Error getting industry benchmarks")


@router.get("/health")
async def health_check():
    """Health check endpoint for budget optimization service."""
    return {
        'status': 'healthy',
        'service': 'Budget Optimization',
        'version': '1.0.0',
        'features': [
            'budget_optimization',
            'roi_calculation',
            'budget_analytics',
            'industry_benchmarks',
            'allocation_recommendations'
        ],
        'timestamp': datetime.utcnow().isoformat()
    }
