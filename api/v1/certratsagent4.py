"""CertRatsAgent4 Unified API - Integration of Agent 2 & Agent 4.

This module provides a unified API that integrates the AI Study Assistant (Agent 2)
and Career & Cost Intelligence (Agent 4) for comprehensive certification planning.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Header
from sqlalchemy.orm import Session
from datetime import datetime

from database import get_db
from services.ai_study_assistant import OnDeviceAIStudyAssistant
from services.career_transition import CareerTransitionService, PathfindingConstraints
from services.salary_intelligence import SalaryIntelligenceService
from services.enterprise_budget_optimizer import EnterpriseBudgetOptimizer
from services.unified_error_handler import CareerIntelligenceErrorHandler, service_error
from schemas.certratsagent4 import (
    ComprehensivePlanRequest, ComprehensivePlanResponse,
    PersonalizedDashboardResponse, EnterpriseAnalysisRequest, EnterpriseAnalysisResponse
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/unified-intelligence", tags=["Unified Intelligence Platform"])


def get_user_id_from_traefik(
    x_forwarded_user: Optional[str] = Header(None),
    x_remote_user: Optional[str] = Header(None),
    x_user: Optional[str] = Header(None)
) -> str:
    """Get user ID from Traefik authentication headers."""
    user_id = x_forwarded_user or x_remote_user or x_user
    if not user_id:
        # For development/testing, use a default user
        user_id = "test_user_1"
        logger.warning("No user ID found in Traefik headers, using test user")
    return user_id


@router.post("/comprehensive-plan", response_model=ComprehensivePlanResponse)
async def create_comprehensive_plan(
    request: ComprehensivePlanRequest,
    user_id: str = Depends(get_user_id_from_traefik),
    db: Session = Depends(get_db)
):
    """Create a comprehensive certification and career plan using both AI agents."""
    try:
        logger.info(f"Creating comprehensive plan for user {user_id}")
        
        # Initialize services
        ai_assistant = OnDeviceAIStudyAssistant(db)
        career_service = CareerTransitionService(db)
        salary_service = SalaryIntelligenceService(db)
        
        # Generate AI study recommendations
        study_recommendations = ai_assistant.generate_personalized_recommendations(
            user_id=user_id,
            context="comprehensive_planning"
        )
        
        # Generate adaptive learning path
        adaptive_path = ai_assistant.generate_adaptive_learning_path(
            user_id=user_id,
            certification_id=request.target_certification_id,
            target_date=request.target_date
        )
        
        # Find career transition paths
        constraints = PathfindingConstraints(
            max_budget=request.max_budget,
            max_timeline_months=request.max_timeline_months,
            max_difficulty=request.max_difficulty,
            preferred_learning_style=request.learning_style,
            study_hours_per_week=request.study_hours_per_week,
            currency=request.currency
        )
        
        career_paths = career_service.find_career_paths(
            source_role_id=request.current_role_id,
            target_role_id=request.target_role_id,
            constraints=constraints,
            max_paths=5
        )
        
        # Calculate ROI analysis
        roi_analysis = salary_service.calculate_certification_roi(
            certification_id=request.target_certification_id,
            current_role_id=request.current_role_id or request.target_role_id,
            target_role_id=request.target_role_id,
            investment_cost=request.max_budget or 5000,
            location=request.location,
            experience_years=request.experience_years
        )
        
        # Generate learning insights
        learning_insights = ai_assistant.analyze_learning_patterns(user_id)
        
        # Assess knowledge level
        user_data = ai_assistant._gather_user_data(user_id)
        from models.certification import Certification
        certification = db.query(Certification).filter(
            Certification.id == request.target_certification_id
        ).first()
        
        knowledge_assessment = None
        if certification:
            knowledge_level = ai_assistant._assess_knowledge_level(user_data, certification)
            knowledge_assessment = {
                'knowledge_level': knowledge_level,
                'knowledge_percentage': knowledge_level * 100,
                'difficulty_level': ai_assistant._determine_difficulty_level(knowledge_level),
                'weak_areas': ai_assistant._identify_weak_areas(user_data, certification),
                'strong_areas': ai_assistant._identify_strong_areas(user_data, certification)
            }
        
        # Generate integrated recommendations
        integrated_recommendations = _generate_integrated_recommendations(
            study_recommendations, career_paths, roi_analysis, learning_insights
        )
        
        # Calculate success probability
        success_probability = _calculate_comprehensive_success_probability(
            adaptive_path, career_paths, roi_analysis, knowledge_assessment
        )
        
        return ComprehensivePlanResponse(
            user_id=user_id,
            plan_id=f"comprehensive_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            study_recommendations=study_recommendations[:10],
            adaptive_learning_path=adaptive_path,
            career_transition_paths=career_paths[:3],
            roi_analysis=roi_analysis,
            learning_insights=learning_insights[:5],
            knowledge_assessment=knowledge_assessment,
            integrated_recommendations=integrated_recommendations,
            success_probability=success_probability,
            estimated_timeline_weeks=adaptive_path.estimated_duration_weeks,
            total_investment_estimate=roi_analysis.investment_cost,
            expected_salary_increase=roi_analysis.expected_salary_increase,
            plan_created_at=datetime.utcnow().isoformat()
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Use unified error handling
        raise service_error(
            "unified_intelligence",
            "comprehensive_plan_creation",
            e,
            user_id=user_id,
            request_data=request.dict()
        )


@router.get("/dashboard", response_model=PersonalizedDashboardResponse)
async def get_personalized_dashboard(
    include_market_trends: bool = Query(True, description="Include market trend analysis"),
    include_salary_intelligence: bool = Query(True, description="Include salary intelligence"),
    user_id: str = Depends(get_user_id_from_traefik),
    db: Session = Depends(get_db)
):
    """Get personalized dashboard with AI insights and career intelligence."""
    try:
        logger.info(f"Generating personalized dashboard for user {user_id}")
        
        # Initialize services
        ai_assistant = OnDeviceAIStudyAssistant(db)
        salary_service = SalaryIntelligenceService(db)
        
        # Get AI recommendations
        recommendations = ai_assistant.generate_personalized_recommendations(user_id)
        
        # Get learning insights
        insights = ai_assistant.analyze_learning_patterns(user_id)
        
        # Get learning style analysis
        user_data = ai_assistant._gather_user_data(user_id)
        learning_style = ai_assistant._identify_learning_style(user_data)
        
        # Calculate study metrics
        sessions = user_data['sessions']
        study_efficiency = _calculate_study_efficiency(sessions)
        consistency_score = _calculate_consistency_score(sessions)
        
        # Get salary intelligence if requested
        salary_insights = None
        if include_salary_intelligence:
            # Get user's current role (simplified - would come from user profile)
            current_role_id = 1  # Default role
            salary_intel = salary_service.analyze_salary_intelligence(
                role_id=current_role_id,
                location="remote",
                experience_years=5
            )
            salary_insights = {
                'current_salary_range': salary_intel.current_salary_range,
                'market_position': 'at_market',  # Simplified
                'growth_potential': salary_intel.growth_projections.get('five_year', 0)
            }
        
        # Get market trends if requested
        market_trends = None
        if include_market_trends:
            market_trends = salary_service._analyze_market_trends('cybersecurity')
        
        # Generate next milestones
        next_milestones = _generate_next_milestones(recommendations, insights)
        
        # Generate motivational message
        motivational_message = _generate_motivational_message(
            study_efficiency, consistency_score, len(sessions)
        )
        
        return PersonalizedDashboardResponse(
            user_id=user_id,
            dashboard_date=datetime.utcnow().isoformat(),
            top_recommendations=recommendations[:5],
            key_insights=insights[:3],
            current_knowledge_level=0.65,  # Would be calculated from actual data
            study_efficiency_score=study_efficiency,
            consistency_score=consistency_score,
            learning_style=learning_style,
            salary_insights=salary_insights,
            market_trends=market_trends,
            next_milestones=next_milestones,
            motivational_message=motivational_message,
            quick_actions=[
                "Take a practice test to assess current knowledge",
                "Review weak areas identified in recent sessions",
                "Schedule study time for this week",
                "Explore certification ROI calculator"
            ]
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Use unified error handling
        raise service_error(
            "certratsagent4",
            "dashboard_generation",
            e,
            user_id=user_id,
            include_market_trends=include_market_trends,
            include_salary_intelligence=include_salary_intelligence
        )


@router.post("/enterprise-analysis", response_model=EnterpriseAnalysisResponse)
async def analyze_enterprise_training_needs(
    request: EnterpriseAnalysisRequest,
    db: Session = Depends(get_db)
):
    """Analyze enterprise training needs and optimize budget allocation."""
    try:
        logger.info(f"Analyzing enterprise training needs for enterprise {request.enterprise_id}")
        
        # Initialize enterprise optimizer
        optimizer = EnterpriseBudgetOptimizer(db)
        
        # Optimize budget allocation
        optimization = optimizer.optimize_enterprise_budget(
            enterprise_id=request.enterprise_id,
            total_budget=request.total_budget,
            budget_period_months=request.budget_period_months,
            strategic_priorities=request.strategic_priorities
        )
        
        # Generate comprehensive report
        report = optimizer.generate_budget_report(request.enterprise_id, optimization)
        
        # Calculate enterprise metrics
        total_employees = sum(
            team['budget_needed'] / 2000  # Estimate employees from budget
            for team in optimization.team_priorities
        )
        
        avg_roi = optimization.roi_projections.get('one_year_roi', 0)
        
        return EnterpriseAnalysisResponse(
            enterprise_id=request.enterprise_id,
            analysis_date=datetime.utcnow().isoformat(),
            total_budget=optimization.total_budget,
            optimized_allocation=optimization.optimized_allocation,
            projected_cost_savings=optimization.cost_savings,
            roi_projections=optimization.roi_projections,
            team_priorities=optimization.team_priorities,
            recommended_certifications=optimization.recommended_certifications,
            implementation_timeline=optimization.timeline_recommendations,
            risk_assessment=optimization.risk_assessment,
            enterprise_metrics={
                'total_employees_estimated': int(total_employees),
                'teams_analyzed': len(optimization.team_priorities),
                'average_roi_projection': avg_roi,
                'budget_efficiency_score': min(optimization.cost_savings / optimization.total_budget * 100, 100),
                'strategic_alignment_score': 85.0  # Simplified calculation
            },
            key_insights=report['key_insights'],
            next_steps=report['next_steps']
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Use unified error handling
        raise service_error(
            "certratsagent4",
            "enterprise_analysis",
            e,
            enterprise_id=request.enterprise_id,
            total_budget=request.total_budget
        )


# Helper Functions

def _generate_integrated_recommendations(
    study_recommendations,
    career_paths,
    roi_analysis,
    learning_insights
) -> List[str]:
    """Generate integrated recommendations combining AI and career intelligence."""
    recommendations = []

    # Study-focused recommendations
    if study_recommendations:
        top_study = study_recommendations[0]
        recommendations.append(
            f"Focus on {top_study.title.lower()} to address immediate learning needs"
        )

    # Career-focused recommendations
    if career_paths:
        best_path = career_paths[0]
        recommendations.append(
            f"Follow career path with {best_path.total_duration_months}-month timeline for optimal ROI"
        )

    # ROI-focused recommendations
    if roi_analysis.payback_period_months < 12:
        recommendations.append(
            f"Excellent ROI potential with {roi_analysis.payback_period_months}-month payback period"
        )
    elif roi_analysis.five_year_roi > 100:
        recommendations.append(
            f"Strong long-term investment with {roi_analysis.five_year_roi:.0f}% 5-year ROI"
        )

    # Learning efficiency recommendations
    if learning_insights:
        efficiency_insights = [i for i in learning_insights if i.category == 'efficiency']
        if efficiency_insights:
            recommendations.append(
                f"Optimize study approach: {efficiency_insights[0].actionable_steps[0]}"
            )

    return recommendations[:5]


def _calculate_comprehensive_success_probability(
    adaptive_path,
    career_paths,
    roi_analysis,
    knowledge_assessment
) -> float:
    """Calculate overall success probability for the comprehensive plan."""
    factors = []

    # Learning path success probability
    if adaptive_path:
        factors.append(adaptive_path.success_probability)

    # Career transition success probability
    if career_paths:
        factors.append(career_paths[0].success_probability)

    # ROI confidence score
    factors.append(roi_analysis.confidence_score)

    # Knowledge readiness factor
    if knowledge_assessment:
        knowledge_factor = knowledge_assessment['knowledge_level']
        factors.append(knowledge_factor)

    # Calculate weighted average
    if factors:
        return sum(factors) / len(factors)

    return 0.7  # Default moderate confidence


def _calculate_study_efficiency(sessions) -> float:
    """Calculate study efficiency score from session data."""
    if not sessions:
        return 0.5

    # Calculate based on effectiveness ratings and progress
    effectiveness_scores = [s.effectiveness_rating for s in sessions if s.effectiveness_rating]
    progress_gains = [
        s.progress_after - s.progress_before
        for s in sessions
        if s.progress_after and s.progress_before
    ]

    efficiency = 0.5  # Base score

    if effectiveness_scores:
        avg_effectiveness = sum(effectiveness_scores) / len(effectiveness_scores)
        efficiency += (avg_effectiveness - 3) * 0.1  # Adjust based on 1-5 scale

    if progress_gains:
        avg_progress = sum(progress_gains) / len(progress_gains)
        efficiency += min(avg_progress / 20, 0.3)  # Cap progress contribution

    return min(max(efficiency, 0.0), 1.0)


def _calculate_consistency_score(sessions) -> float:
    """Calculate study consistency score from session data."""
    if not sessions or len(sessions) < 7:
        return 0.3

    # Calculate study frequency
    study_dates = set(s.started_at.date() for s in sessions if s.started_at)
    days_span = (max(study_dates) - min(study_dates)).days if len(study_dates) > 1 else 1

    frequency_score = min(len(study_dates) / max(days_span, 1) * 7, 1.0)  # Weekly frequency

    # Calculate session length consistency
    durations = [s.duration_minutes for s in sessions if s.duration_minutes]
    if durations:
        avg_duration = sum(durations) / len(durations)
        duration_variance = sum((d - avg_duration) ** 2 for d in durations) / len(durations)
        consistency_factor = max(0, 1 - (duration_variance ** 0.5) / avg_duration)
    else:
        consistency_factor = 0.5

    return (frequency_score * 0.7 + consistency_factor * 0.3)


def _generate_next_milestones(recommendations, insights) -> List[str]:
    """Generate next milestones based on recommendations and insights."""
    milestones = []

    # Study milestones
    if recommendations:
        high_priority = [r for r in recommendations if r.priority >= 4]
        if high_priority:
            milestones.append(f"Complete {high_priority[0].title} within 2 weeks")

    # Performance milestones
    performance_insights = [i for i in insights if i.category == 'performance']
    if performance_insights:
        if performance_insights[0].insight_type == 'weakness':
            milestones.append("Improve test scores by 10% in next practice session")
        else:
            milestones.append("Maintain current performance level and increase difficulty")

    # Consistency milestones
    consistency_insights = [i for i in insights if i.category == 'consistency']
    if consistency_insights:
        milestones.append("Establish daily 30-minute study routine")

    # Default milestones if none generated
    if not milestones:
        milestones = [
            "Complete next practice test",
            "Review weak areas from last session",
            "Set up study schedule for next week"
        ]

    return milestones[:4]


def _generate_motivational_message(efficiency_score, consistency_score, session_count) -> str:
    """Generate personalized motivational message."""
    if efficiency_score > 0.8 and consistency_score > 0.8:
        return "Outstanding progress! Your consistent, efficient study approach is setting you up for certification success."
    elif efficiency_score > 0.6 and consistency_score > 0.6:
        return "Great work! You're building strong study habits. Keep up the momentum to reach your goals."
    elif session_count > 20:
        return "Your dedication shows! With this much practice, you're building the foundation for success."
    elif consistency_score > 0.7:
        return "Excellent consistency! Regular study sessions are key to long-term retention and success."
    elif efficiency_score > 0.7:
        return "Your study sessions are highly effective! Focus on maintaining this quality approach."
    else:
        return "Every step forward counts! Each study session brings you closer to your certification goals."


@router.get("/health")
async def health_check():
    """Health check endpoint for CertRatsAgent4 unified service."""
    return {
        'status': 'healthy',
        'service': 'Unified Intelligence Platform',
        'version': '1.0.0',
        'components': {
            'ai_study_assistant': 'AI Study Assistant - Active',
            'career_intelligence': 'Career & Cost Intelligence - Active',
            'salary_intelligence': 'Salary Intelligence - Active',
            'budget_optimizer': 'Budget Optimizer - Active'
        },
        'features': [
            'comprehensive_planning',
            'personalized_dashboard',
            'enterprise_analysis',
            'ai_study_recommendations',
            'career_pathfinding',
            'salary_intelligence',
            'roi_analysis',
            'budget_optimization'
        ],
        'timestamp': datetime.utcnow().isoformat()
    }
