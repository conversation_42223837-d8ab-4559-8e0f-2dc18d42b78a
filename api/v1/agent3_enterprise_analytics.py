"""FastAPI endpoints for Agent 3: Enterprise & Analytics Engine.

This module provides comprehensive API endpoints for enterprise analytics,
compliance automation, data intelligence, and organizational insights that
build upon Agent 2's AI Study Assistant capabilities.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from database import get_db
from services.enterprise_ai_service import EnterpriseAIService
from services.ai_study_assistant import OnDeviceAIStudyAssistant
from schemas.enterprise_ai import (
    UserSuccessPredictionResponse, ChurnRiskPredictionResponse,
    PerformanceForecastResponse, AutomatedInsightsResponse,
    PredictionRequest, OptimizationRequest, InsightRequest
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/agent3-enterprise-analytics", tags=["Agent 3: Enterprise Analytics"])


def get_current_admin_user() -> str:
    """Get current admin user ID from authentication context."""
    # TODO: Implement proper admin authentication
    return "admin_user_1"


@router.post("/enterprise-study-insights/{org_id}")
async def generate_enterprise_study_insights(
    org_id: int = Path(..., description="Organization ID"),
    db: Session = Depends(get_db)
):
    """Generate enterprise-wide study insights using Agent 2's AI capabilities."""
    try:
        admin_user = get_current_admin_user()
        logger.info(f"Generating enterprise study insights for org {org_id} by {admin_user}")
        
        enterprise_ai = EnterpriseAIService(db)
        insights = enterprise_ai.generate_enterprise_study_insights(org_id)
        
        if 'error' in insights:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=insights['error']
            )
        
        return {
            'status': 'success',
            'data': insights,
            'message': 'Enterprise study insights generated successfully'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating enterprise study insights: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate enterprise study insights: {str(e)}"
        )


@router.post("/skills-gap-analysis/{org_id}")
async def generate_skills_gap_analysis(
    org_id: int = Path(..., description="Organization ID"),
    department_id: Optional[int] = Query(None, description="Filter by department ID"),
    db: Session = Depends(get_db)
):
    """Generate comprehensive skills gap analysis for the organization."""
    try:
        admin_user = get_current_admin_user()
        logger.info(f"Generating skills gap analysis for org {org_id}, dept {department_id} by {admin_user}")
        
        enterprise_ai = EnterpriseAIService(db)
        analysis = enterprise_ai.generate_skills_gap_analysis(org_id, department_id)
        
        if 'error' in analysis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=analysis['error']
            )
        
        return {
            'status': 'success',
            'data': analysis,
            'message': 'Skills gap analysis completed successfully'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating skills gap analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate skills gap analysis: {str(e)}"
        )


@router.post("/compliance-report/{org_id}")
async def generate_compliance_report(
    org_id: int = Path(..., description="Organization ID"),
    compliance_type: str = Query(..., description="Compliance type (GDPR, HIPAA, SOX)"),
    period_start: str = Query(..., description="Report period start date (YYYY-MM-DD)"),
    period_end: str = Query(..., description="Report period end date (YYYY-MM-DD)"),
    db: Session = Depends(get_db)
):
    """Generate automated compliance reports (GDPR, HIPAA, SOX, etc.)."""
    try:
        admin_user = get_current_admin_user()
        logger.info(f"Generating {compliance_type} compliance report for org {org_id} by {admin_user}")
        
        # Parse dates
        try:
            start_date = datetime.strptime(period_start, '%Y-%m-%d')
            end_date = datetime.strptime(period_end, '%Y-%m-%d')
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )
        
        # Validate compliance type
        valid_types = ['GDPR', 'HIPAA', 'SOX']
        if compliance_type.upper() not in valid_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid compliance type. Must be one of: {', '.join(valid_types)}"
            )
        
        enterprise_ai = EnterpriseAIService(db)
        report = enterprise_ai.generate_compliance_automation_report(
            org_id, compliance_type, start_date, end_date
        )
        
        if 'error' in report:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=report['error']
            )
        
        return {
            'status': 'success',
            'data': report,
            'message': f'{compliance_type} compliance report generated successfully'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating compliance report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate compliance report: {str(e)}"
        )


@router.get("/data-intelligence/salary-benchmarks")
async def get_salary_intelligence(
    industry: Optional[str] = Query(None, description="Filter by industry"),
    location: Optional[str] = Query(None, description="Filter by location"),
    role: Optional[str] = Query(None, description="Filter by role"),
    certification: Optional[str] = Query(None, description="Filter by certification"),
    db: Session = Depends(get_db)
):
    """Get industry salary benchmarking and intelligence data."""
    try:
        admin_user = get_current_admin_user()
        logger.info(f"Generating salary intelligence by {admin_user}")
        
        # This would integrate with external salary data sources
        # For now, return mock data structure
        salary_intelligence = {
            'filters': {
                'industry': industry,
                'location': location,
                'role': role,
                'certification': certification
            },
            'salary_ranges': {
                'entry_level': {'min': 65000, 'max': 85000, 'median': 75000},
                'mid_level': {'min': 85000, 'max': 120000, 'median': 102500},
                'senior_level': {'min': 120000, 'max': 180000, 'median': 150000}
            },
            'certification_premium': {
                'Security+': 8500,
                'CISSP': 15000,
                'CISM': 12000,
                'CEH': 7500
            },
            'market_trends': {
                'growth_rate': 0.12,
                'demand_level': 'high',
                'skills_premium': ['cloud_security', 'incident_response', 'compliance']
            },
            'generated_at': datetime.now().isoformat()
        }
        
        return {
            'status': 'success',
            'data': salary_intelligence,
            'message': 'Salary intelligence data retrieved successfully'
        }
        
    except Exception as e:
        logger.error(f"Error generating salary intelligence: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate salary intelligence: {str(e)}"
        )


@router.get("/data-intelligence/market-trends")
async def get_market_trends(
    vertical: Optional[str] = Query(None, description="Industry vertical"),
    time_period: int = Query(12, description="Time period in months"),
    db: Session = Depends(get_db)
):
    """Get cybersecurity workforce trend analysis and market intelligence."""
    try:
        admin_user = get_current_admin_user()
        logger.info(f"Generating market trends analysis by {admin_user}")
        
        # This would analyze aggregated data from the platform
        # For now, return mock trend data
        market_trends = {
            'vertical': vertical,
            'time_period_months': time_period,
            'certification_trends': {
                'growing': ['Security+', 'CISSP', 'Cloud Security'],
                'stable': ['CISM', 'CISA'],
                'declining': ['Legacy certifications']
            },
            'skills_evolution': {
                'emerging_skills': ['Zero Trust', 'Cloud Security', 'DevSecOps'],
                'stable_skills': ['Network Security', 'Risk Management'],
                'declining_skills': ['Legacy firewall management']
            },
            'job_market_analysis': {
                'demand_growth': 0.15,
                'supply_shortage': 0.23,
                'salary_inflation': 0.08
            },
            'future_predictions': {
                'next_12_months': 'Continued high demand for cloud security skills',
                'skills_gap_forecast': 'Growing gap in advanced threat detection',
                'certification_recommendations': ['Security+', 'CISSP', 'Cloud certifications']
            },
            'generated_at': datetime.now().isoformat()
        }
        
        return {
            'status': 'success',
            'data': market_trends,
            'message': 'Market trends analysis completed successfully'
        }
        
    except Exception as e:
        logger.error(f"Error generating market trends: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate market trends: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """Health check endpoint for Agent 3 Enterprise Analytics service."""
    return {
        'status': 'healthy',
        'service': 'Agent 3: Enterprise & Analytics Engine',
        'version': '1.0.0',
        'features': [
            'enterprise_study_insights',
            'skills_gap_analysis', 
            'compliance_automation',
            'data_intelligence',
            'salary_benchmarking',
            'market_trend_analysis',
            'agent2_integration'
        ],
        'dependencies': {
            'agent2_ai_study_assistant': 'integrated',
            'enterprise_ai_service': 'active',
            'database': 'connected'
        },
        'timestamp': datetime.now().isoformat()
    }
