"""Study session API endpoints matching PRD requirements"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import logging

from database import get_db
from api.v1.auth import get_current_user
from models.user import User
from models.progress_tracking import StudySession
from schemas.study_session import (
    StudySessionCreate,
    StudySessionResponse,
    StudySessionUpdate,
    StudyGoalCreate,
    StudyGoalResponse
)
from services.progress_tracking import ProgressTrackingService

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/study", tags=["Study Sessions"])


@router.post("/sessions", response_model=StudySessionResponse, status_code=status.HTTP_201_CREATED)
async def log_study_session(
    session_data: StudySessionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Log a new study session (POST /api/v1/study/sessions)"""
    try:
        logger.info(f"Logging study session for user {current_user.user_id}")
        
        service = ProgressTrackingService(db)
        
        # Start the study session
        session = service.start_study_session(
            user_id=current_user.user_id,
            session_type=session_data.session_type.value,
            certification_id=session_data.certification_id,
            topic=session_data.topic,
            planned_duration_minutes=session_data.planned_duration_minutes
        )
        
        # If this is a completed session (has end time), end it immediately
        if hasattr(session_data, 'actual_duration_minutes') and session_data.actual_duration_minutes:
            session = service.end_study_session(
                session_id=session.id,
                user_id=current_user.user_id,
                progress_after=getattr(session_data, 'progress_percentage', 0.0),
                notes=getattr(session_data, 'notes', None)
            )
        
        return session.to_dict()
        
    except Exception as e:
        logger.error(f"Error logging study session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to log study session"
        )


@router.get("/sessions", response_model=List[StudySessionResponse])
async def get_study_history(
    certification_id: Optional[int] = Query(None, description="Filter by certification ID"),
    session_type: Optional[str] = Query(None, description="Filter by session type"),
    days_back: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    limit: int = Query(50, ge=1, le=200, description="Maximum number of sessions to return"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user study history (GET /api/v1/study/sessions)"""
    try:
        logger.info(f"Retrieving study history for user {current_user.user_id}")
        
        # Calculate cutoff date
        cutoff_date = datetime.utcnow() - timedelta(days=days_back)
        
        # Build query
        query = db.query(StudySession).filter(
            StudySession.user_id == current_user.user_id,
            StudySession.started_at >= cutoff_date
        )
        
        # Apply filters
        if certification_id:
            query = query.filter(StudySession.certification_id == certification_id)
        
        if session_type:
            query = query.filter(StudySession.session_type == session_type)
        
        # Get sessions ordered by most recent first
        sessions = query.order_by(StudySession.started_at.desc()).limit(limit).all()
        
        return [session.to_dict() for session in sessions]
        
    except Exception as e:
        logger.error(f"Error retrieving study history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve study history"
        )


@router.get("/progress", response_model=dict)
async def get_study_progress(
    certification_id: Optional[int] = Query(None, description="Filter by certification ID"),
    period: str = Query("month", description="Time period: week, month, quarter, year"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get study progress analytics (GET /api/v1/study/progress)"""
    try:
        logger.info(f"Retrieving study progress for user {current_user.user_id}")
        
        service = ProgressTrackingService(db)
        
        # Calculate time period
        period_days = {
            "week": 7,
            "month": 30,
            "quarter": 90,
            "year": 365
        }.get(period, 30)
        
        cutoff_date = datetime.utcnow() - timedelta(days=period_days)
        
        # Build base query
        query = db.query(StudySession).filter(
            StudySession.user_id == current_user.user_id,
            StudySession.started_at >= cutoff_date,
            StudySession.ended_at.isnot(None)  # Only completed sessions
        )
        
        if certification_id:
            query = query.filter(StudySession.certification_id == certification_id)
        
        sessions = query.all()
        
        # Calculate analytics
        total_sessions = len(sessions)
        total_minutes = sum(s.duration_minutes or 0 for s in sessions)
        total_hours = round(total_minutes / 60, 2)
        
        # Average session duration
        avg_session_duration = round(total_minutes / total_sessions, 1) if total_sessions > 0 else 0
        
        # Study days (unique dates)
        study_dates = set(s.started_at.date() for s in sessions)
        total_study_days = len(study_dates)
        
        # Progress by session type
        session_types = {}
        for session in sessions:
            session_type = session.session_type
            if session_type not in session_types:
                session_types[session_type] = {
                    "count": 0,
                    "total_minutes": 0
                }
            session_types[session_type]["count"] += 1
            session_types[session_type]["total_minutes"] += session.duration_minutes or 0
        
        # Recent activity (last 7 days)
        recent_cutoff = datetime.utcnow() - timedelta(days=7)
        recent_sessions = [s for s in sessions if s.started_at >= recent_cutoff]
        recent_minutes = sum(s.duration_minutes or 0 for s in recent_sessions)
        
        # Calculate study streak
        study_streak = service.calculate_study_streak(current_user.user_id)
        
        return {
            "period": period,
            "period_days": period_days,
            "total_sessions": total_sessions,
            "total_study_hours": total_hours,
            "total_study_minutes": total_minutes,
            "total_study_days": total_study_days,
            "average_session_duration_minutes": avg_session_duration,
            "session_types": session_types,
            "recent_activity": {
                "last_7_days_sessions": len(recent_sessions),
                "last_7_days_minutes": recent_minutes,
                "last_7_days_hours": round(recent_minutes / 60, 2)
            },
            "study_streak": {
                "current_streak_days": study_streak.get("current_streak", 0),
                "longest_streak_days": study_streak.get("longest_streak", 0)
            },
            "certification_id": certification_id
        }
        
    except Exception as e:
        logger.error(f"Error retrieving study progress: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve study progress"
        )


@router.post("/goals", response_model=StudyGoalResponse, status_code=status.HTTP_201_CREATED)
async def set_study_goal(
    goal_data: StudyGoalCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Set learning goals (POST /api/v1/study/goals)"""
    try:
        logger.info(f"Setting study goal for user {current_user.user_id}")
        
        service = ProgressTrackingService(db)
        
        goal = service.create_learning_goal(
            user_id=current_user.user_id,
            title=goal_data.title,
            goal_type=goal_data.goal_type.value,
            target_value=goal_data.target_value,
            target_unit=goal_data.target_unit,
            target_date=goal_data.target_date,
            certification_id=goal_data.certification_id,
            description=goal_data.description
        )
        
        return goal.to_dict()
        
    except Exception as e:
        logger.error(f"Error setting study goal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set study goal"
        )


@router.get("/goals", response_model=List[StudyGoalResponse])
async def get_study_goals(
    status_filter: Optional[str] = Query(None, alias="status", description="Filter by goal status"),
    certification_id: Optional[int] = Query(None, description="Filter by certification ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's study goals"""
    try:
        logger.info(f"Retrieving study goals for user {current_user.user_id}")
        
        from models.progress_tracking import LearningGoal
        
        query = db.query(LearningGoal).filter(LearningGoal.user_id == current_user.user_id)
        
        if status_filter:
            query = query.filter(LearningGoal.status == status_filter)
        
        if certification_id:
            query = query.filter(LearningGoal.certification_id == certification_id)
        
        goals = query.order_by(LearningGoal.created_at.desc()).all()
        
        return [goal.to_dict() for goal in goals]
        
    except Exception as e:
        logger.error(f"Error retrieving study goals: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve study goals"
        )
