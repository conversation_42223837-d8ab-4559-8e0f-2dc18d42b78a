"""Security Career Framework API endpoints.

This module provides comprehensive API endpoints for security career paths,
job types, seniority levels, and skill matrices based on <PERSON>'s
8 security areas and industry standards.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from database import get_db
from models.security_career_framework import (
    SecurityJobType, SecurityCareerPath, SecuritySkillMatrix, SecurityMarketData,
    SecurityArea, SeniorityLevel, JobFamily
)
from schemas.security_career_framework import (
    SecurityJobTypeCreate, SecurityJobTypeUpdate, SecurityJobTypeResponse, SecurityJobTypeListResponse,
    SecurityCareerPathCreate, SecurityCareerPathResponse,
    SecuritySkillMatrixCreate, SecuritySkillMatrixResponse,
    SecurityMarketDataCreate, SecurityMarketDataResponse,
    SecurityAreaFilter, CareerRecommendationRequest, CareerRecommendationResponse,
    SecurityAreaSummary, SecurityCareerAnalytics,
    SecurityAreaEnum, SeniorityLevelEnum, DemandLevelEnum
)

router = APIRouter(prefix="/security-career-framework", tags=["Security Career Framework"])


# Security Job Types endpoints

@router.get("/job-types", response_model=SecurityJobTypeListResponse)
async def get_security_job_types(
    security_area: Optional[SecurityAreaEnum] = Query(None, description="Filter by security area"),
    seniority_level: Optional[SeniorityLevelEnum] = Query(None, description="Filter by seniority level"),
    job_family: Optional[str] = Query(None, description="Filter by job family"),
    demand_level: Optional[DemandLevelEnum] = Query(None, description="Filter by demand level"),
    remote_friendly: Optional[bool] = Query(None, description="Filter by remote work friendly"),
    min_salary: Optional[float] = Query(None, ge=0, description="Minimum salary filter"),
    max_salary: Optional[float] = Query(None, ge=0, description="Maximum salary filter"),
    min_experience: Optional[int] = Query(None, ge=0, description="Minimum experience filter"),
    max_experience: Optional[int] = Query(None, ge=0, description="Maximum experience filter"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """Get security job types with optional filtering."""
    try:
        query = db.query(SecurityJobType).filter(SecurityJobType.is_active == True)
        
        # Apply filters
        if security_area:
            query = query.filter(SecurityJobType.security_area == security_area.value)
        if seniority_level:
            query = query.filter(SecurityJobType.seniority_level == seniority_level.value)
        if job_family:
            query = query.filter(SecurityJobType.job_family == job_family)
        if demand_level:
            query = query.filter(SecurityJobType.demand_level == demand_level.value)
        if remote_friendly is not None:
            query = query.filter(SecurityJobType.remote_friendly == remote_friendly)
        if min_salary is not None:
            query = query.filter(SecurityJobType.salary_min >= min_salary)
        if max_salary is not None:
            query = query.filter(SecurityJobType.salary_max <= max_salary)
        if min_experience is not None:
            query = query.filter(SecurityJobType.min_years_experience >= min_experience)
        if max_experience is not None:
            query = query.filter(SecurityJobType.max_years_experience <= max_experience)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        job_types = query.offset(skip).limit(limit).all()
        
        # Convert to response format
        job_type_responses = []
        for job_type in job_types:
            job_dict = job_type.to_dict()
            job_dict['salary_range'] = {
                'min': job_type.salary_min,
                'max': job_type.salary_max,
                'currency': job_type.salary_currency
            }
            job_dict['career_progression'] = {
                'from_roles': job_type.career_progression_from or [],
                'to_roles': job_type.career_progression_to or []
            }
            job_type_responses.append(SecurityJobTypeResponse(**job_dict))
        
        filters_applied = {
            'security_area': security_area.value if security_area else None,
            'seniority_level': seniority_level.value if seniority_level else None,
            'job_family': job_family,
            'demand_level': demand_level.value if demand_level else None,
            'remote_friendly': remote_friendly,
            'salary_range': {'min': min_salary, 'max': max_salary},
            'experience_range': {'min': min_experience, 'max': max_experience}
        }
        
        return SecurityJobTypeListResponse(
            job_types=job_type_responses,
            total_count=total_count,
            filters_applied=filters_applied
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving job types: {str(e)}")


@router.get("/job-types/{job_type_id}", response_model=SecurityJobTypeResponse)
async def get_security_job_type(
    job_type_id: int = Path(..., description="Job type ID"),
    db: Session = Depends(get_db)
):
    """Get a specific security job type by ID."""
    try:
        job_type = db.query(SecurityJobType).filter(
            SecurityJobType.id == job_type_id,
            SecurityJobType.is_active == True
        ).first()
        
        if not job_type:
            raise HTTPException(status_code=404, detail="Job type not found")
        
        job_dict = job_type.to_dict()
        job_dict['salary_range'] = {
            'min': job_type.salary_min,
            'max': job_type.salary_max,
            'currency': job_type.salary_currency
        }
        job_dict['career_progression'] = {
            'from_roles': job_type.career_progression_from or [],
            'to_roles': job_type.career_progression_to or []
        }
        
        return SecurityJobTypeResponse(**job_dict)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving job type: {str(e)}")


@router.post("/job-types", response_model=SecurityJobTypeResponse)
async def create_security_job_type(
    job_type_data: SecurityJobTypeCreate,
    db: Session = Depends(get_db)
):
    """Create a new security job type."""
    try:
        # Create new job type
        job_type = SecurityJobType(
            title=job_type_data.title,
            security_area=job_type_data.security_area.value,
            job_family=job_type_data.job_family,
            seniority_level=job_type_data.seniority_level.value,
            description=job_type_data.description,
            responsibilities=job_type_data.responsibilities,
            required_skills=job_type_data.required_skills,
            preferred_skills=job_type_data.preferred_skills,
            min_years_experience=job_type_data.min_years_experience,
            max_years_experience=job_type_data.max_years_experience,
            education_requirements=job_type_data.education_requirements,
            required_certifications=job_type_data.required_certifications,
            preferred_certifications=job_type_data.preferred_certifications,
            salary_min=job_type_data.salary_min,
            salary_max=job_type_data.salary_max,
            salary_currency=job_type_data.salary_currency,
            career_progression_from=job_type_data.career_progression_from,
            career_progression_to=job_type_data.career_progression_to,
            demand_level=job_type_data.demand_level.value,
            remote_friendly=job_type_data.remote_friendly
        )
        
        db.add(job_type)
        db.commit()
        db.refresh(job_type)
        
        job_dict = job_type.to_dict()
        job_dict['salary_range'] = {
            'min': job_type.salary_min,
            'max': job_type.salary_max,
            'currency': job_type.salary_currency
        }
        job_dict['career_progression'] = {
            'from_roles': job_type.career_progression_from or [],
            'to_roles': job_type.career_progression_to or []
        }
        
        return SecurityJobTypeResponse(**job_dict)
    
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating job type: {str(e)}")


@router.put("/job-types/{job_type_id}", response_model=SecurityJobTypeResponse)
async def update_security_job_type(
    job_type_id: int = Path(..., description="Job type ID"),
    job_type_data: SecurityJobTypeUpdate = None,
    db: Session = Depends(get_db)
):
    """Update a security job type."""
    try:
        job_type = db.query(SecurityJobType).filter(
            SecurityJobType.id == job_type_id,
            SecurityJobType.is_active == True
        ).first()
        
        if not job_type:
            raise HTTPException(status_code=404, detail="Job type not found")
        
        # Update fields if provided
        update_data = job_type_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(job_type, field):
                setattr(job_type, field, value)
        
        db.commit()
        db.refresh(job_type)
        
        job_dict = job_type.to_dict()
        job_dict['salary_range'] = {
            'min': job_type.salary_min,
            'max': job_type.salary_max,
            'currency': job_type.salary_currency
        }
        job_dict['career_progression'] = {
            'from_roles': job_type.career_progression_from or [],
            'to_roles': job_type.career_progression_to or []
        }
        
        return SecurityJobTypeResponse(**job_dict)
    
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating job type: {str(e)}")


# Security Career Paths endpoints

@router.get("/career-paths", response_model=List[SecurityCareerPathResponse])
async def get_security_career_paths(
    security_area: Optional[SecurityAreaEnum] = Query(None, description="Filter by security area"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """Get security career paths with optional filtering."""
    try:
        query = db.query(SecurityCareerPath).filter(SecurityCareerPath.is_active == True)
        
        if security_area:
            query = query.filter(SecurityCareerPath.security_area == security_area.value)
        
        career_paths = query.offset(skip).limit(limit).all()
        
        return [SecurityCareerPathResponse.from_orm(path) for path in career_paths]
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving career paths: {str(e)}")


@router.get("/career-paths/{path_id}", response_model=SecurityCareerPathResponse)
async def get_security_career_path(
    path_id: int = Path(..., description="Career path ID"),
    db: Session = Depends(get_db)
):
    """Get a specific security career path by ID."""
    try:
        career_path = db.query(SecurityCareerPath).filter(
            SecurityCareerPath.id == path_id,
            SecurityCareerPath.is_active == True
        ).first()
        
        if not career_path:
            raise HTTPException(status_code=404, detail="Career path not found")
        
        return SecurityCareerPathResponse.from_orm(career_path)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving career path: {str(e)}")


# Security Skill Matrix endpoints

@router.get("/skill-matrix", response_model=List[SecuritySkillMatrixResponse])
async def get_security_skill_matrix(
    security_area: Optional[SecurityAreaEnum] = Query(None, description="Filter by security area"),
    job_family: Optional[str] = Query(None, description="Filter by job family"),
    seniority_level: Optional[SeniorityLevelEnum] = Query(None, description="Filter by seniority level"),
    db: Session = Depends(get_db)
):
    """Get security skill matrix with optional filtering."""
    try:
        query = db.query(SecuritySkillMatrix)
        
        if security_area:
            query = query.filter(SecuritySkillMatrix.security_area == security_area.value)
        if job_family:
            query = query.filter(SecuritySkillMatrix.job_family == job_family)
        if seniority_level:
            query = query.filter(SecuritySkillMatrix.seniority_level == seniority_level.value)
        
        skill_matrices = query.all()
        
        # Convert to response format with computed fields
        responses = []
        for matrix in skill_matrices:
            matrix_dict = matrix.to_dict()
            matrix_dict['skills'] = {
                'core': matrix.core_skills or [],
                'advanced': matrix.advanced_skills or [],
                'leadership': matrix.leadership_skills or [],
                'business': matrix.business_skills or []
            }
            matrix_dict['certifications'] = {
                'entry': matrix.entry_certifications or [],
                'intermediate': matrix.intermediate_certifications or [],
                'advanced': matrix.advanced_certifications or [],
                'expert': matrix.expert_certifications or []
            }
            matrix_dict['tools'] = {
                'required': matrix.required_tools or [],
                'preferred': matrix.preferred_tools or []
            }
            responses.append(SecuritySkillMatrixResponse(**matrix_dict))
        
        return responses
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving skill matrix: {str(e)}")


# Career Recommendations endpoint

@router.post("/career-recommendations", response_model=CareerRecommendationResponse)
async def get_career_recommendations(
    request: CareerRecommendationRequest,
    db: Session = Depends(get_db)
):
    """Get personalized career recommendations based on user profile."""
    try:
        # This is a simplified implementation - in production, this would use
        # machine learning algorithms to provide personalized recommendations
        
        # Find matching job types based on experience and preferences
        query = db.query(SecurityJobType).filter(SecurityJobType.is_active == True)
        
        if request.security_area:
            query = query.filter(SecurityJobType.security_area == request.security_area.value)
        
        if request.target_seniority:
            query = query.filter(SecurityJobType.seniority_level == request.target_seniority.value)
        
        # Filter by experience level
        query = query.filter(SecurityJobType.min_years_experience <= request.years_experience)
        
        if request.salary_expectations and request.salary_expectations.min:
            query = query.filter(SecurityJobType.salary_min >= request.salary_expectations.min)
        
        recommended_jobs = query.limit(10).all()
        
        # Get relevant career paths
        career_paths_query = db.query(SecurityCareerPath).filter(SecurityCareerPath.is_active == True)
        if request.security_area:
            career_paths_query = career_paths_query.filter(
                SecurityCareerPath.security_area == request.security_area.value
            )
        
        career_paths = career_paths_query.limit(5).all()
        
        # Analyze skill gaps (simplified)
        skill_gaps = []
        if recommended_jobs:
            all_required_skills = set()
            for job in recommended_jobs:
                all_required_skills.update(job.required_skills or [])
            
            user_skills = set(request.preferred_skills)
            skill_gaps = list(all_required_skills - user_skills)
        
        # Certification recommendations
        cert_recommendations = []
        if recommended_jobs:
            all_certs = set()
            for job in recommended_jobs:
                all_certs.update(job.required_certifications or [])
                all_certs.update(job.preferred_certifications or [])
            
            user_certs = set(request.current_certifications)
            cert_recommendations = list(all_certs - user_certs)
        
        # Convert to response format
        job_responses = []
        for job in recommended_jobs:
            job_dict = job.to_dict()
            job_dict['salary_range'] = {
                'min': job.salary_min,
                'max': job.salary_max,
                'currency': job.salary_currency
            }
            job_dict['career_progression'] = {
                'from_roles': job.career_progression_from or [],
                'to_roles': job.career_progression_to or []
            }
            job_responses.append(SecurityJobTypeResponse(**job_dict))
        
        path_responses = [SecurityCareerPathResponse.from_orm(path) for path in career_paths]
        
        return CareerRecommendationResponse(
            recommended_roles=job_responses,
            career_paths=path_responses,
            skill_gaps=skill_gaps[:10],  # Top 10 skill gaps
            certification_recommendations=cert_recommendations[:10],  # Top 10 certs
            market_insights={
                'total_opportunities': len(recommended_jobs),
                'average_salary': sum(job.salary_min or 0 for job in recommended_jobs) / len(recommended_jobs) if recommended_jobs else 0,
                'remote_friendly_percentage': sum(1 for job in recommended_jobs if job.remote_friendly) / len(recommended_jobs) * 100 if recommended_jobs else 0
            },
            next_steps=[
                "Review recommended job roles and their requirements",
                "Identify skill gaps and create a learning plan",
                "Pursue recommended certifications",
                "Build a portfolio showcasing relevant skills",
                "Network with professionals in your target security area"
            ]
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating recommendations: {str(e)}")


# Analytics endpoints

@router.get("/analytics", response_model=SecurityCareerAnalytics)
async def get_security_career_analytics(db: Session = Depends(get_db)):
    """Get comprehensive analytics for security careers."""
    try:
        # Get basic counts
        total_job_types = db.query(SecurityJobType).filter(SecurityJobType.is_active == True).count()
        total_career_paths = db.query(SecurityCareerPath).filter(SecurityCareerPath.is_active == True).count()
        
        # Get security area summaries
        area_summaries = []
        for area in SecurityAreaEnum:
            area_jobs = db.query(SecurityJobType).filter(
                SecurityJobType.security_area == area.value,
                SecurityJobType.is_active == True
            ).all()
            
            if area_jobs:
                avg_salary = sum(job.salary_min or 0 for job in area_jobs) / len(area_jobs)
                demand_counts = {}
                for job in area_jobs:
                    demand_counts[job.demand_level] = demand_counts.get(job.demand_level, 0) + 1
                
                overall_demand = max(demand_counts.items(), key=lambda x: x[1])[0] if demand_counts else 'medium'
                
                # Get top skills
                all_skills = []
                for job in area_jobs:
                    all_skills.extend(job.required_skills or [])
                
                skill_counts = {}
                for skill in all_skills:
                    skill_counts[skill] = skill_counts.get(skill, 0) + 1
                
                top_skills = sorted(skill_counts.items(), key=lambda x: x[1], reverse=True)[:5]
                top_skills = [skill[0] for skill in top_skills]
                
                area_summaries.append(SecurityAreaSummary(
                    security_area=area,
                    total_job_types=len(area_jobs),
                    average_salary=avg_salary,
                    demand_level=DemandLevelEnum(overall_demand),
                    growth_rate=5.0,  # Mock data
                    top_skills=top_skills,
                    top_certifications=['Security+', 'CISSP'],  # Mock data
                    remote_work_percentage=75.0  # Mock data
                ))
        
        return SecurityCareerAnalytics(
            total_job_types=total_job_types,
            total_career_paths=total_career_paths,
            security_area_summaries=area_summaries,
            trending_skills=['Cloud Security', 'DevSecOps', 'AI/ML Security'],
            highest_demand_roles=['SOC Analyst', 'Security Engineer', 'Penetration Tester'],
            highest_paying_roles=['CISO', 'Security Architect', 'Principal Security Engineer'],
            fastest_growing_areas=['Cloud Security', 'DevSecOps', 'AI Security'],
            certification_popularity={'Security+': 100, 'CISSP': 85, 'CySA+': 70},
            market_trends={'remote_work_adoption': 80.0, 'salary_growth': 8.5},
            generated_at=datetime.utcnow()
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating analytics: {str(e)}")


# Utility endpoints

@router.get("/security-areas", response_model=List[str])
async def get_security_areas():
    """Get list of all security areas."""
    return [area.value for area in SecurityAreaEnum]


@router.get("/seniority-levels", response_model=List[str])
async def get_seniority_levels():
    """Get list of all seniority levels."""
    return [level.value for level in SeniorityLevelEnum]


@router.get("/job-families/{security_area}", response_model=List[str])
async def get_job_families_by_area(
    security_area: SecurityAreaEnum = Path(..., description="Security area"),
    db: Session = Depends(get_db)
):
    """Get job families for a specific security area."""
    try:
        job_families = db.query(SecurityJobType.job_family).filter(
            SecurityJobType.security_area == security_area.value,
            SecurityJobType.is_active == True
        ).distinct().all()
        
        return [family[0] for family in job_families]
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving job families: {str(e)}")
