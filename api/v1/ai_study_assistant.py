"""FastAPI endpoints for AI-powered study assistant.

This module provides API endpoints for on-device AI recommendations,
adaptive learning paths, intelligent insights, and personalized study assistance.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from datetime import datetime

from database import get_db
from services.ai_study_assistant import OnDeviceAIStudyAssistant
from schemas.ai_study_assistant import (
    StudyRecommendationResponse, LearningInsightResponse, AdaptivePathResponse,
    PracticeQuestionResponse, StudyFeedbackResponse, StudyFeedbackRequest,
    GenerateQuestionsRequest, AdaptivePathRequest
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/ai-assistant", tags=["AI Study Assistant"])


def get_current_user_id() -> str:
    """Get current user ID from authentication context."""
    # TODO: Implement proper authentication
    return "test_user_1"


@router.get("/recommendations", response_model=List[StudyRecommendationResponse])
async def get_study_recommendations(
    context: str = Query("general", description="Context for recommendations"),
    limit: int = Query(10, ge=1, le=20, description="Maximum number of recommendations"),
    db: Session = Depends(get_db)
):
    """Get personalized study recommendations based on user data and AI analysis."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Generating AI recommendations for user {user_id}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        recommendations = ai_assistant.generate_personalized_recommendations(
            user_id=user_id,
            context=context
        )
        
        # Convert to response format
        response_recommendations = []
        for rec in recommendations[:limit]:
            response_recommendations.append(StudyRecommendationResponse(
                type=rec.type,
                priority=rec.priority,
                title=rec.title,
                description=rec.description,
                reasoning=rec.reasoning,
                estimated_time_minutes=rec.estimated_time_minutes,
                confidence_score=rec.confidence_score,
                metadata=rec.metadata
            ))
        
        return response_recommendations
        
    except Exception as e:
        logger.error(f"Error generating study recommendations: {e}")
        raise HTTPException(status_code=500, detail="Error generating study recommendations")


@router.get("/insights", response_model=List[LearningInsightResponse])
async def get_learning_insights(
    category: Optional[str] = Query(None, description="Filter by insight category"),
    db: Session = Depends(get_db)
):
    """Get AI-powered learning insights and pattern analysis."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Generating learning insights for user {user_id}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        insights = ai_assistant.analyze_learning_patterns(user_id)
        
        # Filter by category if specified
        if category:
            insights = [insight for insight in insights if insight.category == category]
        
        # Convert to response format
        response_insights = []
        for insight in insights:
            response_insights.append(LearningInsightResponse(
                category=insight.category,
                insight_type=insight.insight_type,
                title=insight.title,
                description=insight.description,
                evidence=insight.evidence,
                actionable_steps=insight.actionable_steps,
                impact_score=insight.impact_score
            ))
        
        return response_insights
        
    except Exception as e:
        logger.error(f"Error generating learning insights: {e}")
        raise HTTPException(status_code=500, detail="Error generating learning insights")


@router.post("/adaptive-path", response_model=AdaptivePathResponse)
async def generate_adaptive_learning_path(
    request: AdaptivePathRequest,
    db: Session = Depends(get_db)
):
    """Generate an adaptive learning path tailored to the user's current state and goals."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Generating adaptive learning path for user {user_id}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        
        target_date = None
        if request.target_date:
            target_date = datetime.fromisoformat(request.target_date.replace('Z', '+00:00'))
        
        adaptive_path = ai_assistant.generate_adaptive_learning_path(
            user_id=user_id,
            certification_id=request.certification_id,
            target_date=target_date
        )
        
        return AdaptivePathResponse(
            path_id=adaptive_path.path_id,
            name=adaptive_path.name,
            description=adaptive_path.description,
            difficulty_level=adaptive_path.difficulty_level,
            estimated_duration_weeks=adaptive_path.estimated_duration_weeks,
            topics=adaptive_path.topics,
            prerequisites=adaptive_path.prerequisites,
            success_probability=adaptive_path.success_probability,
            personalization_factors=adaptive_path.personalization_factors
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating adaptive learning path: {e}")
        raise HTTPException(status_code=500, detail="Error generating adaptive learning path")


@router.post("/practice-questions", response_model=List[PracticeQuestionResponse])
async def generate_practice_questions(
    request: GenerateQuestionsRequest,
    db: Session = Depends(get_db)
):
    """Generate AI-powered practice questions for a specific topic."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Generating practice questions for topic: {request.topic}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        questions = ai_assistant.generate_practice_questions(
            topic=request.topic,
            difficulty=request.difficulty,
            count=request.count
        )
        
        # Convert to response format
        response_questions = []
        for question in questions:
            response_questions.append(PracticeQuestionResponse(
                id=question['id'],
                question=question['question'],
                type=question['type'],
                difficulty=question['difficulty'],
                topic=question['topic'],
                options=question['options'],
                correct_answer=question['correct_answer'],
                explanation=question['explanation']
            ))
        
        return response_questions
        
    except Exception as e:
        logger.error(f"Error generating practice questions: {e}")
        raise HTTPException(status_code=500, detail="Error generating practice questions")


@router.post("/study-feedback", response_model=StudyFeedbackResponse)
async def provide_study_feedback(
    request: StudyFeedbackRequest,
    db: Session = Depends(get_db)
):
    """Provide AI-powered feedback and suggestions for a study session."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Providing study feedback for user {user_id}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        feedback = ai_assistant.provide_study_feedback(
            user_id=user_id,
            session_data=request.session_data
        )
        
        return StudyFeedbackResponse(
            overall_rating=feedback['overall_rating'],
            strengths=feedback['strengths'],
            improvements=feedback['improvements'],
            next_session_suggestions=feedback['next_session_suggestions'],
            technique_recommendations=feedback['technique_recommendations'],
            motivation_boost=feedback['motivation_boost']
        )
        
    except Exception as e:
        logger.error(f"Error providing study feedback: {e}")
        raise HTTPException(status_code=500, detail="Error providing study feedback")


@router.get("/study-techniques", response_model=Dict[str, Any])
async def get_study_techniques(
    learning_style: Optional[str] = Query(None, description="Filter by learning style"),
    effectiveness_threshold: float = Query(0.7, ge=0.0, le=1.0, description="Minimum effectiveness threshold"),
    db: Session = Depends(get_db)
):
    """Get AI-recommended study techniques based on user preferences and effectiveness."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Getting study techniques for user {user_id}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        
        # Get all techniques from knowledge base
        all_techniques = ai_assistant.knowledge_base['study_techniques']
        
        # Filter by effectiveness threshold
        filtered_techniques = {
            name: info for name, info in all_techniques.items()
            if info['effectiveness'] >= effectiveness_threshold
        }
        
        # Filter by learning style if specified
        if learning_style:
            # This is a simplified filter - in practice, you'd have more sophisticated matching
            style_compatible = {
                'visual': ['active_recall', 'spaced_repetition'],
                'auditory': ['feynman_technique'],
                'kinesthetic': ['pomodoro', 'hands_on'],
                'mixed': list(all_techniques.keys())
            }
            
            compatible_techniques = style_compatible.get(learning_style, list(all_techniques.keys()))
            filtered_techniques = {
                name: info for name, info in filtered_techniques.items()
                if name in compatible_techniques
            }
        
        return {
            'techniques': filtered_techniques,
            'user_learning_style': learning_style or 'mixed',
            'total_techniques': len(filtered_techniques),
            'effectiveness_threshold': effectiveness_threshold
        }
        
    except Exception as e:
        logger.error(f"Error getting study techniques: {e}")
        raise HTTPException(status_code=500, detail="Error getting study techniques")


@router.get("/knowledge-assessment/{certification_id}")
async def assess_knowledge_level(
    certification_id: int = Path(..., description="Certification ID"),
    db: Session = Depends(get_db)
):
    """Assess user's current knowledge level for a specific certification."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Assessing knowledge level for user {user_id}, cert {certification_id}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        user_data = ai_assistant._gather_user_data(user_id)
        
        # Get certification
        from models.certification import Certification
        certification = db.query(Certification).filter(
            Certification.id == certification_id
        ).first()
        
        if not certification:
            raise HTTPException(status_code=404, detail="Certification not found")
        
        knowledge_level = ai_assistant._assess_knowledge_level(user_data, certification)
        
        # Get additional insights
        weak_areas = ai_assistant._identify_weak_areas(user_data, certification)
        strong_areas = ai_assistant._identify_strong_areas(user_data, certification)
        
        level_description = {
            'beginner': 'You are just starting your journey in this domain',
            'intermediate': 'You have solid foundational knowledge',
            'advanced': 'You have strong expertise in this area'
        }
        
        difficulty_level = ai_assistant._determine_difficulty_level(knowledge_level)
        
        return {
            'certification_id': certification_id,
            'certification_name': certification.name,
            'knowledge_level': knowledge_level,
            'knowledge_percentage': knowledge_level * 100,
            'difficulty_level': difficulty_level,
            'level_description': level_description.get(difficulty_level, 'Assessment in progress'),
            'weak_areas': weak_areas,
            'strong_areas': strong_areas,
            'assessment_date': datetime.now().isoformat(),
            'recommendations': [
                f'Focus on {area}' for area in weak_areas[:3]
            ] if weak_areas else ['Continue building on your strong foundation']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assessing knowledge level: {e}")
        raise HTTPException(status_code=500, detail="Error assessing knowledge level")


@router.post("/train-models")
async def train_ai_models(
    user_id: Optional[str] = Query(None, description="Specific user ID to train on"),
    db: Session = Depends(get_db)
):
    """Train or update AI models with latest user data."""
    try:
        current_user_id = get_current_user_id()
        target_user_id = user_id or current_user_id
        
        logger.info(f"Training AI models for user {target_user_id}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        ai_assistant.train_models(target_user_id)
        
        return {
            'message': 'AI models trained successfully',
            'user_id': target_user_id,
            'training_date': datetime.now().isoformat(),
            'models_updated': [
                'performance_predictor',
                'difficulty_estimator', 
                'topic_recommender'
            ]
        }
        
    except Exception as e:
        logger.error(f"Error training AI models: {e}")
        raise HTTPException(status_code=500, detail="Error training AI models")


@router.get("/learning-style-analysis")
async def analyze_learning_style(
    db: Session = Depends(get_db)
):
    """Analyze user's learning style based on study session data."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Analyzing learning style for user {user_id}")
        
        ai_assistant = OnDeviceAIStudyAssistant(db)
        user_data = ai_assistant._gather_user_data(user_id)
        
        learning_style = ai_assistant._identify_learning_style(user_data)
        optimal_session_length = ai_assistant._calculate_optimal_session_length(user_data)
        best_study_times = ai_assistant._identify_best_study_times(user_data)
        
        # Get session type distribution
        sessions = user_data['sessions']
        session_types = [s.session_type for s in sessions if s.session_type]
        
        type_distribution = {}
        if session_types:
            import pandas as pd
            type_counts = pd.Series(session_types).value_counts()
            total_sessions = len(session_types)
            type_distribution = {
                session_type: {
                    'count': int(count),
                    'percentage': round((count / total_sessions) * 100, 1)
                }
                for session_type, count in type_counts.items()
            }
        
        style_descriptions = {
            'visual': 'You learn best through reading, diagrams, and visual materials',
            'auditory': 'You learn best through discussions and verbal explanations',
            'kinesthetic': 'You learn best through hands-on practice and experimentation',
            'analytical': 'You learn best through testing and problem-solving',
            'mixed': 'You use a balanced approach across different learning methods'
        }
        
        return {
            'learning_style': learning_style,
            'style_description': style_descriptions.get(learning_style, 'Learning style analysis in progress'),
            'optimal_session_length_minutes': optimal_session_length,
            'best_study_times': best_study_times,
            'session_type_distribution': type_distribution,
            'total_sessions_analyzed': len(sessions),
            'analysis_date': datetime.now().isoformat(),
            'recommendations': [
                f'Your {learning_style} learning style suggests focusing on specific techniques',
                f'Optimal session length appears to be {optimal_session_length} minutes',
                f'Best study times: {", ".join(best_study_times)}'
            ]
        }
        
    except Exception as e:
        logger.error(f"Error analyzing learning style: {e}")
        raise HTTPException(status_code=500, detail="Error analyzing learning style")


@router.get("/health")
async def health_check():
    """Health check endpoint for AI assistant service."""
    return {
        'status': 'healthy',
        'service': 'AI Study Assistant',
        'version': '1.0.0',
        'features': [
            'personalized_recommendations',
            'learning_insights',
            'adaptive_paths',
            'practice_questions',
            'study_feedback',
            'knowledge_assessment'
        ],
        'timestamp': datetime.now().isoformat()
    }
