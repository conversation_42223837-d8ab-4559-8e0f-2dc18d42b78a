"""FastAPI endpoints for mobile enterprise applications.

This module provides comprehensive mobile API endpoints with offline capabilities,
synchronization, push notifications, and enterprise mobile device management
for iOS and Android applications.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Header
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from database import get_db
from mobile.mobile_api_service import MobileAPIService
from schemas.mobile import (
    DeviceRegistrationRequest, DeviceRegistrationResponse,
    MobileAuthRequest, MobileAuthResponse,
    OfflineDataRequest, OfflineDataResponse,
    SyncRequest, SyncResponse,
    PushNotificationRequest, PushNotificationResponse,
    MobileAnalyticsRequest, MobileAnalyticsResponse,
    MobileDashboardResponse, MobileHealthResponse
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/mobile", tags=["Mobile Enterprise"])


def get_current_mobile_user(authorization: str = Header(None)) -> str:
    """Get current mobile user from session token."""
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    # TODO: Implement proper token validation
    # For now, extract user_id from token (simplified)
    token = authorization.replace("Bearer ", "")
    # In production, would validate JWT token and extract user_id
    return "mobile_user_1"


def get_device_id(x_device_id: str = Header(None)) -> str:
    """Get device ID from request headers."""
    if not x_device_id:
        raise HTTPException(status_code=400, detail="Device ID header required")
    return x_device_id


# Device Registration and Authentication

@router.post("/register-device", response_model=DeviceRegistrationResponse, status_code=status.HTTP_201_CREATED)
async def register_mobile_device(
    registration_request: DeviceRegistrationRequest,
    db: Session = Depends(get_db)
):
    """Register a mobile device for enterprise access."""
    try:
        logger.info(f"Registering mobile device for user {registration_request.user_id}")
        
        mobile_service = MobileAPIService(db)
        result = mobile_service.register_mobile_device(
            user_id=registration_request.user_id,
            device_data=registration_request.dict()
        )
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return DeviceRegistrationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error registering mobile device: {e}")
        raise HTTPException(status_code=500, detail="Error registering mobile device")


@router.post("/authenticate", response_model=MobileAuthResponse)
async def authenticate_mobile_user(
    auth_request: MobileAuthRequest,
    db: Session = Depends(get_db)
):
    """Authenticate user for mobile application access."""
    try:
        logger.info(f"Authenticating mobile user: {auth_request.email}")
        
        mobile_service = MobileAPIService(db)
        result = mobile_service.authenticate_mobile_user(auth_request.dict())
        
        if 'error' in result:
            error_code = result.get('error_code', 'AUTH_ERROR')
            if error_code == 'AUTH_FAILED':
                raise HTTPException(status_code=401, detail=result['error'])
            else:
                raise HTTPException(status_code=400, detail=result['error'])
        
        return MobileAuthResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error authenticating mobile user: {e}")
        raise HTTPException(status_code=500, detail="Error authenticating mobile user")


# Offline Data Management

@router.post("/offline-data", response_model=OfflineDataResponse)
async def get_offline_data_package(
    offline_request: OfflineDataRequest,
    user_id: str = Depends(get_current_mobile_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive offline data package for mobile app."""
    try:
        logger.info(f"Preparing offline data package for user {user_id}")
        
        mobile_service = MobileAPIService(db)
        result = mobile_service.prepare_offline_data_package(
            user_id=user_id,
            sync_token=offline_request.sync_token
        )
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return OfflineDataResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error preparing offline data package: {e}")
        raise HTTPException(status_code=500, detail="Error preparing offline data package")


@router.post("/sync", response_model=SyncResponse)
async def sync_mobile_data(
    sync_request: SyncRequest,
    user_id: str = Depends(get_current_mobile_user),
    device_id: str = Depends(get_device_id),
    db: Session = Depends(get_db)
):
    """Synchronize mobile app data with server."""
    try:
        logger.info(f"Syncing mobile data for user {user_id}, device {device_id}")
        
        mobile_service = MobileAPIService(db)
        result = mobile_service.sync_mobile_data(
            user_id=user_id,
            device_id=device_id,
            sync_data=sync_request.dict()
        )
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return SyncResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error syncing mobile data: {e}")
        raise HTTPException(status_code=500, detail="Error syncing mobile data")


# Push Notifications

@router.post("/notifications/send", response_model=PushNotificationResponse)
async def send_push_notification(
    notification_request: PushNotificationRequest,
    user_id: str = Depends(get_current_mobile_user),
    db: Session = Depends(get_db)
):
    """Send push notification to user's mobile devices."""
    try:
        logger.info(f"Sending push notification to user {user_id}")
        
        mobile_service = MobileAPIService(db)
        result = mobile_service.send_push_notification(
            user_id=user_id,
            notification_data=notification_request.dict()
        )
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return PushNotificationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending push notification: {e}")
        raise HTTPException(status_code=500, detail="Error sending push notification")


@router.post("/notifications/schedule-smart")
async def schedule_smart_notifications(
    user_id: str = Depends(get_current_mobile_user),
    db: Session = Depends(get_db)
):
    """Schedule intelligent notifications based on user behavior and AI insights."""
    try:
        logger.info(f"Scheduling smart notifications for user {user_id}")
        
        mobile_service = MobileAPIService(db)
        result = mobile_service.schedule_smart_notifications(user_id)
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error scheduling smart notifications: {e}")
        raise HTTPException(status_code=500, detail="Error scheduling smart notifications")


# Mobile Analytics

@router.post("/analytics/track", response_model=MobileAnalyticsResponse)
async def track_mobile_analytics(
    analytics_request: MobileAnalyticsRequest,
    user_id: str = Depends(get_current_mobile_user),
    device_id: str = Depends(get_device_id),
    db: Session = Depends(get_db)
):
    """Track mobile app usage analytics."""
    try:
        mobile_service = MobileAPIService(db)
        result = mobile_service.track_mobile_analytics(
            user_id=user_id,
            device_id=device_id,
            analytics_data=analytics_request.dict()
        )
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return MobileAnalyticsResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error tracking mobile analytics: {e}")
        raise HTTPException(status_code=500, detail="Error tracking mobile analytics")


@router.get("/dashboard", response_model=MobileDashboardResponse)
async def get_mobile_dashboard(
    user_id: str = Depends(get_current_mobile_user),
    db: Session = Depends(get_db)
):
    """Get mobile-optimized dashboard data."""
    try:
        logger.info(f"Getting mobile dashboard data for user {user_id}")
        
        mobile_service = MobileAPIService(db)
        result = mobile_service.get_mobile_dashboard_data(user_id)
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        return MobileDashboardResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting mobile dashboard data: {e}")
        raise HTTPException(status_code=500, detail="Error getting mobile dashboard data")


# Mobile-Specific Features

@router.get("/study-session/quick-start")
async def quick_start_study_session(
    certification_id: Optional[str] = Query(None, description="Specific certification to study"),
    duration_minutes: int = Query(30, ge=5, le=180, description="Study session duration"),
    user_id: str = Depends(get_current_mobile_user),
    db: Session = Depends(get_db)
):
    """Quick start a mobile-optimized study session."""
    try:
        logger.info(f"Quick starting study session for user {user_id}")
        
        # Create optimized study session for mobile
        session_data = {
            'session_id': f"mobile_{user_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            'user_id': user_id,
            'certification_id': certification_id,
            'duration_minutes': duration_minutes,
            'session_type': 'mobile_quick_study',
            'started_at': datetime.utcnow().isoformat(),
            'mobile_optimized': True,
            'offline_capable': True
        }
        
        # Get study materials optimized for mobile
        mobile_service = MobileAPIService(db)
        study_materials = mobile_service._get_mobile_study_materials(user_id, certification_id)
        
        return {
            'session_data': session_data,
            'study_materials': study_materials,
            'estimated_completion': (datetime.utcnow() + timedelta(minutes=duration_minutes)).isoformat(),
            'offline_available': True
        }
        
    except Exception as e:
        logger.error(f"Error starting quick study session: {e}")
        raise HTTPException(status_code=500, detail="Error starting study session")


@router.get("/practice-test/mobile")
async def get_mobile_practice_test(
    certification_id: str = Query(..., description="Certification ID"),
    question_count: int = Query(10, ge=5, le=50, description="Number of questions"),
    difficulty: str = Query("mixed", description="Question difficulty level"),
    user_id: str = Depends(get_current_mobile_user),
    db: Session = Depends(get_db)
):
    """Get mobile-optimized practice test."""
    try:
        logger.info(f"Getting mobile practice test for user {user_id}")
        
        # Generate mobile-optimized practice test
        practice_test = {
            'test_id': f"mobile_test_{user_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            'certification_id': certification_id,
            'question_count': question_count,
            'difficulty': difficulty,
            'estimated_duration_minutes': question_count * 2,  # 2 minutes per question
            'mobile_optimized': True,
            'offline_capable': True,
            'adaptive_scoring': True,
            'created_at': datetime.utcnow().isoformat()
        }
        
        # Get questions optimized for mobile display
        questions = []  # Would generate actual questions
        for i in range(question_count):
            questions.append({
                'question_id': f"q_{i+1}",
                'question_text': f"Sample mobile-optimized question {i+1}",
                'question_type': 'multiple_choice',
                'options': ['Option A', 'Option B', 'Option C', 'Option D'],
                'mobile_formatted': True,
                'images_optimized': True
            })
        
        return {
            'practice_test': practice_test,
            'questions': questions,
            'instructions': {
                'mobile_specific': True,
                'touch_optimized': True,
                'offline_submission': True
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting mobile practice test: {e}")
        raise HTTPException(status_code=500, detail="Error getting practice test")


@router.get("/offline-ai/recommendations")
async def get_offline_ai_recommendations(
    user_id: str = Depends(get_current_mobile_user),
    db: Session = Depends(get_db)
):
    """Get AI recommendations optimized for offline mobile use."""
    try:
        logger.info(f"Getting offline AI recommendations for user {user_id}")
        
        mobile_service = MobileAPIService(db)
        
        # Get lightweight AI recommendations for offline use
        recommendations = {
            'study_recommendations': [
                {
                    'type': 'study_focus',
                    'title': 'Focus on Network Security',
                    'description': 'Based on your recent performance, spend more time on network security topics',
                    'priority': 'high',
                    'estimated_time_minutes': 45,
                    'offline_available': True
                },
                {
                    'type': 'practice_test',
                    'title': 'Take Practice Test',
                    'description': 'You\'re ready for a practice test to assess your progress',
                    'priority': 'medium',
                    'estimated_time_minutes': 30,
                    'offline_available': True
                }
            ],
            'learning_insights': [
                {
                    'insight': 'Your study consistency has improved 25% this week',
                    'confidence': 0.89,
                    'actionable': True
                },
                {
                    'insight': 'Morning study sessions show 15% better retention',
                    'confidence': 0.76,
                    'actionable': True
                }
            ],
            'next_actions': [
                'Complete Chapter 5 review',
                'Take network security practice quiz',
                'Review weak areas from last test'
            ],
            'generated_at': datetime.utcnow().isoformat(),
            'offline_valid_until': (datetime.utcnow() + timedelta(days=3)).isoformat()
        }
        
        return recommendations
        
    except Exception as e:
        logger.error(f"Error getting offline AI recommendations: {e}")
        raise HTTPException(status_code=500, detail="Error getting AI recommendations")


# Health and Status

@router.get("/health", response_model=MobileHealthResponse)
async def mobile_health_check():
    """Health check endpoint for mobile API service."""
    try:
        return MobileHealthResponse(
            status="healthy",
            service="Mobile Enterprise API",
            version="1.0.0",
            features=[
                "device_registration",
                "mobile_authentication",
                "offline_data_sync",
                "push_notifications",
                "mobile_analytics",
                "offline_ai",
                "mobile_dashboard",
                "quick_study_sessions",
                "mobile_practice_tests"
            ],
            mobile_capabilities={
                "offline_support": True,
                "push_notifications": True,
                "background_sync": True,
                "ai_recommendations": True,
                "biometric_auth": True,
                "adaptive_ui": True
            },
            supported_platforms=["iOS", "Android", "Progressive Web App"],
            api_version="v1",
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error in mobile health check: {e}")
        raise HTTPException(status_code=500, detail="Mobile service health check failed")


@router.get("/app-config")
async def get_mobile_app_config(
    platform: str = Query(..., description="Mobile platform (ios/android)"),
    app_version: str = Query(..., description="App version"),
    user_id: str = Depends(get_current_mobile_user),
    db: Session = Depends(get_db)
):
    """Get mobile app configuration and feature flags."""
    try:
        logger.info(f"Getting mobile app config for {platform} v{app_version}")
        
        # Get user-specific and platform-specific configuration
        config = {
            'app_config': {
                'theme': 'adaptive',
                'offline_storage_limit_mb': 500,
                'sync_frequency_hours': 6,
                'notification_settings': {
                    'study_reminders': True,
                    'achievement_alerts': True,
                    'ai_insights': True,
                    'system_updates': True
                },
                'ai_features': {
                    'offline_recommendations': True,
                    'adaptive_learning': True,
                    'smart_notifications': True,
                    'performance_prediction': True
                }
            },
            'feature_flags': {
                'biometric_auth': platform in ['ios', 'android'],
                'background_sync': True,
                'offline_ai': True,
                'social_features': False,  # Coming soon
                'advanced_analytics': True,
                'voice_commands': platform == 'ios'  # iOS first
            },
            'api_endpoints': {
                'base_url': '/api/v1/mobile',
                'websocket_url': '/ws/mobile',
                'cdn_url': '/cdn/mobile'
            },
            'security_settings': {
                'session_timeout_minutes': 1440,  # 24 hours
                'biometric_timeout_minutes': 15,
                'auto_lock_minutes': 5,
                'require_pin_backup': True
            },
            'performance_settings': {
                'image_quality': 'adaptive',
                'video_quality': 'medium',
                'cache_size_mb': 100,
                'preload_content': True
            }
        }
        
        return config
        
    except Exception as e:
        logger.error(f"Error getting mobile app config: {e}")
        raise HTTPException(status_code=500, detail="Error getting app configuration")
