"""API endpoints for security domain management"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from database import get_db
from models.security_domain import SecurityDomain
from schemas.domain import DomainResponse, DomainList

# Create router without prefix (prefix is handled by main router)
router = APIRouter(tags=["domains"])

@router.get("/", response_model=DomainList)
async def list_domains(db: Session = Depends(get_db)) -> DomainList:
    """Get all security domains"""
    domains = db.query(SecurityDomain).filter(SecurityDomain.is_deleted == False).all()
    return DomainList(domains=[DomainResponse.from_orm(domain) for domain in domains])

@router.get("/{domain_id}", response_model=DomainResponse)
async def get_domain(domain_id: int, db: Session = Depends(get_db)) -> DomainResponse:
    """Get a specific security domain by ID"""
    domain = db.query(SecurityDomain).filter(
        SecurityDomain.id == domain_id,
        SecurityDomain.is_deleted == False
    ).first()
    if not domain:
        raise HTTPException(status_code=404, detail="Domain not found")
    return DomainResponse.from_orm(domain)