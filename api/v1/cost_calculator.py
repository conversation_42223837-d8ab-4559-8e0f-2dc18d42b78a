"""Cost calculator API endpoints for certification cost analysis.

This module provides RESTful API endpoints for:
- Cost calculation creation and management
- Currency rate management and conversion
- Cost scenario management and application
- Cost comparison and analysis
- Bulk cost calculations
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from database import get_db
from services.cost_calculator import CostCalculatorService
from models.cost_calculation import CostCalculation
from schemas.cost_calculation import (
    CostCalculationCreate, CostCalculationUpdate, CostCalculationResponse,
    CostScenarioCreate, CostScenarioUpdate, CostScenarioResponse,
    CurrencyRateCreate, CurrencyRateResponse,
    CostComparisonRequest, CostComparisonResponse,
    BulkCostCalculationRequest,
    CostCalculationListResponse, CostScenarioListResponse,
    CurrencyCode, ScenarioType
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cost-calculator", tags=["Cost Calculator"])

# Dependency to get current user ID (placeholder - replace with actual auth)
def get_current_user_id() -> str:
    """Get current user ID from authentication context.
    
    TODO: Replace with actual authentication logic
    """
    return "test_user_1"  # Placeholder


# Currency Rate Endpoints
@router.post("/currency-rates", response_model=CurrencyRateResponse, status_code=status.HTTP_201_CREATED)
def create_currency_rate(
    rate_data: CurrencyRateCreate,
    db: Session = Depends(get_db)
) -> CurrencyRateResponse:
    """Create a new currency exchange rate.
    
    Args:
        rate_data: Currency rate creation data
        db: Database session
        
    Returns:
        Created currency rate
        
    Raises:
        HTTPException: If validation fails
    """
    try:
        service = CostCalculatorService(db)
        rate = service.create_currency_rate(rate_data)
        return CurrencyRateResponse.from_orm(rate)
    except ValueError as e:
        logger.error(f"Error creating currency rate: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error creating currency rate: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/currency-rates/{base_currency}/{target_currency}")
def get_exchange_rate(
    base_currency: CurrencyCode,
    target_currency: CurrencyCode,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get current exchange rate between two currencies.
    
    Args:
        base_currency: Base currency code
        target_currency: Target currency code
        db: Database session
        
    Returns:
        Exchange rate information
        
    Raises:
        HTTPException: If rate not found
    """
    try:
        service = CostCalculatorService(db)
        rate = service.get_exchange_rate(base_currency.value, target_currency.value)
        
        if rate is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Exchange rate not found for {base_currency.value}/{target_currency.value}"
            )
        
        return {
            "base_currency": base_currency.value,
            "target_currency": target_currency.value,
            "rate": rate,
            "timestamp": datetime.utcnow().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting exchange rate: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/currency-rates/update-from-api")
def update_exchange_rates_from_api(
    base_currency: CurrencyCode = CurrencyCode.USD,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Update exchange rates from external API.
    
    Args:
        base_currency: Base currency for rate updates
        db: Database session
        
    Returns:
        Update results
    """
    try:
        service = CostCalculatorService(db)
        result = service.update_exchange_rates_from_api(base_currency.value)
        return result
    except Exception as e:
        logger.error(f"Error updating exchange rates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# Cost Scenario Endpoints
@router.post("/scenarios", response_model=CostScenarioResponse, status_code=status.HTTP_201_CREATED)
def create_cost_scenario(
    scenario_data: CostScenarioCreate,
    db: Session = Depends(get_db)
) -> CostScenarioResponse:
    """Create a new cost scenario.
    
    Args:
        scenario_data: Cost scenario creation data
        db: Database session
        
    Returns:
        Created cost scenario
    """
    try:
        service = CostCalculatorService(db)
        scenario = service.create_cost_scenario(scenario_data)
        return CostScenarioResponse.from_orm(scenario)
    except Exception as e:
        logger.error(f"Error creating cost scenario: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/scenarios", response_model=CostScenarioListResponse)
def get_cost_scenarios(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Number of scenarios per page"),
    scenario_type: Optional[ScenarioType] = Query(None, description="Filter by scenario type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db)
) -> CostScenarioListResponse:
    """Get paginated list of cost scenarios.
    
    Args:
        page: Page number (1-based)
        page_size: Number of scenarios per page
        scenario_type: Optional scenario type filter
        is_active: Optional active status filter
        db: Database session
        
    Returns:
        Paginated list of cost scenarios
    """
    try:
        service = CostCalculatorService(db)
        scenarios, total_count = service.get_cost_scenarios(
            page=page,
            page_size=page_size,
            scenario_type=scenario_type.value if scenario_type else None,
            is_active=is_active
        )
        
        total_pages = (total_count + page_size - 1) // page_size
        
        return CostScenarioListResponse(
            scenarios=[CostScenarioResponse.from_orm(scenario) for scenario in scenarios],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    except Exception as e:
        logger.error(f"Error getting cost scenarios: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/scenarios/{scenario_id}", response_model=CostScenarioResponse)
def update_cost_scenario(
    scenario_id: int,
    update_data: CostScenarioUpdate,
    db: Session = Depends(get_db)
) -> CostScenarioResponse:
    """Update a cost scenario.
    
    Args:
        scenario_id: ID of the scenario
        update_data: Update data
        db: Database session
        
    Returns:
        Updated cost scenario
        
    Raises:
        HTTPException: If scenario not found
    """
    try:
        service = CostCalculatorService(db)
        scenario = service.update_cost_scenario(scenario_id, update_data)
        
        if not scenario:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cost scenario not found"
            )
        
        return CostScenarioResponse.from_orm(scenario)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating cost scenario {scenario_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# Cost Calculation Endpoints
@router.post("/calculations", response_model=CostCalculationResponse, status_code=status.HTTP_201_CREATED)
def create_cost_calculation(
    calculation_data: CostCalculationCreate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> CostCalculationResponse:
    """Create a new cost calculation.
    
    Args:
        calculation_data: Cost calculation creation data
        db: Database session
        user_id: Current user ID
        
    Returns:
        Created cost calculation
        
    Raises:
        HTTPException: If validation fails or certifications not found
    """
    try:
        service = CostCalculatorService(db)
        calculation = service.calculate_certification_costs(user_id, calculation_data)
        return CostCalculationResponse.from_orm(calculation)
    except ValueError as e:
        logger.error(f"Error creating cost calculation: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error creating cost calculation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/calculations", response_model=CostCalculationListResponse)
def get_cost_calculations(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Number of calculations per page"),
    is_saved: Optional[bool] = Query(None, description="Filter by saved status"),
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> CostCalculationListResponse:
    """Get paginated list of cost calculations for the current user.
    
    Args:
        page: Page number (1-based)
        page_size: Number of calculations per page
        is_saved: Optional saved status filter
        db: Database session
        user_id: Current user ID
        
    Returns:
        Paginated list of cost calculations
    """
    try:
        service = CostCalculatorService(db)
        calculations, total_count = service.get_user_cost_calculations(
            user_id=user_id,
            page=page,
            page_size=page_size,
            is_saved=is_saved
        )
        
        total_pages = (total_count + page_size - 1) // page_size
        
        return CostCalculationListResponse(
            calculations=[CostCalculationResponse.from_orm(calc) for calc in calculations],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    except Exception as e:
        logger.error(f"Error getting cost calculations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/calculations/{calculation_id}", response_model=CostCalculationResponse)
def get_cost_calculation(
    calculation_id: int,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> CostCalculationResponse:
    """Get a specific cost calculation by ID.
    
    Args:
        calculation_id: ID of the cost calculation
        db: Database session
        user_id: Current user ID
        
    Returns:
        Cost calculation details
        
    Raises:
        HTTPException: If calculation not found
    """
    try:
        service = CostCalculatorService(db)
        calculation = service.db.query(CostCalculation).filter(
            and_(
                CostCalculation.id == calculation_id,
                CostCalculation.user_id == user_id
            )
        ).first()

        if not calculation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cost calculation not found"
            )

        return CostCalculationResponse.from_orm(calculation)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting cost calculation {calculation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/calculations/{calculation_id}", response_model=CostCalculationResponse)
def update_cost_calculation(
    calculation_id: int,
    update_data: CostCalculationUpdate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> CostCalculationResponse:
    """Update a cost calculation.

    Args:
        calculation_id: ID of the cost calculation
        update_data: Update data
        db: Database session
        user_id: Current user ID

    Returns:
        Updated cost calculation

    Raises:
        HTTPException: If calculation not found
    """
    try:
        service = CostCalculatorService(db)
        calculation = service.update_cost_calculation(calculation_id, user_id, update_data)

        if not calculation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cost calculation not found"
            )

        return CostCalculationResponse.from_orm(calculation)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating cost calculation {calculation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/calculations/{calculation_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_cost_calculation(
    calculation_id: int,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> None:
    """Delete a cost calculation.

    Args:
        calculation_id: ID of the cost calculation
        db: Database session
        user_id: Current user ID

    Raises:
        HTTPException: If calculation not found
    """
    try:
        service = CostCalculatorService(db)
        deleted = service.delete_cost_calculation(calculation_id, user_id)

        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cost calculation not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting cost calculation {calculation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# Comparison and Analysis Endpoints
@router.post("/calculations/compare", response_model=CostComparisonResponse)
def compare_cost_calculations(
    comparison_request: CostComparisonRequest,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> CostComparisonResponse:
    """Compare multiple cost calculations.

    Args:
        comparison_request: Comparison parameters
        db: Database session
        user_id: Current user ID

    Returns:
        Comparison analysis

    Raises:
        HTTPException: If calculations not found or not accessible
    """
    try:
        service = CostCalculatorService(db)
        result = service.compare_cost_calculations(user_id, comparison_request)

        return CostComparisonResponse(
            calculations=[CostCalculationResponse.from_orm(calc) for calc in result['calculations']],
            comparison_currency=result['comparison_currency'],
            summary=result['summary'],
            recommendations=result['recommendations']
        )
    except ValueError as e:
        logger.error(f"Error comparing cost calculations: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error comparing cost calculations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/calculations/bulk", response_model=List[CostCalculationResponse])
def create_bulk_cost_calculations(
    bulk_request: BulkCostCalculationRequest,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> List[CostCalculationResponse]:
    """Create multiple cost calculations in bulk.

    Args:
        bulk_request: Bulk calculation request
        db: Database session
        user_id: Current user ID

    Returns:
        List of created cost calculations

    Raises:
        HTTPException: If validation fails
    """
    try:
        service = CostCalculatorService(db)
        calculations = []

        for calc_data in bulk_request.calculations:
            # Apply bulk discount if specified
            if bulk_request.apply_bulk_discount and bulk_request.bulk_discount_percentage > 0:
                calc_data.additional_costs = calc_data.additional_costs * (
                    1 - bulk_request.bulk_discount_percentage / 100
                )

            calculation = service.calculate_certification_costs(user_id, calc_data)
            calculations.append(calculation)

        return [CostCalculationResponse.from_orm(calc) for calc in calculations]
    except ValueError as e:
        logger.error(f"Error creating bulk cost calculations: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error creating bulk cost calculations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
