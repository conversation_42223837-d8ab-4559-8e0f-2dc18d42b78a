"""Authentication API endpoints"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional
import logging

from database import get_db
from services.auth_service import AuthenticationService
from schemas.auth import (
    UserRegistrationRequest,
    UserLoginRequest,
    TokenRefreshRequest,
    UserAuthResponse,
    UserProfileResponse,
    PasswordResetRequest,
    PasswordResetConfirm,
    EmailVerificationRequest,
    UserPreferencesUpdate,
    EmailCheckRequest,
    EmailCheckResponse,
    ResendVerificationRequest
)
from models.user import User

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/auth", tags=["Authentication"])
security = HTTPBearer()


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user"""
    auth_service = AuthenticationService(db)
    user = auth_service.get_current_user(credentials.credentials)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


@router.post("/register", response_model=UserAuthResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    registration_data: UserRegistrationRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """Register a new user account"""
    try:
        logger.info(f"User registration attempt: {registration_data.email}")
        
        auth_service = AuthenticationService(db)
        success, message, user = auth_service.register_user(registration_data)
        
        if not success:
            if "already exists" in message:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=message
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=message
                )
        
        # Auto-login after registration
        login_data = UserLoginRequest(
            email=registration_data.email,
            password=registration_data.password,
            remember_me=False
        )
        
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        login_success, login_message, auth_response = auth_service.authenticate_user(
            login_data, client_ip, user_agent
        )
        
        if login_success and auth_response:
            logger.info(f"User registered and logged in: {registration_data.email}")
            return auth_response
        else:
            # Registration succeeded but auto-login failed
            user_profile = UserProfileResponse.from_orm(user)
            # Create a basic response without tokens
            raise HTTPException(
                status_code=status.HTTP_201_CREATED,
                detail="Registration successful. Please log in."
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed due to server error"
        )


@router.post("/login", response_model=UserAuthResponse)
async def login_user(
    login_data: UserLoginRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """Authenticate user and return access tokens"""
    try:
        logger.info(f"User login attempt: {login_data.email}")
        
        auth_service = AuthenticationService(db)
        
        # Get client information
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        success, message, auth_response = auth_service.authenticate_user(
            login_data, client_ip, user_agent
        )
        
        if not success:
            if "locked" in message.lower():
                raise HTTPException(
                    status_code=status.HTTP_423_LOCKED,
                    detail=message
                )
            elif "verify" in message.lower():
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=message
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=message
                )
        
        logger.info(f"User logged in successfully: {login_data.email}")
        return auth_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed due to server error"
        )


@router.post("/refresh", response_model=UserAuthResponse)
async def refresh_token(
    refresh_data: TokenRefreshRequest,
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token"""
    try:
        auth_service = AuthenticationService(db)
        success, message, auth_response = auth_service.refresh_token(refresh_data.refresh_token)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=message
            )
        
        return auth_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Logout user and invalidate session"""
    try:
        auth_service = AuthenticationService(db)
        success, message = auth_service.logout_user(credentials.credentials)
        
        return {"message": message}
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/me", response_model=UserProfileResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user)
):
    """Get current user profile"""
    return UserProfileResponse.from_orm(current_user)


@router.put("/preferences", response_model=UserProfileResponse)
async def update_user_preferences(
    preferences_data: UserPreferencesUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user preferences"""
    try:
        current_user.preferences = preferences_data.preferences
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"User preferences updated: {current_user.email}")
        return UserProfileResponse.from_orm(current_user)
        
    except Exception as e:
        logger.error(f"Error updating preferences: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update preferences"
        )


@router.post("/forgot-password")
async def request_password_reset(
    reset_data: PasswordResetRequest,
    db: Session = Depends(get_db)
):
    """Request password reset"""
    try:
        auth_service = AuthenticationService(db)
        success, message = auth_service.request_password_reset(reset_data.email)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )

        return {"message": "Password reset email sent successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset request error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process password reset request"
        )


@router.post("/reset-password")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """Confirm password reset with token"""
    try:
        auth_service = AuthenticationService(db)
        success, message = auth_service.confirm_password_reset(
            reset_data.token,
            reset_data.new_password
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )

        return {"message": "Password reset successful"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset confirmation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset password"
        )


@router.post("/verify-email")
async def verify_email(
    verification_data: EmailVerificationRequest,
    db: Session = Depends(get_db)
):
    """Verify user email with token"""
    try:
        auth_service = AuthenticationService(db)
        success, message = auth_response = auth_service.verify_email(verification_data.token)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )

        # Return auth response if verification includes auto-login
        if auth_response and hasattr(auth_response, 'access_token'):
            return auth_response

        return {"message": "Email verified successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify email"
        )


@router.get("/check-email", response_model=EmailCheckResponse)
async def check_email_availability(
    email: str,
    db: Session = Depends(get_db)
):
    """Check if email address is available for registration"""
    try:
        auth_service = AuthenticationService(db)
        is_available, suggestions = auth_service.check_email_availability(email)

        return EmailCheckResponse(
            available=is_available,
            suggestions=suggestions if not is_available else None
        )

    except Exception as e:
        logger.error(f"Email check error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check email availability"
        )


@router.post("/resend-verification")
async def resend_verification_email(
    resend_data: ResendVerificationRequest,
    db: Session = Depends(get_db)
):
    """Resend email verification"""
    try:
        auth_service = AuthenticationService(db)
        success, message = auth_service.resend_verification_email(resend_data.email)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )

        return {"message": "Verification email sent successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resend verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resend verification email"
        )
