"""Job Types API endpoints.

This module provides RESTful API endpoints for job types CRUD operations
following PEP 8, 257, and 484 standards with comprehensive error handling.
"""

from typing import List, Optional, Dict, Any
import logging
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from database import get_db
from services.job_types_crud import (
    JobTypeCRUDService, JobTypeCreateRequest, JobTypeUpdateRequest,
    JobTypeResponse, JobTypeFilters
)
from services.base_crud import (
    PaginationParams, SortParams, ValidationError, NotFoundError, ConflictError
)
from auth.dependencies import get_current_user, require_permissions
from models.user import User

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/job-types", tags=["job-types"])


def get_job_type_service(db: Session = Depends(get_db)) -> JobTypeCRUDService:
    """Get job type service instance.
    
    Args:
        db: Database session
        
    Returns:
        JobTypeCRUDService instance
    """
    return JobTypeCRUDService(db)


@router.post(
    "/",
    response_model=JobTypeResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new job type",
    description="Create a new security job type with validation and audit trail"
)
async def create_job_type(
    job_type_data: JobTypeCreateRequest,
    service: JobTypeCRUDService = Depends(get_job_type_service),
    current_user: User = Depends(get_current_user)
) -> JobTypeResponse:
    """Create a new job type.
    
    Args:
        job_type_data: Job type creation data
        service: Job type CRUD service
        current_user: Current authenticated user
        
    Returns:
        Created job type response
        
    Raises:
        HTTPException: If validation fails or creation conflicts
    """
    try:
        logger.info(
            f"Creating job type: {job_type_data.title}",
            extra={'user_id': current_user.id, 'title': job_type_data.title}
        )
        
        result = service.create(job_type_data, user_id=current_user.id)
        
        logger.info(
            f"Successfully created job type with ID: {result.id}",
            extra={'user_id': current_user.id, 'job_type_id': result.id}
        )
        
        return result
        
    except ValidationError as e:
        logger.warning(
            f"Validation error creating job type: {e.message}",
            extra={'user_id': current_user.id, 'details': e.details}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                'message': e.message,
                'details': e.details,
                'type': 'validation_error'
            }
        )
    except ConflictError as e:
        logger.warning(
            f"Conflict error creating job type: {e.message}",
            extra={'user_id': current_user.id, 'details': e.details}
        )
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={
                'message': e.message,
                'details': e.details,
                'type': 'conflict_error'
            }
        )
    except Exception as e:
        logger.error(
            f"Unexpected error creating job type: {str(e)}",
            extra={'user_id': current_user.id},
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )


@router.get(
    "/{job_type_id}",
    response_model=JobTypeResponse,
    summary="Get job type by ID",
    description="Retrieve a specific job type by its ID"
)
async def get_job_type(
    job_type_id: int,
    service: JobTypeCRUDService = Depends(get_job_type_service),
    include_deleted: bool = Query(False, description="Include soft-deleted records")
) -> JobTypeResponse:
    """Get job type by ID.
    
    Args:
        job_type_id: ID of the job type to retrieve
        service: Job type CRUD service
        include_deleted: Whether to include soft-deleted records
        
    Returns:
        Job type response
        
    Raises:
        HTTPException: If job type is not found
    """
    try:
        result = service.get(job_type_id, include_deleted=include_deleted)
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    'message': f'Job type with ID {job_type_id} not found',
                    'type': 'not_found_error'
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error retrieving job type {job_type_id}: {str(e)}",
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )


@router.get(
    "/",
    response_model=Dict[str, Any],
    summary="List job types with filtering",
    description="List job types with advanced filtering, pagination, and sorting"
)
async def list_job_types(
    # Pagination parameters
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    
    # Sorting parameters
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", pattern="^(asc|desc)$", description="Sort order"),
    
    # Search parameters
    search: Optional[str] = Query(None, description="Search query"),
    
    # Filter parameters
    security_area: Optional[List[str]] = Query(None, description="Security areas"),
    seniority_level: Optional[List[str]] = Query(None, description="Seniority levels"),
    job_family: Optional[List[str]] = Query(None, description="Job families"),
    salary_min: Optional[float] = Query(None, ge=0, description="Minimum salary"),
    salary_max: Optional[float] = Query(None, ge=0, description="Maximum salary"),
    remote_friendly: Optional[bool] = Query(None, description="Remote friendly jobs"),
    demand_level: Optional[List[str]] = Query(None, description="Demand levels"),
    include_deleted: bool = Query(False, description="Include soft-deleted records"),
    
    service: JobTypeCRUDService = Depends(get_job_type_service)
) -> Dict[str, Any]:
    """List job types with filtering and pagination.
    
    Args:
        page: Page number
        page_size: Items per page
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        search: Search query
        security_area: Security area filters
        seniority_level: Seniority level filters
        job_family: Job family filters
        salary_min: Minimum salary filter
        salary_max: Maximum salary filter
        remote_friendly: Remote friendly filter
        demand_level: Demand level filters
        include_deleted: Include soft-deleted records
        service: Job type CRUD service
        
    Returns:
        Paginated job types response with aggregations
    """
    try:
        # Build filters
        filters = JobTypeFilters(
            search=search,
            search_fields=['title', 'description'] if search else [],
            security_area=security_area,
            seniority_level=seniority_level,
            job_family=job_family,
            salary_min=salary_min,
            salary_max=salary_max,
            remote_friendly=remote_friendly,
            demand_level=demand_level,
            include_deleted=include_deleted
        )
        
        # Build pagination
        pagination = PaginationParams(page=page, page_size=page_size)
        
        # Build sorting
        sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)
        
        result = service.list_with_filters(filters, pagination, sort_params)
        
        return result
        
    except Exception as e:
        logger.error(
            f"Unexpected error listing job types: {str(e)}",
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )


@router.put(
    "/{job_type_id}",
    response_model=JobTypeResponse,
    summary="Update job type",
    description="Update an existing job type with validation and audit trail"
)
async def update_job_type(
    job_type_id: int,
    job_type_data: JobTypeUpdateRequest,
    service: JobTypeCRUDService = Depends(get_job_type_service),
    current_user: User = Depends(get_current_user)
) -> JobTypeResponse:
    """Update an existing job type.
    
    Args:
        job_type_id: ID of the job type to update
        job_type_data: Job type update data
        service: Job type CRUD service
        current_user: Current authenticated user
        
    Returns:
        Updated job type response
        
    Raises:
        HTTPException: If validation fails, not found, or update conflicts
    """
    try:
        logger.info(
            f"Updating job type {job_type_id}",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id}
        )
        
        result = service.update(job_type_id, job_type_data, user_id=current_user.id)
        
        logger.info(
            f"Successfully updated job type {job_type_id}",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id}
        )
        
        return result
        
    except NotFoundError as e:
        logger.warning(
            f"Job type {job_type_id} not found for update",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id}
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                'message': e.message,
                'type': 'not_found_error'
            }
        )
    except ValidationError as e:
        logger.warning(
            f"Validation error updating job type {job_type_id}: {e.message}",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id, 'details': e.details}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                'message': e.message,
                'details': e.details,
                'type': 'validation_error'
            }
        )
    except ConflictError as e:
        logger.warning(
            f"Conflict error updating job type {job_type_id}: {e.message}",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id, 'details': e.details}
        )
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={
                'message': e.message,
                'details': e.details,
                'type': 'conflict_error'
            }
        )
    except Exception as e:
        logger.error(
            f"Unexpected error updating job type {job_type_id}: {str(e)}",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id},
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )


@router.delete(
    "/{job_type_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete job type",
    description="Delete a job type (soft delete by default)"
)
async def delete_job_type(
    job_type_id: int,
    soft: bool = Query(True, description="Perform soft delete"),
    service: JobTypeCRUDService = Depends(get_job_type_service),
    current_user: User = Depends(get_current_user)
) -> None:
    """Delete a job type.

    Args:
        job_type_id: ID of the job type to delete
        soft: Whether to perform soft delete
        service: Job type CRUD service
        current_user: Current authenticated user

    Raises:
        HTTPException: If job type is not found or deletion fails
    """
    try:
        logger.info(
            f"Deleting job type {job_type_id} (soft={soft})",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id}
        )

        success = service.delete(job_type_id, user_id=current_user.id, soft=soft)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    'message': 'Failed to delete job type',
                    'type': 'deletion_error'
                }
            )

        logger.info(
            f"Successfully deleted job type {job_type_id}",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id}
        )

    except NotFoundError as e:
        logger.warning(
            f"Job type {job_type_id} not found for deletion",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id}
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                'message': e.message,
                'type': 'not_found_error'
            }
        )
    except Exception as e:
        logger.error(
            f"Unexpected error deleting job type {job_type_id}: {str(e)}",
            extra={'user_id': current_user.id, 'job_type_id': job_type_id},
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )


@router.post(
    "/bulk",
    response_model=List[JobTypeResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Bulk create job types",
    description="Create multiple job types in a single operation"
)
async def bulk_create_job_types(
    job_types_data: List[JobTypeCreateRequest],
    batch_size: int = Query(100, ge=1, le=1000, description="Batch size for processing"),
    service: JobTypeCRUDService = Depends(get_job_type_service),
    current_user: User = Depends(get_current_user)
) -> List[JobTypeResponse]:
    """Bulk create job types.

    Args:
        job_types_data: List of job type creation data
        batch_size: Batch size for processing
        service: Job type CRUD service
        current_user: Current authenticated user

    Returns:
        List of created job type responses

    Raises:
        HTTPException: If validation fails or creation conflicts
    """
    try:
        logger.info(
            f"Bulk creating {len(job_types_data)} job types",
            extra={'user_id': current_user.id, 'count': len(job_types_data)}
        )

        if len(job_types_data) > 1000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    'message': 'Cannot create more than 1000 job types at once',
                    'type': 'validation_error'
                }
            )

        result = service.bulk_create(
            job_types_data,
            user_id=current_user.id,
            batch_size=batch_size
        )

        logger.info(
            f"Successfully bulk created {len(result)} job types",
            extra={'user_id': current_user.id, 'count': len(result)}
        )

        return result

    except ValidationError as e:
        logger.warning(
            f"Validation error in bulk create: {e.message}",
            extra={'user_id': current_user.id, 'details': e.details}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                'message': e.message,
                'details': e.details,
                'type': 'validation_error'
            }
        )
    except Exception as e:
        logger.error(
            f"Unexpected error in bulk create: {str(e)}",
            extra={'user_id': current_user.id},
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )


@router.get(
    "/search",
    response_model=Dict[str, Any],
    summary="Search job types",
    description="Search job types using full-text search with filtering"
)
async def search_job_types(
    q: str = Query(..., min_length=2, description="Search query"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    security_area: Optional[List[str]] = Query(None, description="Security areas"),
    seniority_level: Optional[List[str]] = Query(None, description="Seniority levels"),
    service: JobTypeCRUDService = Depends(get_job_type_service)
) -> Dict[str, Any]:
    """Search job types with full-text search.

    Args:
        q: Search query
        page: Page number
        page_size: Items per page
        security_area: Security area filters
        seniority_level: Seniority level filters
        service: Job type CRUD service

    Returns:
        Search results with pagination and aggregations
    """
    try:
        filters = JobTypeFilters(
            security_area=security_area,
            seniority_level=seniority_level
        )

        pagination = PaginationParams(page=page, page_size=page_size)

        result = service.search_job_types(q, filters, pagination)

        return result

    except Exception as e:
        logger.error(
            f"Unexpected error searching job types: {str(e)}",
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )


@router.get(
    "/{job_type_id}/analytics",
    response_model=Dict[str, Any],
    summary="Get job type analytics",
    description="Get comprehensive analytics data for a specific job type"
)
async def get_job_type_analytics(
    job_type_id: int,
    service: JobTypeCRUDService = Depends(get_job_type_service)
) -> Dict[str, Any]:
    """Get analytics data for a job type.

    Args:
        job_type_id: ID of the job type
        service: Job type CRUD service

    Returns:
        Analytics data including related jobs and career progression

    Raises:
        HTTPException: If job type is not found
    """
    try:
        result = service.get_job_type_analytics(job_type_id)
        return result

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                'message': e.message,
                'type': 'not_found_error'
            }
        )
    except Exception as e:
        logger.error(
            f"Unexpected error getting analytics for job type {job_type_id}: {str(e)}",
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )


@router.get(
    "/popular",
    response_model=List[JobTypeResponse],
    summary="Get popular job types",
    description="Get popular job types by security area"
)
async def get_popular_job_types(
    security_area: Optional[str] = Query(None, description="Security area filter"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    service: JobTypeCRUDService = Depends(get_job_type_service)
) -> List[JobTypeResponse]:
    """Get popular job types.

    Args:
        security_area: Optional security area filter
        limit: Maximum number of results
        service: Job type CRUD service

    Returns:
        List of popular job types
    """
    try:
        result = service.get_popular_job_types(security_area, limit)
        return result

    except Exception as e:
        logger.error(
            f"Unexpected error getting popular job types: {str(e)}",
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                'message': 'Internal server error occurred',
                'type': 'internal_error'
            }
        )
