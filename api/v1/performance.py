"""Performance monitoring API endpoints."""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from api.utils.performance import (
    performance_monitor, 
    get_performance_health,
    PerformanceMonitor,
    RequestMetrics
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/performance", tags=["Performance Monitoring"])


class PerformanceStatsResponse(BaseModel):
    """Performance statistics response model."""
    uptime_seconds: float
    uptime_human: str
    total_requests: int
    requests_last_hour: int
    avg_response_time: float
    error_count: int
    error_rate: float
    unique_endpoints: int
    system_metrics: Dict[str, Any]
    timestamp: str


class EndpointStatsResponse(BaseModel):
    """Endpoint statistics response model."""
    endpoint: str
    method: str
    count: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    error_count: int
    error_rate: float
    last_called: Optional[str]


class RecentRequestResponse(BaseModel):
    """Recent request response model."""
    path: str
    method: str
    status_code: int
    duration: float
    memory_usage: float
    cpu_usage: float
    timestamp: str


class PerformanceHealthResponse(BaseModel):
    """Performance health response model."""
    status: str
    issues: List[str]
    metrics: Dict[str, Any]
    timestamp: str


class MetricResponse(BaseModel):
    """Individual metric response model."""
    name: str
    value: float
    unit: str
    timestamp: str
    tags: Dict[str, str]


@router.get("/summary", response_model=PerformanceStatsResponse)
async def get_performance_summary():
    """Get comprehensive performance summary."""
    try:
        summary = performance_monitor.get_performance_summary()
        
        return PerformanceStatsResponse(
            uptime_seconds=summary.get('uptime_seconds', 0),
            uptime_human=summary.get('uptime_human', '0:00:00'),
            total_requests=summary.get('total_requests', 0),
            requests_last_hour=summary.get('requests_last_hour', 0),
            avg_response_time=summary.get('avg_response_time', 0),
            error_count=summary.get('error_count', 0),
            error_rate=summary.get('error_rate', 0),
            unique_endpoints=summary.get('unique_endpoints', 0),
            system_metrics=summary.get('system_metrics', {}),
            timestamp=summary.get('timestamp', '')
        )
        
    except Exception as e:
        logger.error(f"Error getting performance summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get performance summary: {str(e)}"
        )


@router.get("/endpoints", response_model=List[EndpointStatsResponse])
async def get_endpoint_statistics(
    limit: int = Query(50, ge=1, le=100, description="Maximum number of endpoints to return")
):
    """Get endpoint performance statistics."""
    try:
        stats = performance_monitor.get_endpoint_stats(limit=limit)
        
        return [
            EndpointStatsResponse(
                endpoint=stat['endpoint'],
                method=stat['method'],
                count=stat['count'],
                avg_response_time=stat['avg_response_time'],
                min_response_time=stat['min_response_time'],
                max_response_time=stat['max_response_time'],
                error_count=stat['error_count'],
                error_rate=stat['error_rate'],
                last_called=stat['last_called']
            )
            for stat in stats
        ]
        
    except Exception as e:
        logger.error(f"Error getting endpoint statistics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get endpoint statistics: {str(e)}"
        )


@router.get("/requests/recent", response_model=List[RecentRequestResponse])
async def get_recent_requests(
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of recent requests to return")
):
    """Get recent request performance data."""
    try:
        requests = performance_monitor.get_recent_requests(limit=limit)
        
        return [
            RecentRequestResponse(
                path=req['path'],
                method=req['method'],
                status_code=req['status_code'],
                duration=req['duration'],
                memory_usage=req['memory_usage'],
                cpu_usage=req['cpu_usage'],
                timestamp=req['timestamp']
            )
            for req in requests
        ]
        
    except Exception as e:
        logger.error(f"Error getting recent requests: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get recent requests: {str(e)}"
        )


@router.get("/health", response_model=PerformanceHealthResponse)
async def get_performance_health_status():
    """Get performance health status with recommendations."""
    try:
        health = get_performance_health()
        
        return PerformanceHealthResponse(
            status=health.get('status', 'unknown'),
            issues=health.get('issues', []),
            metrics=health.get('metrics', {}),
            timestamp=health.get('timestamp', '')
        )
        
    except Exception as e:
        logger.error(f"Error getting performance health: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get performance health: {str(e)}"
        )


@router.get("/metrics")
async def get_performance_metrics(
    limit: int = Query(1000, ge=1, le=10000, description="Maximum number of metrics to return")
):
    """Get raw performance metrics."""
    try:
        # Get recent metrics from the monitor
        metrics = list(performance_monitor.metrics)[-limit:]
        
        return {
            "metrics": [
                {
                    "name": metric.name,
                    "value": metric.value,
                    "unit": metric.unit,
                    "timestamp": metric.timestamp.isoformat(),
                    "tags": metric.tags
                }
                for metric in metrics
            ],
            "total_metrics": len(performance_monitor.metrics),
            "returned": len(metrics)
        }
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get performance metrics: {str(e)}"
        )


@router.get("/system")
async def get_system_metrics():
    """Get current system resource metrics."""
    try:
        system_metrics = performance_monitor.get_system_metrics()
        
        return {
            "system_metrics": system_metrics,
            "status": "healthy" if "error" not in system_metrics else "degraded"
        }
        
    except Exception as e:
        logger.error(f"Error getting system metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get system metrics: {str(e)}"
        )


@router.post("/clear")
async def clear_performance_metrics():
    """Clear all performance metrics (use with caution)."""
    try:
        performance_monitor.clear_metrics()
        
        return {
            "success": True,
            "message": "All performance metrics cleared",
            "timestamp": performance_monitor.start_time.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error clearing performance metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear performance metrics: {str(e)}"
        )


@router.get("/alerts")
async def get_performance_alerts():
    """Get performance alerts and recommendations."""
    try:
        health = get_performance_health()
        summary = performance_monitor.get_performance_summary()
        
        alerts = []
        
        # Check for performance issues
        if health.get('status') == 'degraded':
            for issue in health.get('issues', []):
                alerts.append({
                    "type": "warning",
                    "message": issue,
                    "timestamp": health.get('timestamp'),
                    "severity": "medium"
                })
        
        # Check response time
        avg_response_time = summary.get('avg_response_time', 0)
        if avg_response_time > 2000:  # 2 seconds
            alerts.append({
                "type": "performance",
                "message": f"High average response time: {avg_response_time:.1f}ms",
                "timestamp": summary.get('timestamp'),
                "severity": "high"
            })
        
        # Check error rate
        error_rate = summary.get('error_rate', 0)
        if error_rate > 5:  # 5%
            alerts.append({
                "type": "error",
                "message": f"High error rate: {error_rate:.2f}%",
                "timestamp": summary.get('timestamp'),
                "severity": "high"
            })
        
        # Check system resources
        system_metrics = summary.get('system_metrics', {})
        if isinstance(system_metrics, dict):
            cpu_usage = system_metrics.get('cpu', {}).get('usage_percent', 0)
            memory_usage = system_metrics.get('memory', {}).get('usage_percent', 0)
            
            if cpu_usage > 80:
                alerts.append({
                    "type": "resource",
                    "message": f"High CPU usage: {cpu_usage:.1f}%",
                    "timestamp": system_metrics.get('timestamp'),
                    "severity": "medium"
                })
            
            if memory_usage > 85:
                alerts.append({
                    "type": "resource",
                    "message": f"High memory usage: {memory_usage:.1f}%",
                    "timestamp": system_metrics.get('timestamp'),
                    "severity": "medium"
                })
        
        return {
            "alerts": alerts,
            "total_alerts": len(alerts),
            "status": "healthy" if len(alerts) == 0 else "degraded",
            "timestamp": summary.get('timestamp')
        }
        
    except Exception as e:
        logger.error(f"Error getting performance alerts: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get performance alerts: {str(e)}"
        )


@router.get("/export")
async def export_performance_data(
    format: str = Query("json", description="Export format: json, csv"),
    hours: int = Query(24, ge=1, le=168, description="Hours of data to export")
):
    """Export performance data for analysis."""
    try:
        from datetime import datetime, timedelta
        
        # Get data from the last N hours
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter recent requests
        recent_requests = [
            req for req in performance_monitor.request_metrics
            if req.timestamp > cutoff_time
        ]
        
        # Filter recent metrics
        recent_metrics = [
            metric for metric in performance_monitor.metrics
            if metric.timestamp > cutoff_time
        ]
        
        export_data = {
            "export_info": {
                "generated_at": datetime.now().isoformat(),
                "hours_included": hours,
                "total_requests": len(recent_requests),
                "total_metrics": len(recent_metrics)
            },
            "requests": [
                {
                    "path": req.path,
                    "method": req.method,
                    "status_code": req.status_code,
                    "duration": req.duration,
                    "memory_usage": req.memory_usage,
                    "cpu_usage": req.cpu_usage,
                    "timestamp": req.timestamp.isoformat()
                }
                for req in recent_requests
            ],
            "metrics": [
                {
                    "name": metric.name,
                    "value": metric.value,
                    "unit": metric.unit,
                    "timestamp": metric.timestamp.isoformat(),
                    "tags": metric.tags
                }
                for metric in recent_metrics
            ],
            "summary": performance_monitor.get_performance_summary()
        }
        
        if format.lower() == "csv":
            # For CSV format, we'd need to implement CSV conversion
            # For now, return JSON with a note
            export_data["note"] = "CSV export not yet implemented, returning JSON format"
        
        return export_data
        
    except Exception as e:
        logger.error(f"Error exporting performance data: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to export performance data: {str(e)}"
        )
