"""FastAPI endpoints for Salary Intelligence and ROI Analysis.

This module provides comprehensive API endpoints for salary intelligence,
market analysis, and ROI calculations for certifications and career transitions.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from datetime import datetime

from database import get_db
from services.salary_intelligence import SalaryIntelligenceService
from schemas.salary_intelligence import (
    SalaryIntelligenceResponse, ROIAnalysisResponse, SalaryBenchmarkResponse,
    CertificationImpactResponse, ROICalculationRequest, SalaryAnalysisRequest
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/salary-intelligence", tags=["Salary Intelligence"])


def get_current_user_id() -> str:
    """Get current user ID from authentication context."""
    # TODO: Implement proper authentication
    return "test_user_1"


@router.get("/roles/{role_id}/analysis", response_model=SalaryIntelligenceResponse)
async def analyze_role_salary(
    role_id: int = Path(..., description="Career role ID"),
    location: str = Query("remote", description="Location for salary analysis"),
    experience_years: int = Query(5, ge=0, le=50, description="Years of experience"),
    db: Session = Depends(get_db)
):
    """Get comprehensive salary intelligence analysis for a specific role."""
    try:
        logger.info(f"Analyzing salary for role {role_id} in {location}")
        
        service = SalaryIntelligenceService(db)
        analysis = service.analyze_salary_intelligence(
            role_id=role_id,
            location=location,
            experience_years=experience_years
        )
        
        return SalaryIntelligenceResponse(
            role_id=analysis.role_id,
            role_name=analysis.role_name,
            current_salary_range=analysis.current_salary_range,
            market_salary_range=analysis.market_salary_range,
            certification_impact=analysis.certification_impact,
            location_adjustments=analysis.location_adjustments,
            experience_multipliers=analysis.experience_multipliers,
            growth_projections=analysis.growth_projections,
            market_trends=analysis.market_trends,
            analysis_date=datetime.utcnow().isoformat(),
            location=location,
            experience_years=experience_years
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error analyzing role salary: {e}")
        raise HTTPException(status_code=500, detail="Error analyzing role salary")


@router.post("/roi-analysis", response_model=ROIAnalysisResponse)
async def calculate_certification_roi(
    request: ROICalculationRequest,
    db: Session = Depends(get_db)
):
    """Calculate ROI analysis for certification investment."""
    try:
        logger.info(f"Calculating ROI for certification {request.certification_id}")
        
        service = SalaryIntelligenceService(db)
        analysis = service.calculate_certification_roi(
            certification_id=request.certification_id,
            current_role_id=request.current_role_id,
            target_role_id=request.target_role_id,
            investment_cost=request.investment_cost,
            location=request.location,
            experience_years=request.experience_years
        )
        
        return ROIAnalysisResponse(
            certification_id=request.certification_id,
            current_role_id=request.current_role_id,
            target_role_id=request.target_role_id,
            investment_cost=analysis.investment_cost,
            expected_salary_increase=analysis.expected_salary_increase,
            payback_period_months=analysis.payback_period_months,
            five_year_roi=analysis.five_year_roi,
            ten_year_roi=analysis.ten_year_roi,
            risk_factors=analysis.risk_factors,
            confidence_score=analysis.confidence_score,
            market_conditions=analysis.market_conditions,
            analysis_date=datetime.utcnow().isoformat(),
            location=request.location,
            experience_years=request.experience_years
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating ROI: {e}")
        raise HTTPException(status_code=500, detail="Error calculating ROI")


@router.get("/benchmarks/{domain}", response_model=SalaryBenchmarkResponse)
async def get_salary_benchmarks(
    domain: str = Path(..., description="Career domain"),
    location: str = Query("remote", description="Location for benchmarks"),
    db: Session = Depends(get_db)
):
    """Get salary benchmarks for a specific domain and location."""
    try:
        logger.info(f"Getting salary benchmarks for {domain} in {location}")
        
        service = SalaryIntelligenceService(db)
        benchmarks = service.get_salary_benchmarks(domain, location)
        
        if "error" in benchmarks:
            raise HTTPException(status_code=404, detail=benchmarks["error"])
        
        return SalaryBenchmarkResponse(
            domain=benchmarks["domain"],
            location=benchmarks["location"],
            location_multiplier=benchmarks["location_multiplier"],
            benchmarks=benchmarks["benchmarks"],
            market_summary=benchmarks["market_summary"],
            generated_at=benchmarks["generated_at"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting salary benchmarks: {e}")
        raise HTTPException(status_code=500, detail="Error getting salary benchmarks")


@router.get("/certifications/{certification_id}/impact", response_model=CertificationImpactResponse)
async def get_certification_salary_impact(
    certification_id: int = Path(..., description="Certification ID"),
    db: Session = Depends(get_db)
):
    """Get comprehensive salary impact analysis for a certification."""
    try:
        logger.info(f"Analyzing salary impact for certification {certification_id}")
        
        service = SalaryIntelligenceService(db)
        impact = service.get_certification_salary_impact(certification_id)
        
        return CertificationImpactResponse(
            certification_id=certification_id,
            certification_name=impact["certification_name"],
            average_salary_premium=impact["average_salary_premium"],
            role_impacts=impact["role_impacts"],
            market_demand=impact["market_demand"],
            investment_recommendation=impact["investment_recommendation"],
            analysis_date=impact["analysis_date"]
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error analyzing certification impact: {e}")
        raise HTTPException(status_code=500, detail="Error analyzing certification impact")


@router.get("/market-trends/{domain}")
async def get_market_trends(
    domain: str = Path(..., description="Career domain"),
    db: Session = Depends(get_db)
):
    """Get market trends and intelligence for a specific domain."""
    try:
        logger.info(f"Getting market trends for {domain}")
        
        service = SalaryIntelligenceService(db)
        
        # Get market trends from the service
        trends = service._analyze_market_trends(domain)
        
        # Add additional market intelligence
        market_intelligence = {
            'domain': domain,
            'trends': trends,
            'salary_growth_forecast': {
                'next_year': trends['growth_rate'],
                'three_year': trends['growth_rate'] * 3,
                'five_year': trends['growth_rate'] * 5
            },
            'skill_demand': service._get_emerging_skills(domain),
            'market_drivers': service._get_market_drivers(domain),
            'risk_factors': service._get_market_risks(domain),
            'generated_at': datetime.utcnow().isoformat()
        }
        
        return market_intelligence
        
    except Exception as e:
        logger.error(f"Error getting market trends: {e}")
        raise HTTPException(status_code=500, detail="Error getting market trends")


@router.get("/salary-calculator")
async def salary_calculator(
    base_salary: float = Query(..., ge=30000, le=500000, description="Base salary amount"),
    location: str = Query("remote", description="Location"),
    domain: str = Query("cybersecurity", description="Career domain"),
    experience_years: int = Query(5, ge=0, le=50, description="Years of experience"),
    certifications: Optional[List[str]] = Query(None, description="List of certification names"),
    db: Session = Depends(get_db)
):
    """Calculate adjusted salary based on various factors."""
    try:
        logger.info(f"Calculating adjusted salary for {domain} role")
        
        service = SalaryIntelligenceService(db)
        
        # Get location multiplier
        location_multiplier = service.market_data['location_multipliers'].get(
            location.lower().replace(' ', '_'), 1.0
        )
        
        # Get experience multiplier
        experience_multiplier = min(1.0 + (experience_years * 0.05), 2.0)
        
        # Calculate certification premiums
        cert_premium = 0.0
        cert_details = {}
        if certifications:
            for cert in certifications:
                premium = service.market_data['certification_premiums'].get(
                    cert.replace(' ', '_'), 0.08
                )
                cert_premium += premium
                cert_details[cert] = {
                    'premium_percentage': premium * 100,
                    'salary_increase': base_salary * premium
                }
        
        # Calculate adjusted salary
        adjusted_salary = base_salary * location_multiplier * experience_multiplier * (1 + cert_premium)
        
        return {
            'base_salary': base_salary,
            'adjusted_salary': adjusted_salary,
            'total_increase': adjusted_salary - base_salary,
            'adjustments': {
                'location': {
                    'multiplier': location_multiplier,
                    'increase': base_salary * (location_multiplier - 1)
                },
                'experience': {
                    'multiplier': experience_multiplier,
                    'increase': base_salary * (experience_multiplier - 1)
                },
                'certifications': {
                    'total_premium': cert_premium * 100,
                    'total_increase': base_salary * cert_premium,
                    'details': cert_details
                }
            },
            'calculation_date': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in salary calculator: {e}")
        raise HTTPException(status_code=500, detail="Error calculating salary")


@router.get("/health")
async def health_check():
    """Health check endpoint for salary intelligence service."""
    return {
        'status': 'healthy',
        'service': 'Salary Intelligence',
        'version': '1.0.0',
        'features': [
            'salary_analysis',
            'roi_calculation',
            'market_benchmarks',
            'certification_impact',
            'market_trends',
            'salary_calculator'
        ],
        'timestamp': datetime.utcnow().isoformat()
    }
