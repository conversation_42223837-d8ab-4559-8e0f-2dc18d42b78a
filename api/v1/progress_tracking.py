"""FastAPI endpoints for progress tracking and learning analytics.

This module provides comprehensive API endpoints for study session tracking,
practice test results, learning goals, achievements, and analytics.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from database import get_db
from api.v1.auth import get_current_user
from models.user import User
from schemas.progress_tracking import (
    StudySessionStart, StudySessionEnd, StudySessionResponse,
    PracticeTestResultCreate, PracticeTestResultResponse,
    LearningGoalCreate, LearningGoalUpdate, LearningGoalResponse,
    GoalProgressUpdate, AchievementResponse,
    ProgressSummaryResponse, StudyInsightsResponse, LearningAnalyticsResponse,
    ProgressDashboardResponse, PeriodType
)
from services.progress_tracking import ProgressTrackingService
from models.progress_tracking import StudySession, PracticeTestResult, LearningGoal, Achievement

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/progress", tags=["Progress Tracking"])


# Remove the old get_current_user_id function - we'll use the auth dependency


@router.post("/sessions/start", response_model=StudySessionResponse)
async def start_study_session(
    request: StudySessionStart,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a new study session."""
    try:
        user_id = current_user.user_id
        logger.info(f"Starting study session for user {user_id}")
        
        service = ProgressTrackingService(db)
        
        session = service.start_study_session(
            user_id=user_id,
            session_type=request.session_type.value,
            certification_id=request.certification_id,
            learning_path_id=request.learning_path_id,
            transition_plan_id=request.transition_plan_id,
            topic=request.topic,
            planned_duration_minutes=request.planned_duration_minutes
        )
        
        return session.to_dict()
        
    except Exception as e:
        logger.error(f"Error starting study session: {e}")
        raise HTTPException(status_code=500, detail="Error starting study session")


@router.put("/sessions/{session_id}/end", response_model=StudySessionResponse)
async def end_study_session(
    session_id: int,
    request: StudySessionEnd,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """End a study session and record progress."""
    try:
        user_id = current_user.user_id
        logger.info(f"Ending study session {session_id} for user {user_id}")
        
        service = ProgressTrackingService(db)
        
        session = service.end_study_session(
            session_id=session_id,
            user_id=user_id,
            progress_after=request.progress_after,
            confidence_level=request.confidence_level,
            difficulty_rating=request.difficulty_rating,
            focus_rating=request.focus_rating,
            effectiveness_rating=request.effectiveness_rating,
            notes=request.notes,
            materials_used=request.materials_used,
            tags=request.tags
        )
        
        return session.to_dict()
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error ending study session: {e}")
        raise HTTPException(status_code=500, detail="Error ending study session")


@router.get("/sessions", response_model=List[StudySessionResponse])
async def get_study_sessions(
    certification_id: Optional[int] = Query(None, description="Filter by certification"),
    session_type: Optional[str] = Query(None, description="Filter by session type"),
    days_back: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's study sessions with filtering options."""
    try:
        user_id = current_user.user_id
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_back)
        
        query = db.query(StudySession).filter(
            StudySession.user_id == user_id,
            StudySession.started_at >= cutoff_date
        )
        
        if certification_id:
            query = query.filter(StudySession.certification_id == certification_id)
        
        if session_type:
            query = query.filter(StudySession.session_type == session_type)
        
        # Apply pagination
        offset = (page - 1) * page_size
        sessions = query.order_by(StudySession.started_at.desc()).offset(offset).limit(page_size).all()
        
        return [session.to_dict() for session in sessions]
        
    except Exception as e:
        logger.error(f"Error retrieving study sessions: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving study sessions")


@router.post("/practice-tests", response_model=PracticeTestResultResponse)
async def record_practice_test_result(
    request: PracticeTestResultCreate,
    db: Session = Depends(get_db)
):
    """Record a practice test result."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Recording practice test result for user {user_id}")
        
        service = ProgressTrackingService(db)
        
        result = service.record_practice_test_result(
            user_id=user_id,
            certification_id=request.certification_id,
            test_name=request.test_name,
            test_type=request.test_type.value,
            total_questions=request.total_questions,
            correct_answers=request.correct_answers,
            time_taken_minutes=request.time_taken_minutes,
            domain_scores=request.domain_scores,
            topic_scores=request.topic_scores,
            question_details=request.question_details,
            test_provider=request.test_provider,
            passing_score=request.passing_score
        )
        
        return result.to_dict()
        
    except Exception as e:
        logger.error(f"Error recording practice test result: {e}")
        raise HTTPException(status_code=500, detail="Error recording practice test result")


@router.get("/practice-tests", response_model=List[PracticeTestResultResponse])
async def get_practice_test_results(
    certification_id: Optional[int] = Query(None, description="Filter by certification"),
    test_type: Optional[str] = Query(None, description="Filter by test type"),
    days_back: int = Query(90, ge=1, le=365, description="Number of days to look back"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get user's practice test results with filtering options."""
    try:
        user_id = get_current_user_id()
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_back)
        
        query = db.query(PracticeTestResult).filter(
            PracticeTestResult.user_id == user_id,
            PracticeTestResult.created_at >= cutoff_date
        )
        
        if certification_id:
            query = query.filter(PracticeTestResult.certification_id == certification_id)
        
        if test_type:
            query = query.filter(PracticeTestResult.test_type == test_type)
        
        # Apply pagination
        offset = (page - 1) * page_size
        results = query.order_by(PracticeTestResult.created_at.desc()).offset(offset).limit(page_size).all()
        
        return [result.to_dict() for result in results]
        
    except Exception as e:
        logger.error(f"Error retrieving practice test results: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving practice test results")


@router.post("/goals", response_model=LearningGoalResponse)
async def create_learning_goal(
    request: LearningGoalCreate,
    db: Session = Depends(get_db)
):
    """Create a new learning goal."""
    try:
        user_id = get_current_user_id()
        logger.info(f"Creating learning goal for user {user_id}")
        
        service = ProgressTrackingService(db)
        
        goal = service.create_learning_goal(
            user_id=user_id,
            title=request.title,
            goal_type=request.goal_type.value,
            target_value=request.target_value,
            target_unit=request.target_unit,
            target_date=request.target_date,
            certification_id=request.certification_id,
            description=request.description,
            priority=request.priority.value
        )
        
        return goal.to_dict()
        
    except Exception as e:
        logger.error(f"Error creating learning goal: {e}")
        raise HTTPException(status_code=500, detail="Error creating learning goal")


@router.get("/goals", response_model=List[LearningGoalResponse])
async def get_learning_goals(
    status: Optional[str] = Query(None, description="Filter by goal status"),
    goal_type: Optional[str] = Query(None, description="Filter by goal type"),
    certification_id: Optional[int] = Query(None, description="Filter by certification"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get user's learning goals with filtering options."""
    try:
        user_id = get_current_user_id()
        
        query = db.query(LearningGoal).filter(LearningGoal.user_id == user_id)
        
        if status:
            query = query.filter(LearningGoal.status == status)
        
        if goal_type:
            query = query.filter(LearningGoal.goal_type == goal_type)
        
        if certification_id:
            query = query.filter(LearningGoal.certification_id == certification_id)
        
        # Apply pagination
        offset = (page - 1) * page_size
        goals = query.order_by(LearningGoal.created_at.desc()).offset(offset).limit(page_size).all()
        
        return [goal.to_dict() for goal in goals]
        
    except Exception as e:
        logger.error(f"Error retrieving learning goals: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving learning goals")


@router.put("/goals/{goal_id}", response_model=LearningGoalResponse)
async def update_learning_goal(
    goal_id: int,
    request: LearningGoalUpdate,
    db: Session = Depends(get_db)
):
    """Update a learning goal."""
    try:
        user_id = get_current_user_id()
        
        goal = db.query(LearningGoal).filter(
            LearningGoal.id == goal_id,
            LearningGoal.user_id == user_id
        ).first()
        
        if not goal:
            raise HTTPException(status_code=404, detail="Learning goal not found")
        
        # Update fields
        update_data = request.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(goal, field):
                setattr(goal, field, value.value if hasattr(value, 'value') else value)
        
        goal.updated_at = datetime.utcnow()
        db.commit()
        
        return goal.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating learning goal: {e}")
        raise HTTPException(status_code=500, detail="Error updating learning goal")


@router.put("/goals/{goal_id}/progress", response_model=LearningGoalResponse)
async def update_goal_progress(
    goal_id: int,
    request: GoalProgressUpdate,
    db: Session = Depends(get_db)
):
    """Update progress on a learning goal."""
    try:
        user_id = get_current_user_id()
        
        service = ProgressTrackingService(db)
        goal = service.update_goal_progress(goal_id, user_id, request.current_value)
        
        return goal.to_dict()
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating goal progress: {e}")
        raise HTTPException(status_code=500, detail="Error updating goal progress")


@router.get("/achievements", response_model=List[AchievementResponse])
async def get_achievements(
    earned_only: bool = Query(False, description="Show only earned achievements"),
    category: Optional[str] = Query(None, description="Filter by achievement category"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get user's achievements."""
    try:
        user_id = get_current_user_id()
        
        query = db.query(Achievement).filter(Achievement.user_id == user_id)
        
        if earned_only:
            query = query.filter(Achievement.is_earned == True)
        
        if category:
            query = query.filter(Achievement.category == category)
        
        # Apply pagination
        offset = (page - 1) * page_size
        achievements = query.order_by(Achievement.earned_at.desc().nullslast()).offset(offset).limit(page_size).all()
        
        return [achievement.to_dict() for achievement in achievements]
        
    except Exception as e:
        logger.error(f"Error retrieving achievements: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving achievements")


@router.get("/summary", response_model=ProgressSummaryResponse)
async def get_progress_summary(
    db: Session = Depends(get_db)
):
    """Get comprehensive progress summary for the user."""
    try:
        user_id = get_current_user_id()
        
        service = ProgressTrackingService(db)
        summary = service.get_user_progress_summary(user_id)
        
        return summary
        
    except Exception as e:
        logger.error(f"Error generating progress summary: {e}")
        raise HTTPException(status_code=500, detail="Error generating progress summary")


@router.get("/insights", response_model=StudyInsightsResponse)
async def get_study_insights(
    days_back: int = Query(30, ge=7, le=365, description="Number of days to analyze"),
    db: Session = Depends(get_db)
):
    """Get intelligent study insights and recommendations."""
    try:
        user_id = get_current_user_id()
        
        service = ProgressTrackingService(db)
        insights = service.get_study_insights(user_id, days_back)
        
        return insights
        
    except Exception as e:
        logger.error(f"Error generating study insights: {e}")
        raise HTTPException(status_code=500, detail="Error generating study insights")


@router.get("/analytics", response_model=LearningAnalyticsResponse)
async def get_learning_analytics(
    period_type: PeriodType = Query(PeriodType.MONTHLY, description="Analytics period type"),
    db: Session = Depends(get_db)
):
    """Get learning analytics for a specific period."""
    try:
        user_id = get_current_user_id()
        
        service = ProgressTrackingService(db)
        analytics = service.get_learning_analytics(user_id, period_type.value)
        
        if not analytics:
            raise HTTPException(status_code=404, detail="Analytics not found for the specified period")
        
        return analytics.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving learning analytics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving learning analytics")


@router.get("/dashboard", response_model=ProgressDashboardResponse)
async def get_progress_dashboard(
    db: Session = Depends(get_db)
):
    """Get comprehensive progress dashboard with all key information."""
    try:
        user_id = get_current_user_id()
        
        service = ProgressTrackingService(db)
        
        # Get summary
        summary = service.get_user_progress_summary(user_id)
        
        # Get insights
        insights = service.get_study_insights(user_id, days_back=30)
        
        # Get recent sessions (last 10)
        recent_sessions = db.query(StudySession).filter(
            StudySession.user_id == user_id,
            StudySession.ended_at.isnot(None)
        ).order_by(StudySession.started_at.desc()).limit(10).all()
        
        # Get recent test results (last 5)
        recent_tests = db.query(PracticeTestResult).filter(
            PracticeTestResult.user_id == user_id
        ).order_by(PracticeTestResult.created_at.desc()).limit(5).all()
        
        # Get active goals
        active_goals = db.query(LearningGoal).filter(
            LearningGoal.user_id == user_id,
            LearningGoal.status == 'active'
        ).order_by(LearningGoal.created_at.desc()).limit(5).all()
        
        # Get recent achievements (last 5)
        recent_achievements = db.query(Achievement).filter(
            Achievement.user_id == user_id,
            Achievement.is_earned == True
        ).order_by(Achievement.earned_at.desc()).limit(5).all()
        
        # Get current month analytics
        analytics = service.get_learning_analytics(user_id, 'monthly')
        
        return ProgressDashboardResponse(
            summary=summary,
            insights=insights,
            recent_sessions=[session.to_dict() for session in recent_sessions],
            recent_test_results=[test.to_dict() for test in recent_tests],
            active_goals=[goal.to_dict() for goal in active_goals],
            recent_achievements=[achievement.to_dict() for achievement in recent_achievements],
            analytics=analytics.to_dict() if analytics else None
        )
        
    except Exception as e:
        logger.error(f"Error generating progress dashboard: {e}")
        raise HTTPException(status_code=500, detail="Error generating progress dashboard")


@router.delete("/sessions/{session_id}")
async def delete_study_session(
    session_id: int,
    db: Session = Depends(get_db)
):
    """Delete a study session."""
    try:
        user_id = get_current_user_id()
        
        session = db.query(StudySession).filter(
            StudySession.id == session_id,
            StudySession.user_id == user_id
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Study session not found")
        
        db.delete(session)
        db.commit()
        
        return {"message": "Study session deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting study session: {e}")
        raise HTTPException(status_code=500, detail="Error deleting study session")


@router.delete("/goals/{goal_id}")
async def delete_learning_goal(
    goal_id: int,
    db: Session = Depends(get_db)
):
    """Delete a learning goal."""
    try:
        user_id = get_current_user_id()
        
        goal = db.query(LearningGoal).filter(
            LearningGoal.id == goal_id,
            LearningGoal.user_id == user_id
        ).first()
        
        if not goal:
            raise HTTPException(status_code=404, detail="Learning goal not found")
        
        db.delete(goal)
        db.commit()
        
        return {"message": "Learning goal deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting learning goal: {e}")
        raise HTTPException(status_code=500, detail="Error deleting learning goal")
