"""Cache management API endpoints."""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from api.services.cache import cache, get_cache_stats, CacheStats
from api.middleware.cache import clear_cache, warm_cache

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cache", tags=["Cache Management"])


class CacheStatsResponse(BaseModel):
    """Cache statistics response model."""
    status: str = "healthy"
    stats: CacheStats
    redis_connected: bool
    total_memory_usage: str
    hit_rate: float = 0.0


class CacheOperationResponse(BaseModel):
    """Cache operation response model."""
    success: bool
    message: str
    details: Dict[str, Any] = {}


class CacheWarmupRequest(BaseModel):
    """Cache warmup request model."""
    endpoints: Optional[List[str]] = Field(
        None,
        description="List of endpoints to warm up. If not provided, default endpoints will be used."
    )


class CacheClearRequest(BaseModel):
    """Cache clear request model."""
    pattern: Optional[str] = Field(
        None,
        description="Pattern to match keys for deletion. If not provided, all cache will be cleared."
    )
    confirm: bool = Field(
        False,
        description="Confirmation flag required for clearing all cache data."
    )


@router.get("/stats", response_model=CacheStatsResponse)
async def get_cache_statistics():
    """Get comprehensive cache statistics."""
    try:
        stats = get_cache_stats()
        health_check = cache.health_check()
        
        return CacheStatsResponse(
            status=health_check.get("status", "unknown"),
            stats=stats,
            redis_connected=health_check.get("connected", False),
            total_memory_usage=stats.memory_usage,
            hit_rate=0.0  # Calculate if you track hits/misses
        )
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cache statistics: {str(e)}"
        )


@router.get("/health")
async def cache_health_check():
    """Perform cache service health check."""
    try:
        health_status = cache.health_check()
        
        if health_status.get("status") == "healthy":
            return {
                "status": "healthy",
                "cache_service": "operational",
                "redis_connected": health_status.get("connected", False),
                "test_passed": health_status.get("test_passed", False),
                "details": health_status
            }
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "unhealthy",
                    "cache_service": "degraded",
                    "error": health_status.get("error", "Unknown error"),
                    "details": health_status
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Cache health check failed: {str(e)}"
        )


@router.post("/warm", response_model=CacheOperationResponse)
async def warm_cache_endpoints(request: CacheWarmupRequest):
    """Warm up cache by pre-loading specified endpoints."""
    try:
        result = await warm_cache(request.endpoints)
        
        return CacheOperationResponse(
            success=result.get("success", False),
            message=result.get("message", "Cache warmup completed"),
            details={
                "warmed_count": result.get("warmed_count", 0),
                "errors": result.get("errors", []),
                "endpoints": request.endpoints or "default endpoints"
            }
        )
        
    except Exception as e:
        logger.error(f"Cache warmup failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Cache warmup failed: {str(e)}"
        )


@router.delete("/clear", response_model=CacheOperationResponse)
async def clear_cache_data(request: CacheClearRequest):
    """Clear cache data with optional pattern matching."""
    try:
        # Safety check for clearing all cache
        if not request.pattern and not request.confirm:
            raise HTTPException(
                status_code=400,
                detail="Clearing all cache data requires confirmation. Set 'confirm' to true."
            )
        
        result = await clear_cache(request.pattern)
        
        return CacheOperationResponse(
            success=result.get("success", False),
            message=result.get("message", "Cache clear completed"),
            details={
                "cleared_count": result.get("cleared_count", 0),
                "pattern": request.pattern or "all",
                "confirmed": request.confirm
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Cache clear failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Cache clear failed: {str(e)}"
        )


@router.get("/keys")
async def list_cache_keys(
    pattern: str = Query("*", description="Pattern to match cache keys"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of keys to return")
):
    """List cache keys matching the specified pattern."""
    try:
        if not cache._connected or not cache._client:
            raise HTTPException(
                status_code=503,
                detail="Cache service is not available"
            )
        
        # Get keys matching pattern
        keys = cache._client.keys(f"{cache.config.key_prefix}:{pattern}")
        
        # Limit results
        limited_keys = keys[:limit] if keys else []
        
        return {
            "keys": limited_keys,
            "total_found": len(keys) if keys else 0,
            "returned": len(limited_keys),
            "pattern": pattern,
            "limit": limit
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list cache keys: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list cache keys: {str(e)}"
        )


@router.get("/key/{key}")
async def get_cache_key_value(key: str):
    """Get the value of a specific cache key."""
    try:
        # Add prefix if not already present
        if not key.startswith(cache.config.key_prefix):
            full_key = f"{cache.config.key_prefix}:{key}"
        else:
            full_key = key
        
        value = cache.get(full_key)
        
        if value is None:
            raise HTTPException(
                status_code=404,
                detail=f"Cache key '{key}' not found"
            )
        
        # Get TTL if available
        ttl = None
        if cache._connected and cache._client:
            try:
                ttl = cache._client.ttl(full_key)
                if ttl == -1:
                    ttl = "no expiration"
                elif ttl == -2:
                    ttl = "key not found"
                else:
                    ttl = f"{ttl} seconds"
            except Exception:
                ttl = "unknown"
        
        return {
            "key": key,
            "full_key": full_key,
            "value": value,
            "ttl": ttl,
            "exists": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get cache key value: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cache key value: {str(e)}"
        )


@router.delete("/key/{key}")
async def delete_cache_key(key: str):
    """Delete a specific cache key."""
    try:
        # Add prefix if not already present
        if not key.startswith(cache.config.key_prefix):
            full_key = f"{cache.config.key_prefix}:{key}"
        else:
            full_key = key
        
        deleted = cache.delete(full_key)
        
        return {
            "key": key,
            "full_key": full_key,
            "deleted": deleted,
            "message": f"Key '{key}' {'deleted' if deleted else 'not found'}"
        }
        
    except Exception as e:
        logger.error(f"Failed to delete cache key: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete cache key: {str(e)}"
        )


@router.post("/invalidate")
async def invalidate_cache_pattern(
    pattern: str = Query(..., description="Pattern to match keys for invalidation"),
    dry_run: bool = Query(False, description="If true, only show what would be deleted")
):
    """Invalidate cache keys matching a pattern."""
    try:
        if not cache._connected or not cache._client:
            raise HTTPException(
                status_code=503,
                detail="Cache service is not available"
            )
        
        # Get keys that would be affected
        full_pattern = f"{cache.config.key_prefix}:{pattern}"
        keys = cache._client.keys(full_pattern)
        
        if dry_run:
            return {
                "dry_run": True,
                "pattern": pattern,
                "full_pattern": full_pattern,
                "keys_to_delete": keys,
                "count": len(keys) if keys else 0,
                "message": f"Would delete {len(keys) if keys else 0} keys matching pattern '{pattern}'"
            }
        
        # Actually delete the keys
        deleted_count = cache.delete_pattern(full_pattern)
        
        return {
            "dry_run": False,
            "pattern": pattern,
            "full_pattern": full_pattern,
            "deleted_count": deleted_count,
            "message": f"Deleted {deleted_count} keys matching pattern '{pattern}'"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to invalidate cache pattern: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to invalidate cache pattern: {str(e)}"
        )
