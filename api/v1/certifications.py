"""API endpoints for certification management"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, func
import logging

from database import get_db
from api.v1.auth import get_current_user
from models.user import User
from models.certification import Certification, Organization
from schemas.certification import (
    CertificationCreate,
    CertificationResponse,
    CertificationList,
    StudyPlanResponse,
    CertificationSearchRequest,
    CertificationComparisonRequest,
    CertificationComparisonResponse
)
from services.certification import CertificationService
from utils.growth_analyzer import StudyPlanner

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/certifications", tags=["Certifications"])

@router.get("/", response_model=CertificationList)
async def list_certifications(
    domain: Optional[str] = Query(None, description="Filter by security domain"),
    level: Optional[str] = Query(None, description="Filter by certification level"),
    focus: Optional[str] = Query(None, description="Filter by focus area"),
    difficulty: Optional[int] = Query(None, ge=1, le=4, description="Filter by difficulty (1-4)"),
    min_cost: Optional[float] = Query(None, ge=0, description="Minimum cost filter"),
    max_cost: Optional[float] = Query(None, ge=0, description="Maximum cost filter"),
    organization: Optional[str] = Query(None, description="Filter by organization name"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
) -> CertificationList:
    """Get all certifications with advanced filtering (GET /api/v1/certifications)"""
    try:
        logger.info("Retrieving certifications with filters")

        # Build query
        query = db.query(Certification).filter(Certification.is_active == True)

        # Apply filters
        if domain:
            query = query.filter(Certification.domain.ilike(f"%{domain}%"))

        if level:
            query = query.filter(Certification.level.ilike(f"%{level}%"))

        if focus:
            query = query.filter(Certification.focus.ilike(f"%{focus}%"))

        if difficulty:
            query = query.filter(Certification.difficulty == difficulty)

        if min_cost is not None:
            query = query.filter(Certification.cost >= min_cost)

        if max_cost is not None:
            query = query.filter(Certification.cost <= max_cost)

        if organization:
            query = query.join(Organization).filter(
                Organization.name.ilike(f"%{organization}%")
            )

        if search:
            search_filter = or_(
                Certification.name.ilike(f"%{search}%"),
                Certification.description.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)

        # Get total count for pagination
        total_count = query.count()

        # Apply pagination
        offset = (page - 1) * page_size
        certifications = query.order_by(Certification.name).offset(offset).limit(page_size).all()

        return CertificationList(
            certifications=[cert.to_dict() for cert in certifications],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=(total_count + page_size - 1) // page_size
        )

    except Exception as e:
        logger.error(f"Error retrieving certifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve certifications"
        )

@router.get("/{cert_id}", response_model=CertificationResponse)
async def get_certification_details(
    cert_id: int,
    db: Session = Depends(get_db)
):
    """Get detailed certification information (GET /api/v1/certifications/{id})"""
    try:
        logger.info(f"Retrieving certification details for ID: {cert_id}")

        certification = db.query(Certification).filter(
            Certification.id == cert_id,
            Certification.is_active == True
        ).first()

        if not certification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Certification not found"
            )

        return certification.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving certification details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve certification details"
        )


@router.get("/search", response_model=CertificationList)
async def search_certifications(
    q: str = Query(..., min_length=2, description="Search query"),
    domain: Optional[str] = Query(None, description="Filter by domain"),
    level: Optional[str] = Query(None, description="Filter by level"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Full-text search with suggestions (GET /api/v1/certifications/search)"""
    try:
        logger.info(f"Searching certifications with query: {q}")

        # Build search query
        search_terms = q.split()
        search_conditions = []

        for term in search_terms:
            term_condition = or_(
                Certification.name.ilike(f"%{term}%"),
                Certification.description.ilike(f"%{term}%"),
                Certification.domain.ilike(f"%{term}%"),
                Certification.category.ilike(f"%{term}%")
            )
            search_conditions.append(term_condition)

        query = db.query(Certification).filter(
            Certification.is_active == True,
            and_(*search_conditions)
        )

        # Apply additional filters
        if domain:
            query = query.filter(Certification.domain.ilike(f"%{domain}%"))

        if level:
            query = query.filter(Certification.level.ilike(f"%{level}%"))

        # Get total count
        total_count = query.count()

        # Apply pagination and ordering (relevance-based)
        offset = (page - 1) * page_size
        certifications = query.order_by(
            # Prioritize exact name matches
            func.case(
                (Certification.name.ilike(f"%{q}%"), 1),
                else_=2
            ),
            Certification.name
        ).offset(offset).limit(page_size).all()

        return CertificationList(
            certifications=[cert.to_dict() for cert in certifications],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=(total_count + page_size - 1) // page_size
        )

    except Exception as e:
        logger.error(f"Error searching certifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search certifications"
        )


@router.get("/domains", response_model=Dict[str, Any])
async def get_certification_domains(
    db: Session = Depends(get_db)
):
    """Get available domains and categories (GET /api/v1/certifications/domains)"""
    try:
        logger.info("Retrieving certification domains and categories")

        # Get unique domains
        domains = db.query(Certification.domain).filter(
            Certification.is_active == True,
            Certification.domain.isnot(None)
        ).distinct().all()

        # Get unique categories
        categories = db.query(Certification.category).filter(
            Certification.is_active == True,
            Certification.category.isnot(None)
        ).distinct().all()

        # Get unique levels
        levels = db.query(Certification.level).filter(
            Certification.is_active == True,
            Certification.level.isnot(None)
        ).distinct().all()

        # Get organizations
        organizations = db.query(Organization.name).filter(
            Organization.id.in_(
                db.query(Certification.organization_id).filter(
                    Certification.is_active == True,
                    Certification.organization_id.isnot(None)
                ).distinct()
            )
        ).all()

        return {
            "domains": [d[0] for d in domains if d[0]],
            "categories": [c[0] for c in categories if c[0]],
            "levels": [l[0] for l in levels if l[0]],
            "organizations": [o[0] for o in organizations if o[0]],
            "difficulty_range": {"min": 1, "max": 4},
            "total_certifications": db.query(Certification).filter(
                Certification.is_active == True
            ).count()
        }

    except Exception as e:
        logger.error(f"Error retrieving domains: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve domains"
        )


@router.post("/compare", response_model=CertificationComparisonResponse)
async def compare_certifications(
    comparison_request: CertificationComparisonRequest,
    db: Session = Depends(get_db)
):
    """Multi-certification comparison (POST /api/v1/certifications/compare)"""
    try:
        logger.info(f"Comparing certifications: {comparison_request.certification_ids}")

        if len(comparison_request.certification_ids) < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least 2 certifications required for comparison"
            )

        if len(comparison_request.certification_ids) > 5:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 5 certifications allowed for comparison"
            )

        # Get certifications
        certifications = db.query(Certification).filter(
            Certification.id.in_(comparison_request.certification_ids),
            Certification.is_active == True
        ).all()

        if len(certifications) != len(comparison_request.certification_ids):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="One or more certifications not found"
            )

        # Build comparison data
        comparison_data = {
            "certifications": [cert.to_dict() for cert in certifications],
            "comparison_matrix": {
                "cost": {cert.id: cert.cost for cert in certifications},
                "difficulty": {cert.id: cert.difficulty for cert in certifications},
                "domain": {cert.id: cert.domain for cert in certifications},
                "level": {cert.id: cert.level for cert in certifications},
                "organization": {cert.id: cert.organization.name if cert.organization else None for cert in certifications}
            },
            "summary": {
                "total_cost": sum(cert.cost or 0 for cert in certifications),
                "average_difficulty": sum(cert.difficulty for cert in certifications) / len(certifications),
                "domains": list(set(cert.domain for cert in certifications if cert.domain)),
                "levels": list(set(cert.level for cert in certifications if cert.level)),
                "organizations": list(set(cert.organization.name for cert in certifications if cert.organization))
            }
        }

        return comparison_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing certifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to compare certifications"
        )


@router.get("/{cert_id}/study-plan", response_model=StudyPlanResponse)
async def get_study_plan(
    cert_id: int,
    hours_per_week: float = Query(..., gt=0, le=168),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> StudyPlanResponse:
    """Generate a study plan for a specific certification"""
    try:
        logger.info(f"Generating study plan for certification {cert_id}")

        service = CertificationService(db)
        certification = service.get_certification_by_id(cert_id)
        if not certification:
            raise HTTPException(status_code=404, detail="Certification not found")

        planner = StudyPlanner(db)
        study_plan = planner.generate_study_schedule(
            certification_id=cert_id,
            hours_per_week=int(hours_per_week)
        )

        return StudyPlanResponse(**study_plan)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating study plan: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate study plan"
        )