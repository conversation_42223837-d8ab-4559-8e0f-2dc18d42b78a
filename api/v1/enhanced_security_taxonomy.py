"""Enhanced Security Taxonomy API Endpoints.

This module provides comprehensive API endpoints for the enhanced security
taxonomy including skills, certifications, job titles, and technologies.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import logging

from database import get_db
from services.enhanced_security_taxonomy_service import EnhancedSecurityTaxonomyService
from schemas.enhanced_security_taxonomy import (
    SecuritySkillTaxonomyCreate, SecuritySkillTaxonomyResponse,
    SecurityCertificationTaxonomyCreate, SecurityCertificationTaxonomyResponse,
    JobTitleTaxonomyCreate, JobTitleTaxonomyResponse,
    SecurityTechnologyTaxonomyCreate, SecurityTechnologyTaxonomyResponse,
    TaxonomyFilter, TaxonomyAnalytics, UserSkillAssessment,
    UserCertificationGoal, UserCareerGoal, TaxonomyRecommendation,
    SecurityDomainEnum, SkillCategoryEnum, CertificationLevelEnum
)

router = APIRouter(prefix="/enhanced-taxonomy", tags=["Enhanced Security Taxonomy"])
logger = logging.getLogger(__name__)


# Skills Endpoints

@router.post("/skills", response_model=SecuritySkillTaxonomyResponse)
async def create_skill(
    skill_data: SecuritySkillTaxonomyCreate,
    db: Session = Depends(get_db)
):
    """Create a new security skill in the taxonomy."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.create_skill(skill_data)
    except Exception as e:
        logger.error(f"Error creating skill: {e}")
        raise HTTPException(status_code=500, detail="Error creating skill")


@router.get("/skills", response_model=List[SecuritySkillTaxonomyResponse])
async def get_skills(
    domain: Optional[SecurityDomainEnum] = Query(None, description="Filter by security domain"),
    category: Optional[SkillCategoryEnum] = Query(None, description="Filter by skill category"),
    trending: Optional[bool] = Query(None, description="Filter trending skills"),
    min_demand_score: Optional[float] = Query(None, description="Minimum demand score"),
    limit: int = Query(50, description="Maximum number of results"),
    db: Session = Depends(get_db)
):
    """Get security skills with optional filters."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        
        if trending:
            return service.get_trending_skills(limit=limit)
        elif domain:
            return service.get_skills_by_domain(domain)
        elif min_demand_score:
            return service.get_high_impact_skills(min_salary_impact=min_demand_score)
        else:
            # Return all skills (implement pagination in production)
            return service.get_trending_skills(limit=limit)
            
    except Exception as e:
        logger.error(f"Error getting skills: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving skills")


@router.get("/skills/trending", response_model=List[SecuritySkillTaxonomyResponse])
async def get_trending_skills(
    limit: int = Query(10, description="Number of trending skills to return"),
    db: Session = Depends(get_db)
):
    """Get trending security skills."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.get_trending_skills(limit=limit)
    except Exception as e:
        logger.error(f"Error getting trending skills: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving trending skills")


@router.get("/skills/high-impact", response_model=List[SecuritySkillTaxonomyResponse])
async def get_high_impact_skills(
    min_salary_impact: float = Query(10.0, description="Minimum salary impact percentage"),
    db: Session = Depends(get_db)
):
    """Get skills with high salary impact."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.get_high_impact_skills(min_salary_impact=min_salary_impact)
    except Exception as e:
        logger.error(f"Error getting high impact skills: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving high impact skills")


# Certifications Endpoints

@router.post("/certifications", response_model=SecurityCertificationTaxonomyResponse)
async def create_certification(
    cert_data: SecurityCertificationTaxonomyCreate,
    db: Session = Depends(get_db)
):
    """Create a new security certification in the taxonomy."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.create_certification(cert_data)
    except Exception as e:
        logger.error(f"Error creating certification: {e}")
        raise HTTPException(status_code=500, detail="Error creating certification")


@router.get("/certifications", response_model=List[SecurityCertificationTaxonomyResponse])
async def get_certifications(
    level: Optional[CertificationLevelEnum] = Query(None, description="Filter by certification level"),
    domain: Optional[SecurityDomainEnum] = Query(None, description="Filter by security domain"),
    min_salary_premium: Optional[float] = Query(None, description="Minimum salary premium"),
    db: Session = Depends(get_db)
):
    """Get security certifications with optional filters."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        
        if level:
            return service.get_certifications_by_level(level)
        elif min_salary_premium:
            return service.get_high_value_certifications(min_salary_premium=min_salary_premium)
        else:
            # Return high-value certifications by default
            return service.get_high_value_certifications(min_salary_premium=1000.0)
            
    except Exception as e:
        logger.error(f"Error getting certifications: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving certifications")


@router.get("/certifications/high-value", response_model=List[SecurityCertificationTaxonomyResponse])
async def get_high_value_certifications(
    min_salary_premium: float = Query(5000.0, description="Minimum salary premium"),
    db: Session = Depends(get_db)
):
    """Get certifications with high salary premium."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.get_high_value_certifications(min_salary_premium=min_salary_premium)
    except Exception as e:
        logger.error(f"Error getting high value certifications: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving high value certifications")


@router.get("/certifications/{cert_id}/progression", response_model=List[SecurityCertificationTaxonomyResponse])
async def get_certification_progression(
    cert_id: int = Path(..., description="Current certification ID"),
    db: Session = Depends(get_db)
):
    """Get logical next certifications based on current certification."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.get_certification_progression_path(cert_id)
    except Exception as e:
        logger.error(f"Error getting certification progression: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving certification progression")


# Job Titles Endpoints

@router.post("/job-titles", response_model=JobTitleTaxonomyResponse)
async def create_job_title(
    job_data: JobTitleTaxonomyCreate,
    db: Session = Depends(get_db)
):
    """Create a new job title in the taxonomy."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.create_job_title(job_data)
    except Exception as e:
        logger.error(f"Error creating job title: {e}")
        raise HTTPException(status_code=500, detail="Error creating job title")


@router.get("/job-titles", response_model=List[JobTitleTaxonomyResponse])
async def get_job_titles(
    domain: Optional[SecurityDomainEnum] = Query(None, description="Filter by security domain"),
    seniority_level: Optional[str] = Query(None, description="Filter by seniority level"),
    min_posting_frequency: Optional[int] = Query(None, description="Minimum job posting frequency"),
    db: Session = Depends(get_db)
):
    """Get job titles with optional filters."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        
        if domain:
            return service.get_job_titles_by_domain(domain)
        elif min_posting_frequency:
            return service.get_high_demand_roles(min_posting_frequency=min_posting_frequency)
        else:
            # Return high-demand roles by default
            return service.get_high_demand_roles(min_posting_frequency=50)
            
    except Exception as e:
        logger.error(f"Error getting job titles: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving job titles")


@router.get("/job-titles/high-demand", response_model=List[JobTitleTaxonomyResponse])
async def get_high_demand_roles(
    min_posting_frequency: int = Query(100, description="Minimum job posting frequency"),
    db: Session = Depends(get_db)
):
    """Get high-demand job roles."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.get_high_demand_roles(min_posting_frequency=min_posting_frequency)
    except Exception as e:
        logger.error(f"Error getting high demand roles: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving high demand roles")


@router.get("/job-titles/{job_id}/progression", response_model=Dict[str, List[JobTitleTaxonomyResponse]])
async def get_career_progression(
    job_id: int = Path(..., description="Current job title ID"),
    db: Session = Depends(get_db)
):
    """Get career progression options from current job."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.get_career_progression_path(job_id)
    except Exception as e:
        logger.error(f"Error getting career progression: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving career progression")


# Technologies Endpoints

@router.post("/technologies", response_model=SecurityTechnologyTaxonomyResponse)
async def create_technology(
    tech_data: SecurityTechnologyTaxonomyCreate,
    db: Session = Depends(get_db)
):
    """Create a new security technology in the taxonomy."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.create_technology(tech_data)
    except Exception as e:
        logger.error(f"Error creating technology: {e}")
        raise HTTPException(status_code=500, detail="Error creating technology")


@router.get("/technologies/trending", response_model=List[SecurityTechnologyTaxonomyResponse])
async def get_trending_technologies(
    min_adoption_rate: float = Query(5.0, description="Minimum adoption rate"),
    db: Session = Depends(get_db)
):
    """Get trending security technologies."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.get_trending_technologies(min_adoption_rate=min_adoption_rate)
    except Exception as e:
        logger.error(f"Error getting trending technologies: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving trending technologies")


@router.get("/technologies/high-demand", response_model=List[SecurityTechnologyTaxonomyResponse])
async def get_high_demand_technologies(
    min_job_demand: int = Query(50, description="Minimum job demand"),
    db: Session = Depends(get_db)
):
    """Get technologies with high job market demand."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.get_high_demand_technologies(min_job_demand=min_job_demand)
    except Exception as e:
        logger.error(f"Error getting high demand technologies: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving high demand technologies")


# Analytics Endpoints

@router.get("/analytics", response_model=TaxonomyAnalytics)
async def get_taxonomy_analytics(
    db: Session = Depends(get_db)
):
    """Get comprehensive taxonomy analytics."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.generate_taxonomy_analytics()
    except Exception as e:
        logger.error(f"Error getting taxonomy analytics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving taxonomy analytics")


# Recommendation Endpoints

@router.post("/recommendations/skills", response_model=List[TaxonomyRecommendation])
async def get_skill_recommendations(
    user_id: int,
    current_skills: List[str],
    target_domain: Optional[SecurityDomainEnum] = None,
    db: Session = Depends(get_db)
):
    """Get personalized skill recommendations."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.generate_skill_recommendations(user_id, current_skills, target_domain)
    except Exception as e:
        logger.error(f"Error getting skill recommendations: {e}")
        raise HTTPException(status_code=500, detail="Error generating skill recommendations")


@router.post("/recommendations/certifications", response_model=List[TaxonomyRecommendation])
async def get_certification_recommendations(
    user_id: int,
    current_certs: List[str],
    career_level: str = "intermediate",
    db: Session = Depends(get_db)
):
    """Get personalized certification recommendations."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.generate_certification_recommendations(user_id, current_certs, career_level)
    except Exception as e:
        logger.error(f"Error getting certification recommendations: {e}")
        raise HTTPException(status_code=500, detail="Error generating certification recommendations")


@router.post("/recommendations/career-paths", response_model=List[TaxonomyRecommendation])
async def get_career_path_recommendations(
    user_id: int,
    current_role: str,
    target_timeframe: int = 24,
    db: Session = Depends(get_db)
):
    """Get personalized career path recommendations."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.generate_career_path_recommendations(user_id, current_role, target_timeframe)
    except Exception as e:
        logger.error(f"Error getting career path recommendations: {e}")
        raise HTTPException(status_code=500, detail="Error generating career path recommendations")


# User Assessment Endpoints

@router.post("/assess/skills", response_model=Dict[str, Any])
async def assess_user_skills(
    user_id: int,
    skill_assessments: List[UserSkillAssessment],
    db: Session = Depends(get_db)
):
    """Process user skill assessments and provide insights."""
    try:
        service = EnhancedSecurityTaxonomyService(db)
        return service.assess_user_skills(user_id, skill_assessments)
    except Exception as e:
        logger.error(f"Error assessing user skills: {e}")
        raise HTTPException(status_code=500, detail="Error processing skill assessment")
