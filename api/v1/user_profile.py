"""User profile management API endpoints"""
from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import logging
from database import get_db
from models.user_experience import UserExperience
from schemas.user_profile import (
    UserProfileCreate,
    UserProfileUpdate,
    UserProfileResponse,
    UserProfileSync
)

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/profile", response_model=UserProfileResponse)
async def get_user_profile(user_id: str, db: Session = Depends(get_db)):
    """Get user profile"""
    try:
        logger.info(f"Fetching profile for user {user_id}")
        logger.debug(f"Using database session: {id(db)}")

        if not user_id or not user_id.strip():
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Invalid user ID"
            )

        profile = db.query(UserExperience).filter_by(user_id=user_id).first()
        logger.debug(f"Retrieved profile from database: {profile.__dict__ if profile else None}")

        if not profile:
            logger.warning(f"Profile not found for user {user_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )

        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching user profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch user profile"
        )

@router.post("/profile", response_model=UserProfileResponse, status_code=status.HTTP_201_CREATED)
async def create_user_profile(
    user_id: str,
    profile_data: UserProfileCreate,
    db: Session = Depends(get_db)
):
    """Create new user profile"""
    try:
        logger.info(f"Creating profile for user {user_id}")

        # Check if profile already exists
        existing = db.query(UserExperience).filter_by(user_id=user_id).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Profile already exists"
            )

        # Create new profile
        profile = UserExperience(
            user_id=user_id,
            **profile_data.dict()
        )
        db.add(profile)
        db.commit()
        db.refresh(profile)

        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user profile: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user profile"
        )

@router.put("/profile", response_model=UserProfileResponse)
async def update_user_profile(
    user_id: str,
    profile_data: UserProfileUpdate,
    db: Session = Depends(get_db)
):
    """Update existing user profile"""
    try:
        logger.info(f"Updating profile for user {user_id}")
        profile = db.query(UserExperience).filter_by(user_id=user_id).first()

        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )

        # Update only provided fields
        update_data = profile_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(profile, key, value)

        profile.last_updated = datetime.utcnow()
        db.commit()
        db.refresh(profile)

        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )

@router.post("/profile/sync", response_model=UserProfileResponse)
async def sync_user_profile(
    user_id: str,
    sync_data: UserProfileSync,
    db: Session = Depends(get_db)
):
    """Synchronize user profile with remote changes"""
    try:
        logger.info(f"Syncing profile for user {user_id}")
        profile = db.query(UserExperience).filter_by(user_id=user_id).first()

        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )

        # Check for conflicts unless force sync is requested
        if not sync_data.force and profile.last_updated > sync_data.last_sync:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Profile has been modified since last sync"
            )

        # Log what fields are being synchronized
        logger.info(f"Synchronizing fields: {sync_data.changes}")

        profile.last_updated = datetime.utcnow()
        db.commit()
        db.refresh(profile)

        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error syncing user profile: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to sync user profile"
        )

@router.delete("/profile", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_profile(user_id: str, db: Session = Depends(get_db)):
    """Soft delete user profile"""
    try:
        logger.info(f"Deleting profile for user {user_id}")
        profile = db.query(UserExperience).filter_by(user_id=user_id).first()

        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )

        profile.is_deleted = True
        profile.deleted_at = datetime.utcnow()
        db.commit()

        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting user profile: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user profile"
        )