"""FastAPI endpoints for Agent 5 Marketplace & Integration Hub.

This module provides comprehensive API endpoints for marketplace operations,
including vendor management, course marketplace, partnerships, and international support.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, BackgroundTasks
from sqlalchemy.orm import Session
from datetime import datetime
from decimal import Decimal

from database import get_db
from services.marketplace_service import MarketplaceService
from schemas.marketplace import (
    # Vendor schemas
    MarketplaceVendorCreate, MarketplaceVendorUpdate, MarketplaceVendorResponse, MarketplaceVendorListResponse,
    # Course schemas
    MarketplaceCourseCreate, MarketplaceCourseUpdate, MarketplaceCourseResponse, MarketplaceCourseListResponse,
    # Partnership schemas
    PartnershipAgreementCreate, PartnershipAgreementResponse,
    # Commission schemas
    CommissionRecordCreate, CommissionRecordResponse,
    # Enrollment schemas
    CourseEnrollmentCreate, CourseEnrollmentResponse,
    # Review schemas
    CourseReviewCreate, CourseReviewResponse,
    # International schemas
    CurrencyRateCreate, CurrencyRateResponse, InternationalMarketCreate, InternationalMarketResponse,
    # Analytics schemas
    VendorAnalytics, MarketplaceAnalytics,
    # Search schemas
    MarketplaceSearchRequest, CurrencyConversionRequest, CurrencyConversionResponse,
    # Enums
    VendorStatusEnum, CourseStatusEnum, PartnershipTierEnum
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/marketplace", tags=["Marketplace & Integration Hub"])


def get_current_user() -> str:
    """Get current user ID from authentication context."""
    # TODO: Implement proper authentication
    return "user_1"


def get_current_admin_user() -> str:
    """Get current admin user ID from authentication context."""
    # TODO: Implement proper admin authentication
    return "admin_user_1"


# Vendor Management Endpoints
@router.post("/vendors", response_model=MarketplaceVendorResponse, status_code=status.HTTP_201_CREATED)
async def create_vendor(
    vendor_data: MarketplaceVendorCreate,
    current_user: str = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Create a new marketplace vendor."""
    try:
        logger.info(f"Creating vendor: {vendor_data.vendor_name}")
        
        marketplace_service = MarketplaceService(db)
        vendor = marketplace_service.create_vendor(vendor_data)
        
        return MarketplaceVendorResponse.from_orm(vendor)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating vendor: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/vendors/{vendor_id}", response_model=MarketplaceVendorResponse)
async def get_vendor(
    vendor_id: int = Path(..., description="Vendor ID"),
    db: Session = Depends(get_db)
):
    """Get vendor by ID."""
    try:
        marketplace_service = MarketplaceService(db)
        vendor = marketplace_service.get_vendor(vendor_id)
        
        if not vendor:
            raise HTTPException(status_code=404, detail="Vendor not found")
        
        return MarketplaceVendorResponse.from_orm(vendor)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting vendor {vendor_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/vendors/{vendor_id}", response_model=MarketplaceVendorResponse)
async def update_vendor(
    vendor_id: int = Path(..., description="Vendor ID"),
    vendor_data: MarketplaceVendorUpdate = ...,
    current_user: str = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Update vendor information."""
    try:
        marketplace_service = MarketplaceService(db)
        vendor = marketplace_service.update_vendor(vendor_id, vendor_data)
        
        if not vendor:
            raise HTTPException(status_code=404, detail="Vendor not found")
        
        return MarketplaceVendorResponse.from_orm(vendor)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating vendor {vendor_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/vendors", response_model=MarketplaceVendorListResponse)
async def list_vendors(
    status: Optional[VendorStatusEnum] = Query(None, description="Filter by vendor status"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """List vendors with pagination."""
    try:
        marketplace_service = MarketplaceService(db)
        vendors, total = marketplace_service.list_vendors(status, page, per_page)
        
        total_pages = (total + per_page - 1) // per_page
        
        return MarketplaceVendorListResponse(
            vendors=[MarketplaceVendorResponse.from_orm(vendor) for vendor in vendors],
            total=total,
            page=page,
            per_page=per_page,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"Error listing vendors: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/vendors/{vendor_id}/verify", response_model=MarketplaceVendorResponse)
async def verify_vendor(
    vendor_id: int = Path(..., description="Vendor ID"),
    verification_status: str = Query("verified", description="Verification status"),
    current_user: str = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Verify a vendor."""
    try:
        marketplace_service = MarketplaceService(db)
        vendor = marketplace_service.verify_vendor(vendor_id, verification_status)
        
        if not vendor:
            raise HTTPException(status_code=404, detail="Vendor not found")
        
        return MarketplaceVendorResponse.from_orm(vendor)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying vendor {vendor_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Course Management Endpoints
@router.post("/courses", response_model=MarketplaceCourseResponse, status_code=status.HTTP_201_CREATED)
async def create_course(
    course_data: MarketplaceCourseCreate,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new marketplace course."""
    try:
        logger.info(f"Creating course: {course_data.course_title}")
        
        marketplace_service = MarketplaceService(db)
        course = marketplace_service.create_course(course_data)
        
        return MarketplaceCourseResponse.from_orm(course)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating course: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/courses/{course_id}", response_model=MarketplaceCourseResponse)
async def get_course(
    course_id: int = Path(..., description="Course ID"),
    db: Session = Depends(get_db)
):
    """Get course by ID."""
    try:
        marketplace_service = MarketplaceService(db)
        course = marketplace_service.get_course(course_id)
        
        if not course:
            raise HTTPException(status_code=404, detail="Course not found")
        
        return MarketplaceCourseResponse.from_orm(course)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting course {course_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/courses/{course_id}", response_model=MarketplaceCourseResponse)
async def update_course(
    course_id: int = Path(..., description="Course ID"),
    course_data: MarketplaceCourseUpdate = ...,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update course information."""
    try:
        marketplace_service = MarketplaceService(db)
        course = marketplace_service.update_course(course_id, course_data)
        
        if not course:
            raise HTTPException(status_code=404, detail="Course not found")
        
        return MarketplaceCourseResponse.from_orm(course)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating course {course_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/courses/{course_id}/approve", response_model=MarketplaceCourseResponse)
async def approve_course(
    course_id: int = Path(..., description="Course ID"),
    current_user: str = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Approve a course for publication."""
    try:
        marketplace_service = MarketplaceService(db)
        course = marketplace_service.approve_course(course_id)
        
        if not course:
            raise HTTPException(status_code=404, detail="Course not found")
        
        return MarketplaceCourseResponse.from_orm(course)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving course {course_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/courses/{course_id}/publish", response_model=MarketplaceCourseResponse)
async def publish_course(
    course_id: int = Path(..., description="Course ID"),
    current_user: str = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Publish an approved course."""
    try:
        marketplace_service = MarketplaceService(db)
        course = marketplace_service.publish_course(course_id)
        
        if not course:
            raise HTTPException(status_code=404, detail="Course not found")
        
        return MarketplaceCourseResponse.from_orm(course)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error publishing course {course_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Course Search and Discovery
@router.post("/courses/search", response_model=MarketplaceCourseListResponse)
async def search_courses(
    search_request: MarketplaceSearchRequest,
    db: Session = Depends(get_db)
):
    """Search courses with advanced filtering."""
    try:
        marketplace_service = MarketplaceService(db)
        courses, total = marketplace_service.search_courses(search_request)

        total_pages = (total + search_request.per_page - 1) // search_request.per_page

        return MarketplaceCourseListResponse(
            courses=[MarketplaceCourseResponse.from_orm(course) for course in courses],
            total=total,
            page=search_request.page,
            per_page=search_request.per_page,
            total_pages=total_pages
        )

    except Exception as e:
        logger.error(f"Error searching courses: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Enrollment Management
@router.post("/enrollments", response_model=CourseEnrollmentResponse, status_code=status.HTTP_201_CREATED)
async def enroll_in_course(
    enrollment_data: CourseEnrollmentCreate,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Enroll a user in a course."""
    try:
        # Override user_id with authenticated user
        enrollment_data.user_id = current_user

        marketplace_service = MarketplaceService(db)
        enrollment = marketplace_service.enroll_user_in_course(enrollment_data)

        return CourseEnrollmentResponse.from_orm(enrollment)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error enrolling in course: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Course Reviews
@router.post("/courses/{course_id}/reviews", response_model=CourseReviewResponse, status_code=status.HTTP_201_CREATED)
async def add_course_review(
    course_id: int = Path(..., description="Course ID"),
    review_data: CourseReviewCreate = ...,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a review for a course."""
    try:
        # Override course_id and user_id
        review_data.course_id = course_id
        review_data.user_id = current_user

        marketplace_service = MarketplaceService(db)
        review = marketplace_service.add_course_review(review_data)

        return CourseReviewResponse.from_orm(review)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding course review: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Analytics Endpoints
@router.get("/vendors/{vendor_id}/analytics", response_model=VendorAnalytics)
async def get_vendor_analytics(
    vendor_id: int = Path(..., description="Vendor ID"),
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get analytics for a specific vendor."""
    try:
        marketplace_service = MarketplaceService(db)
        analytics = marketplace_service.get_vendor_analytics(vendor_id)

        return VendorAnalytics(**analytics)

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting vendor analytics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/analytics", response_model=MarketplaceAnalytics)
async def get_marketplace_analytics(
    current_user: str = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get overall marketplace analytics."""
    try:
        marketplace_service = MarketplaceService(db)
        analytics = marketplace_service.get_marketplace_analytics()

        return MarketplaceAnalytics(**analytics)

    except Exception as e:
        logger.error(f"Error getting marketplace analytics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# International Support
@router.post("/international/convert", response_model=CurrencyConversionResponse)
async def convert_currency(
    conversion_request: CurrencyConversionRequest,
    db: Session = Depends(get_db)
):
    """Convert currency using current exchange rates."""
    try:
        marketplace_service = MarketplaceService(db)
        conversion = marketplace_service.convert_currency(conversion_request)

        return CurrencyConversionResponse(**conversion)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error converting currency: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/international/currencies")
async def get_supported_currencies(
    db: Session = Depends(get_db)
):
    """Get list of supported currencies."""
    try:
        marketplace_service = MarketplaceService(db)
        currencies = marketplace_service.get_supported_currencies()

        return {"currencies": currencies}

    except Exception as e:
        logger.error(f"Error getting supported currencies: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/international/markets", response_model=List[InternationalMarketResponse])
async def get_international_markets(
    db: Session = Depends(get_db)
):
    """Get list of active international markets."""
    try:
        marketplace_service = MarketplaceService(db)
        markets = marketplace_service.get_international_markets()

        return [InternationalMarketResponse.from_orm(market) for market in markets]

    except Exception as e:
        logger.error(f"Error getting international markets: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Partnership Management
@router.post("/partnerships", response_model=PartnershipAgreementResponse, status_code=status.HTTP_201_CREATED)
async def create_partnership(
    partnership_data: PartnershipAgreementCreate,
    current_user: str = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Create a new partnership agreement."""
    try:
        marketplace_service = MarketplaceService(db)
        partnership = marketplace_service.create_partnership(partnership_data)

        return PartnershipAgreementResponse.from_orm(partnership)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating partnership: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Commission Management
@router.post("/commissions", response_model=CommissionRecordResponse, status_code=status.HTTP_201_CREATED)
async def create_commission_record(
    commission_data: CommissionRecordCreate,
    current_user: str = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Create a commission record."""
    try:
        marketplace_service = MarketplaceService(db)
        commission = marketplace_service.create_commission_record(commission_data)

        return CommissionRecordResponse.from_orm(commission)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating commission record: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Health Check
@router.get("/health")
async def marketplace_health_check():
    """Health check endpoint for marketplace service."""
    try:
        return {
            "status": "healthy",
            "service": "Marketplace & Integration Hub",
            "version": "1.0.0",
            "features": [
                "vendor_management",
                "course_marketplace",
                "partnership_agreements",
                "commission_tracking",
                "international_support",
                "currency_conversion",
                "analytics_dashboard",
                "course_search",
                "enrollment_management",
                "review_system"
            ],
            "supported_currencies": ["USD", "EUR", "GBP", "CAD", "AUD"],
            "supported_languages": ["en", "es", "fr", "de", "it", "pt", "ja", "ko", "zh"],
            "partnership_tiers": ["tier_1", "tier_2", "tier_3"],
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in marketplace health check: {e}")
        raise HTTPException(status_code=500, detail="Service unhealthy")
