"""FastAPI endpoints for compliance automation and regulatory reporting.

This module provides comprehensive API endpoints for compliance management
including GDPR, HIPAA, SOX reporting, audit trails, and risk assessment.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime, timed<PERSON>ta

from database import get_db
from services.compliance_service import ComplianceService
from models.compliance import ComplianceFramework, ComplianceStatus, RiskLevel
from schemas.compliance import (
    ComplianceRequirementResponse, ComplianceRequirementCreate, ComplianceRequirementUpdate,
    ComplianceAssessmentResponse, ComplianceAssessmentCreate,
    ComplianceReportResponse, ComplianceReportGenerate,
    AuditLogResponse, DataProcessingActivityResponse, DataProcessingActivityCreate
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/compliance", tags=["Compliance & Audit"])


def get_current_user_id() -> str:
    """Get current user ID from authentication context."""
    # TODO: Implement proper authentication
    return "user_123"


def get_current_organization_id() -> int:
    """Get current organization ID from authentication context."""
    # TODO: Implement proper organization context
    return 1


def require_compliance_admin():
    """Require compliance administrator permissions."""
    # TODO: Implement proper role-based access control
    pass


# Compliance Requirements Management

@router.post("/requirements", response_model=ComplianceRequirementResponse, status_code=status.HTTP_201_CREATED)
async def create_compliance_requirement(
    requirement_data: ComplianceRequirementCreate,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Create a new compliance requirement."""
    try:
        require_compliance_admin()
        org_id = get_current_organization_id()
        
        logger.info(f"Creating compliance requirement: {requirement_data.title}")
        
        compliance_service = ComplianceService(db)
        requirement_dict = requirement_data.dict()
        requirement_dict['organization_id'] = org_id
        
        requirement = compliance_service.create_compliance_requirement(requirement_dict)
        
        return ComplianceRequirementResponse(**requirement.to_dict())
        
    except Exception as e:
        logger.error(f"Error creating compliance requirement: {e}")
        raise HTTPException(status_code=500, detail="Error creating compliance requirement")


@router.get("/requirements", response_model=List[ComplianceRequirementResponse])
async def get_compliance_requirements(
    framework: Optional[ComplianceFramework] = Query(None, description="Filter by compliance framework"),
    status_filter: Optional[ComplianceStatus] = Query(None, description="Filter by compliance status"),
    risk_level: Optional[RiskLevel] = Query(None, description="Filter by risk level"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Get compliance requirements for the organization."""
    try:
        org_id = get_current_organization_id()
        
        compliance_service = ComplianceService(db)
        requirements = compliance_service.get_organization_requirements(org_id, framework)
        
        # Apply additional filters
        if status_filter:
            requirements = [req for req in requirements if req.status == status_filter]
        
        if risk_level:
            requirements = [req for req in requirements if req.risk_level == risk_level]
        
        return [ComplianceRequirementResponse(**req.to_dict()) for req in requirements]
        
    except Exception as e:
        logger.error(f"Error getting compliance requirements: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving compliance requirements")


@router.post("/requirements/{requirement_id}/assess", response_model=ComplianceAssessmentResponse)
async def assess_compliance_requirement(
    requirement_id: int = Path(..., description="Compliance requirement ID"),
    assessment_data: ComplianceAssessmentCreate = ...,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Create a compliance assessment for a requirement."""
    try:
        require_compliance_admin()
        user_id = get_current_user_id()
        org_id = get_current_organization_id()
        
        logger.info(f"Creating compliance assessment for requirement {requirement_id}")
        
        compliance_service = ComplianceService(db)
        assessment_dict = assessment_data.dict()
        assessment_dict.update({
            'assessor_id': user_id,
            'organization_id': org_id
        })
        
        assessment = compliance_service.assess_compliance_requirement(requirement_id, assessment_dict)
        
        return ComplianceAssessmentResponse(**assessment.to_dict())
        
    except Exception as e:
        logger.error(f"Error creating compliance assessment: {e}")
        raise HTTPException(status_code=500, detail="Error creating compliance assessment")


# Compliance Reporting

@router.post("/reports/generate", response_model=ComplianceReportResponse)
async def generate_compliance_report(
    report_request: ComplianceReportGenerate,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Generate a compliance report for the specified framework and period."""
    try:
        require_compliance_admin()
        org_id = get_current_organization_id()
        
        logger.info(f"Generating {report_request.framework.value} compliance report")
        
        compliance_service = ComplianceService(db)
        
        # Generate report based on framework
        if report_request.framework == ComplianceFramework.GDPR:
            report = compliance_service.generate_gdpr_report(
                org_id, report_request.period_start, report_request.period_end
            )
        elif report_request.framework == ComplianceFramework.HIPAA:
            report = compliance_service.generate_hipaa_report(
                org_id, report_request.period_start, report_request.period_end
            )
        elif report_request.framework == ComplianceFramework.SOX:
            report = compliance_service.generate_sox_report(
                org_id, report_request.period_start, report_request.period_end
            )
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"Report generation not implemented for {report_request.framework.value}"
            )
        
        return ComplianceReportResponse(**report.to_dict())
        
    except Exception as e:
        logger.error(f"Error generating compliance report: {e}")
        raise HTTPException(status_code=500, detail="Error generating compliance report")


@router.get("/reports", response_model=List[ComplianceReportResponse])
async def get_compliance_reports(
    framework: Optional[ComplianceFramework] = Query(None, description="Filter by compliance framework"),
    limit: int = Query(10, ge=1, le=100, description="Number of reports to return"),
    offset: int = Query(0, ge=0, description="Number of reports to skip"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Get compliance reports for the organization."""
    try:
        org_id = get_current_organization_id()
        
        from models.compliance import ComplianceReport
        
        query = db.query(ComplianceReport).filter(
            ComplianceReport.organization_id == org_id
        )
        
        if framework:
            query = query.filter(ComplianceReport.report_type == framework)
        
        reports = query.order_by(ComplianceReport.generated_at.desc()).offset(offset).limit(limit).all()
        
        return [ComplianceReportResponse(**report.to_dict()) for report in reports]
        
    except Exception as e:
        logger.error(f"Error getting compliance reports: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving compliance reports")


@router.get("/reports/{report_id}", response_model=ComplianceReportResponse)
async def get_compliance_report(
    report_id: int = Path(..., description="Compliance report ID"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Get a specific compliance report."""
    try:
        org_id = get_current_organization_id()
        
        from models.compliance import ComplianceReport
        
        report = db.query(ComplianceReport).filter(
            ComplianceReport.id == report_id,
            ComplianceReport.organization_id == org_id
        ).first()
        
        if not report:
            raise HTTPException(status_code=404, detail="Compliance report not found")
        
        return ComplianceReportResponse(**report.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting compliance report: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving compliance report")


# Audit Logs

@router.get("/audit/logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    start_date: Optional[datetime] = Query(None, description="Filter by start date"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date"),
    limit: int = Query(50, ge=1, le=1000, description="Number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Get audit logs for the organization."""
    try:
        require_compliance_admin()
        org_id = get_current_organization_id()
        
        from models.compliance import AuditLog
        
        query = db.query(AuditLog).filter(
            AuditLog.organization_id == org_id
        )
        
        if event_type:
            query = query.filter(AuditLog.event_type == event_type)
        
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        if start_date:
            query = query.filter(AuditLog.created_at >= start_date)
        
        if end_date:
            query = query.filter(AuditLog.created_at <= end_date)
        
        logs = query.order_by(AuditLog.created_at.desc()).offset(offset).limit(limit).all()
        
        return [AuditLogResponse(**log.to_dict()) for log in logs]
        
    except Exception as e:
        logger.error(f"Error getting audit logs: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving audit logs")


# Data Processing Activities (GDPR)

@router.post("/gdpr/data-processing-activities", response_model=DataProcessingActivityResponse)
async def create_data_processing_activity(
    activity_data: DataProcessingActivityCreate,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Create a new GDPR data processing activity."""
    try:
        require_compliance_admin()
        org_id = get_current_organization_id()
        
        from models.compliance import DataProcessingActivity
        
        activity_dict = activity_data.dict()
        activity_dict['organization_id'] = org_id
        
        activity = DataProcessingActivity(**activity_dict)
        db.add(activity)
        db.commit()
        db.refresh(activity)
        
        logger.info(f"Created data processing activity: {activity.activity_name}")
        
        return DataProcessingActivityResponse(**activity.to_dict())
        
    except Exception as e:
        logger.error(f"Error creating data processing activity: {e}")
        raise HTTPException(status_code=500, detail="Error creating data processing activity")


@router.get("/gdpr/data-processing-activities", response_model=List[DataProcessingActivityResponse])
async def get_data_processing_activities(
    db: Session = Depends(get_db),
    _: str = Depends(get_current_user_id)
):
    """Get GDPR data processing activities for the organization."""
    try:
        org_id = get_current_organization_id()
        
        from models.compliance import DataProcessingActivity
        
        activities = db.query(DataProcessingActivity).filter(
            DataProcessingActivity.organization_id == org_id,
            DataProcessingActivity.deleted_at.is_(None)
        ).all()
        
        return [DataProcessingActivityResponse(**activity.to_dict()) for activity in activities]
        
    except Exception as e:
        logger.error(f"Error getting data processing activities: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving data processing activities")


@router.get("/health")
async def health_check():
    """Health check endpoint for compliance service."""
    return {
        'status': 'healthy',
        'service': 'Compliance & Audit',
        'version': '1.0.0',
        'features': [
            'compliance_requirements',
            'compliance_assessments',
            'gdpr_reporting',
            'hipaa_reporting',
            'sox_reporting',
            'audit_logging',
            'data_processing_activities'
        ],
        'timestamp': datetime.now().isoformat()
    }
