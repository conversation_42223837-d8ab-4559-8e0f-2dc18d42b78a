"""Organization management API endpoints for core platform"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
from datetime import datetime

from database import get_db
from api.v1.auth import get_current_user
from models.user import User
from models.certification import Organization
from schemas.organization import (
    OrganizationResponse,
    OrganizationCreate,
    OrganizationUpdate,
    OrganizationList,
    TeamInviteRequest,
    TeamMemberResponse
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/organizations", tags=["Organizations"])


@router.get("/", response_model=OrganizationList)
async def list_organizations(
    search: Optional[str] = Query(None, description="Search organizations by name"),
    category: Optional[str] = Query(None, description="Filter by category"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get list of certification organizations (GET /api/v1/organizations)"""
    try:
        logger.info("Retrieving organizations list")
        
        # Build query
        query = db.query(Organization)
        
        # Apply filters
        if search:
            query = query.filter(Organization.name.ilike(f"%{search}%"))
        
        if category:
            query = query.filter(Organization.category.ilike(f"%{category}%"))
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        offset = (page - 1) * page_size
        organizations = query.order_by(Organization.name).offset(offset).limit(page_size).all()
        
        return OrganizationList(
            organizations=[org.to_dict() for org in organizations],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=(total_count + page_size - 1) // page_size
        )
        
    except Exception as e:
        logger.error(f"Error retrieving organizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organizations"
        )


@router.get("/{org_id}", response_model=OrganizationResponse)
async def get_organization(
    org_id: int,
    db: Session = Depends(get_db)
):
    """Get organization details (GET /api/v1/organizations/{id})"""
    try:
        logger.info(f"Retrieving organization details for ID: {org_id}")
        
        organization = db.query(Organization).filter(Organization.id == org_id).first()
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        return organization.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving organization details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization details"
        )


@router.post("/", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED)
async def create_organization(
    org_data: OrganizationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new organization (POST /api/v1/organizations)"""
    try:
        logger.info(f"Creating organization: {org_data.name}")
        
        # Check if organization name already exists
        existing_org = db.query(Organization).filter(Organization.name == org_data.name).first()
        if existing_org:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Organization with this name already exists"
            )
        
        # Create organization
        organization = Organization(
            name=org_data.name,
            description=org_data.description,
            website=org_data.website,
            category=org_data.category,
            logo_url=org_data.logo_url,
            contact_email=org_data.contact_email,
            contact_phone=org_data.contact_phone,
            address=org_data.address,
            is_verified=False,  # New organizations need verification
            created_by=current_user.user_id
        )
        
        db.add(organization)
        db.commit()
        db.refresh(organization)
        
        logger.info(f"Organization created successfully: {organization.name} (ID: {organization.id})")
        return organization.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating organization: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization"
        )


@router.put("/{org_id}", response_model=OrganizationResponse)
async def update_organization(
    org_id: int,
    update_data: OrganizationUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update organization (PUT /api/v1/organizations/{id})"""
    try:
        logger.info(f"Updating organization: {org_id}")
        
        organization = db.query(Organization).filter(Organization.id == org_id).first()
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Check if user has permission to update (for now, any authenticated user can update)
        # In production, implement proper authorization
        
        # Update fields
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            if hasattr(organization, field):
                setattr(organization, field, value)
        
        organization.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(organization)
        
        logger.info(f"Organization updated successfully: {organization.name}")
        return organization.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating organization: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update organization"
        )


@router.get("/{org_id}/users", response_model=List[TeamMemberResponse])
async def get_organization_users(
    org_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get organization team members (GET /api/v1/organizations/{id}/users)"""
    try:
        logger.info(f"Retrieving team members for organization: {org_id}")
        
        organization = db.query(Organization).filter(Organization.id == org_id).first()
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # For now, return basic info since we don't have a full team management system
        # In a real implementation, you'd have a separate table for organization memberships
        
        # Mock response for demonstration
        team_members = []
        
        # If the current user created this organization, show them as admin
        if hasattr(organization, 'created_by') and organization.created_by == current_user.user_id:
            team_members.append({
                "user_id": current_user.user_id,
                "email": current_user.email,
                "name": current_user.name,
                "role": "admin",
                "joined_at": organization.created_at.isoformat() if organization.created_at else None,
                "is_active": True
            })
        
        return team_members
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving organization users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization users"
        )


@router.post("/{org_id}/invite", status_code=status.HTTP_202_ACCEPTED)
async def invite_user_to_organization(
    org_id: int,
    invite_data: TeamInviteRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Invite user to organization (POST /api/v1/organizations/{id}/invite)"""
    try:
        logger.info(f"Inviting user to organization {org_id}: {invite_data.email}")
        
        organization = db.query(Organization).filter(Organization.id == org_id).first()
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Check if user has permission to invite (for now, any authenticated user can invite)
        # In production, implement proper authorization
        
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == invite_data.email).first()
        
        if existing_user:
            # User exists, could add them directly or send notification
            logger.info(f"User {invite_data.email} already exists, invitation would be sent")
        else:
            # User doesn't exist, would send invitation email
            logger.info(f"User {invite_data.email} doesn't exist, invitation email would be sent")
        
        # TODO: Implement actual invitation system with email notifications
        # For now, just return success
        
        return {
            "message": f"Invitation sent to {invite_data.email}",
            "organization_id": org_id,
            "invited_email": invite_data.email,
            "role": invite_data.role,
            "invited_by": current_user.email
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error inviting user to organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send invitation"
        )


@router.get("/categories", response_model=List[str])
async def get_organization_categories(
    db: Session = Depends(get_db)
):
    """Get available organization categories"""
    try:
        logger.info("Retrieving organization categories")
        
        # Get unique categories from existing organizations
        categories = db.query(Organization.category).filter(
            Organization.category.isnot(None)
        ).distinct().all()
        
        category_list = [cat[0] for cat in categories if cat[0]]
        
        # Add some default categories if none exist
        if not category_list:
            category_list = [
                "Technology",
                "Education",
                "Healthcare",
                "Finance",
                "Government",
                "Non-Profit",
                "Consulting",
                "Training Provider"
            ]
        
        return sorted(category_list)
        
    except Exception as e:
        logger.error(f"Error retrieving organization categories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization categories"
        )
