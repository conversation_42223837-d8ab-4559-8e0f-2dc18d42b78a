"""Study timer API endpoints for managing study sessions, goals, and streaks.

This module provides RESTful API endpoints for:
- Study session CRUD operations
- Study session control (start, pause, resume, complete, cancel)
- Study goal management
- Study streak tracking
- Study statistics and analytics
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date
import logging

from database import get_db
from services.study_timer import StudyTimerService
from schemas.study_session import (
    StudySessionCreate, StudySessionUpdate, StudySessionResponse,
    StudyGoalCreate, StudyGoalUpdate, StudyGoalResponse,
    StudyStreakResponse, SessionControlRequest, StudyStatistics,
    SessionListResponse, GoalListResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/study-timer", tags=["Study Timer"])

# Dependency to get current user ID (placeholder - replace with actual auth)
def get_current_user_id() -> str:
    """Get current user ID from authentication context.
    
    TODO: Replace with actual authentication logic
    """
    return "test_user_1"  # Placeholder


# Study Session Endpoints
@router.post("/sessions", response_model=StudySessionResponse, status_code=status.HTTP_201_CREATED)
def create_study_session(
    session_data: StudySessionCreate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StudySessionResponse:
    """Create a new study session.
    
    Args:
        session_data: Study session creation data
        db: Database session
        user_id: Current user ID
        
    Returns:
        Created study session
        
    Raises:
        HTTPException: If validation fails or certification/learning path item not found
    """
    try:
        service = StudyTimerService(db)
        session = service.create_study_session(user_id, session_data)
        return StudySessionResponse.from_orm(session)
    except ValueError as e:
        logger.error(f"Error creating study session: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error creating study session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/sessions", response_model=SessionListResponse)
def get_study_sessions(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Number of sessions per page"),
    status: Optional[str] = Query(None, description="Filter by session status"),
    certification_id: Optional[int] = Query(None, description="Filter by certification ID"),
    start_date: Optional[date] = Query(None, description="Filter by start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="Filter by end date (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> SessionListResponse:
    """Get paginated list of study sessions for the current user.
    
    Args:
        page: Page number (1-based)
        page_size: Number of sessions per page
        status: Optional status filter
        certification_id: Optional certification filter
        start_date: Optional start date filter
        end_date: Optional end date filter
        db: Database session
        user_id: Current user ID
        
    Returns:
        Paginated list of study sessions
    """
    try:
        service = StudyTimerService(db)
        sessions, total_count = service.get_user_study_sessions(
            user_id=user_id,
            page=page,
            page_size=page_size,
            status=status,
            certification_id=certification_id,
            start_date=start_date,
            end_date=end_date
        )
        
        total_pages = (total_count + page_size - 1) // page_size
        
        return SessionListResponse(
            sessions=[StudySessionResponse.from_orm(session) for session in sessions],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    except Exception as e:
        logger.error(f"Error getting study sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/sessions/{session_id}", response_model=StudySessionResponse)
def get_study_session(
    session_id: int,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StudySessionResponse:
    """Get a specific study session by ID.
    
    Args:
        session_id: ID of the study session
        db: Database session
        user_id: Current user ID
        
    Returns:
        Study session details
        
    Raises:
        HTTPException: If session not found
    """
    try:
        service = StudyTimerService(db)
        session = service.get_study_session(session_id, user_id)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Study session not found"
            )
        
        return StudySessionResponse.from_orm(session)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting study session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/sessions/{session_id}", response_model=StudySessionResponse)
def update_study_session(
    session_id: int,
    update_data: StudySessionUpdate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StudySessionResponse:
    """Update a study session.
    
    Args:
        session_id: ID of the study session
        update_data: Update data
        db: Database session
        user_id: Current user ID
        
    Returns:
        Updated study session
        
    Raises:
        HTTPException: If session not found
    """
    try:
        service = StudyTimerService(db)
        session = service.update_study_session(session_id, user_id, update_data)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Study session not found"
            )
        
        return StudySessionResponse.from_orm(session)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating study session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/sessions/{session_id}/control", response_model=StudySessionResponse)
def control_study_session(
    session_id: int,
    control_request: SessionControlRequest,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StudySessionResponse:
    """Control a study session (start, pause, resume, complete, cancel).
    
    Args:
        session_id: ID of the study session
        control_request: Control action and parameters
        db: Database session
        user_id: Current user ID
        
    Returns:
        Updated study session
        
    Raises:
        HTTPException: If session not found or action invalid
    """
    try:
        service = StudyTimerService(db)
        session = service.control_study_session(
            session_id=session_id,
            user_id=user_id,
            action=control_request.action,
            notes=control_request.notes,
            focus_rating=control_request.focus_rating,
            difficulty_rating=control_request.difficulty_rating,
            satisfaction_rating=control_request.satisfaction_rating
        )
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Study session not found"
            )
        
        return StudySessionResponse.from_orm(session)
    except ValueError as e:
        logger.error(f"Error controlling study session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error controlling study session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/sessions/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_study_session(
    session_id: int,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> None:
    """Delete a study session.
    
    Args:
        session_id: ID of the study session
        db: Database session
        user_id: Current user ID
        
    Raises:
        HTTPException: If session not found
    """
    try:
        service = StudyTimerService(db)
        deleted = service.delete_study_session(session_id, user_id)
        
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Study session not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting study session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/goals/{goal_id}", response_model=StudyGoalResponse)
def update_study_goal(
    goal_id: int,
    update_data: StudyGoalUpdate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StudyGoalResponse:
    """Update a study goal.

    Args:
        goal_id: ID of the study goal
        update_data: Update data
        db: Database session
        user_id: Current user ID

    Returns:
        Updated study goal

    Raises:
        HTTPException: If goal not found
    """
    try:
        service = StudyTimerService(db)
        goal = service.update_study_goal(goal_id, user_id, update_data)

        if not goal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Study goal not found"
            )

        return StudyGoalResponse.from_orm(goal)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating study goal {goal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/goals/{goal_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_study_goal(
    goal_id: int,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> None:
    """Delete a study goal.

    Args:
        goal_id: ID of the study goal
        db: Database session
        user_id: Current user ID

    Raises:
        HTTPException: If goal not found
    """
    try:
        service = StudyTimerService(db)
        deleted = service.delete_study_goal(goal_id, user_id)

        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Study goal not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting study goal {goal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# Study Streak and Statistics Endpoints
@router.get("/streak", response_model=StudyStreakResponse)
def get_study_streak(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StudyStreakResponse:
    """Get study streak information for the current user.

    Args:
        db: Database session
        user_id: Current user ID

    Returns:
        Study streak information
    """
    try:
        service = StudyTimerService(db)
        streak = service.get_user_study_streak(user_id)
        return StudyStreakResponse.from_orm(streak)
    except Exception as e:
        logger.error(f"Error getting study streak: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/statistics", response_model=StudyStatistics)
def get_study_statistics(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StudyStatistics:
    """Get comprehensive study statistics for the current user.

    Args:
        db: Database session
        user_id: Current user ID

    Returns:
        Study statistics
    """
    try:
        service = StudyTimerService(db)
        statistics = service.get_study_statistics(user_id)
        return statistics
    except Exception as e:
        logger.error(f"Error getting study statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# Study Goal Endpoints
@router.post("/goals", response_model=StudyGoalResponse, status_code=status.HTTP_201_CREATED)
def create_study_goal(
    goal_data: StudyGoalCreate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StudyGoalResponse:
    """Create a new study goal.
    
    Args:
        goal_data: Study goal creation data
        db: Database session
        user_id: Current user ID
        
    Returns:
        Created study goal
        
    Raises:
        HTTPException: If validation fails or certification not found
    """
    try:
        service = StudyTimerService(db)
        goal = service.create_study_goal(user_id, goal_data)
        return StudyGoalResponse.from_orm(goal)
    except ValueError as e:
        logger.error(f"Error creating study goal: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error creating study goal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/goals", response_model=GoalListResponse)
def get_study_goals(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Number of goals per page"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    goal_type: Optional[str] = Query(None, description="Filter by goal type"),
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> GoalListResponse:
    """Get paginated list of study goals for the current user.
    
    Args:
        page: Page number (1-based)
        page_size: Number of goals per page
        is_active: Optional active status filter
        goal_type: Optional goal type filter
        db: Database session
        user_id: Current user ID
        
    Returns:
        Paginated list of study goals
    """
    try:
        service = StudyTimerService(db)
        goals, total_count = service.get_user_study_goals(
            user_id=user_id,
            page=page,
            page_size=page_size,
            is_active=is_active,
            goal_type=goal_type
        )
        
        total_pages = (total_count + page_size - 1) // page_size
        
        return GoalListResponse(
            goals=[StudyGoalResponse.from_orm(goal) for goal in goals],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    except Exception as e:
        logger.error(f"Error getting study goals: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
