"""FastAPI application configuration"""
import os
import logging
from pathlib import Path
from typing import Dict, Any, List, Union
from pydantic import field_validator
from pydantic_settings import BaseSettings

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """Application settings with environment variable support"""

    # API Configuration
    API_V1_PREFIX: str = "/api/v1"
    PROJECT_NAME: str = "CertPathFinder API"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "The Ultimate Cybersecurity Certification Journey Planner API"

    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False
    ENVIRONMENT: str = "development"

    # CORS Configuration
    CORS_ORIGINS: Union[str, List[str]] = ["*"]  # In production, replace with specific origins
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    # Database Configuration
    DATABASE_URL: str = ""
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600

    # Redis Configuration (for caching)
    REDIS_URL: str = ""
    REDIS_TTL: int = 3600

    # Authentication Configuration
    SECRET_KEY: str = ""
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # AI Configuration
    ANTHROPIC_API_KEY: str = ""
    OPENAI_API_KEY: str = ""

    # External Services
    TWILIO_ACCOUNT_SID: str = ""
    TWILIO_AUTH_TOKEN: str = ""

    # File Upload Configuration
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = ["pdf", "doc", "docx", "txt"]

    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60

    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def validate_database_url(cls, v):
        if not v:
            # Default to SQLite for development
            return "sqlite:///./certpathfinder.db"
        return v

    @field_validator("SECRET_KEY", mode="before")
    @classmethod
    def validate_secret_key(cls, v):
        if not v:
            logger.warning("SECRET_KEY not set, using default (not secure for production)")
            return "dev-secret-key-change-in-production"
        return v

    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def validate_cors_origins(cls, v):
        if isinstance(v, str):
            if not v.strip():  # Handle empty strings
                return ["*"]  # Default to allow all origins for development
            return [origin.strip() for origin in v.split(",")]
        return v

    @property
    def fastapi_kwargs(self) -> Dict[str, Any]:
        """FastAPI initialization arguments"""
        return {
            "title": self.PROJECT_NAME,
            "description": self.DESCRIPTION,
            "version": self.VERSION,
            "docs_url": "/docs" if self.DEBUG else None,
            "redoc_url": "/redoc" if self.DEBUG else None,
            "openapi_url": "/openapi.json" if self.DEBUG else None,
        }

    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.ENVIRONMENT.lower() == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.ENVIRONMENT.lower() == "development"

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True
    }

# Create global settings instance
settings = Settings()