"""
Test-Driven Development (TDD) tests for Dashboard API
Following TDD principles: Red -> Green -> Refactor
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch
from datetime import datetime

from api.app import app
from database import get_db
from api.v1.auth import get_current_user
from models.user import User


class TestDashboardAPI:
    """TDD Test suite for Dashboard API endpoints"""
    
    def setup_method(self):
        """Set up test client and mock dependencies"""
        self.client = TestClient(app)
        self.mock_db = Mock(spec=Session)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 1
        self.mock_user.email = "<EMAIL>"
        self.mock_user.full_name = "Test User"
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.mock_db
        app.dependency_overrides[get_current_user] = lambda: self.mock_user
    
    def teardown_method(self):
        """Clean up after each test"""
        app.dependency_overrides.clear()

    # RED: Write failing tests first
    
    def test_get_dashboard_overview_success_should_return_complete_data(self):
        """
        TDD RED: Test dashboard overview success
        Should return complete dashboard data including stats and activities
        """
        # Arrange
        mock_learning_paths = [
            Mock(id=1, name="AWS Path", description="AWS Learning", progress_percentage=75, created_at=datetime.now()),
            Mock(id=2, name="Azure Path", description="Azure Learning", progress_percentage=50, created_at=datetime.now())
        ]
        
        mock_progress_records = [
            Mock(id=1, status='completed', certification=Mock(name="AWS SAA"), updated_at=datetime.now(), study_hours_logged=40),
            Mock(id=2, status='in_progress', certification=Mock(name="Azure AZ-104"), updated_at=datetime.now(), study_hours_logged=20),
            Mock(id=3, status='completed', certification=Mock(name="GCP ACE"), updated_at=datetime.now(), study_hours_logged=35)
        ]
        
        mock_certifications = [
            Mock(id=1, name="AWS SAA", provider="AWS", difficulty_level="Intermediate", estimated_study_hours=60),
            Mock(id=2, name="Azure AZ-104", provider="Microsoft", difficulty_level="Intermediate", estimated_study_hours=50)
        ]
        
        # Mock database queries
        self.mock_db.query.return_value.filter.return_value.all.side_effect = [
            mock_learning_paths,  # First call for learning paths
            mock_progress_records  # Second call for progress records
        ]
        self.mock_db.query.return_value.limit.return_value.all.return_value = mock_certifications
        
        expected_response = {
            "user": {
                "id": 1,
                "name": "Test User",
                "email": "<EMAIL>"
            },
            "statistics": {
                "total_certifications": 2,  # 2 completed
                "in_progress": 1,  # 1 in progress
                "total_paths": 2,  # 2 learning paths
                "completion_percentage": 66.7  # 2/3 * 100
            },
            "recent_activity": [
                {
                    "id": 1,
                    "certification_name": "AWS SAA",
                    "status": "completed",
                    "updated_at": mock_progress_records[0].updated_at.isoformat()
                },
                {
                    "id": 2,
                    "certification_name": "Azure AZ-104",
                    "status": "in_progress",
                    "updated_at": mock_progress_records[1].updated_at.isoformat()
                },
                {
                    "id": 3,
                    "certification_name": "GCP ACE",
                    "status": "completed",
                    "updated_at": mock_progress_records[2].updated_at.isoformat()
                }
            ],
            "learning_paths": [
                {
                    "id": 1,
                    "name": "AWS Path",
                    "description": "AWS Learning",
                    "progress": 75,
                    "created_at": mock_learning_paths[0].created_at.isoformat()
                },
                {
                    "id": 2,
                    "name": "Azure Path",
                    "description": "Azure Learning",
                    "progress": 50,
                    "created_at": mock_learning_paths[1].created_at.isoformat()
                }
            ],
            "recommended_certifications": [
                {
                    "id": 1,
                    "name": "AWS SAA",
                    "provider": "AWS",
                    "difficulty": "Intermediate",
                    "estimated_hours": 60
                },
                {
                    "id": 2,
                    "name": "Azure AZ-104",
                    "provider": "Microsoft",
                    "difficulty": "Intermediate",
                    "estimated_hours": 50
                }
            ]
        }
        
        # Act
        response = self.client.get("/dashboard/overview")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["user"]["id"] == expected_response["user"]["id"]
        assert response_data["statistics"]["total_certifications"] == expected_response["statistics"]["total_certifications"]
        assert len(response_data["learning_paths"]) == 2
        assert len(response_data["recommended_certifications"]) == 2

    def test_get_dashboard_overview_no_data_should_return_empty_stats(self):
        """
        TDD RED: Test dashboard overview with no user data
        Should return zero statistics and empty arrays
        """
        # Arrange - Mock empty results
        self.mock_db.query.return_value.filter.return_value.all.return_value = []
        self.mock_db.query.return_value.limit.return_value.all.return_value = []
        
        # Act
        response = self.client.get("/dashboard/overview")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["statistics"]["total_certifications"] == 0
        assert response_data["statistics"]["in_progress"] == 0
        assert response_data["statistics"]["total_paths"] == 0
        assert response_data["statistics"]["completion_percentage"] == 0
        assert len(response_data["recent_activity"]) == 0
        assert len(response_data["learning_paths"]) == 0

    def test_get_user_notifications_success_should_return_notifications(self):
        """
        TDD RED: Test getting user notifications
        Should return list of notifications with metadata
        """
        # Act
        response = self.client.get("/dashboard/notifications")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "notifications" in response_data
        assert "total_count" in response_data
        assert "unread_count" in response_data
        assert len(response_data["notifications"]) > 0

    def test_get_user_notifications_unread_only_should_filter_notifications(self):
        """
        TDD RED: Test getting only unread notifications
        Should return only unread notifications
        """
        # Act
        response = self.client.get("/dashboard/notifications?unread_only=true")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        # All returned notifications should be unread
        for notification in response_data["notifications"]:
            assert notification["read"] == False

    def test_get_user_notifications_with_limit_should_respect_limit(self):
        """
        TDD RED: Test notifications with limit parameter
        Should return limited number of notifications
        """
        # Act
        response = self.client.get("/dashboard/notifications?limit=2")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["notifications"]) <= 2

    def test_mark_notification_read_success_should_return_success_message(self):
        """
        TDD RED: Test marking notification as read
        Should return success message
        """
        # Act
        response = self.client.post("/dashboard/notifications/1/mark-read")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "message" in response_data
        assert "marked as read" in response_data["message"]

    def test_get_quick_stats_success_should_return_statistics(self):
        """
        TDD RED: Test getting quick statistics
        Should return dashboard widget statistics
        """
        # Arrange
        mock_progress_records = [
            Mock(status='completed', updated_at=datetime(2023, 12, 1), study_hours_logged=10),
            Mock(status='completed', updated_at=datetime(2023, 12, 15), study_hours_logged=15),
            Mock(status='in_progress', updated_at=datetime(2023, 11, 20), study_hours_logged=8)
        ]
        
        self.mock_db.query.return_value.filter.return_value.all.return_value = mock_progress_records
        
        # Act
        response = self.client.get("/dashboard/quick-stats")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "completed_this_month" in response_data
        assert "study_streak_days" in response_data
        assert "next_exam_in_days" in response_data
        assert "total_study_hours" in response_data
        assert response_data["total_study_hours"] == 33  # 10 + 15 + 8

    def test_get_recent_activity_success_should_return_activity_list(self):
        """
        TDD RED: Test getting recent activity
        Should return list of recent user activities
        """
        # Arrange
        mock_progress_records = [
            Mock(
                id=1, 
                certification_id=1, 
                certification=Mock(name="AWS SAA"), 
                status='completed',
                progress_percentage=100,
                updated_at=datetime.now()
            ),
            Mock(
                id=2, 
                certification_id=2, 
                certification=Mock(name="Azure AZ-104"), 
                status='in_progress',
                progress_percentage=75,
                updated_at=datetime.now()
            )
        ]
        
        self.mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = mock_progress_records
        
        # Act
        response = self.client.get("/dashboard/recent-activity")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "activities" in response_data
        assert "total_count" in response_data
        assert len(response_data["activities"]) == 2
        
        # Check activity structure
        activity = response_data["activities"][0]
        assert "id" in activity
        assert "type" in activity
        assert "certification_name" in activity
        assert "status" in activity
        assert "progress_percentage" in activity
        assert "updated_at" in activity

    def test_get_recent_activity_with_limit_should_respect_limit(self):
        """
        TDD RED: Test recent activity with limit parameter
        Should return limited number of activities
        """
        # Arrange
        mock_progress_records = [Mock() for _ in range(5)]  # 5 mock records
        self.mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = mock_progress_records[:3]  # Limit to 3
        
        # Act
        response = self.client.get("/dashboard/recent-activity?limit=3")
        
        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["activities"]) <= 3

    def test_dashboard_endpoints_unauthorized_should_return_401(self):
        """
        TDD RED: Test dashboard endpoints without authentication
        Should return 401 Unauthorized status
        """
        # Remove the auth override to test unauthorized access
        app.dependency_overrides.clear()
        app.dependency_overrides[get_db] = lambda: self.mock_db
        
        endpoints = [
            "/dashboard/overview",
            "/dashboard/notifications",
            "/dashboard/quick-stats",
            "/dashboard/recent-activity"
        ]
        
        for endpoint in endpoints:
            response = self.client.get(endpoint)
            assert response.status_code == 403  # FastAPI returns 403 for missing auth

    def test_dashboard_database_error_should_return_500(self):
        """
        TDD RED: Test dashboard endpoints with database errors
        Should return 500 Internal Server Error
        """
        # Arrange - Mock database error
        self.mock_db.query.side_effect = Exception("Database connection failed")
        
        # Act
        response = self.client.get("/dashboard/overview")
        
        # Assert
        assert response.status_code == 500
        assert "Failed to load dashboard data" in response.json()["detail"]
