"""
Test-Driven Development (TDD) tests for Authentication API
Following TDD principles: Red -> Green -> Refactor
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch
import json

from api.app import app
from database import get_db
from models.user import User
from schemas.auth import UserRegistrationRequest, UserLoginRequest


class TestAuthenticationAPI:
    """TDD Test suite for Authentication API endpoints"""
    
    def setup_method(self):
        """Set up test client and mock database"""
        self.client = TestClient(app)
        self.mock_db = Mock(spec=Session)
        
        # Override database dependency
        app.dependency_overrides[get_db] = lambda: self.mock_db
    
    def teardown_method(self):
        """Clean up after each test"""
        app.dependency_overrides.clear()

    # RED: Write failing tests first
    
    def test_register_user_success_should_return_201_with_auth_response(self):
        """
        TDD RED: Test user registration success
        Should return 201 status with authentication response
        """
        # Arrange
        registration_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "full_name": "Test User",
            "confirm_password": "password123"
        }
        
        expected_response = {
            "access_token": "mock_access_token",
            "refresh_token": "mock_refresh_token",
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": 1,
                "email": "<EMAIL>",
                "full_name": "Test User",
                "is_verified": False
            }
        }
        
        # Mock the auth service
        with patch('api.v1.auth.AuthenticationService') as mock_auth_service:
            mock_service = mock_auth_service.return_value
            mock_service.register_user.return_value = (True, "Success", Mock())
            mock_service.authenticate_user.return_value = (True, "Success", expected_response)
            
            # Act
            response = self.client.post("/auth/register", json=registration_data)
            
            # Assert
            assert response.status_code == 201
            assert response.json() == expected_response
            mock_service.register_user.assert_called_once()
            mock_service.authenticate_user.assert_called_once()

    def test_register_user_duplicate_email_should_return_409(self):
        """
        TDD RED: Test user registration with duplicate email
        Should return 409 Conflict status
        """
        # Arrange
        registration_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "full_name": "Test User",
            "confirm_password": "password123"
        }
        
        # Mock the auth service to return duplicate email error
        with patch('api.v1.auth.AuthenticationService') as mock_auth_service:
            mock_service = mock_auth_service.return_value
            mock_service.register_user.return_value = (False, "Email already exists", None)
            
            # Act
            response = self.client.post("/auth/register", json=registration_data)
            
            # Assert
            assert response.status_code == 409
            assert "already exists" in response.json()["detail"]

    def test_register_user_invalid_data_should_return_400(self):
        """
        TDD RED: Test user registration with invalid data
        Should return 400 Bad Request status
        """
        # Arrange
        invalid_registration_data = {
            "email": "invalid-email",  # Invalid email format
            "password": "123",  # Too short
            "full_name": "",  # Empty name
            "confirm_password": "different"  # Passwords don't match
        }
        
        # Act
        response = self.client.post("/auth/register", json=invalid_registration_data)
        
        # Assert
        assert response.status_code == 422  # FastAPI validation error

    def test_login_user_success_should_return_200_with_tokens(self):
        """
        TDD RED: Test successful user login
        Should return 200 status with access and refresh tokens
        """
        # Arrange
        login_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "remember_me": False
        }
        
        expected_response = {
            "access_token": "mock_access_token",
            "refresh_token": "mock_refresh_token",
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": 1,
                "email": "<EMAIL>",
                "full_name": "Test User",
                "is_verified": True
            }
        }
        
        # Mock the auth service
        with patch('api.v1.auth.AuthenticationService') as mock_auth_service:
            mock_service = mock_auth_service.return_value
            mock_service.authenticate_user.return_value = (True, "Success", expected_response)
            
            # Act
            response = self.client.post("/auth/login", json=login_data)
            
            # Assert
            assert response.status_code == 200
            assert response.json() == expected_response
            mock_service.authenticate_user.assert_called_once()

    def test_login_user_invalid_credentials_should_return_401(self):
        """
        TDD RED: Test login with invalid credentials
        Should return 401 Unauthorized status
        """
        # Arrange
        login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword",
            "remember_me": False
        }
        
        # Mock the auth service to return authentication failure
        with patch('api.v1.auth.AuthenticationService') as mock_auth_service:
            mock_service = mock_auth_service.return_value
            mock_service.authenticate_user.return_value = (False, "Invalid credentials", None)
            
            # Act
            response = self.client.post("/auth/login", json=login_data)
            
            # Assert
            assert response.status_code == 401
            assert "Invalid credentials" in response.json()["detail"]

    def test_login_user_account_locked_should_return_423(self):
        """
        TDD RED: Test login with locked account
        Should return 423 Locked status
        """
        # Arrange
        login_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "remember_me": False
        }
        
        # Mock the auth service to return account locked error
        with patch('api.v1.auth.AuthenticationService') as mock_auth_service:
            mock_service = mock_auth_service.return_value
            mock_service.authenticate_user.return_value = (False, "Account is locked", None)
            
            # Act
            response = self.client.post("/auth/login", json=login_data)
            
            # Assert
            assert response.status_code == 423
            assert "locked" in response.json()["detail"].lower()

    def test_refresh_token_success_should_return_new_tokens(self):
        """
        TDD RED: Test successful token refresh
        Should return new access and refresh tokens
        """
        # Arrange
        refresh_data = {
            "refresh_token": "valid_refresh_token"
        }
        
        expected_response = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": 1,
                "email": "<EMAIL>",
                "full_name": "Test User",
                "is_verified": True
            }
        }
        
        # Mock the auth service
        with patch('api.v1.auth.AuthenticationService') as mock_auth_service:
            mock_service = mock_auth_service.return_value
            mock_service.refresh_token.return_value = (True, "Success", expected_response)
            
            # Act
            response = self.client.post("/auth/refresh", json=refresh_data)
            
            # Assert
            assert response.status_code == 200
            assert response.json() == expected_response

    def test_refresh_token_invalid_should_return_401(self):
        """
        TDD RED: Test token refresh with invalid token
        Should return 401 Unauthorized status
        """
        # Arrange
        refresh_data = {
            "refresh_token": "invalid_refresh_token"
        }
        
        # Mock the auth service to return invalid token error
        with patch('api.v1.auth.AuthenticationService') as mock_auth_service:
            mock_service = mock_auth_service.return_value
            mock_service.refresh_token.return_value = (False, "Invalid refresh token", None)
            
            # Act
            response = self.client.post("/auth/refresh", json=refresh_data)
            
            # Assert
            assert response.status_code == 401
            assert "Invalid refresh token" in response.json()["detail"]

    def test_logout_user_success_should_return_200(self):
        """
        TDD RED: Test successful user logout
        Should return 200 status with success message
        """
        # Arrange
        headers = {"Authorization": "Bearer valid_access_token"}
        
        # Mock the auth service
        with patch('api.v1.auth.AuthenticationService') as mock_auth_service:
            mock_service = mock_auth_service.return_value
            mock_service.logout_user.return_value = (True, "Logged out successfully")
            
            # Act
            response = self.client.post("/auth/logout", headers=headers)
            
            # Assert
            assert response.status_code == 200
            assert "Logged out successfully" in response.json()["message"]

    def test_get_current_user_profile_success_should_return_user_data(self):
        """
        TDD RED: Test getting current user profile
        Should return user profile data
        """
        # Arrange
        headers = {"Authorization": "Bearer valid_access_token"}
        
        expected_user_data = {
            "id": 1,
            "email": "<EMAIL>",
            "full_name": "Test User",
            "is_verified": True,
            "created_at": "2023-01-01T00:00:00",
            "preferences": {}
        }
        
        # Mock the get_current_user dependency
        with patch('api.v1.auth.get_current_user') as mock_get_user:
            mock_user = Mock()
            mock_user.id = 1
            mock_user.email = "<EMAIL>"
            mock_user.full_name = "Test User"
            mock_user.is_verified = True
            mock_get_user.return_value = mock_user
            
            with patch('schemas.auth.UserProfileResponse.from_orm') as mock_from_orm:
                mock_from_orm.return_value = expected_user_data
                
                # Act
                response = self.client.get("/auth/me", headers=headers)
                
                # Assert
                assert response.status_code == 200
                assert response.json() == expected_user_data

    def test_get_current_user_profile_unauthorized_should_return_401(self):
        """
        TDD RED: Test getting user profile without authentication
        Should return 401 Unauthorized status
        """
        # Act
        response = self.client.get("/auth/me")
        
        # Assert
        assert response.status_code == 403  # FastAPI returns 403 for missing auth

    def test_update_user_preferences_success_should_return_updated_profile(self):
        """
        TDD RED: Test updating user preferences
        Should return updated user profile
        """
        # Arrange
        headers = {"Authorization": "Bearer valid_access_token"}
        preferences_data = {
            "preferences": {
                "theme": "dark",
                "notifications": True,
                "language": "en"
            }
        }
        
        expected_response = {
            "id": 1,
            "email": "<EMAIL>",
            "full_name": "Test User",
            "is_verified": True,
            "preferences": preferences_data["preferences"]
        }
        
        # Mock the get_current_user dependency
        with patch('api.v1.auth.get_current_user') as mock_get_user:
            mock_user = Mock()
            mock_user.email = "<EMAIL>"
            mock_get_user.return_value = mock_user
            
            with patch('schemas.auth.UserProfileResponse.from_orm') as mock_from_orm:
                mock_from_orm.return_value = expected_response
                
                # Act
                response = self.client.put("/auth/preferences", json=preferences_data, headers=headers)
                
                # Assert
                assert response.status_code == 200
                assert response.json() == expected_response

    # Integration tests for complete user flows
    
    def test_complete_registration_and_login_flow(self):
        """
        TDD RED: Test complete user registration and login flow
        Should successfully register user and then login
        """
        # This test would involve multiple API calls
        # and verify the complete user journey
        pass

    def test_password_reset_flow(self):
        """
        TDD RED: Test password reset flow
        Should successfully request and confirm password reset
        """
        # This test would verify the complete password reset journey
        pass

    def test_email_verification_flow(self):
        """
        TDD RED: Test email verification flow
        Should successfully verify user email
        """
        # This test would verify the email verification journey
        pass
