# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
jinja2==3.1.2

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Data processing and visualization
plotly==5.17.0
pandas==2.1.3
numpy==1.25.2
scipy==1.11.4

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-decouple==3.8
ldap3==2.9.1
cryptography==41.0.7
bcrypt==4.1.1

# HTTP clients
requests==2.31.0
httpx==0.25.2
aiohttp==3.9.1

# Data validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Machine Learning and AI
scikit-learn==1.3.2
joblib==1.3.2

# Natural Language Processing
nltk==3.8.1

# Utilities
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7

# Logging
structlog==23.2.0

# File handling
openpyxl==3.1.2
reportlab==4.0.7
pillow==10.1.0

# Async support
aiofiles==23.2.1

# Environment
python-dotenv==1.0.0

# Caching
redis==5.0.1
cachetools==5.3.2

# Background tasks
celery==5.3.4

# Cloud storage
boto3==1.34.0

# Monitoring
prometheus-client==0.19.0

# WebSocket
websockets==12.0

# JSON handling
orjson==3.9.10

# Type hints
typing-extensions==4.8.0

# CORS
fastapi-cors==0.0.6

# Additional useful packages
rich==13.7.0
typer==0.9.0
