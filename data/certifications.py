"""
Certification data structure and loading from JSON file
"""
import json
import os
import logging

# Try to import roadmap parser, but don't fail if dependencies are missing
try:
    from utils.roadmap_parser import parse_roadmap_html, merge_roadmap_data
    ROADMAP_AVAILABLE = True
except ImportError:
    ROADMAP_AVAILABLE = False

    def parse_roadmap_html():
        return {}

    def merge_roadmap_data(certs, roadmap):
        return certs

def convert_normalized_to_dict(normalized_data):
    """Convert normalized certification list to dictionary format expected by the app"""
    certifications = {}

    for cert in normalized_data:
        if not isinstance(cert, dict) or 'name' not in cert:
            continue

        name = cert['name']

        # Map difficulty from numeric to text
        difficulty_map = {1: 'Low', 2: 'Medium', 3: 'High'}
        difficulty = difficulty_map.get(cert.get('difficulty', 1), 'Medium')

        # Map focus to domain
        focus_to_domain = {
            'Engineer': 'Security Engineering',
            'Blueops': 'Defensive Security',
            'Redops': 'Offensive Security',
            'Test': 'Security Testing',
            'Mgmt': 'Security Management'
        }
        domain = focus_to_domain.get(cert.get('focus', 'Engineer'), cert.get('domain', 'Security Engineering'))

        certifications[name] = {
            'level': cert.get('category', 'Entry Level Security Certifications').replace(' Security Certifications', ''),
            'domain': domain,
            'difficulty': difficulty,
            'cost': cert.get('cost'),
            'prerequisites': cert.get('prerequisites', []),
            'description': cert.get('description', ''),
            'url': cert.get('url', ''),
            'related_certs': {'prerequisites': [], 'leads_to': [], 'related': []}
        }

    return certifications

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_certifications():
    """Load certifications from JSON file and roadmap data"""
    # Try normalized certifications first
    normalized_path = os.path.join(os.path.dirname(__file__), 'normalized_certifications.json')
    json_path = os.path.join(os.path.dirname(__file__), 'certifications.json')

    try:
        # First try to load from normalized certifications
        if os.path.exists(normalized_path):
            logger.info(f"Loading certifications from normalized file: {normalized_path}")
            with open(normalized_path, 'r') as f:
                normalized_data = json.load(f)
                if isinstance(normalized_data, list) and len(normalized_data) > 0:
                    logger.info(f"Found {len(normalized_data)} certifications in normalized file")
                    return convert_normalized_to_dict(normalized_data)

        logger.info(f"Attempting to load certifications from {json_path}")

        # Check if JSON file exists
        if not os.path.exists(json_path):
            logger.error(f"Certifications file not found: {json_path}")
            # If JSON doesn't exist, try using only roadmap data
            logger.info("Attempting to load certifications from roadmap only")
            roadmap_data = parse_roadmap_html()
            if roadmap_data:
                logger.info(f"Successfully loaded {len(roadmap_data)} certifications from roadmap")
                return roadmap_data
            return {}

        with open(json_path, 'r') as f:
            data = json.load(f)
            if not isinstance(data, dict) or 'categories' not in data:
                logger.error("Invalid JSON structure: expected object with 'categories' key")
                return {}

            categories = data['categories']
            logger.info(f"Loaded {len(categories)} categories from JSON")

            if not isinstance(categories, list):
                logger.error(f"Expected list of categories, got {type(categories)}")
                return {}

        # Create a flat dictionary of certifications from JSON
        certifications = {}
        for category_data in categories:
            if not isinstance(category_data, dict):
                logger.warning(f"Skipping invalid category data: {category_data}")
                continue

            category_name = category_data.get('category')
            cert_list = category_data.get('certifications', [])

            if not isinstance(cert_list, list):
                logger.warning(f"Invalid certifications list for category {category_name}")
                continue

            logger.info(f"Processing {len(cert_list)} certifications in category {category_name}")

            for cert in cert_list:
                if not isinstance(cert, dict):
                    logger.warning(f"Skipping invalid certification data: {cert}")
                    continue

                # Map category to difficulty level
                difficulty_map = {
                    'Entry Level': 'Low',
                    'Intermediate': 'Medium',
                    'Advanced': 'High',
                    'Expert': 'High'
                }

                name = cert.get('name')
                if not name:
                    logger.warning("Skipping certification with no name")
                    continue

                certifications[name] = {
                    'level': cert.get('category', 'Entry Level'),
                    'domain': cert.get('focus', 'General Security'),
                    'difficulty': difficulty_map.get(cert.get('category'), 'Medium'),
                    'cost': None,  # Will be determined by roadmap data
                    'prerequisites': None,  # Will be determined by roadmap data
                    'description': f"{cert.get('focus', 'Security')} certification at {cert.get('category', 'Entry')} level",
                    'url': None,  # Will be determined by roadmap data
                    'related_certs': {'prerequisites': [], 'leads_to': [], 'related': []}
                }

        logger.info(f"Successfully loaded {len(certifications)} certifications from JSON")

        # Load and merge roadmap data
        try:
            logger.info("Attempting to parse roadmap HTML data")
            roadmap_data = parse_roadmap_html()
            logger.info(f"Found {len(roadmap_data)} certifications in roadmap")
            if roadmap_data:
                certifications = merge_roadmap_data(certifications, roadmap_data)
                logger.info(f"Successfully merged roadmap data, final count: {len(certifications)}")
            else:
                logger.warning("No roadmap data found")
        except Exception as e:
            logger.error(f"Could not load roadmap data: {str(e)}")

        return certifications

    except FileNotFoundError:
        logger.error(f"Certifications file not found: {json_path}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in certifications file: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error loading certifications: {e}")
        return {}

# Load certification data
logger.info("Loading certification data...")
CERTIFICATIONS = load_certifications()
logger.info(f"Loaded {len(CERTIFICATIONS)} total certifications")

# Extract unique domains, levels, and difficulties
DOMAINS = sorted(list(set(cert['domain'] for cert in CERTIFICATIONS.values())))
LEVELS = ["Entry Level", "Intermediate", "Advanced"]
DIFFICULTIES = ["Low", "Medium", "High"]