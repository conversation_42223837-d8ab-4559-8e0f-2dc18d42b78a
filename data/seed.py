"""Import and seeding functionality for certification data"""
import sys
import os
import time
from sqlalchemy.exc import SQLAlchemyError, OperationalError
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_db, get_db
from models.certification import Certification, Organization
from models.job import SecurityJob
from utils.html_processor import process_certification_html
from scripts.populate_security_jobs import parse_job_titles, is_valid_job_title
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MAX_RETRIES = 3
RETRY_DELAY = 2

def extract_organization_info(cert_name, cert_data):
    """Extract organization information from certification data"""
    org_sources = [
        cert_data.get('issuing_organization'),
        cert_name.split()[0],  # First word of cert name
        cert_data.get('category', '').split()[0]  # First word of category
    ]

    org_name = next((source for source in org_sources if source), 'Unknown')

    org_mapping = {
        'AWS': ('Amazon Web Services', 'United States', 'https://aws.amazon.com/certification/'),
        'COMPTIA': ('CompTIA', 'United States', 'https://www.comptia.org/certifications'),
        'ISC2': ('International Information System Security Certification Consortium', 'United States', 'https://www.isc2.org/'),
        'ISACA': ('Information Systems Audit and Control Association', 'United States', 'https://www.isaca.org/credentialing'),
        'MS': ('Microsoft', 'United States', 'https://learn.microsoft.com/en-us/certifications/'),
        'CISCO': ('Cisco Systems', 'United States', 'https://www.cisco.com/c/en/us/training-events/training-certifications.html'),
        'EC-COUNCIL': ('EC-Council', 'United States', 'https://www.eccouncil.org/programs/'),
        'GIAC': ('Global Information Assurance Certification', 'United States', 'https://www.giac.org/certifications'),
        'ORACLE': ('Oracle Corporation', 'United States', 'https://education.oracle.com/certification'),
    }

    if org_name.upper() in org_mapping:
        return org_mapping[org_name.upper()]
    else:
        return (org_name, 'Unknown', None)

def handle_transaction(db, func, *args, **kwargs):
    """Handle database transactions with retries"""
    retries = 0
    while retries < MAX_RETRIES:
        try:
            result = func(*args, **kwargs)
            db.commit()
            return result
        except OperationalError as e:
            db.rollback()
            if "SSL connection has been closed" in str(e) and retries < MAX_RETRIES - 1:
                retries += 1
                logger.warning(f"Database connection lost, retrying ({retries}/{MAX_RETRIES})...")
                time.sleep(RETRY_DELAY)
                continue
            raise
        except SQLAlchemyError as e:
            db.rollback()
            raise

def create_organization(db, org_name, org_data, cert_name):
    """Create a single organization"""
    org = db.query(Organization).filter_by(name=org_name).first()
    if not org:
        org = Organization(
            name=org_name,
            country=org_data['country'],
            url=org_data['url'],
            description=f"Issuing organization for {cert_name}"
        )
        db.add(org)
        db.flush()
        logger.info(f"Created organization: {org_name}")
    return org

def create_certification(db, cert_name, cert_data, org):
    """Create a single certification"""
    existing = db.query(Certification).filter_by(name=cert_name).first()
    if existing:
        logger.debug(f"Skipping existing certification: {cert_name}")
        return False

    cert = Certification(
        name=cert_name,
        category=cert_data['category'],
        domain=cert_data['domain'],
        level=cert_data['level'],
        difficulty=cert_data['difficulty'],
        description=cert_data['description'],
        prerequisites=','.join(cert_data['prerequisites']) if cert_data['prerequisites'] else None,
        validity_period=cert_data['validity_period'],
        url=cert_data['url'],
        organization_id=org.id
    )
    db.add(cert)
    db.flush()
    return True

def import_certifications(db=None):
    """Import certifications into database with improved error handling"""
    if not db:
        db = next(get_db())

    try:
        logger.info("Starting certification import process...")
        certifications = load_certification_data()

        if not certifications:
            logger.error("No certification data loaded to import")
            return 0, 0

        organizations = {}
        success_count = 0
        error_count = 0

        # Create organizations
        for cert_name, cert_data in certifications.items():
            try:
                org_data = cert_data['organization']
                org_name = org_data['name']

                if org_name not in organizations:
                    org = handle_transaction(db, create_organization, db, org_name, org_data, cert_name)
                    organizations[org_name] = org

            except SQLAlchemyError as e:
                logger.error(f"Error creating organization {org_name}: {str(e)}")
                error_count += 1
                continue

        # Create certifications
        for cert_name, cert_data in certifications.items():
            try:
                org = organizations.get(cert_data['organization']['name'])
                if not org:
                    logger.warning(f"Organization not found for {cert_name}")
                    error_count += 1
                    continue

                if handle_transaction(db, create_certification, db, cert_name, cert_data, org):
                    success_count += 1
                    if success_count % 50 == 0:
                        logger.info(f"Created {success_count} certifications so far...")

            except SQLAlchemyError as e:
                logger.error(f"Error creating certification {cert_name}: {str(e)}")
                error_count += 1

        logger.info(f"Created {success_count} certifications with {error_count} errors")
        return success_count, error_count

    except Exception as e:
        logger.error(f"Error during certification import: {str(e)}")
        if db:
            db.rollback()
        return 0, 0

def load_certification_data():
    """Load certification data from HTML processor"""
    logger.info("Processing certification data from roadmap...")
    categories = process_certification_html()

    if not categories:
        logger.error("No certification categories found")
        return {}

    certifications = {}
    total_certs = sum(len(category['certifications']) for category in categories)
    logger.info(f"Found {total_certs} total certifications to process")

    for category in categories:
        category_name = category['category']
        certs_in_category = len(category['certifications'])
        logger.info(f"Processing {certs_in_category} certifications from {category_name}")

        for cert in category['certifications']:
            name = cert['name']
            if not name:
                logger.warning("Skipping certification with no name")
                continue

            if name in certifications:
                logger.warning(f"Duplicate certification found: {name}")
                continue

            logger.debug(f"Processing certification: {name}")

            # Extract organization information
            org_name, org_country, org_url = extract_organization_info(name, cert)

            certifications[name] = {
                'name': name,
                'category': category_name,
                'domain': cert.get('domain', 'General Security'),
                'level': cert.get('level', 'Entry Level'),
                'difficulty': cert.get('difficulty', 1),
                'description': cert.get('description', ''),
                'prerequisites': cert.get('prerequisites', []),
                'validity_period': 36,  # Default to 3 years
                'url': cert.get('url'),
                'organization': {
                    'name': org_name,
                    'country': org_country,
                    'url': org_url
                }
            }

    logger.info(f"Successfully processed {len(certifications)} unique certifications")
    return certifications

def seed_jobs(db=None):
    """Seed security jobs data with improved validation"""
    if not db:
        db = next(get_db())

    try:
        file_path = 'attached_assets/Pasted-Cybersecurity-Job-Titles-Based-on-an-exhaustive-search-of-job-boards-and-websites-like-Indeed-Linke-1740515734230.txt'
        with open(file_path, 'r') as f:
            content = f.read()

        jobs = parse_job_titles(content)
        success_count = 0
        error_count = 0
        skipped_count = 0

        for job_data in jobs:
            try:
                title = job_data['title']

                if not is_valid_job_title(title):
                    logger.warning(f"Skipping invalid job title format: {title}")
                    skipped_count += 1
                    continue

                existing = db.query(SecurityJob).filter_by(
                    title=title,
                    domain=job_data['domain']
                ).first()

                if not existing:
                    job = SecurityJob(
                        title=title,
                        domain=job_data['domain'],
                        description=job_data['description']
                    )
                    db.add(job)
                    success_count += 1
                else:
                    logger.info(f"Skipping existing job: {title}")
                    skipped_count += 1
            except Exception as e:
                logger.error(f"Error seeding job {job_data['title']}: {str(e)}")
                error_count += 1

        db.commit()
        logger.info(f"Successfully seeded {success_count} jobs")
        logger.info(f"Skipped {skipped_count} jobs (invalid or existing)")
        logger.info(f"Encountered {error_count} errors")
        return success_count, error_count
    except Exception as e:
        logger.error(f"Error during job seeding: {str(e)}")
        db.rollback()
        return 0, 0

def seed_database():
    """Seed the database with initial data"""
    logger.info("Initializing database...")
    init_db()

    db = next(get_db())

    try:
        # Import certifications first
        cert_success, cert_errors = import_certifications(db)
        logger.info(f"Imported {cert_success} certifications with {cert_errors} errors")

        db.commit()
        logger.info("Database seeding completed successfully!")
    except Exception as e:
        logger.error(f"Error seeding database: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    seed_database()