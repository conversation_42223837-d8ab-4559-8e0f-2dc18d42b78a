# Nix Migration Guide

This document explains the migration from pip-based dependency management to <PERSON> for the CertPathFinder project.

## 🎯 Why Nix?

### Benefits of Nix
- **Reproducible environments** - Same dependencies across all machines
- **Declarative configuration** - Dependencies defined in code
- **Isolation** - No conflicts with system packages
- **Rollback capability** - Easy to revert changes
- **Cross-platform** - Works on Linux, macOS, and Windows (WSL)

### Problems with pip
- **Environment conflicts** - Different Python versions, package conflicts
- **Non-reproducible** - `pip install` can give different results
- **System pollution** - Installs packages globally or in virtual environments
- **Missing system dependencies** - Doesn't handle non-Python dependencies

## 📦 New File Structure

```
├── shell.nix           # Legacy Nix shell configuration
├── flake.nix          # Modern Nix flakes configuration
├── .envrc             # Direnv automatic activation
├── replit.nix         # Updated Replit configuration
├── requirements.txt   # Kept for reference/fallback
└── NIX_MIGRATION_GUIDE.md  # This file
```

## 🚀 Getting Started

### 1. Install Nix

```bash
# Install Nix package manager
curl -L https://nixos.org/nix/install | sh

# Restart your shell or source the profile
source ~/.nix-profile/etc/profile.d/nix.sh

# Enable flakes (optional but recommended)
mkdir -p ~/.config/nix
echo 'experimental-features = nix-command flakes' >> ~/.config/nix/nix.conf
```

### 2. Enter Development Environment

#### Option A: Nix Flakes (Recommended)
```bash
nix develop
# or
make nix-develop
```

#### Option B: Legacy Nix Shell
```bash
nix-shell
# or
make nix-shell
```

#### Option C: Automatic with direnv (Best Experience)
```bash
# Install direnv
curl -sfL https://direnv.net/install.sh | bash

# Allow the .envrc file
direnv allow

# Now the environment activates automatically when you cd into the project
```

### 3. Verify Installation

```bash
# Check Python and packages are available
python --version
python -c "import fastapi; print('FastAPI available')"
python -c "import sklearn; print('scikit-learn available')"

# Run tests to verify everything works
make test
```

## 🔧 Development Workflow

### Daily Development
```bash
# Enter environment (if not using direnv)
nix develop

# Start development server
make dev
# or directly:
uvicorn api.app:app --reload

# Run tests
make test

# Format code
make format

# Run linting
make lint
```

### Adding New Dependencies

#### For packages available in nixpkgs:
1. Edit `shell.nix` or `flake.nix`
2. Add the package to the `pythonEnv` list
3. Re-enter the shell: `exit` then `nix develop`

#### For packages not in nixpkgs:
1. Add to the pip install section in the shellHook
2. Or temporarily use: `pip install --prefix="$PWD/.nix-pip" package-name`

## 📋 Command Comparison

| Task | Old (pip) | New (Nix) |
|------|-----------|-----------|
| Setup environment | `pip install -r requirements.txt` | `nix develop` |
| Activate environment | `source venv/bin/activate` | `nix develop` (or automatic with direnv) |
| Add dependency | Edit requirements.txt, pip install | Edit flake.nix/shell.nix |
| Clean environment | `rm -rf venv` | `nix-collect-garbage` |
| Reproduce environment | Hope requirements.txt is complete | `nix develop` (guaranteed reproducible) |

## 🔄 Migration Status

### ✅ Completed
- [x] Created `shell.nix` with all major dependencies
- [x] Created `flake.nix` for modern Nix flakes
- [x] Updated `replit.nix` with system dependencies
- [x] Added Nix commands to Makefile
- [x] Created `.envrc` for direnv integration
- [x] Fixed SQLAlchemy import issues
- [x] Fixed table name conflicts

### 🔄 In Progress
- [ ] Test all dependencies work correctly
- [ ] Update CI/CD to use Nix
- [ ] Update documentation

### 📦 Package Status

| Package | nixpkgs | pip fallback | Status |
|---------|---------|--------------|--------|
| fastapi | ✅ | - | Available |
| uvicorn | ✅ | - | Available |
| sqlalchemy | ✅ | - | Available |
| scikit-learn | ✅ | - | Available |
| pandas | ✅ | - | Available |
| numpy | ✅ | - | Available |
| pytest | ✅ | - | Available |
| fastapi-mail | ❌ | ✅ | Fallback |
| orjson | ❌ | ✅ | Fallback |
| catboost | ❌ | ✅ | Fallback |

## 🐛 Troubleshooting

### Common Issues

#### "command not found: nix"
```bash
# Install Nix
curl -L https://nixos.org/nix/install | sh
source ~/.nix-profile/etc/profile.d/nix.sh
```

#### "experimental feature 'flakes' is disabled"
```bash
mkdir -p ~/.config/nix
echo 'experimental-features = nix-command flakes' >> ~/.config/nix/nix.conf
```

#### "Package not found in nixpkgs"
- Check https://search.nixos.org/packages
- Use pip fallback in shellHook
- Or use `nix-shell -p python3Packages.package-name --run "python"`

#### "ImportError: cannot import name 'Decimal'"
- Fixed by updating SQLAlchemy imports
- Use `from sqlalchemy.types import Numeric as Decimal`

### Getting Help

1. **Nix documentation**: https://nixos.org/manual/nix/stable/
2. **NixOS packages search**: https://search.nixos.org/packages
3. **Nix community**: https://discourse.nixos.org/
4. **Project issues**: Create an issue in the repository

## 🔄 Rollback Plan

If Nix causes issues, you can always fall back to pip:

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Use old workflow
make install  # Uses pip
```

## 🎉 Benefits Realized

After migration, you'll have:
- ✅ **Reproducible development environment**
- ✅ **No more "works on my machine" issues**
- ✅ **Automatic dependency management**
- ✅ **System-level dependencies handled**
- ✅ **Easy onboarding for new developers**
- ✅ **Consistent CI/CD environments**

## 📚 Next Steps

1. **Try the new environment**: `nix develop`
2. **Install direnv** for automatic activation
3. **Update your IDE** to use the Nix Python interpreter
4. **Share feedback** on the migration experience
5. **Help improve** the Nix configuration

---

**Migration completed**: January 2025  
**Maintained by**: Development Team  
**Questions?** Create an issue or ask in team chat
