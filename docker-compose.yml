version: '3.8'

# CertRats Platform - Main Docker Compose Configuration
#
# This configuration sets up the complete CertRats certification platform
# with all core services, data layer, monitoring, and background processing.
#
# Services included:
# - Core: API, Frontend, Documentation, Status Dashboard
# - Data: PostgreSQL, Redis, MinIO Object Storage
# - Background: Celery Workers, Beat Scheduler, Flower Monitoring
# - Monitoring: Prometheus, Grafana, Alert Manager
# - Proxy: Traefik Reverse Proxy with SSL termination
#
# Network: All services use the 'certrats_network' for internal communication
# Domain Pattern: servicename.certrats.localhost
# Namespace: All containers prefixed with 'certrats_'

networks:
  certrats_network:
    name: certrats_network
    driver: bridge
  traefik_network:
    name: traefik_network
    external: true

volumes:
  certrats_postgres_data:
    name: certrats_postgres_data
  certrats_redis_data:
    name: certrats_redis_data
  certrats_minio_data:
    name: certrats_minio_data
  certrats_prometheus_data:
    name: certrats_prometheus_data
  certrats_grafana_data:
    name: certrats_grafana_data

services:
  # =============================================================================
  # CORE SERVICES
  # =============================================================================

  # FastAPI Backend Service
  api:
    container_name: certrats_api
    build:
      context: ./api
      dockerfile: Dockerfile
      target: development
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=*****************************************************/certrats_db
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=storage:9000
      - MINIO_ACCESS_KEY=certrats_minio
      - MINIO_SECRET_KEY=certrats_minio_secret
      - SECRET_KEY=your-secret-key-change-in-production
      - CORS_ORIGINS=http://app.certrats.localhost,http://localhost:3000
    volumes:
      - ./api:/app
      - /app/__pycache__
    networks:
      - certrats_network
      - traefik_network
    depends_on:
      - database
      - redis
      - storage
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-api.rule=Host(`api.certrats.localhost`)"
      - "traefik.http.routers.certrats-api.entrypoints=web"
      - "traefik.http.services.certrats-api.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # React Frontend Service
  frontend:
    container_name: certrats_frontend
    build:
      context: ./frontend-next
      dockerfile: Dockerfile
      target: development
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://api.certrats.localhost
      - REACT_APP_ENVIRONMENT=development
    volumes:
      - ./frontend-next:/app
      - /app/node_modules
    networks:
      - certrats_network
      - traefik_network
    depends_on:
      - api
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-frontend.rule=Host(`app.certrats.localhost`)"
      - "traefik.http.routers.certrats-frontend.entrypoints=web"
      - "traefik.http.services.certrats-frontend.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # DATA LAYER
  # =============================================================================

  # PostgreSQL Database
  database:
    container_name: certrats_database
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=certrats_db
      - POSTGRES_USER=certrats
      - POSTGRES_PASSWORD=certrats_password
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - certrats_postgres_data:/var/lib/postgresql/data
      - ./docker/database/init:/docker-entrypoint-initdb.d
    networks:
      - certrats_network
    ports:
      - "5432:5432"  # Exposed for development access
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U certrats -d certrats_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache and Message Broker
  redis:
    container_name: certrats_redis
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass certrats_redis_password
    volumes:
      - certrats_redis_data:/data
    networks:
      - certrats_network
    ports:
      - "6379:6379"  # Exposed for development access
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MinIO Object Storage
  storage:
    container_name: certrats_storage
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=certrats_minio
      - MINIO_ROOT_PASSWORD=certrats_minio_secret
    volumes:
      - certrats_minio_data:/data
    networks:
      - certrats_network
      - traefik_network
    labels:
      # MinIO API
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-storage.rule=Host(`storage.certrats.localhost`)"
      - "traefik.http.routers.certrats-storage.entrypoints=web"
      - "traefik.http.routers.certrats-storage.service=certrats-storage"
      - "traefik.http.services.certrats-storage.loadbalancer.server.port=9000"
      # MinIO Console
      - "traefik.http.routers.certrats-storage-console.rule=Host(`storage-console.certrats.localhost`)"
      - "traefik.http.routers.certrats-storage-console.entrypoints=web"
      - "traefik.http.routers.certrats-storage-console.service=certrats-storage-console"
      - "traefik.http.services.certrats-storage-console.loadbalancer.server.port=9001"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # BACKGROUND PROCESSING
  # =============================================================================

  # Celery Worker for Background Tasks
  celery_worker:
    container_name: certrats_celery_worker
    build:
      context: ./api
      dockerfile: Dockerfile
      target: development
    command: celery -A app.celery worker --loglevel=info --concurrency=4
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=*****************************************************/certrats_db
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=storage:9000
      - MINIO_ACCESS_KEY=certrats_minio
      - MINIO_SECRET_KEY=certrats_minio_secret
    volumes:
      - ./api:/app
    networks:
      - certrats_network
    depends_on:
      - database
      - redis
      - storage
    healthcheck:
      test: ["CMD", "celery", "-A", "app.celery", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Beat Scheduler
  celery_beat:
    container_name: certrats_celery_beat
    build:
      context: ./api
      dockerfile: Dockerfile
      target: development
    command: celery -A app.celery beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=*****************************************************/certrats_db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./api:/app
    networks:
      - certrats_network
    depends_on:
      - database
      - redis
      - celery_worker

  # Celery Flower Monitoring
  flower:
    container_name: certrats_celery_flower
    build:
      context: ./api
      dockerfile: Dockerfile
      target: development
    command: celery -A app.celery flower --port=5555
    environment:
      - ENVIRONMENT=development
      - REDIS_URL=redis://redis:6379/0
    networks:
      - certrats_network
      - traefik_network
    depends_on:
      - redis
      - celery_worker
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-flower.rule=Host(`flower.certrats.localhost`)"
      - "traefik.http.routers.certrats-flower.entrypoints=web"
      - "traefik.http.services.certrats-flower.loadbalancer.server.port=5555"
      - "traefik.docker.network=traefik_network"

  # =============================================================================
  # MONITORING STACK
  # =============================================================================

  # Prometheus Metrics Collection
  prometheus:
    container_name: certrats_prometheus
    image: prom/prometheus:latest
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - certrats_prometheus_data:/prometheus
    networks:
      - certrats_network
      - traefik_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-prometheus.rule=Host(`metrics.certrats.localhost`)"
      - "traefik.http.routers.certrats-prometheus.entrypoints=web"
      - "traefik.http.services.certrats-prometheus.loadbalancer.server.port=9090"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Grafana Metrics Visualization
  grafana:
    container_name: certrats_grafana
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=certrats_grafana_admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - certrats_grafana_data:/var/lib/grafana
      - ./docker/monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./docker/monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - certrats_network
      - traefik_network
    depends_on:
      - prometheus
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-grafana.rule=Host(`grafana.certrats.localhost`)"
      - "traefik.http.routers.certrats-grafana.entrypoints=web"
      - "traefik.http.services.certrats-grafana.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # SUPPORT SERVICES
  # =============================================================================

  # Documentation Service (Sphinx)
  docs:
    container_name: certrats_docs
    build:
      context: ./docs
      dockerfile: Dockerfile
    volumes:
      - ./docs:/app
    networks:
      - certrats_network
      - traefik_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-docs.rule=Host(`docs.certrats.localhost`)"
      - "traefik.http.routers.certrats-docs.entrypoints=web"
      - "traefik.http.services.certrats-docs.loadbalancer.server.port=8080"
      - "traefik.docker.network=traefik_network"

  # Status Dashboard
  status:
    container_name: certrats_status
    build:
      context: ./docker/status
      dockerfile: Dockerfile
    environment:
      - CERTRATS_ENV=development
    volumes:
      - ./config:/app/config
      - ./utils:/app/utils
    networks:
      - certrats_network
      - traefik_network
    depends_on:
      - api
      - database
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certrats-status.rule=Host(`status.certrats.localhost`)"
      - "traefik.http.routers.certrats-status.entrypoints=web"
      - "traefik.http.services.certrats-status.loadbalancer.server.port=8081"
      - "traefik.docker.network=traefik_network"
