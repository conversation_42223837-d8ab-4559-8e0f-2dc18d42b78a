# CertRats Cachet Local Development Configuration
# This file is used for local development with auto-setup

# Application Settings
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost:8080
APP_KEY=base64:WfKUhY0wAOyr5hkxoctZUSWPGdXbBUWFvNLCoI7kTU8=
APP_TIMEZONE=UTC
APP_LOCALE=en

# Database Configuration
DB_DRIVER=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=cachet
DB_USERNAME=cachet_user
DB_PASSWORD=cachet_password_123

# Cache Configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DATABASE=1

# Mail Configuration
MAIL_DRIVER=log
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=CertRats Status

# Auto-setup Configuration (eliminates manual setup)
CACHET_SETUP=true
CACHET_SITE_NAME=CertRats Status
CACHET_SITE_DOMAIN=localhost:8080
CACHET_SITE_TIMEZONE=UTC
CACHET_ADMIN_USERNAME=admin
CACHET_ADMIN_EMAIL=<EMAIL>
CACHET_ADMIN_PASSWORD=admin123!
CACHET_ADMIN_FIRSTNAME=CertRats
CACHET_ADMIN_LASTNAME=Admin

# Monitoring Configuration
MONITOR_ENABLED=true
MONITOR_INTERVAL=60
MONITOR_TIMEOUT=10
