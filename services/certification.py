"""Service layer for certification management using standardized CRUD patterns"""
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from pydantic import BaseModel, Field

from models.certification import Certification
from services.base_crud import (
    BaseCRUDService, FilterParams, PaginationParams, SortParams,
    PaginatedResponse, ValidationError, NotFoundError
)


# Pydantic schemas for certification operations
class CertificationCreate(BaseModel):
    """Schema for creating a new certification"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    level: str = Field(..., min_length=1, max_length=50)
    difficulty: str = Field(..., min_length=1, max_length=50)
    focus: str = Field(..., min_length=1, max_length=50)
    domain: str = Field(..., min_length=1, max_length=100)
    category: str = Field(..., min_length=1, max_length=100)
    cost: Optional[float] = Field(None, ge=0)
    currency: str = Field(default="USD", max_length=3)
    organization_id: int = Field(..., gt=0)
    url: Optional[str] = None
    exam_code: Optional[str] = None
    validity_period: Optional[int] = Field(None, gt=0)
    prerequisites: Optional[List[str]] = None
    recommended_experience: Optional[str] = None


class CertificationUpdate(BaseModel):
    """Schema for updating an existing certification"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    level: Optional[str] = Field(None, min_length=1, max_length=50)
    difficulty: Optional[str] = Field(None, min_length=1, max_length=50)
    focus: Optional[str] = Field(None, min_length=1, max_length=50)
    domain: Optional[str] = Field(None, min_length=1, max_length=100)
    category: Optional[str] = Field(None, min_length=1, max_length=100)
    cost: Optional[float] = Field(None, ge=0)
    currency: Optional[str] = Field(None, max_length=3)
    organization_id: Optional[int] = Field(None, gt=0)
    url: Optional[str] = None
    exam_code: Optional[str] = None
    validity_period: Optional[int] = Field(None, gt=0)
    prerequisites: Optional[List[str]] = None
    recommended_experience: Optional[str] = None
    is_active: Optional[bool] = None


class CertificationResponse(BaseModel):
    """Schema for certification responses"""
    id: int
    name: str
    description: Optional[str]
    level: str
    difficulty: str
    focus: str
    domain: str
    category: str
    cost: Optional[float]
    currency: str
    organization_id: int
    url: Optional[str]
    exam_code: Optional[str]
    validity_period: Optional[int]
    prerequisites: Optional[List[str]]
    recommended_experience: Optional[str]
    is_active: bool
    is_deleted: bool
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class CertificationFilters(FilterParams):
    """Extended filter parameters for certifications"""
    domains: Optional[List[str]] = None
    levels: Optional[List[str]] = None
    difficulties: Optional[List[str]] = None
    focuses: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    organization_ids: Optional[List[int]] = None
    cost_min: Optional[float] = Field(None, ge=0)
    cost_max: Optional[float] = Field(None, ge=0)
    is_active: Optional[bool] = None
    has_prerequisites: Optional[bool] = None


class CertificationService(BaseCRUDService[
    Certification, CertificationCreate, CertificationUpdate, CertificationResponse
]):
    """Enhanced certification service using standardized CRUD patterns"""

    def __init__(self, db: Session):
        super().__init__(
            db=db,
            model=Certification,
            response_schema=CertificationResponse
        )

    def _validate_create_data(self, obj_in: CertificationCreate) -> CertificationCreate:
        """Validate certification creation data"""
        # Check if certification with same name already exists
        existing = self.db.query(Certification).filter(
            and_(
                Certification.name == obj_in.name,
                Certification.organization_id == obj_in.organization_id,
                Certification.is_deleted == False
            )
        ).first()

        if existing:
            raise ValidationError(
                f"Certification '{obj_in.name}' already exists for this organization",
                details={'existing_id': existing.id}
            )

        return obj_in

    def _validate_update_data(
        self,
        obj_in: CertificationUpdate,
        current_obj: Certification
    ) -> CertificationUpdate:
        """Validate certification update data"""
        # Check for name conflicts if name is being updated
        if obj_in.name and obj_in.name != current_obj.name:
            organization_id = obj_in.organization_id or current_obj.organization_id
            existing = self.db.query(Certification).filter(
                and_(
                    Certification.name == obj_in.name,
                    Certification.organization_id == organization_id,
                    Certification.id != current_obj.id,
                    Certification.is_deleted == False
                )
            ).first()

            if existing:
                raise ValidationError(
                    f"Certification '{obj_in.name}' already exists for this organization",
                    details={'existing_id': existing.id}
                )

        return obj_in

    def _apply_filters(self, query, filters: CertificationFilters):
        """Apply certification-specific filters"""
        # Apply base filters first
        query = super()._apply_filters(query, filters)

        # Apply certification-specific filters
        if filters.domains:
            query = query.filter(Certification.domain.in_(filters.domains))

        if filters.levels:
            query = query.filter(Certification.level.in_(filters.levels))

        if filters.difficulties:
            query = query.filter(Certification.difficulty.in_(filters.difficulties))

        if filters.focuses:
            query = query.filter(Certification.focus.in_(filters.focuses))

        if filters.categories:
            query = query.filter(Certification.category.in_(filters.categories))

        if filters.organization_ids:
            query = query.filter(Certification.organization_id.in_(filters.organization_ids))

        if filters.cost_min is not None:
            query = query.filter(Certification.cost >= filters.cost_min)

        if filters.cost_max is not None:
            query = query.filter(Certification.cost <= filters.cost_max)

        if filters.is_active is not None:
            query = query.filter(Certification.is_active == filters.is_active)

        if filters.has_prerequisites is not None:
            if filters.has_prerequisites:
                query = query.filter(Certification.prerequisites.isnot(None))
            else:
                query = query.filter(Certification.prerequisites.is_(None))

        return query

    def get_certifications_by_domain(
        self,
        domain: str,
        pagination: Optional[PaginationParams] = None
    ) -> PaginatedResponse[CertificationResponse]:
        """Get certifications filtered by domain"""
        filters = CertificationFilters(domains=[domain])
        pagination = pagination or PaginationParams()
        sort_params = SortParams(sort_by='name', sort_order='asc')

        return self.list(filters, pagination, sort_params)

    def get_certifications_by_level(
        self,
        level: str,
        pagination: Optional[PaginationParams] = None
    ) -> PaginatedResponse[CertificationResponse]:
        """Get certifications filtered by level"""
        filters = CertificationFilters(levels=[level])
        pagination = pagination or PaginationParams()
        sort_params = SortParams(sort_by='name', sort_order='asc')

        return self.list(filters, pagination, sort_params)

    def search_certifications(
        self,
        query: str,
        filters: Optional[CertificationFilters] = None,
        pagination: Optional[PaginationParams] = None
    ) -> PaginatedResponse[CertificationResponse]:
        """Search certifications with enhanced filtering"""
        search_fields = ['name', 'description', 'domain', 'category', 'exam_code']
        filters = filters or CertificationFilters()
        pagination = pagination or PaginationParams()

        return self.search(query, search_fields, filters, pagination)

    def get_certification_stats(self) -> Dict[str, Any]:
        """Get certification statistics"""
        total_count = self.db.query(Certification).filter(
            Certification.is_deleted == False
        ).count()

        active_count = self.db.query(Certification).filter(
            and_(
                Certification.is_deleted == False,
                Certification.is_active == True
            )
        ).count()

        # Get domain distribution
        domain_stats = self.db.query(
            Certification.domain,
            func.count(Certification.id).label('count')
        ).filter(
            Certification.is_deleted == False
        ).group_by(Certification.domain).all()

        # Get level distribution
        level_stats = self.db.query(
            Certification.level,
            func.count(Certification.id).label('count')
        ).filter(
            Certification.is_deleted == False
        ).group_by(Certification.level).all()

        # Get cost statistics
        cost_stats = self.db.query(
            func.min(Certification.cost).label('min_cost'),
            func.max(Certification.cost).label('max_cost'),
            func.avg(Certification.cost).label('avg_cost')
        ).filter(
            and_(
                Certification.is_deleted == False,
                Certification.cost.isnot(None)
            )
        ).first()

        return {
            'total_certifications': total_count,
            'active_certifications': active_count,
            'inactive_certifications': total_count - active_count,
            'domain_distribution': {domain: count for domain, count in domain_stats},
            'level_distribution': {level: count for level, count in level_stats},
            'cost_statistics': {
                'min_cost': float(cost_stats.min_cost) if cost_stats.min_cost else None,
                'max_cost': float(cost_stats.max_cost) if cost_stats.max_cost else None,
                'avg_cost': float(cost_stats.avg_cost) if cost_stats.avg_cost else None,
            }
        }

    def get_related_certifications(
        self,
        certification_id: int,
        limit: int = 5
    ) -> List[CertificationResponse]:
        """Get certifications related to the given certification"""
        # Get the source certification
        source_cert = self.get_or_404(certification_id)

        # Find related certifications based on domain, level, and organization
        query = self.db.query(Certification).filter(
            and_(
                Certification.id != certification_id,
                Certification.is_deleted == False,
                Certification.is_active == True,
                or_(
                    Certification.domain == source_cert.domain,
                    Certification.organization_id == source_cert.organization_id,
                    Certification.level == source_cert.level
                )
            )
        ).limit(limit)

        related_certs = query.all()
        return [CertificationResponse.from_orm(cert) for cert in related_certs]
