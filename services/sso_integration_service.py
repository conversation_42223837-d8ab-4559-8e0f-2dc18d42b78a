"""SSO Integration Service for enterprise authentication.

This service provides comprehensive SSO integration capabilities including
SAML, OIDC, OAuth2, LDAP, and Active Directory integration for enterprise
authentication and user provisioning.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from enum import Enum
import xml.etree.ElementTree as ET
import base64
import json
import requests
from urllib.parse import urlencode

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole
from models.compliance import AuditLog, AuditEventType
from services.audit_service import AuditService

logger = logging.getLogger(__name__)


class SSOProvider(str, Enum):
    """Supported SSO providers."""
    SAML = "saml"
    OIDC = "oidc"
    OAUTH2 = "oauth2"
    LDAP = "ldap"
    ACTIVE_DIRECTORY = "active_directory"
    OKTA = "okta"
    AZURE_AD = "azure_ad"
    GOOGLE_WORKSPACE = "google_workspace"
    ONELOGIN = "onelogin"


class ProvisioningAction(str, Enum):
    """User provisioning actions."""
    CREATE = "create"
    UPDATE = "update"
    DEACTIVATE = "deactivate"
    DELETE = "delete"
    SYNC = "sync"


class SSOIntegrationService:
    """Service for SSO integration and user provisioning."""
    
    def __init__(self, db: Session):
        self.db = db
        self.audit_service = AuditService(db)
    
    # SAML Integration
    
    def configure_saml_integration(
        self, 
        organization_id: int, 
        saml_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure SAML SSO integration for an organization."""
        try:
            logger.info(f"Configuring SAML integration for organization {organization_id}")
            
            # Validate SAML configuration
            self._validate_saml_config(saml_config)
            
            # Get organization
            org = self.db.query(EnterpriseOrganization).get(organization_id)
            if not org:
                raise ValueError("Organization not found")
            
            # Update organization SSO settings
            sso_settings = org.sso_settings or {}
            sso_settings.update({
                'provider': SSOProvider.SAML.value,
                'saml': {
                    'entity_id': saml_config['entity_id'],
                    'sso_url': saml_config['sso_url'],
                    'slo_url': saml_config.get('slo_url'),
                    'x509_cert': saml_config['x509_cert'],
                    'attribute_mapping': saml_config.get('attribute_mapping', {}),
                    'name_id_format': saml_config.get('name_id_format', 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress')
                },
                'enabled': True,
                'configured_at': datetime.utcnow().isoformat(),
                'auto_provisioning': saml_config.get('auto_provisioning', False)
            })
            
            org.sso_settings = sso_settings
            self.db.commit()
            
            # Generate service provider metadata
            sp_metadata = self._generate_saml_sp_metadata(organization_id, saml_config)
            
            # Log configuration
            self.audit_service.log_event(
                organization_id=organization_id,
                event_type=AuditEventType.SYSTEM_CONFIGURATION,
                description="SAML SSO integration configured",
                additional_data={'provider': 'SAML', 'entity_id': saml_config['entity_id']}
            )
            
            return {
                'status': 'configured',
                'provider': 'SAML',
                'sp_metadata': sp_metadata,
                'sso_url': self._generate_sso_url(organization_id, SSOProvider.SAML),
                'acs_url': self._generate_acs_url(organization_id)
            }
            
        except Exception as e:
            logger.error(f"Error configuring SAML integration: {e}")
            raise
    
    def process_saml_response(
        self, 
        organization_id: int, 
        saml_response: str
    ) -> Dict[str, Any]:
        """Process SAML authentication response."""
        try:
            logger.info(f"Processing SAML response for organization {organization_id}")
            
            # Decode and parse SAML response
            decoded_response = base64.b64decode(saml_response)
            saml_data = self._parse_saml_response(decoded_response)
            
            # Validate SAML response
            if not self._validate_saml_response(organization_id, saml_data):
                raise ValueError("Invalid SAML response")
            
            # Extract user information
            user_info = self._extract_user_info_from_saml(organization_id, saml_data)
            
            # Provision or update user
            user = self._provision_user_from_sso(organization_id, user_info, SSOProvider.SAML)
            
            return {
                'user_id': user.user_id,
                'email': user.email,
                'provisioned': True,
                'attributes': user_info
            }
            
        except Exception as e:
            logger.error(f"Error processing SAML response: {e}")
            raise
    
    # OIDC Integration
    
    def configure_oidc_integration(
        self, 
        organization_id: int, 
        oidc_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure OIDC SSO integration for an organization."""
        try:
            logger.info(f"Configuring OIDC integration for organization {organization_id}")
            
            # Validate OIDC configuration
            self._validate_oidc_config(oidc_config)
            
            # Get organization
            org = self.db.query(EnterpriseOrganization).get(organization_id)
            if not org:
                raise ValueError("Organization not found")
            
            # Update organization SSO settings
            sso_settings = org.sso_settings or {}
            sso_settings.update({
                'provider': SSOProvider.OIDC.value,
                'oidc': {
                    'issuer': oidc_config['issuer'],
                    'client_id': oidc_config['client_id'],
                    'client_secret': oidc_config['client_secret'],
                    'authorization_endpoint': oidc_config['authorization_endpoint'],
                    'token_endpoint': oidc_config['token_endpoint'],
                    'userinfo_endpoint': oidc_config['userinfo_endpoint'],
                    'jwks_uri': oidc_config.get('jwks_uri'),
                    'scopes': oidc_config.get('scopes', ['openid', 'email', 'profile'])
                },
                'enabled': True,
                'configured_at': datetime.utcnow().isoformat(),
                'auto_provisioning': oidc_config.get('auto_provisioning', False)
            })
            
            org.sso_settings = sso_settings
            self.db.commit()
            
            # Log configuration
            self.audit_service.log_event(
                organization_id=organization_id,
                event_type=AuditEventType.SYSTEM_CONFIGURATION,
                description="OIDC SSO integration configured",
                additional_data={'provider': 'OIDC', 'issuer': oidc_config['issuer']}
            )
            
            return {
                'status': 'configured',
                'provider': 'OIDC',
                'authorization_url': self._generate_oidc_auth_url(organization_id),
                'redirect_uri': self._generate_oidc_redirect_uri(organization_id)
            }
            
        except Exception as e:
            logger.error(f"Error configuring OIDC integration: {e}")
            raise
    
    def process_oidc_callback(
        self, 
        organization_id: int, 
        authorization_code: str,
        state: str = None
    ) -> Dict[str, Any]:
        """Process OIDC authorization callback."""
        try:
            logger.info(f"Processing OIDC callback for organization {organization_id}")
            
            # Get organization SSO settings
            org = self.db.query(EnterpriseOrganization).get(organization_id)
            if not org or not org.sso_settings.get('oidc'):
                raise ValueError("OIDC not configured for organization")
            
            oidc_config = org.sso_settings['oidc']
            
            # Exchange authorization code for tokens
            token_response = self._exchange_oidc_code(oidc_config, authorization_code, organization_id)
            
            # Get user information
            user_info = self._get_oidc_user_info(oidc_config, token_response['access_token'])
            
            # Provision or update user
            user = self._provision_user_from_sso(organization_id, user_info, SSOProvider.OIDC)
            
            return {
                'user_id': user.user_id,
                'email': user.email,
                'provisioned': True,
                'tokens': token_response
            }
            
        except Exception as e:
            logger.error(f"Error processing OIDC callback: {e}")
            raise
    
    # LDAP Integration
    
    def configure_ldap_integration(
        self, 
        organization_id: int, 
        ldap_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure LDAP integration for an organization."""
        try:
            logger.info(f"Configuring LDAP integration for organization {organization_id}")
            
            # Validate LDAP configuration
            self._validate_ldap_config(ldap_config)
            
            # Test LDAP connection
            if not self._test_ldap_connection(ldap_config):
                raise ValueError("Unable to connect to LDAP server")
            
            # Get organization
            org = self.db.query(EnterpriseOrganization).get(organization_id)
            if not org:
                raise ValueError("Organization not found")
            
            # Update organization SSO settings
            sso_settings = org.sso_settings or {}
            sso_settings.update({
                'provider': SSOProvider.LDAP.value,
                'ldap': {
                    'server': ldap_config['server'],
                    'port': ldap_config.get('port', 389),
                    'use_ssl': ldap_config.get('use_ssl', False),
                    'bind_dn': ldap_config['bind_dn'],
                    'bind_password': ldap_config['bind_password'],
                    'base_dn': ldap_config['base_dn'],
                    'user_filter': ldap_config.get('user_filter', '(uid={username})'),
                    'attribute_mapping': ldap_config.get('attribute_mapping', {})
                },
                'enabled': True,
                'configured_at': datetime.utcnow().isoformat(),
                'auto_provisioning': ldap_config.get('auto_provisioning', False)
            })
            
            org.sso_settings = sso_settings
            self.db.commit()
            
            # Log configuration
            self.audit_service.log_event(
                organization_id=organization_id,
                event_type=AuditEventType.SYSTEM_CONFIGURATION,
                description="LDAP integration configured",
                additional_data={'provider': 'LDAP', 'server': ldap_config['server']}
            )
            
            return {
                'status': 'configured',
                'provider': 'LDAP',
                'server': ldap_config['server'],
                'connection_test': 'successful'
            }
            
        except Exception as e:
            logger.error(f"Error configuring LDAP integration: {e}")
            raise
    
    def authenticate_ldap_user(
        self, 
        organization_id: int, 
        username: str, 
        password: str
    ) -> Dict[str, Any]:
        """Authenticate user against LDAP."""
        try:
            logger.info(f"Authenticating LDAP user {username} for organization {organization_id}")
            
            # Get organization LDAP settings
            org = self.db.query(EnterpriseOrganization).get(organization_id)
            if not org or not org.sso_settings.get('ldap'):
                raise ValueError("LDAP not configured for organization")
            
            ldap_config = org.sso_settings['ldap']
            
            # Authenticate against LDAP
            user_info = self._authenticate_ldap(ldap_config, username, password)
            
            if not user_info:
                raise ValueError("LDAP authentication failed")
            
            # Provision or update user
            user = self._provision_user_from_sso(organization_id, user_info, SSOProvider.LDAP)
            
            return {
                'user_id': user.user_id,
                'email': user.email,
                'provisioned': True,
                'ldap_attributes': user_info
            }
            
        except Exception as e:
            logger.error(f"Error authenticating LDAP user: {e}")
            raise
    
    # User Provisioning
    
    def _provision_user_from_sso(
        self, 
        organization_id: int, 
        user_info: Dict[str, Any],
        provider: SSOProvider
    ) -> EnterpriseUser:
        """Provision or update user from SSO information."""
        try:
            email = user_info.get('email')
            if not email:
                raise ValueError("Email not provided in SSO response")
            
            # Check if user already exists
            user = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.email == email,
                EnterpriseUser.organization_id == organization_id
            ).first()
            
            if user:
                # Update existing user
                action = ProvisioningAction.UPDATE
                self._update_user_from_sso(user, user_info)
            else:
                # Create new user
                action = ProvisioningAction.CREATE
                user = self._create_user_from_sso(organization_id, user_info)
            
            self.db.commit()
            
            # Log provisioning action
            self.audit_service.log_event(
                organization_id=organization_id,
                event_type=AuditEventType.USER_LOGIN,
                description=f"User {action.value} via SSO: {provider.value}",
                user_id=user.user_id,
                additional_data={
                    'provider': provider.value,
                    'action': action.value,
                    'email': email
                }
            )
            
            return user
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error provisioning user from SSO: {e}")
            raise
    
    def _create_user_from_sso(
        self, 
        organization_id: int, 
        user_info: Dict[str, Any]
    ) -> EnterpriseUser:
        """Create new user from SSO information."""
        user_data = {
            'user_id': f"sso_{organization_id}_{user_info['email'].replace('@', '_').replace('.', '_')}",
            'organization_id': organization_id,
            'email': user_info['email'],
            'first_name': user_info.get('first_name', ''),
            'last_name': user_info.get('last_name', ''),
            'job_title': user_info.get('job_title'),
            'role': UserRole.LEARNER,  # Default role
            'is_active': True,
            'sso_provider': user_info.get('provider'),
            'sso_user_id': user_info.get('sso_user_id')
        }
        
        user = EnterpriseUser(**user_data)
        self.db.add(user)
        
        return user
    
    def _update_user_from_sso(self, user: EnterpriseUser, user_info: Dict[str, Any]):
        """Update existing user from SSO information."""
        # Update user attributes
        if user_info.get('first_name'):
            user.first_name = user_info['first_name']
        
        if user_info.get('last_name'):
            user.last_name = user_info['last_name']
        
        if user_info.get('job_title'):
            user.job_title = user_info['job_title']
        
        # Update last login
        user.last_login = datetime.utcnow()
        
        # Update SSO information
        user.sso_provider = user_info.get('provider')
        user.sso_user_id = user_info.get('sso_user_id')
    
    # Validation Methods
    
    def _validate_saml_config(self, config: Dict[str, Any]):
        """Validate SAML configuration."""
        required_fields = ['entity_id', 'sso_url', 'x509_cert']
        for field in required_fields:
            if not config.get(field):
                raise ValueError(f"Missing required SAML field: {field}")
    
    def _validate_oidc_config(self, config: Dict[str, Any]):
        """Validate OIDC configuration."""
        required_fields = ['issuer', 'client_id', 'client_secret', 'authorization_endpoint', 'token_endpoint']
        for field in required_fields:
            if not config.get(field):
                raise ValueError(f"Missing required OIDC field: {field}")
    
    def _validate_ldap_config(self, config: Dict[str, Any]):
        """Validate LDAP configuration."""
        required_fields = ['server', 'bind_dn', 'bind_password', 'base_dn']
        for field in required_fields:
            if not config.get(field):
                raise ValueError(f"Missing required LDAP field: {field}")
    
    # Placeholder methods for actual SSO implementation
    # These would be implemented with proper SSO libraries
    
    def _parse_saml_response(self, response: bytes) -> Dict[str, Any]:
        """Parse SAML response (placeholder)."""
        # TODO: Implement with proper SAML library (python3-saml, etc.)
        return {}
    
    def _validate_saml_response(self, org_id: int, saml_data: Dict[str, Any]) -> bool:
        """Validate SAML response (placeholder)."""
        # TODO: Implement SAML signature validation
        return True
    
    def _extract_user_info_from_saml(self, org_id: int, saml_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract user info from SAML response (placeholder)."""
        # TODO: Implement SAML attribute extraction
        return {}
    
    def _exchange_oidc_code(self, config: Dict[str, Any], code: str, org_id: int) -> Dict[str, Any]:
        """Exchange OIDC authorization code for tokens (placeholder)."""
        # TODO: Implement OIDC token exchange
        return {}
    
    def _get_oidc_user_info(self, config: Dict[str, Any], access_token: str) -> Dict[str, Any]:
        """Get user info from OIDC userinfo endpoint (placeholder)."""
        # TODO: Implement OIDC userinfo request
        return {}
    
    def _test_ldap_connection(self, config: Dict[str, Any]) -> bool:
        """Test LDAP connection (placeholder)."""
        # TODO: Implement with ldap3 library
        return True
    
    def _authenticate_ldap(self, config: Dict[str, Any], username: str, password: str) -> Dict[str, Any]:
        """Authenticate against LDAP (placeholder)."""
        # TODO: Implement LDAP authentication
        return {}
    
    # URL Generation Methods
    
    def _generate_sso_url(self, org_id: int, provider: SSOProvider) -> str:
        """Generate SSO URL for organization."""
        return f"/api/v1/auth/sso/{provider.value}/{org_id}"
    
    def _generate_acs_url(self, org_id: int) -> str:
        """Generate SAML ACS URL."""
        return f"/api/v1/auth/saml/acs/{org_id}"
    
    def _generate_oidc_auth_url(self, org_id: int) -> str:
        """Generate OIDC authorization URL."""
        return f"/api/v1/auth/oidc/authorize/{org_id}"
    
    def _generate_oidc_redirect_uri(self, org_id: int) -> str:
        """Generate OIDC redirect URI."""
        return f"/api/v1/auth/oidc/callback/{org_id}"
    
    def _generate_saml_sp_metadata(self, org_id: int, config: Dict[str, Any]) -> str:
        """Generate SAML Service Provider metadata."""
        # TODO: Generate proper SAML SP metadata XML
        return f"<EntityDescriptor><!-- SP metadata for org {org_id} --></EntityDescriptor>"
