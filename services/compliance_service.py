"""Compliance service for automated regulatory reporting and audit management.

This service provides comprehensive compliance automation including GDPR, HIPAA,
SOX, CMMC reporting, audit trail management, and risk assessment capabilities.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
import json
from collections import defaultdict
import pandas as pd

from models.compliance import (
    ComplianceRequirement, ComplianceAssessment, ComplianceReport, AuditLog,
    DataProcessingActivity, ComplianceFramework, ComplianceStatus, 
    AuditEventType, RiskLevel
)
from models.enterprise import EnterpriseOrganization, EnterpriseUser
from services.audit_service import AuditService

logger = logging.getLogger(__name__)


class ComplianceService:
    """Service for compliance automation and regulatory reporting."""
    
    def __init__(self, db: Session):
        self.db = db
        self.audit_service = AuditService(db)
    
    # Compliance Requirements Management
    
    def create_compliance_requirement(self, req_data: Dict[str, Any]) -> ComplianceRequirement:
        """Create a new compliance requirement."""
        try:
            requirement = ComplianceRequirement(**req_data)
            self.db.add(requirement)
            self.db.commit()
            self.db.refresh(requirement)
            
            # Log the creation
            self.audit_service.log_event(
                organization_id=requirement.organization_id,
                event_type=AuditEventType.SYSTEM_CONFIGURATION,
                description=f"Created compliance requirement: {requirement.title}",
                resource_type="compliance_requirement",
                resource_id=str(requirement.id),
                compliance_relevant=True
            )
            
            logger.info(f"Created compliance requirement: {requirement.title}")
            return requirement
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating compliance requirement: {e}")
            raise
    
    def get_organization_requirements(
        self, 
        org_id: int, 
        framework: Optional[ComplianceFramework] = None
    ) -> List[ComplianceRequirement]:
        """Get compliance requirements for an organization."""
        query = self.db.query(ComplianceRequirement).filter(
            ComplianceRequirement.organization_id == org_id,
            ComplianceRequirement.deleted_at.is_(None)
        )
        
        if framework:
            query = query.filter(ComplianceRequirement.framework == framework)
        
        return query.order_by(ComplianceRequirement.risk_level.desc()).all()
    
    def assess_compliance_requirement(
        self, 
        requirement_id: int, 
        assessment_data: Dict[str, Any]
    ) -> ComplianceAssessment:
        """Create a compliance assessment for a requirement."""
        try:
            assessment = ComplianceAssessment(
                requirement_id=requirement_id,
                **assessment_data
            )
            self.db.add(assessment)
            
            # Update requirement status and last assessed date
            requirement = self.db.query(ComplianceRequirement).get(requirement_id)
            if requirement:
                requirement.status = assessment.status
                requirement.last_assessed = assessment.assessment_date
                requirement.next_assessment_due = self._calculate_next_assessment_date(
                    requirement.check_frequency
                )
            
            self.db.commit()
            self.db.refresh(assessment)
            
            # Log the assessment
            self.audit_service.log_event(
                organization_id=assessment.organization_id,
                event_type=AuditEventType.COMPLIANCE_REPORT,
                description=f"Compliance assessment completed: {assessment.status.value}",
                resource_type="compliance_assessment",
                resource_id=str(assessment.id),
                compliance_relevant=True
            )
            
            logger.info(f"Created compliance assessment: {assessment.id}")
            return assessment
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating compliance assessment: {e}")
            raise
    
    # GDPR Compliance
    
    def generate_gdpr_report(
        self, 
        org_id: int, 
        period_start: datetime, 
        period_end: datetime
    ) -> ComplianceReport:
        """Generate comprehensive GDPR compliance report."""
        try:
            logger.info(f"Generating GDPR report for organization {org_id}")
            
            # Gather GDPR-specific data
            gdpr_data = self._gather_gdpr_data(org_id, period_start, period_end)
            
            # Calculate compliance score
            compliance_score = self._calculate_gdpr_compliance_score(gdpr_data)
            
            # Determine overall status
            overall_status = self._determine_compliance_status(compliance_score)
            
            # Generate executive summary
            executive_summary = self._generate_gdpr_executive_summary(gdpr_data, compliance_score)
            
            # Create compliance report
            report = ComplianceReport(
                organization_id=org_id,
                report_type=ComplianceFramework.GDPR,
                report_name=f"GDPR Compliance Report - {period_start.strftime('%Y-%m')}",
                report_period_start=period_start,
                report_period_end=period_end,
                generated_by="system",
                executive_summary=executive_summary,
                overall_status=overall_status,
                overall_score=compliance_score,
                compliant_requirements=gdpr_data.get('compliant_requirements', []),
                non_compliant_requirements=gdpr_data.get('non_compliant_requirements', []),
                partially_compliant_requirements=gdpr_data.get('partially_compliant_requirements', []),
                high_risk_findings=gdpr_data.get('high_risk_findings', []),
                recommendations=gdpr_data.get('recommendations', []),
                action_items=gdpr_data.get('action_items', []),
                report_data=gdpr_data
            )
            
            self.db.add(report)
            self.db.commit()
            self.db.refresh(report)
            
            # Log report generation
            self.audit_service.log_event(
                organization_id=org_id,
                event_type=AuditEventType.COMPLIANCE_REPORT,
                description=f"Generated GDPR compliance report: {report.report_name}",
                resource_type="compliance_report",
                resource_id=str(report.id),
                compliance_relevant=True
            )
            
            logger.info(f"Generated GDPR report: {report.id}")
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error generating GDPR report: {e}")
            raise
    
    def _gather_gdpr_data(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Gather GDPR-specific compliance data."""
        data = {
            'organization_id': org_id,
            'period_start': start_date.isoformat(),
            'period_end': end_date.isoformat(),
            'compliant_requirements': [],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': [],
            'high_risk_findings': [],
            'recommendations': [],
            'action_items': []
        }
        
        # Get GDPR requirements and assessments
        requirements = self.get_organization_requirements(org_id, ComplianceFramework.GDPR)
        
        for req in requirements:
            req_data = {
                'requirement_id': req.requirement_id,
                'title': req.title,
                'status': req.status.value,
                'risk_level': req.risk_level.value,
                'last_assessed': req.last_assessed.isoformat() if req.last_assessed else None
            }
            
            if req.status == ComplianceStatus.COMPLIANT:
                data['compliant_requirements'].append(req_data)
            elif req.status == ComplianceStatus.NON_COMPLIANT:
                data['non_compliant_requirements'].append(req_data)
                if req.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                    data['high_risk_findings'].append(req_data)
            elif req.status == ComplianceStatus.PARTIALLY_COMPLIANT:
                data['partially_compliant_requirements'].append(req_data)
        
        # Get data processing activities
        activities = self.db.query(DataProcessingActivity).filter(
            DataProcessingActivity.organization_id == org_id,
            DataProcessingActivity.deleted_at.is_(None)
        ).all()
        
        data['data_processing_activities'] = [
            {
                'name': activity.activity_name,
                'purpose': activity.purpose_of_processing,
                'legal_basis': activity.legal_basis,
                'data_categories': activity.data_categories,
                'dpia_required': activity.dpia_required,
                'dpia_completed': activity.dpia_completed
            }
            for activity in activities
        ]
        
        # Get relevant audit logs
        audit_logs = self.db.query(AuditLog).filter(
            AuditLog.organization_id == org_id,
            AuditLog.compliance_relevant == True,
            AuditLog.created_at >= start_date,
            AuditLog.created_at <= end_date
        ).count()
        
        data['audit_events_count'] = audit_logs
        
        # Generate recommendations based on findings
        data['recommendations'] = self._generate_gdpr_recommendations(data)
        data['action_items'] = self._generate_gdpr_action_items(data)
        
        return data
    
    def _calculate_gdpr_compliance_score(self, gdpr_data: Dict[str, Any]) -> float:
        """Calculate GDPR compliance score (0-100)."""
        total_requirements = (
            len(gdpr_data['compliant_requirements']) +
            len(gdpr_data['non_compliant_requirements']) +
            len(gdpr_data['partially_compliant_requirements'])
        )
        
        if total_requirements == 0:
            return 0.0
        
        compliant_score = len(gdpr_data['compliant_requirements']) * 100
        partial_score = len(gdpr_data['partially_compliant_requirements']) * 50
        
        return (compliant_score + partial_score) / total_requirements
    
    def _generate_gdpr_executive_summary(self, gdpr_data: Dict[str, Any], score: float) -> str:
        """Generate executive summary for GDPR report."""
        total_reqs = (
            len(gdpr_data['compliant_requirements']) +
            len(gdpr_data['non_compliant_requirements']) +
            len(gdpr_data['partially_compliant_requirements'])
        )
        
        summary = f"""
        GDPR Compliance Executive Summary
        
        Overall Compliance Score: {score:.1f}%
        
        Requirements Assessment:
        - Total Requirements Evaluated: {total_reqs}
        - Compliant: {len(gdpr_data['compliant_requirements'])}
        - Non-Compliant: {len(gdpr_data['non_compliant_requirements'])}
        - Partially Compliant: {len(gdpr_data['partially_compliant_requirements'])}
        
        High-Risk Findings: {len(gdpr_data['high_risk_findings'])}
        Data Processing Activities: {len(gdpr_data.get('data_processing_activities', []))}
        Audit Events Recorded: {gdpr_data.get('audit_events_count', 0)}
        
        Key Recommendations: {len(gdpr_data['recommendations'])}
        Action Items Required: {len(gdpr_data['action_items'])}
        """
        
        return summary.strip()
    
    def _generate_gdpr_recommendations(self, gdpr_data: Dict[str, Any]) -> List[str]:
        """Generate GDPR-specific recommendations."""
        recommendations = []
        
        if len(gdpr_data['non_compliant_requirements']) > 0:
            recommendations.append(
                "Prioritize remediation of non-compliant requirements, especially high-risk items"
            )
        
        if len(gdpr_data['high_risk_findings']) > 0:
            recommendations.append(
                "Immediate attention required for high-risk compliance gaps"
            )
        
        # Check for DPIA requirements
        activities_needing_dpia = [
            activity for activity in gdpr_data.get('data_processing_activities', [])
            if activity.get('dpia_required') and not activity.get('dpia_completed')
        ]
        
        if activities_needing_dpia:
            recommendations.append(
                f"Complete Data Protection Impact Assessments for {len(activities_needing_dpia)} activities"
            )
        
        return recommendations
    
    def _generate_gdpr_action_items(self, gdpr_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate specific action items for GDPR compliance."""
        action_items = []
        
        for req in gdpr_data['non_compliant_requirements']:
            action_items.append({
                'type': 'compliance_gap',
                'priority': 'high' if req['risk_level'] in ['high', 'critical'] else 'medium',
                'title': f"Address non-compliance: {req['title']}",
                'description': f"Requirement {req['requirement_id']} requires immediate attention",
                'due_date': (datetime.now() + timedelta(days=30)).isoformat()
            })
        
        return action_items
    
    # Utility methods
    
    def _calculate_next_assessment_date(self, frequency: Optional[str]) -> Optional[datetime]:
        """Calculate next assessment due date based on frequency."""
        if not frequency:
            return None
        
        now = datetime.now()
        frequency_map = {
            'daily': timedelta(days=1),
            'weekly': timedelta(weeks=1),
            'monthly': timedelta(days=30),
            'quarterly': timedelta(days=90),
            'annually': timedelta(days=365)
        }
        
        delta = frequency_map.get(frequency.lower(), timedelta(days=90))
        return now + delta
    
    def _determine_compliance_status(self, score: float) -> ComplianceStatus:
        """Determine overall compliance status based on score."""
        if score >= 95:
            return ComplianceStatus.COMPLIANT
        elif score >= 70:
            return ComplianceStatus.PARTIALLY_COMPLIANT
        else:
            return ComplianceStatus.NON_COMPLIANT

    # HIPAA Compliance

    def generate_hipaa_report(
        self,
        org_id: int,
        period_start: datetime,
        period_end: datetime
    ) -> ComplianceReport:
        """Generate comprehensive HIPAA compliance report."""
        try:
            logger.info(f"Generating HIPAA report for organization {org_id}")

            # Gather HIPAA-specific data
            hipaa_data = self._gather_hipaa_data(org_id, period_start, period_end)

            # Calculate compliance score
            compliance_score = self._calculate_hipaa_compliance_score(hipaa_data)

            # Determine overall status
            overall_status = self._determine_compliance_status(compliance_score)

            # Generate executive summary
            executive_summary = self._generate_hipaa_executive_summary(hipaa_data, compliance_score)

            # Create compliance report
            report = ComplianceReport(
                organization_id=org_id,
                report_type=ComplianceFramework.HIPAA,
                report_name=f"HIPAA Compliance Report - {period_start.strftime('%Y-%m')}",
                report_period_start=period_start,
                report_period_end=period_end,
                generated_by="system",
                executive_summary=executive_summary,
                overall_status=overall_status,
                overall_score=compliance_score,
                compliant_requirements=hipaa_data.get('compliant_requirements', []),
                non_compliant_requirements=hipaa_data.get('non_compliant_requirements', []),
                partially_compliant_requirements=hipaa_data.get('partially_compliant_requirements', []),
                high_risk_findings=hipaa_data.get('high_risk_findings', []),
                recommendations=hipaa_data.get('recommendations', []),
                action_items=hipaa_data.get('action_items', []),
                report_data=hipaa_data
            )

            self.db.add(report)
            self.db.commit()
            self.db.refresh(report)

            # Log report generation
            self.audit_service.log_event(
                organization_id=org_id,
                event_type=AuditEventType.COMPLIANCE_REPORT,
                description=f"Generated HIPAA compliance report: {report.report_name}",
                resource_type="compliance_report",
                resource_id=str(report.id),
                compliance_relevant=True
            )

            logger.info(f"Generated HIPAA report: {report.id}")
            return report

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error generating HIPAA report: {e}")
            raise

    def _gather_hipaa_data(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Gather HIPAA-specific compliance data."""
        data = {
            'organization_id': org_id,
            'period_start': start_date.isoformat(),
            'period_end': end_date.isoformat(),
            'compliant_requirements': [],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': [],
            'high_risk_findings': [],
            'recommendations': [],
            'action_items': [],
            'phi_access_events': 0,
            'security_incidents': 0,
            'training_completion_rate': 0.0
        }

        # Get HIPAA requirements and assessments
        requirements = self.get_organization_requirements(org_id, ComplianceFramework.HIPAA)

        for req in requirements:
            req_data = {
                'requirement_id': req.requirement_id,
                'title': req.title,
                'status': req.status.value,
                'risk_level': req.risk_level.value,
                'last_assessed': req.last_assessed.isoformat() if req.last_assessed else None
            }

            if req.status == ComplianceStatus.COMPLIANT:
                data['compliant_requirements'].append(req_data)
            elif req.status == ComplianceStatus.NON_COMPLIANT:
                data['non_compliant_requirements'].append(req_data)
                if req.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                    data['high_risk_findings'].append(req_data)
            elif req.status == ComplianceStatus.PARTIALLY_COMPLIANT:
                data['partially_compliant_requirements'].append(req_data)

        # Get PHI access events
        phi_events = self.db.query(AuditLog).filter(
            AuditLog.organization_id == org_id,
            AuditLog.event_type == AuditEventType.DATA_ACCESS,
            AuditLog.created_at >= start_date,
            AuditLog.created_at <= end_date,
            AuditLog.additional_data.contains({'data_type': 'phi'})
        ).count()
        data['phi_access_events'] = phi_events

        # Get security incidents
        incidents = self.db.query(AuditLog).filter(
            AuditLog.organization_id == org_id,
            AuditLog.event_type == AuditEventType.SECURITY_INCIDENT,
            AuditLog.created_at >= start_date,
            AuditLog.created_at <= end_date
        ).count()
        data['security_incidents'] = incidents

        # Generate recommendations and action items
        data['recommendations'] = self._generate_hipaa_recommendations(data)
        data['action_items'] = self._generate_hipaa_action_items(data)

        return data

    def _calculate_hipaa_compliance_score(self, hipaa_data: Dict[str, Any]) -> float:
        """Calculate HIPAA compliance score (0-100)."""
        total_requirements = (
            len(hipaa_data['compliant_requirements']) +
            len(hipaa_data['non_compliant_requirements']) +
            len(hipaa_data['partially_compliant_requirements'])
        )

        if total_requirements == 0:
            return 0.0

        compliant_score = len(hipaa_data['compliant_requirements']) * 100
        partial_score = len(hipaa_data['partially_compliant_requirements']) * 50

        base_score = (compliant_score + partial_score) / total_requirements

        # Adjust for security incidents (penalty)
        incident_penalty = min(hipaa_data['security_incidents'] * 5, 20)  # Max 20% penalty

        return max(0, base_score - incident_penalty)

    def _generate_hipaa_executive_summary(self, hipaa_data: Dict[str, Any], score: float) -> str:
        """Generate executive summary for HIPAA report."""
        total_reqs = (
            len(hipaa_data['compliant_requirements']) +
            len(hipaa_data['non_compliant_requirements']) +
            len(hipaa_data['partially_compliant_requirements'])
        )

        summary = f"""
        HIPAA Compliance Executive Summary

        Overall Compliance Score: {score:.1f}%

        Requirements Assessment:
        - Total Requirements Evaluated: {total_reqs}
        - Compliant: {len(hipaa_data['compliant_requirements'])}
        - Non-Compliant: {len(hipaa_data['non_compliant_requirements'])}
        - Partially Compliant: {len(hipaa_data['partially_compliant_requirements'])}

        Security Metrics:
        - PHI Access Events: {hipaa_data['phi_access_events']}
        - Security Incidents: {hipaa_data['security_incidents']}
        - High-Risk Findings: {len(hipaa_data['high_risk_findings'])}

        Key Recommendations: {len(hipaa_data['recommendations'])}
        Action Items Required: {len(hipaa_data['action_items'])}
        """

        return summary.strip()

    def _generate_hipaa_recommendations(self, hipaa_data: Dict[str, Any]) -> List[str]:
        """Generate HIPAA-specific recommendations."""
        recommendations = []

        if hipaa_data['security_incidents'] > 0:
            recommendations.append(
                "Review and strengthen security incident response procedures"
            )

        if len(hipaa_data['non_compliant_requirements']) > 0:
            recommendations.append(
                "Address non-compliant HIPAA requirements to avoid potential violations"
            )

        if hipaa_data['phi_access_events'] > 1000:  # Threshold for high access
            recommendations.append(
                "Implement additional PHI access monitoring and controls"
            )

        return recommendations

    def _generate_hipaa_action_items(self, hipaa_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate specific action items for HIPAA compliance."""
        action_items = []

        for req in hipaa_data['non_compliant_requirements']:
            action_items.append({
                'type': 'hipaa_compliance_gap',
                'priority': 'critical' if req['risk_level'] in ['high', 'critical'] else 'high',
                'title': f"HIPAA Compliance: {req['title']}",
                'description': f"Address HIPAA requirement {req['requirement_id']}",
                'due_date': (datetime.now() + timedelta(days=14)).isoformat()  # Shorter timeline for HIPAA
            })

        if hipaa_data['security_incidents'] > 0:
            action_items.append({
                'type': 'security_review',
                'priority': 'high',
                'title': 'Security Incident Review',
                'description': f"Review and remediate {hipaa_data['security_incidents']} security incidents",
                'due_date': (datetime.now() + timedelta(days=7)).isoformat()
            })

        return action_items

    # SOX Compliance

    def generate_sox_report(
        self,
        org_id: int,
        period_start: datetime,
        period_end: datetime
    ) -> ComplianceReport:
        """Generate comprehensive SOX compliance report."""
        try:
            logger.info(f"Generating SOX report for organization {org_id}")

            # Gather SOX-specific data
            sox_data = self._gather_sox_data(org_id, period_start, period_end)

            # Calculate compliance score
            compliance_score = self._calculate_sox_compliance_score(sox_data)

            # Determine overall status
            overall_status = self._determine_compliance_status(compliance_score)

            # Generate executive summary
            executive_summary = self._generate_sox_executive_summary(sox_data, compliance_score)

            # Create compliance report
            report = ComplianceReport(
                organization_id=org_id,
                report_type=ComplianceFramework.SOX,
                report_name=f"SOX Compliance Report - {period_start.strftime('%Y-%m')}",
                report_period_start=period_start,
                report_period_end=period_end,
                generated_by="system",
                executive_summary=executive_summary,
                overall_status=overall_status,
                overall_score=compliance_score,
                compliant_requirements=sox_data.get('compliant_requirements', []),
                non_compliant_requirements=sox_data.get('non_compliant_requirements', []),
                partially_compliant_requirements=sox_data.get('partially_compliant_requirements', []),
                high_risk_findings=sox_data.get('high_risk_findings', []),
                recommendations=sox_data.get('recommendations', []),
                action_items=sox_data.get('action_items', []),
                report_data=sox_data
            )

            self.db.add(report)
            self.db.commit()
            self.db.refresh(report)

            # Log report generation
            self.audit_service.log_event(
                organization_id=org_id,
                event_type=AuditEventType.COMPLIANCE_REPORT,
                description=f"Generated SOX compliance report: {report.report_name}",
                resource_type="compliance_report",
                resource_id=str(report.id),
                compliance_relevant=True
            )

            logger.info(f"Generated SOX report: {report.id}")
            return report

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error generating SOX report: {e}")
            raise

    def _gather_sox_data(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Gather SOX-specific compliance data."""
        data = {
            'organization_id': org_id,
            'period_start': start_date.isoformat(),
            'period_end': end_date.isoformat(),
            'compliant_requirements': [],
            'non_compliant_requirements': [],
            'partially_compliant_requirements': [],
            'high_risk_findings': [],
            'recommendations': [],
            'action_items': [],
            'it_general_controls': [],
            'access_reviews_completed': 0,
            'change_management_events': 0
        }

        # Get SOX requirements and assessments
        requirements = self.get_organization_requirements(org_id, ComplianceFramework.SOX)

        for req in requirements:
            req_data = {
                'requirement_id': req.requirement_id,
                'title': req.title,
                'status': req.status.value,
                'risk_level': req.risk_level.value,
                'last_assessed': req.last_assessed.isoformat() if req.last_assessed else None
            }

            if req.status == ComplianceStatus.COMPLIANT:
                data['compliant_requirements'].append(req_data)
            elif req.status == ComplianceStatus.NON_COMPLIANT:
                data['non_compliant_requirements'].append(req_data)
                if req.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                    data['high_risk_findings'].append(req_data)
            elif req.status == ComplianceStatus.PARTIALLY_COMPLIANT:
                data['partially_compliant_requirements'].append(req_data)

        # Get system configuration changes
        config_changes = self.db.query(AuditLog).filter(
            AuditLog.organization_id == org_id,
            AuditLog.event_type == AuditEventType.SYSTEM_CONFIGURATION,
            AuditLog.created_at >= start_date,
            AuditLog.created_at <= end_date
        ).count()
        data['change_management_events'] = config_changes

        # Get permission changes (access reviews)
        permission_changes = self.db.query(AuditLog).filter(
            AuditLog.organization_id == org_id,
            AuditLog.event_type == AuditEventType.PERMISSION_CHANGE,
            AuditLog.created_at >= start_date,
            AuditLog.created_at <= end_date
        ).count()
        data['access_reviews_completed'] = permission_changes

        # Generate recommendations and action items
        data['recommendations'] = self._generate_sox_recommendations(data)
        data['action_items'] = self._generate_sox_action_items(data)

        return data

    def _calculate_sox_compliance_score(self, sox_data: Dict[str, Any]) -> float:
        """Calculate SOX compliance score (0-100)."""
        total_requirements = (
            len(sox_data['compliant_requirements']) +
            len(sox_data['non_compliant_requirements']) +
            len(sox_data['partially_compliant_requirements'])
        )

        if total_requirements == 0:
            return 0.0

        compliant_score = len(sox_data['compliant_requirements']) * 100
        partial_score = len(sox_data['partially_compliant_requirements']) * 50

        base_score = (compliant_score + partial_score) / total_requirements

        # Bonus for good change management practices
        if sox_data['change_management_events'] > 0 and sox_data['access_reviews_completed'] > 0:
            base_score = min(100, base_score + 5)  # 5% bonus for active governance

        return base_score

    def _generate_sox_executive_summary(self, sox_data: Dict[str, Any], score: float) -> str:
        """Generate executive summary for SOX report."""
        total_reqs = (
            len(sox_data['compliant_requirements']) +
            len(sox_data['non_compliant_requirements']) +
            len(sox_data['partially_compliant_requirements'])
        )

        summary = f"""
        SOX Compliance Executive Summary

        Overall Compliance Score: {score:.1f}%

        Requirements Assessment:
        - Total Requirements Evaluated: {total_reqs}
        - Compliant: {len(sox_data['compliant_requirements'])}
        - Non-Compliant: {len(sox_data['non_compliant_requirements'])}
        - Partially Compliant: {len(sox_data['partially_compliant_requirements'])}

        IT General Controls:
        - Change Management Events: {sox_data['change_management_events']}
        - Access Reviews Completed: {sox_data['access_reviews_completed']}
        - High-Risk Findings: {len(sox_data['high_risk_findings'])}

        Key Recommendations: {len(sox_data['recommendations'])}
        Action Items Required: {len(sox_data['action_items'])}
        """

        return summary.strip()

    def _generate_sox_recommendations(self, sox_data: Dict[str, Any]) -> List[str]:
        """Generate SOX-specific recommendations."""
        recommendations = []

        if sox_data['change_management_events'] == 0:
            recommendations.append(
                "Implement formal change management procedures for IT systems"
            )

        if sox_data['access_reviews_completed'] == 0:
            recommendations.append(
                "Conduct regular access reviews and permission audits"
            )

        if len(sox_data['non_compliant_requirements']) > 0:
            recommendations.append(
                "Address non-compliant SOX requirements to ensure financial reporting integrity"
            )

        return recommendations

    def _generate_sox_action_items(self, sox_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate specific action items for SOX compliance."""
        action_items = []

        for req in sox_data['non_compliant_requirements']:
            action_items.append({
                'type': 'sox_compliance_gap',
                'priority': 'critical' if req['risk_level'] in ['high', 'critical'] else 'high',
                'title': f"SOX Compliance: {req['title']}",
                'description': f"Address SOX requirement {req['requirement_id']} for financial reporting integrity",
                'due_date': (datetime.now() + timedelta(days=21)).isoformat()
            })

        if sox_data['change_management_events'] == 0:
            action_items.append({
                'type': 'process_improvement',
                'priority': 'medium',
                'title': 'Implement Change Management Process',
                'description': 'Establish formal change management procedures for IT systems',
                'due_date': (datetime.now() + timedelta(days=60)).isoformat()
            })

        return action_items
