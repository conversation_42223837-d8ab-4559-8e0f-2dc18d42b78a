"""Integration Hub Service for enterprise system integrations.

This service provides comprehensive integration capabilities with SSO providers,
LDAP/Active Directory, LMS systems, HR platforms, and other enterprise systems
for seamless data synchronization and user management.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
import json
import uuid
import asyncio
import aiohttp
from enum import Enum
import ldap3
import xml.etree.ElementTree as ET
from cryptography.fernet import Fernet
import base64

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserLicense
from models.progress_tracking import StudySession, PracticeTestResult, LearningGoal
from services.enterprise_ai_service import EnterpriseAIService

logger = logging.getLogger(__name__)


class IntegrationType(Enum):
    """Integration type enumeration."""
    SSO_SAML = "sso_saml"
    SSO_OIDC = "sso_oidc"
    SSO_OAUTH2 = "sso_oauth2"
    LDAP = "ldap"
    ACTIVE_DIRECTORY = "active_directory"
    LMS_CANVAS = "lms_canvas"
    LMS_MOODLE = "lms_moodle"
    LMS_BLACKBOARD = "lms_blackboard"
    HR_WORKDAY = "hr_workday"
    HR_BAMBOO = "hr_bamboo"
    HR_ADPWORKFORCE = "hr_adpworkforce"
    CRM_SALESFORCE = "crm_salesforce"
    ERP_SAP = "erp_sap"
    SLACK = "slack"
    TEAMS = "teams"
    WEBHOOK = "webhook"


class SyncDirection(Enum):
    """Data synchronization direction."""
    INBOUND = "inbound"
    OUTBOUND = "outbound"
    BIDIRECTIONAL = "bidirectional"


class IntegrationStatus(Enum):
    """Integration status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"
    TESTING = "testing"


class IntegrationHubService:
    """Service for managing enterprise system integrations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = EnterpriseAIService(db)
        self.encryption_key = self._get_encryption_key()
    
    # SSO Integration Management
    
    def configure_sso_integration(self, organization_id: int, sso_config: Dict[str, Any]) -> Dict[str, Any]:
        """Configure SSO integration for an organization."""
        try:
            integration_id = str(uuid.uuid4())
            
            # Validate organization
            org = self.db.query(EnterpriseOrganization).filter(
                EnterpriseOrganization.id == organization_id
            ).first()
            
            if not org:
                return {'error': 'Organization not found'}
            
            # Validate SSO configuration
            validation_result = self._validate_sso_config(sso_config)
            if not validation_result['valid']:
                return {'error': f"Invalid SSO configuration: {validation_result['errors']}"}
            
            # Encrypt sensitive data
            encrypted_config = self._encrypt_sensitive_data(sso_config)
            
            # Create SSO integration record
            sso_integration = {
                'integration_id': integration_id,
                'organization_id': organization_id,
                'integration_type': sso_config.get('type', IntegrationType.SSO_SAML.value),
                'provider_name': sso_config.get('provider_name', 'Unknown'),
                'configuration': encrypted_config,
                'status': IntegrationStatus.PENDING.value,
                'created_at': datetime.utcnow(),
                'last_sync': None,
                'sync_frequency_hours': sso_config.get('sync_frequency_hours', 24),
                'auto_provision_users': sso_config.get('auto_provision_users', True),
                'default_role': sso_config.get('default_role', 'learner'),
                'attribute_mapping': sso_config.get('attribute_mapping', {}),
                'group_mapping': sso_config.get('group_mapping', {})
            }
            
            # Test SSO connection
            test_result = self._test_sso_connection(sso_config)
            if test_result['success']:
                sso_integration['status'] = IntegrationStatus.ACTIVE.value
                sso_integration['last_test'] = datetime.utcnow()
            else:
                sso_integration['status'] = IntegrationStatus.ERROR.value
                sso_integration['error_message'] = test_result.get('error', 'Connection test failed')
            
            # Store integration (would be in sso_integrations table)
            logger.info(f"SSO integration {integration_id} configured for organization {organization_id}")
            
            return {
                'integration_id': integration_id,
                'status': sso_integration['status'],
                'provider_name': sso_integration['provider_name'],
                'test_result': test_result,
                'metadata_url': self._generate_metadata_url(integration_id),
                'acs_url': self._generate_acs_url(integration_id),
                'entity_id': f"certpathfinder-{organization_id}",
                'auto_provision_enabled': sso_integration['auto_provision_users']
            }
        
        except Exception as e:
            logger.error(f"Error configuring SSO integration: {e}")
            return {'error': str(e)}
    
    def authenticate_sso_user(self, integration_id: str, saml_response: str) -> Dict[str, Any]:
        """Authenticate user via SSO SAML response."""
        try:
            # Get SSO integration configuration
            integration = self._get_integration(integration_id)
            if not integration:
                return {'error': 'Integration not found'}
            
            # Parse and validate SAML response
            saml_data = self._parse_saml_response(saml_response, integration['configuration'])
            if not saml_data['valid']:
                return {'error': f"Invalid SAML response: {saml_data['error']}"}
            
            # Extract user attributes
            user_attributes = saml_data['attributes']
            
            # Map attributes to user profile
            user_profile = self._map_sso_attributes(user_attributes, integration['attribute_mapping'])
            
            # Find or create user
            user = self._find_or_create_sso_user(
                integration['organization_id'],
                user_profile,
                integration['auto_provision_users']
            )
            
            if not user:
                return {'error': 'User not found and auto-provisioning is disabled'}
            
            # Update user's last login
            user['last_login'] = datetime.utcnow()
            
            # Generate session token
            session_token = self._generate_session_token(user['user_id'])
            
            # Log SSO authentication
            self._log_sso_authentication(integration_id, user['user_id'], True)
            
            return {
                'authentication_status': 'success',
                'user_profile': user,
                'session_token': session_token,
                'session_expires_at': (datetime.utcnow() + timedelta(hours=8)).isoformat(),
                'sso_provider': integration['provider_name']
            }
        
        except Exception as e:
            logger.error(f"Error authenticating SSO user: {e}")
            self._log_sso_authentication(integration_id, None, False, str(e))
            return {'error': str(e)}
    
    # LDAP/Active Directory Integration
    
    def configure_ldap_integration(self, organization_id: int, ldap_config: Dict[str, Any]) -> Dict[str, Any]:
        """Configure LDAP/Active Directory integration."""
        try:
            integration_id = str(uuid.uuid4())
            
            # Validate LDAP configuration
            validation_result = self._validate_ldap_config(ldap_config)
            if not validation_result['valid']:
                return {'error': f"Invalid LDAP configuration: {validation_result['errors']}"}
            
            # Encrypt sensitive data
            encrypted_config = self._encrypt_sensitive_data(ldap_config)
            
            # Create LDAP integration record
            ldap_integration = {
                'integration_id': integration_id,
                'organization_id': organization_id,
                'integration_type': IntegrationType.LDAP.value,
                'server_url': ldap_config.get('server_url'),
                'configuration': encrypted_config,
                'status': IntegrationStatus.PENDING.value,
                'created_at': datetime.utcnow(),
                'sync_direction': ldap_config.get('sync_direction', SyncDirection.INBOUND.value),
                'sync_frequency_hours': ldap_config.get('sync_frequency_hours', 6),
                'user_base_dn': ldap_config.get('user_base_dn'),
                'group_base_dn': ldap_config.get('group_base_dn'),
                'user_filter': ldap_config.get('user_filter', '(objectClass=person)'),
                'group_filter': ldap_config.get('group_filter', '(objectClass=group)'),
                'attribute_mapping': ldap_config.get('attribute_mapping', {}),
                'auto_provision_users': ldap_config.get('auto_provision_users', True)
            }
            
            # Test LDAP connection
            test_result = self._test_ldap_connection(ldap_config)
            if test_result['success']:
                ldap_integration['status'] = IntegrationStatus.ACTIVE.value
                ldap_integration['last_test'] = datetime.utcnow()
            else:
                ldap_integration['status'] = IntegrationStatus.ERROR.value
                ldap_integration['error_message'] = test_result.get('error', 'Connection test failed')
            
            # Store integration (would be in ldap_integrations table)
            logger.info(f"LDAP integration {integration_id} configured for organization {organization_id}")
            
            return {
                'integration_id': integration_id,
                'status': ldap_integration['status'],
                'server_url': ldap_integration['server_url'],
                'test_result': test_result,
                'sync_direction': ldap_integration['sync_direction'],
                'auto_provision_enabled': ldap_integration['auto_provision_users']
            }
        
        except Exception as e:
            logger.error(f"Error configuring LDAP integration: {e}")
            return {'error': str(e)}
    
    def sync_ldap_users(self, integration_id: str) -> Dict[str, Any]:
        """Synchronize users from LDAP/Active Directory."""
        try:
            # Get LDAP integration configuration
            integration = self._get_integration(integration_id)
            if not integration:
                return {'error': 'Integration not found'}
            
            # Decrypt configuration
            ldap_config = self._decrypt_sensitive_data(integration['configuration'])
            
            # Connect to LDAP server
            ldap_connection = self._connect_to_ldap(ldap_config)
            if not ldap_connection:
                return {'error': 'Failed to connect to LDAP server'}
            
            # Search for users
            users = self._search_ldap_users(ldap_connection, integration)
            
            # Synchronize users
            sync_results = {
                'users_found': len(users),
                'users_created': 0,
                'users_updated': 0,
                'users_deactivated': 0,
                'errors': []
            }
            
            for ldap_user in users:
                try:
                    result = self._sync_ldap_user(integration, ldap_user)
                    if result['action'] == 'created':
                        sync_results['users_created'] += 1
                    elif result['action'] == 'updated':
                        sync_results['users_updated'] += 1
                except Exception as e:
                    sync_results['errors'].append(f"Error syncing user {ldap_user.get('cn', 'unknown')}: {str(e)}")
            
            # Update integration last sync time
            integration['last_sync'] = datetime.utcnow()
            
            # Close LDAP connection
            ldap_connection.unbind()
            
            logger.info(f"LDAP sync completed for integration {integration_id}: {sync_results}")
            
            return {
                'sync_id': str(uuid.uuid4()),
                'integration_id': integration_id,
                'sync_results': sync_results,
                'sync_completed_at': datetime.utcnow().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error syncing LDAP users: {e}")
            return {'error': str(e)}
    
    # LMS Integration
    
    def configure_lms_integration(self, organization_id: int, lms_config: Dict[str, Any]) -> Dict[str, Any]:
        """Configure LMS integration (Canvas, Moodle, Blackboard)."""
        try:
            integration_id = str(uuid.uuid4())
            
            # Validate LMS configuration
            validation_result = self._validate_lms_config(lms_config)
            if not validation_result['valid']:
                return {'error': f"Invalid LMS configuration: {validation_result['errors']}"}
            
            # Encrypt sensitive data
            encrypted_config = self._encrypt_sensitive_data(lms_config)
            
            # Create LMS integration record
            lms_integration = {
                'integration_id': integration_id,
                'organization_id': organization_id,
                'integration_type': lms_config.get('type', IntegrationType.LMS_CANVAS.value),
                'lms_url': lms_config.get('lms_url'),
                'configuration': encrypted_config,
                'status': IntegrationStatus.PENDING.value,
                'created_at': datetime.utcnow(),
                'sync_direction': lms_config.get('sync_direction', SyncDirection.BIDIRECTIONAL.value),
                'sync_frequency_hours': lms_config.get('sync_frequency_hours', 12),
                'course_mapping': lms_config.get('course_mapping', {}),
                'grade_sync_enabled': lms_config.get('grade_sync_enabled', True),
                'enrollment_sync_enabled': lms_config.get('enrollment_sync_enabled', True),
                'assignment_sync_enabled': lms_config.get('assignment_sync_enabled', False)
            }
            
            # Test LMS connection
            test_result = self._test_lms_connection(lms_config)
            if test_result['success']:
                lms_integration['status'] = IntegrationStatus.ACTIVE.value
                lms_integration['last_test'] = datetime.utcnow()
            else:
                lms_integration['status'] = IntegrationStatus.ERROR.value
                lms_integration['error_message'] = test_result.get('error', 'Connection test failed')
            
            # Store integration (would be in lms_integrations table)
            logger.info(f"LMS integration {integration_id} configured for organization {organization_id}")
            
            return {
                'integration_id': integration_id,
                'status': lms_integration['status'],
                'lms_url': lms_integration['lms_url'],
                'lms_type': lms_integration['integration_type'],
                'test_result': test_result,
                'sync_capabilities': {
                    'courses': True,
                    'enrollments': lms_integration['enrollment_sync_enabled'],
                    'grades': lms_integration['grade_sync_enabled'],
                    'assignments': lms_integration['assignment_sync_enabled']
                }
            }
        
        except Exception as e:
            logger.error(f"Error configuring LMS integration: {e}")
            return {'error': str(e)}
    
    def sync_lms_data(self, integration_id: str, data_types: List[str] = None) -> Dict[str, Any]:
        """Synchronize data with LMS system."""
        try:
            # Get LMS integration configuration
            integration = self._get_integration(integration_id)
            if not integration:
                return {'error': 'Integration not found'}
            
            # Default data types to sync
            if not data_types:
                data_types = ['courses', 'enrollments', 'grades']
            
            # Decrypt configuration
            lms_config = self._decrypt_sensitive_data(integration['configuration'])
            
            # Initialize LMS API client
            lms_client = self._get_lms_client(integration['integration_type'], lms_config)
            
            sync_results = {
                'sync_id': str(uuid.uuid4()),
                'integration_id': integration_id,
                'data_types_synced': data_types,
                'results': {},
                'errors': []
            }
            
            # Sync each data type
            for data_type in data_types:
                try:
                    if data_type == 'courses':
                        result = self._sync_lms_courses(lms_client, integration)
                    elif data_type == 'enrollments':
                        result = self._sync_lms_enrollments(lms_client, integration)
                    elif data_type == 'grades':
                        result = self._sync_lms_grades(lms_client, integration)
                    else:
                        result = {'error': f'Unsupported data type: {data_type}'}
                    
                    sync_results['results'][data_type] = result
                except Exception as e:
                    sync_results['errors'].append(f"Error syncing {data_type}: {str(e)}")
            
            # Update integration last sync time
            integration['last_sync'] = datetime.utcnow()
            
            logger.info(f"LMS sync completed for integration {integration_id}")
            
            return sync_results
        
        except Exception as e:
            logger.error(f"Error syncing LMS data: {e}")
            return {'error': str(e)}
    
    # HR System Integration
    
    def configure_hr_integration(self, organization_id: int, hr_config: Dict[str, Any]) -> Dict[str, Any]:
        """Configure HR system integration (Workday, BambooHR, ADP)."""
        try:
            integration_id = str(uuid.uuid4())
            
            # Validate HR configuration
            validation_result = self._validate_hr_config(hr_config)
            if not validation_result['valid']:
                return {'error': f"Invalid HR configuration: {validation_result['errors']}"}
            
            # Encrypt sensitive data
            encrypted_config = self._encrypt_sensitive_data(hr_config)
            
            # Create HR integration record
            hr_integration = {
                'integration_id': integration_id,
                'organization_id': organization_id,
                'integration_type': hr_config.get('type', IntegrationType.HR_WORKDAY.value),
                'hr_system_url': hr_config.get('hr_system_url'),
                'configuration': encrypted_config,
                'status': IntegrationStatus.PENDING.value,
                'created_at': datetime.utcnow(),
                'sync_direction': hr_config.get('sync_direction', SyncDirection.INBOUND.value),
                'sync_frequency_hours': hr_config.get('sync_frequency_hours', 24),
                'employee_sync_enabled': hr_config.get('employee_sync_enabled', True),
                'org_structure_sync_enabled': hr_config.get('org_structure_sync_enabled', True),
                'training_records_sync_enabled': hr_config.get('training_records_sync_enabled', False),
                'field_mapping': hr_config.get('field_mapping', {})
            }
            
            # Test HR connection
            test_result = self._test_hr_connection(hr_config)
            if test_result['success']:
                hr_integration['status'] = IntegrationStatus.ACTIVE.value
                hr_integration['last_test'] = datetime.utcnow()
            else:
                hr_integration['status'] = IntegrationStatus.ERROR.value
                hr_integration['error_message'] = test_result.get('error', 'Connection test failed')
            
            # Store integration (would be in hr_integrations table)
            logger.info(f"HR integration {integration_id} configured for organization {organization_id}")
            
            return {
                'integration_id': integration_id,
                'status': hr_integration['status'],
                'hr_system_url': hr_integration['hr_system_url'],
                'hr_system_type': hr_integration['integration_type'],
                'test_result': test_result,
                'sync_capabilities': {
                    'employees': hr_integration['employee_sync_enabled'],
                    'org_structure': hr_integration['org_structure_sync_enabled'],
                    'training_records': hr_integration['training_records_sync_enabled']
                }
            }
        
        except Exception as e:
            logger.error(f"Error configuring HR integration: {e}")
            return {'error': str(e)}
    
    # Helper Methods
    
    def _get_encryption_key(self) -> bytes:
        """Get or generate encryption key for sensitive data."""
        # In production, this would be stored securely
        return Fernet.generate_key()
    
    def _encrypt_sensitive_data(self, data: Dict[str, Any]) -> str:
        """Encrypt sensitive configuration data."""
        fernet = Fernet(self.encryption_key)
        json_data = json.dumps(data)
        encrypted_data = fernet.encrypt(json_data.encode())
        return base64.b64encode(encrypted_data).decode()
    
    def _decrypt_sensitive_data(self, encrypted_data: str) -> Dict[str, Any]:
        """Decrypt sensitive configuration data."""
        fernet = Fernet(self.encryption_key)
        decoded_data = base64.b64decode(encrypted_data.encode())
        decrypted_data = fernet.decrypt(decoded_data)
        return json.loads(decrypted_data.decode())
    
    def _validate_sso_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate SSO configuration."""
        errors = []
        
        required_fields = ['type', 'provider_name', 'entity_id', 'sso_url']
        for field in required_fields:
            if not config.get(field):
                errors.append(f"Missing required field: {field}")
        
        if config.get('type') == IntegrationType.SSO_SAML.value:
            if not config.get('x509_certificate'):
                errors.append("X.509 certificate required for SAML SSO")
        
        return {'valid': len(errors) == 0, 'errors': errors}
    
    def _validate_ldap_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate LDAP configuration."""
        errors = []
        
        required_fields = ['server_url', 'bind_dn', 'bind_password', 'user_base_dn']
        for field in required_fields:
            if not config.get(field):
                errors.append(f"Missing required field: {field}")
        
        return {'valid': len(errors) == 0, 'errors': errors}
    
    def _validate_lms_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate LMS configuration."""
        errors = []
        
        required_fields = ['type', 'lms_url', 'api_key']
        for field in required_fields:
            if not config.get(field):
                errors.append(f"Missing required field: {field}")
        
        return {'valid': len(errors) == 0, 'errors': errors}
    
    def _validate_hr_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate HR system configuration."""
        errors = []
        
        required_fields = ['type', 'hr_system_url', 'username', 'password']
        for field in required_fields:
            if not config.get(field):
                errors.append(f"Missing required field: {field}")
        
        return {'valid': len(errors) == 0, 'errors': errors}
