"""Unified error handling service for all agents."""

import logging
import traceback
from typing import Dict, Any, Optional
from fastapi import HTTPEx<PERSON>, status
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class UnifiedErrorHandler:
    """Centralized error handling for all 5 agents."""
    
    @staticmethod
    def create_error_response(
        error: Exception,
        error_code: str,
        user_message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Create standardized error response."""
        
        error_id = str(uuid.uuid4())
        
        # Log the error with full context
        logger.error(
            f"Error {error_id}: {error_code} - {str(error)}",
            extra={
                "error_id": error_id,
                "error_code": error_code,
                "error_type": type(error).__name__,
                "context": context or {},
                "traceback": traceback.format_exc()
            }
        )
        
        # Create user-friendly error response
        error_detail = {
            "error_id": error_id,
            "error_code": error_code,
            "message": user_message,
            "timestamp": datetime.utcnow().isoformat(),
            "type": "application_error"
        }
        
        # Add context for debugging (only in development)
        if context and logger.level <= logging.DEBUG:
            error_detail["debug_context"] = context
        
        return HTTPException(
            status_code=status_code,
            detail=error_detail
        )
    
    @staticmethod
    def handle_validation_error(
        field: str,
        value: Any,
        message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Handle validation errors consistently."""
        return UnifiedErrorHandler.create_error_response(
            error=ValueError(f"Validation failed for {field}: {message}"),
            error_code="VALIDATION_ERROR",
            user_message=f"Invalid {field}: {message}",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            context={
                "field": field,
                "value": str(value),
                "validation_message": message,
                **(context or {})
            }
        )
    
    @staticmethod
    def handle_not_found_error(
        resource_type: str,
        resource_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Handle resource not found errors consistently."""
        return UnifiedErrorHandler.create_error_response(
            error=ValueError(f"{resource_type} not found: {resource_id}"),
            error_code="RESOURCE_NOT_FOUND",
            user_message=f"{resource_type} not found",
            status_code=status.HTTP_404_NOT_FOUND,
            context={
                "resource_type": resource_type,
                "resource_id": resource_id,
                **(context or {})
            }
        )
    
    @staticmethod
    def handle_authentication_error(
        message: str = "Authentication required",
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Handle authentication errors consistently."""
        return UnifiedErrorHandler.create_error_response(
            error=ValueError("Authentication failed"),
            error_code="AUTHENTICATION_ERROR",
            user_message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            context=context
        )
    
    @staticmethod
    def handle_authorization_error(
        resource: str,
        action: str,
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Handle authorization errors consistently."""
        return UnifiedErrorHandler.create_error_response(
            error=ValueError(f"Access denied to {action} {resource}"),
            error_code="AUTHORIZATION_ERROR",
            user_message=f"Access denied to {action} {resource}",
            status_code=status.HTTP_403_FORBIDDEN,
            context={
                "resource": resource,
                "action": action,
                **(context or {})
            }
        )
    
    @staticmethod
    def handle_service_error(
        service_name: str,
        operation: str,
        error: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Handle service-specific errors consistently."""
        return UnifiedErrorHandler.create_error_response(
            error=error,
            error_code=f"{service_name.upper()}_SERVICE_ERROR",
            user_message=f"Error in {service_name} service during {operation}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            context={
                "service": service_name,
                "operation": operation,
                **(context or {})
            }
        )
    
    @staticmethod
    def handle_external_api_error(
        api_name: str,
        operation: str,
        error: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Handle external API errors consistently."""
        return UnifiedErrorHandler.create_error_response(
            error=error,
            error_code="EXTERNAL_API_ERROR",
            user_message=f"External service temporarily unavailable",
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            context={
                "external_api": api_name,
                "operation": operation,
                **(context or {})
            }
        )
    
    @staticmethod
    def handle_database_error(
        operation: str,
        error: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Handle database errors consistently."""
        return UnifiedErrorHandler.create_error_response(
            error=error,
            error_code="DATABASE_ERROR",
            user_message="Database operation failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            context={
                "database_operation": operation,
                **(context or {})
            }
        )
    
    @staticmethod
    def handle_rate_limit_error(
        limit: int,
        window: str,
        context: Optional[Dict[str, Any]] = None
    ) -> HTTPException:
        """Handle rate limiting errors consistently."""
        return UnifiedErrorHandler.create_error_response(
            error=ValueError("Rate limit exceeded"),
            error_code="RATE_LIMIT_EXCEEDED",
            user_message=f"Rate limit exceeded: {limit} requests per {window}",
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            context={
                "rate_limit": limit,
                "time_window": window,
                **(context or {})
            }
        )


# Convenience functions for common error patterns
def validation_error(field: str, value: Any, message: str, **context) -> HTTPException:
    """Quick validation error."""
    return UnifiedErrorHandler.handle_validation_error(field, value, message, context)

def not_found_error(resource_type: str, resource_id: str, **context) -> HTTPException:
    """Quick not found error."""
    return UnifiedErrorHandler.handle_not_found_error(resource_type, resource_id, context)

def service_error(service_name: str, operation: str, error: Exception, **context) -> HTTPException:
    """Quick service error."""
    return UnifiedErrorHandler.handle_service_error(service_name, operation, error, context)

def auth_error(message: str = "Authentication required", **context) -> HTTPException:
    """Quick authentication error."""
    return UnifiedErrorHandler.handle_authentication_error(message, context)

def authz_error(resource: str, action: str, **context) -> HTTPException:
    """Quick authorization error."""
    return UnifiedErrorHandler.handle_authorization_error(resource, action, context)


# Error handling decorator
def handle_service_errors(service_name: str):
    """Decorator to automatically handle service errors."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except HTTPException:
                # Re-raise HTTP exceptions as-is
                raise
            except Exception as e:
                # Convert other exceptions to standardized format
                raise service_error(service_name, func.__name__, e)
        return wrapper
    return decorator


# Component-specific error handlers
class CorePlatformErrorHandler(UnifiedErrorHandler):
    """Error handler for Core Platform."""

    @staticmethod
    def certification_not_found(cert_id: str) -> HTTPException:
        return not_found_error("Certification", cert_id, component="core_platform")

    @staticmethod
    def user_not_found(user_id: str) -> HTTPException:
        return not_found_error("User", user_id, component="core_platform")


class AIStudyAssistantErrorHandler(UnifiedErrorHandler):
    """Error handler for AI Study Assistant."""

    @staticmethod
    def ai_model_error(operation: str, error: Exception) -> HTTPException:
        return service_error("ai_study_assistant", operation, error, component="ai_study_assistant")

    @staticmethod
    def recommendation_generation_failed(user_id: str, error: Exception) -> HTTPException:
        return service_error(
            "ai_study_assistant",
            "recommendation_generation",
            error,
            component="ai_study_assistant",
            user_id=user_id
        )


class EnterpriseAnalyticsErrorHandler(UnifiedErrorHandler):
    """Error handler for Enterprise Analytics."""

    @staticmethod
    def enterprise_not_found(enterprise_id: str) -> HTTPException:
        return not_found_error("Enterprise", enterprise_id, component="enterprise_analytics")

    @staticmethod
    def compliance_analysis_failed(enterprise_id: str, error: Exception) -> HTTPException:
        return service_error(
            "enterprise_analytics",
            "compliance_analysis",
            error,
            component="enterprise_analytics",
            enterprise_id=enterprise_id
        )


class CareerIntelligenceErrorHandler(UnifiedErrorHandler):
    """Error handler for Career & Cost Intelligence."""

    @staticmethod
    def salary_data_unavailable(role_id: str, location: str) -> HTTPException:
        return service_error(
            "salary_intelligence",
            "salary_analysis",
            ValueError("Salary data unavailable"),
            component="career_intelligence",
            role_id=role_id,
            location=location
        )

    @staticmethod
    def career_path_not_found(source_role: str, target_role: str) -> HTTPException:
        return not_found_error(
            "Career Path",
            f"{source_role} -> {target_role}",
            component="career_intelligence"
        )


class MarketplaceHubErrorHandler(UnifiedErrorHandler):
    """Error handler for Marketplace Hub."""

    @staticmethod
    def marketplace_api_error(api_name: str, operation: str, error: Exception) -> HTTPException:
        return UnifiedErrorHandler.handle_external_api_error(
            api_name,
            operation,
            error,
            {"component": "marketplace_hub"}
        )

    @staticmethod
    def partner_integration_failed(partner: str, error: Exception) -> HTTPException:
        return service_error(
            "marketplace_hub",
            "partner_integration",
            error,
            component="marketplace_hub",
            partner=partner
        )
