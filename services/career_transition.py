"""Career transition service for pathfinding and budget-aware planning.

This service provides comprehensive career transition planning with budget constraints,
timeline optimization, and difficulty-based pathfinding algorithms.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime, timedelta
import heapq
from dataclasses import dataclass

from models.career_transition import (
    CareerRole, CareerTransitionPath, CareerTransitionPlan, CareerTransitionStep
)
from models.certification import Certification
from models.cost_calculation import CostCalculation
from services.cost_calculator import CostCalculatorService

logger = logging.getLogger(__name__)


@dataclass
class PathfindingConstraints:
    """Constraints for career pathfinding algorithm."""
    max_budget: Optional[float] = None
    max_timeline_months: Optional[int] = None
    max_difficulty: str = 'Expert'  # Easy, Medium, Hard, Expert
    preferred_learning_style: str = 'Mixed'
    study_hours_per_week: int = 10
    currency: str = 'USD'


@dataclass
class PathOption:
    """Represents a potential career transition path option."""
    path_id: int
    total_cost: float
    total_duration_months: int
    difficulty_score: float
    success_probability: float
    steps: List[Dict[str, Any]]
    certifications_required: List[int]
    estimated_salary_increase: float


class CareerTransitionService:
    """Service for career transition planning and pathfinding."""
    
    def __init__(self, db: Session):
        self.db = db
        self.cost_calculator = CostCalculatorService(db)
        self.difficulty_weights = {
            'Easy': 1.0,
            'Medium': 2.0,
            'Hard': 3.0,
            'Expert': 4.0
        }
    
    def find_career_paths(
        self,
        source_role_id: Optional[int],
        target_role_id: int,
        constraints: PathfindingConstraints,
        max_paths: int = 10
    ) -> List[PathOption]:
        """Find optimal career transition paths using A* algorithm with budget constraints."""
        logger.info(f"Finding career paths from role {source_role_id} to {target_role_id}")
        
        # Get all possible paths
        if source_role_id:
            direct_paths = self._find_direct_paths(source_role_id, target_role_id)
            indirect_paths = self._find_indirect_paths(source_role_id, target_role_id, max_hops=3)
            all_paths = direct_paths + indirect_paths
        else:
            # Starting from scratch - find entry-level paths to target
            all_paths = self._find_entry_level_paths(target_role_id)
        
        # Evaluate and rank paths
        evaluated_paths = []
        for path in all_paths:
            try:
                path_option = self._evaluate_path(path, constraints)
                if path_option and self._meets_constraints(path_option, constraints):
                    evaluated_paths.append(path_option)
            except Exception as e:
                logger.warning(f"Error evaluating path {path.get('id', 'unknown')}: {e}")
                continue
        
        # Sort by composite score (cost, time, difficulty, success rate)
        evaluated_paths.sort(key=lambda p: self._calculate_path_score(p, constraints))
        
        return evaluated_paths[:max_paths]
    
    def _find_direct_paths(self, source_role_id: int, target_role_id: int) -> List[Dict[str, Any]]:
        """Find direct transition paths between two roles."""
        paths = self.db.query(CareerTransitionPath).filter(
            and_(
                CareerTransitionPath.source_role_id == source_role_id,
                CareerTransitionPath.target_role_id == target_role_id,
                CareerTransitionPath.is_active == True
            )
        ).all()
        
        return [path.to_dict() for path in paths]
    
    def _find_indirect_paths(
        self, 
        source_role_id: int, 
        target_role_id: int, 
        max_hops: int = 3
    ) -> List[Dict[str, Any]]:
        """Find indirect paths through intermediate roles using graph traversal."""
        visited = set()
        paths = []
        
        def dfs(current_role_id: int, path: List[int], depth: int):
            if depth > max_hops or current_role_id in visited:
                return
            
            if current_role_id == target_role_id and len(path) > 1:
                # Found a valid indirect path
                path_segments = []
                for i in range(len(path) - 1):
                    segment = self.db.query(CareerTransitionPath).filter(
                        and_(
                            CareerTransitionPath.source_role_id == path[i],
                            CareerTransitionPath.target_role_id == path[i + 1],
                            CareerTransitionPath.is_active == True
                        )
                    ).first()
                    
                    if segment:
                        path_segments.append(segment.to_dict())
                
                if len(path_segments) == len(path) - 1:
                    paths.append({
                        'type': 'indirect',
                        'segments': path_segments,
                        'total_hops': len(path) - 1
                    })
                return
            
            visited.add(current_role_id)
            
            # Find next possible roles
            next_paths = self.db.query(CareerTransitionPath).filter(
                and_(
                    CareerTransitionPath.source_role_id == current_role_id,
                    CareerTransitionPath.is_active == True
                )
            ).all()
            
            for next_path in next_paths:
                dfs(next_path.target_role_id, path + [next_path.target_role_id], depth + 1)
            
            visited.remove(current_role_id)
        
        dfs(source_role_id, [source_role_id], 0)
        return paths
    
    def _find_entry_level_paths(self, target_role_id: int) -> List[Dict[str, Any]]:
        """Find paths from entry-level positions to target role."""
        # Find entry-level roles in the same domain as target
        target_role = self.db.query(CareerRole).filter(CareerRole.id == target_role_id).first()
        if not target_role:
            return []
        
        entry_roles = self.db.query(CareerRole).filter(
            and_(
                CareerRole.domain == target_role.domain,
                CareerRole.level.in_(['Entry', 'Junior']),
                CareerRole.is_active == True
            )
        ).all()
        
        paths = []
        for entry_role in entry_roles:
            if entry_role.id != target_role_id:
                indirect_paths = self._find_indirect_paths(entry_role.id, target_role_id, max_hops=4)
                paths.extend(indirect_paths)
        
        return paths
    
    def _evaluate_path(self, path: Dict[str, Any], constraints: PathfindingConstraints) -> Optional[PathOption]:
        """Evaluate a career transition path and calculate costs/timeline."""
        try:
            if path.get('type') == 'indirect':
                return self._evaluate_indirect_path(path, constraints)
            else:
                return self._evaluate_direct_path(path, constraints)
        except Exception as e:
            logger.error(f"Error evaluating path: {e}")
            return None
    
    def _evaluate_direct_path(self, path: Dict[str, Any], constraints: PathfindingConstraints) -> PathOption:
        """Evaluate a direct transition path."""
        # Calculate certification costs
        required_certs = path.get('required_certifications', [])
        recommended_certs = path.get('recommended_certifications', [])
        
        total_cost = 0.0
        certifications_needed = []
        
        # Get certification costs
        if required_certs or recommended_certs:
            all_cert_ids = list(set(required_certs + recommended_certs))
            certifications = self.db.query(Certification).filter(
                Certification.id.in_(all_cert_ids)
            ).all()
            
            for cert in certifications:
                # Use cost calculator for accurate pricing
                try:
                    cost_calc = self.cost_calculator.calculate_certification_costs(
                        user_id='temp_user',
                        certification_ids=[cert.id],
                        base_currency=constraints.currency,
                        target_currency=constraints.currency,
                        scenario_id=None,  # Use default scenario
                        materials_cost=0.0,
                        additional_costs=0.0
                    )
                    total_cost += cost_calc.total_cost_target
                    certifications_needed.append(cert.id)
                except Exception as e:
                    logger.warning(f"Error calculating cost for cert {cert.id}: {e}")
                    # Fallback to basic cost
                    total_cost += cert.cost or 0.0
                    certifications_needed.append(cert.id)
        
        # Add estimated additional costs from path
        total_cost += path.get('estimated_cost_min', 0.0)
        
        # Calculate difficulty score
        difficulty_score = self.difficulty_weights.get(path.get('difficulty_level', 'Medium'), 2.0)
        
        # Create steps
        steps = [{
            'name': f"Complete {path.get('name', 'Career Transition')}",
            'description': path.get('description', ''),
            'type': 'transition',
            'duration_months': path.get('estimated_duration_months', 12),
            'cost': total_cost,
            'certifications': certifications_needed
        }]
        
        return PathOption(
            path_id=path.get('id', 0),
            total_cost=total_cost,
            total_duration_months=path.get('estimated_duration_months', 12),
            difficulty_score=difficulty_score,
            success_probability=path.get('success_rate', 0.7),
            steps=steps,
            certifications_required=certifications_needed,
            estimated_salary_increase=path.get('average_salary_increase', 0.0)
        )
    
    def _evaluate_indirect_path(self, path: Dict[str, Any], constraints: PathfindingConstraints) -> PathOption:
        """Evaluate an indirect transition path with multiple segments."""
        segments = path.get('segments', [])
        if not segments:
            return None
        
        total_cost = 0.0
        total_duration = 0
        total_difficulty = 0.0
        all_certifications = []
        steps = []
        success_probability = 1.0
        total_salary_increase = 0.0
        
        for i, segment in enumerate(segments):
            # Evaluate each segment
            segment_option = self._evaluate_direct_path(segment, constraints)
            if segment_option:
                total_cost += segment_option.total_cost
                total_duration += segment_option.total_duration_months
                total_difficulty += segment_option.difficulty_score
                all_certifications.extend(segment_option.certifications_required)
                steps.extend(segment_option.steps)
                success_probability *= segment_option.success_probability
                total_salary_increase += segment_option.estimated_salary_increase
        
        # Remove duplicate certifications
        unique_certifications = list(set(all_certifications))
        
        # Average difficulty across segments
        avg_difficulty = total_difficulty / len(segments) if segments else 2.0
        
        return PathOption(
            path_id=f"indirect_{hash(str(segments))}",
            total_cost=total_cost,
            total_duration_months=total_duration,
            difficulty_score=avg_difficulty,
            success_probability=success_probability,
            steps=steps,
            certifications_required=unique_certifications,
            estimated_salary_increase=total_salary_increase
        )
    
    def _meets_constraints(self, path_option: PathOption, constraints: PathfindingConstraints) -> bool:
        """Check if a path option meets the given constraints."""
        # Budget constraint
        if constraints.max_budget and path_option.total_cost > constraints.max_budget:
            return False
        
        # Timeline constraint
        if constraints.max_timeline_months and path_option.total_duration_months > constraints.max_timeline_months:
            return False
        
        # Difficulty constraint
        max_difficulty_score = self.difficulty_weights.get(constraints.max_difficulty, 4.0)
        if path_option.difficulty_score > max_difficulty_score:
            return False
        
        return True
    
    def _calculate_path_score(self, path_option: PathOption, constraints: PathfindingConstraints) -> float:
        """Calculate a composite score for ranking path options (lower is better)."""
        # Normalize factors (0-1 scale)
        cost_score = 0.0
        if constraints.max_budget:
            cost_score = min(path_option.total_cost / constraints.max_budget, 1.0)
        
        time_score = 0.0
        if constraints.max_timeline_months:
            time_score = min(path_option.total_duration_months / constraints.max_timeline_months, 1.0)
        
        difficulty_score = path_option.difficulty_score / 4.0  # Normalize to 0-1
        
        success_score = 1.0 - path_option.success_probability  # Invert (lower is better)
        
        # Weighted composite score
        composite_score = (
            cost_score * 0.3 +           # 30% weight on cost
            time_score * 0.25 +          # 25% weight on time
            difficulty_score * 0.2 +     # 20% weight on difficulty
            success_score * 0.25         # 25% weight on success probability
        )
        
        return composite_score
    
    def create_transition_plan(
        self,
        user_id: str,
        name: str,
        current_role_id: Optional[int],
        target_role_id: int,
        constraints: PathfindingConstraints,
        description: Optional[str] = None
    ) -> CareerTransitionPlan:
        """Create a new career transition plan for a user."""
        logger.info(f"Creating transition plan for user {user_id}")
        
        # Find optimal paths
        path_options = self.find_career_paths(
            current_role_id, target_role_id, constraints, max_paths=5
        )
        
        if not path_options:
            raise ValueError("No viable career transition paths found with given constraints")
        
        # Select the best path
        best_path = path_options[0]
        alternative_path_ids = [p.path_id for p in path_options[1:]]
        
        # Create the plan
        plan = CareerTransitionPlan(
            user_id=user_id,
            name=name,
            description=description,
            current_role_id=current_role_id,
            target_role_id=target_role_id,
            budget_min=0.0,
            budget_max=constraints.max_budget,
            budget_currency=constraints.currency,
            timeline_months=best_path.total_duration_months,
            max_timeline_months=constraints.max_timeline_months,
            difficulty_preference=constraints.max_difficulty,
            learning_style=constraints.preferred_learning_style,
            study_hours_per_week=constraints.study_hours_per_week,
            selected_path_id=best_path.path_id if isinstance(best_path.path_id, int) else None,
            alternative_paths=alternative_path_ids
        )
        
        self.db.add(plan)
        self.db.flush()  # Get the plan ID
        
        # Create transition steps
        for i, step_data in enumerate(best_path.steps):
            step = CareerTransitionStep(
                plan_id=plan.id,
                name=step_data['name'],
                description=step_data.get('description', ''),
                step_type=step_data.get('type', 'transition'),
                sequence=i + 1,
                estimated_duration_weeks=step_data.get('duration_months', 1) * 4,
                estimated_cost=step_data.get('cost', 0.0),
                cost_currency=constraints.currency
            )
            self.db.add(step)
        
        self.db.commit()
        
        logger.info(f"Created transition plan {plan.id} with {len(best_path.steps)} steps")
        return plan
    
    def get_user_transition_plans(self, user_id: str) -> List[CareerTransitionPlan]:
        """Get all transition plans for a user."""
        return self.db.query(CareerTransitionPlan).filter(
            and_(
                CareerTransitionPlan.user_id == user_id,
                CareerTransitionPlan.is_active == True
            )
        ).order_by(CareerTransitionPlan.created_at.desc()).all()
    
    def update_plan_progress(self, plan_id: int, step_id: int, progress_percentage: float) -> bool:
        """Update progress for a specific step in a transition plan."""
        step = self.db.query(CareerTransitionStep).filter(
            CareerTransitionStep.id == step_id,
            CareerTransitionStep.plan_id == plan_id
        ).first()
        
        if not step:
            return False
        
        step.progress_percentage = progress_percentage
        
        if progress_percentage >= 100.0:
            step.status = 'completed'
            step.completed_at = datetime.utcnow()
        elif progress_percentage > 0.0:
            step.status = 'in_progress'
            if not step.started_at:
                step.started_at = datetime.utcnow()
        
        # Update overall plan progress
        plan = self.db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.id == plan_id
        ).first()
        
        if plan:
            all_steps = self.db.query(CareerTransitionStep).filter(
                CareerTransitionStep.plan_id == plan_id
            ).all()
            
            total_progress = sum(s.progress_percentage for s in all_steps)
            plan.progress_percentage = total_progress / len(all_steps) if all_steps else 0.0
            
            if plan.progress_percentage >= 100.0:
                plan.status = 'completed'
        
        self.db.commit()
        return True
