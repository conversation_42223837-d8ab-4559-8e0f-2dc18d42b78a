"""Progress tracking and learning analytics service.

This service provides comprehensive progress tracking, analytics generation,
and intelligent insights for user learning journeys.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from datetime import datetime, timedelta
from dataclasses import dataclass
import statistics

from models.progress_tracking import (
    StudySession, PracticeTestResult, LearningGoal, Achievement, LearningAnalytics
)
from models.certification import Certification
from models.learning_path import LearningPath
from models.career_transition import CareerTransitionPlan

logger = logging.getLogger(__name__)


@dataclass
class StudyInsights:
    """Data class for study insights and recommendations."""
    optimal_study_time: str
    recommended_session_length: int
    focus_improvement_tips: List[str]
    weak_areas: List[str]
    strong_areas: List[str]
    next_actions: List[str]


@dataclass
class ProgressSummary:
    """Data class for progress summary information."""
    total_study_hours: float
    current_streak: int
    longest_streak: int
    average_session_rating: float
    certifications_in_progress: int
    goals_completed: int
    achievements_earned: int
    overall_progress_percentage: float


class ProgressTrackingService:
    """Service for comprehensive progress tracking and analytics."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def start_study_session(
        self,
        user_id: str,
        session_type: str,
        certification_id: Optional[int] = None,
        learning_path_id: Optional[int] = None,
        transition_plan_id: Optional[int] = None,
        topic: Optional[str] = None,
        planned_duration_minutes: Optional[int] = None
    ) -> StudySession:
        """Start a new study session."""
        logger.info(f"Starting study session for user {user_id}")
        
        session = StudySession(
            user_id=user_id,
            certification_id=certification_id,
            learning_path_id=learning_path_id,
            transition_plan_id=transition_plan_id,
            session_type=session_type,
            topic=topic,
            started_at=datetime.utcnow(),
            planned_duration_minutes=planned_duration_minutes
        )
        
        self.db.add(session)
        self.db.commit()
        
        logger.info(f"Started study session {session.id}")
        return session
    
    def end_study_session(
        self,
        session_id: int,
        user_id: str,
        progress_after: float,
        confidence_level: Optional[int] = None,
        difficulty_rating: Optional[int] = None,
        focus_rating: Optional[int] = None,
        effectiveness_rating: Optional[int] = None,
        notes: Optional[str] = None,
        materials_used: Optional[List[str]] = None,
        tags: Optional[List[str]] = None
    ) -> StudySession:
        """End a study session and record progress."""
        logger.info(f"Ending study session {session_id} for user {user_id}")
        
        session = self.db.query(StudySession).filter(
            StudySession.id == session_id,
            StudySession.user_id == user_id
        ).first()
        
        if not session:
            raise ValueError(f"Study session {session_id} not found for user {user_id}")
        
        # Calculate duration
        ended_at = datetime.utcnow()
        duration_minutes = int((ended_at - session.started_at).total_seconds() / 60)
        
        # Update session
        session.ended_at = ended_at
        session.duration_minutes = duration_minutes
        session.progress_after = progress_after
        session.confidence_level = confidence_level
        session.difficulty_rating = difficulty_rating
        session.focus_rating = focus_rating
        session.effectiveness_rating = effectiveness_rating
        session.notes = notes
        session.materials_used = materials_used or []
        session.tags = tags or []
        
        self.db.commit()
        
        # Update analytics
        self._update_analytics_for_session(session)
        
        # Check for achievements
        self._check_study_achievements(user_id, session)
        
        logger.info(f"Ended study session {session_id}, duration: {duration_minutes} minutes")
        return session
    
    def record_practice_test_result(
        self,
        user_id: str,
        certification_id: int,
        test_name: str,
        test_type: str,
        total_questions: int,
        correct_answers: int,
        time_taken_minutes: Optional[int] = None,
        domain_scores: Optional[Dict[str, float]] = None,
        topic_scores: Optional[Dict[str, float]] = None,
        question_details: Optional[List[Dict]] = None,
        test_provider: Optional[str] = None,
        passing_score: Optional[float] = None
    ) -> PracticeTestResult:
        """Record a practice test result."""
        logger.info(f"Recording practice test result for user {user_id}")
        
        # Calculate scores
        incorrect_answers = total_questions - correct_answers
        percentage = (correct_answers / total_questions) * 100
        passed = passing_score is None or percentage >= passing_score
        
        # Analyze weak and strong areas
        weak_areas = []
        strong_areas = []
        
        if domain_scores:
            for domain, score in domain_scores.items():
                if score < 70:
                    weak_areas.append(domain)
                elif score >= 85:
                    strong_areas.append(domain)
        
        result = PracticeTestResult(
            user_id=user_id,
            certification_id=certification_id,
            test_name=test_name,
            test_type=test_type,
            test_provider=test_provider,
            total_questions=total_questions,
            passing_score=passing_score,
            score=correct_answers,
            percentage=percentage,
            passed=passed,
            time_taken_minutes=time_taken_minutes,
            correct_answers=correct_answers,
            incorrect_answers=incorrect_answers,
            domain_scores=domain_scores or {},
            topic_scores=topic_scores or {},
            weak_areas=weak_areas,
            strong_areas=strong_areas,
            question_details=question_details or []
        )
        
        self.db.add(result)
        self.db.commit()
        
        # Update analytics
        self._update_analytics_for_test(result)
        
        # Check for achievements
        self._check_test_achievements(user_id, result)
        
        logger.info(f"Recorded practice test result {result.id}, score: {percentage:.1f}%")
        return result
    
    def create_learning_goal(
        self,
        user_id: str,
        title: str,
        goal_type: str,
        target_value: Optional[float] = None,
        target_unit: Optional[str] = None,
        target_date: Optional[datetime] = None,
        certification_id: Optional[int] = None,
        description: Optional[str] = None,
        priority: str = 'medium'
    ) -> LearningGoal:
        """Create a new learning goal."""
        logger.info(f"Creating learning goal for user {user_id}: {title}")
        
        goal = LearningGoal(
            user_id=user_id,
            title=title,
            description=description,
            goal_type=goal_type,
            certification_id=certification_id,
            target_value=target_value,
            target_unit=target_unit,
            target_date=target_date,
            priority=priority
        )
        
        self.db.add(goal)
        self.db.commit()
        
        logger.info(f"Created learning goal {goal.id}")
        return goal
    
    def update_goal_progress(self, goal_id: int, user_id: str, current_value: float) -> LearningGoal:
        """Update progress on a learning goal."""
        goal = self.db.query(LearningGoal).filter(
            LearningGoal.id == goal_id,
            LearningGoal.user_id == user_id
        ).first()
        
        if not goal:
            raise ValueError(f"Learning goal {goal_id} not found for user {user_id}")
        
        goal.current_value = current_value
        
        # Calculate progress percentage
        if goal.target_value and goal.target_value > 0:
            goal.progress_percentage = min((current_value / goal.target_value) * 100, 100)
        
        # Check if goal is completed
        if goal.progress_percentage >= 100 and goal.status == 'active':
            goal.status = 'completed'
            self._award_goal_achievement(user_id, goal)
        
        self.db.commit()
        
        logger.info(f"Updated goal {goal_id} progress: {goal.progress_percentage:.1f}%")
        return goal
    
    def get_user_progress_summary(self, user_id: str) -> ProgressSummary:
        """Get comprehensive progress summary for a user."""
        logger.info(f"Generating progress summary for user {user_id}")
        
        # Study time analytics
        sessions = self.db.query(StudySession).filter(
            StudySession.user_id == user_id,
            StudySession.ended_at.isnot(None)
        ).all()
        
        total_minutes = sum(s.duration_minutes or 0 for s in sessions)
        total_hours = total_minutes / 60
        
        # Calculate study streak
        current_streak = self._calculate_study_streak(user_id)
        longest_streak = self._calculate_longest_streak(user_id)
        
        # Average session rating
        ratings = [s.effectiveness_rating for s in sessions if s.effectiveness_rating]
        avg_rating = statistics.mean(ratings) if ratings else 0.0
        
        # Goals and achievements
        goals_completed = self.db.query(LearningGoal).filter(
            LearningGoal.user_id == user_id,
            LearningGoal.status == 'completed'
        ).count()
        
        achievements_earned = self.db.query(Achievement).filter(
            Achievement.user_id == user_id,
            Achievement.is_earned == True
        ).count()
        
        # Certifications in progress
        active_paths = self.db.query(LearningPath).filter(
            LearningPath.user_experience.has(user_id=user_id),
            LearningPath.status == 'active'
        ).count()
        
        # Overall progress (simplified calculation)
        overall_progress = self._calculate_overall_progress(user_id)
        
        return ProgressSummary(
            total_study_hours=total_hours,
            current_streak=current_streak,
            longest_streak=longest_streak,
            average_session_rating=avg_rating,
            certifications_in_progress=active_paths,
            goals_completed=goals_completed,
            achievements_earned=achievements_earned,
            overall_progress_percentage=overall_progress
        )
    
    def get_study_insights(self, user_id: str, days_back: int = 30) -> StudyInsights:
        """Generate intelligent study insights and recommendations."""
        logger.info(f"Generating study insights for user {user_id}")
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_back)
        
        # Get recent sessions
        sessions = self.db.query(StudySession).filter(
            StudySession.user_id == user_id,
            StudySession.started_at >= cutoff_date,
            StudySession.ended_at.isnot(None)
        ).all()
        
        # Get recent test results
        test_results = self.db.query(PracticeTestResult).filter(
            PracticeTestResult.user_id == user_id,
            PracticeTestResult.created_at >= cutoff_date
        ).all()
        
        # Analyze optimal study time
        optimal_time = self._analyze_optimal_study_time(sessions)
        
        # Recommend session length
        recommended_length = self._recommend_session_length(sessions)
        
        # Generate focus improvement tips
        focus_tips = self._generate_focus_tips(sessions)
        
        # Identify weak and strong areas
        weak_areas, strong_areas = self._analyze_performance_areas(test_results)
        
        # Generate next actions
        next_actions = self._generate_next_actions(user_id, sessions, test_results)
        
        return StudyInsights(
            optimal_study_time=optimal_time,
            recommended_session_length=recommended_length,
            focus_improvement_tips=focus_tips,
            weak_areas=weak_areas,
            strong_areas=strong_areas,
            next_actions=next_actions
        )
    
    def get_learning_analytics(
        self, 
        user_id: str, 
        period_type: str = 'monthly'
    ) -> Optional[LearningAnalytics]:
        """Get or generate learning analytics for a specific period."""
        # Calculate period boundaries
        now = datetime.utcnow()
        
        if period_type == 'daily':
            period_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            period_end = period_start + timedelta(days=1)
        elif period_type == 'weekly':
            days_since_monday = now.weekday()
            period_start = (now - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
            period_end = period_start + timedelta(days=7)
        elif period_type == 'monthly':
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month = period_start.replace(month=period_start.month + 1) if period_start.month < 12 else period_start.replace(year=period_start.year + 1, month=1)
            period_end = next_month
        else:
            raise ValueError(f"Unsupported period type: {period_type}")
        
        # Check if analytics already exist
        analytics = self.db.query(LearningAnalytics).filter(
            LearningAnalytics.user_id == user_id,
            LearningAnalytics.period_type == period_type,
            LearningAnalytics.period_start == period_start
        ).first()
        
        if analytics:
            return analytics
        
        # Generate new analytics
        return self._generate_learning_analytics(user_id, period_type, period_start, period_end)
    
    def _update_analytics_for_session(self, session: StudySession) -> None:
        """Update analytics after a study session."""
        # This would update daily/weekly/monthly analytics
        # Implementation would aggregate session data
        pass
    
    def _update_analytics_for_test(self, result: PracticeTestResult) -> None:
        """Update analytics after a practice test."""
        # This would update test performance analytics
        # Implementation would track score trends and weak areas
        pass
    
    def _check_study_achievements(self, user_id: str, session: StudySession) -> None:
        """Check and award study-related achievements."""
        # Check for study time achievements, consistency streaks, etc.
        pass
    
    def _check_test_achievements(self, user_id: str, result: PracticeTestResult) -> None:
        """Check and award test-related achievements."""
        # Check for high scores, improvement, mastery achievements
        pass
    
    def _award_goal_achievement(self, user_id: str, goal: LearningGoal) -> None:
        """Award achievement for completing a goal."""
        achievement = Achievement(
            user_id=user_id,
            achievement_type='goal_completion',
            title=f'Goal Achieved: {goal.title}',
            description=f'Successfully completed the learning goal: {goal.title}',
            criteria_type='goal_completion',
            earned_at=datetime.utcnow(),
            is_earned=True,
            progress_percentage=100.0,
            category='goals',
            learning_goal_id=goal.id
        )
        
        self.db.add(achievement)
        self.db.commit()
    
    def _calculate_study_streak(self, user_id: str) -> int:
        """Calculate current study streak in days."""
        # Implementation would check consecutive days with study sessions
        return 0  # Placeholder
    
    def _calculate_longest_streak(self, user_id: str) -> int:
        """Calculate longest study streak in days."""
        # Implementation would find the longest consecutive study period
        return 0  # Placeholder
    
    def _calculate_overall_progress(self, user_id: str) -> float:
        """Calculate overall learning progress percentage."""
        # Implementation would aggregate progress across all learning paths and goals
        return 0.0  # Placeholder
    
    def _analyze_optimal_study_time(self, sessions: List[StudySession]) -> str:
        """Analyze optimal study time based on session effectiveness."""
        # Implementation would analyze when user is most effective
        return "Morning (9-11 AM)"  # Placeholder
    
    def _recommend_session_length(self, sessions: List[StudySession]) -> int:
        """Recommend optimal session length in minutes."""
        # Implementation would analyze session effectiveness vs duration
        return 45  # Placeholder
    
    def _generate_focus_tips(self, sessions: List[StudySession]) -> List[str]:
        """Generate personalized focus improvement tips."""
        return [
            "Try the Pomodoro Technique with 25-minute focused sessions",
            "Consider studying in a quieter environment",
            "Take short breaks every 30 minutes to maintain focus"
        ]
    
    def _analyze_performance_areas(self, test_results: List[PracticeTestResult]) -> Tuple[List[str], List[str]]:
        """Analyze weak and strong performance areas."""
        weak_areas = []
        strong_areas = []
        
        # Aggregate domain scores across all tests
        domain_totals = {}
        domain_counts = {}
        
        for result in test_results:
            for domain, score in result.domain_scores.items():
                if domain not in domain_totals:
                    domain_totals[domain] = 0
                    domain_counts[domain] = 0
                domain_totals[domain] += score
                domain_counts[domain] += 1
        
        # Calculate averages and categorize
        for domain in domain_totals:
            avg_score = domain_totals[domain] / domain_counts[domain]
            if avg_score < 70:
                weak_areas.append(domain)
            elif avg_score >= 85:
                strong_areas.append(domain)
        
        return weak_areas, strong_areas
    
    def _generate_next_actions(
        self, 
        user_id: str, 
        sessions: List[StudySession], 
        test_results: List[PracticeTestResult]
    ) -> List[str]:
        """Generate personalized next action recommendations."""
        actions = []
        
        # Check recent activity
        if not sessions:
            actions.append("Start your first study session to begin tracking progress")
        elif len(sessions) < 5:
            actions.append("Maintain consistent study schedule with daily sessions")
        
        # Check test performance
        if test_results:
            latest_result = max(test_results, key=lambda x: x.created_at)
            if latest_result.percentage < 70:
                actions.append("Focus on weak areas identified in recent practice tests")
            elif latest_result.percentage >= 85:
                actions.append("Consider taking a full practice exam to assess readiness")
        else:
            actions.append("Take a practice test to establish baseline performance")
        
        return actions
    
    def _generate_learning_analytics(
        self, 
        user_id: str, 
        period_type: str, 
        period_start: datetime, 
        period_end: datetime
    ) -> LearningAnalytics:
        """Generate comprehensive learning analytics for a period."""
        # Get sessions in period
        sessions = self.db.query(StudySession).filter(
            StudySession.user_id == user_id,
            StudySession.started_at >= period_start,
            StudySession.started_at < period_end,
            StudySession.ended_at.isnot(None)
        ).all()
        
        # Get test results in period
        test_results = self.db.query(PracticeTestResult).filter(
            PracticeTestResult.user_id == user_id,
            PracticeTestResult.created_at >= period_start,
            PracticeTestResult.created_at < period_end
        ).all()
        
        # Calculate metrics
        total_study_minutes = sum(s.duration_minutes or 0 for s in sessions)
        sessions_count = len(sessions)
        avg_session_minutes = total_study_minutes / sessions_count if sessions_count > 0 else 0
        
        tests_taken = len(test_results)
        avg_test_score = statistics.mean([r.percentage for r in test_results]) if test_results else 0
        
        # Create analytics record
        analytics = LearningAnalytics(
            user_id=user_id,
            period_type=period_type,
            period_start=period_start,
            period_end=period_end,
            total_study_minutes=total_study_minutes,
            average_session_minutes=avg_session_minutes,
            study_sessions_count=sessions_count,
            practice_tests_taken=tests_taken,
            average_test_score=avg_test_score
        )
        
        self.db.add(analytics)
        self.db.commit()
        
        return analytics
