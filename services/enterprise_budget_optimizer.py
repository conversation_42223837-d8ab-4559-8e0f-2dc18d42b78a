"""Enterprise Budget Optimization Service for Agent 4.

This service provides comprehensive budget optimization for enterprise training programs,
ROI analysis for team certifications, and strategic workforce development planning.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
from collections import defaultdict

from models.enterprise import Enterprise, EnterpriseTeam, EnterpriseUser
from models.certification import Certification
from models.cost_calculation import CostCalculation
from models.career_transition import CareerRole
from services.cost_calculator import CostCalculatorService
from services.salary_intelligence import SalaryIntelligenceService

logger = logging.getLogger(__name__)


@dataclass
class BudgetOptimization:
    """Data class for budget optimization results."""
    total_budget: float
    optimized_allocation: Dict[str, float]
    cost_savings: float
    roi_projections: Dict[str, float]
    recommended_certifications: List[Dict[str, Any]]
    team_priorities: List[Dict[str, Any]]
    timeline_recommendations: Dict[str, Any]
    risk_assessment: Dict[str, Any]


@dataclass
class TeamAnalysis:
    """Data class for team skill analysis."""
    team_id: int
    team_name: str
    current_skills: Dict[str, int]
    skill_gaps: List[str]
    recommended_certifications: List[Dict[str, Any]]
    budget_allocation: float
    expected_roi: float
    priority_score: float


class EnterpriseBudgetOptimizer:
    """Service for enterprise budget optimization and workforce planning."""
    
    def __init__(self, db: Session):
        self.db = db
        self.cost_calculator = CostCalculatorService(db)
        self.salary_intelligence = SalaryIntelligenceService(db)
        
        # Enterprise optimization parameters
        self.optimization_weights = {
            'roi_potential': 0.35,
            'skill_gap_urgency': 0.25,
            'team_size': 0.20,
            'strategic_alignment': 0.20
        }
        
        # Industry benchmarks for optimization
        self.industry_benchmarks = {
            'training_budget_percentage': 0.03,  # 3% of total compensation
            'certification_success_rate': 0.75,
            'average_roi_timeline': 18,  # months
            'skill_gap_tolerance': 0.15  # 15% skill gap threshold
        }
    
    def optimize_enterprise_budget(
        self,
        enterprise_id: int,
        total_budget: float,
        budget_period_months: int = 12,
        strategic_priorities: Optional[List[str]] = None
    ) -> BudgetOptimization:
        """Optimize enterprise training budget allocation."""
        logger.info(f"Optimizing budget for enterprise {enterprise_id}")
        
        # Get enterprise and teams
        enterprise = self.db.query(Enterprise).filter(
            Enterprise.id == enterprise_id
        ).first()
        if not enterprise:
            raise ValueError(f"Enterprise {enterprise_id} not found")
        
        teams = self.db.query(EnterpriseTeam).filter(
            EnterpriseTeam.enterprise_id == enterprise_id,
            EnterpriseTeam.is_active == True
        ).all()
        
        # Analyze each team
        team_analyses = []
        for team in teams:
            analysis = self._analyze_team_needs(team, strategic_priorities)
            team_analyses.append(analysis)
        
        # Optimize budget allocation
        optimized_allocation = self._optimize_budget_allocation(
            team_analyses, total_budget, budget_period_months
        )
        
        # Calculate cost savings and ROI
        cost_savings = self._calculate_cost_savings(team_analyses, optimized_allocation)
        roi_projections = self._calculate_roi_projections(team_analyses, optimized_allocation)
        
        # Generate recommendations
        recommended_certifications = self._generate_certification_recommendations(team_analyses)
        team_priorities = self._prioritize_teams(team_analyses)
        timeline_recommendations = self._generate_timeline_recommendations(
            team_analyses, budget_period_months
        )
        
        # Assess risks
        risk_assessment = self._assess_optimization_risks(
            team_analyses, optimized_allocation, total_budget
        )
        
        return BudgetOptimization(
            total_budget=total_budget,
            optimized_allocation=optimized_allocation,
            cost_savings=cost_savings,
            roi_projections=roi_projections,
            recommended_certifications=recommended_certifications,
            team_priorities=team_priorities,
            timeline_recommendations=timeline_recommendations,
            risk_assessment=risk_assessment
        )
    
    def _analyze_team_needs(
        self, 
        team: EnterpriseTeam, 
        strategic_priorities: Optional[List[str]]
    ) -> TeamAnalysis:
        """Analyze individual team training needs and priorities."""
        # Get team members
        team_members = self.db.query(EnterpriseUser).filter(
            EnterpriseUser.team_id == team.id,
            EnterpriseUser.is_active == True
        ).all()
        
        # Analyze current skills
        current_skills = self._analyze_current_skills(team_members)
        
        # Identify skill gaps
        skill_gaps = self._identify_skill_gaps(team, current_skills, strategic_priorities)
        
        # Recommend certifications
        recommended_certs = self._recommend_team_certifications(
            team, skill_gaps, strategic_priorities
        )
        
        # Calculate budget needs
        budget_allocation = self._calculate_team_budget_needs(recommended_certs, len(team_members))
        
        # Calculate expected ROI
        expected_roi = self._calculate_team_roi(team, recommended_certs, len(team_members))
        
        # Calculate priority score
        priority_score = self._calculate_team_priority(
            team, skill_gaps, len(team_members), strategic_priorities
        )
        
        return TeamAnalysis(
            team_id=team.id,
            team_name=team.name,
            current_skills=current_skills,
            skill_gaps=skill_gaps,
            recommended_certifications=recommended_certs,
            budget_allocation=budget_allocation,
            expected_roi=expected_roi,
            priority_score=priority_score
        )
    
    def _analyze_current_skills(self, team_members: List[EnterpriseUser]) -> Dict[str, int]:
        """Analyze current skill distribution in the team."""
        skills = defaultdict(int)
        
        for member in team_members:
            # Get user's certifications
            if member.certifications:
                for cert_id in member.certifications:
                    cert = self.db.query(Certification).filter(
                        Certification.id == cert_id
                    ).first()
                    if cert and cert.domain:
                        skills[cert.domain] += 1
            
            # Get user's role skills
            if member.role_id:
                role = self.db.query(CareerRole).filter(
                    CareerRole.id == member.role_id
                ).first()
                if role and role.domain:
                    skills[role.domain] += 1
        
        return dict(skills)
    
    def _identify_skill_gaps(
        self, 
        team: EnterpriseTeam, 
        current_skills: Dict[str, int],
        strategic_priorities: Optional[List[str]]
    ) -> List[str]:
        """Identify critical skill gaps in the team."""
        gaps = []
        
        # Check against team requirements
        if team.required_skills:
            for skill in team.required_skills:
                current_count = current_skills.get(skill, 0)
                if current_count < team.target_team_size * 0.5:  # Less than 50% coverage
                    gaps.append(skill)
        
        # Check against strategic priorities
        if strategic_priorities:
            for priority in strategic_priorities:
                if priority not in current_skills or current_skills[priority] < 2:
                    gaps.append(priority)
        
        return list(set(gaps))  # Remove duplicates
    
    def _recommend_team_certifications(
        self,
        team: EnterpriseTeam,
        skill_gaps: List[str],
        strategic_priorities: Optional[List[str]]
    ) -> List[Dict[str, Any]]:
        """Recommend certifications for the team based on gaps and priorities."""
        recommendations = []
        
        # Get relevant certifications for skill gaps
        for gap in skill_gaps:
            certs = self.db.query(Certification).filter(
                Certification.domain.ilike(f"%{gap}%"),
                Certification.is_active == True
            ).all()
            
            for cert in certs[:3]:  # Top 3 per gap
                # Calculate ROI for this certification
                roi_analysis = self.salary_intelligence.get_certification_salary_impact(cert.id)
                
                recommendations.append({
                    'certification_id': cert.id,
                    'certification_name': cert.name,
                    'skill_gap': gap,
                    'estimated_cost': cert.cost or 2000,
                    'expected_roi': roi_analysis.get('average_salary_premium', 8.0),
                    'market_demand': roi_analysis.get('market_demand', 'medium'),
                    'priority': 'high' if gap in (strategic_priorities or []) else 'medium',
                    'recommended_team_members': min(team.target_team_size, 5)
                })
        
        # Sort by ROI and priority
        recommendations.sort(
            key=lambda x: (
                1 if x['priority'] == 'high' else 0,
                x['expected_roi']
            ),
            reverse=True
        )
        
        return recommendations[:10]  # Top 10 recommendations
    
    def _calculate_team_budget_needs(
        self, 
        recommended_certs: List[Dict[str, Any]], 
        team_size: int
    ) -> float:
        """Calculate budget needs for team certification recommendations."""
        total_budget = 0.0
        
        for cert in recommended_certs:
            cert_cost = cert['estimated_cost']
            team_members = cert['recommended_team_members']
            total_budget += cert_cost * team_members
        
        return total_budget
    
    def _calculate_team_roi(
        self,
        team: EnterpriseTeam,
        recommended_certs: List[Dict[str, Any]],
        team_size: int
    ) -> float:
        """Calculate expected ROI for team certification investments."""
        total_investment = 0.0
        total_return = 0.0
        
        # Estimate average team salary
        avg_salary = 100000  # Default estimate
        if team.budget_allocated and team.target_team_size:
            avg_salary = (team.budget_allocated * 0.8) / team.target_team_size  # 80% for salaries
        
        for cert in recommended_certs:
            investment = cert['estimated_cost'] * cert['recommended_team_members']
            roi_percentage = cert['expected_roi'] / 100
            annual_return = avg_salary * roi_percentage * cert['recommended_team_members']
            
            total_investment += investment
            total_return += annual_return
        
        return (total_return / total_investment) * 100 if total_investment > 0 else 0.0

    def _calculate_team_priority(
        self,
        team: EnterpriseTeam,
        skill_gaps: List[str],
        team_size: int,
        strategic_priorities: Optional[List[str]]
    ) -> float:
        """Calculate priority score for team training investment."""
        score = 0.0

        # Skill gap urgency (0-40 points)
        gap_score = min(len(skill_gaps) * 10, 40)
        score += gap_score

        # Team size impact (0-30 points)
        size_score = min(team_size * 3, 30)
        score += size_score

        # Strategic alignment (0-30 points)
        if strategic_priorities:
            strategic_gaps = [gap for gap in skill_gaps if gap in strategic_priorities]
            strategic_score = min(len(strategic_gaps) * 15, 30)
            score += strategic_score

        return min(score, 100.0)

    def _optimize_budget_allocation(
        self,
        team_analyses: List[TeamAnalysis],
        total_budget: float,
        budget_period_months: int
    ) -> Dict[str, float]:
        """Optimize budget allocation across teams using weighted scoring."""
        if not team_analyses:
            return {}

        # Calculate total priority score
        total_priority = sum(analysis.priority_score for analysis in team_analyses)

        allocation = {}
        remaining_budget = total_budget

        # Sort teams by priority score
        sorted_teams = sorted(team_analyses, key=lambda x: x.priority_score, reverse=True)

        for analysis in sorted_teams:
            if total_priority > 0:
                # Base allocation by priority
                priority_allocation = (analysis.priority_score / total_priority) * total_budget

                # Adjust by ROI potential
                roi_multiplier = min(analysis.expected_roi / 100, 2.0)  # Cap at 2x
                adjusted_allocation = priority_allocation * (0.7 + 0.3 * roi_multiplier)

                # Ensure we don't exceed team's actual needs
                final_allocation = min(adjusted_allocation, analysis.budget_allocation, remaining_budget)

                allocation[analysis.team_name] = final_allocation
                remaining_budget -= final_allocation

        # Distribute any remaining budget proportionally
        if remaining_budget > 0 and allocation:
            total_allocated = sum(allocation.values())
            for team_name in allocation:
                additional = (allocation[team_name] / total_allocated) * remaining_budget
                allocation[team_name] += additional

        return allocation

    def _calculate_cost_savings(
        self,
        team_analyses: List[TeamAnalysis],
        optimized_allocation: Dict[str, float]
    ) -> float:
        """Calculate cost savings from optimization."""
        total_requested = sum(analysis.budget_allocation for analysis in team_analyses)
        total_allocated = sum(optimized_allocation.values())

        # Savings from bulk purchasing and strategic timing
        bulk_savings = total_allocated * 0.15  # 15% bulk discount

        # Savings from avoiding redundant training
        redundancy_savings = total_allocated * 0.08  # 8% redundancy reduction

        return bulk_savings + redundancy_savings

    def _calculate_roi_projections(
        self,
        team_analyses: List[TeamAnalysis],
        optimized_allocation: Dict[str, float]
    ) -> Dict[str, float]:
        """Calculate ROI projections for the optimized allocation."""
        total_investment = sum(optimized_allocation.values())

        # Calculate weighted average ROI
        weighted_roi = 0.0
        total_weight = 0.0

        for analysis in team_analyses:
            team_allocation = optimized_allocation.get(analysis.team_name, 0.0)
            if team_allocation > 0:
                weight = team_allocation / total_investment
                weighted_roi += analysis.expected_roi * weight
                total_weight += weight

        avg_roi = weighted_roi / total_weight if total_weight > 0 else 0.0

        return {
            'one_year_roi': avg_roi,
            'three_year_roi': avg_roi * 2.5,  # Compounding effect
            'five_year_roi': avg_roi * 4.0,
            'break_even_months': 12 / (avg_roi / 100) if avg_roi > 0 else 999
        }

    def _generate_certification_recommendations(
        self,
        team_analyses: List[TeamAnalysis]
    ) -> List[Dict[str, Any]]:
        """Generate enterprise-wide certification recommendations."""
        all_recommendations = []

        for analysis in team_analyses:
            for cert in analysis.recommended_certifications:
                cert_copy = cert.copy()
                cert_copy['team_name'] = analysis.team_name
                cert_copy['team_priority'] = analysis.priority_score
                all_recommendations.append(cert_copy)

        # Consolidate and rank recommendations
        cert_consolidation = defaultdict(list)
        for rec in all_recommendations:
            key = rec['certification_name']
            cert_consolidation[key].append(rec)

        consolidated_recommendations = []
        for cert_name, recs in cert_consolidation.items():
            total_members = sum(r['recommended_team_members'] for r in recs)
            avg_roi = sum(r['expected_roi'] for r in recs) / len(recs)
            total_cost = sum(r['estimated_cost'] * r['recommended_team_members'] for r in recs)

            consolidated_recommendations.append({
                'certification_name': cert_name,
                'total_recommended_members': total_members,
                'average_roi': avg_roi,
                'total_investment': total_cost,
                'teams_interested': len(recs),
                'priority': 'high' if avg_roi > 12 and total_members > 5 else 'medium'
            })

        # Sort by total impact (ROI * members)
        consolidated_recommendations.sort(
            key=lambda x: x['average_roi'] * x['total_recommended_members'],
            reverse=True
        )

        return consolidated_recommendations[:15]  # Top 15 enterprise recommendations

    def _prioritize_teams(self, team_analyses: List[TeamAnalysis]) -> List[Dict[str, Any]]:
        """Prioritize teams for training investment."""
        priorities = []

        for analysis in team_analyses:
            priorities.append({
                'team_name': analysis.team_name,
                'priority_score': analysis.priority_score,
                'skill_gaps_count': len(analysis.skill_gaps),
                'expected_roi': analysis.expected_roi,
                'budget_needed': analysis.budget_allocation,
                'urgency': 'high' if analysis.priority_score > 70 else 'medium' if analysis.priority_score > 40 else 'low',
                'key_skill_gaps': analysis.skill_gaps[:3]  # Top 3 gaps
            })

        priorities.sort(key=lambda x: x['priority_score'], reverse=True)
        return priorities

    def _generate_timeline_recommendations(
        self,
        team_analyses: List[TeamAnalysis],
        budget_period_months: int
    ) -> Dict[str, Any]:
        """Generate timeline recommendations for training rollout."""
        timeline = {
            'immediate_actions': [],  # 0-3 months
            'short_term_goals': [],   # 3-6 months
            'medium_term_goals': [],  # 6-12 months
            'long_term_goals': []     # 12+ months
        }

        # Sort teams by priority
        sorted_teams = sorted(team_analyses, key=lambda x: x.priority_score, reverse=True)

        for i, analysis in enumerate(sorted_teams):
            if i < 2:  # Top 2 priority teams
                timeline['immediate_actions'].append({
                    'team': analysis.team_name,
                    'action': f'Begin certification training for {analysis.skill_gaps[0] if analysis.skill_gaps else "core skills"}',
                    'budget': analysis.budget_allocation * 0.4
                })
            elif i < 5:  # Next 3 teams
                timeline['short_term_goals'].append({
                    'team': analysis.team_name,
                    'action': f'Start skill gap assessment and training planning',
                    'budget': analysis.budget_allocation * 0.6
                })
            elif i < 8:  # Next 3 teams
                timeline['medium_term_goals'].append({
                    'team': analysis.team_name,
                    'action': f'Implement comprehensive training program',
                    'budget': analysis.budget_allocation
                })
            else:  # Remaining teams
                timeline['long_term_goals'].append({
                    'team': analysis.team_name,
                    'action': f'Evaluate and plan future training needs',
                    'budget': analysis.budget_allocation
                })

        return timeline

    def _assess_optimization_risks(
        self,
        team_analyses: List[TeamAnalysis],
        optimized_allocation: Dict[str, float],
        total_budget: float
    ) -> Dict[str, Any]:
        """Assess risks associated with the budget optimization."""
        risks = {
            'high_risk_factors': [],
            'medium_risk_factors': [],
            'low_risk_factors': [],
            'mitigation_strategies': [],
            'overall_risk_score': 0.0
        }

        risk_score = 0.0

        # Budget concentration risk
        max_allocation = max(optimized_allocation.values()) if optimized_allocation else 0
        if max_allocation > total_budget * 0.4:  # More than 40% to one team
            risks['high_risk_factors'].append('High budget concentration in single team')
            risk_score += 20

        # Skill gap coverage risk
        total_gaps = sum(len(analysis.skill_gaps) for analysis in team_analyses)
        covered_teams = len([team for team, budget in optimized_allocation.items() if budget > 0])
        coverage_ratio = covered_teams / len(team_analyses) if team_analyses else 0

        if coverage_ratio < 0.6:  # Less than 60% team coverage
            risks['medium_risk_factors'].append('Limited team coverage may leave skill gaps unaddressed')
            risk_score += 15

        # ROI realization risk
        avg_roi = sum(analysis.expected_roi for analysis in team_analyses) / len(team_analyses) if team_analyses else 0
        if avg_roi < 15:  # Less than 15% ROI
            risks['medium_risk_factors'].append('Lower than expected ROI may impact business case')
            risk_score += 10

        # Market volatility risk
        risks['low_risk_factors'].append('Technology evolution may affect certification value')
        risk_score += 5

        # Generate mitigation strategies
        if risk_score > 30:
            risks['mitigation_strategies'].extend([
                'Implement phased rollout to reduce concentration risk',
                'Establish regular ROI monitoring and adjustment mechanisms',
                'Create contingency budget for unexpected training needs'
            ])
        elif risk_score > 15:
            risks['mitigation_strategies'].extend([
                'Monitor team progress and adjust allocations quarterly',
                'Establish cross-team knowledge sharing programs'
            ])
        else:
            risks['mitigation_strategies'].append('Continue with planned optimization strategy')

        risks['overall_risk_score'] = min(risk_score, 100.0)

        return risks

    def generate_budget_report(
        self,
        enterprise_id: int,
        optimization_result: BudgetOptimization
    ) -> Dict[str, Any]:
        """Generate comprehensive budget optimization report."""
        enterprise = self.db.query(Enterprise).filter(
            Enterprise.id == enterprise_id
        ).first()

        report = {
            'enterprise_name': enterprise.name if enterprise else 'Unknown',
            'report_date': datetime.utcnow().isoformat(),
            'executive_summary': {
                'total_budget': optimization_result.total_budget,
                'projected_cost_savings': optimization_result.cost_savings,
                'expected_roi': optimization_result.roi_projections.get('one_year_roi', 0),
                'teams_covered': len(optimization_result.optimized_allocation),
                'top_certification_recommendation': optimization_result.recommended_certifications[0]['certification_name'] if optimization_result.recommended_certifications else 'None'
            },
            'detailed_allocation': optimization_result.optimized_allocation,
            'roi_analysis': optimization_result.roi_projections,
            'certification_recommendations': optimization_result.recommended_certifications,
            'team_priorities': optimization_result.team_priorities,
            'implementation_timeline': optimization_result.timeline_recommendations,
            'risk_assessment': optimization_result.risk_assessment,
            'key_insights': self._generate_key_insights(optimization_result),
            'next_steps': self._generate_next_steps(optimization_result)
        }

        return report

    def _generate_key_insights(self, optimization_result: BudgetOptimization) -> List[str]:
        """Generate key insights from the optimization analysis."""
        insights = []

        # Budget efficiency insight
        if optimization_result.cost_savings > optimization_result.total_budget * 0.1:
            insights.append(f"Optimization can save {optimization_result.cost_savings:,.0f} (>10% of budget) through strategic planning")

        # ROI insight
        one_year_roi = optimization_result.roi_projections.get('one_year_roi', 0)
        if one_year_roi > 20:
            insights.append(f"Strong ROI potential of {one_year_roi:.1f}% indicates excellent investment opportunity")

        # Team priority insight
        if optimization_result.team_priorities:
            high_priority_teams = [t for t in optimization_result.team_priorities if t['urgency'] == 'high']
            if high_priority_teams:
                insights.append(f"{len(high_priority_teams)} teams require immediate attention due to critical skill gaps")

        # Certification insight
        if optimization_result.recommended_certifications:
            top_cert = optimization_result.recommended_certifications[0]
            insights.append(f"{top_cert['certification_name']} offers the highest enterprise-wide impact")

        return insights

    def _generate_next_steps(self, optimization_result: BudgetOptimization) -> List[str]:
        """Generate recommended next steps."""
        next_steps = []

        # Immediate actions
        immediate_actions = optimization_result.timeline_recommendations.get('immediate_actions', [])
        if immediate_actions:
            next_steps.append(f"Begin with {immediate_actions[0]['team']} team training within 30 days")

        # Budget approval
        next_steps.append("Present optimization plan to leadership for budget approval")

        # Implementation planning
        next_steps.append("Develop detailed implementation timeline with team leads")

        # Monitoring setup
        next_steps.append("Establish ROI tracking and progress monitoring systems")

        # Risk mitigation
        if optimization_result.risk_assessment['overall_risk_score'] > 30:
            next_steps.append("Implement risk mitigation strategies before full rollout")

        return next_steps
