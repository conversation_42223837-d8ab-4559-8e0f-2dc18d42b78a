"""Enterprise Authentication and Authorization Service.

This service provides comprehensive enterprise-grade authentication including
SSO integration, RBAC, multi-tenant security, and advanced access controls.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import jwt
import hashlib
import secrets
from enum import Enum

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserRole
from models.compliance import AuditLog, AuditEventType, RiskLevel
from services.audit_service import AuditService

logger = logging.getLogger(__name__)


class AuthenticationMethod(str, Enum):
    """Supported authentication methods."""
    PASSWORD = "password"
    SSO_SAML = "sso_saml"
    SSO_OIDC = "sso_oidc"
    SSO_OAUTH2 = "sso_oauth2"
    LDAP = "ldap"
    ACTIVE_DIRECTORY = "active_directory"
    MFA = "mfa"


class Permission(str, Enum):
    """System permissions for RBAC."""
    # Organization management
    ORG_ADMIN = "org_admin"
    ORG_VIEW = "org_view"
    ORG_EDIT = "org_edit"
    
    # User management
    USER_ADMIN = "user_admin"
    USER_VIEW = "user_view"
    USER_EDIT = "user_edit"
    USER_INVITE = "user_invite"
    
    # Department management
    DEPT_ADMIN = "dept_admin"
    DEPT_VIEW = "dept_view"
    DEPT_EDIT = "dept_edit"
    
    # Budget management
    BUDGET_ADMIN = "budget_admin"
    BUDGET_VIEW = "budget_view"
    BUDGET_APPROVE = "budget_approve"
    
    # Compliance management
    COMPLIANCE_ADMIN = "compliance_admin"
    COMPLIANCE_VIEW = "compliance_view"
    COMPLIANCE_REPORT = "compliance_report"
    
    # Analytics and reporting
    ANALYTICS_ADMIN = "analytics_admin"
    ANALYTICS_VIEW = "analytics_view"
    ANALYTICS_EXPORT = "analytics_export"
    
    # Audit and security
    AUDIT_ADMIN = "audit_admin"
    AUDIT_VIEW = "audit_view"
    SECURITY_ADMIN = "security_admin"


class EnterpriseAuthService:
    """Service for enterprise authentication and authorization."""
    
    def __init__(self, db: Session):
        self.db = db
        self.audit_service = AuditService(db)
        self.jwt_secret = self._get_jwt_secret()
        self.jwt_algorithm = "HS256"
        self.token_expiry_hours = 8
        
        # Role-based permissions mapping
        self.role_permissions = self._initialize_role_permissions()
    
    def _get_jwt_secret(self) -> str:
        """Get JWT secret key (should be from environment/config)."""
        # TODO: Get from secure configuration
        return "your-super-secret-jwt-key-change-in-production"
    
    def _initialize_role_permissions(self) -> Dict[UserRole, Set[Permission]]:
        """Initialize role-based permissions mapping."""
        return {
            UserRole.SUPER_ADMIN: {
                Permission.ORG_ADMIN, Permission.USER_ADMIN, Permission.DEPT_ADMIN,
                Permission.BUDGET_ADMIN, Permission.COMPLIANCE_ADMIN, Permission.ANALYTICS_ADMIN,
                Permission.AUDIT_ADMIN, Permission.SECURITY_ADMIN
            },
            UserRole.ORG_ADMIN: {
                Permission.ORG_VIEW, Permission.ORG_EDIT, Permission.USER_ADMIN,
                Permission.DEPT_ADMIN, Permission.BUDGET_ADMIN, Permission.COMPLIANCE_ADMIN,
                Permission.ANALYTICS_ADMIN, Permission.AUDIT_VIEW
            },
            UserRole.MANAGER: {
                Permission.ORG_VIEW, Permission.USER_VIEW, Permission.USER_EDIT,
                Permission.DEPT_VIEW, Permission.DEPT_EDIT, Permission.BUDGET_VIEW,
                Permission.BUDGET_APPROVE, Permission.COMPLIANCE_VIEW, Permission.ANALYTICS_VIEW
            },
            UserRole.TEAM_LEAD: {
                Permission.ORG_VIEW, Permission.USER_VIEW, Permission.DEPT_VIEW,
                Permission.BUDGET_VIEW, Permission.COMPLIANCE_VIEW, Permission.ANALYTICS_VIEW
            },
            UserRole.LEARNER: {
                Permission.ORG_VIEW, Permission.USER_VIEW, Permission.COMPLIANCE_VIEW
            },
            UserRole.COMPLIANCE_OFFICER: {
                Permission.ORG_VIEW, Permission.USER_VIEW, Permission.COMPLIANCE_ADMIN,
                Permission.AUDIT_VIEW, Permission.ANALYTICS_VIEW
            },
            UserRole.AUDITOR: {
                Permission.ORG_VIEW, Permission.USER_VIEW, Permission.COMPLIANCE_VIEW,
                Permission.AUDIT_VIEW, Permission.ANALYTICS_VIEW
            }
        }
    
    # Authentication Methods
    
    def authenticate_user(
        self, 
        email: str, 
        password: str = None,
        auth_method: AuthenticationMethod = AuthenticationMethod.PASSWORD,
        sso_token: str = None,
        organization_domain: str = None
    ) -> Dict[str, Any]:
        """Authenticate user with various methods."""
        try:
            # Get user by email
            user = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.email == email,
                EnterpriseUser.is_active == True,
                EnterpriseUser.deleted_at.is_(None)
            ).first()
            
            if not user:
                self._log_failed_authentication(email, "user_not_found", auth_method)
                raise ValueError("Invalid credentials")
            
            # Check organization context if provided
            if organization_domain:
                org = self.db.query(EnterpriseOrganization).filter(
                    EnterpriseOrganization.domain == organization_domain,
                    EnterpriseOrganization.is_active == True
                ).first()
                
                if not org or user.organization_id != org.id:
                    self._log_failed_authentication(email, "organization_mismatch", auth_method)
                    raise ValueError("Invalid organization context")
            
            # Authenticate based on method
            if auth_method == AuthenticationMethod.PASSWORD:
                if not self._verify_password(password, user.password_hash):
                    self._log_failed_authentication(email, "invalid_password", auth_method)
                    raise ValueError("Invalid credentials")
            
            elif auth_method in [AuthenticationMethod.SSO_SAML, AuthenticationMethod.SSO_OIDC]:
                if not self._verify_sso_token(sso_token, user.organization_id):
                    self._log_failed_authentication(email, "invalid_sso_token", auth_method)
                    raise ValueError("Invalid SSO token")
            
            # Generate access token
            access_token = self._generate_access_token(user)
            
            # Update last login
            user.last_login = datetime.utcnow()
            self.db.commit()
            
            # Log successful authentication
            self.audit_service.log_event(
                organization_id=user.organization_id,
                event_type=AuditEventType.USER_LOGIN,
                description=f"User authenticated successfully: {auth_method.value}",
                user_id=user.user_id,
                additional_data={'auth_method': auth_method.value}
            )
            
            return {
                'access_token': access_token,
                'token_type': 'bearer',
                'expires_in': self.token_expiry_hours * 3600,
                'user': self._serialize_user(user),
                'permissions': list(self._get_user_permissions(user)),
                'organization': self._serialize_organization(user.organization) if user.organization else None
            }
            
        except Exception as e:
            logger.error(f"Authentication error for {email}: {e}")
            raise
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash."""
        # TODO: Implement proper password hashing (bcrypt, scrypt, etc.)
        return hashlib.sha256(password.encode()).hexdigest() == password_hash
    
    def _verify_sso_token(self, token: str, organization_id: int) -> bool:
        """Verify SSO token."""
        # TODO: Implement SSO token verification
        # This would validate SAML assertions, OIDC tokens, etc.
        return True  # Placeholder
    
    def _generate_access_token(self, user: EnterpriseUser) -> str:
        """Generate JWT access token."""
        payload = {
            'user_id': user.user_id,
            'email': user.email,
            'organization_id': user.organization_id,
            'role': user.role.value,
            'permissions': list(self._get_user_permissions(user)),
            'exp': datetime.utcnow() + timedelta(hours=self.token_expiry_hours),
            'iat': datetime.utcnow(),
            'iss': 'certpathfinder-enterprise'
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    def verify_access_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode access token."""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            # Verify user still exists and is active
            user = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.user_id == payload['user_id'],
                EnterpriseUser.is_active == True,
                EnterpriseUser.deleted_at.is_(None)
            ).first()
            
            if not user:
                raise ValueError("User no longer exists or is inactive")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise ValueError("Token has expired")
        except jwt.InvalidTokenError:
            raise ValueError("Invalid token")
    
    # Authorization Methods
    
    def check_permission(
        self, 
        user_id: str, 
        permission: Permission,
        organization_id: int = None,
        resource_id: str = None
    ) -> bool:
        """Check if user has specific permission."""
        try:
            user = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.user_id == user_id,
                EnterpriseUser.is_active == True,
                EnterpriseUser.deleted_at.is_(None)
            ).first()
            
            if not user:
                return False
            
            # Check organization context
            if organization_id and user.organization_id != organization_id:
                return False
            
            # Get user permissions
            user_permissions = self._get_user_permissions(user)
            
            # Check if user has the required permission
            has_permission = permission in user_permissions
            
            # Additional resource-level checks
            if has_permission and resource_id:
                has_permission = self._check_resource_access(user, permission, resource_id)
            
            # Log permission check for audit
            if not has_permission:
                self.audit_service.log_event(
                    organization_id=user.organization_id,
                    event_type=AuditEventType.PERMISSION_CHANGE,
                    description=f"Permission denied: {permission.value}",
                    user_id=user_id,
                    risk_level=RiskLevel.MEDIUM,
                    additional_data={
                        'permission': permission.value,
                        'resource_id': resource_id
                    }
                )
            
            return has_permission
            
        except Exception as e:
            logger.error(f"Permission check error for {user_id}: {e}")
            return False
    
    def _get_user_permissions(self, user: EnterpriseUser) -> Set[Permission]:
        """Get all permissions for a user."""
        # Base permissions from role
        permissions = self.role_permissions.get(user.role, set())
        
        # Additional permissions from user.permissions field
        if user.permissions:
            additional_perms = user.permissions.get('additional_permissions', [])
            for perm_str in additional_perms:
                try:
                    permissions.add(Permission(perm_str))
                except ValueError:
                    logger.warning(f"Invalid permission: {perm_str}")
        
        return permissions
    
    def _check_resource_access(
        self, 
        user: EnterpriseUser, 
        permission: Permission, 
        resource_id: str
    ) -> bool:
        """Check resource-level access permissions."""
        # Implement resource-level access control
        # This would check if user can access specific resources
        # based on department, team membership, etc.
        
        # For now, allow access if user has the base permission
        return True
    
    # Multi-tenant Security
    
    def enforce_tenant_isolation(
        self, 
        user_id: str, 
        requested_org_id: int
    ) -> bool:
        """Enforce multi-tenant data isolation."""
        user = self.db.query(EnterpriseUser).filter(
            EnterpriseUser.user_id == user_id,
            EnterpriseUser.is_active == True
        ).first()
        
        if not user:
            return False
        
        # Super admins can access any organization
        if user.role == UserRole.SUPER_ADMIN:
            return True
        
        # Regular users can only access their own organization
        return user.organization_id == requested_org_id
    
    def get_user_organization_context(self, user_id: str) -> Optional[int]:
        """Get user's organization context."""
        user = self.db.query(EnterpriseUser).filter(
            EnterpriseUser.user_id == user_id,
            EnterpriseUser.is_active == True
        ).first()
        
        return user.organization_id if user else None
    
    # Utility Methods
    
    def _log_failed_authentication(
        self, 
        email: str, 
        reason: str, 
        auth_method: AuthenticationMethod
    ):
        """Log failed authentication attempt."""
        self.audit_service.log_event(
            organization_id=None,
            event_type=AuditEventType.USER_LOGIN,
            description=f"Failed authentication: {reason}",
            user_id=email,
            risk_level=RiskLevel.MEDIUM,
            additional_data={
                'auth_method': auth_method.value,
                'failure_reason': reason
            }
        )
    
    def _serialize_user(self, user: EnterpriseUser) -> Dict[str, Any]:
        """Serialize user for API response."""
        return {
            'user_id': user.user_id,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'role': user.role.value,
            'department_id': user.department_id,
            'job_title': user.job_title,
            'last_login': user.last_login.isoformat() if user.last_login else None
        }
    
    def _serialize_organization(self, org: EnterpriseOrganization) -> Dict[str, Any]:
        """Serialize organization for API response."""
        return {
            'id': org.id,
            'name': org.name,
            'domain': org.domain,
            'subscription_tier': org.subscription_tier.value,
            'features_enabled': org.features_enabled or {}
        }
