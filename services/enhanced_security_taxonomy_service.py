"""Enhanced Security Taxonomy Service.

This service manages the comprehensive security taxonomy including skills,
certifications, job titles, and technologies based on real-world market data.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime, timedelta

from models.enhanced_security_taxonomy import (
    SecuritySkillTaxonomy, SecurityCertificationTaxonomy,
    JobTitleTaxonomy, SecurityTechnologyTaxonomy,
    SecurityDomain, SkillCategory, SkillLevel, CertificationLevel
)
from schemas.enhanced_security_taxonomy import (
    SecuritySkillTaxonomyCreate, SecuritySkillTaxonomyResponse,
    SecurityCertificationTaxonomyCreate, SecurityCertificationTaxonomyResponse,
    JobTitleTaxonomyCreate, JobTitleTaxonomyResponse,
    SecurityTechnologyTaxonomyCreate, SecurityTechnologyTaxonomyResponse,
    TaxonomyFilter, TaxonomyAnalytics, UserSkillAssessment,
    UserCertificationGoal, UserCareerGoal, TaxonomyRecommendation
)

logger = logging.getLogger(__name__)


class EnhancedSecurityTaxonomyService:
    """Service for managing enhanced security taxonomy data."""
    
    def __init__(self, db: Session):
        self.db = db
    
    # Skills Management
    
    def create_skill(self, skill_data: SecuritySkillTaxonomyCreate) -> SecuritySkillTaxonomyResponse:
        """Create a new security skill in the taxonomy."""
        try:
            skill = SecuritySkillTaxonomy(**skill_data.dict())
            self.db.add(skill)
            self.db.commit()
            self.db.refresh(skill)
            
            logger.info(f"Created skill: {skill.skill_name}")
            return SecuritySkillTaxonomyResponse.from_orm(skill)
            
        except Exception as e:
            logger.error(f"Error creating skill: {e}")
            self.db.rollback()
            raise
    
    def get_skills_by_domain(self, domain: SecurityDomain) -> List[SecuritySkillTaxonomyResponse]:
        """Get all skills for a specific security domain."""
        try:
            skills = self.db.query(SecuritySkillTaxonomy).filter(
                SecuritySkillTaxonomy.security_domains.contains([domain.value])
            ).all()
            
            return [SecuritySkillTaxonomyResponse.from_orm(skill) for skill in skills]
            
        except Exception as e:
            logger.error(f"Error getting skills by domain: {e}")
            raise
    
    def get_trending_skills(self, limit: int = 10) -> List[SecuritySkillTaxonomyResponse]:
        """Get trending skills based on market data."""
        try:
            skills = self.db.query(SecuritySkillTaxonomy).filter(
                SecuritySkillTaxonomy.is_trending == True
            ).order_by(SecuritySkillTaxonomy.demand_score.desc()).limit(limit).all()
            
            return [SecuritySkillTaxonomyResponse.from_orm(skill) for skill in skills]
            
        except Exception as e:
            logger.error(f"Error getting trending skills: {e}")
            raise
    
    def get_high_impact_skills(self, min_salary_impact: float = 10.0) -> List[SecuritySkillTaxonomyResponse]:
        """Get skills with high salary impact."""
        try:
            skills = self.db.query(SecuritySkillTaxonomy).filter(
                SecuritySkillTaxonomy.salary_impact >= min_salary_impact
            ).order_by(SecuritySkillTaxonomy.salary_impact.desc()).all()
            
            return [SecuritySkillTaxonomyResponse.from_orm(skill) for skill in skills]
            
        except Exception as e:
            logger.error(f"Error getting high impact skills: {e}")
            raise
    
    # Certifications Management
    
    def create_certification(self, cert_data: SecurityCertificationTaxonomyCreate) -> SecurityCertificationTaxonomyResponse:
        """Create a new security certification in the taxonomy."""
        try:
            certification = SecurityCertificationTaxonomy(**cert_data.dict())
            self.db.add(certification)
            self.db.commit()
            self.db.refresh(certification)
            
            logger.info(f"Created certification: {certification.certification_name}")
            return SecurityCertificationTaxonomyResponse.from_orm(certification)
            
        except Exception as e:
            logger.error(f"Error creating certification: {e}")
            self.db.rollback()
            raise
    
    def get_certifications_by_level(self, level: CertificationLevel) -> List[SecurityCertificationTaxonomyResponse]:
        """Get certifications by difficulty level."""
        try:
            certifications = self.db.query(SecurityCertificationTaxonomy).filter(
                SecurityCertificationTaxonomy.certification_level == level.value
            ).all()
            
            return [SecurityCertificationTaxonomyResponse.from_orm(cert) for cert in certifications]
            
        except Exception as e:
            logger.error(f"Error getting certifications by level: {e}")
            raise
    
    def get_high_value_certifications(self, min_salary_premium: float = 5000.0) -> List[SecurityCertificationTaxonomyResponse]:
        """Get certifications with high salary premium."""
        try:
            certifications = self.db.query(SecurityCertificationTaxonomy).filter(
                SecurityCertificationTaxonomy.salary_premium >= min_salary_premium
            ).order_by(SecurityCertificationTaxonomy.salary_premium.desc()).all()
            
            return [SecurityCertificationTaxonomyResponse.from_orm(cert) for cert in certifications]
            
        except Exception as e:
            logger.error(f"Error getting high value certifications: {e}")
            raise
    
    def get_certification_progression_path(self, current_cert_id: int) -> List[SecurityCertificationTaxonomyResponse]:
        """Get logical next certifications based on current certification."""
        try:
            current_cert = self.db.query(SecurityCertificationTaxonomy).filter(
                SecurityCertificationTaxonomy.id == current_cert_id
            ).first()
            
            if not current_cert:
                return []
            
            # Find certifications that list this one as a prerequisite
            next_certs = self.db.query(SecurityCertificationTaxonomy).filter(
                SecurityCertificationTaxonomy.prerequisites.contains([current_cert.certification_code])
            ).all()
            
            return [SecurityCertificationTaxonomyResponse.from_orm(cert) for cert in next_certs]
            
        except Exception as e:
            logger.error(f"Error getting certification progression path: {e}")
            raise
    
    # Job Titles Management
    
    def create_job_title(self, job_data: JobTitleTaxonomyCreate) -> JobTitleTaxonomyResponse:
        """Create a new job title in the taxonomy."""
        try:
            job_title = JobTitleTaxonomy(**job_data.dict())
            self.db.add(job_title)
            self.db.commit()
            self.db.refresh(job_title)
            
            logger.info(f"Created job title: {job_title.job_title}")
            return JobTitleTaxonomyResponse.from_orm(job_title)
            
        except Exception as e:
            logger.error(f"Error creating job title: {e}")
            self.db.rollback()
            raise
    
    def get_job_titles_by_domain(self, domain: SecurityDomain) -> List[JobTitleTaxonomyResponse]:
        """Get job titles for a specific security domain."""
        try:
            job_titles = self.db.query(JobTitleTaxonomy).filter(
                JobTitleTaxonomy.security_domain == domain.value
            ).all()
            
            return [JobTitleTaxonomyResponse.from_orm(job) for job in job_titles]
            
        except Exception as e:
            logger.error(f"Error getting job titles by domain: {e}")
            raise
    
    def get_high_demand_roles(self, min_posting_frequency: int = 100) -> List[JobTitleTaxonomyResponse]:
        """Get high-demand job roles based on posting frequency."""
        try:
            job_titles = self.db.query(JobTitleTaxonomy).filter(
                JobTitleTaxonomy.job_posting_frequency >= min_posting_frequency
            ).order_by(JobTitleTaxonomy.job_posting_frequency.desc()).all()
            
            return [JobTitleTaxonomyResponse.from_orm(job) for job in job_titles]
            
        except Exception as e:
            logger.error(f"Error getting high demand roles: {e}")
            raise
    
    def get_career_progression_path(self, current_job_id: int) -> Dict[str, List[JobTitleTaxonomyResponse]]:
        """Get career progression options from current job."""
        try:
            current_job = self.db.query(JobTitleTaxonomy).filter(
                JobTitleTaxonomy.id == current_job_id
            ).first()
            
            if not current_job:
                return {"advancement": [], "lateral": []}
            
            # Get advancement roles
            advancement_jobs = self.db.query(JobTitleTaxonomy).filter(
                JobTitleTaxonomy.normalized_title.in_(current_job.advancement_roles)
            ).all()
            
            # Get lateral move roles
            lateral_jobs = self.db.query(JobTitleTaxonomy).filter(
                JobTitleTaxonomy.normalized_title.in_(current_job.lateral_move_roles)
            ).all()
            
            return {
                "advancement": [JobTitleTaxonomyResponse.from_orm(job) for job in advancement_jobs],
                "lateral": [JobTitleTaxonomyResponse.from_orm(job) for job in lateral_jobs]
            }
            
        except Exception as e:
            logger.error(f"Error getting career progression path: {e}")
            raise
    
    # Technologies Management
    
    def create_technology(self, tech_data: SecurityTechnologyTaxonomyCreate) -> SecurityTechnologyTaxonomyResponse:
        """Create a new security technology in the taxonomy."""
        try:
            technology = SecurityTechnologyTaxonomy(**tech_data.dict())
            self.db.add(technology)
            self.db.commit()
            self.db.refresh(technology)
            
            logger.info(f"Created technology: {technology.technology_name}")
            return SecurityTechnologyTaxonomyResponse.from_orm(technology)
            
        except Exception as e:
            logger.error(f"Error creating technology: {e}")
            self.db.rollback()
            raise
    
    def get_trending_technologies(self, min_adoption_rate: float = 5.0) -> List[SecurityTechnologyTaxonomyResponse]:
        """Get trending technologies based on adoption rate."""
        try:
            technologies = self.db.query(SecurityTechnologyTaxonomy).filter(
                SecurityTechnologyTaxonomy.adoption_rate >= min_adoption_rate
            ).order_by(SecurityTechnologyTaxonomy.adoption_rate.desc()).all()
            
            return [SecurityTechnologyTaxonomyResponse.from_orm(tech) for tech in technologies]
            
        except Exception as e:
            logger.error(f"Error getting trending technologies: {e}")
            raise
    
    def get_high_demand_technologies(self, min_job_demand: int = 50) -> List[SecurityTechnologyTaxonomyResponse]:
        """Get technologies with high job market demand."""
        try:
            technologies = self.db.query(SecurityTechnologyTaxonomy).filter(
                SecurityTechnologyTaxonomy.job_demand >= min_job_demand
            ).order_by(SecurityTechnologyTaxonomy.job_demand.desc()).all()
            
            return [SecurityTechnologyTaxonomyResponse.from_orm(tech) for tech in technologies]
            
        except Exception as e:
            logger.error(f"Error getting high demand technologies: {e}")
            raise

    # Analytics and Insights

    def generate_taxonomy_analytics(self) -> TaxonomyAnalytics:
        """Generate comprehensive taxonomy analytics."""
        try:
            # Count totals
            total_skills = self.db.query(SecuritySkillTaxonomy).count()
            total_certifications = self.db.query(SecurityCertificationTaxonomy).count()
            total_job_titles = self.db.query(JobTitleTaxonomy).count()
            total_technologies = self.db.query(SecurityTechnologyTaxonomy).count()

            # Get trending items
            trending_skills = [
                skill.skill_name for skill in
                self.db.query(SecuritySkillTaxonomy).filter(
                    SecuritySkillTaxonomy.is_trending == True
                ).order_by(SecuritySkillTaxonomy.demand_score.desc()).limit(10).all()
            ]

            emerging_technologies = [
                tech.technology_name for tech in
                self.db.query(SecurityTechnologyTaxonomy).filter(
                    SecurityTechnologyTaxonomy.adoption_rate > 10.0
                ).order_by(SecurityTechnologyTaxonomy.adoption_rate.desc()).limit(10).all()
            ]

            high_demand_certifications = [
                cert.certification_name for cert in
                self.db.query(SecurityCertificationTaxonomy).filter(
                    SecurityCertificationTaxonomy.job_requirement_frequency > 20.0
                ).order_by(SecurityCertificationTaxonomy.job_requirement_frequency.desc()).limit(10).all()
            ]

            top_paying_roles = [
                job.job_title for job in
                self.db.query(JobTitleTaxonomy).filter(
                    JobTitleTaxonomy.average_salary.isnot(None)
                ).order_by(JobTitleTaxonomy.average_salary.desc()).limit(10).all()
            ]

            # Identify skill gaps (skills with high demand but low supply indicators)
            skill_gaps = [
                skill.skill_name for skill in
                self.db.query(SecuritySkillTaxonomy).filter(
                    and_(
                        SecuritySkillTaxonomy.demand_score > 70.0,
                        SecuritySkillTaxonomy.job_frequency < 100
                    )
                ).all()
            ]

            # Market trends analysis
            market_trends = {
                "cloud_security_growth": self._calculate_domain_growth(SecurityDomain.CLOUD_SECURITY),
                "ai_ml_security_emergence": self._calculate_emerging_skill_trend("AI", "ML", "Machine Learning"),
                "zero_trust_adoption": self._calculate_technology_adoption("Zero Trust"),
                "devsecops_integration": self._calculate_domain_growth(SecurityDomain.SOFTWARE_DEVELOPMENT_SECURITY),
                "remote_work_impact": self._calculate_remote_work_trends()
            }

            return TaxonomyAnalytics(
                total_skills=total_skills,
                total_certifications=total_certifications,
                total_job_titles=total_job_titles,
                total_technologies=total_technologies,
                trending_skills=trending_skills,
                emerging_technologies=emerging_technologies,
                high_demand_certifications=high_demand_certifications,
                top_paying_roles=top_paying_roles,
                skill_gaps=skill_gaps,
                market_trends=market_trends,
                generated_at=datetime.utcnow()
            )

        except Exception as e:
            logger.error(f"Error generating taxonomy analytics: {e}")
            raise

    def _calculate_domain_growth(self, domain: SecurityDomain) -> float:
        """Calculate growth rate for a security domain."""
        try:
            # This would typically analyze historical data
            # For now, return a placeholder calculation
            job_count = self.db.query(JobTitleTaxonomy).filter(
                JobTitleTaxonomy.security_domain == domain.value
            ).count()

            skill_count = self.db.query(SecuritySkillTaxonomy).filter(
                SecuritySkillTaxonomy.security_domains.contains([domain.value])
            ).count()

            # Simple growth indicator based on job and skill availability
            return min(100.0, (job_count + skill_count) * 2.5)

        except Exception as e:
            logger.error(f"Error calculating domain growth: {e}")
            return 0.0

    def _calculate_emerging_skill_trend(self, *keywords) -> float:
        """Calculate trend score for emerging skills based on keywords."""
        try:
            emerging_count = 0
            total_count = 0

            for keyword in keywords:
                skills = self.db.query(SecuritySkillTaxonomy).filter(
                    or_(
                        SecuritySkillTaxonomy.skill_name.ilike(f"%{keyword}%"),
                        SecuritySkillTaxonomy.description.ilike(f"%{keyword}%")
                    )
                ).all()

                total_count += len(skills)
                emerging_count += sum(1 for skill in skills if skill.is_emerging)

            return (emerging_count / max(1, total_count)) * 100.0

        except Exception as e:
            logger.error(f"Error calculating emerging skill trend: {e}")
            return 0.0

    def _calculate_technology_adoption(self, technology_name: str) -> float:
        """Calculate adoption rate for a specific technology."""
        try:
            technology = self.db.query(SecurityTechnologyTaxonomy).filter(
                SecurityTechnologyTaxonomy.technology_name.ilike(f"%{technology_name}%")
            ).first()

            return technology.adoption_rate if technology else 0.0

        except Exception as e:
            logger.error(f"Error calculating technology adoption: {e}")
            return 0.0

    def _calculate_remote_work_trends(self) -> float:
        """Calculate remote work trends in security roles."""
        try:
            total_jobs = self.db.query(JobTitleTaxonomy).count()
            if total_jobs == 0:
                return 0.0

            avg_remote_percentage = self.db.query(
                func.avg(JobTitleTaxonomy.remote_work_percentage)
            ).scalar() or 0.0

            return float(avg_remote_percentage)

        except Exception as e:
            logger.error(f"Error calculating remote work trends: {e}")
            return 0.0

    # Personalized Recommendations

    def generate_skill_recommendations(self, user_id: int, current_skills: List[str],
                                     target_domain: Optional[SecurityDomain] = None) -> List[TaxonomyRecommendation]:
        """Generate personalized skill recommendations for a user."""
        try:
            recommendations = []

            # Get user's current skill gaps
            if target_domain:
                domain_skills = self.get_skills_by_domain(target_domain)
                missing_skills = [
                    skill for skill in domain_skills
                    if skill.skill_name not in current_skills
                ]
            else:
                # Get high-impact skills across all domains
                missing_skills = self.get_high_impact_skills(min_salary_impact=5.0)
                missing_skills = [
                    skill for skill in missing_skills
                    if skill.skill_name not in current_skills
                ]

            # Sort by relevance (demand score + salary impact)
            missing_skills.sort(
                key=lambda x: x.demand_score + x.salary_impact,
                reverse=True
            )

            for skill in missing_skills[:10]:  # Top 10 recommendations
                recommendation = TaxonomyRecommendation(
                    user_id=user_id,
                    recommendation_type="skill",
                    item_id=skill.id,
                    item_name=skill.skill_name,
                    relevance_score=min(100.0, skill.demand_score + skill.salary_impact),
                    reasoning=f"High-demand skill with {skill.salary_impact}% salary impact",
                    estimated_impact=f"Potential {skill.salary_impact}% salary increase",
                    time_investment="2-6 months depending on current level",
                    prerequisites=skill.related_skills[:3],  # Top 3 related skills
                    next_steps=[
                        "Assess current skill level",
                        "Find learning resources",
                        "Practice with hands-on labs",
                        "Build portfolio projects"
                    ]
                )
                recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            logger.error(f"Error generating skill recommendations: {e}")
            raise

    def generate_certification_recommendations(self, user_id: int, current_certs: List[str],
                                             career_level: str = "intermediate") -> List[TaxonomyRecommendation]:
        """Generate personalized certification recommendations."""
        try:
            recommendations = []

            # Map career level to certification level
            cert_level_mapping = {
                "beginner": CertificationLevel.ENTRY,
                "intermediate": CertificationLevel.ASSOCIATE,
                "advanced": CertificationLevel.PROFESSIONAL,
                "expert": CertificationLevel.EXPERT,
                "senior": CertificationLevel.ARCHITECT
            }

            target_level = cert_level_mapping.get(career_level, CertificationLevel.ASSOCIATE)

            # Get relevant certifications
            relevant_certs = self.get_certifications_by_level(target_level)
            missing_certs = [
                cert for cert in relevant_certs
                if cert.certification_code not in current_certs
            ]

            # Sort by value (salary premium + job requirement frequency)
            missing_certs.sort(
                key=lambda x: x.salary_premium + (x.job_requirement_frequency * 100),
                reverse=True
            )

            for cert in missing_certs[:5]:  # Top 5 recommendations
                recommendation = TaxonomyRecommendation(
                    user_id=user_id,
                    recommendation_type="certification",
                    item_id=cert.id,
                    item_name=cert.certification_name,
                    relevance_score=min(100.0, (cert.salary_premium / 1000) + cert.job_requirement_frequency),
                    reasoning=f"High-value certification with ${cert.salary_premium:,.0f} salary premium",
                    estimated_impact=f"${cert.salary_premium:,.0f} salary increase, required by {cert.job_requirement_frequency}% of jobs",
                    time_investment=f"3-6 months preparation, ${cert.exam_cost:,.0f} exam cost",
                    cost_estimate=cert.exam_cost,
                    prerequisites=cert.prerequisites,
                    next_steps=[
                        "Review exam objectives",
                        "Enroll in training course",
                        "Study with official materials",
                        "Take practice exams",
                        "Schedule certification exam"
                    ]
                )
                recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            logger.error(f"Error generating certification recommendations: {e}")
            raise

    def generate_career_path_recommendations(self, user_id: int, current_role: str,
                                           target_timeframe: int = 24) -> List[TaxonomyRecommendation]:
        """Generate career path recommendations."""
        try:
            recommendations = []

            # Find current job in taxonomy
            current_job = self.db.query(JobTitleTaxonomy).filter(
                or_(
                    JobTitleTaxonomy.job_title.ilike(f"%{current_role}%"),
                    JobTitleTaxonomy.normalized_title.ilike(f"%{current_role}%")
                )
            ).first()

            if not current_job:
                # If current role not found, recommend popular entry-level roles
                entry_roles = self.db.query(JobTitleTaxonomy).filter(
                    JobTitleTaxonomy.seniority_level.in_(["beginner", "intermediate"])
                ).order_by(JobTitleTaxonomy.job_posting_frequency.desc()).limit(5).all()

                for role in entry_roles:
                    recommendation = TaxonomyRecommendation(
                        user_id=user_id,
                        recommendation_type="career_path",
                        item_id=role.id,
                        item_name=role.job_title,
                        relevance_score=min(100.0, role.job_posting_frequency / 10),
                        reasoning="High-demand entry-level security role",
                        estimated_impact=f"Average salary: ${role.average_salary:,.0f}" if role.average_salary else "Competitive salary",
                        time_investment=f"{target_timeframe} months transition time",
                        prerequisites=role.required_skills[:5],
                        next_steps=[
                            "Assess skill gaps",
                            "Pursue required certifications",
                            "Build relevant experience",
                            "Network with professionals",
                            "Apply for positions"
                        ]
                    )
                    recommendations.append(recommendation)
            else:
                # Get advancement opportunities
                progression_paths = self.get_career_progression_path(current_job.id)

                for role in progression_paths["advancement"][:3]:
                    salary_increase = 0
                    if current_job.average_salary and role.average_salary:
                        salary_increase = role.average_salary - current_job.average_salary

                    recommendation = TaxonomyRecommendation(
                        user_id=user_id,
                        recommendation_type="career_advancement",
                        item_id=role.id,
                        item_name=role.job_title,
                        relevance_score=85.0,  # High relevance for direct advancement
                        reasoning="Natural career progression from current role",
                        estimated_impact=f"Salary increase: ${salary_increase:,.0f}" if salary_increase > 0 else "Career advancement",
                        time_investment=f"{target_timeframe} months preparation",
                        prerequisites=role.required_skills[:5],
                        next_steps=[
                            "Develop leadership skills",
                            "Gain additional certifications",
                            "Expand technical expertise",
                            "Build management experience",
                            "Seek internal promotions"
                        ]
                    )
                    recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            logger.error(f"Error generating career path recommendations: {e}")
            raise

    # User Interaction Methods

    def assess_user_skills(self, user_id: int, skill_assessments: List[UserSkillAssessment]) -> Dict[str, Any]:
        """Process user skill assessments and provide insights."""
        try:
            skill_profile = {
                "strengths": [],
                "development_areas": [],
                "skill_gaps": [],
                "recommendations": []
            }

            for assessment in skill_assessments:
                skill = self.db.query(SecuritySkillTaxonomy).filter(
                    SecuritySkillTaxonomy.id == assessment.skill_id
                ).first()

                if not skill:
                    continue

                skill_data = {
                    "skill_name": skill.skill_name,
                    "level": assessment.skill_level,
                    "confidence": assessment.confidence_level,
                    "market_demand": skill.demand_score,
                    "salary_impact": skill.salary_impact
                }

                # Categorize based on level and confidence
                if assessment.skill_level in ["advanced", "expert"] and assessment.confidence_level >= 4:
                    skill_profile["strengths"].append(skill_data)
                elif assessment.skill_level in ["basic", "intermediate"] or assessment.confidence_level < 3:
                    skill_profile["development_areas"].append(skill_data)

            # Identify skill gaps based on high-demand skills not assessed
            assessed_skill_ids = [a.skill_id for a in skill_assessments]
            high_demand_skills = self.db.query(SecuritySkillTaxonomy).filter(
                and_(
                    SecuritySkillTaxonomy.demand_score > 70.0,
                    ~SecuritySkillTaxonomy.id.in_(assessed_skill_ids)
                )
            ).limit(10).all()

            for skill in high_demand_skills:
                skill_profile["skill_gaps"].append({
                    "skill_name": skill.skill_name,
                    "demand_score": skill.demand_score,
                    "salary_impact": skill.salary_impact,
                    "learning_priority": "high" if skill.demand_score > 85.0 else "medium"
                })

            return skill_profile

        except Exception as e:
            logger.error(f"Error assessing user skills: {e}")
            raise
