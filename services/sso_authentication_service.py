"""SSO Authentication Service for enterprise single sign-on.

This service provides comprehensive SSO authentication capabilities including
SAML 2.0, OpenID Connect, OAuth 2.0, and enterprise identity provider
integrations with automatic user provisioning and role mapping.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import json
import uuid
import xml.etree.ElementTree as ET
import base64
import jwt
import requests
from urllib.parse import urlencode, parse_qs
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.x509 import load_pem_x509_certificate
import secrets

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserLicense
from services.integration_hub_service import IntegrationHubService

logger = logging.getLogger(__name__)


class SSOAuthenticationService:
    """Service for SSO authentication and user management."""
    
    def __init__(self, db: Session):
        self.db = db
        self.integration_service = IntegrationHubService(db)
    
    # SAML 2.0 Authentication
    
    def initiate_saml_login(self, integration_id: str, relay_state: str = None) -> Dict[str, Any]:
        """Initiate SAML SSO login process."""
        try:
            # Get SSO integration configuration
            integration = self._get_sso_integration(integration_id)
            if not integration:
                return {'error': 'SSO integration not found'}
            
            # Generate SAML AuthnRequest
            request_id = f"_saml_request_{uuid.uuid4()}"
            timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
            
            # Build SAML AuthnRequest XML
            authn_request = f"""<?xml version="1.0" encoding="UTF-8"?>
<samlp:AuthnRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                    xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                    ID="{request_id}"
                    Version="2.0"
                    IssueInstant="{timestamp}"
                    Destination="{integration['sso_url']}"
                    AssertionConsumerServiceURL="{self._get_acs_url(integration_id)}"
                    ProtocolBinding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST">
    <saml:Issuer>{integration['entity_id']}</saml:Issuer>
    <samlp:NameIDPolicy Format="urn:oasis:names:tc:SAML:2.0:nameid-format:emailAddress"
                        AllowCreate="true"/>
</samlp:AuthnRequest>"""
            
            # Encode and sign the request
            encoded_request = base64.b64encode(authn_request.encode()).decode()
            
            # Build SSO URL with parameters
            sso_params = {
                'SAMLRequest': encoded_request,
                'RelayState': relay_state or '',
                'SigAlg': 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256'
            }
            
            sso_url = f"{integration['sso_url']}?{urlencode(sso_params)}"
            
            # Store request for validation
            self._store_saml_request(request_id, integration_id, relay_state)
            
            return {
                'sso_url': sso_url,
                'request_id': request_id,
                'relay_state': relay_state,
                'method': 'redirect'
            }
        
        except Exception as e:
            logger.error(f"Error initiating SAML login: {e}")
            return {'error': str(e)}
    
    def process_saml_response(self, integration_id: str, saml_response: str, relay_state: str = None) -> Dict[str, Any]:
        """Process SAML response and authenticate user."""
        try:
            # Get SSO integration configuration
            integration = self._get_sso_integration(integration_id)
            if not integration:
                return {'error': 'SSO integration not found'}
            
            # Decode SAML response
            decoded_response = base64.b64decode(saml_response).decode()
            
            # Parse SAML response XML
            saml_data = self._parse_saml_response(decoded_response, integration)
            if not saml_data['valid']:
                return {'error': f"Invalid SAML response: {saml_data['error']}"}
            
            # Extract user attributes
            user_attributes = saml_data['attributes']
            
            # Map SAML attributes to user profile
            user_profile = self._map_saml_attributes(user_attributes, integration['attribute_mapping'])
            
            # Find or create user
            user = self._find_or_create_user(
                integration['organization_id'],
                user_profile,
                integration['auto_provision_users']
            )
            
            if not user:
                return {'error': 'User not found and auto-provisioning is disabled'}
            
            # Update user's last login and SSO info
            user['last_login'] = datetime.utcnow()
            user['sso_provider'] = integration['provider_name']
            user['sso_subject_id'] = saml_data.get('subject_id')
            
            # Generate session token
            session_token = self._generate_session_token(user['user_id'], integration_id)
            
            # Log successful authentication
            self._log_authentication_event(integration_id, user['user_id'], 'saml_success')
            
            return {
                'authentication_status': 'success',
                'user_profile': user,
                'session_token': session_token,
                'session_expires_at': (datetime.utcnow() + timedelta(hours=8)).isoformat(),
                'sso_provider': integration['provider_name'],
                'relay_state': relay_state
            }
        
        except Exception as e:
            logger.error(f"Error processing SAML response: {e}")
            self._log_authentication_event(integration_id, None, 'saml_error', str(e))
            return {'error': str(e)}
    
    # OpenID Connect Authentication
    
    def initiate_oidc_login(self, integration_id: str, state: str = None) -> Dict[str, Any]:
        """Initiate OpenID Connect login process."""
        try:
            # Get SSO integration configuration
            integration = self._get_sso_integration(integration_id)
            if not integration:
                return {'error': 'SSO integration not found'}
            
            # Generate state parameter for security
            if not state:
                state = secrets.token_urlsafe(32)
            
            # Generate nonce for ID token validation
            nonce = secrets.token_urlsafe(32)
            
            # Build authorization URL
            auth_params = {
                'response_type': 'code',
                'client_id': integration['client_id'],
                'redirect_uri': self._get_oidc_redirect_uri(integration_id),
                'scope': 'openid email profile',
                'state': state,
                'nonce': nonce
            }
            
            auth_url = f"{integration['authorization_endpoint']}?{urlencode(auth_params)}"
            
            # Store state and nonce for validation
            self._store_oidc_state(state, integration_id, nonce)
            
            return {
                'auth_url': auth_url,
                'state': state,
                'nonce': nonce,
                'method': 'redirect'
            }
        
        except Exception as e:
            logger.error(f"Error initiating OIDC login: {e}")
            return {'error': str(e)}
    
    def process_oidc_callback(self, integration_id: str, code: str, state: str) -> Dict[str, Any]:
        """Process OpenID Connect callback and authenticate user."""
        try:
            # Get SSO integration configuration
            integration = self._get_sso_integration(integration_id)
            if not integration:
                return {'error': 'SSO integration not found'}
            
            # Validate state parameter
            stored_state = self._get_oidc_state(state)
            if not stored_state or stored_state['integration_id'] != integration_id:
                return {'error': 'Invalid state parameter'}
            
            # Exchange authorization code for tokens
            token_response = self._exchange_oidc_code(integration, code)
            if 'error' in token_response:
                return token_response
            
            # Validate and decode ID token
            id_token_data = self._validate_oidc_id_token(
                token_response['id_token'],
                integration,
                stored_state['nonce']
            )
            if 'error' in id_token_data:
                return id_token_data
            
            # Get user info from userinfo endpoint
            user_info = self._get_oidc_user_info(
                token_response['access_token'],
                integration['userinfo_endpoint']
            )
            
            # Combine ID token claims and user info
            user_attributes = {**id_token_data['claims'], **user_info}
            
            # Map OIDC attributes to user profile
            user_profile = self._map_oidc_attributes(user_attributes, integration['attribute_mapping'])
            
            # Find or create user
            user = self._find_or_create_user(
                integration['organization_id'],
                user_profile,
                integration['auto_provision_users']
            )
            
            if not user:
                return {'error': 'User not found and auto-provisioning is disabled'}
            
            # Update user's last login and SSO info
            user['last_login'] = datetime.utcnow()
            user['sso_provider'] = integration['provider_name']
            user['sso_subject_id'] = id_token_data['claims'].get('sub')
            
            # Generate session token
            session_token = self._generate_session_token(user['user_id'], integration_id)
            
            # Log successful authentication
            self._log_authentication_event(integration_id, user['user_id'], 'oidc_success')
            
            return {
                'authentication_status': 'success',
                'user_profile': user,
                'session_token': session_token,
                'session_expires_at': (datetime.utcnow() + timedelta(hours=8)).isoformat(),
                'sso_provider': integration['provider_name'],
                'access_token': token_response['access_token'],
                'refresh_token': token_response.get('refresh_token')
            }
        
        except Exception as e:
            logger.error(f"Error processing OIDC callback: {e}")
            self._log_authentication_event(integration_id, None, 'oidc_error', str(e))
            return {'error': str(e)}
    
    # OAuth 2.0 Authentication
    
    def initiate_oauth_login(self, integration_id: str, state: str = None) -> Dict[str, Any]:
        """Initiate OAuth 2.0 login process."""
        try:
            # Get SSO integration configuration
            integration = self._get_sso_integration(integration_id)
            if not integration:
                return {'error': 'SSO integration not found'}
            
            # Generate state parameter for security
            if not state:
                state = secrets.token_urlsafe(32)
            
            # Build authorization URL
            auth_params = {
                'response_type': 'code',
                'client_id': integration['client_id'],
                'redirect_uri': self._get_oauth_redirect_uri(integration_id),
                'scope': integration.get('scope', 'read:user user:email'),
                'state': state
            }
            
            auth_url = f"{integration['authorization_endpoint']}?{urlencode(auth_params)}"
            
            # Store state for validation
            self._store_oauth_state(state, integration_id)
            
            return {
                'auth_url': auth_url,
                'state': state,
                'method': 'redirect'
            }
        
        except Exception as e:
            logger.error(f"Error initiating OAuth login: {e}")
            return {'error': str(e)}
    
    def process_oauth_callback(self, integration_id: str, code: str, state: str) -> Dict[str, Any]:
        """Process OAuth 2.0 callback and authenticate user."""
        try:
            # Get SSO integration configuration
            integration = self._get_sso_integration(integration_id)
            if not integration:
                return {'error': 'SSO integration not found'}
            
            # Validate state parameter
            if not self._validate_oauth_state(state, integration_id):
                return {'error': 'Invalid state parameter'}
            
            # Exchange authorization code for access token
            token_response = self._exchange_oauth_code(integration, code)
            if 'error' in token_response:
                return token_response
            
            # Get user info using access token
            user_info = self._get_oauth_user_info(
                token_response['access_token'],
                integration['userinfo_endpoint']
            )
            
            # Map OAuth attributes to user profile
            user_profile = self._map_oauth_attributes(user_info, integration['attribute_mapping'])
            
            # Find or create user
            user = self._find_or_create_user(
                integration['organization_id'],
                user_profile,
                integration['auto_provision_users']
            )
            
            if not user:
                return {'error': 'User not found and auto-provisioning is disabled'}
            
            # Update user's last login and SSO info
            user['last_login'] = datetime.utcnow()
            user['sso_provider'] = integration['provider_name']
            user['sso_subject_id'] = user_info.get('id') or user_info.get('sub')
            
            # Generate session token
            session_token = self._generate_session_token(user['user_id'], integration_id)
            
            # Log successful authentication
            self._log_authentication_event(integration_id, user['user_id'], 'oauth_success')
            
            return {
                'authentication_status': 'success',
                'user_profile': user,
                'session_token': session_token,
                'session_expires_at': (datetime.utcnow() + timedelta(hours=8)).isoformat(),
                'sso_provider': integration['provider_name'],
                'access_token': token_response['access_token']
            }
        
        except Exception as e:
            logger.error(f"Error processing OAuth callback: {e}")
            self._log_authentication_event(integration_id, None, 'oauth_error', str(e))
            return {'error': str(e)}
    
    # User Management
    
    def logout_sso_user(self, session_token: str, integration_id: str = None) -> Dict[str, Any]:
        """Logout SSO user and invalidate session."""
        try:
            # Validate session token
            session_data = self._validate_session_token(session_token)
            if not session_data:
                return {'error': 'Invalid session token'}
            
            # Get user and integration info
            user_id = session_data['user_id']
            integration_id = integration_id or session_data.get('integration_id')
            
            # Invalidate session token
            self._invalidate_session_token(session_token)
            
            # Get SSO integration for logout URL
            logout_url = None
            if integration_id:
                integration = self._get_sso_integration(integration_id)
                if integration and integration.get('slo_url'):
                    logout_url = self._build_sso_logout_url(integration, user_id)
            
            # Log logout event
            self._log_authentication_event(integration_id, user_id, 'logout')
            
            return {
                'logout_status': 'success',
                'sso_logout_url': logout_url,
                'message': 'User logged out successfully'
            }
        
        except Exception as e:
            logger.error(f"Error logging out SSO user: {e}")
            return {'error': str(e)}
    
    # Helper Methods
    
    def _get_sso_integration(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get SSO integration configuration."""
        # Mock implementation - would query from database
        return {
            'integration_id': integration_id,
            'organization_id': 1,
            'provider_name': 'Azure AD',
            'entity_id': 'certpathfinder-enterprise',
            'sso_url': 'https://login.microsoftonline.com/tenant/saml2',
            'slo_url': 'https://login.microsoftonline.com/tenant/saml2/logout',
            'x509_certificate': '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
            'attribute_mapping': {
                'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
                'first_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
                'last_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname'
            },
            'auto_provision_users': True,
            'default_role': 'learner'
        }
    
    def _parse_saml_response(self, saml_response: str, integration: Dict[str, Any]) -> Dict[str, Any]:
        """Parse and validate SAML response."""
        try:
            # Parse XML
            root = ET.fromstring(saml_response)
            
            # Extract attributes (simplified)
            attributes = {}
            for attr_stmt in root.findall('.//{urn:oasis:names:tc:SAML:2.0:assertion}AttributeStatement'):
                for attr in attr_stmt.findall('.//{urn:oasis:names:tc:SAML:2.0:assertion}Attribute'):
                    attr_name = attr.get('Name')
                    attr_values = [val.text for val in attr.findall('.//{urn:oasis:names:tc:SAML:2.0:assertion}AttributeValue')]
                    attributes[attr_name] = attr_values[0] if len(attr_values) == 1 else attr_values
            
            # Extract subject
            subject_elem = root.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}Subject/{urn:oasis:names:tc:SAML:2.0:assertion}NameID')
            subject_id = subject_elem.text if subject_elem is not None else None
            
            return {
                'valid': True,
                'attributes': attributes,
                'subject_id': subject_id
            }
        
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    def _map_saml_attributes(self, saml_attributes: Dict[str, Any], mapping: Dict[str, str]) -> Dict[str, Any]:
        """Map SAML attributes to user profile."""
        user_profile = {}
        
        for target_attr, saml_attr in mapping.items():
            if saml_attr in saml_attributes:
                user_profile[target_attr] = saml_attributes[saml_attr]
        
        return user_profile
    
    def _find_or_create_user(self, organization_id: int, user_profile: Dict[str, Any], auto_provision: bool) -> Optional[Dict[str, Any]]:
        """Find existing user or create new one if auto-provisioning is enabled."""
        # Mock implementation - would query/create in database
        return {
            'user_id': f"user_{user_profile.get('email', 'unknown')}",
            'email': user_profile.get('email'),
            'first_name': user_profile.get('first_name'),
            'last_name': user_profile.get('last_name'),
            'display_name': f"{user_profile.get('first_name', '')} {user_profile.get('last_name', '')}".strip(),
            'organization_id': organization_id,
            'role': 'learner',
            'is_active': True,
            'created_via_sso': auto_provision
        }
    
    def _generate_session_token(self, user_id: str, integration_id: str) -> str:
        """Generate secure session token."""
        payload = {
            'user_id': user_id,
            'integration_id': integration_id,
            'issued_at': datetime.utcnow().isoformat(),
            'expires_at': (datetime.utcnow() + timedelta(hours=8)).isoformat()
        }
        
        # In production, use proper JWT signing
        return jwt.encode(payload, 'secret_key', algorithm='HS256')
    
    def _log_authentication_event(self, integration_id: str, user_id: str, event_type: str, details: str = None):
        """Log authentication event for audit purposes."""
        logger.info(f"SSO Auth Event - Integration: {integration_id}, User: {user_id}, Type: {event_type}, Details: {details}")
    
    def _get_acs_url(self, integration_id: str) -> str:
        """Get Assertion Consumer Service URL."""
        return f"https://api.certpathfinder.com/v1/integration-hub/sso/{integration_id}/acs"
    
    def _get_oidc_redirect_uri(self, integration_id: str) -> str:
        """Get OIDC redirect URI."""
        return f"https://api.certpathfinder.com/v1/integration-hub/sso/{integration_id}/oidc/callback"
    
    def _get_oauth_redirect_uri(self, integration_id: str) -> str:
        """Get OAuth redirect URI."""
        return f"https://api.certpathfinder.com/v1/integration-hub/sso/{integration_id}/oauth/callback"
