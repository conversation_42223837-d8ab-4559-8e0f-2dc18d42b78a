"""Enhanced PDF report generation service for career summaries and transition plans.

This service provides comprehensive PDF report generation with charts, graphs,
and detailed career analysis including transition pathways and cost breakdowns.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import io
import base64
from pathlib import Path

# PDF generation libraries
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, 
    PageBreak, Image, KeepTogether
)
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.linecharts import HorizontalLineChart
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.legends import Legend
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY

# Data visualization
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_agg import FigureCanvasAgg
import seaborn as sns
import pandas as pd
import numpy as np

from sqlalchemy.orm import Session
from models.career_transition import CareerTransitionPlan, CareerTransitionStep, CareerRole
from models.certification import Certification
from models.cost_calculation import CostCalculation
from services.career_transition import CareerTransitionService

logger = logging.getLogger(__name__)


class EnhancedPDFReportGenerator:
    """Enhanced PDF report generator for career summaries and transition plans."""
    
    def __init__(self, db: Session):
        self.db = db
        self.career_service = CareerTransitionService(db)
        
        # Set up matplotlib style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # PDF styling
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Set up custom paragraph styles for the PDF."""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor('#2C3E50'),
            alignment=TA_CENTER
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=colors.HexColor('#34495E'),
            borderWidth=1,
            borderColor=colors.HexColor('#BDC3C7'),
            borderPadding=5
        ))
        
        # Subsection header style
        self.styles.add(ParagraphStyle(
            name='SubsectionHeader',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=8,
            spaceBefore=12,
            textColor=colors.HexColor('#2980B9')
        ))
        
        # Highlight style
        self.styles.add(ParagraphStyle(
            name='Highlight',
            parent=self.styles['Normal'],
            fontSize=12,
            textColor=colors.HexColor('#E74C3C'),
            backColor=colors.HexColor('#FCF3CF'),
            borderWidth=1,
            borderColor=colors.HexColor('#F39C12'),
            borderPadding=8
        ))
        
        # Info box style
        self.styles.add(ParagraphStyle(
            name='InfoBox',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=colors.HexColor('#2C3E50'),
            backColor=colors.HexColor('#EBF5FB'),
            borderWidth=1,
            borderColor=colors.HexColor('#3498DB'),
            borderPadding=10,
            alignment=TA_JUSTIFY
        ))
    
    def generate_career_summary_report(
        self,
        user_id: str,
        include_transition_plans: bool = True,
        include_cost_analysis: bool = True,
        include_recommendations: bool = True
    ) -> bytes:
        """Generate comprehensive career summary PDF report."""
        logger.info(f"Generating career summary report for user {user_id}")
        
        # Create PDF buffer
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Build report content
        story = []
        
        # Title page
        story.extend(self._create_title_page(user_id))
        story.append(PageBreak())
        
        # Executive summary
        story.extend(self._create_executive_summary(user_id))
        story.append(PageBreak())
        
        # Current career status
        story.extend(self._create_current_status_section(user_id))
        
        if include_transition_plans:
            story.append(PageBreak())
            story.extend(self._create_transition_plans_section(user_id))
        
        if include_cost_analysis:
            story.append(PageBreak())
            story.extend(self._create_cost_analysis_section(user_id))
        
        if include_recommendations:
            story.append(PageBreak())
            story.extend(self._create_recommendations_section(user_id))
        
        # Appendices
        story.append(PageBreak())
        story.extend(self._create_appendices(user_id))
        
        # Build PDF
        doc.build(story)
        
        # Get PDF bytes
        pdf_bytes = buffer.getvalue()
        buffer.close()
        
        logger.info(f"Generated career summary report ({len(pdf_bytes)} bytes)")
        return pdf_bytes
    
    def _create_title_page(self, user_id: str) -> List[Any]:
        """Create the title page of the report."""
        story = []
        
        # Main title
        story.append(Spacer(1, 2*inch))
        story.append(Paragraph(
            "Career Development & Transition Report",
            self.styles['CustomTitle']
        ))
        
        story.append(Spacer(1, 0.5*inch))
        story.append(Paragraph(
            f"Prepared for: {user_id}",
            self.styles['Heading2']
        ))
        
        story.append(Spacer(1, 0.3*inch))
        story.append(Paragraph(
            f"Generated on: {datetime.now().strftime('%B %d, %Y')}",
            self.styles['Normal']
        ))
        
        story.append(Spacer(1, 1*inch))
        
        # Report overview
        overview_text = """
        This comprehensive report provides an in-depth analysis of your career development 
        journey, including current status, transition plans, cost analysis, and personalized 
        recommendations for achieving your career goals.
        
        The report includes:
        • Current career status and achievements
        • Active and planned career transitions
        • Detailed cost analysis and budget planning
        • Certification roadmaps and timelines
        • Personalized recommendations and next steps
        """
        
        story.append(Paragraph(overview_text, self.styles['InfoBox']))
        
        return story
    
    def _create_executive_summary(self, user_id: str) -> List[Any]:
        """Create executive summary section."""
        story = []
        
        story.append(Paragraph("Executive Summary", self.styles['SectionHeader']))
        
        # Get user's transition plans
        plans = self.career_service.get_user_transition_plans(user_id)
        
        # Get user's cost calculations
        cost_calculations = self.db.query(CostCalculation).filter(
            CostCalculation.user_id == user_id,
            CostCalculation.is_saved == True
        ).all()
        
        # Summary statistics
        total_plans = len(plans)
        active_plans = len([p for p in plans if p.status == 'active'])
        total_budget = sum(p.budget_max or 0.0 for p in plans)
        total_certifications = len(set().union(*[p.alternative_paths for p in plans]))
        
        summary_data = [
            ['Metric', 'Value'],
            ['Total Career Plans', str(total_plans)],
            ['Active Plans', str(active_plans)],
            ['Total Budget Allocated', f"${total_budget:,.2f}"],
            ['Certifications in Pipeline', str(total_certifications)],
            ['Cost Calculations Created', str(len(cost_calculations))]
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498DB')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#ECF0F1')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # Key insights
        insights_text = f"""
        <b>Key Insights:</b><br/>
        • You have {active_plans} active career transition plan{'s' if active_plans != 1 else ''} in progress<br/>
        • Total investment planned: ${total_budget:,.2f}<br/>
        • Average plan duration: {self._calculate_average_duration(plans)} months<br/>
        • Most common target domain: {self._get_most_common_domain(plans)}<br/>
        • Recommended next action: {self._get_next_recommended_action(plans)}
        """
        
        story.append(Paragraph(insights_text, self.styles['InfoBox']))
        
        return story
    
    def _create_current_status_section(self, user_id: str) -> List[Any]:
        """Create current career status section."""
        story = []
        
        story.append(Paragraph("Current Career Status", self.styles['SectionHeader']))
        
        # Get user's current roles from plans
        plans = self.career_service.get_user_transition_plans(user_id)
        current_roles = [p.current_role for p in plans if p.current_role]
        
        if current_roles:
            # Current role information
            current_role = current_roles[0]  # Use the most recent
            story.append(Paragraph("Current Position", self.styles['SubsectionHeader']))
            
            role_data = [
                ['Title', current_role.title],
                ['Domain', current_role.domain],
                ['Level', current_role.level],
                ['Experience Required', f"{current_role.min_years_experience}+ years"],
                ['Market Demand', current_role.market_demand],
                ['Growth Outlook', current_role.growth_outlook]
            ]
            
            role_table = Table(role_data, colWidths=[2*inch, 3*inch])
            role_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#E8F6F3')),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(role_table)
            story.append(Spacer(1, 20))
        
        # Skills and certifications
        story.append(Paragraph("Skills & Certifications", self.styles['SubsectionHeader']))
        
        # Get user's certifications from cost calculations
        cost_calculations = self.db.query(CostCalculation).filter(
            CostCalculation.user_id == user_id
        ).all()
        
        all_cert_ids = []
        for calc in cost_calculations:
            all_cert_ids.extend(calc.certification_ids)
        
        unique_cert_ids = list(set(all_cert_ids))
        certifications = self.db.query(Certification).filter(
            Certification.id.in_(unique_cert_ids)
        ).all() if unique_cert_ids else []
        
        if certifications:
            # Create certification chart
            cert_chart = self._create_certification_chart(certifications)
            story.append(cert_chart)
            story.append(Spacer(1, 20))
        
        return story
    
    def _create_transition_plans_section(self, user_id: str) -> List[Any]:
        """Create career transition plans section."""
        story = []
        
        story.append(Paragraph("Career Transition Plans", self.styles['SectionHeader']))
        
        plans = self.career_service.get_user_transition_plans(user_id)
        
        if not plans:
            story.append(Paragraph(
                "No career transition plans found. Consider creating a plan to map your career journey.",
                self.styles['Normal']
            ))
            return story
        
        for i, plan in enumerate(plans):
            story.append(Paragraph(f"Plan {i+1}: {plan.name}", self.styles['SubsectionHeader']))
            
            # Plan overview
            plan_data = [
                ['Status', plan.status.title()],
                ['Progress', f"{plan.progress_percentage:.1f}%"],
                ['Target Role', plan.target_role.title if plan.target_role else 'Not specified'],
                ['Timeline', f"{plan.timeline_months} months" if plan.timeline_months else 'Flexible'],
                ['Budget', f"${plan.budget_max:,.2f}" if plan.budget_max else 'Not specified'],
                ['Study Hours/Week', f"{plan.study_hours_per_week} hours"]
            ]
            
            plan_table = Table(plan_data, colWidths=[2*inch, 3*inch])
            plan_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#FEF9E7')),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(plan_table)
            story.append(Spacer(1, 15))
            
            # Plan steps
            steps = self.db.query(CareerTransitionStep).filter(
                CareerTransitionStep.plan_id == plan.id
            ).order_by(CareerTransitionStep.sequence).all()
            
            if steps:
                story.append(Paragraph("Plan Steps", self.styles['Normal']))
                
                step_data = [['Step', 'Description', 'Status', 'Progress']]
                for step in steps:
                    step_data.append([
                        str(step.sequence),
                        step.name[:40] + '...' if len(step.name) > 40 else step.name,
                        step.status.title(),
                        f"{step.progress_percentage:.0f}%"
                    ])
                
                step_table = Table(step_data, colWidths=[0.5*inch, 2.5*inch, 1*inch, 1*inch])
                step_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2980B9')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#EBF5FB')),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(step_table)
            
            story.append(Spacer(1, 20))
        
        return story
    
    def _create_cost_analysis_section(self, user_id: str) -> List[Any]:
        """Create cost analysis section with charts and breakdowns."""
        story = []
        
        story.append(Paragraph("Cost Analysis", self.styles['SectionHeader']))
        
        # Get cost calculations
        cost_calculations = self.db.query(CostCalculation).filter(
            CostCalculation.user_id == user_id,
            CostCalculation.is_saved == True
        ).all()
        
        if not cost_calculations:
            story.append(Paragraph(
                "No saved cost calculations found. Create cost calculations to see detailed analysis.",
                self.styles['Normal']
            ))
            return story
        
        # Cost summary
        total_costs = sum(calc.total_cost_target for calc in cost_calculations)
        avg_cost = total_costs / len(cost_calculations)
        
        cost_summary_data = [
            ['Metric', 'Value'],
            ['Total Calculations', str(len(cost_calculations))],
            ['Total Estimated Cost', f"${total_costs:,.2f}"],
            ['Average Cost per Path', f"${avg_cost:,.2f}"],
            ['Most Expensive Path', f"${max(calc.total_cost_target for calc in cost_calculations):,.2f}"],
            ['Most Affordable Path', f"${min(calc.total_cost_target for calc in cost_calculations):,.2f}"]
        ]
        
        cost_table = Table(cost_summary_data, colWidths=[2.5*inch, 2.5*inch])
        cost_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#E74C3C')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#FADBD8')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(cost_table)
        story.append(Spacer(1, 20))
        
        # Cost breakdown chart
        if len(cost_calculations) > 1:
            cost_chart = self._create_cost_breakdown_chart(cost_calculations)
            story.append(cost_chart)
            story.append(Spacer(1, 20))
        
        # Detailed cost breakdown
        story.append(Paragraph("Detailed Cost Breakdown", self.styles['SubsectionHeader']))
        
        for i, calc in enumerate(cost_calculations[:5]):  # Limit to top 5
            calc_data = [
                ['Component', 'Cost'],
                ['Exam Fees', f"${calc.exam_fees_total:,.2f}"],
                ['Materials', f"${calc.materials_cost:,.2f}"],
                ['Training', f"${calc.training_cost:,.2f}"],
                ['Retakes', f"${calc.retake_cost:,.2f}"],
                ['Additional', f"${calc.additional_costs:,.2f}"],
                ['Total', f"${calc.total_cost_target:,.2f}"]
            ]
            
            calc_table = Table(calc_data, colWidths=[2*inch, 1.5*inch])
            calc_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#F39C12')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('BACKGROUND', (-1, -1), (-1, -1), colors.HexColor('#F7DC6F')),
                ('FONTNAME', (-1, -1), (-1, -1), 'Helvetica-Bold'),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(Paragraph(f"Calculation {i+1}: {calc.name}", self.styles['Normal']))
            story.append(calc_table)
            story.append(Spacer(1, 15))
        
        return story
    
    def _create_recommendations_section(self, user_id: str) -> List[Any]:
        """Create personalized recommendations section."""
        story = []
        
        story.append(Paragraph("Personalized Recommendations", self.styles['SectionHeader']))
        
        # Get user data for recommendations
        plans = self.career_service.get_user_transition_plans(user_id)
        cost_calculations = self.db.query(CostCalculation).filter(
            CostCalculation.user_id == user_id
        ).all()
        
        recommendations = self._generate_recommendations(plans, cost_calculations)
        
        for category, recs in recommendations.items():
            story.append(Paragraph(category, self.styles['SubsectionHeader']))
            
            for rec in recs:
                story.append(Paragraph(f"• {rec}", self.styles['Normal']))
            
            story.append(Spacer(1, 15))
        
        return story
    
    def _create_appendices(self, user_id: str) -> List[Any]:
        """Create appendices with additional information."""
        story = []
        
        story.append(Paragraph("Appendices", self.styles['SectionHeader']))
        
        # Appendix A: Methodology
        story.append(Paragraph("Appendix A: Methodology", self.styles['SubsectionHeader']))
        methodology_text = """
        This report is generated using advanced career pathfinding algorithms that consider:
        • Budget constraints and cost optimization
        • Timeline preferences and scheduling
        • Difficulty levels and learning preferences
        • Market demand and growth outlook
        • Historical success rates and industry trends
        
        Cost calculations include exam fees, materials, training, retake probability,
        and additional expenses. All recommendations are personalized based on your
        specific goals, constraints, and preferences.
        """
        story.append(Paragraph(methodology_text, self.styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Appendix B: Glossary
        story.append(Paragraph("Appendix B: Glossary", self.styles['SubsectionHeader']))
        glossary_data = [
            ['Term', 'Definition'],
            ['Career Transition Plan', 'A structured plan for moving from one career role to another'],
            ['Pathfinding', 'Algorithm for finding optimal career transition routes'],
            ['Cost Scenario', 'Different approaches to learning (self-study, bootcamp, etc.)'],
            ['Success Rate', 'Historical probability of successful career transition'],
            ['Market Demand', 'Current job market demand for specific roles'],
            ['Growth Outlook', 'Projected future growth for career domains']
        ]
        
        glossary_table = Table(glossary_data, colWidths=[2*inch, 3.5*inch])
        glossary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#95A5A6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(glossary_table)
        
        return story

    def _create_certification_chart(self, certifications: List[Certification]) -> Drawing:
        """Create a certification difficulty distribution chart."""
        # Count certifications by difficulty
        difficulty_counts = {}
        for cert in certifications:
            diff = cert.difficulty or 3
            difficulty_level = ['Easy', 'Easy', 'Medium', 'Medium', 'Hard', 'Expert'][min(diff, 5)]
            difficulty_counts[difficulty_level] = difficulty_counts.get(difficulty_level, 0) + 1

        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(8, 4))

        difficulties = list(difficulty_counts.keys())
        counts = list(difficulty_counts.values())
        colors_list = ['#2ECC71', '#F39C12', '#E74C3C', '#8E44AD']

        bars = ax.bar(difficulties, counts, color=colors_list[:len(difficulties)])
        ax.set_title('Certification Difficulty Distribution', fontsize=14, fontweight='bold')
        ax.set_ylabel('Number of Certifications')
        ax.set_xlabel('Difficulty Level')

        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom')

        plt.tight_layout()

        # Convert to ReportLab image
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
        img_buffer.seek(0)
        plt.close()

        # Create ReportLab Image
        img = Image(img_buffer, width=6*inch, height=3*inch)
        return img

    def _create_cost_breakdown_chart(self, cost_calculations: List[CostCalculation]) -> Drawing:
        """Create a cost breakdown comparison chart."""
        # Prepare data
        calc_names = [calc.name[:20] + '...' if len(calc.name) > 20 else calc.name
                     for calc in cost_calculations]

        exam_costs = [calc.exam_fees_total for calc in cost_calculations]
        material_costs = [calc.materials_cost for calc in cost_calculations]
        training_costs = [calc.training_cost for calc in cost_calculations]
        other_costs = [calc.retake_cost + calc.additional_costs for calc in cost_calculations]

        # Create stacked bar chart
        fig, ax = plt.subplots(figsize=(10, 6))

        width = 0.6
        x = np.arange(len(calc_names))

        p1 = ax.bar(x, exam_costs, width, label='Exam Fees', color='#3498DB')
        p2 = ax.bar(x, material_costs, width, bottom=exam_costs, label='Materials', color='#2ECC71')
        p3 = ax.bar(x, training_costs, width,
                   bottom=np.array(exam_costs) + np.array(material_costs),
                   label='Training', color='#F39C12')
        p4 = ax.bar(x, other_costs, width,
                   bottom=np.array(exam_costs) + np.array(material_costs) + np.array(training_costs),
                   label='Other', color='#E74C3C')

        ax.set_title('Cost Breakdown by Calculation', fontsize=14, fontweight='bold')
        ax.set_ylabel('Cost ($)')
        ax.set_xlabel('Cost Calculations')
        ax.set_xticks(x)
        ax.set_xticklabels(calc_names, rotation=45, ha='right')
        ax.legend()

        # Format y-axis as currency
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

        plt.tight_layout()

        # Convert to ReportLab image
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
        img_buffer.seek(0)
        plt.close()

        img = Image(img_buffer, width=7*inch, height=4*inch)
        return img

    def _calculate_average_duration(self, plans: List[CareerTransitionPlan]) -> int:
        """Calculate average plan duration."""
        durations = [p.timeline_months for p in plans if p.timeline_months]
        return int(sum(durations) / len(durations)) if durations else 12

    def _get_most_common_domain(self, plans: List[CareerTransitionPlan]) -> str:
        """Get the most common target domain."""
        domains = [p.target_role.domain for p in plans if p.target_role]
        if not domains:
            return "Not specified"

        domain_counts = {}
        for domain in domains:
            domain_counts[domain] = domain_counts.get(domain, 0) + 1

        return max(domain_counts, key=domain_counts.get)

    def _get_next_recommended_action(self, plans: List[CareerTransitionPlan]) -> str:
        """Get next recommended action based on plan status."""
        if not plans:
            return "Create your first career transition plan"

        active_plans = [p for p in plans if p.status == 'active']
        if active_plans:
            plan = active_plans[0]
            if plan.progress_percentage < 25:
                return "Focus on completing initial steps in your active plan"
            elif plan.progress_percentage < 75:
                return "Continue progressing through your current plan milestones"
            else:
                return "Prepare for plan completion and next career phase"

        planning_plans = [p for p in plans if p.status == 'planning']
        if planning_plans:
            return "Activate your planned career transition"

        return "Review and update your career goals"

    def _generate_recommendations(
        self,
        plans: List[CareerTransitionPlan],
        cost_calculations: List[CostCalculation]
    ) -> Dict[str, List[str]]:
        """Generate personalized recommendations."""
        recommendations = {
            "Career Development": [],
            "Budget Optimization": [],
            "Timeline Management": [],
            "Skill Building": []
        }

        # Career Development recommendations
        if not plans:
            recommendations["Career Development"].append(
                "Create a career transition plan to map your professional journey"
            )
        else:
            active_plans = [p for p in plans if p.status == 'active']
            if len(active_plans) > 2:
                recommendations["Career Development"].append(
                    "Consider focusing on fewer plans simultaneously for better results"
                )

            if any(p.progress_percentage < 10 for p in active_plans):
                recommendations["Career Development"].append(
                    "Start taking action on your planned career transitions"
                )

        # Budget Optimization recommendations
        if cost_calculations:
            costs = [calc.total_cost_target for calc in cost_calculations]
            avg_cost = sum(costs) / len(costs)

            if avg_cost > 5000:
                recommendations["Budget Optimization"].append(
                    "Consider self-study options to reduce training costs"
                )

            if len(set(calc.target_currency for calc in cost_calculations)) > 1:
                recommendations["Budget Optimization"].append(
                    "Standardize currency for better budget planning"
                )

        # Timeline Management recommendations
        if plans:
            long_plans = [p for p in plans if p.timeline_months and p.timeline_months > 24]
            if long_plans:
                recommendations["Timeline Management"].append(
                    "Break down long-term plans into shorter milestones"
                )

            study_hours = [p.study_hours_per_week for p in plans if p.study_hours_per_week]
            if study_hours and max(study_hours) > 30:
                recommendations["Timeline Management"].append(
                    "Consider reducing study hours to maintain work-life balance"
                )

        # Skill Building recommendations
        recommendations["Skill Building"].append(
            "Focus on high-demand skills in your target domain"
        )
        recommendations["Skill Building"].append(
            "Consider obtaining foundational certifications first"
        )

        return recommendations

    def generate_transition_plan_report(
        self,
        plan_id: int,
        user_id: str,
        include_detailed_steps: bool = True,
        include_cost_projections: bool = True
    ) -> bytes:
        """Generate detailed PDF report for a specific transition plan."""
        logger.info(f"Generating transition plan report for plan {plan_id}")

        # Get plan data
        plan = self.db.query(CareerTransitionPlan).filter(
            CareerTransitionPlan.id == plan_id,
            CareerTransitionPlan.user_id == user_id
        ).first()

        if not plan:
            raise ValueError(f"Transition plan {plan_id} not found for user {user_id}")

        # Create PDF buffer
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )

        story = []

        # Title
        story.append(Paragraph(
            f"Career Transition Plan: {plan.name}",
            self.styles['CustomTitle']
        ))
        story.append(Spacer(1, 30))

        # Plan overview
        story.extend(self._create_plan_overview(plan))

        if include_detailed_steps:
            story.append(PageBreak())
            story.extend(self._create_detailed_steps_section(plan))

        if include_cost_projections:
            story.append(PageBreak())
            story.extend(self._create_cost_projections_section(plan))

        # Timeline visualization
        story.append(PageBreak())
        story.extend(self._create_timeline_section(plan))

        # Build PDF
        doc.build(story)

        pdf_bytes = buffer.getvalue()
        buffer.close()

        return pdf_bytes

    def _create_plan_overview(self, plan: CareerTransitionPlan) -> List[Any]:
        """Create plan overview section."""
        story = []

        story.append(Paragraph("Plan Overview", self.styles['SectionHeader']))

        # Basic information
        overview_data = [
            ['Field', 'Value'],
            ['Plan Name', plan.name],
            ['Status', plan.status.title()],
            ['Progress', f"{plan.progress_percentage:.1f}%"],
            ['Current Role', plan.current_role.title if plan.current_role else 'Not specified'],
            ['Target Role', plan.target_role.title if plan.target_role else 'Not specified'],
            ['Timeline', f"{plan.timeline_months} months" if plan.timeline_months else 'Flexible'],
            ['Budget', f"${plan.budget_max:,.2f}" if plan.budget_max else 'Not specified'],
            ['Study Hours/Week', f"{plan.study_hours_per_week} hours"],
            ['Difficulty Preference', plan.difficulty_preference],
            ['Learning Style', plan.learning_style]
        ]

        overview_table = Table(overview_data, colWidths=[2.5*inch, 3*inch])
        overview_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2C3E50')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('BACKGROUND', (0, 1), (0, -1), colors.HexColor('#ECF0F1')),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (0, -1), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(overview_table)
        story.append(Spacer(1, 20))

        # Description
        if plan.description:
            story.append(Paragraph("Description", self.styles['SubsectionHeader']))
            story.append(Paragraph(plan.description, self.styles['Normal']))
            story.append(Spacer(1, 20))

        return story

    def _create_detailed_steps_section(self, plan: CareerTransitionPlan) -> List[Any]:
        """Create detailed steps section."""
        story = []

        story.append(Paragraph("Detailed Steps", self.styles['SectionHeader']))

        steps = self.db.query(CareerTransitionStep).filter(
            CareerTransitionStep.plan_id == plan.id
        ).order_by(CareerTransitionStep.sequence).all()

        if not steps:
            story.append(Paragraph("No steps defined for this plan.", self.styles['Normal']))
            return story

        for step in steps:
            # Step header
            story.append(Paragraph(
                f"Step {step.sequence}: {step.name}",
                self.styles['SubsectionHeader']
            ))

            # Step details
            step_data = [
                ['Field', 'Value'],
                ['Type', step.step_type.title()],
                ['Status', step.status.title()],
                ['Progress', f"{step.progress_percentage:.1f}%"],
                ['Duration', f"{step.estimated_duration_weeks} weeks" if step.estimated_duration_weeks else 'Not specified'],
                ['Cost', f"${step.estimated_cost:,.2f}"],
                ['Started', step.started_at.strftime('%Y-%m-%d') if step.started_at else 'Not started'],
                ['Completed', step.completed_at.strftime('%Y-%m-%d') if step.completed_at else 'Not completed']
            ]

            step_table = Table(step_data, colWidths=[1.5*inch, 3*inch])
            step_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#E8F6F3')),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(step_table)

            if step.description:
                story.append(Spacer(1, 10))
                story.append(Paragraph(f"Description: {step.description}", self.styles['Normal']))

            story.append(Spacer(1, 20))

        return story

    def _create_cost_projections_section(self, plan: CareerTransitionPlan) -> List[Any]:
        """Create cost projections section."""
        story = []

        story.append(Paragraph("Cost Projections", self.styles['SectionHeader']))

        # Get steps with costs
        steps = self.db.query(CareerTransitionStep).filter(
            CareerTransitionStep.plan_id == plan.id
        ).order_by(CareerTransitionStep.sequence).all()

        total_cost = sum(step.estimated_cost for step in steps)

        # Cost summary
        cost_data = [
            ['Component', 'Cost'],
            ['Total Estimated Cost', f"${total_cost:,.2f}"],
            ['Budget Allocated', f"${plan.budget_max:,.2f}" if plan.budget_max else 'Not specified'],
            ['Budget Remaining', f"${(plan.budget_max or 0) - total_cost:,.2f}" if plan.budget_max else 'N/A'],
            ['Cost per Month', f"${total_cost / (plan.timeline_months or 1):,.2f}" if plan.timeline_months else 'N/A']
        ]

        cost_table = Table(cost_data, colWidths=[2.5*inch, 2*inch])
        cost_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#E74C3C')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#FADBD8')),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(cost_table)
        story.append(Spacer(1, 20))

        # Step-by-step costs
        if steps:
            story.append(Paragraph("Step-by-Step Costs", self.styles['SubsectionHeader']))

            step_cost_data = [['Step', 'Name', 'Cost']]
            for step in steps:
                step_cost_data.append([
                    str(step.sequence),
                    step.name[:30] + '...' if len(step.name) > 30 else step.name,
                    f"${step.estimated_cost:,.2f}"
                ])

            step_cost_table = Table(step_cost_data, colWidths=[0.5*inch, 3*inch, 1*inch])
            step_cost_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#F39C12')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(step_cost_table)

        return story

    def _create_timeline_section(self, plan: CareerTransitionPlan) -> List[Any]:
        """Create timeline visualization section."""
        story = []

        story.append(Paragraph("Timeline & Milestones", self.styles['SectionHeader']))

        steps = self.db.query(CareerTransitionStep).filter(
            CareerTransitionStep.plan_id == plan.id
        ).order_by(CareerTransitionStep.sequence).all()

        if not steps:
            story.append(Paragraph("No timeline available - no steps defined.", self.styles['Normal']))
            return story

        # Create timeline chart
        timeline_chart = self._create_timeline_chart(steps, plan)
        story.append(timeline_chart)
        story.append(Spacer(1, 20))

        # Milestone table
        milestone_data = [['Milestone', 'Target Date', 'Status']]
        current_date = datetime.now()

        for step in steps:
            if step.estimated_duration_weeks:
                target_date = current_date + timedelta(weeks=step.estimated_duration_weeks * step.sequence)
                milestone_data.append([
                    step.name[:40] + '...' if len(step.name) > 40 else step.name,
                    target_date.strftime('%Y-%m-%d'),
                    step.status.title()
                ])

        if len(milestone_data) > 1:
            milestone_table = Table(milestone_data, colWidths=[3*inch, 1.5*inch, 1*inch])
            milestone_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#8E44AD')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(milestone_table)

        return story

    def _create_timeline_chart(self, steps: List[CareerTransitionStep], plan: CareerTransitionPlan) -> Drawing:
        """Create a timeline Gantt chart."""
        fig, ax = plt.subplots(figsize=(10, 6))

        # Prepare data
        step_names = [f"Step {step.sequence}" for step in steps]
        start_weeks = []
        durations = []

        current_week = 0
        for step in steps:
            start_weeks.append(current_week)
            duration = step.estimated_duration_weeks or 4
            durations.append(duration)
            current_week += duration

        # Create Gantt chart
        colors_list = plt.cm.Set3(np.linspace(0, 1, len(steps)))

        for i, (start, duration) in enumerate(zip(start_weeks, durations)):
            ax.barh(i, duration, left=start, height=0.6,
                   color=colors_list[i], alpha=0.8, label=step_names[i])

        ax.set_yticks(range(len(steps)))
        ax.set_yticklabels([step.name[:20] + '...' if len(step.name) > 20 else step.name
                           for step in steps])
        ax.set_xlabel('Weeks')
        ax.set_title('Career Transition Timeline', fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()

        # Convert to ReportLab image
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
        img_buffer.seek(0)
        plt.close()

        img = Image(img_buffer, width=7*inch, height=4*inch)
        return img
