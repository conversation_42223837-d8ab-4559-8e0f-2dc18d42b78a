"""Study timer service for managing study sessions, goals, and streaks.

This service provides business logic for:
- Study session management (CRUD operations)
- Study goal tracking and progress monitoring
- Study streak calculation and achievements
- Study statistics and analytics
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc, asc
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta, date
import logging

from models.study_session import StudyTimerSession, StudyGoal, StudyStreak
from models.certification import Certification
from models.learning_path import LearningPathItem
from schemas.study_session import (
    StudyTimerSessionCreate, StudyTimerSessionUpdate, StudyGoalCreate, StudyGoalUpdate,
    StudyStatistics
)

logger = logging.getLogger(__name__)


class StudyTimerService:
    """Service class for study timer functionality."""

    def __init__(self, db: Session):
        """Initialize the service with database session.
        
        Args:
            db: SQLAlchemy database session
        """
        self.db = db

    # Study Session Management
    def create_study_session(self, user_id: str, session_data: StudyTimerSessionCreate) -> StudyTimerSession:
        """Create a new study session.
        
        Args:
            user_id: ID of the user creating the session
            session_data: Session creation data
            
        Returns:
            Created study session
            
        Raises:
            ValueError: If certification or learning path item doesn't exist
        """
        # Validate certification exists if provided
        if session_data.certification_id:
            cert = self.db.query(Certification).filter(
                Certification.id == session_data.certification_id
            ).first()
            if not cert:
                raise ValueError(f"Certification with ID {session_data.certification_id} not found")

        # Validate learning path item exists if provided
        if session_data.learning_path_item_id:
            item = self.db.query(LearningPathItem).filter(
                LearningPathItem.id == session_data.learning_path_item_id
            ).first()
            if not item:
                raise ValueError(f"Learning path item with ID {session_data.learning_path_item_id} not found")

        # Create the session
        session = StudyTimerSession(
            user_id=user_id,
            certification_id=session_data.certification_id,
            learning_path_item_id=session_data.learning_path_item_id,
            planned_duration_minutes=session_data.planned_duration_minutes,
            session_type=session_data.session_type.value,
            topic=session_data.topic,
            status='planned'
        )

        self.db.add(session)
        self.db.commit()
        self.db.refresh(session)

        logger.info(f"Created study session {session.id} for user {user_id}")
        return session

    def get_study_session(self, session_id: int, user_id: str) -> Optional[StudyTimerSession]:
        """Get a study session by ID for a specific user.
        
        Args:
            session_id: ID of the session
            user_id: ID of the user
            
        Returns:
            Study session if found, None otherwise
        """
        return self.db.query(StudyTimerSession).filter(
            and_(StudyTimerSession.id == session_id, StudyTimerSession.user_id == user_id)
        ).first()

    def get_user_study_sessions(
        self, 
        user_id: str, 
        page: int = 1, 
        page_size: int = 20,
        status: Optional[str] = None,
        certification_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Tuple[List[StudyTimerSession], int]:
        """Get paginated study sessions for a user with optional filters.
        
        Args:
            user_id: ID of the user
            page: Page number (1-based)
            page_size: Number of sessions per page
            status: Optional status filter
            certification_id: Optional certification filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            Tuple of (sessions list, total count)
        """
        query = self.db.query(StudyTimerSession).filter(StudyTimerSession.user_id == user_id)

        # Apply filters
        if status:
            query = query.filter(StudyTimerSession.status == status)
        if certification_id:
            query = query.filter(StudyTimerSession.certification_id == certification_id)
        if start_date:
            query = query.filter(StudyTimerSession.start_time >= start_date)
        if end_date:
            query = query.filter(StudyTimerSession.start_time <= end_date)

        # Get total count
        total_count = query.count()

        # Apply pagination and ordering
        sessions = query.order_by(desc(StudyTimerSession.start_time)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()

        return sessions, total_count

    def update_study_session(
        self, 
        session_id: int, 
        user_id: str, 
        update_data: StudyTimerSessionUpdate
    ) -> Optional[StudyTimerSession]:
        """Update a study session.
        
        Args:
            session_id: ID of the session
            user_id: ID of the user
            update_data: Update data
            
        Returns:
            Updated session if found, None otherwise
        """
        session = self.get_study_session(session_id, user_id)
        if not session:
            return None

        # Update fields
        for field, value in update_data.dict(exclude_unset=True).items():
            setattr(session, field, value)

        self.db.commit()
        self.db.refresh(session)

        logger.info(f"Updated study session {session_id} for user {user_id}")
        return session

    def control_study_session(
        self, 
        session_id: int, 
        user_id: str, 
        action: str,
        **kwargs
    ) -> Optional[StudyTimerSession]:
        """Control a study session (start, pause, resume, complete, cancel).
        
        Args:
            session_id: ID of the session
            user_id: ID of the user
            action: Action to perform
            **kwargs: Additional parameters for the action
            
        Returns:
            Updated session if found, None otherwise
            
        Raises:
            ValueError: If action is invalid for current session state
        """
        session = self.get_study_session(session_id, user_id)
        if not session:
            return None

        try:
            if action == 'start':
                session.start_session()
            elif action == 'pause':
                session.pause_session()
            elif action == 'resume':
                session.resume_session()
            elif action == 'complete':
                session.complete_session(
                    notes=kwargs.get('notes'),
                    focus_rating=kwargs.get('focus_rating'),
                    difficulty_rating=kwargs.get('difficulty_rating'),
                    satisfaction_rating=kwargs.get('satisfaction_rating')
                )
                # Update streak when session is completed
                if session.actual_duration_minutes and session.actual_duration_minutes >= 5:
                    self._update_study_streak(user_id, session.actual_duration_minutes, session.end_time)
            elif action == 'cancel':
                session.cancel_session(kwargs.get('notes'))
            else:
                raise ValueError(f"Invalid action: {action}")

            self.db.commit()
            self.db.refresh(session)

            logger.info(f"Performed action '{action}' on session {session_id} for user {user_id}")
            return session

        except ValueError as e:
            logger.error(f"Error controlling session {session_id}: {e}")
            raise

    def delete_study_session(self, session_id: int, user_id: str) -> bool:
        """Delete a study session.
        
        Args:
            session_id: ID of the session
            user_id: ID of the user
            
        Returns:
            True if deleted, False if not found
        """
        session = self.get_study_session(session_id, user_id)
        if not session:
            return False

        self.db.delete(session)
        self.db.commit()

        logger.info(f"Deleted study session {session_id} for user {user_id}")
        return True

    # Study Goal Management
    def create_study_goal(self, user_id: str, goal_data: StudyGoalCreate) -> StudyGoal:
        """Create a new study goal.
        
        Args:
            user_id: ID of the user creating the goal
            goal_data: Goal creation data
            
        Returns:
            Created study goal
            
        Raises:
            ValueError: If certification doesn't exist
        """
        # Validate certification exists if provided
        if goal_data.certification_id:
            cert = self.db.query(Certification).filter(
                Certification.id == goal_data.certification_id
            ).first()
            if not cert:
                raise ValueError(f"Certification with ID {goal_data.certification_id} not found")

        goal = StudyGoal(
            user_id=user_id,
            certification_id=goal_data.certification_id,
            goal_type=goal_data.goal_type.value,
            title=goal_data.title,
            description=goal_data.description,
            target_value=goal_data.target_value,
            target_date=goal_data.target_date,
            reminder_enabled=goal_data.reminder_enabled
        )

        self.db.add(goal)
        self.db.commit()
        self.db.refresh(goal)

        logger.info(f"Created study goal {goal.id} for user {user_id}")
        return goal

    def get_user_study_goals(
        self, 
        user_id: str, 
        page: int = 1, 
        page_size: int = 20,
        is_active: Optional[bool] = None,
        goal_type: Optional[str] = None
    ) -> Tuple[List[StudyGoal], int]:
        """Get paginated study goals for a user with optional filters.
        
        Args:
            user_id: ID of the user
            page: Page number (1-based)
            page_size: Number of goals per page
            is_active: Optional active status filter
            goal_type: Optional goal type filter
            
        Returns:
            Tuple of (goals list, total count)
        """
        query = self.db.query(StudyGoal).filter(StudyGoal.user_id == user_id)

        # Apply filters
        if is_active is not None:
            query = query.filter(StudyGoal.is_active == is_active)
        if goal_type:
            query = query.filter(StudyGoal.goal_type == goal_type)

        # Get total count
        total_count = query.count()

        # Apply pagination and ordering
        goals = query.order_by(desc(StudyGoal.created_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()

        return goals, total_count

    def update_study_goal(
        self, 
        goal_id: int, 
        user_id: str, 
        update_data: StudyGoalUpdate
    ) -> Optional[StudyGoal]:
        """Update a study goal.
        
        Args:
            goal_id: ID of the goal
            user_id: ID of the user
            update_data: Update data
            
        Returns:
            Updated goal if found, None otherwise
        """
        goal = self.db.query(StudyGoal).filter(
            and_(StudyGoal.id == goal_id, StudyGoal.user_id == user_id)
        ).first()

        if not goal:
            return None

        # Update fields
        for field, value in update_data.dict(exclude_unset=True).items():
            setattr(goal, field, value)

        self.db.commit()
        self.db.refresh(goal)

        logger.info(f"Updated study goal {goal_id} for user {user_id}")
        return goal

    def delete_study_goal(self, goal_id: int, user_id: str) -> bool:
        """Delete a study goal.
        
        Args:
            goal_id: ID of the goal
            user_id: ID of the user
            
        Returns:
            True if deleted, False if not found
        """
        goal = self.db.query(StudyGoal).filter(
            and_(StudyGoal.id == goal_id, StudyGoal.user_id == user_id)
        ).first()

        if not goal:
            return False

        self.db.delete(goal)
        self.db.commit()

        logger.info(f"Deleted study goal {goal_id} for user {user_id}")
        return True

    # Study Streak Management
    def get_user_study_streak(self, user_id: str) -> StudyStreak:
        """Get or create study streak for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            User's study streak
        """
        streak = self.db.query(StudyStreak).filter(StudyStreak.user_id == user_id).first()
        
        if not streak:
            streak = StudyStreak(user_id=user_id)
            self.db.add(streak)
            self.db.commit()
            self.db.refresh(streak)
            logger.info(f"Created new study streak for user {user_id}")

        return streak

    def _update_study_streak(self, user_id: str, duration_minutes: int, study_date: datetime) -> None:
        """Update study streak for a user.
        
        Args:
            user_id: ID of the user
            duration_minutes: Duration of the study session
            study_date: Date of the study session
        """
        streak = self.get_user_study_streak(user_id)
        streak.add_study_session(duration_minutes, study_date)
        self.db.commit()

    # Statistics and Analytics
    def get_study_statistics(self, user_id: str) -> StudyStatistics:
        """Get comprehensive study statistics for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Study statistics
        """
        # Get basic session statistics
        session_stats = self.db.query(
            func.count(StudyTimerSession.id).label('total_sessions'),
            func.sum(StudyTimerSession.actual_duration_minutes).label('total_minutes'),
            func.avg(StudyTimerSession.actual_duration_minutes).label('avg_duration'),
            func.avg(StudyTimerSession.focus_rating).label('avg_focus'),
            func.avg(StudyTimerSession.difficulty_rating).label('avg_difficulty'),
            func.avg(StudyTimerSession.satisfaction_rating).label('avg_satisfaction')
        ).filter(
            and_(
                StudyTimerSession.user_id == user_id,
                StudyTimerSession.status == 'completed',
                StudyTimerSession.actual_duration_minutes.isnot(None)
            )
        ).first()

        # Get streak information
        streak = self.get_user_study_streak(user_id)

        # Get this week's statistics
        week_start = datetime.now() - timedelta(days=datetime.now().weekday())
        week_stats = self.db.query(
            func.count(StudyTimerSession.id).label('sessions_this_week'),
            func.sum(StudyTimerSession.actual_duration_minutes).label('minutes_this_week')
        ).filter(
            and_(
                StudyTimerSession.user_id == user_id,
                StudyTimerSession.status == 'completed',
                StudyTimerSession.start_time >= week_start,
                StudyTimerSession.actual_duration_minutes.isnot(None)
            )
        ).first()

        # Get this month's statistics
        month_start = datetime.now().replace(day=1)
        month_stats = self.db.query(
            func.count(StudyTimerSession.id).label('sessions_this_month'),
            func.sum(StudyTimerSession.actual_duration_minutes).label('minutes_this_month')
        ).filter(
            and_(
                StudyTimerSession.user_id == user_id,
                StudyTimerSession.status == 'completed',
                StudyTimerSession.start_time >= month_start,
                StudyTimerSession.actual_duration_minutes.isnot(None)
            )
        ).first()

        return StudyStatistics(
            total_sessions=session_stats.total_sessions or 0,
            total_study_minutes=session_stats.total_minutes or 0,
            average_session_duration=session_stats.avg_duration or 0.0,
            total_study_days=streak.total_study_days,
            current_streak=streak.current_streak_days,
            longest_streak=streak.longest_streak_days,
            sessions_this_week=week_stats.sessions_this_week or 0,
            minutes_this_week=week_stats.minutes_this_week or 0,
            sessions_this_month=month_stats.sessions_this_month or 0,
            minutes_this_month=month_stats.minutes_this_month or 0,
            focus_rating_average=session_stats.avg_focus,
            difficulty_rating_average=session_stats.avg_difficulty,
            satisfaction_rating_average=session_stats.avg_satisfaction
        )
