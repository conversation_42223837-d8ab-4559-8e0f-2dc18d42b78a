"""Audit service for tracking database changes and user actions.

This module provides comprehensive audit trail functionality following
PEP 8, 257, and 484 standards.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import json
import logging

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class AuditLogEntry(BaseModel):
    """Audit log entry data structure.
    
    Attributes:
        table_name: Name of the affected table
        record_id: ID of the affected record
        operation: Type of operation (INSERT, UPDATE, DELETE)
        old_values: Previous values (for updates and deletes)
        new_values: New values (for inserts and updates)
        changed_fields: List of fields that were changed
        user_id: ID of user who performed the operation
        user_role: Role of the user
        ip_address: IP address of the request
        user_agent: User agent string
        session_id: Session identifier
        timestamp: When the operation occurred
    """
    
    table_name: str
    record_id: int
    operation: str
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    changed_fields: List[str] = []
    user_id: Optional[str] = None
    user_role: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    session_id: Optional[str] = None
    timestamp: datetime = datetime.utcnow()


class AuditService:
    """Service for managing audit trails and change tracking.
    
    This service provides methods to log database changes, track user
    actions, and query audit history with proper error handling and
    performance optimization.
    """
    
    def __init__(self, db: Session):
        """Initialize audit service.
        
        Args:
            db: Database session for audit operations
        """
        self.db = db
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def log_create(
        self,
        table_name: str,
        record_id: int,
        new_values: Dict[str, Any],
        user_id: Optional[str] = None,
        user_role: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """Log a CREATE operation.
        
        Args:
            table_name: Name of the affected table
            record_id: ID of the created record
            new_values: Values of the created record
            user_id: ID of user who performed the operation
            user_role: Role of the user
            ip_address: IP address of the request
            user_agent: User agent string
            session_id: Session identifier
            
        Returns:
            True if logging was successful, False otherwise
        """
        try:
            audit_entry = AuditLogEntry(
                table_name=table_name,
                record_id=record_id,
                operation='INSERT',
                new_values=self._sanitize_values(new_values),
                user_id=user_id,
                user_role=user_role,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            return self._write_audit_log(audit_entry)
            
        except Exception as e:
            self.logger.error(
                f"Failed to log CREATE operation: {str(e)}",
                extra={
                    'table_name': table_name,
                    'record_id': record_id,
                    'user_id': user_id
                }
            )
            return False
    
    def log_update(
        self,
        table_name: str,
        record_id: int,
        old_values: Dict[str, Any],
        new_values: Dict[str, Any],
        user_id: Optional[str] = None,
        user_role: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """Log an UPDATE operation.
        
        Args:
            table_name: Name of the affected table
            record_id: ID of the updated record
            old_values: Previous values of the record
            new_values: New values of the record
            user_id: ID of user who performed the operation
            user_role: Role of the user
            ip_address: IP address of the request
            user_agent: User agent string
            session_id: Session identifier
            
        Returns:
            True if logging was successful, False otherwise
        """
        try:
            # Identify changed fields
            changed_fields = self._identify_changed_fields(old_values, new_values)
            
            if not changed_fields:
                # No actual changes, skip logging
                return True
            
            audit_entry = AuditLogEntry(
                table_name=table_name,
                record_id=record_id,
                operation='UPDATE',
                old_values=self._sanitize_values(old_values),
                new_values=self._sanitize_values(new_values),
                changed_fields=changed_fields,
                user_id=user_id,
                user_role=user_role,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            return self._write_audit_log(audit_entry)
            
        except Exception as e:
            self.logger.error(
                f"Failed to log UPDATE operation: {str(e)}",
                extra={
                    'table_name': table_name,
                    'record_id': record_id,
                    'user_id': user_id
                }
            )
            return False
    
    def log_delete(
        self,
        table_name: str,
        record_id: int,
        old_values: Dict[str, Any],
        user_id: Optional[str] = None,
        user_role: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        soft_delete: bool = False
    ) -> bool:
        """Log a DELETE operation.
        
        Args:
            table_name: Name of the affected table
            record_id: ID of the deleted record
            old_values: Values of the record before deletion
            user_id: ID of user who performed the operation
            user_role: Role of the user
            ip_address: IP address of the request
            user_agent: User agent string
            session_id: Session identifier
            soft_delete: Whether this was a soft delete operation
            
        Returns:
            True if logging was successful, False otherwise
        """
        try:
            operation = 'SOFT_DELETE' if soft_delete else 'DELETE'
            
            audit_entry = AuditLogEntry(
                table_name=table_name,
                record_id=record_id,
                operation=operation,
                old_values=self._sanitize_values(old_values),
                user_id=user_id,
                user_role=user_role,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            return self._write_audit_log(audit_entry)
            
        except Exception as e:
            self.logger.error(
                f"Failed to log DELETE operation: {str(e)}",
                extra={
                    'table_name': table_name,
                    'record_id': record_id,
                    'user_id': user_id
                }
            )
            return False
    
    def _sanitize_values(self, values: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize values for audit logging.
        
        Removes sensitive information and converts non-serializable types.
        
        Args:
            values: Dictionary of values to sanitize
            
        Returns:
            Sanitized dictionary
        """
        if not values:
            return {}
        
        sanitized = {}
        sensitive_fields = {
            'password', 'token', 'secret', 'key', 'credential',
            'auth', 'session', 'cookie'
        }
        
        for key, value in values.items():
            # Skip sensitive fields
            if any(sensitive in key.lower() for sensitive in sensitive_fields):
                sanitized[key] = '[REDACTED]'
                continue
            
            # Handle different value types
            if value is None:
                sanitized[key] = None
            elif isinstance(value, (str, int, float, bool)):
                sanitized[key] = value
            elif isinstance(value, datetime):
                sanitized[key] = value.isoformat()
            elif isinstance(value, (list, dict)):
                try:
                    # Ensure JSON serializable
                    json.dumps(value)
                    sanitized[key] = value
                except (TypeError, ValueError):
                    sanitized[key] = str(value)
            else:
                sanitized[key] = str(value)
        
        return sanitized
    
    def _identify_changed_fields(
        self,
        old_values: Dict[str, Any],
        new_values: Dict[str, Any]
    ) -> List[str]:
        """Identify which fields have changed between old and new values.
        
        Args:
            old_values: Previous values
            new_values: New values
            
        Returns:
            List of field names that changed
        """
        changed_fields = []
        
        # Check all fields in new values
        for key, new_value in new_values.items():
            old_value = old_values.get(key)
            
            # Handle different comparison scenarios
            if old_value != new_value:
                # Special handling for datetime objects
                if isinstance(old_value, datetime) and isinstance(new_value, datetime):
                    # Compare with some tolerance for microseconds
                    if abs((old_value - new_value).total_seconds()) > 1:
                        changed_fields.append(key)
                else:
                    changed_fields.append(key)
        
        # Check for removed fields (present in old but not in new)
        for key in old_values.keys():
            if key not in new_values:
                changed_fields.append(key)
        
        return changed_fields
    
    def _write_audit_log(self, audit_entry: AuditLogEntry) -> bool:
        """Write audit log entry to database.
        
        Args:
            audit_entry: Audit log entry to write
            
        Returns:
            True if write was successful, False otherwise
        """
        try:
            # For now, we'll use the existing database session
            # In a production environment, you might want to use a separate
            # audit database or logging system
            
            # Convert to dictionary for database storage
            audit_data = audit_entry.model_dump()
            
            # Log to application logger as well
            self.logger.info(
                f"Audit: {audit_entry.operation} on {audit_entry.table_name}",
                extra={
                    'audit_data': audit_data,
                    'table_name': audit_entry.table_name,
                    'record_id': audit_entry.record_id,
                    'operation': audit_entry.operation,
                    'user_id': audit_entry.user_id
                }
            )
            
            # TODO: Implement actual database storage for audit logs
            # This would typically involve:
            # 1. Creating an audit_log table
            # 2. Inserting the audit entry
            # 3. Handling any database errors
            
            return True
            
        except Exception as e:
            self.logger.error(
                f"Failed to write audit log: {str(e)}",
                extra={'audit_entry': audit_entry.model_dump()}
            )
            return False
