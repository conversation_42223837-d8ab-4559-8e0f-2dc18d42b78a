"""LDAP Synchronization Service for enterprise directory integration.

This service provides comprehensive LDAP/Active Directory synchronization
capabilities including user and group sync, organizational structure mapping,
and automated user provisioning with role assignment.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import json
import uuid
import ldap3
from ldap3 import Server, Connection, ALL, SUBTREE, MODIFY_REPLACE
from ldap3.core.exceptions import LDAPException
import asyncio
from concurrent.futures import ThreadPoolExecutor

from models.enterprise import EnterpriseOrganization, EnterpriseUser, UserLicense
from services.integration_hub_service import IntegrationHubService, SyncDirection

logger = logging.getLogger(__name__)


class LDAPSyncService:
    """Service for LDAP/Active Directory synchronization."""
    
    def __init__(self, db: Session):
        self.db = db
        self.integration_service = IntegrationHubService(db)
        self.executor = ThreadPoolExecutor(max_workers=5)
    
    # LDAP Connection Management
    
    def connect_to_ldap(self, ldap_config: Dict[str, Any]) -> Optional[Connection]:
        """Establish connection to LDAP server."""
        try:
            # Create LDAP server object
            server = Server(
                ldap_config['server_url'],
                get_info=ALL,
                use_ssl=ldap_config.get('use_ssl', True),
                port=ldap_config.get('port', 636 if ldap_config.get('use_ssl', True) else 389)
            )
            
            # Create connection
            connection = Connection(
                server,
                user=ldap_config['bind_dn'],
                password=ldap_config['bind_password'],
                auto_bind=True,
                auto_range=True
            )
            
            logger.info(f"Successfully connected to LDAP server: {ldap_config['server_url']}")
            return connection
        
        except LDAPException as e:
            logger.error(f"LDAP connection error: {e}")
            return None
        except Exception as e:
            logger.error(f"Error connecting to LDAP: {e}")
            return None
    
    def test_ldap_connection(self, ldap_config: Dict[str, Any]) -> Dict[str, Any]:
        """Test LDAP connection and return status."""
        try:
            connection = self.connect_to_ldap(ldap_config)
            if not connection:
                return {'success': False, 'error': 'Failed to establish connection'}
            
            # Test search operation
            search_result = connection.search(
                search_base=ldap_config['user_base_dn'],
                search_filter='(objectClass=*)',
                search_scope=SUBTREE,
                size_limit=1
            )
            
            connection.unbind()
            
            if search_result:
                return {
                    'success': True,
                    'message': 'LDAP connection successful',
                    'server_info': {
                        'vendor': connection.server.info.vendor_name if connection.server.info else 'Unknown',
                        'version': connection.server.info.vendor_version if connection.server.info else 'Unknown'
                    }
                }
            else:
                return {'success': False, 'error': 'Search operation failed'}
        
        except Exception as e:
            logger.error(f"LDAP connection test failed: {e}")
            return {'success': False, 'error': str(e)}
    
    # User Synchronization
    
    async def sync_ldap_users(self, integration_id: str) -> Dict[str, Any]:
        """Synchronize users from LDAP directory."""
        try:
            # Get LDAP integration configuration
            integration = self._get_ldap_integration(integration_id)
            if not integration:
                return {'error': 'LDAP integration not found'}
            
            # Decrypt configuration
            ldap_config = self.integration_service._decrypt_sensitive_data(integration['configuration'])
            
            # Connect to LDAP
            connection = self.connect_to_ldap(ldap_config)
            if not connection:
                return {'error': 'Failed to connect to LDAP server'}
            
            # Search for users
            users = await self._search_ldap_users(connection, integration)
            
            # Synchronize users
            sync_results = await self._process_user_sync(integration, users)
            
            # Update integration last sync time
            self._update_integration_sync_time(integration_id)
            
            # Close connection
            connection.unbind()
            
            logger.info(f"LDAP user sync completed for integration {integration_id}")
            return sync_results
        
        except Exception as e:
            logger.error(f"Error syncing LDAP users: {e}")
            return {'error': str(e)}
    
    async def _search_ldap_users(self, connection: Connection, integration: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for users in LDAP directory."""
        try:
            users = []
            
            # Perform search
            search_result = connection.search(
                search_base=integration['user_base_dn'],
                search_filter=integration['user_filter'],
                search_scope=SUBTREE,
                attributes=['*']
            )
            
            if search_result:
                for entry in connection.entries:
                    user_data = self._extract_user_attributes(entry, integration['attribute_mapping'])
                    if user_data:
                        users.append(user_data)
            
            logger.info(f"Found {len(users)} users in LDAP directory")
            return users
        
        except Exception as e:
            logger.error(f"Error searching LDAP users: {e}")
            return []
    
    def _extract_user_attributes(self, ldap_entry: Any, attribute_mapping: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """Extract user attributes from LDAP entry."""
        try:
            user_data = {
                'dn': str(ldap_entry.entry_dn),
                'attributes': {}
            }
            
            # Map LDAP attributes to user profile
            for target_attr, ldap_attr in attribute_mapping.items():
                if hasattr(ldap_entry, ldap_attr):
                    value = getattr(ldap_entry, ldap_attr).value
                    if value:
                        user_data['attributes'][target_attr] = value
            
            # Extract common attributes
            common_mappings = {
                'email': ['mail', 'userPrincipalName', 'email'],
                'first_name': ['givenName', 'firstName'],
                'last_name': ['sn', 'surname', 'lastName'],
                'display_name': ['displayName', 'cn', 'name'],
                'employee_id': ['employeeID', 'employeeNumber'],
                'department': ['department', 'departmentNumber'],
                'title': ['title', 'jobTitle'],
                'manager': ['manager'],
                'phone': ['telephoneNumber', 'mobile'],
                'office': ['physicalDeliveryOfficeName', 'office']
            }
            
            for target_attr, possible_ldap_attrs in common_mappings.items():
                if target_attr not in user_data['attributes']:
                    for ldap_attr in possible_ldap_attrs:
                        if hasattr(ldap_entry, ldap_attr):
                            value = getattr(ldap_entry, ldap_attr).value
                            if value:
                                user_data['attributes'][target_attr] = value
                                break
            
            # Ensure we have at least an email
            if not user_data['attributes'].get('email'):
                logger.warning(f"No email found for LDAP entry: {ldap_entry.entry_dn}")
                return None
            
            return user_data
        
        except Exception as e:
            logger.error(f"Error extracting user attributes: {e}")
            return None
    
    async def _process_user_sync(self, integration: Dict[str, Any], ldap_users: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process user synchronization with database."""
        try:
            sync_results = {
                'sync_id': str(uuid.uuid4()),
                'integration_id': integration['integration_id'],
                'users_found': len(ldap_users),
                'users_created': 0,
                'users_updated': 0,
                'users_deactivated': 0,
                'errors': [],
                'sync_started': datetime.utcnow().isoformat()
            }
            
            # Process users in batches
            batch_size = 50
            for i in range(0, len(ldap_users), batch_size):
                batch = ldap_users[i:i + batch_size]
                batch_results = await self._process_user_batch(integration, batch)
                
                sync_results['users_created'] += batch_results['created']
                sync_results['users_updated'] += batch_results['updated']
                sync_results['errors'].extend(batch_results['errors'])
            
            # Handle user deactivation if sync direction allows
            if integration['sync_direction'] in [SyncDirection.INBOUND.value, SyncDirection.BIDIRECTIONAL.value]:
                deactivated_count = await self._deactivate_missing_users(integration, ldap_users)
                sync_results['users_deactivated'] = deactivated_count
            
            sync_results['sync_completed'] = datetime.utcnow().isoformat()
            return sync_results
        
        except Exception as e:
            logger.error(f"Error processing user sync: {e}")
            return {'error': str(e)}
    
    async def _process_user_batch(self, integration: Dict[str, Any], user_batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process a batch of users for synchronization."""
        batch_results = {
            'created': 0,
            'updated': 0,
            'errors': []
        }
        
        for ldap_user in user_batch:
            try:
                result = await self._sync_individual_user(integration, ldap_user)
                if result['action'] == 'created':
                    batch_results['created'] += 1
                elif result['action'] == 'updated':
                    batch_results['updated'] += 1
            except Exception as e:
                batch_results['errors'].append({
                    'user_dn': ldap_user.get('dn', 'unknown'),
                    'error': str(e)
                })
        
        return batch_results
    
    async def _sync_individual_user(self, integration: Dict[str, Any], ldap_user: Dict[str, Any]) -> Dict[str, Any]:
        """Synchronize an individual user."""
        try:
            user_attrs = ldap_user['attributes']
            email = user_attrs.get('email')
            
            if not email:
                raise ValueError("User email is required")
            
            # Check if user exists
            existing_user = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.email == email,
                EnterpriseUser.organization_id == integration['organization_id']
            ).first()
            
            if existing_user:
                # Update existing user
                updated = self._update_user_from_ldap(existing_user, user_attrs, integration)
                return {'action': 'updated' if updated else 'no_change', 'user_id': existing_user.user_id}
            else:
                # Create new user if auto-provisioning is enabled
                if integration['auto_provision_users']:
                    new_user = self._create_user_from_ldap(user_attrs, integration)
                    return {'action': 'created', 'user_id': new_user.user_id}
                else:
                    return {'action': 'skipped', 'reason': 'auto_provision_disabled'}
        
        except Exception as e:
            logger.error(f"Error syncing individual user: {e}")
            raise
    
    def _update_user_from_ldap(self, user: EnterpriseUser, ldap_attrs: Dict[str, Any], integration: Dict[str, Any]) -> bool:
        """Update existing user with LDAP attributes."""
        updated = False
        
        # Update user attributes
        if ldap_attrs.get('first_name') and user.first_name != ldap_attrs['first_name']:
            user.first_name = ldap_attrs['first_name']
            updated = True
        
        if ldap_attrs.get('last_name') and user.last_name != ldap_attrs['last_name']:
            user.last_name = ldap_attrs['last_name']
            updated = True
        
        if ldap_attrs.get('display_name') and user.display_name != ldap_attrs['display_name']:
            user.display_name = ldap_attrs['display_name']
            updated = True
        
        # Update LDAP-specific fields
        user.ldap_dn = ldap_attrs.get('dn')
        user.last_ldap_sync = datetime.utcnow()
        
        if updated:
            user.updated_at = datetime.utcnow()
            self.db.commit()
        
        return updated
    
    def _create_user_from_ldap(self, ldap_attrs: Dict[str, Any], integration: Dict[str, Any]) -> EnterpriseUser:
        """Create new user from LDAP attributes."""
        new_user = EnterpriseUser(
            user_id=str(uuid.uuid4()),
            organization_id=integration['organization_id'],
            email=ldap_attrs['email'],
            first_name=ldap_attrs.get('first_name', ''),
            last_name=ldap_attrs.get('last_name', ''),
            display_name=ldap_attrs.get('display_name', f"{ldap_attrs.get('first_name', '')} {ldap_attrs.get('last_name', '')}".strip()),
            role=integration.get('default_role', 'learner'),
            is_active=True,
            created_via_ldap=True,
            ldap_dn=ldap_attrs.get('dn'),
            last_ldap_sync=datetime.utcnow(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(new_user)
        self.db.commit()
        
        logger.info(f"Created new user from LDAP: {new_user.email}")
        return new_user
    
    async def _deactivate_missing_users(self, integration: Dict[str, Any], ldap_users: List[Dict[str, Any]]) -> int:
        """Deactivate users that are no longer in LDAP."""
        try:
            # Get emails of all LDAP users
            ldap_emails = {user['attributes']['email'] for user in ldap_users if user['attributes'].get('email')}
            
            # Find users in database that were created via LDAP but not in current sync
            db_users = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.organization_id == integration['organization_id'],
                EnterpriseUser.created_via_ldap == True,
                EnterpriseUser.is_active == True
            ).all()
            
            deactivated_count = 0
            for user in db_users:
                if user.email not in ldap_emails:
                    user.is_active = False
                    user.deactivated_at = datetime.utcnow()
                    user.deactivation_reason = 'ldap_sync_missing'
                    deactivated_count += 1
            
            if deactivated_count > 0:
                self.db.commit()
                logger.info(f"Deactivated {deactivated_count} users missing from LDAP")
            
            return deactivated_count
        
        except Exception as e:
            logger.error(f"Error deactivating missing users: {e}")
            return 0
    
    # Group Synchronization
    
    async def sync_ldap_groups(self, integration_id: str) -> Dict[str, Any]:
        """Synchronize groups from LDAP directory."""
        try:
            # Get LDAP integration configuration
            integration = self._get_ldap_integration(integration_id)
            if not integration:
                return {'error': 'LDAP integration not found'}
            
            # Check if group sync is enabled
            if not integration.get('group_base_dn'):
                return {'error': 'Group synchronization not configured'}
            
            # Decrypt configuration
            ldap_config = self.integration_service._decrypt_sensitive_data(integration['configuration'])
            
            # Connect to LDAP
            connection = self.connect_to_ldap(ldap_config)
            if not connection:
                return {'error': 'Failed to connect to LDAP server'}
            
            # Search for groups
            groups = await self._search_ldap_groups(connection, integration)
            
            # Synchronize groups
            sync_results = await self._process_group_sync(integration, groups)
            
            # Close connection
            connection.unbind()
            
            logger.info(f"LDAP group sync completed for integration {integration_id}")
            return sync_results
        
        except Exception as e:
            logger.error(f"Error syncing LDAP groups: {e}")
            return {'error': str(e)}
    
    async def _search_ldap_groups(self, connection: Connection, integration: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for groups in LDAP directory."""
        try:
            groups = []
            
            # Perform search
            search_result = connection.search(
                search_base=integration['group_base_dn'],
                search_filter=integration['group_filter'],
                search_scope=SUBTREE,
                attributes=['*']
            )
            
            if search_result:
                for entry in connection.entries:
                    group_data = self._extract_group_attributes(entry)
                    if group_data:
                        groups.append(group_data)
            
            logger.info(f"Found {len(groups)} groups in LDAP directory")
            return groups
        
        except Exception as e:
            logger.error(f"Error searching LDAP groups: {e}")
            return []
    
    def _extract_group_attributes(self, ldap_entry: Any) -> Optional[Dict[str, Any]]:
        """Extract group attributes from LDAP entry."""
        try:
            group_data = {
                'dn': str(ldap_entry.entry_dn),
                'name': getattr(ldap_entry, 'cn', getattr(ldap_entry, 'name', None)).value,
                'description': getattr(ldap_entry, 'description', None),
                'members': []
            }
            
            # Extract group members
            if hasattr(ldap_entry, 'member'):
                members = getattr(ldap_entry, 'member').value
                if isinstance(members, list):
                    group_data['members'] = members
                elif members:
                    group_data['members'] = [members]
            
            return group_data
        
        except Exception as e:
            logger.error(f"Error extracting group attributes: {e}")
            return None
    
    async def _process_group_sync(self, integration: Dict[str, Any], ldap_groups: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process group synchronization with database."""
        try:
            sync_results = {
                'sync_id': str(uuid.uuid4()),
                'integration_id': integration['integration_id'],
                'groups_found': len(ldap_groups),
                'groups_created': 0,
                'groups_updated': 0,
                'group_memberships_updated': 0,
                'errors': [],
                'sync_started': datetime.utcnow().isoformat()
            }
            
            # Process groups
            for ldap_group in ldap_groups:
                try:
                    result = await self._sync_individual_group(integration, ldap_group)
                    if result['action'] == 'created':
                        sync_results['groups_created'] += 1
                    elif result['action'] == 'updated':
                        sync_results['groups_updated'] += 1
                    
                    sync_results['group_memberships_updated'] += result.get('memberships_updated', 0)
                except Exception as e:
                    sync_results['errors'].append({
                        'group_dn': ldap_group.get('dn', 'unknown'),
                        'error': str(e)
                    })
            
            sync_results['sync_completed'] = datetime.utcnow().isoformat()
            return sync_results
        
        except Exception as e:
            logger.error(f"Error processing group sync: {e}")
            return {'error': str(e)}
    
    async def _sync_individual_group(self, integration: Dict[str, Any], ldap_group: Dict[str, Any]) -> Dict[str, Any]:
        """Synchronize an individual group."""
        # Implementation would handle group creation/update and membership management
        # For now, return mock result
        return {
            'action': 'updated',
            'group_id': 'group_123',
            'memberships_updated': len(ldap_group.get('members', []))
        }
    
    # Helper Methods
    
    def _get_ldap_integration(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get LDAP integration configuration."""
        # Mock implementation - would query from database
        return {
            'integration_id': integration_id,
            'organization_id': 1,
            'server_url': 'ldaps://dc.company.com:636',
            'user_base_dn': 'OU=Users,DC=company,DC=com',
            'group_base_dn': 'OU=Groups,DC=company,DC=com',
            'user_filter': '(objectClass=person)',
            'group_filter': '(objectClass=group)',
            'sync_direction': SyncDirection.INBOUND.value,
            'auto_provision_users': True,
            'default_role': 'learner',
            'attribute_mapping': {
                'email': 'mail',
                'first_name': 'givenName',
                'last_name': 'sn',
                'display_name': 'displayName'
            },
            'configuration': 'encrypted_config_data'
        }
    
    def _update_integration_sync_time(self, integration_id: str):
        """Update integration last sync time."""
        # Would update in database
        logger.info(f"Updated last sync time for integration {integration_id}")
    
    # Scheduled Sync Management
    
    async def schedule_ldap_sync(self, integration_id: str, frequency_hours: int = 6):
        """Schedule periodic LDAP synchronization."""
        try:
            while True:
                await asyncio.sleep(frequency_hours * 3600)  # Convert hours to seconds
                
                logger.info(f"Starting scheduled LDAP sync for integration {integration_id}")
                
                # Perform user sync
                user_sync_result = await self.sync_ldap_users(integration_id)
                
                # Perform group sync if configured
                group_sync_result = await self.sync_ldap_groups(integration_id)
                
                logger.info(f"Scheduled LDAP sync completed for integration {integration_id}")
        
        except Exception as e:
            logger.error(f"Error in scheduled LDAP sync: {e}")
    
    def start_scheduled_sync(self, integration_id: str, frequency_hours: int = 6):
        """Start scheduled LDAP synchronization in background."""
        asyncio.create_task(self.schedule_ldap_sync(integration_id, frequency_hours))
