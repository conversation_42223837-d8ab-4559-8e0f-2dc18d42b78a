"""Base CRUD service providing common database operations.

This module implements a generic CRUD service following PEP 8, 257, and 484
standards with comprehensive type hints and documentation.
"""

from abc import ABC, abstractmethod
from typing import (
    TypeVar, Generic, List, Optional, Dict, Any, Type, Union, Sequence
)
from datetime import datetime
import logging

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy import and_, or_, func, desc, asc
from pydantic import BaseModel, ValidationError

from models.base import Base
from models.mixins import TimestampMixin, SoftDeleteMixin

# Type variables for generic CRUD operations
ModelType = TypeVar('ModelType', bound=Base)
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)
ResponseSchemaType = TypeVar('ResponseSchemaType', bound=BaseModel)

logger = logging.getLogger(__name__)


class CRUDError(Exception):
    """Base exception for CRUD operations."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Initialize CRUD error with message and optional details.
        
        Args:
            message: Error message describing what went wrong
            details: Optional dictionary with additional error context
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}


class ValidationError(CRUDError):
    """Exception raised when data validation fails."""
    pass


class NotFoundError(CRUDError):
    """Exception raised when requested resource is not found."""
    pass


class ConflictError(CRUDError):
    """Exception raised when operation conflicts with existing data."""
    pass


class PaginationParams(BaseModel):
    """Parameters for paginated queries.
    
    Attributes:
        page: Page number (1-based)
        page_size: Number of items per page
        max_page_size: Maximum allowed page size
    """
    
    page: int = 1
    page_size: int = 20
    max_page_size: int = 100
    
    def __post_init__(self) -> None:
        """Validate pagination parameters."""
        if self.page < 1:
            raise ValueError("Page number must be >= 1")
        if self.page_size < 1:
            raise ValueError("Page size must be >= 1")
        if self.page_size > self.max_page_size:
            self.page_size = self.max_page_size


class SortParams(BaseModel):
    """Parameters for sorting query results.
    
    Attributes:
        sort_by: Field name to sort by
        sort_order: Sort direction ('asc' or 'desc')
    """
    
    sort_by: Optional[str] = None
    sort_order: str = 'asc'
    
    def __post_init__(self) -> None:
        """Validate sort parameters."""
        if self.sort_order not in ('asc', 'desc'):
            raise ValueError("Sort order must be 'asc' or 'desc'")


class FilterParams(BaseModel):
    """Base parameters for filtering query results.
    
    Attributes:
        search: Global search term
        search_fields: Fields to search in
        include_deleted: Whether to include soft-deleted records
    """
    
    search: Optional[str] = None
    search_fields: List[str] = []
    include_deleted: bool = False


class PaginatedResponse(BaseModel, Generic[ResponseSchemaType]):
    """Paginated response wrapper.
    
    Attributes:
        data: List of response items
        pagination: Pagination metadata
        total_count: Total number of items
        filters_applied: Filters that were applied
    """
    
    data: List[ResponseSchemaType]
    pagination: Dict[str, Any]
    total_count: int
    filters_applied: Dict[str, Any]


class BaseCRUDService(
    Generic[ModelType, CreateSchemaType, UpdateSchemaType, ResponseSchemaType],
    ABC
):
    """Abstract base class for CRUD operations.
    
    This class provides a standardized interface for Create, Read, Update,
    and Delete operations on database models with proper error handling,
    validation, and audit trails.
    
    Type Parameters:
        ModelType: SQLAlchemy model class
        CreateSchemaType: Pydantic schema for creation
        UpdateSchemaType: Pydantic schema for updates
        ResponseSchemaType: Pydantic schema for responses
    """
    
    def __init__(
        self,
        db: Session,
        model: Type[ModelType],
        response_schema: Type[ResponseSchemaType]
    ):
        """Initialize CRUD service.
        
        Args:
            db: Database session
            model: SQLAlchemy model class
            response_schema: Pydantic response schema class
        """
        self.db = db
        self.model = model
        self.response_schema = response_schema
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Set up logging for the service."""
        self.logger = logging.getLogger(
            f"{self.__class__.__module__}.{self.__class__.__name__}"
        )
    
    @abstractmethod
    def _validate_create_data(
        self, 
        obj_in: CreateSchemaType
    ) -> CreateSchemaType:
        """Validate data for creation.
        
        Args:
            obj_in: Input data for creation
            
        Returns:
            Validated input data
            
        Raises:
            ValidationError: If validation fails
        """
        pass
    
    @abstractmethod
    def _validate_update_data(
        self,
        obj_in: UpdateSchemaType,
        current_obj: ModelType
    ) -> UpdateSchemaType:
        """Validate data for update.
        
        Args:
            obj_in: Input data for update
            current_obj: Current database object
            
        Returns:
            Validated input data
            
        Raises:
            ValidationError: If validation fails
        """
        pass
    
    def _apply_filters(
        self,
        query: Any,
        filters: FilterParams
    ) -> Any:
        """Apply filters to query.
        
        Args:
            query: SQLAlchemy query object
            filters: Filter parameters
            
        Returns:
            Filtered query object
        """
        # Handle soft delete filtering
        if hasattr(self.model, 'is_deleted') and not filters.include_deleted:
            query = query.filter(self.model.is_deleted == False)  # noqa: E712
        
        # Handle global search
        if filters.search and filters.search_fields:
            search_conditions = []
            for field_name in filters.search_fields:
                if hasattr(self.model, field_name):
                    field = getattr(self.model, field_name)
                    search_conditions.append(
                        field.ilike(f"%{filters.search}%")
                    )
            
            if search_conditions:
                query = query.filter(or_(*search_conditions))
        
        return query
    
    def _apply_sorting(
        self,
        query: Any,
        sort_params: SortParams
    ) -> Any:
        """Apply sorting to query.
        
        Args:
            query: SQLAlchemy query object
            sort_params: Sort parameters
            
        Returns:
            Sorted query object
        """
        if sort_params.sort_by and hasattr(self.model, sort_params.sort_by):
            field = getattr(self.model, sort_params.sort_by)
            if sort_params.sort_order == 'desc':
                query = query.order_by(desc(field))
            else:
                query = query.order_by(asc(field))
        
        return query
    
    def _build_pagination_info(
        self,
        pagination: PaginationParams,
        total_count: int
    ) -> Dict[str, Any]:
        """Build pagination metadata.
        
        Args:
            pagination: Pagination parameters
            total_count: Total number of items
            
        Returns:
            Pagination metadata dictionary
        """
        total_pages = (total_count + pagination.page_size - 1) // pagination.page_size
        
        return {
            'page': pagination.page,
            'page_size': pagination.page_size,
            'total_items': total_count,
            'total_pages': total_pages,
            'has_next': pagination.page < total_pages,
            'has_previous': pagination.page > 1
        }
    
    def create(
        self,
        obj_in: CreateSchemaType,
        user_id: Optional[str] = None
    ) -> ResponseSchemaType:
        """Create a new entity.
        
        Args:
            obj_in: Input data for creation
            user_id: ID of user performing the operation
            
        Returns:
            Created entity response
            
        Raises:
            ValidationError: If input validation fails
            ConflictError: If creation conflicts with existing data
            CRUDError: If database operation fails
        """
        try:
            # Validate input data
            validated_data = self._validate_create_data(obj_in)
            
            # Create database object
            db_obj_data = validated_data.model_dump()
            
            # Add audit fields if model supports them
            if hasattr(self.model, 'created_by') and user_id:
                db_obj_data['created_by'] = user_id
            
            db_obj = self.model(**db_obj_data)
            
            # Save to database
            self.db.add(db_obj)
            self.db.commit()
            self.db.refresh(db_obj)
            
            self.logger.info(
                f"Created {self.model.__name__} with ID {db_obj.id}",
                extra={'user_id': user_id, 'entity_id': db_obj.id}
            )
            
            return self.response_schema.model_validate(db_obj)
            
        except ValidationError:
            self.db.rollback()
            raise
        except IntegrityError as e:
            self.db.rollback()
            raise ConflictError(
                f"Creation failed due to data conflict: {str(e)}",
                details={'original_error': str(e)}
            )
        except SQLAlchemyError as e:
            self.db.rollback()
            raise CRUDError(
                f"Database error during creation: {str(e)}",
                details={'original_error': str(e)}
            )
    
    def get(
        self,
        entity_id: int,
        include_deleted: bool = False
    ) -> Optional[ResponseSchemaType]:
        """Get entity by ID.
        
        Args:
            entity_id: ID of entity to retrieve
            include_deleted: Whether to include soft-deleted entities
            
        Returns:
            Entity response or None if not found
        """
        try:
            query = self.db.query(self.model).filter(self.model.id == entity_id)
            
            # Handle soft delete filtering
            if hasattr(self.model, 'is_deleted') and not include_deleted:
                query = query.filter(self.model.is_deleted == False)  # noqa: E712
            
            db_obj = query.first()
            
            if db_obj is None:
                return None
            
            return self.response_schema.model_validate(db_obj)
            
        except SQLAlchemyError as e:
            raise CRUDError(
                f"Database error during retrieval: {str(e)}",
                details={'entity_id': entity_id, 'original_error': str(e)}
            )
    
    def get_or_404(
        self,
        entity_id: int,
        include_deleted: bool = False
    ) -> ResponseSchemaType:
        """Get entity by ID or raise NotFoundError.
        
        Args:
            entity_id: ID of entity to retrieve
            include_deleted: Whether to include soft-deleted entities
            
        Returns:
            Entity response
            
        Raises:
            NotFoundError: If entity is not found
        """
        entity = self.get(entity_id, include_deleted)
        if entity is None:
            raise NotFoundError(
                f"{self.model.__name__} with ID {entity_id} not found"
            )
        return entity

    def list(
        self,
        filters: FilterParams,
        pagination: PaginationParams,
        sort_params: SortParams
    ) -> PaginatedResponse[ResponseSchemaType]:
        """List entities with filtering, pagination, and sorting.

        Args:
            filters: Filter parameters
            pagination: Pagination parameters
            sort_params: Sort parameters

        Returns:
            Paginated response with entities

        Raises:
            CRUDError: If database operation fails
        """
        try:
            # Build base query
            query = self.db.query(self.model)

            # Apply filters
            query = self._apply_filters(query, filters)

            # Get total count before pagination
            total_count = query.count()

            # Apply sorting
            query = self._apply_sorting(query, sort_params)

            # Apply pagination
            offset = (pagination.page - 1) * pagination.page_size
            query = query.offset(offset).limit(pagination.page_size)

            # Execute query
            entities = query.all()

            # Convert to response schemas
            response_data = [
                self.response_schema.model_validate(entity) for entity in entities
            ]

            # Build pagination info
            pagination_info = self._build_pagination_info(pagination, total_count)

            return PaginatedResponse(
                data=response_data,
                pagination=pagination_info,
                total_count=total_count,
                filters_applied=filters.model_dump()
            )

        except SQLAlchemyError as e:
            raise CRUDError(
                f"Database error during list operation: {str(e)}",
                details={'original_error': str(e)}
            )

    def update(
        self,
        entity_id: int,
        obj_in: UpdateSchemaType,
        user_id: Optional[str] = None
    ) -> ResponseSchemaType:
        """Update an existing entity.

        Args:
            entity_id: ID of entity to update
            obj_in: Input data for update
            user_id: ID of user performing the operation

        Returns:
            Updated entity response

        Raises:
            NotFoundError: If entity is not found
            ValidationError: If input validation fails
            ConflictError: If update conflicts with existing data
            CRUDError: If database operation fails
        """
        try:
            # Get current entity
            db_obj = self.db.query(self.model).filter(
                self.model.id == entity_id
            ).first()

            if db_obj is None:
                raise NotFoundError(
                    f"{self.model.__name__} with ID {entity_id} not found"
                )

            # Check if entity is soft-deleted
            if hasattr(db_obj, 'is_deleted') and db_obj.is_deleted:
                raise NotFoundError(
                    f"{self.model.__name__} with ID {entity_id} is deleted"
                )

            # Validate update data
            validated_data = self._validate_update_data(obj_in, db_obj)

            # Update fields
            update_data = validated_data.model_dump(exclude_unset=True)

            # Add audit fields if model supports them
            if hasattr(self.model, 'updated_by') and user_id:
                update_data['updated_by'] = user_id
            if hasattr(self.model, 'updated_at'):
                update_data['updated_at'] = datetime.utcnow()

            # Apply updates
            for field, value in update_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)

            # Save to database
            self.db.commit()
            self.db.refresh(db_obj)

            self.logger.info(
                f"Updated {self.model.__name__} with ID {entity_id}",
                extra={'user_id': user_id, 'entity_id': entity_id}
            )

            return self.response_schema.model_validate(db_obj)

        except (NotFoundError, ValidationError):
            self.db.rollback()
            raise
        except IntegrityError as e:
            self.db.rollback()
            raise ConflictError(
                f"Update failed due to data conflict: {str(e)}",
                details={'entity_id': entity_id, 'original_error': str(e)}
            )
        except SQLAlchemyError as e:
            self.db.rollback()
            raise CRUDError(
                f"Database error during update: {str(e)}",
                details={'entity_id': entity_id, 'original_error': str(e)}
            )

    def delete(
        self,
        entity_id: int,
        user_id: Optional[str] = None,
        soft: bool = True
    ) -> bool:
        """Delete an entity.

        Args:
            entity_id: ID of entity to delete
            user_id: ID of user performing the operation
            soft: Whether to perform soft delete (if supported)

        Returns:
            True if deletion was successful

        Raises:
            NotFoundError: If entity is not found
            CRUDError: If database operation fails
        """
        try:
            # Get entity
            db_obj = self.db.query(self.model).filter(
                self.model.id == entity_id
            ).first()

            if db_obj is None:
                raise NotFoundError(
                    f"{self.model.__name__} with ID {entity_id} not found"
                )

            # Check if already soft-deleted
            if hasattr(db_obj, 'is_deleted') and db_obj.is_deleted:
                raise NotFoundError(
                    f"{self.model.__name__} with ID {entity_id} is already deleted"
                )

            # Perform soft or hard delete
            if soft and hasattr(db_obj, 'is_deleted'):
                # Soft delete
                db_obj.is_deleted = True
                if hasattr(db_obj, 'deleted_at'):
                    db_obj.deleted_at = datetime.utcnow()
                if hasattr(db_obj, 'deleted_by') and user_id:
                    db_obj.deleted_by = user_id

                self.db.commit()

                self.logger.info(
                    f"Soft deleted {self.model.__name__} with ID {entity_id}",
                    extra={'user_id': user_id, 'entity_id': entity_id}
                )
            else:
                # Hard delete
                self.db.delete(db_obj)
                self.db.commit()

                self.logger.info(
                    f"Hard deleted {self.model.__name__} with ID {entity_id}",
                    extra={'user_id': user_id, 'entity_id': entity_id}
                )

            return True

        except NotFoundError:
            self.db.rollback()
            raise
        except SQLAlchemyError as e:
            self.db.rollback()
            raise CRUDError(
                f"Database error during deletion: {str(e)}",
                details={'entity_id': entity_id, 'original_error': str(e)}
            )

    def bulk_create(
        self,
        objects: List[CreateSchemaType],
        user_id: Optional[str] = None,
        batch_size: int = 100
    ) -> List[ResponseSchemaType]:
        """Create multiple entities in batches.

        Args:
            objects: List of input data for creation
            user_id: ID of user performing the operation
            batch_size: Number of objects to process per batch

        Returns:
            List of created entity responses

        Raises:
            ValidationError: If any input validation fails
            CRUDError: If database operation fails
        """
        if not objects:
            return []

        created_entities = []

        try:
            # Process in batches
            for i in range(0, len(objects), batch_size):
                batch = objects[i:i + batch_size]
                batch_entities = []

                for obj_in in batch:
                    # Validate input data
                    validated_data = self._validate_create_data(obj_in)

                    # Create database object
                    db_obj_data = validated_data.model_dump()

                    # Add audit fields if model supports them
                    if hasattr(self.model, 'created_by') and user_id:
                        db_obj_data['created_by'] = user_id

                    db_obj = self.model(**db_obj_data)
                    batch_entities.append(db_obj)

                # Add batch to session
                self.db.add_all(batch_entities)
                self.db.commit()

                # Refresh and convert to response schemas
                for db_obj in batch_entities:
                    self.db.refresh(db_obj)
                    created_entities.append(self.response_schema.model_validate(db_obj))

            self.logger.info(
                f"Bulk created {len(created_entities)} {self.model.__name__} entities",
                extra={'user_id': user_id, 'count': len(created_entities)}
            )

            return created_entities

        except ValidationError:
            self.db.rollback()
            raise
        except SQLAlchemyError as e:
            self.db.rollback()
            raise CRUDError(
                f"Database error during bulk creation: {str(e)}",
                details={'batch_size': batch_size, 'original_error': str(e)}
            )

    def search(
        self,
        query: str,
        search_fields: List[str],
        filters: Optional[FilterParams] = None,
        pagination: Optional[PaginationParams] = None
    ) -> PaginatedResponse[ResponseSchemaType]:
        """Search entities using full-text search.

        Args:
            query: Search query string
            search_fields: Fields to search in
            filters: Additional filter parameters
            pagination: Pagination parameters

        Returns:
            Paginated response with search results

        Raises:
            CRUDError: If database operation fails
        """
        if filters is None:
            filters = FilterParams()
        if pagination is None:
            pagination = PaginationParams()

        # Set search parameters
        filters.search = query
        filters.search_fields = search_fields

        # Use regular list method with search filters
        sort_params = SortParams()  # Default sorting

        return self.list(filters, pagination, sort_params)
