"""Job Types CRUD service implementation.

This module provides comprehensive CRUD operations for security job types
following PEP 8, 257, and 484 standards with business logic validation.
"""

from typing import List, Optional, Dict, Any
import logging

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from pydantic import BaseModel, Field, field_validator, ConfigDict

from services.base_crud import (
    BaseCRUDService, FilterParams, PaginationParams, SortParams,
    ValidationError, NotFoundError, ConflictError
)
from services.audit_service import AuditService
from models.security_career_framework import SecurityJobType

logger = logging.getLogger(__name__)


class JobTypeFilters(FilterParams):
    """Filters specific to job types.
    
    Attributes:
        security_area: Filter by security areas
        seniority_level: Filter by seniority levels
        job_family: Filter by job families
        salary_min: Minimum salary filter
        salary_max: Maximum salary filter
        location: Location filters
        remote_friendly: Filter for remote-friendly jobs
        demand_level: Filter by demand level
        required_certifications: Filter by required certifications
        preferred_certifications: Filter by preferred certifications
    """
    
    security_area: Optional[List[str]] = None
    seniority_level: Optional[List[str]] = None
    job_family: Optional[List[str]] = None
    salary_min: Optional[float] = None
    salary_max: Optional[float] = None
    location: Optional[List[str]] = None
    remote_friendly: Optional[bool] = None
    demand_level: Optional[List[str]] = None
    required_certifications: Optional[List[str]] = None
    preferred_certifications: Optional[List[str]] = None


class JobTypeCreateRequest(BaseModel):
    """Request schema for creating job types.
    
    Attributes:
        title: Job title
        security_area: Security area classification
        job_family: Job family classification
        seniority_level: Seniority level
        description: Job description
        responsibilities: List of responsibilities
        required_skills: Required skills list
        preferred_skills: Preferred skills list
        min_years_experience: Minimum years of experience
        max_years_experience: Maximum years of experience
        education_requirements: Education requirements list
        required_certifications: Required certifications list
        preferred_certifications: Preferred certifications list
        salary_min: Minimum salary
        salary_max: Maximum salary
        salary_currency: Salary currency code
        career_progression_from: Career progression from job types
        career_progression_to: Career progression to job types
        demand_level: Job demand level
        remote_friendly: Whether job is remote-friendly
        tags: Additional tags
        is_active: Whether job type is active
    """
    
    title: str = Field(..., min_length=3, max_length=200)
    security_area: str = Field(..., min_length=1, max_length=100)
    job_family: str = Field(..., min_length=1, max_length=100)
    seniority_level: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = None
    responsibilities: Optional[List[str]] = []
    required_skills: Optional[List[str]] = []
    preferred_skills: Optional[List[str]] = []
    min_years_experience: Optional[int] = Field(None, ge=0, le=50)
    max_years_experience: Optional[int] = Field(None, ge=0, le=50)
    education_requirements: Optional[List[str]] = []
    required_certifications: Optional[List[str]] = []
    preferred_certifications: Optional[List[str]] = []
    salary_min: Optional[float] = Field(None, ge=0, le=1000000)
    salary_max: Optional[float] = Field(None, ge=0, le=1000000)
    salary_currency: str = Field(default='USD', pattern=r'^[A-Z]{3}$')
    career_progression_from: Optional[List[int]] = []
    career_progression_to: Optional[List[int]] = []
    demand_level: str = Field(default='medium')
    remote_friendly: bool = Field(default=True)
    tags: Optional[List[str]] = []
    is_active: bool = Field(default=True)
    
    @field_validator('max_years_experience')
    @classmethod
    def validate_experience_range(cls, v, info):
        """Validate that max experience is greater than min experience."""
        if v is not None and info.data and 'min_years_experience' in info.data:
            min_exp = info.data['min_years_experience']
            if min_exp is not None and v < min_exp:
                raise ValueError('Maximum experience must be >= minimum experience')
        return v

    @field_validator('salary_max')
    @classmethod
    def validate_salary_range(cls, v, info):
        """Validate that max salary is greater than min salary."""
        if v is not None and info.data and 'salary_min' in info.data:
            min_sal = info.data['salary_min']
            if min_sal is not None and v < min_sal:
                raise ValueError('Maximum salary must be >= minimum salary')
        return v

    @field_validator('demand_level')
    @classmethod
    def validate_demand_level(cls, v):
        """Validate demand level values."""
        valid_levels = ['low', 'medium', 'high', 'critical']
        if v not in valid_levels:
            raise ValueError(f'Demand level must be one of: {valid_levels}')
        return v

    @field_validator('security_area')
    @classmethod
    def validate_security_area(cls, v):
        """Validate security area values."""
        valid_areas = [
            'Security Architecture and Engineering',
            'Security Operations',
            'Security and Risk Management',
            'Security Assessment and Testing',
            'Software Security',
            'Identity and Access Management',
            'Communication and Network Security',
            'Asset Security'
        ]
        if v not in valid_areas:
            raise ValueError(f'Security area must be one of: {valid_areas}')
        return v


class JobTypeUpdateRequest(BaseModel):
    """Request schema for updating job types."""
    
    title: Optional[str] = Field(None, min_length=3, max_length=200)
    security_area: Optional[str] = Field(None, min_length=1, max_length=100)
    job_family: Optional[str] = Field(None, min_length=1, max_length=100)
    seniority_level: Optional[str] = Field(None, min_length=1, max_length=50)
    description: Optional[str] = None
    responsibilities: Optional[List[str]] = None
    required_skills: Optional[List[str]] = None
    preferred_skills: Optional[List[str]] = None
    min_years_experience: Optional[int] = Field(None, ge=0, le=50)
    max_years_experience: Optional[int] = Field(None, ge=0, le=50)
    education_requirements: Optional[List[str]] = None
    required_certifications: Optional[List[str]] = None
    preferred_certifications: Optional[List[str]] = None
    salary_min: Optional[float] = Field(None, ge=0, le=1000000)
    salary_max: Optional[float] = Field(None, ge=0, le=1000000)
    salary_currency: Optional[str] = Field(None, pattern=r'^[A-Z]{3}$')
    career_progression_from: Optional[List[int]] = None
    career_progression_to: Optional[List[int]] = None
    demand_level: Optional[str] = None
    remote_friendly: Optional[bool] = None
    tags: Optional[List[str]] = None
    is_active: Optional[bool] = None


class JobTypeResponse(BaseModel):
    """Response schema for job types."""
    
    id: int
    title: str
    security_area: str
    job_family: str
    seniority_level: str
    description: Optional[str] = None
    responsibilities: Optional[List[str]] = None
    required_skills: Optional[List[str]] = None
    preferred_skills: Optional[List[str]] = None
    min_years_experience: Optional[int] = None
    max_years_experience: Optional[int] = None
    education_requirements: Optional[List[str]] = None
    required_certifications: Optional[List[str]] = None
    preferred_certifications: Optional[List[str]] = None
    salary_min: Optional[float] = None
    salary_max: Optional[float] = None
    salary_currency: str
    career_progression_from: Optional[List[int]] = None
    career_progression_to: Optional[List[int]] = None
    demand_level: str
    remote_friendly: bool
    tags: Optional[List[str]] = None
    is_active: bool
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


class JobTypeCRUDService(
    BaseCRUDService[SecurityJobType, JobTypeCreateRequest, JobTypeUpdateRequest, JobTypeResponse]
):
    """CRUD service for security job types.
    
    This service provides comprehensive CRUD operations for security job types
    with business logic validation, audit trails, and advanced search capabilities.
    """
    
    def __init__(self, db: Session):
        """Initialize job types CRUD service.
        
        Args:
            db: Database session
        """
        super().__init__(db, SecurityJobType, JobTypeResponse)
        self.audit_service = AuditService(db)
    
    def _validate_create_data(self, obj_in: JobTypeCreateRequest) -> JobTypeCreateRequest:
        """Validate data for job type creation.
        
        Args:
            obj_in: Input data for creation
            
        Returns:
            Validated input data
            
        Raises:
            ValidationError: If validation fails
        """
        # Check for duplicate titles in same security area
        existing = self.db.query(SecurityJobType).filter(
            and_(
                SecurityJobType.title.ilike(f"%{obj_in.title}%"),
                SecurityJobType.security_area == obj_in.security_area,
                SecurityJobType.is_active == True  # noqa: E712
            )
        ).first()
        
        if existing:
            raise ValidationError(
                f"Similar job type already exists: {existing.title}",
                details={'existing_id': existing.id, 'existing_title': existing.title}
            )
        
        # Validate career progression references
        if obj_in.career_progression_from:
            self._validate_job_type_references(obj_in.career_progression_from)
        
        if obj_in.career_progression_to:
            self._validate_job_type_references(obj_in.career_progression_to)
        
        # Check skill requirements consistency
        if obj_in.required_skills and obj_in.preferred_skills:
            overlap = set(obj_in.required_skills) & set(obj_in.preferred_skills)
            if overlap:
                raise ValidationError(
                    f"Skills cannot be both required and preferred: {list(overlap)}"
                )
        
        return obj_in
    
    def _validate_update_data(
        self,
        obj_in: JobTypeUpdateRequest,
        current_obj: SecurityJobType
    ) -> JobTypeUpdateRequest:
        """Validate data for job type update.
        
        Args:
            obj_in: Input data for update
            current_obj: Current database object
            
        Returns:
            Validated input data
            
        Raises:
            ValidationError: If validation fails
        """
        # Check for duplicate titles if title is being updated
        if obj_in.title and obj_in.title != current_obj.title:
            security_area = obj_in.security_area or current_obj.security_area
            existing = self.db.query(SecurityJobType).filter(
                and_(
                    SecurityJobType.id != current_obj.id,
                    SecurityJobType.title.ilike(f"%{obj_in.title}%"),
                    SecurityJobType.security_area == security_area,
                    SecurityJobType.is_active == True  # noqa: E712
                )
            ).first()
            
            if existing:
                raise ValidationError(
                    f"Similar job type already exists: {existing.title}",
                    details={'existing_id': existing.id, 'existing_title': existing.title}
                )
        
        # Validate career progression references
        if obj_in.career_progression_from is not None:
            self._validate_job_type_references(obj_in.career_progression_from)
        
        if obj_in.career_progression_to is not None:
            self._validate_job_type_references(obj_in.career_progression_to)
        
        return obj_in
    
    def _validate_job_type_references(self, job_type_ids: List[int]) -> None:
        """Validate job type references exist.
        
        Args:
            job_type_ids: List of job type IDs to validate
            
        Raises:
            ValidationError: If any references are invalid
        """
        if not job_type_ids:
            return
        
        existing_ids = self.db.query(SecurityJobType.id).filter(
            and_(
                SecurityJobType.id.in_(job_type_ids),
                SecurityJobType.is_active == True  # noqa: E712
            )
        ).all()
        
        existing_ids = [id[0] for id in existing_ids]
        invalid_ids = [id for id in job_type_ids if id not in existing_ids]
        
        if invalid_ids:
            raise ValidationError(
                f"Invalid job type references: {invalid_ids}",
                details={'invalid_ids': invalid_ids}
            )
    
    def _apply_filters(self, query: Any, filters: JobTypeFilters) -> Any:
        """Apply job type specific filters to query.
        
        Args:
            query: SQLAlchemy query object
            filters: Job type filter parameters
            
        Returns:
            Filtered query object
        """
        # Apply base filters
        query = super()._apply_filters(query, filters)
        
        # Apply job type specific filters
        if filters.security_area:
            query = query.filter(SecurityJobType.security_area.in_(filters.security_area))
        
        if filters.seniority_level:
            query = query.filter(SecurityJobType.seniority_level.in_(filters.seniority_level))
        
        if filters.job_family:
            query = query.filter(SecurityJobType.job_family.in_(filters.job_family))
        
        if filters.salary_min is not None:
            query = query.filter(SecurityJobType.salary_min >= filters.salary_min)
        
        if filters.salary_max is not None:
            query = query.filter(SecurityJobType.salary_max <= filters.salary_max)
        
        if filters.remote_friendly is not None:
            query = query.filter(SecurityJobType.remote_friendly == filters.remote_friendly)
        
        if filters.demand_level:
            query = query.filter(SecurityJobType.demand_level.in_(filters.demand_level))
        
        # Filter by certifications (JSON array contains)
        if filters.required_certifications:
            for cert in filters.required_certifications:
                query = query.filter(
                    SecurityJobType.required_certifications.contains([cert])
                )
        
        if filters.preferred_certifications:
            for cert in filters.preferred_certifications:
                query = query.filter(
                    SecurityJobType.preferred_certifications.contains([cert])
                )
        
        return query

    def list_with_filters(
        self,
        filters: JobTypeFilters,
        pagination: PaginationParams,
        sort_params: SortParams
    ) -> Dict[str, Any]:
        """List job types with advanced filtering and aggregations.

        Args:
            filters: Job type filter parameters
            pagination: Pagination parameters
            sort_params: Sort parameters

        Returns:
            Dictionary with job types data and aggregations
        """
        # Build base query
        query = self.db.query(SecurityJobType)

        # Apply filters
        query = self._apply_filters(query, filters)

        # Get total count before pagination
        total_count = query.count()

        # Apply sorting
        query = self._apply_sorting(query, sort_params)

        # Apply pagination
        offset = (pagination.page - 1) * pagination.page_size
        query = query.offset(offset).limit(pagination.page_size)

        # Execute query
        job_types = query.all()

        # Convert to response schemas
        response_data = [JobTypeResponse.from_orm(jt) for jt in job_types]

        # Generate aggregations
        aggregations = self._generate_aggregations(filters)

        # Build pagination info
        pagination_info = self._build_pagination_info(pagination, total_count)

        return {
            'data': response_data,
            'pagination': pagination_info,
            'total_count': total_count,
            'filters_applied': filters.model_dump(),
            'aggregations': aggregations
        }

    def _generate_aggregations(self, filters: JobTypeFilters) -> Dict[str, Dict[str, int]]:
        """Generate aggregations for faceted search.

        Args:
            filters: Current filter parameters

        Returns:
            Dictionary of aggregations by field
        """
        aggregations = {}

        # Base query for aggregations
        base_query = self.db.query(SecurityJobType)
        base_query = super()._apply_filters(base_query, filters)

        # Security area aggregation
        security_area_agg = self.db.query(
            SecurityJobType.security_area,
            func.count(SecurityJobType.id).label('count')
        ).filter(
            SecurityJobType.id.in_(base_query.subquery().select())
        ).group_by(SecurityJobType.security_area).all()

        aggregations['security_areas'] = {
            row.security_area: row.count for row in security_area_agg
        }

        # Seniority level aggregation
        seniority_agg = self.db.query(
            SecurityJobType.seniority_level,
            func.count(SecurityJobType.id).label('count')
        ).filter(
            SecurityJobType.id.in_(base_query.subquery().select())
        ).group_by(SecurityJobType.seniority_level).all()

        aggregations['seniority_levels'] = {
            row.seniority_level: row.count for row in seniority_agg
        }

        # Demand level aggregation
        demand_agg = self.db.query(
            SecurityJobType.demand_level,
            func.count(SecurityJobType.id).label('count')
        ).filter(
            SecurityJobType.id.in_(base_query.subquery().select())
        ).group_by(SecurityJobType.demand_level).all()

        aggregations['demand_levels'] = {
            row.demand_level: row.count for row in demand_agg
        }

        # Salary range aggregation
        salary_ranges = {
            '0-50k': 0,
            '50k-75k': 0,
            '75k-100k': 0,
            '100k-150k': 0,
            '150k+': 0
        }

        salary_data = self.db.query(SecurityJobType.salary_min, SecurityJobType.salary_max).filter(
            SecurityJobType.id.in_(base_query.subquery().select())
        ).all()

        for min_sal, max_sal in salary_data:
            if min_sal is not None:
                if min_sal < 50000:
                    salary_ranges['0-50k'] += 1
                elif min_sal < 75000:
                    salary_ranges['50k-75k'] += 1
                elif min_sal < 100000:
                    salary_ranges['75k-100k'] += 1
                elif min_sal < 150000:
                    salary_ranges['100k-150k'] += 1
                else:
                    salary_ranges['150k+'] += 1

        aggregations['salary_ranges'] = salary_ranges

        return aggregations

    def search_job_types(
        self,
        query: str,
        filters: Optional[JobTypeFilters] = None,
        pagination: Optional[PaginationParams] = None
    ) -> Dict[str, Any]:
        """Search job types with full-text search.

        Args:
            query: Search query string
            filters: Additional filter parameters
            pagination: Pagination parameters

        Returns:
            Search results with relevance scoring
        """
        if filters is None:
            filters = JobTypeFilters()
        if pagination is None:
            pagination = PaginationParams()

        # Set search parameters
        filters.search = query
        filters.search_fields = ['title', 'description', 'responsibilities']

        # Use list method with search filters
        sort_params = SortParams(sort_by='title', sort_order='asc')

        return self.list_with_filters(filters, pagination, sort_params)

    def get_job_type_analytics(self, job_type_id: int) -> Dict[str, Any]:
        """Get analytics data for a specific job type.

        Args:
            job_type_id: ID of the job type

        Returns:
            Analytics data dictionary

        Raises:
            NotFoundError: If job type is not found
        """
        job_type = self.get_or_404(job_type_id)

        # Get related job types in same security area
        related_jobs = self.db.query(SecurityJobType).filter(
            and_(
                SecurityJobType.security_area == job_type.security_area,
                SecurityJobType.id != job_type_id,
                SecurityJobType.is_active == True  # noqa: E712
            )
        ).limit(5).all()

        # Get career progression paths
        progression_from = []
        progression_to = []

        if job_type.career_progression_from:
            progression_from = self.db.query(SecurityJobType).filter(
                SecurityJobType.id.in_(job_type.career_progression_from)
            ).all()

        if job_type.career_progression_to:
            progression_to = self.db.query(SecurityJobType).filter(
                SecurityJobType.id.in_(job_type.career_progression_to)
            ).all()

        return {
            'job_type': job_type,
            'related_jobs': [JobTypeResponse.from_orm(jt) for jt in related_jobs],
            'career_progression': {
                'from': [JobTypeResponse.from_orm(jt) for jt in progression_from],
                'to': [JobTypeResponse.from_orm(jt) for jt in progression_to]
            },
            'market_data': {
                'demand_level': job_type.demand_level,
                'remote_friendly': job_type.remote_friendly,
                'salary_range': {
                    'min': job_type.salary_min,
                    'max': job_type.salary_max,
                    'currency': job_type.salary_currency
                }
            }
        }

    def get_popular_job_types(
        self,
        security_area: Optional[str] = None,
        limit: int = 10
    ) -> List[JobTypeResponse]:
        """Get popular job types by security area.

        Args:
            security_area: Optional security area filter
            limit: Maximum number of results

        Returns:
            List of popular job types
        """
        query = self.db.query(SecurityJobType).filter(
            SecurityJobType.is_active == True  # noqa: E712
        )

        if security_area:
            query = query.filter(SecurityJobType.security_area == security_area)

        # Order by demand level and creation date
        query = query.order_by(
            SecurityJobType.demand_level.desc(),
            SecurityJobType.created_at.desc()
        ).limit(limit)

        job_types = query.all()
        return [JobTypeResponse.from_orm(jt) for jt in job_types]
