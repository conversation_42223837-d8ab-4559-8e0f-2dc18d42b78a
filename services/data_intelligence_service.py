"""Data Intelligence Service for enterprise analytics and market insights.

This service provides comprehensive data intelligence capabilities including
salary benchmarking, skills gap analysis, market trends, and custom analytics
for monetizing aggregated platform data.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, text
import pandas as pd
import numpy as np
from collections import defaultdict
import json

from models.enterprise import EnterpriseOrganization, EnterpriseUser, Department
from models.certification import Certification
from models.progress_tracking import StudySession, PracticeTestResult, Achievement
from models.job_role import JobRole
from services.audit_service import AuditService

logger = logging.getLogger(__name__)


class DataIntelligenceService:
    """Service for data intelligence and analytics monetization."""
    
    def __init__(self, db: Session):
        self.db = db
        self.audit_service = AuditService(db)
    
    # Salary Intelligence
    
    def generate_salary_intelligence(
        self, 
        filters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive salary intelligence report."""
        try:
            logger.info("Generating salary intelligence report")
            
            filters = filters or {}
            
            # Base query for enterprise users with salary data
            query = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.salary_range.isnot(None),
                EnterpriseUser.is_active == True,
                EnterpriseUser.deleted_at.is_(None)
            )
            
            # Apply filters
            if filters.get('location'):
                query = query.filter(EnterpriseUser.location.ilike(f"%{filters['location']}%"))
            
            if filters.get('role'):
                query = query.filter(EnterpriseUser.role.ilike(f"%{filters['role']}%"))
            
            if filters.get('seniority_level'):
                query = query.filter(EnterpriseUser.seniority_level == filters['seniority_level'])
            
            users = query.all()
            
            if not users:
                return self._empty_salary_report()
            
            # Convert to DataFrame for analysis
            df = pd.DataFrame([
                {
                    'user_id': user.user_id,
                    'role': user.role,
                    'seniority_level': user.seniority_level,
                    'location': user.location,
                    'salary_range': user.salary_range,
                    'certifications_completed': len(user.certifications_completed or []),
                    'hire_date': user.hire_date
                }
                for user in users
            ])
            
            # Parse salary ranges and calculate statistics
            salary_data = self._parse_salary_ranges(df)
            
            # Generate comprehensive report
            report = {
                'report_id': f"salary_intel_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'generated_at': datetime.now().isoformat(),
                'filters_applied': filters,
                'sample_size': len(users),
                'salary_statistics': self._calculate_salary_statistics(salary_data),
                'role_based_analysis': self._analyze_salaries_by_role(salary_data),
                'location_based_analysis': self._analyze_salaries_by_location(salary_data),
                'certification_impact': self._analyze_certification_impact(salary_data),
                'career_progression': self._analyze_career_progression(salary_data),
                'market_trends': self._analyze_salary_trends(salary_data),
                'recommendations': self._generate_salary_recommendations(salary_data)
            }
            
            # Log the intelligence generation
            self.audit_service.log_event(
                organization_id=None,  # Cross-organization intelligence
                event_type="data_intelligence",
                description="Generated salary intelligence report",
                resource_type="salary_report",
                resource_id=report['report_id'],
                additional_data={'sample_size': len(users), 'filters': filters}
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating salary intelligence: {e}")
            raise
    
    def _parse_salary_ranges(self, df: pd.DataFrame) -> pd.DataFrame:
        """Parse salary range strings into numeric values."""
        def parse_range(salary_range):
            if not salary_range:
                return None, None
            
            # Handle different formats: "50000-70000", "$50K-$70K", "50-70K", etc.
            import re
            
            # Remove currency symbols and normalize
            cleaned = re.sub(r'[$,]', '', str(salary_range).upper())
            
            # Extract numbers
            numbers = re.findall(r'\d+', cleaned)
            
            if len(numbers) >= 2:
                min_sal = int(numbers[0])
                max_sal = int(numbers[1])
                
                # Handle K notation
                if 'K' in cleaned:
                    min_sal *= 1000
                    max_sal *= 1000
                
                return min_sal, max_sal
            
            return None, None
        
        # Parse salary ranges
        salary_parsed = df['salary_range'].apply(parse_range)
        df['salary_min'] = [x[0] for x in salary_parsed]
        df['salary_max'] = [x[1] for x in salary_parsed]
        df['salary_avg'] = (df['salary_min'] + df['salary_max']) / 2
        
        # Filter out invalid data
        return df.dropna(subset=['salary_min', 'salary_max'])
    
    def _calculate_salary_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate comprehensive salary statistics."""
        return {
            'overall': {
                'mean': float(df['salary_avg'].mean()),
                'median': float(df['salary_avg'].median()),
                'std_dev': float(df['salary_avg'].std()),
                'min': float(df['salary_min'].min()),
                'max': float(df['salary_max'].max()),
                'percentiles': {
                    '25th': float(df['salary_avg'].quantile(0.25)),
                    '75th': float(df['salary_avg'].quantile(0.75)),
                    '90th': float(df['salary_avg'].quantile(0.90))
                }
            },
            'by_seniority': df.groupby('seniority_level')['salary_avg'].agg([
                'mean', 'median', 'count'
            ]).to_dict('index') if 'seniority_level' in df.columns else {}
        }
    
    def _analyze_salaries_by_role(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze salary data by job role."""
        if 'role' not in df.columns or df['role'].isna().all():
            return {}
        
        role_analysis = {}
        
        for role in df['role'].dropna().unique():
            role_data = df[df['role'] == role]
            
            if len(role_data) >= 3:  # Minimum sample size
                role_analysis[role] = {
                    'sample_size': len(role_data),
                    'salary_range': {
                        'min': float(role_data['salary_min'].min()),
                        'max': float(role_data['salary_max'].max()),
                        'avg': float(role_data['salary_avg'].mean()),
                        'median': float(role_data['salary_avg'].median())
                    },
                    'certification_correlation': float(
                        role_data['certifications_completed'].corr(role_data['salary_avg'])
                    ) if len(role_data) > 1 else 0.0
                }
        
        return role_analysis
    
    def _analyze_salaries_by_location(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze salary data by location."""
        if 'location' not in df.columns or df['location'].isna().all():
            return {}
        
        location_analysis = {}
        
        for location in df['location'].dropna().unique():
            location_data = df[df['location'] == location]
            
            if len(location_data) >= 3:  # Minimum sample size
                location_analysis[location] = {
                    'sample_size': len(location_data),
                    'salary_statistics': {
                        'mean': float(location_data['salary_avg'].mean()),
                        'median': float(location_data['salary_avg'].median()),
                        'std_dev': float(location_data['salary_avg'].std())
                    },
                    'cost_of_living_adjusted': self._calculate_col_adjustment(
                        location, float(location_data['salary_avg'].mean())
                    )
                }
        
        return location_analysis
    
    def _analyze_certification_impact(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze the impact of certifications on salary."""
        if 'certifications_completed' not in df.columns:
            return {}
        
        # Group by certification count
        cert_groups = df.groupby(
            pd.cut(df['certifications_completed'], bins=[0, 1, 3, 5, float('inf')], 
                   labels=['0', '1-2', '3-4', '5+'])
        )['salary_avg'].agg(['mean', 'median', 'count']).to_dict('index')
        
        # Calculate correlation
        correlation = float(df['certifications_completed'].corr(df['salary_avg']))
        
        # Calculate premium for certifications
        no_certs = df[df['certifications_completed'] == 0]['salary_avg'].mean()
        with_certs = df[df['certifications_completed'] > 0]['salary_avg'].mean()
        
        certification_premium = ((with_certs - no_certs) / no_certs * 100) if no_certs > 0 else 0
        
        return {
            'correlation_coefficient': correlation,
            'certification_premium_percent': float(certification_premium),
            'salary_by_cert_count': cert_groups,
            'insights': self._generate_certification_insights(correlation, certification_premium)
        }
    
    def _analyze_career_progression(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze career progression and salary growth."""
        if 'hire_date' not in df.columns or df['hire_date'].isna().all():
            return {}
        
        # Calculate years of experience
        df['years_experience'] = (datetime.now() - pd.to_datetime(df['hire_date'])).dt.days / 365.25
        
        # Group by experience ranges
        exp_groups = df.groupby(
            pd.cut(df['years_experience'], bins=[0, 2, 5, 10, float('inf')], 
                   labels=['0-2 years', '2-5 years', '5-10 years', '10+ years'])
        )['salary_avg'].agg(['mean', 'median', 'count']).to_dict('index')
        
        # Calculate growth trajectory
        correlation = float(df['years_experience'].corr(df['salary_avg']))
        
        return {
            'experience_correlation': correlation,
            'salary_by_experience': exp_groups,
            'average_annual_growth': self._calculate_annual_growth(df),
            'career_milestones': self._identify_career_milestones(df)
        }
    
    def _analyze_salary_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze salary trends over time."""
        # This would typically use historical data
        # For now, provide current market insights
        return {
            'market_direction': 'stable',  # Would be calculated from historical data
            'growth_rate_annual': 3.5,    # Industry average
            'demand_indicators': {
                'high_demand_roles': ['Security Architect', 'CISO', 'Penetration Tester'],
                'emerging_roles': ['Cloud Security Engineer', 'DevSecOps Engineer'],
                'declining_roles': ['Legacy System Administrator']
            },
            'regional_hotspots': ['San Francisco', 'New York', 'Austin', 'Seattle']
        }
    
    def _generate_salary_recommendations(self, df: pd.DataFrame) -> List[str]:
        """Generate actionable salary recommendations."""
        recommendations = []
        
        # Certification impact
        cert_correlation = df['certifications_completed'].corr(df['salary_avg'])
        if cert_correlation > 0.3:
            recommendations.append(
                "Strong correlation between certifications and salary - invest in certification programs"
            )
        
        # Location analysis
        if 'location' in df.columns:
            location_variance = df.groupby('location')['salary_avg'].mean().std()
            if location_variance > 10000:  # High variance
                recommendations.append(
                    "Significant salary variations by location - consider location-based compensation adjustments"
                )
        
        # Experience correlation
        if 'years_experience' in df.columns:
            exp_correlation = df['years_experience'].corr(df['salary_avg'])
            if exp_correlation < 0.5:
                recommendations.append(
                    "Weak correlation between experience and salary - review promotion and compensation policies"
                )
        
        return recommendations
    
    # Skills Gap Analysis
    
    def analyze_skills_gap(
        self, 
        industry: str = None, 
        location: str = None,
        organization_id: int = None
    ) -> Dict[str, Any]:
        """Generate comprehensive skills gap analysis."""
        try:
            logger.info(f"Analyzing skills gap for industry: {industry}, location: {location}")
            
            # Get current skills landscape
            current_skills = self._get_current_skills_landscape(industry, location, organization_id)
            
            # Get market demand data
            market_demand = self._get_market_demand_data(industry, location)
            
            # Calculate gaps
            skills_gaps = self._calculate_skills_gaps(current_skills, market_demand)
            
            # Generate recommendations
            recommendations = self._generate_skills_recommendations(skills_gaps)
            
            report = {
                'report_id': f"skills_gap_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'generated_at': datetime.now().isoformat(),
                'analysis_scope': {
                    'industry': industry,
                    'location': location,
                    'organization_id': organization_id
                },
                'current_skills_landscape': current_skills,
                'market_demand': market_demand,
                'identified_gaps': skills_gaps,
                'priority_certifications': self._identify_priority_certifications(skills_gaps),
                'training_recommendations': recommendations,
                'roi_projections': self._calculate_training_roi(skills_gaps)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error analyzing skills gap: {e}")
            raise
    
    # Utility Methods
    
    def _empty_salary_report(self) -> Dict[str, Any]:
        """Return empty salary report structure."""
        return {
            'report_id': f"salary_intel_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'generated_at': datetime.now().isoformat(),
            'sample_size': 0,
            'message': 'Insufficient data for salary analysis'
        }
    
    def _calculate_col_adjustment(self, location: str, salary: float) -> float:
        """Calculate cost of living adjustment (simplified)."""
        # This would typically use external cost of living data
        col_multipliers = {
            'San Francisco': 1.4,
            'New York': 1.3,
            'Seattle': 1.2,
            'Austin': 1.0,
            'Denver': 0.95,
            'Atlanta': 0.9
        }
        
        multiplier = col_multipliers.get(location, 1.0)
        return salary / multiplier
    
    def _generate_certification_insights(self, correlation: float, premium: float) -> List[str]:
        """Generate insights about certification impact."""
        insights = []
        
        if correlation > 0.5:
            insights.append("Strong positive correlation between certifications and salary")
        elif correlation > 0.3:
            insights.append("Moderate positive correlation between certifications and salary")
        else:
            insights.append("Weak correlation between certifications and salary")
        
        if premium > 15:
            insights.append(f"Certifications provide significant salary premium ({premium:.1f}%)")
        elif premium > 5:
            insights.append(f"Certifications provide moderate salary premium ({premium:.1f}%)")
        else:
            insights.append("Limited salary premium from certifications")
        
        return insights
    
    def _calculate_annual_growth(self, df: pd.DataFrame) -> float:
        """Calculate average annual salary growth."""
        if 'years_experience' not in df.columns:
            return 0.0
        
        # Simple linear regression to estimate growth
        correlation = df['years_experience'].corr(df['salary_avg'])
        if correlation > 0:
            # Rough estimate: 3-5% annual growth is typical
            return 4.2
        
        return 0.0
    
    def _identify_career_milestones(self, df: pd.DataFrame) -> Dict[str, float]:
        """Identify key career milestones and associated salaries."""
        milestones = {}
        
        if 'years_experience' in df.columns:
            # Define milestone years
            milestone_years = [2, 5, 10, 15]
            
            for year in milestone_years:
                milestone_data = df[
                    (df['years_experience'] >= year - 0.5) & 
                    (df['years_experience'] <= year + 0.5)
                ]
                
                if len(milestone_data) > 0:
                    milestones[f"{year}_years"] = float(milestone_data['salary_avg'].mean())
        
        return milestones

    def _get_current_skills_landscape(
        self,
        industry: str = None,
        location: str = None,
        organization_id: int = None
    ) -> Dict[str, Any]:
        """Get current skills landscape from platform data."""
        # Query certifications being pursued
        cert_query = self.db.query(
            Certification.name,
            func.count(StudySession.id).label('study_sessions'),
            func.count(PracticeTestResult.id).label('practice_tests'),
            func.count(Achievement.id).label('completions')
        ).outerjoin(StudySession).outerjoin(PracticeTestResult).outerjoin(Achievement)

        if organization_id:
            # Filter by organization if specified
            cert_query = cert_query.join(EnterpriseUser).filter(
                EnterpriseUser.organization_id == organization_id
            )

        cert_data = cert_query.group_by(Certification.name).all()

        return {
            'popular_certifications': [
                {
                    'name': cert.name,
                    'study_activity': cert.study_sessions,
                    'practice_activity': cert.practice_tests,
                    'completion_count': cert.completions
                }
                for cert in cert_data[:20]  # Top 20
            ],
            'skill_categories': self._categorize_skills(cert_data),
            'proficiency_levels': self._assess_proficiency_levels(cert_data)
        }

    def _get_market_demand_data(self, industry: str = None, location: str = None) -> Dict[str, Any]:
        """Get market demand data (would integrate with job market APIs)."""
        # This would typically integrate with job market APIs
        # For now, return representative data
        return {
            'high_demand_skills': [
                'Cloud Security', 'DevSecOps', 'Zero Trust Architecture',
                'Incident Response', 'Threat Hunting', 'Compliance Management'
            ],
            'emerging_skills': [
                'AI/ML Security', 'Container Security', 'IoT Security',
                'Privacy Engineering', 'Security Automation'
            ],
            'declining_skills': [
                'Legacy Firewall Management', 'Basic Network Security',
                'Traditional Antivirus Management'
            ],
            'skill_demand_scores': {
                'Cloud Security': 95,
                'DevSecOps': 90,
                'Zero Trust': 85,
                'Incident Response': 80,
                'Compliance': 75
            }
        }

    def _calculate_skills_gaps(
        self,
        current_skills: Dict[str, Any],
        market_demand: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate skills gaps between current landscape and market demand."""
        gaps = {
            'critical_gaps': [],
            'moderate_gaps': [],
            'minor_gaps': [],
            'oversupplied_skills': []
        }

        # Analyze each high-demand skill
        for skill in market_demand['high_demand_skills']:
            current_activity = self._find_skill_activity(skill, current_skills)
            demand_score = market_demand['skill_demand_scores'].get(skill, 50)

            gap_severity = self._calculate_gap_severity(current_activity, demand_score)

            gap_info = {
                'skill': skill,
                'current_activity': current_activity,
                'market_demand': demand_score,
                'gap_score': gap_severity
            }

            if gap_severity > 70:
                gaps['critical_gaps'].append(gap_info)
            elif gap_severity > 40:
                gaps['moderate_gaps'].append(gap_info)
            else:
                gaps['minor_gaps'].append(gap_info)

        return gaps

    def _generate_skills_recommendations(self, skills_gaps: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate training recommendations based on skills gaps."""
        recommendations = []

        # Critical gaps - immediate action needed
        for gap in skills_gaps['critical_gaps']:
            recommendations.append({
                'priority': 'critical',
                'skill': gap['skill'],
                'action': 'immediate_training',
                'recommended_certifications': self._get_relevant_certifications(gap['skill']),
                'estimated_timeline': '3-6 months',
                'business_impact': 'high'
            })

        # Moderate gaps - plan for next quarter
        for gap in skills_gaps['moderate_gaps']:
            recommendations.append({
                'priority': 'moderate',
                'skill': gap['skill'],
                'action': 'planned_training',
                'recommended_certifications': self._get_relevant_certifications(gap['skill']),
                'estimated_timeline': '6-12 months',
                'business_impact': 'medium'
            })

        return recommendations

    def _identify_priority_certifications(self, skills_gaps: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify priority certifications based on skills gaps."""
        priority_certs = []

        # Map skills to certifications
        skill_cert_mapping = {
            'Cloud Security': ['AWS Security Specialty', 'Azure Security Engineer', 'CCSP'],
            'DevSecOps': ['DevSecOps Foundation', 'Certified DevSecOps Professional'],
            'Zero Trust': ['Zero Trust Architecture Certification'],
            'Incident Response': ['GCIH', 'GCFA', 'GNFA'],
            'Compliance': ['CISA', 'CISM', 'CISSP']
        }

        for gap in skills_gaps['critical_gaps'] + skills_gaps['moderate_gaps']:
            skill = gap['skill']
            certs = skill_cert_mapping.get(skill, [])

            for cert in certs:
                priority_certs.append({
                    'certification': cert,
                    'skill_addressed': skill,
                    'gap_score': gap['gap_score'],
                    'priority_level': 'critical' if gap in skills_gaps['critical_gaps'] else 'moderate'
                })

        return sorted(priority_certs, key=lambda x: x['gap_score'], reverse=True)

    def _calculate_training_roi(self, skills_gaps: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate ROI projections for training investments."""
        # Simplified ROI calculation
        total_gaps = len(skills_gaps['critical_gaps']) + len(skills_gaps['moderate_gaps'])

        if total_gaps == 0:
            return {'roi_percentage': 0, 'payback_period_months': 0}

        # Estimate costs and benefits
        avg_training_cost = 2500  # Per person per certification
        avg_salary_increase = 8000  # Annual increase from closing skill gaps
        avg_productivity_gain = 15000  # Annual productivity improvement

        total_investment = total_gaps * avg_training_cost
        annual_benefits = (avg_salary_increase + avg_productivity_gain) * total_gaps

        roi_percentage = ((annual_benefits - total_investment) / total_investment) * 100
        payback_months = (total_investment / (annual_benefits / 12)) if annual_benefits > 0 else 0

        return {
            'total_investment': total_investment,
            'annual_benefits': annual_benefits,
            'roi_percentage': roi_percentage,
            'payback_period_months': payback_months,
            'break_even_point': f"{payback_months:.1f} months"
        }

    # Market Trends Analysis

    def generate_market_trends(self, vertical: str = None) -> Dict[str, Any]:
        """Generate cybersecurity workforce trend analysis."""
        try:
            logger.info(f"Generating market trends for vertical: {vertical}")

            # Analyze certification popularity trends
            cert_trends = self._analyze_certification_trends()

            # Analyze job market trends
            job_trends = self._analyze_job_market_trends(vertical)

            # Predict future skills
            future_skills = self._predict_future_skills()

            report = {
                'report_id': f"market_trends_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'generated_at': datetime.now().isoformat(),
                'vertical': vertical,
                'certification_trends': cert_trends,
                'job_market_trends': job_trends,
                'emerging_technologies': self._identify_emerging_technologies(),
                'future_skills_predictions': future_skills,
                'industry_insights': self._generate_industry_insights(vertical),
                'recommendations': self._generate_market_recommendations(cert_trends, job_trends)
            }

            return report

        except Exception as e:
            logger.error(f"Error generating market trends: {e}")
            raise

    # Utility methods for skills analysis

    def _categorize_skills(self, cert_data) -> Dict[str, List[str]]:
        """Categorize skills by domain."""
        categories = {
            'Technical Security': [],
            'Governance & Compliance': [],
            'Cloud Security': [],
            'Network Security': [],
            'Application Security': []
        }

        # Simple categorization based on certification names
        for cert in cert_data:
            name = cert.name.lower()
            if any(keyword in name for keyword in ['cloud', 'aws', 'azure', 'gcp']):
                categories['Cloud Security'].append(cert.name)
            elif any(keyword in name for keyword in ['cissp', 'cism', 'cisa', 'compliance']):
                categories['Governance & Compliance'].append(cert.name)
            elif any(keyword in name for keyword in ['network', 'firewall', 'ccna']):
                categories['Network Security'].append(cert.name)
            elif any(keyword in name for keyword in ['application', 'web', 'secure coding']):
                categories['Application Security'].append(cert.name)
            else:
                categories['Technical Security'].append(cert.name)

        return categories

    def _assess_proficiency_levels(self, cert_data) -> Dict[str, int]:
        """Assess overall proficiency levels."""
        total_activity = sum(cert.study_sessions + cert.practice_tests for cert in cert_data)
        total_completions = sum(cert.completions for cert in cert_data)

        return {
            'total_learning_activity': total_activity,
            'total_completions': total_completions,
            'completion_rate': (total_completions / total_activity * 100) if total_activity > 0 else 0
        }

    def _find_skill_activity(self, skill: str, current_skills: Dict[str, Any]) -> int:
        """Find current activity level for a specific skill."""
        # Search through popular certifications for related skills
        activity_score = 0

        for cert in current_skills['popular_certifications']:
            if skill.lower() in cert['name'].lower():
                activity_score += cert['study_activity'] + cert['practice_activity']

        return activity_score

    def _calculate_gap_severity(self, current_activity: int, demand_score: int) -> float:
        """Calculate gap severity score (0-100)."""
        if current_activity == 0:
            return demand_score  # Full gap

        # Normalize current activity (assume max activity of 1000)
        normalized_activity = min(current_activity / 1000 * 100, 100)

        # Gap is the difference between demand and current supply
        gap = max(0, demand_score - normalized_activity)

        return gap

    def _get_relevant_certifications(self, skill: str) -> List[str]:
        """Get relevant certifications for a skill."""
        cert_mapping = {
            'Cloud Security': ['AWS Security Specialty', 'Azure Security Engineer', 'CCSP'],
            'DevSecOps': ['DevSecOps Foundation', 'Certified DevSecOps Professional'],
            'Zero Trust Architecture': ['Zero Trust Architecture Certification'],
            'Incident Response': ['GCIH', 'GCFA', 'GNFA'],
            'Threat Hunting': ['GCTI', 'GNFA', 'CyberSeek Threat Hunter'],
            'Compliance Management': ['CISA', 'CISM', 'CISSP']
        }

        return cert_mapping.get(skill, ['Security+', 'CISSP'])  # Default recommendations
