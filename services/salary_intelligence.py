"""Salary Intelligence Service for Career & Cost Intelligence Agent.

This service provides comprehensive salary analysis, market intelligence,
and ROI calculations for certification investments and career transitions.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime, timedelta
from dataclasses import dataclass
import statistics
import json

from models.career_transition import CareerRole
from models.certification import Certification
from models.cost_calculation import CostCalculation

logger = logging.getLogger(__name__)


@dataclass
class SalaryIntelligence:
    """Data class for salary intelligence analysis."""
    role_id: int
    role_name: str
    current_salary_range: Dict[str, float]
    market_salary_range: Dict[str, float]
    certification_impact: Dict[str, float]
    location_adjustments: Dict[str, float]
    experience_multipliers: Dict[str, float]
    growth_projections: Dict[str, float]
    market_trends: Dict[str, Any]


@dataclass
class ROIAnalysis:
    """Data class for ROI analysis results."""
    investment_cost: float
    expected_salary_increase: float
    payback_period_months: int
    five_year_roi: float
    ten_year_roi: float
    risk_factors: List[str]
    confidence_score: float
    market_conditions: Dict[str, Any]


class SalaryIntelligenceService:
    """Service for salary intelligence and ROI analysis."""
    
    def __init__(self, db: Session):
        self.db = db
        
        # Market data - in production, this would come from external APIs
        self.market_data = {
            'salary_multipliers': {
                'entry': 1.0,
                'junior': 1.2,
                'mid': 1.5,
                'senior': 2.0,
                'lead': 2.5,
                'principal': 3.0,
                'director': 4.0
            },
            'location_multipliers': {
                'san_francisco': 1.8,
                'new_york': 1.6,
                'seattle': 1.5,
                'austin': 1.3,
                'denver': 1.2,
                'atlanta': 1.1,
                'remote': 1.0,
                'international': 0.8
            },
            'certification_premiums': {
                'CISSP': 0.15,
                'CISM': 0.12,
                'Security+': 0.08,
                'Network+': 0.06,
                'CEH': 0.10,
                'OSCP': 0.18,
                'AWS_Solutions_Architect': 0.14,
                'Azure_Security_Engineer': 0.12
            },
            'market_growth_rates': {
                'cybersecurity': 0.08,
                'cloud_security': 0.12,
                'devops_security': 0.10,
                'compliance': 0.06,
                'incident_response': 0.09
            }
        }
    
    def analyze_salary_intelligence(
        self, 
        role_id: int, 
        location: str = 'remote',
        experience_years: int = 5
    ) -> SalaryIntelligence:
        """Analyze comprehensive salary intelligence for a role."""
        logger.info(f"Analyzing salary intelligence for role {role_id}")
        
        # Get role information
        role = self.db.query(CareerRole).filter(CareerRole.id == role_id).first()
        if not role:
            raise ValueError(f"Role {role_id} not found")
        
        # Calculate current salary range
        current_salary_range = self._calculate_current_salary_range(role, location, experience_years)
        
        # Get market salary data
        market_salary_range = self._get_market_salary_range(role, location, experience_years)
        
        # Analyze certification impact
        certification_impact = self._analyze_certification_impact(role)
        
        # Calculate location adjustments
        location_adjustments = self._calculate_location_adjustments(role.domain)
        
        # Get experience multipliers
        experience_multipliers = self._get_experience_multipliers(role.domain)
        
        # Project salary growth
        growth_projections = self._project_salary_growth(role, current_salary_range)
        
        # Analyze market trends
        market_trends = self._analyze_market_trends(role.domain)
        
        return SalaryIntelligence(
            role_id=role_id,
            role_name=role.name,
            current_salary_range=current_salary_range,
            market_salary_range=market_salary_range,
            certification_impact=certification_impact,
            location_adjustments=location_adjustments,
            experience_multipliers=experience_multipliers,
            growth_projections=growth_projections,
            market_trends=market_trends
        )
    
    def calculate_certification_roi(
        self,
        certification_id: int,
        current_role_id: int,
        target_role_id: Optional[int] = None,
        investment_cost: float = 0.0,
        location: str = 'remote',
        experience_years: int = 5
    ) -> ROIAnalysis:
        """Calculate ROI for a certification investment."""
        logger.info(f"Calculating ROI for certification {certification_id}")
        
        # Get certification and role information
        certification = self.db.query(Certification).filter(
            Certification.id == certification_id
        ).first()
        if not certification:
            raise ValueError(f"Certification {certification_id} not found")
        
        current_role = self.db.query(CareerRole).filter(
            CareerRole.id == current_role_id
        ).first()
        if not current_role:
            raise ValueError(f"Current role {current_role_id} not found")
        
        # Calculate current salary
        current_salary_intel = self.analyze_salary_intelligence(
            current_role_id, location, experience_years
        )
        current_salary = current_salary_intel.current_salary_range['median']
        
        # Calculate expected salary increase
        cert_premium = self.market_data['certification_premiums'].get(
            certification.name.replace(' ', '_'), 0.10
        )
        
        if target_role_id:
            # Career transition scenario
            target_salary_intel = self.analyze_salary_intelligence(
                target_role_id, location, experience_years
            )
            target_salary = target_salary_intel.current_salary_range['median']
            expected_salary_increase = (target_salary - current_salary) + (target_salary * cert_premium)
        else:
            # Same role with certification
            expected_salary_increase = current_salary * cert_premium
        
        # Calculate payback period
        monthly_increase = expected_salary_increase / 12
        payback_period_months = int(investment_cost / monthly_increase) if monthly_increase > 0 else 999
        
        # Calculate long-term ROI
        five_year_gain = expected_salary_increase * 5
        ten_year_gain = expected_salary_increase * 10 * 1.03  # Assume 3% annual growth
        
        five_year_roi = ((five_year_gain - investment_cost) / investment_cost) * 100 if investment_cost > 0 else 0
        ten_year_roi = ((ten_year_gain - investment_cost) / investment_cost) * 100 if investment_cost > 0 else 0
        
        # Assess risk factors
        risk_factors = self._assess_roi_risk_factors(certification, current_role, target_role_id)
        
        # Calculate confidence score
        confidence_score = self._calculate_roi_confidence(
            certification, current_role, expected_salary_increase, market_trends={}
        )
        
        # Get market conditions
        market_conditions = self._get_market_conditions(current_role.domain)
        
        return ROIAnalysis(
            investment_cost=investment_cost,
            expected_salary_increase=expected_salary_increase,
            payback_period_months=payback_period_months,
            five_year_roi=five_year_roi,
            ten_year_roi=ten_year_roi,
            risk_factors=risk_factors,
            confidence_score=confidence_score,
            market_conditions=market_conditions
        )
    
    def get_salary_benchmarks(
        self, 
        domain: str, 
        location: str = 'remote'
    ) -> Dict[str, Any]:
        """Get salary benchmarks for a domain and location."""
        logger.info(f"Getting salary benchmarks for {domain} in {location}")
        
        # Get roles in the domain
        roles = self.db.query(CareerRole).filter(
            and_(
                CareerRole.domain.ilike(f"%{domain}%"),
                CareerRole.is_active == True
            )
        ).all()
        
        if not roles:
            return {"error": f"No roles found for domain {domain}"}
        
        benchmarks = {}
        location_multiplier = self.market_data['location_multipliers'].get(
            location.lower().replace(' ', '_'), 1.0
        )
        
        for role in roles:
            salary_intel = self.analyze_salary_intelligence(role.id, location)
            benchmarks[role.name] = {
                'level': role.level,
                'salary_range': salary_intel.current_salary_range,
                'market_position': self._calculate_market_position(
                    salary_intel.current_salary_range,
                    salary_intel.market_salary_range
                ),
                'growth_potential': salary_intel.growth_projections.get('five_year', 0.0)
            }
        
        return {
            'domain': domain,
            'location': location,
            'location_multiplier': location_multiplier,
            'benchmarks': benchmarks,
            'market_summary': self._generate_market_summary(benchmarks),
            'generated_at': datetime.utcnow().isoformat()
        }
    
    def _calculate_current_salary_range(
        self, 
        role: CareerRole, 
        location: str, 
        experience_years: int
    ) -> Dict[str, float]:
        """Calculate current salary range for a role."""
        base_min = role.salary_min or 60000
        base_max = role.salary_max or 120000
        
        # Apply location multiplier
        location_multiplier = self.market_data['location_multipliers'].get(
            location.lower().replace(' ', '_'), 1.0
        )
        
        # Apply experience multiplier
        experience_multiplier = min(1.0 + (experience_years * 0.05), 2.0)
        
        adjusted_min = base_min * location_multiplier * experience_multiplier
        adjusted_max = base_max * location_multiplier * experience_multiplier
        
        return {
            'min': adjusted_min,
            'max': adjusted_max,
            'median': (adjusted_min + adjusted_max) / 2,
            'location_multiplier': location_multiplier,
            'experience_multiplier': experience_multiplier
        }
    
    def _get_market_salary_range(
        self, 
        role: CareerRole, 
        location: str, 
        experience_years: int
    ) -> Dict[str, float]:
        """Get market salary range (simulated - would use real market data)."""
        current_range = self._calculate_current_salary_range(role, location, experience_years)
        
        # Simulate market variance
        market_adjustment = 1.05  # Market is typically 5% higher
        
        return {
            'min': current_range['min'] * market_adjustment,
            'max': current_range['max'] * market_adjustment,
            'median': current_range['median'] * market_adjustment,
            'percentile_25': current_range['min'] * market_adjustment * 1.1,
            'percentile_75': current_range['max'] * market_adjustment * 0.9
        }

    def _analyze_certification_impact(self, role: CareerRole) -> Dict[str, float]:
        """Analyze the impact of certifications on salary for this role."""
        domain_certs = {
            'cybersecurity': ['CISSP', 'CISM', 'Security+', 'CEH', 'OSCP'],
            'cloud': ['AWS_Solutions_Architect', 'Azure_Security_Engineer'],
            'network': ['Network+', 'CCNA', 'CCNP'],
            'compliance': ['CISA', 'CRISC']
        }

        relevant_certs = []
        for domain, certs in domain_certs.items():
            if domain.lower() in role.domain.lower():
                relevant_certs.extend(certs)

        impact_analysis = {}
        for cert in relevant_certs:
            premium = self.market_data['certification_premiums'].get(cert, 0.08)
            impact_analysis[cert] = {
                'salary_premium_percentage': premium * 100,
                'estimated_increase': (role.salary_max or 100000) * premium,
                'market_demand': 'high' if premium > 0.12 else 'medium' if premium > 0.08 else 'low'
            }

        return impact_analysis

    def _calculate_location_adjustments(self, domain: str) -> Dict[str, float]:
        """Calculate salary adjustments by location for the domain."""
        adjustments = {}
        base_multipliers = self.market_data['location_multipliers']

        # Add domain-specific adjustments
        domain_bonus = {
            'cybersecurity': 0.1,
            'cloud': 0.15,
            'devops': 0.12,
            'compliance': 0.05
        }

        bonus = domain_bonus.get(domain.lower(), 0.0)

        for location, multiplier in base_multipliers.items():
            adjustments[location.replace('_', ' ').title()] = {
                'multiplier': multiplier + bonus,
                'description': f"{'Above' if multiplier > 1.2 else 'At' if multiplier > 0.9 else 'Below'} market rate"
            }

        return adjustments

    def _get_experience_multipliers(self, domain: str) -> Dict[str, float]:
        """Get experience-based salary multipliers."""
        base_multipliers = self.market_data['salary_multipliers']

        # Domain-specific experience premiums
        domain_premiums = {
            'cybersecurity': 0.05,
            'cloud': 0.08,
            'ai': 0.12,
            'blockchain': 0.10
        }

        premium = domain_premiums.get(domain.lower(), 0.0)

        return {
            level: multiplier + premium
            for level, multiplier in base_multipliers.items()
        }

    def _project_salary_growth(
        self,
        role: CareerRole,
        current_range: Dict[str, float]
    ) -> Dict[str, float]:
        """Project salary growth over time."""
        domain_growth = self.market_data['market_growth_rates'].get(
            role.domain.lower(), 0.06
        )

        current_median = current_range['median']

        return {
            'one_year': current_median * (1 + domain_growth),
            'three_year': current_median * ((1 + domain_growth) ** 3),
            'five_year': current_median * ((1 + domain_growth) ** 5),
            'ten_year': current_median * ((1 + domain_growth) ** 10),
            'annual_growth_rate': domain_growth * 100
        }

    def _analyze_market_trends(self, domain: str) -> Dict[str, Any]:
        """Analyze market trends for the domain."""
        growth_rate = self.market_data['market_growth_rates'].get(domain.lower(), 0.06)

        trends = {
            'growth_rate': growth_rate * 100,
            'market_outlook': 'strong' if growth_rate > 0.08 else 'stable' if growth_rate > 0.05 else 'moderate',
            'demand_level': 'high' if growth_rate > 0.10 else 'medium' if growth_rate > 0.06 else 'normal',
            'key_drivers': self._get_market_drivers(domain),
            'emerging_skills': self._get_emerging_skills(domain),
            'risk_factors': self._get_market_risks(domain)
        }

        return trends

    def _get_market_drivers(self, domain: str) -> List[str]:
        """Get key market drivers for the domain."""
        drivers = {
            'cybersecurity': [
                'Increasing cyber threats',
                'Regulatory compliance requirements',
                'Digital transformation',
                'Remote work security needs'
            ],
            'cloud': [
                'Cloud migration acceleration',
                'Multi-cloud strategies',
                'DevOps adoption',
                'Cost optimization needs'
            ],
            'ai': [
                'AI/ML adoption',
                'Automation initiatives',
                'Data-driven decision making',
                'Competitive advantage'
            ]
        }

        return drivers.get(domain.lower(), ['Technology advancement', 'Digital transformation'])

    def _get_emerging_skills(self, domain: str) -> List[str]:
        """Get emerging skills for the domain."""
        skills = {
            'cybersecurity': [
                'Zero Trust Architecture',
                'Cloud Security',
                'AI/ML Security',
                'DevSecOps'
            ],
            'cloud': [
                'Kubernetes Security',
                'Infrastructure as Code',
                'Serverless Security',
                'Multi-cloud Management'
            ],
            'ai': [
                'MLOps',
                'AI Ethics',
                'Explainable AI',
                'Edge AI'
            ]
        }

        return skills.get(domain.lower(), ['Automation', 'Cloud Technologies'])

    def _get_market_risks(self, domain: str) -> List[str]:
        """Get market risk factors for the domain."""
        risks = {
            'cybersecurity': [
                'Skills shortage may drive up salaries',
                'Rapid technology change requires continuous learning',
                'Economic downturns may reduce security budgets'
            ],
            'cloud': [
                'Market saturation in basic cloud skills',
                'Vendor-specific certifications may become obsolete',
                'Automation may reduce demand for manual tasks'
            ]
        }

        return risks.get(domain.lower(), ['Technology disruption', 'Market competition'])

    def _assess_roi_risk_factors(
        self,
        certification: Certification,
        current_role: CareerRole,
        target_role_id: Optional[int]
    ) -> List[str]:
        """Assess risk factors for ROI calculation."""
        risk_factors = []

        # Certification-specific risks
        if certification.difficulty and certification.difficulty > 3:
            risk_factors.append("High certification difficulty may affect pass rate")

        # Market saturation risks
        cert_name = certification.name.replace(' ', '_')
        if cert_name in ['Security+', 'Network+']:
            risk_factors.append("Entry-level certification with high market saturation")

        # Role transition risks
        if target_role_id:
            risk_factors.append("Career transition involves additional uncertainties")

        # Domain-specific risks
        if 'cloud' in current_role.domain.lower():
            risk_factors.append("Rapid technology evolution may affect certification value")

        if not risk_factors:
            risk_factors.append("Low risk investment with stable market demand")

        return risk_factors

    def _calculate_roi_confidence(
        self,
        certification: Certification,
        current_role: CareerRole,
        expected_increase: float,
        market_trends: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for ROI analysis."""
        confidence = 0.7  # Base confidence

        # Adjust based on certification popularity
        cert_name = certification.name.replace(' ', '_')
        if cert_name in ['CISSP', 'CISM', 'AWS_Solutions_Architect']:
            confidence += 0.2  # High-value certifications
        elif cert_name in ['Security+', 'Network+']:
            confidence += 0.1  # Established certifications

        # Adjust based on expected increase
        if expected_increase > 15000:
            confidence += 0.1
        elif expected_increase < 5000:
            confidence -= 0.1

        # Adjust based on role level
        if current_role.level in ['Senior', 'Lead', 'Principal']:
            confidence += 0.1

        return min(max(confidence, 0.0), 1.0)

    def _get_market_conditions(self, domain: str) -> Dict[str, Any]:
        """Get current market conditions for the domain."""
        growth_rate = self.market_data['market_growth_rates'].get(domain.lower(), 0.06)

        return {
            'market_health': 'strong' if growth_rate > 0.08 else 'stable',
            'job_availability': 'high' if growth_rate > 0.08 else 'medium',
            'salary_trend': 'increasing' if growth_rate > 0.05 else 'stable',
            'competition_level': 'moderate',
            'economic_factors': [
                'Post-pandemic digital acceleration',
                'Increased cybersecurity awareness',
                'Remote work normalization'
            ]
        }

    def _calculate_market_position(
        self,
        current_range: Dict[str, float],
        market_range: Dict[str, float]
    ) -> str:
        """Calculate market position relative to benchmarks."""
        current_median = current_range['median']
        market_median = market_range['median']

        ratio = current_median / market_median

        if ratio > 1.1:
            return "above_market"
        elif ratio > 0.9:
            return "at_market"
        else:
            return "below_market"

    def _generate_market_summary(self, benchmarks: Dict[str, Any]) -> Dict[str, Any]:
        """Generate market summary from benchmarks."""
        if not benchmarks:
            return {}

        salaries = []
        for role_data in benchmarks.values():
            salaries.append(role_data['salary_range']['median'])

        return {
            'average_salary': statistics.mean(salaries),
            'median_salary': statistics.median(salaries),
            'salary_range': {
                'min': min(salaries),
                'max': max(salaries)
            },
            'total_roles': len(benchmarks),
            'market_health': 'strong'  # Simplified
        }

    def get_certification_salary_impact(self, certification_id: int) -> Dict[str, Any]:
        """Get comprehensive salary impact analysis for a certification."""
        certification = self.db.query(Certification).filter(
            Certification.id == certification_id
        ).first()

        if not certification:
            raise ValueError(f"Certification {certification_id} not found")

        cert_name = certification.name.replace(' ', '_')
        base_premium = self.market_data['certification_premiums'].get(cert_name, 0.08)

        # Get roles that commonly require this certification
        relevant_roles = self.db.query(CareerRole).filter(
            or_(
                CareerRole.required_certifications.contains([certification_id]),
                CareerRole.recommended_certifications.contains([certification_id])
            )
        ).all()

        role_impacts = {}
        for role in relevant_roles:
            salary_intel = self.analyze_salary_intelligence(role.id)
            base_salary = salary_intel.current_salary_range['median']

            role_impacts[role.name] = {
                'base_salary': base_salary,
                'with_certification': base_salary * (1 + base_premium),
                'increase_amount': base_salary * base_premium,
                'increase_percentage': base_premium * 100,
                'role_level': role.level
            }

        return {
            'certification_name': certification.name,
            'average_salary_premium': base_premium * 100,
            'role_impacts': role_impacts,
            'market_demand': 'high' if base_premium > 0.12 else 'medium' if base_premium > 0.08 else 'low',
            'investment_recommendation': self._generate_investment_recommendation(base_premium, len(relevant_roles)),
            'analysis_date': datetime.utcnow().isoformat()
        }

    def _generate_investment_recommendation(self, premium: float, role_count: int) -> str:
        """Generate investment recommendation based on premium and market demand."""
        if premium > 0.15 and role_count > 5:
            return "Highly recommended - strong ROI potential"
        elif premium > 0.10 and role_count > 3:
            return "Recommended - good ROI potential"
        elif premium > 0.08:
            return "Consider - moderate ROI potential"
        else:
            return "Evaluate carefully - limited ROI potential"
