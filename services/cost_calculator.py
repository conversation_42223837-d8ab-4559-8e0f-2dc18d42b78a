"""Cost calculator service for managing certification cost calculations.

This service provides business logic for:
- Cost calculation creation and management
- Currency conversion and rate management
- Cost scenario application and analysis
- Historical cost tracking and trends
- Bulk calculations and comparisons
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc, asc
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
import logging
import requests
from decimal import Decimal

from models.cost_calculation import CurrencyRate, CostScenario, CostCalculation, CostHistory
from models.certification import Certification
from schemas.cost_calculation import (
    CostCalculationCreate, CostCalculationUpdate, CostScenarioCreate, CostScenarioUpdate,
    CurrencyRateCreate, CostComparisonRequest, BulkCostCalculationRequest
)

logger = logging.getLogger(__name__)


class CostCalculatorService:
    """Service class for cost calculator functionality."""

    def __init__(self, db: Session):
        """Initialize the service with database session.
        
        Args:
            db: SQLAlchemy database session
        """
        self.db = db

    # Currency Rate Management
    def get_exchange_rate(self, base_currency: str, target_currency: str) -> Optional[float]:
        """Get current exchange rate between two currencies.
        
        Args:
            base_currency: Base currency code
            target_currency: Target currency code
            
        Returns:
            Exchange rate if found, None otherwise
        """
        if base_currency == target_currency:
            return 1.0

        # Try to find direct rate
        rate = self.db.query(CurrencyRate).filter(
            and_(
                CurrencyRate.base_currency == base_currency,
                CurrencyRate.target_currency == target_currency,
                CurrencyRate.is_active == True
            )
        ).order_by(desc(CurrencyRate.valid_from)).first()

        if rate and rate.is_valid:
            return rate.rate

        # Try inverse rate
        inverse_rate = self.db.query(CurrencyRate).filter(
            and_(
                CurrencyRate.base_currency == target_currency,
                CurrencyRate.target_currency == base_currency,
                CurrencyRate.is_active == True
            )
        ).order_by(desc(CurrencyRate.valid_from)).first()

        if inverse_rate and inverse_rate.is_valid:
            return 1.0 / inverse_rate.rate

        # Fallback to USD conversion if available
        if base_currency != 'USD' and target_currency != 'USD':
            base_to_usd = self.get_exchange_rate(base_currency, 'USD')
            usd_to_target = self.get_exchange_rate('USD', target_currency)
            
            if base_to_usd and usd_to_target:
                return base_to_usd * usd_to_target

        return None

    def create_currency_rate(self, rate_data: CurrencyRateCreate) -> CurrencyRate:
        """Create a new currency rate.
        
        Args:
            rate_data: Currency rate creation data
            
        Returns:
            Created currency rate
        """
        # Deactivate existing rates for the same currency pair
        existing_rates = self.db.query(CurrencyRate).filter(
            and_(
                CurrencyRate.base_currency == rate_data.base_currency.value,
                CurrencyRate.target_currency == rate_data.target_currency.value,
                CurrencyRate.is_active == True
            )
        ).all()

        for existing_rate in existing_rates:
            existing_rate.is_active = False
            existing_rate.valid_until = datetime.utcnow()

        # Create new rate
        rate = CurrencyRate(
            base_currency=rate_data.base_currency.value,
            target_currency=rate_data.target_currency.value,
            rate=rate_data.rate,
            source=rate_data.source.value,
            valid_from=rate_data.valid_from or datetime.utcnow(),
            valid_until=rate_data.valid_until
        )

        self.db.add(rate)
        self.db.commit()
        self.db.refresh(rate)

        logger.info(f"Created currency rate {rate.base_currency}/{rate.target_currency}={rate.rate}")
        return rate

    def update_exchange_rates_from_api(self, base_currency: str = 'USD') -> Dict[str, Any]:
        """Update exchange rates from external API.
        
        Args:
            base_currency: Base currency for rate updates
            
        Returns:
            Dictionary with update results
        """
        try:
            # Use a free exchange rate API (example: exchangerate-api.com)
            # In production, you might want to use a paid service for better reliability
            url = f"https://api.exchangerate-api.com/v4/latest/{base_currency}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            rates = data.get('rates', {})
            
            updated_count = 0
            errors = []
            
            for target_currency, rate in rates.items():
                if target_currency != base_currency:
                    try:
                        rate_data = CurrencyRateCreate(
                            base_currency=base_currency,
                            target_currency=target_currency,
                            rate=float(rate),
                            source='api'
                        )
                        self.create_currency_rate(rate_data)
                        updated_count += 1
                    except Exception as e:
                        errors.append(f"{target_currency}: {str(e)}")
            
            return {
                'success': True,
                'updated_count': updated_count,
                'errors': errors,
                'timestamp': datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Failed to update exchange rates: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow()
            }

    # Cost Scenario Management
    def create_cost_scenario(self, scenario_data: CostScenarioCreate) -> CostScenario:
        """Create a new cost scenario.
        
        Args:
            scenario_data: Cost scenario creation data
            
        Returns:
            Created cost scenario
        """
        scenario = CostScenario(
            name=scenario_data.name,
            description=scenario_data.description,
            scenario_type=scenario_data.scenario_type.value,
            materials_multiplier=scenario_data.materials_multiplier,
            training_multiplier=scenario_data.training_multiplier,
            retake_probability=scenario_data.retake_probability,
            includes_training=scenario_data.includes_training,
            includes_mentoring=scenario_data.includes_mentoring,
            includes_practice_exams=scenario_data.includes_practice_exams,
            study_time_multiplier=scenario_data.study_time_multiplier,
            preparation_weeks=scenario_data.preparation_weeks
        )

        self.db.add(scenario)
        self.db.commit()
        self.db.refresh(scenario)

        logger.info(f"Created cost scenario '{scenario.name}' (ID: {scenario.id})")
        return scenario

    def get_cost_scenarios(
        self, 
        page: int = 1, 
        page_size: int = 20,
        scenario_type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Tuple[List[CostScenario], int]:
        """Get paginated cost scenarios with optional filters.
        
        Args:
            page: Page number (1-based)
            page_size: Number of scenarios per page
            scenario_type: Optional scenario type filter
            is_active: Optional active status filter
            
        Returns:
            Tuple of (scenarios list, total count)
        """
        query = self.db.query(CostScenario)

        # Apply filters
        if scenario_type:
            query = query.filter(CostScenario.scenario_type == scenario_type)
        if is_active is not None:
            query = query.filter(CostScenario.is_active == is_active)

        # Get total count
        total_count = query.count()

        # Apply pagination and ordering
        scenarios = query.order_by(asc(CostScenario.name)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()

        return scenarios, total_count

    def update_cost_scenario(
        self, 
        scenario_id: int, 
        update_data: CostScenarioUpdate
    ) -> Optional[CostScenario]:
        """Update a cost scenario.
        
        Args:
            scenario_id: ID of the scenario
            update_data: Update data
            
        Returns:
            Updated scenario if found, None otherwise
        """
        scenario = self.db.query(CostScenario).filter(CostScenario.id == scenario_id).first()
        if not scenario:
            return None

        # Update fields
        for field, value in update_data.dict(exclude_unset=True).items():
            setattr(scenario, field, value)

        self.db.commit()
        self.db.refresh(scenario)

        logger.info(f"Updated cost scenario {scenario_id}")
        return scenario

    # Cost Calculation Management
    def calculate_certification_costs(
        self, 
        user_id: str, 
        calculation_data: CostCalculationCreate
    ) -> CostCalculation:
        """Calculate costs for a set of certifications.
        
        Args:
            user_id: ID of the user creating the calculation
            calculation_data: Calculation parameters
            
        Returns:
            Created cost calculation
            
        Raises:
            ValueError: If certifications not found or invalid scenario
        """
        # Validate certifications exist
        certifications = self.db.query(Certification).filter(
            Certification.id.in_(calculation_data.certification_ids)
        ).all()

        if len(certifications) != len(calculation_data.certification_ids):
            found_ids = [cert.id for cert in certifications]
            missing_ids = set(calculation_data.certification_ids) - set(found_ids)
            raise ValueError(f"Certifications not found: {missing_ids}")

        # Get cost scenario if specified
        scenario = None
        if calculation_data.scenario_id:
            scenario = self.db.query(CostScenario).filter(
                CostScenario.id == calculation_data.scenario_id
            ).first()
            if not scenario:
                raise ValueError(f"Cost scenario with ID {calculation_data.scenario_id} not found")

        # Calculate base costs
        exam_fees_total = sum(cert.cost or 0 for cert in certifications)
        
        # Apply scenario multipliers if available
        materials_cost = calculation_data.materials_cost
        training_cost = 0.0
        retake_cost = 0.0
        
        if scenario:
            materials_cost *= scenario.materials_multiplier
            
            if scenario.includes_training:
                # Estimate training cost based on certification difficulty
                avg_difficulty = sum(cert.difficulty for cert in certifications) / len(certifications)
                base_training_cost = avg_difficulty * 500  # $500 per difficulty level
                training_cost = base_training_cost * scenario.training_multiplier
            
            # Calculate potential retake costs
            retake_cost = exam_fees_total * scenario.retake_probability

        # Get exchange rate
        exchange_rate = self.get_exchange_rate(
            calculation_data.base_currency.value,
            calculation_data.target_currency.value
        ) or 1.0

        # Estimate study time
        total_study_hours = sum(
            self._estimate_study_hours(cert, scenario) for cert in certifications
        )
        estimated_weeks = None
        if scenario and scenario.preparation_weeks:
            estimated_weeks = scenario.preparation_weeks
        elif total_study_hours:
            estimated_weeks = max(1, int(total_study_hours / 10))  # Assume 10 hours per week

        # Create calculation
        calculation = CostCalculation(
            user_id=user_id,
            name=calculation_data.name,
            description=calculation_data.description,
            base_currency=calculation_data.base_currency.value,
            target_currency=calculation_data.target_currency.value,
            scenario_id=calculation_data.scenario_id,
            certification_ids=calculation_data.certification_ids,
            exam_fees_total=exam_fees_total,
            materials_cost=materials_cost,
            training_cost=training_cost,
            retake_cost=retake_cost,
            additional_costs=calculation_data.additional_costs,
            exchange_rate_used=exchange_rate,
            estimated_study_hours=total_study_hours,
            estimated_weeks=estimated_weeks,
            is_saved=calculation_data.is_saved
        )

        # Update totals
        calculation.update_totals()

        self.db.add(calculation)
        self.db.commit()
        self.db.refresh(calculation)

        logger.info(f"Created cost calculation '{calculation.name}' for user {user_id}")
        return calculation

    def _estimate_study_hours(self, certification: Certification, scenario: Optional[CostScenario]) -> int:
        """Estimate study hours for a certification.
        
        Args:
            certification: Certification to estimate for
            scenario: Optional cost scenario with time multipliers
            
        Returns:
            Estimated study hours
        """
        # Base hours by difficulty level
        base_hours_map = {
            1: 40,   # Entry level
            2: 80,   # Intermediate
            3: 120,  # Advanced
            4: 160,  # Expert
            5: 200   # Master
        }
        
        base_hours = base_hours_map.get(certification.difficulty, 80)
        
        # Apply scenario multiplier if available
        if scenario:
            base_hours = int(base_hours * scenario.study_time_multiplier)
        
        return base_hours

    def get_user_cost_calculations(
        self, 
        user_id: str, 
        page: int = 1, 
        page_size: int = 20,
        is_saved: Optional[bool] = None
    ) -> Tuple[List[CostCalculation], int]:
        """Get paginated cost calculations for a user.
        
        Args:
            user_id: ID of the user
            page: Page number (1-based)
            page_size: Number of calculations per page
            is_saved: Optional saved status filter
            
        Returns:
            Tuple of (calculations list, total count)
        """
        query = self.db.query(CostCalculation).filter(CostCalculation.user_id == user_id)

        # Apply filters
        if is_saved is not None:
            query = query.filter(CostCalculation.is_saved == is_saved)

        # Get total count
        total_count = query.count()

        # Apply pagination and ordering
        calculations = query.order_by(desc(CostCalculation.calculation_date)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()

        return calculations, total_count

    def update_cost_calculation(
        self, 
        calculation_id: int, 
        user_id: str, 
        update_data: CostCalculationUpdate
    ) -> Optional[CostCalculation]:
        """Update a cost calculation.
        
        Args:
            calculation_id: ID of the calculation
            user_id: ID of the user (for authorization)
            update_data: Update data
            
        Returns:
            Updated calculation if found, None otherwise
        """
        calculation = self.db.query(CostCalculation).filter(
            and_(
                CostCalculation.id == calculation_id,
                CostCalculation.user_id == user_id
            )
        ).first()

        if not calculation:
            return None

        # Update fields
        for field, value in update_data.dict(exclude_unset=True).items():
            if field == 'target_currency' and value:
                # Recalculate exchange rate if currency changed
                new_rate = self.get_exchange_rate(calculation.base_currency, value.value) or 1.0
                calculation.exchange_rate_used = new_rate
                setattr(calculation, field, value.value)
            else:
                setattr(calculation, field, value)

        # Recalculate totals
        calculation.update_totals()

        self.db.commit()
        self.db.refresh(calculation)

        logger.info(f"Updated cost calculation {calculation_id} for user {user_id}")
        return calculation

    def delete_cost_calculation(self, calculation_id: int, user_id: str) -> bool:
        """Delete a cost calculation.
        
        Args:
            calculation_id: ID of the calculation
            user_id: ID of the user (for authorization)
            
        Returns:
            True if deleted, False if not found
        """
        calculation = self.db.query(CostCalculation).filter(
            and_(
                CostCalculation.id == calculation_id,
                CostCalculation.user_id == user_id
            )
        ).first()

        if not calculation:
            return False

        self.db.delete(calculation)
        self.db.commit()

        logger.info(f"Deleted cost calculation {calculation_id} for user {user_id}")
        return True

    def compare_cost_calculations(
        self, 
        user_id: str, 
        comparison_request: CostComparisonRequest
    ) -> Dict[str, Any]:
        """Compare multiple cost calculations.
        
        Args:
            user_id: ID of the user
            comparison_request: Comparison parameters
            
        Returns:
            Comparison analysis
            
        Raises:
            ValueError: If calculations not found or not owned by user
        """
        # Get calculations
        calculations = self.db.query(CostCalculation).filter(
            and_(
                CostCalculation.id.in_(comparison_request.calculation_ids),
                CostCalculation.user_id == user_id
            )
        ).all()

        if len(calculations) != len(comparison_request.calculation_ids):
            found_ids = [calc.id for calc in calculations]
            missing_ids = set(comparison_request.calculation_ids) - set(found_ids)
            raise ValueError(f"Calculations not found or not accessible: {missing_ids}")

        # Convert all costs to comparison currency
        comparison_currency = comparison_request.comparison_currency.value
        converted_costs = []
        
        for calc in calculations:
            rate = self.get_exchange_rate(calc.target_currency, comparison_currency) or 1.0
            converted_cost = calc.total_cost_target * rate
            converted_costs.append(converted_cost)

        # Calculate summary statistics
        summary = {
            'lowest_cost': min(converted_costs),
            'highest_cost': max(converted_costs),
            'average_cost': sum(converted_costs) / len(converted_costs),
            'cost_range': max(converted_costs) - min(converted_costs),
            'total_certifications': sum(calc.certification_count for calc in calculations)
        }

        # Generate recommendations
        recommendations = self._generate_cost_recommendations(calculations, converted_costs)

        return {
            'calculations': calculations,
            'comparison_currency': comparison_currency,
            'summary': summary,
            'recommendations': recommendations
        }

    def _generate_cost_recommendations(
        self, 
        calculations: List[CostCalculation], 
        converted_costs: List[float]
    ) -> List[str]:
        """Generate cost optimization recommendations.
        
        Args:
            calculations: List of cost calculations
            converted_costs: List of costs in comparison currency
            
        Returns:
            List of recommendation strings
        """
        recommendations = []
        
        # Find lowest cost option
        min_cost_idx = converted_costs.index(min(converted_costs))
        lowest_calc = calculations[min_cost_idx]
        
        recommendations.append(
            f"Consider '{lowest_calc.name}' for the most cost-effective option"
        )
        
        # Check for high retake costs
        high_retake_calcs = [
            calc for calc in calculations 
            if calc.retake_cost > calc.exam_fees_total * 0.3
        ]
        
        if high_retake_calcs:
            recommendations.append(
                "Factor in retake probability - consider additional preparation time"
            )
        
        # Check for training vs self-study
        training_calcs = [calc for calc in calculations if calc.training_cost > 0]
        self_study_calcs = [calc for calc in calculations if calc.training_cost == 0]
        
        if training_calcs and self_study_calcs:
            recommendations.append(
                "Compare training vs self-study paths based on your learning style"
            )
        
        return recommendations
