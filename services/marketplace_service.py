"""Marketplace service for vendor management and course operations.

This module provides comprehensive business logic for the Agent 5 Marketplace & Integration Hub,
including vendor onboarding, course management, commission calculations, and analytics.
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

from models.marketplace import (
    MarketplaceVendor, MarketplaceCourse, PartnershipAgreement, CommissionRecord,
    CourseEnrollment, CourseReview, CurrencyRate as MarketplaceCurrencyRate, InternationalMarket,
    VendorStatus, CourseStatus, CommissionStatus, PartnershipTier
)
from schemas.marketplace import (
    MarketplaceVendorCreate, MarketplaceVendorUpdate, MarketplaceCourseCreate, MarketplaceCourseUpdate,
    PartnershipAgreementCreate, CommissionRecordCreate, CourseEnrollmentCreate, CourseReviewCreate,
    MarketplaceSearchRequest, CurrencyConversionRequest
)

logger = logging.getLogger(__name__)


class MarketplaceService:
    """Service class for marketplace operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    # Vendor Management
    def create_vendor(self, vendor_data: MarketplaceVendorCreate) -> MarketplaceVendor:
        """Create a new marketplace vendor."""
        try:
            # Check if vendor slug already exists
            existing_vendor = self.db.query(MarketplaceVendor).filter(
                MarketplaceVendor.vendor_slug == vendor_data.vendor_slug
            ).first()
            
            if existing_vendor:
                raise ValueError(f"Vendor with slug '{vendor_data.vendor_slug}' already exists")
            
            # Create vendor
            vendor = MarketplaceVendor(
                vendor_name=vendor_data.vendor_name,
                vendor_slug=vendor_data.vendor_slug,
                contact_email=vendor_data.contact_email,
                contact_phone=vendor_data.contact_phone,
                website_url=vendor_data.website_url,
                business_name=vendor_data.business_name,
                tax_id=vendor_data.tax_id,
                business_address=vendor_data.business_address,
                content_categories=vendor_data.content_categories,
                specializations=vendor_data.specializations,
                commission_rate=vendor_data.commission_rate,
                payment_terms=vendor_data.payment_terms,
                minimum_payout=vendor_data.minimum_payout,
                vendor_config=vendor_data.vendor_config or {},
                status=VendorStatus.PENDING
            )
            
            self.db.add(vendor)
            self.db.commit()
            self.db.refresh(vendor)
            
            logger.info(f"Created vendor: {vendor.vendor_name} (ID: {vendor.id})")
            return vendor
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating vendor: {e}")
            raise
    
    def get_vendor(self, vendor_id: int) -> Optional[MarketplaceVendor]:
        """Get vendor by ID."""
        return self.db.query(MarketplaceVendor).filter(
            MarketplaceVendor.id == vendor_id,
            MarketplaceVendor.deleted_at.is_(None)
        ).first()
    
    def get_vendor_by_slug(self, vendor_slug: str) -> Optional[MarketplaceVendor]:
        """Get vendor by slug."""
        return self.db.query(MarketplaceVendor).filter(
            MarketplaceVendor.vendor_slug == vendor_slug,
            MarketplaceVendor.deleted_at.is_(None)
        ).first()
    
    def update_vendor(self, vendor_id: int, vendor_data: MarketplaceVendorUpdate) -> Optional[MarketplaceVendor]:
        """Update vendor information."""
        try:
            vendor = self.get_vendor(vendor_id)
            if not vendor:
                return None
            
            # Update fields
            update_data = vendor_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(vendor, field, value)
            
            self.db.commit()
            self.db.refresh(vendor)
            
            logger.info(f"Updated vendor: {vendor.vendor_name} (ID: {vendor.id})")
            return vendor
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating vendor {vendor_id}: {e}")
            raise
    
    def list_vendors(self, status: Optional[VendorStatus] = None, page: int = 1, per_page: int = 20) -> Tuple[List[MarketplaceVendor], int]:
        """List vendors with pagination."""
        query = self.db.query(MarketplaceVendor).filter(
            MarketplaceVendor.deleted_at.is_(None)
        )
        
        if status:
            query = query.filter(MarketplaceVendor.status == status)
        
        total = query.count()
        vendors = query.offset((page - 1) * per_page).limit(per_page).all()
        
        return vendors, total
    
    def verify_vendor(self, vendor_id: int, verification_status: str = 'verified') -> Optional[MarketplaceVendor]:
        """Verify a vendor."""
        try:
            vendor = self.get_vendor(vendor_id)
            if not vendor:
                return None
            
            vendor.verification_status = verification_status
            vendor.verification_date = datetime.utcnow()
            
            if verification_status == 'verified':
                vendor.status = VendorStatus.ACTIVE
            
            self.db.commit()
            self.db.refresh(vendor)
            
            logger.info(f"Verified vendor: {vendor.vendor_name} (ID: {vendor.id})")
            return vendor
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error verifying vendor {vendor_id}: {e}")
            raise
    
    # Course Management
    def create_course(self, course_data: MarketplaceCourseCreate) -> MarketplaceCourse:
        """Create a new marketplace course."""
        try:
            # Verify vendor exists and is active
            vendor = self.get_vendor(course_data.vendor_id)
            if not vendor:
                raise ValueError(f"Vendor with ID {course_data.vendor_id} not found")
            
            if vendor.status != VendorStatus.ACTIVE:
                raise ValueError(f"Vendor must be active to create courses")
            
            # Check if course slug already exists for this vendor
            existing_course = self.db.query(MarketplaceCourse).filter(
                and_(
                    MarketplaceCourse.vendor_id == course_data.vendor_id,
                    MarketplaceCourse.course_slug == course_data.course_slug,
                    MarketplaceCourse.deleted_at.is_(None)
                )
            ).first()
            
            if existing_course:
                raise ValueError(f"Course with slug '{course_data.course_slug}' already exists for this vendor")
            
            # Create course
            course = MarketplaceCourse(
                vendor_id=course_data.vendor_id,
                course_title=course_data.course_title,
                course_slug=course_data.course_slug,
                course_description=course_data.course_description,
                short_description=course_data.short_description,
                course_level=course_data.course_level,
                duration_hours=course_data.duration_hours,
                language=course_data.language,
                price=course_data.price,
                currency=course_data.currency,
                discount_price=course_data.discount_price,
                course_content=course_data.course_content or {},
                learning_objectives=course_data.learning_objectives,
                prerequisites=course_data.prerequisites,
                target_certifications=course_data.target_certifications,
                certification_domains=course_data.certification_domains,
                tags=course_data.tags,
                promotional_text=course_data.promotional_text,
                thumbnail_url=course_data.thumbnail_url,
                preview_video_url=course_data.preview_video_url,
                course_materials=course_data.course_materials,
                status=CourseStatus.DRAFT
            )
            
            self.db.add(course)
            self.db.commit()
            self.db.refresh(course)
            
            # Update vendor course count
            vendor.total_courses += 1
            self.db.commit()
            
            logger.info(f"Created course: {course.course_title} (ID: {course.id})")
            return course
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating course: {e}")
            raise
    
    def get_course(self, course_id: int) -> Optional[MarketplaceCourse]:
        """Get course by ID."""
        return self.db.query(MarketplaceCourse).filter(
            MarketplaceCourse.id == course_id,
            MarketplaceCourse.deleted_at.is_(None)
        ).first()
    
    def update_course(self, course_id: int, course_data: MarketplaceCourseUpdate) -> Optional[MarketplaceCourse]:
        """Update course information."""
        try:
            course = self.get_course(course_id)
            if not course:
                return None
            
            # Update fields
            update_data = course_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(course, field, value)
            
            self.db.commit()
            self.db.refresh(course)
            
            logger.info(f"Updated course: {course.course_title} (ID: {course.id})")
            return course
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating course {course_id}: {e}")
            raise
    
    def approve_course(self, course_id: int) -> Optional[MarketplaceCourse]:
        """Approve a course for publication."""
        try:
            course = self.get_course(course_id)
            if not course:
                return None
            
            course.status = CourseStatus.APPROVED
            course.approval_date = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(course)
            
            logger.info(f"Approved course: {course.course_title} (ID: {course.id})")
            return course
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error approving course {course_id}: {e}")
            raise
    
    def publish_course(self, course_id: int) -> Optional[MarketplaceCourse]:
        """Publish an approved course."""
        try:
            course = self.get_course(course_id)
            if not course:
                return None
            
            if course.status != CourseStatus.APPROVED:
                raise ValueError("Course must be approved before publishing")
            
            course.status = CourseStatus.PUBLISHED
            course.publication_date = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(course)
            
            logger.info(f"Published course: {course.course_title} (ID: {course.id})")
            return course
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error publishing course {course_id}: {e}")
            raise

    def search_courses(self, search_request: MarketplaceSearchRequest) -> Tuple[List[MarketplaceCourse], int]:
        """Search courses with filters and pagination."""
        query = self.db.query(MarketplaceCourse).filter(
            MarketplaceCourse.deleted_at.is_(None),
            MarketplaceCourse.status == CourseStatus.PUBLISHED
        )

        # Apply filters
        if search_request.query:
            search_term = f"%{search_request.query}%"
            query = query.filter(
                or_(
                    MarketplaceCourse.course_title.ilike(search_term),
                    MarketplaceCourse.course_description.ilike(search_term),
                    MarketplaceCourse.tags.contains([search_request.query])
                )
            )

        if search_request.categories:
            query = query.filter(
                MarketplaceCourse.certification_domains.op('&&')(search_request.categories)
            )

        if search_request.level:
            query = query.filter(MarketplaceCourse.course_level == search_request.level)

        if search_request.price_min is not None:
            query = query.filter(MarketplaceCourse.price >= search_request.price_min)

        if search_request.price_max is not None:
            query = query.filter(MarketplaceCourse.price <= search_request.price_max)

        if search_request.rating_min is not None:
            query = query.filter(MarketplaceCourse.average_rating >= search_request.rating_min)

        if search_request.language:
            query = query.filter(MarketplaceCourse.language == search_request.language)

        if search_request.vendor_id:
            query = query.filter(MarketplaceCourse.vendor_id == search_request.vendor_id)

        if search_request.featured_only:
            query = query.filter(MarketplaceCourse.featured == True)

        # Apply sorting
        if search_request.sort_by == "price":
            sort_field = MarketplaceCourse.price
        elif search_request.sort_by == "rating":
            sort_field = MarketplaceCourse.average_rating
        elif search_request.sort_by == "enrollment":
            sort_field = MarketplaceCourse.enrollment_count
        elif search_request.sort_by == "date":
            sort_field = MarketplaceCourse.publication_date
        else:
            sort_field = MarketplaceCourse.created_at

        if search_request.sort_order == "asc":
            query = query.order_by(asc(sort_field))
        else:
            query = query.order_by(desc(sort_field))

        total = query.count()
        courses = query.offset((search_request.page - 1) * search_request.per_page).limit(search_request.per_page).all()

        return courses, total

    # Commission Management
    def calculate_commission(self, vendor_id: int, gross_amount: Decimal, course_id: Optional[int] = None) -> Dict[str, Any]:
        """Calculate commission for a vendor based on performance metrics."""
        try:
            vendor = self.get_vendor(vendor_id)
            if not vendor:
                raise ValueError(f"Vendor with ID {vendor_id} not found")

            # Base commission rate
            base_commission = vendor.commission_rate

            # Calculate bonuses
            quality_bonus = 0.0
            volume_bonus = 0.0
            success_bonus = 0.0

            # Quality bonus: +5% for 4.5+ star rating
            if vendor.average_rating and vendor.average_rating >= 4.5:
                quality_bonus = 0.05

            # Volume bonus: +2% for 1000+ enrollments
            if vendor.total_enrollments >= 1000:
                volume_bonus = 0.02

            # Success bonus: +3% for 85%+ certification pass rate (if course specified)
            if course_id:
                course = self.get_course(course_id)
                if course and course.success_rate and course.success_rate >= 0.85:
                    success_bonus = 0.03

            # Total commission rate (capped at 35%)
            total_commission_rate = min(base_commission + quality_bonus + volume_bonus + success_bonus, 0.35)
            commission_amount = gross_amount * Decimal(str(total_commission_rate))

            return {
                'base_commission': base_commission,
                'quality_bonus': quality_bonus,
                'volume_bonus': volume_bonus,
                'success_bonus': success_bonus,
                'total_commission_rate': total_commission_rate,
                'commission_amount': commission_amount,
                'gross_amount': gross_amount
            }

        except Exception as e:
            logger.error(f"Error calculating commission for vendor {vendor_id}: {e}")
            raise

    def create_commission_record(self, commission_data: CommissionRecordCreate) -> CommissionRecord:
        """Create a commission record."""
        try:
            # Calculate commission details
            commission_calc = self.calculate_commission(
                commission_data.vendor_id,
                commission_data.gross_amount,
                commission_data.course_id
            )

            # Create commission record
            commission = CommissionRecord(
                vendor_id=commission_data.vendor_id,
                course_id=commission_data.course_id,
                transaction_id=commission_data.transaction_id,
                transaction_type=commission_data.transaction_type,
                gross_amount=commission_data.gross_amount,
                commission_rate=commission_calc['total_commission_rate'],
                commission_amount=commission_calc['commission_amount'],
                currency=commission_data.currency,
                base_commission=commission_calc['base_commission'],
                quality_bonus=commission_calc['quality_bonus'],
                volume_bonus=commission_calc['volume_bonus'],
                success_bonus=commission_calc['success_bonus'],
                period_start=commission_data.period_start,
                period_end=commission_data.period_end,
                customer_id=commission_data.customer_id,
                notes=commission_data.notes,
                status=CommissionStatus.CALCULATED,
                calculation_date=datetime.utcnow()
            )

            self.db.add(commission)
            self.db.commit()
            self.db.refresh(commission)

            logger.info(f"Created commission record: {commission.transaction_id} (ID: {commission.id})")
            return commission

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating commission record: {e}")
            raise

    # Enrollment Management
    def enroll_user_in_course(self, enrollment_data: CourseEnrollmentCreate) -> CourseEnrollment:
        """Enroll a user in a course."""
        try:
            # Verify course exists and is published
            course = self.get_course(enrollment_data.course_id)
            if not course:
                raise ValueError(f"Course with ID {enrollment_data.course_id} not found")

            if course.status != CourseStatus.PUBLISHED:
                raise ValueError("Course must be published for enrollment")

            # Check if user is already enrolled
            existing_enrollment = self.db.query(CourseEnrollment).filter(
                and_(
                    CourseEnrollment.course_id == enrollment_data.course_id,
                    CourseEnrollment.user_id == enrollment_data.user_id,
                    CourseEnrollment.status.in_(['active', 'completed'])
                )
            ).first()

            if existing_enrollment:
                raise ValueError("User is already enrolled in this course")

            # Create enrollment
            enrollment = CourseEnrollment(
                course_id=enrollment_data.course_id,
                user_id=enrollment_data.user_id,
                purchase_price=enrollment_data.purchase_price,
                currency=enrollment_data.currency,
                payment_method=enrollment_data.payment_method,
                transaction_id=enrollment_data.transaction_id or str(uuid.uuid4()),
                enrollment_date=datetime.utcnow(),
                status='active'
            )

            self.db.add(enrollment)

            # Update course enrollment count
            course.enrollment_count += 1

            # Update vendor enrollment count
            vendor = course.vendor
            vendor.total_enrollments += 1

            self.db.commit()
            self.db.refresh(enrollment)

            # Create commission record for the enrollment
            commission_data = CommissionRecordCreate(
                vendor_id=course.vendor_id,
                course_id=course.id,
                transaction_id=enrollment.transaction_id,
                transaction_type='course_sale',
                gross_amount=enrollment_data.purchase_price,
                commission_rate=vendor.commission_rate,
                commission_amount=Decimal('0'),  # Will be calculated
                currency=enrollment_data.currency,
                base_commission=vendor.commission_rate,
                period_start=datetime.utcnow().replace(day=1),
                period_end=(datetime.utcnow().replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1),
                customer_id=enrollment_data.user_id
            )

            self.create_commission_record(commission_data)

            logger.info(f"Enrolled user {enrollment_data.user_id} in course {course.course_title}")
            return enrollment

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error enrolling user in course: {e}")
            raise

    def add_course_review(self, review_data: CourseReviewCreate) -> CourseReview:
        """Add a review for a course."""
        try:
            # Verify course exists
            course = self.get_course(review_data.course_id)
            if not course:
                raise ValueError(f"Course with ID {review_data.course_id} not found")

            # Check if user has already reviewed this course
            existing_review = self.db.query(CourseReview).filter(
                and_(
                    CourseReview.course_id == review_data.course_id,
                    CourseReview.user_id == review_data.user_id
                )
            ).first()

            if existing_review:
                raise ValueError("User has already reviewed this course")

            # Create review
            review = CourseReview(
                course_id=review_data.course_id,
                user_id=review_data.user_id,
                rating=review_data.rating,
                title=review_data.title,
                review_text=review_data.review_text,
                is_verified_purchase=review_data.is_verified_purchase
            )

            self.db.add(review)

            # Update course rating and review count
            course.review_count += 1

            # Recalculate average rating
            avg_rating = self.db.query(func.avg(CourseReview.rating)).filter(
                CourseReview.course_id == review_data.course_id,
                CourseReview.is_approved == True
            ).scalar()

            course.average_rating = float(avg_rating) if avg_rating else None

            # Update vendor average rating
            vendor_avg_rating = self.db.query(func.avg(MarketplaceCourse.average_rating)).filter(
                MarketplaceCourse.vendor_id == course.vendor_id,
                MarketplaceCourse.average_rating.isnot(None)
            ).scalar()

            vendor = course.vendor
            vendor.average_rating = float(vendor_avg_rating) if vendor_avg_rating else None
            vendor.total_reviews += 1

            self.db.commit()
            self.db.refresh(review)

            logger.info(f"Added review for course {course.course_title} by user {review_data.user_id}")
            return review

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error adding course review: {e}")
            raise

    # Analytics
    def get_vendor_analytics(self, vendor_id: int) -> Dict[str, Any]:
        """Get analytics for a specific vendor."""
        try:
            vendor = self.get_vendor(vendor_id)
            if not vendor:
                raise ValueError(f"Vendor with ID {vendor_id} not found")

            # Get top performing courses
            top_courses = self.db.query(MarketplaceCourse).filter(
                MarketplaceCourse.vendor_id == vendor_id,
                MarketplaceCourse.deleted_at.is_(None)
            ).order_by(desc(MarketplaceCourse.enrollment_count)).limit(5).all()

            # Get commission earned
            total_commission = self.db.query(func.sum(CommissionRecord.commission_amount)).filter(
                CommissionRecord.vendor_id == vendor_id,
                CommissionRecord.status == CommissionStatus.PAID
            ).scalar() or Decimal('0')

            # Calculate completion rate
            total_enrollments = self.db.query(func.count(CourseEnrollment.id)).join(MarketplaceCourse).filter(
                MarketplaceCourse.vendor_id == vendor_id
            ).scalar() or 0

            completed_enrollments = self.db.query(func.count(CourseEnrollment.id)).join(MarketplaceCourse).filter(
                MarketplaceCourse.vendor_id == vendor_id,
                CourseEnrollment.is_completed == True
            ).scalar() or 0

            completion_rate = (completed_enrollments / total_enrollments * 100) if total_enrollments > 0 else 0

            return {
                'vendor_id': vendor_id,
                'total_courses': vendor.total_courses,
                'total_enrollments': vendor.total_enrollments,
                'total_revenue': vendor.total_revenue,
                'average_rating': vendor.average_rating,
                'completion_rate': completion_rate,
                'commission_earned': total_commission,
                'top_performing_courses': [
                    {
                        'id': course.id,
                        'title': course.course_title,
                        'enrollment_count': course.enrollment_count,
                        'average_rating': course.average_rating
                    }
                    for course in top_courses
                ]
            }

        except Exception as e:
            logger.error(f"Error getting vendor analytics for {vendor_id}: {e}")
            raise

    def get_marketplace_analytics(self) -> Dict[str, Any]:
        """Get overall marketplace analytics."""
        try:
            # Basic counts
            total_vendors = self.db.query(func.count(MarketplaceVendor.id)).filter(
                MarketplaceVendor.deleted_at.is_(None),
                MarketplaceVendor.status == VendorStatus.ACTIVE
            ).scalar() or 0

            total_courses = self.db.query(func.count(MarketplaceCourse.id)).filter(
                MarketplaceCourse.deleted_at.is_(None),
                MarketplaceCourse.status == CourseStatus.PUBLISHED
            ).scalar() or 0

            total_enrollments = self.db.query(func.count(CourseEnrollment.id)).scalar() or 0

            # Revenue calculation
            total_revenue = self.db.query(func.sum(CourseEnrollment.purchase_price)).scalar() or Decimal('0')

            # Average course rating
            avg_course_rating = self.db.query(func.avg(MarketplaceCourse.average_rating)).filter(
                MarketplaceCourse.average_rating.isnot(None),
                MarketplaceCourse.status == CourseStatus.PUBLISHED
            ).scalar()

            # Top vendors by revenue
            top_vendors = self.db.query(MarketplaceVendor).filter(
                MarketplaceVendor.deleted_at.is_(None)
            ).order_by(desc(MarketplaceVendor.total_revenue)).limit(5).all()

            # Top courses by enrollment
            top_courses = self.db.query(MarketplaceCourse).filter(
                MarketplaceCourse.deleted_at.is_(None),
                MarketplaceCourse.status == CourseStatus.PUBLISHED
            ).order_by(desc(MarketplaceCourse.enrollment_count)).limit(5).all()

            return {
                'total_vendors': total_vendors,
                'total_courses': total_courses,
                'total_enrollments': total_enrollments,
                'total_revenue': total_revenue,
                'average_course_rating': float(avg_course_rating) if avg_course_rating else None,
                'top_vendors': [
                    {
                        'id': vendor.id,
                        'name': vendor.vendor_name,
                        'total_revenue': vendor.total_revenue,
                        'total_courses': vendor.total_courses
                    }
                    for vendor in top_vendors
                ],
                'top_courses': [
                    {
                        'id': course.id,
                        'title': course.course_title,
                        'vendor_name': course.vendor.vendor_name,
                        'enrollment_count': course.enrollment_count,
                        'average_rating': course.average_rating
                    }
                    for course in top_courses
                ]
            }

        except Exception as e:
            logger.error(f"Error getting marketplace analytics: {e}")
            raise

    # International Support
    def convert_currency(self, conversion_request: CurrencyConversionRequest) -> Dict[str, Any]:
        """Convert currency using stored exchange rates."""
        try:
            # Get exchange rate
            rate_record = self.db.query(MarketplaceCurrencyRate).filter(
                MarketplaceCurrencyRate.from_currency == conversion_request.from_currency,
                MarketplaceCurrencyRate.to_currency == conversion_request.to_currency,
                MarketplaceCurrencyRate.is_active == True
            ).order_by(desc(MarketplaceCurrencyRate.rate_date)).first()

            if not rate_record:
                # Try reverse rate
                reverse_rate = self.db.query(MarketplaceCurrencyRate).filter(
                    MarketplaceCurrencyRate.from_currency == conversion_request.to_currency,
                    MarketplaceCurrencyRate.to_currency == conversion_request.from_currency,
                    MarketplaceCurrencyRate.is_active == True
                ).order_by(desc(MarketplaceCurrencyRate.rate_date)).first()

                if reverse_rate:
                    exchange_rate = Decimal('1') / reverse_rate.rate
                else:
                    raise ValueError(f"Exchange rate not found for {conversion_request.from_currency}/{conversion_request.to_currency}")
            else:
                exchange_rate = rate_record.rate

            converted_amount = conversion_request.amount * exchange_rate

            return {
                'original_amount': conversion_request.amount,
                'converted_amount': converted_amount,
                'from_currency': conversion_request.from_currency,
                'to_currency': conversion_request.to_currency,
                'exchange_rate': exchange_rate,
                'conversion_date': datetime.utcnow()
            }

        except Exception as e:
            logger.error(f"Error converting currency: {e}")
            raise

    def get_supported_currencies(self) -> List[str]:
        """Get list of supported currencies."""
        try:
            currencies = self.db.query(MarketplaceCurrencyRate.from_currency).distinct().all()
            currencies.extend(self.db.query(MarketplaceCurrencyRate.to_currency).distinct().all())

            unique_currencies = list(set([curr[0] for curr in currencies]))
            return sorted(unique_currencies)

        except Exception as e:
            logger.error(f"Error getting supported currencies: {e}")
            raise

    def get_international_markets(self) -> List[InternationalMarket]:
        """Get list of active international markets."""
        try:
            return self.db.query(InternationalMarket).filter(
                InternationalMarket.is_active == True
            ).order_by(InternationalMarket.country_name).all()

        except Exception as e:
            logger.error(f"Error getting international markets: {e}")
            raise

    # Partnership Management
    def create_partnership(self, partnership_data: PartnershipAgreementCreate) -> PartnershipAgreement:
        """Create a new partnership agreement."""
        try:
            # Verify vendor exists
            vendor = self.get_vendor(partnership_data.vendor_id)
            if not vendor:
                raise ValueError(f"Vendor with ID {partnership_data.vendor_id} not found")

            # Calculate expiration date
            effective_date = datetime.utcnow()
            expiration_date = effective_date + timedelta(days=partnership_data.contract_duration_months * 30)

            # Create partnership
            partnership = PartnershipAgreement(
                vendor_id=partnership_data.vendor_id,
                partner_name=partnership_data.partner_name,
                partnership_tier=partnership_data.partnership_tier,
                partnership_type=partnership_data.partnership_type,
                revenue_share=partnership_data.revenue_share,
                minimum_annual_volume=partnership_data.minimum_annual_volume,
                contract_duration_months=partnership_data.contract_duration_months,
                exclusive_benefits=partnership_data.exclusive_benefits,
                co_branding_rights=partnership_data.co_branding_rights,
                early_access=partnership_data.early_access,
                joint_marketing=partnership_data.joint_marketing,
                agreement_terms=partnership_data.agreement_terms or {},
                status='active',
                effective_date=effective_date,
                expiration_date=expiration_date
            )

            self.db.add(partnership)
            self.db.commit()
            self.db.refresh(partnership)

            logger.info(f"Created partnership: {partnership.partner_name} (ID: {partnership.id})")
            return partnership

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating partnership: {e}")
            raise
