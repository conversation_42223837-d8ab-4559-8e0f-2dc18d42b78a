# Direnv configuration for automatic Nix shell activation
# Install direnv: https://direnv.net/docs/installation.html

# Use Nix flakes if available, fallback to shell.nix
if has nix && has nix-shell && [[ -f flake.nix ]]; then
    echo "🚀 Loading Nix development environment (flakes)..."
    use flake
elif has nix && has nix-shell && [[ -f shell.nix ]]; then
    echo "🚀 Loading Nix development environment (legacy)..."
    use nix
else
    echo "⚠️  Nix not found. Install Nix for automatic dependency management."
    echo "💡 Run: make install-nix"
fi

# Set environment variables
export PYTHONPATH="."
export DATABASE_URL="postgresql://localhost/certrats_dev"
export REDIS_URL="redis://localhost:6379"
export SECRET_KEY="dev-secret-key-change-in-production"
export ENVIRONMENT="development"

# Welcome message
echo "✅ CertPathFinder development environment loaded!"
echo "💡 Available commands: make help"
