"""
FastAPI server entry point
"""
import uvicorn
import logging
import sys
import time
import traceback
from api.app import create_app
from api.config import settings
from database import init_db, get_db
from sqlalchemy import text

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.DEBUG,  # Set to DEBUG for more details
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def verify_database_connection():
    """Verify database connection before starting server"""
    try:
        logger.info("Verifying database connection...")
        db = next(get_db())
        # Test connection with simple query
        db.execute(text("SELECT 1"))
        db.close()
        logger.info("Database connection verified successfully")
        return True
    except Exception as e:
        logger.error(f"Database connection verification failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def run_server():
    """Run the FastAPI server with improved error handling"""
    try:
        # Initialize and verify database connection
        logger.info("Initializing database...")
        init_db()

        if not verify_database_connection():
            logger.error("Failed to verify database connection")
            sys.exit(1)

        # Create FastAPI application
        try:
            logger.info("Creating FastAPI application...")
            app = create_app()
            logger.info("FastAPI application created successfully")
        except Exception as e:
            logger.error(f"Failed to create FastAPI application: {str(e)}")
            logger.error(traceback.format_exc())
            sys.exit(1)

        # Use port 8000 for FastAPI to avoid conflict with Streamlit
        host = "0.0.0.0"  # Allow external access
        port = 8000       # Use different port than Streamlit

        logger.info(f"Starting FastAPI server on {host}:{port}")
        logger.info(f"API will be available at: http://{host}:{port}/api/v1")
        logger.info(f"Swagger docs will be available at: http://{host}:{port}/docs")

        # Start uvicorn server with detailed logging
        uvicorn.run(
            app=app,
            host=host,
            port=port,
            log_level="debug",  # Set to debug for more details
            access_log=True,
            timeout_keep_alive=30,
            workers=1
        )

    except Exception as e:
        logger.error(f"Critical error during server startup: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    logger.info("Starting FastAPI server process")
    run_server()